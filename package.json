{"name": "scaffold-html", "version": "2.0.0", "private": true, "scripts": {"start": "vue-cli-service serve --mode development", "build": "vue-cli-service build", "build:prod": "vue-cli-service build --mode production", "build:sit": "vue-cli-service build --mode production.sit", "build:uat": "vue-cli-service build --mode production.uat"}, "dependencies": {"@amap/amap-jsapi-loader": "^1.0.1", "@tinymce/tinymce-vue": "^3.2.8", "axios": "^0.19.2", "babel-eslint": "^8.0.1", "babel-plugin-component": "^1.1.1", "clipboard": "^2.0.11", "core-js": "^3.6.5", "crypto-js": "^4.1.1", "dom-to-image": "^2.6.0", "echarts": "^5.4.0", "el-cascader-multi": "^1.1.8", "element-ui": "^2.15.10", "html2canvas": "^1.4.1", "lodash": "^4.17.19", "moment": "^2.24.0", "qrcodejs2": "0.0.2", "qs": "^6.11.2", "quill": "^1.3.7", "sass": "^1.26.5", "sass-loader": "^9.0.2", "sortablejs": "^1.15.6", "svg-sprite-loader": "^5.0.0", "three": "^0.176.0", "tinymce": "^5.7.0", "tree-table-vue": "^1.1.0", "video.js": "^7.6.5", "vue": "^2.6.11", "vue-cookie": "1.1.4", "vue-json-viewer": "^2.2.22", "vue-quill-editor": "^3.0.6", "vue-router": "3.0.7", "vue-video-player": "^5.0.2", "vuex": "^3.5.1"}, "devDependencies": {"@vue/cli-plugin-babel": "^4.4.6", "@vue/cli-service": "^4.4.6", "element-theme-chalk": "^2.15.7", "natives": "^1.1.6", "vue-template-compiler": "^2.6.11"}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 10"], "engines": {"node": ">= 8.11.1", "npm": ">= 5.6.0"}}