<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>苏州工业园区新时代文明实践中心建设工作服务平台</title>
    <link rel="shortcut icon" href="<%= BASE_URL %>favicon.ico">
    <!-- 站点配置 -->
    <script>
        window.SITE_CONFIG = {};
        window.SITE_CONFIG['version'] = 'v5.0.0';
        window.SITE_CONFIG['nodeEnv'] = '<%= process.env.VUE_APP_NODE_ENV %>';
        window.SITE_CONFIG['apiURL'] = '';                      // api请求地址
        window.SITE_CONFIG['storeState'] = {};                  // vuex本地储存初始化状态（用于不刷新页面的情况下，也能重置初始化项目中所有状态）
        window.SITE_CONFIG['aseKey'] = 'aaaaaaaaaaaaaaaa';                  // 密码加密
    </script>

    <!-- 开发环境 -->
    <% if (process.env.VUE_APP_NODE_ENV === 'dev') { %>
    <script>
        window.SITE_CONFIG['baseUrl'] = 'http://localhost:9090'
        // window.SITE_CONFIG['baseUrl'] = 'http://demo47.fyxmt.com/volunteer_api'
        window.SITE_CONFIG['ssoBaseUrl'] = 'https://wmyq.sipac.gov.cn/test/#'
        // window.SITE_CONFIG['ssoBaseUrl'] = 'http://localhost:8081/#'
        // window.SITE_CONFIG['attachmentUrl'] = 'http://localhost:9090'
        window.SITE_CONFIG['attachmentUrl'] = 'http://localhost:9090'
        window.SITE_CONFIG['wechatAppID'] = 'wx29145e35261335f9'
        window.SITE_CONFIG['appID'] = 'wxc4d301cafb380fac'
        window.SITE_CONFIG['openRedirectUrl'] = 'https://zyz.sipac.gov.cn/'
        window.SITE_CONFIG['pcSiteBaseUrl'] = 'http://demo47.fyxmt.com/volunteer_pc'
        window.SITE_CONFIG['govWxHost'] = 'https://************' // 政务微信的host
        window.SITE_CONFIG['govWxAppID'] = 'wwec70a5fa19fd89d6' // 政务微信的corpid
        window.SITE_CONFIG['govWxAgentID'] = '1000133' // 政务微信的应用ID
        window.SITE_CONFIG['govWxRedirectUrl'] = 'http://demo47.fyxmt.com/volunteer_backend/#/common/blank' // 政务微信登录回调地址
        window.SITE_CONFIG['csrBackendLoginPage'] = 'http://demo47.fyxmt.com/csr_backend/#/login' // 企业责任联盟后台地址
        window.SITE_CONFIG['csrBackendBlankPage'] = 'http://demo47.fyxmt.com/csr_backend/#/blank' // 企业责任联盟后台地址
    </script>
<!--    <script>-->
<!--        window.SITE_CONFIG['baseUrl'] = 'http://demo47.fyxmt.com/volunteer_zsq_api'-->
<!--        window.SITE_CONFIG['ssoBaseUrl'] = 'https://wmyq.sipac.gov.cn/test/#'-->
<!--        window.SITE_CONFIG['attachmentUrl'] = 'http://demo47.fyxmt.com/volunteer_zsq_api'-->
<!--        window.SITE_CONFIG['wechatAppID'] = 'wx29145e35261335f9'-->
<!--        window.SITE_CONFIG['appID'] = 'wxc4d301cafb380fac'-->
<!--        window.SITE_CONFIG['openRedirectUrl'] = 'https://zyz.sipac.gov.cn/'-->
<!--        window.SITE_CONFIG['pcSiteBaseUrl'] = 'http://demo47.fyxmt.com/volunteer_pc'-->
<!--    </script>-->
    <% } %>
    <!-- 集成测试环境 -->
    <% if (process.env.VUE_APP_NODE_ENV === 'prod:sit') { %>
    <script>
        window.SITE_CONFIG['baseUrl'] = 'http://demo47.fyxmt.com/volunteer_api'
        window.SITE_CONFIG['ssoBaseUrl'] = 'https://wmyq.sipac.gov.cn/test/#'
        window.SITE_CONFIG['attachmentUrl'] = 'http://demo47.fyxmt.com/volunteer_api'
        window.SITE_CONFIG['wechatAppID'] = 'wx29145e35261335f9'
        window.SITE_CONFIG['appID'] = 'wxc4d301cafb380fac'
        window.SITE_CONFIG['openRedirectUrl'] = 'https://zyz.sipac.gov.cn/'
        window.SITE_CONFIG['pcSiteBaseUrl'] = 'http://demo47.fyxmt.com/volunteer_pc'
        window.SITE_CONFIG['govWxHost'] = 'http://************' // 政务微信的host
        window.SITE_CONFIG['govWxAppID'] = 'wwec70a5fa19fd89d6' // 政务微信的corpid
        window.SITE_CONFIG['govWxAgentID'] = '1000133' // 政务微信的应用ID
        window.SITE_CONFIG['govWxRedirectUrl'] = 'http://demo47.fyxmt.com/volunteer_backend/#/common/blank' // 政务微信登录回调地址
        window.SITE_CONFIG['csrBackendLoginPage'] = 'http://demo47.fyxmt.com/csr_backend/#/login' // 企业责任联盟后台地址
        window.SITE_CONFIG['csrBackendBlankPage'] = 'http://demo47.fyxmt.com/csr_backend/#/blank' // 企业责任联盟后台地址
    </script>
    <% } %>
    <!-- 验收测试环境 -->
    <% if (process.env.VUE_APP_NODE_ENV === 'prod:uat') { %>
    <script>
        window.SITE_CONFIG['baseUrl'] = 'http://demo46.fyxmt.com'
        window.SITE_CONFIG['ssoBaseUrl'] = 'https://wmyq.sipac.gov.cn/test/#'
        window.SITE_CONFIG['projectAppUrl'] = 'http://demo46.fyxmt.com/project'
    </script>
    <% } %>
    <!-- 生产环境 -->
    <% if (process.env.VUE_APP_NODE_ENV === 'prod') { %>
    <script>
        window.SITE_CONFIG['baseUrl'] = 'https://zyz.sipac.gov.cn/volunteer_api'
        window.SITE_CONFIG['ssoBaseUrl'] = 'https://wmyq.sipac.gov.cn/#'
        window.SITE_CONFIG['attachmentUrl'] = 'https://zyz.sipac.gov.cn'
        window.SITE_CONFIG['wechatAppID'] = 'wx29145e35261335f9'
        window.SITE_CONFIG['appID'] = 'wxc4d301cafb380fac'
        window.SITE_CONFIG['openRedirectUrl'] = 'https://zyz.sipac.gov.cn/volunteer_backend/#/login-wait'
        window.SITE_CONFIG['pcSiteBaseUrl'] = 'https://zyz.sipac.gov.cn'
        window.SITE_CONFIG['govWxAppID'] = 'wwec70a5fa19fd89d6' // 政务微信的corpid
        window.SITE_CONFIG['govWxAgentID'] = '1000133' // 政务微信的应用ID
        window.SITE_CONFIG['govWxRedirectUrl'] = 'https://zyz.sipac.gov.cn/volunteer_backend/#/common/blank' // 政务微信登录回调地址
        window.SITE_CONFIG['csrBackendLoginPage'] = 'https://csr.sipac.gov.cn/#/login' // 企业责任联盟后台地址
        window.SITE_CONFIG['csrBackendBlankPage'] = 'https://csr.sipac.gov.cn/#/blank' // 企业责任联盟后台地址
    </script>
    <% } %>
</head>
<body>
<div id="app"></div>
</body>
</html>
