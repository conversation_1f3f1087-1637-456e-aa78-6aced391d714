import Vue from 'vue'
export default {
  data () {
    /* eslint-disable */
    return {
      // 设置属性
      uploadOptions: {
        serverCode: 'LocalServer',           // 默认文件处理服务器
        fieldName: null,                       // 文件字段
        maxSize: 10,                         // 限制文件大小
        fileExts: 'jpg,png,jpeg,bmp',        // 允许上传类型
        fileList: [],                        // 上传文件列表
        limit: 1                             // 限制文件个数
      },
      headers: {Authorization: Vue.cookie.get('Authorization')},
    }
    /* eslint-enable */
  },
  methods: {
    callback (res) {
      if (this.uploadOptions.fieldName) {
        this.dataForm[this.uploadOptions.fieldName] = res.obj.path
      }
    },
    successHandle (res, file, fileList, callback) {
      if (!res.success) {
        return this.$message.error('上传失败')
      }
      this.$message({
        message: '操作成功',
        type: 'success',
        duration: 1000
      })

      if (callback) {
        callback(res)
      } else {
        this.callback(res)
      }
    },
    errorHandle (err, file, fileList) {
      this.$message({
        title: '上传失败',
        message: err,
        duration: 1000
      })
    },
    beforeUploadHandle (file, maxSize, fileExts, callback) {
      let types = fileExts || this.uploadOptions.fileExts
      let fileExt = file.name.replace(/.+\./, '')
      let unSupportType = types.indexOf(fileExt.toLowerCase()) === -1
      if (unSupportType) {
        this.$message.error('上传失败: 不支持的文件类型')
        return false
      }
      let sizeLimit = maxSize || this.uploadOptions.maxSize
      let isOutSize = file.size / 1024 / 1024 > sizeLimit
      if (isOutSize) {
        this.$message.error(`上传文件大小不能超过${sizeLimit}MB!`)
        return false
      }
      if (callback) {
        this.callback(file)
      }
    },
    changHandle (file, fileList) {
      let index = fileList.length - this.uploadOptions.limit
      this.uploadOptions.fileList = fileList.slice(index)
    },
    removeHandle (file, fileList, callback) {
      this.uploadOptions.fileList = fileList
      if (callback) {
        callback(file)
      }
    },
    exceedHandle (files, fileList) {

    }
  }
}
