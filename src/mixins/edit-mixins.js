import _ from 'lodash'
export default {
  data () {
    /* eslint-disable */
    return {
      // 设置属性
      editOptions: {
        initUrl: '',                // 初始化请求地址
        submitUrl: null,            // 提交地址前缀
        saveSuffix: 'save',         // 保存地址后缀
        updateSuffix: 'update',     // 编辑地址后缀
        isEmit: true,               // 是否提交上级回调
        emit: 'refreshDataList',    // 提交回调名称
        isUniteUrl: false           // 是否唯一提交地址 是：不区分编辑和保存，否：保存和编辑单独添加地址后缀
      },
      dataFormLoading: false,
      visible: false,
      dataForm: {
        id: null,
        version: 0
      },
      dataRule: {},
      errors: {}
    }
    /* eslint-enable */
  },
  methods: {
    initBeforeHandle () {
      // 初始化预处理函数
    },
    initCallback (data) {
      // 初始化回调函数
      this.dataForm = data
    },
    async init (id) {
      this.dataForm.id = id || null
      if (!id) {
        this.dataForm.createDate = null
        this.dataForm.creator = null
        this.dataForm.version = 0
      }
      await this.initBeforeHandle()
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm']?.resetFields()
        if (this.dataForm.id) {
          this.getData()
        }
      })
    },
    getData() {
      this.$http({
        url: this.$http.adornUrl(this.editOptions.initUrl),
        method: 'get',
        params: this.$http.adornParams({id: this.dataForm.id})
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.initCallback(data.obj)
        }
      })
    },
    submitBeforeHandle () {
      // 提交前预处理函数
      return true
    },
    // 表单提交
    dataFormSubmit () {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.clearErrors()
          this.submitBeforeHandle()
          let submitUrl = this.editOptions.submitUrl || this.editOptions.initUrl
          if (!this.editOptions.isUniteUrl) {
            submitUrl = `${submitUrl}/${!this.dataForm.id ? this.editOptions.saveSuffix : this.editOptions.updateSuffix}`
          }
          this.dataFormLoading = true
          this.$http({
            url: this.$http.adornUrl(submitUrl),
            method: 'post',
            data: this.$http.adornData(this.dataForm)
          }).then(({data}) => {
            if (data && data.code === 0) {
              // 全局修复重复提交问题
              this.visible = false
              return this.$message({
                message: '操作成功',
                type: 'success',
                duration: 1000,
                onClose: () => {
                  this.dataFormLoading = false
                  if(this.editOptions.isEmit) {
                    this.$emit(this.editOptions.emit)
                  }
                }
              })
            }
            if (data && data.code === 303) {
              let errors = {}
              for (let it of data.obj) {
                errors[`${it.field}`] = it.message
              }
              this.errors = _.cloneDeep(errors)
            } else {
              this.$message.error(data.msg)
            }
            this.dataFormLoading = false
          }).catch(() => {
            this.dataFormLoading = false
          })
        }
      })
    },
    clearErrors () {
      this.errors = {}
    }
  }
}
