export default {
  data () {
    /* eslint-disable */
    return {
      // 设置属性
      mixinOptions: {
        method: 'post',             // 请求方式 目前只支持post和get
        pageable: true,             // 是否分页
        createdLoad: false,         // 此页面是否在创建时，调用查询数据列表接口？
        activatedLoad: true,        // 此页面是否在激活（进入）时，调用查询数据列表接口？
        dataUrl: '',                // 数据列表接口，API地址
        deleteUrl: '',              // 删除接口，API地址
        exportUrl: '',              // 导出接口，API地址
        exportFileName: 'data-list' // 导出文件名称
      },
      // 默认属性
      dataForm: {},               // 查询条件
      dataList: [],               // 数据列表
      pageIndex: 1,               // 当前页
      pageSize: 10,               // 分页大小
      totalPage: 0,               // 总条数
      dataListLoading: false,     // 数据列表，loading状态
      dataListSelections: [],     // 数据列表，多选项
      addOrUpdateVisible: false   // 新增／更新，弹窗visible状态
    }
    /* eslint-enable */
  },
  created () {
    if (this.mixinOptions.createdLoad) {
      this.query()
    }
  },
  activated () {
    if (this.mixinOptions.activatedLoad) {
      this.query()
    }
  },
  methods: {
    queryBeforeHandle () {
      // 查询前操作
    },
    queryCallback (data) {
      // 查询后操作
      if (data.code !== 0) {
        this.dataList = []
        this.totalPage = 0
        return this.$message.error(data.msg)
      }
      this.querySuccessHandle(data.obj)
    },
    querySuccessHandle (data) {
      // 查询成功操作
      this.dataList = data.records
      this.totalPage = data.total
    },
    // 获取数据列表
    query () {
      this.queryBeforeHandle()
      let params = this.dataForm
      if (this.mixinOptions.pageable) {
        params = {
          currentPage: this.pageIndex,
          pageSize: this.pageSize,
          ...this.dataForm
        }
      }
      let request = {
        url: this.$http.adornUrl(this.mixinOptions.dataUrl),
        method: this.mixinOptions.method
      }
      if (this.mixinOptions.method === 'post') {
        request.data = this.$http.adornData(params)
      } else {
        request.params = this.$http.adornParams(params)
      }
      this.dataListLoading = true
      this.$http(request).then(({data}) => {
        this.dataListLoading = false
        this.queryCallback(data)
      }).catch(() => {
        this.dataListLoading = false
      })
    },
    // 多选
    selectionChangeHandle (val) {
      this.dataListSelections = val
    },
    // 分页, 每页条数
    sizeChangeHandle (val) {
      this.pageSize = val
      this.pageIndex = 1
      this.query()
    },
    // 分页, 当前页
    currentChangeHandle (val) {
      this.pageIndex = val
      this.query()
    },
    getDataList: function () {
      this.pageIndex = 1
      this.query()
    },
    // 新增 / 修改
    addOrUpdateHandle (id) {
      this.addOrUpdateVisible = true
      this.$nextTick(() => {
        this.$refs.addOrUpdate.init(id)
      })
    },
    // 删除
    deleteHandle (id) {
      let ids = id ? [id] : this.dataListSelections.map(item => {
        return item.id
      })
      this.$confirm(`确定进行${id ? '删除' : '批量删除'}操作?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        closeOnClickModal: false
      }).then(() => {
        this.$http({
          url: this.$http.adornUrl(this.mixinOptions.deleteUrl),
          method: 'get',
          params: this.$http.adornParams({'ids': ids.join(',')}, false)
        }).then(({data}) => {
          if (data.code !== 0) {
            return this.$message.error(data.msg)
          }
          this.$message({
            message: '操作成功',
            type: 'success',
            duration: 1000,
            onClose: () => {
              this.query()
            }
          })
        }).catch(() => {
        })
      }).catch(() => {
      })
    },
    // 导出
    exportHandle () {
      this.exportLoading = true
      this.$http({
        url: this.$http.adornUrl(this.mixinOptions.exportUrl),
        method: 'post',
        responseType: 'arraybuffer',
        data: this.$http.adornData(this.dataForm)
      }).then(({data}) => {
        this.downloadExcel(data, this.mixinOptions.exportFileName)
        this.exportLoading = false
      })
    }
  }
}
