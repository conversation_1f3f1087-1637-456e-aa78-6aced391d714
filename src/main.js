import Vue from 'vue'
import App from '@/App'
import router from '@/router'                 // api: https://github.com/vuejs/vue-router
import store from '@/store'                   // api: https://github.com/vuejs/vuex
import VueCookie from 'vue-cookie'            // api: https://github.com/alfhen/vue-cookie
import '@/element-ui'                         // api: https://github.com/ElemeFE/element
import '@/icons'                              // api: http://www.iconfont.cn/
import 'element-ui/lib/theme-chalk/index.css'
import '@/assets/scss/index.scss'
import httpRequest from '@/utils/httpRequest' // api: https://github.com/axios/axios
import {isAuth, downloadExcel, resetForm} from '@/utils'
import ElDict from './components/el-dict'
import SingleDayDatetimeRangePicker from "@/components/single-day-datetime-picker/single-day-datetime-range-picker";
import SingleDayDatetimePicker from "@/components/single-day-datetime-picker/single-day-datetime-picker";
import treeTable from 'tree-table-vue'
import ModelViewer from '@/components/model-viewer'
import JsonViewer from 'vue-json-viewer'



import cloneDeep from 'lodash/cloneDeep'
import elCascaderMulti from 'el-cascader-multi'

Vue.component(treeTable.name, treeTable)
Vue.use(elCascaderMulti)
Vue.use(ElDict)
Vue.use(JsonViewer)
Vue.use(SingleDayDatetimeRangePicker)
Vue.use(SingleDayDatetimePicker)
Vue.component('model-viewer', ModelViewer)

Vue.component('el-dict', ElDict)
Vue.component('single-day-datetime-range-picker', SingleDayDatetimeRangePicker)
Vue.component('single-day-datetime-picker', SingleDayDatetimePicker)
Vue.use(VueCookie)
Vue.config.productionTip = false

// 挂载全局
Vue.prototype.$http = httpRequest // ajax请求方法
Vue.prototype.isAuth = isAuth     // 权限方法
Vue.prototype.downloadExcel = downloadExcel
Vue.prototype.resetForm = resetForm

// 保存整站vuex本地储存初始状态
window.SITE_CONFIG['storeState'] = cloneDeep(store.state)

!(function (t) {
  function e() {
    var e = this || self;
    (e.globalThis = e), delete t.prototype._T_;
  }
  "object" != typeof globalThis &&
    (this
      ? e()
      : (t.defineProperty(t.prototype, "_T_", {
          configurable: !0,
          get: e,
        }),
        _T_));
})(Object);

/**
 * @description
 * <AUTHOR> the text for this tag by adding docthis.authorName to your settings file.)
 * @date 2019-05-29
 * @param { number } type 1 localStorage 2 sessionStorage
 * @param { string } key 键
 * @param { string } data 要存储的数据
 * @returns
 */
Vue.prototype.$addStorageEvent = function (type, key, data) {
    var newStorageEvent
    if (type === 1) {
        // 创建一个StorageEvent事件
        newStorageEvent = document.createEvent('StorageEvent')
        const storage = {
            setItem: function (k, val) {
                localStorage.setItem(k, val)
                // 初始化创建的事件
                newStorageEvent.initStorageEvent('setItem', false, false, k, null, val, null, null)
                // 派发对象
                window.dispatchEvent(newStorageEvent)
            }
        }
        return storage.setItem(key, data)
    } else {
        // 创建一个StorageEvent事件
        newStorageEvent = document.createEvent('StorageEvent')
        const storage = {
            setItem: function (k, val) {
                sessionStorage.setItem(k, val)
                // 初始化创建的事件
                newStorageEvent.initStorageEvent('setItem', false, false, k, null, val, null, null)
                // 派发对象
                window.dispatchEvent(newStorageEvent)
            }
        }
        return storage.setItem(key, data)
    }
}

/* eslint-disable no-new */
new Vue({
    router,
    store,
    render: h => h(App)
}).$mount('#app')
