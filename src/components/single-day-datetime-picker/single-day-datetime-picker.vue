<template>
  <div  style="display: flex; justify-content: space-between">
    <el-date-picker
        style="width: 50%;padding-right: 3px"
        v-model="dateObj.date"
        type="date"
        placeholder="选择日期"
        format="yyyy-MM-dd"
        value-format="yyyy-MM-dd"
        :picker-options="dateOptions"
    ></el-date-picker>
    <el-time-select
        style="width: 40%;padding-right: 3px"
        placeholder="时间"
        v-model="dateObj.time"
        :key="getKey()"
        :picker-options="{ start: '00:00',step: '00:15',end:'23:45'}">
    </el-time-select>
  </div>
</template>

<script>
import moment from 'moment'
import {uuid} from '@/utils'

export default {
  props: {
    value: {
      type: String,
      required: true,
    },
    dateOptions: {
      type: String,
      required: false
    }
  },
  name: 'single-day-datetime-picker',
  componentName: 'SingleDayDatetimePicker',
  data() {
    return {
      dateObj: {
        date: '',
        time: ''
      }
    };
  },
  watch: {
    value: {
      handler(newValue) {
        console.info('传入组件的时间', newValue)
        if (newValue == null || !newValue) {
          return
        }
        this.dateObj.date = moment(newValue).format('YYYY-MM-DD')
        this.dateObj.time = moment(newValue).format('HH:mm')
        console.info('变化后的dateObj', this.dateObj)
      },
      immediate: true,
    },
    dateObj: {
      handler(newObj, oldObj) {
        if (!this.dateObj.date || !this.dateObj.time) {
          this.$emit('input', '')
          return
        }
        let fullTime = this.dateObj.date + ' ' + this.dateObj.time + ':00'
        this.$emit('input', fullTime)
      },
      deep: true
    }
  },
  methods: {
    getKey() {
      return uuid()
    }
  },
};
</script>
