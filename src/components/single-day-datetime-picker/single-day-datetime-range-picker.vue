<template>
  <div style="display: flex; justify-content: space-between">
    <el-date-picker
        style="width: 35%;padding-right: 3px"
        v-model="dateObj.date"
        type="date"
        :size="size"
        placeholder="选择日期"
        format="yyyy-MM-dd"
        value-format="yyyy-MM-dd"
        :picker-options="dateOptions"
    ></el-date-picker>
    <el-time-select
        style="width: 28%;padding-right: 3px"
        placeholder="开始时间"
        :size="size"
        v-model="dateObj.startTime"
        :key="getKey()"
        :picker-options="{ start: '00:00',step: '00:15',end:'23:45',maxTime:dateObj.endTime}">
    </el-time-select>
    <el-time-select
        style="width: 28%;padding-right: 3px"
        placeholder="结束时间"
        :size="size"
        v-model="dateObj.endTime"
        :key="getKey()"
        :picker-options="{start: '00:00',step: '00:15',end:'23:45', minTime: dateObj.startTime}">
    </el-time-select>
  </div>
</template>

<script>
import moment from 'moment'
import { uuid } from '@/utils'
export default {
  props: {
    value: {
      type: Array,
      required: true,
    },
    dateOptions: {
      type: Object,
      required: false
    },
    timeOptions: {
      type: Object,
      required: false
    },
    size: {
      type: String,
      required: false,
      default: 'medium'
    }
  },
  name: 'single-day-datetime-range-picker',
  componentName: 'SingleDayDatetimeRangePicker',
  data() {
    return {
      dateObj: {
        date: '',
        startTime: '',
        endTime: ''
      }
    };
  },
  watch: {
    value: {
      handler(newValue) {
        // console.info('传入组件的时间', newValue)
        if (newValue == null || !newValue || !Array.isArray(newValue) || newValue.length !== 2) {
          return
        }
        this.dateObj.date = moment(newValue[0]).format('YYYY-MM-DD')
        this.dateObj.startTime = moment(newValue[0]).format('HH:mm')
        this.dateObj.endTime = moment(newValue[1]).format('HH:mm')
        // console.info('变化后的dateObj',this.dateObj)
      },
      immediate: true,
    },
    dateObj: {
      handler(newObj, oldObj) {
        if (!this.dateObj.date || !this.dateObj.startTime || !this.dateObj.endTime) {
          this.$emit('input', [])
          setTimeout(()=>{
            this.dispatch("ElFormItem", "el.form.change", []);
          })
          return
        }
        let startTime = this.dateObj.date + ' ' + this.dateObj.startTime + ':00'
        let endTime = this.dateObj.date + ' ' + this.dateObj.endTime + ':00'
        this.$emit('input', [startTime, endTime])
        setTimeout(()=>{
          this.dispatch("ElFormItem", "el.form.change", [startTime, endTime]);
        })
      },
      deep: true
    }
  },
  methods: {
    getKey () {
      return uuid()
    },
    dispatch(componentName, eventName, params) {
      let parent = this.$parent || this.$root;
      let name = parent.$options.componentName;

      while (parent && (!name || name !== componentName)) {
        parent = parent.$parent;

        if (parent) {
          name = parent.$options.componentName;
        }
      }
      if (parent) {
        parent.$emit.apply(parent, [eventName].concat(params));
      }
    },
  },
};
</script>
