<template>
  <el-dialog title="模板预览" :visible.sync="dialogVisible" width="60%" :close-on-click-modal="true" :modal-append-to-body="true" :append-to-body="true">
    <el-table v-loading="loading" :data="formFields" border style="width: 100%">
      <el-table-column prop="label" label="字段名称" header-align="center" align="center"></el-table-column>
      <el-table-column prop="fieldTypeText" label="字段类型" header-align="center" align="center"></el-table-column>
      <el-table-column prop="placeholder" label="字段提示" header-align="center" align="center"></el-table-column>
      <el-table-column prop="required" label="是否必填" header-align="center" align="center">
        <template slot-scope="scope">
          {{ scope.row.required ? '是' : '否' }}
        </template>
      </el-table-column>
    </el-table>
  </el-dialog>
</template>

<script>
export default {
  name: 'FormTemplatePreview',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    formId: {
      type: String,
      default: null
    },
    snapshotId: {
      type: String,
      default: null
    }
  },
  data() {
    return {
      loading: false,
      formFields: []
    }
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('update:visible', val)
      }
    }
  },
  watch: {
    visible(val) {
      if (val) {
        this.getFormFields()
      }
    }
  },
  methods: {
    getFormFields() {
      this.loading = true
      if (this.snapshotId) {
        // 使用快照ID获取字段列表
        this.$http({
          url: this.$http.adornUrl('/admin/dynamic/field/snapshot/list'),
          method: 'post',
          data: this.$http.adornData({
            snapshotId: this.snapshotId
          })
        }).then(({data}) => {
          this.loading = false
          if (data && data.code === 0) {
            this.formFields = data.obj || []
          } else {
            this.formFields = []
            this.$message.error('获取表单字段失败：' + (data.msg || '未知错误'))
          }
        }).catch(() => {
          this.loading = false
          this.formFields = []
          this.$message.error('获取表单字段失败')
        })
      } else if (this.formId) {
        // 使用表单ID获取字段列表
        this.$http({
          url: this.$http.adornUrl('/admin/dynamic/form/detail'),
          method: 'get',
          params: this.$http.adornParams({
            id: this.formId
          })
        }).then(({data}) => {
          this.loading = false
          if (data && data.code === 0 && data.obj) {
            this.formFields = data.obj.fields || []
          } else {
            this.formFields = []
            this.$message.error('获取表单字段失败：' + (data.msg || '未知错误'))
          }
        }).catch(() => {
          this.loading = false
          this.formFields = []
          this.$message.error('获取表单字段失败')
        })
      } else {
        this.loading = false
        this.formFields = []
      }
    }
  }
}
</script> 