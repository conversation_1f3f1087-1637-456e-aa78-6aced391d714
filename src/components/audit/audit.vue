<template>
  <div>
    <el-dialog
      title="审核"
      :close-on-click-modal="false"
      :visible.sync="visible" width="40%">
      <el-form :model="dataForm" :rules="dataRule" ref="dataForm" label-width="100px">
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="是否通过：" prop="pass" :error="errors['pass']">
              <el-radio-group v-model="dataForm.pass">
                <el-radio :label="true">通过</el-radio>
                <el-radio :disabled="batch && batch === true" :label="false">驳回</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="备注：" prop="memo" :error="errors['memo']">
              <el-input type="textarea" rows="3" v-model="dataForm.memo" placeholder="备注"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="visible = false">取消</el-button>
        <el-button type="primary" @click="submit()">确定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
  import _ from "lodash";

  export default {
    data () {
      return {
        visible: false,
        submitUrl: null,
        batch: false,
        dataForm: {
          id: null,
          pass: true,
          memo: null
        },
        dataRule: {
        },
        errors: {
        }
      }
    },
    methods: {
      init (id, submitUrl, batch) {
        this.visible = true
        this.dataForm.id = id || null
        this.submitUrl = submitUrl
        this.batch = batch
        this.$nextTick(() => {
          this.$refs['dataForm']?.resetFields()
        })
      },
      submit() {
        this.$confirm(`确定进行审核操作?`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
          closeOnClickModal: false
        }).then(() => {
          this.$refs['dataForm'].validate((valid) => {
            if (valid) {
              this.clearErrors()
              if (!this.submitUrl) {
                this.$message.error('审核接口未知！')
                return
              }
              this.$http(this.batch && this.batch === true ?
                  {
                    url: this.$http.adornUrl(this.submitUrl),
                    method: 'get',
                    params: {
                      ids: this.dataForm.id,
                      memo: this.dataForm.memo
                    }
                  } :
                  {
                    url: this.$http.adornUrl(this.submitUrl),
                    method: 'post',
                    data: this.$http.adornData(this.dataForm)
                  }
              ).then(({data}) => {
                if (data && data.code === 0) {
                  return this.$message({
                    message: '操作成功',
                    type: 'success',
                    duration: 1000,
                    onClose: () => {
                      this.visible = false
                      this.$emit('refreshDataList')
                    }
                  })
                }
                if (data && data.code === 303) {
                  let errors = {}
                  for (let it of data.obj) {
                    errors[`${it.field}`] = it.message
                  }
                  this.errors = _.cloneDeep(errors)
                } else {
                  return this.$message({
                    message: data.msg,
                    type: 'error',
                    duration: 1000,
                    onClose: () => {
                      this.visible = false
                      this.$emit('refreshDataList')
                    }
                  })
                }
              }).catch(() => {
              })
            }
          })
        })
      },
      clearErrors () {
        this.errors = {}
      }
    }
  }
</script>

<style lang="scss">
</style>
