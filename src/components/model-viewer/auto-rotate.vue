<template>
  <div class="model-container" ref="container">
    <div v-if="isLoading" class="loading-overlay">
      <i class="el-icon-loading"></i>
      <p>模型加载中...</p>
    </div>
    <div v-if="hasError" class="error-overlay">
      <i class="el-icon-warning"></i>
      <p>模型加载失败</p>
    </div>
  </div>
</template>

<script>
import * as THREE from 'three'
import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader'
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls'

export default {
  name: 'AutoRotateModelViewer',
  props: {
    modelUrl: {
      type: String,
      required: true
    },
    autoRotate: {
      type: Boolean,
      default: true
    },
    autoRotateSpeed: {
      type: Number,
      default: 1.0
    }
  },
  data() {
    return {
      scene: null,
      camera: null,
      renderer: null,
      controls: null,
      mixer: null,
      clock: new THREE.Clock(),
      animationFrameId: null,
      model: null,
      isLoading: true,
      hasError: false
    }
  },
  watch: {
    modelUrl(newVal) {
      if (newVal) {
        this.loadModel()
      }
    },
    autoRotate(val) {
      if (this.controls) {
        this.controls.autoRotate = val
      }
    },
    autoRotateSpeed(val) {
      if (this.controls) {
        this.controls.autoRotateSpeed = val
      }
    }
  },
  mounted() {
    this.initThree()
    if (this.modelUrl) {
      this.loadModel()
    }
    window.addEventListener('resize', this.onWindowResize)
  },
  beforeDestroy() {
    this.destroyThree()
    window.removeEventListener('resize', this.onWindowResize)
  },
  methods: {
    initThree() {
      const container = this.$refs.container
      const width = container.clientWidth
      const height = container.clientHeight || 400

      // 创建场景
      this.scene = new THREE.Scene()
      this.scene.background = new THREE.Color(0xf0f0f0)

      // 创建相机
      this.camera = new THREE.PerspectiveCamera(45, width / height, 0.1, 1000)
      this.camera.position.set(0, 5, 10)

      // 创建渲染器
      this.renderer = new THREE.WebGLRenderer({ antialias: true })
      this.renderer.setSize(width, height)
      this.renderer.setPixelRatio(window.devicePixelRatio)
      this.renderer.outputColorSpace = THREE.SRGBColorSpace
      this.renderer.shadowMap.enabled = true
      container.appendChild(this.renderer.domElement)

      // 添加轨道控制器
      this.controls = new OrbitControls(this.camera, this.renderer.domElement)
      this.controls.enableDamping = true
      this.controls.dampingFactor = 0.05
      this.controls.autoRotate = this.autoRotate
      this.controls.autoRotateSpeed = this.autoRotateSpeed
      // 设置控制器的最小和最大仰角，避免翻转
      this.controls.minPolarAngle = 0
      this.controls.maxPolarAngle = Math.PI / 1.5
      // 启用平移
      this.controls.enablePan = true
      // 启用缩放
      this.controls.enableZoom = true
      // 设置缩放限制
      this.controls.minDistance = 2
      this.controls.maxDistance = 100

      // 添加环境光
      const ambientLight = new THREE.AmbientLight(0xffffff, 0.7)
      this.scene.add(ambientLight)

      // 添加方向光
      const directionalLight = new THREE.DirectionalLight(0xffffff, 1)
      directionalLight.position.set(1, 1, 1)
      directionalLight.castShadow = true
      this.scene.add(directionalLight)

      // 添加顶光
      const topLight = new THREE.DirectionalLight(0xffffff, 0.5)
      topLight.position.set(0, 1, 0)
      this.scene.add(topLight)

      // 开始动画循环
      this.animate()
    },
    
    loadModel() {
      if (!this.modelUrl) return
      
      this.isLoading = true
      this.hasError = false
      
      // 如果已有模型，先移除
      if (this.model) {
        this.scene.remove(this.model)
        this.model = null
      }
      
      // 添加加载进度和错误处理
      const loadingManager = new THREE.LoadingManager()
      loadingManager.onProgress = (url, loaded, total) => {
        const progress = Math.floor((loaded / total) * 100)
        this.$emit('loading-progress', progress)
      }
      
      loadingManager.onError = (url) => {
        this.isLoading = false
        this.hasError = true
        this.$emit('load-error', url)
      }
      
      const loader = new GLTFLoader(loadingManager)
      
      loader.load(
        this.modelUrl,
        (gltf) => {
          this.model = gltf.scene
          
          // 自动调整相机以适应模型
          const box = new THREE.Box3().setFromObject(this.model)
          const size = box.getSize(new THREE.Vector3()).length()
          const center = box.getCenter(new THREE.Vector3())
          
          // 将模型移至中心
          this.model.position.x = -center.x
          this.model.position.y = -center.y
          this.model.position.z = -center.z
          
          // 调整相机位置，确保模型完整显示
          const maxDim = Math.max(
            box.max.x - box.min.x,
            box.max.y - box.min.y,
            box.max.z - box.min.z
          )
          const fov = this.camera.fov * (Math.PI / 180)
          let cameraZ = Math.abs(maxDim / 2 / Math.tan(fov / 2))
          
          // 添加一些额外距离，确保模型完全可见
          cameraZ *= 1.8
          
          // 计算模型的高度
          const height = box.max.y - box.min.y
          
          // 设置相机位置，从斜上方45度角观察，确保可以看到模型顶部
          const cameraY = height / 2
          this.camera.position.set(cameraZ * 0.5, cameraY, cameraZ)
          this.camera.lookAt(0, 0, 0)
          this.controls.update()
          
          // 处理动画
          if (gltf.animations && gltf.animations.length) {
            this.mixer = new THREE.AnimationMixer(this.model)
            gltf.animations.forEach((clip) => {
              this.mixer.clipAction(clip).play()
            })
          }
          
          this.scene.add(this.model)
          this.isLoading = false
          this.$emit('load-success', this.model)
        },
        (xhr) => {
          const progress = Math.floor((xhr.loaded / xhr.total) * 100)
          this.$emit('loading-progress', progress)
        },
        (error) => {
          console.error('模型加载错误:', error)
          this.isLoading = false
          this.hasError = true
          this.$emit('load-error', error)
        }
      )
    },
    
    animate() {
      this.animationFrameId = requestAnimationFrame(this.animate)
      
      // 更新控制器
      if (this.controls) {
        this.controls.update()
      }
      
      // 更新动画混合器
      if (this.mixer) {
        this.mixer.update(this.clock.getDelta())
      }
      
      // 渲染场景
      if (this.renderer && this.scene && this.camera) {
        this.renderer.render(this.scene, this.camera)
      }
    },
    
    onWindowResize() {
      if (!this.camera || !this.renderer || !this.$refs.container) return
      
      const width = this.$refs.container.clientWidth
      const height = this.$refs.container.clientHeight || 400
      
      this.camera.aspect = width / height
      this.camera.updateProjectionMatrix()
      this.renderer.setSize(width, height)
    },
    
    destroyThree() {
      // 取消动画
      if (this.animationFrameId) {
        cancelAnimationFrame(this.animationFrameId)
        this.animationFrameId = null
      }
      
      // 释放资源
      if (this.renderer) {
        this.renderer.dispose()
        if (this.$refs.container && this.renderer.domElement) {
          this.$refs.container.removeChild(this.renderer.domElement)
        }
      }
      
      // 清除引用
      this.scene = null
      this.camera = null
      this.renderer = null
      this.controls = null
      this.mixer = null
      this.model = null
    }
  }
}
</script>

<style scoped>
.model-container {
  width: 100%;
  height: 100%;
  min-height: 400px;
  background-color: #f0f0f0;
  border-radius: 4px;
  overflow: hidden;
  position: relative;
}

.loading-overlay, .error-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: rgba(255, 255, 255, 0.8);
  z-index: 10;
}

.loading-overlay i, .error-overlay i {
  font-size: 32px;
  margin-bottom: 10px;
}

.error-overlay {
  color: #F56C6C;
}
</style> 