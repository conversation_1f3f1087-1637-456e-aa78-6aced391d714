<template>
  <el-dialog
    title="3D模型预览"
    :visible.sync="visible"
    width="70%"
    :before-close="handleClose"
    append-to-body
    destroy-on-close
  >
    <div class="model-container" ref="container"></div>
    <span slot="footer" class="dialog-footer">
      <el-button @click="handleClose">关闭</el-button>
    </span>
  </el-dialog>
</template>

<script>
import * as THREE from 'three'
import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader'
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls'

export default {
  name: 'ModelViewer',
  props: {
    value: {
      type: Boolean,
      default: false
    },
    modelUrl: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      visible: false,
      scene: null,
      camera: null,
      renderer: null,
      controls: null,
      mixer: null,
      clock: new THREE.Clock(),
      animationFrameId: null
    }
  },
  watch: {
    value(val) {
      this.visible = val
      if (val) {
        this.$nextTick(() => {
          this.initThree()
          this.loadModel()
        })
      }
    },
    visible(val) {
      this.$emit('input', val)
      if (!val) {
        this.destroyThree()
      }
    }
  },
  methods: {
    initThree() {
      const container = this.$refs.container
      const width = container.clientWidth
      const height = container.clientHeight || 400

      // 创建场景
      this.scene = new THREE.Scene()
      this.scene.background = new THREE.Color(0xf0f0f0)

      // 创建相机
      this.camera = new THREE.PerspectiveCamera(45, width / height, 0.1, 1000)
      this.camera.position.set(0, 5, 10)

      // 创建渲染器
      this.renderer = new THREE.WebGLRenderer({ antialias: true })
      this.renderer.setSize(width, height)
      this.renderer.setPixelRatio(window.devicePixelRatio)
      this.renderer.outputColorSpace = THREE.SRGBColorSpace
      this.renderer.shadowMap.enabled = true
      container.appendChild(this.renderer.domElement)

      // 添加轨道控制器
      this.controls = new OrbitControls(this.camera, this.renderer.domElement)
      this.controls.enableDamping = true
      this.controls.dampingFactor = 0.05

      // 添加环境光
      const ambientLight = new THREE.AmbientLight(0xffffff, 0.5)
      this.scene.add(ambientLight)

      // 添加方向光
      const directionalLight = new THREE.DirectionalLight(0xffffff, 1)
      directionalLight.position.set(1, 1, 1)
      directionalLight.castShadow = true
      this.scene.add(directionalLight)

      // 开始动画循环
      this.animate()

      // 添加窗口大小变化监听
      window.addEventListener('resize', this.onWindowResize)
    },
    
    loadModel() {
      if (!this.modelUrl) return
      
      const loader = new GLTFLoader()
      
      // 添加加载进度和错误处理
      const loadingManager = new THREE.LoadingManager()
      loadingManager.onProgress = (url, loaded, total) => {
        console.log(`加载进度：${Math.floor((loaded / total) * 100)}%`)
      }
      
      loadingManager.onError = (url) => {
        this.$message.error('模型加载失败')
      }
      
      loader.setPath('')
      loader.load(
        this.modelUrl,
        (gltf) => {
          const model = gltf.scene
          
          // 自动调整相机以适应模型
          const box = new THREE.Box3().setFromObject(model)
          const size = box.getSize(new THREE.Vector3()).length()
          const center = box.getCenter(new THREE.Vector3())
          
          // 将模型移至中心
          model.position.x = -center.x
          model.position.y = -center.y
          model.position.z = -center.z
          
          // 调整相机位置
          this.camera.position.set(0, size / 3, size)
          this.camera.lookAt(0, 0, 0)
          this.controls.update()
          
          // 处理动画
          if (gltf.animations && gltf.animations.length) {
            this.mixer = new THREE.AnimationMixer(model)
            gltf.animations.forEach((clip) => {
              this.mixer.clipAction(clip).play()
            })
          }
          
          this.scene.add(model)
        },
        (xhr) => {
          console.log(`${(xhr.loaded / xhr.total) * 100}% 已加载`)
        },
        (error) => {
          console.error('模型加载错误:', error)
          this.$message.error('模型加载失败')
        }
      )
    },
    
    animate() {
      this.animationFrameId = requestAnimationFrame(this.animate)
      
      // 更新控制器
      if (this.controls) {
        this.controls.update()
      }
      
      // 更新动画混合器
      if (this.mixer) {
        this.mixer.update(this.clock.getDelta())
      }
      
      // 渲染场景
      if (this.renderer && this.scene && this.camera) {
        this.renderer.render(this.scene, this.camera)
      }
    },
    
    onWindowResize() {
      if (!this.camera || !this.renderer || !this.$refs.container) return
      
      const width = this.$refs.container.clientWidth
      const height = this.$refs.container.clientHeight || 400
      
      this.camera.aspect = width / height
      this.camera.updateProjectionMatrix()
      this.renderer.setSize(width, height)
    },
    
    destroyThree() {
      // 取消动画
      if (this.animationFrameId) {
        cancelAnimationFrame(this.animationFrameId)
        this.animationFrameId = null
      }
      
      // 移除事件监听
      window.removeEventListener('resize', this.onWindowResize)
      
      // 释放资源
      if (this.renderer) {
        this.renderer.dispose()
        if (this.$refs.container && this.renderer.domElement) {
          this.$refs.container.removeChild(this.renderer.domElement)
        }
      }
      
      // 清除引用
      this.scene = null
      this.camera = null
      this.renderer = null
      this.controls = null
      this.mixer = null
    },
    
    handleClose() {
      this.visible = false
    }
  },
  beforeDestroy() {
    this.destroyThree()
  }
}
</script>

<style scoped>
.model-container {
  width: 100%;
  height: 500px;
  background-color: #f0f0f0;
  border-radius: 4px;
  overflow: hidden;
}
</style> 