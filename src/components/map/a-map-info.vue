<template>
  <div class="map">
    <!-- 可关闭, 文字可调整 -->
    <div v-if="showHeaderTip" style="margin: 5px;font-weight: bold;color: red">{{ headerTip }}</div>
    <div class="search">
      <el-input placeholder="请输入内容" v-model="keyword" id="tipInput">
        <template slot="append">
          <el-button type="default" @click="searchPlace()">搜索</el-button>
        </template>
      </el-input>
    </div>
    <div id="container" style="height: calc(100vh - 300px);width: 100%;"></div>
    <div class="address">
      <div>当前位置：{{ currentAddress }}</div>
      <el-button type="primary" @click="closeMap">确定</el-button>
    </div>
  </div>
</template>
<script>
import AMapLoader from '@amap/amap-jsapi-loader'

window._AMapSecurityConfig = {
  securityJsCode: 'b1dccae30ea4b221fc6015388cacec9c'
}
export default {
  props: {
    address: {
      type: String,
      default: '苏州工业园区现代大道999号'
    },
    latitude: {
      type: Number,
      default: null
    },
    longitude: {
      type: Number,
      default: null
    },
    mapVisible: {
      type: Boolean,
      default: null
    },
    showHeaderTip: {
      type: Boolean,
      default: true
    },
    headerTip: {
      type: String,
      default: '圆形为签到范围'
    },
    radius: {
      type: Number,
      default: 300
    }
  },

  data() {
    return {
      map: null,
      marker: null,
      circle: null,
      placeSearch: null,
      keyword: '',
      currentAddress: '苏州工业园区现代大道999号',
      lat: 31.324015,
      lng: 120.723503
    }
  },
  created() {
  },
  mounted() {
    this.currentAddress = this.address || '苏州工业园区现代大道999号'
    this.lat = this.latitude || 31.324015
    this.lng = this.longitude || 120.723503
    this.initAMap()
  },
  methods: {
    initAMap() {
      AMapLoader.load({
        key: '518e02c6820ff7379c10b1c749381a58',
        version: '2.0',
        plugins: [
          'AMap.AutoComplete',
          'AMap.PlaceSearch',
          'AMap.Scale',
          'AMap.OverView',
          'AMap.ToolBar',
          'AMap.MapType',
          'AMap.Geolocation',
          'AMap.Geocoder',
          'AMap.CircleEditor',
          'AMap.AdvancedInfoWindow'
        ],
        AMapUI: {
          version: '1.1',
          plugins: []
        },
        Loca: {
          version: '2.0'
        },
      }).then((AMap) => {
        this.map = new AMap.Map('container', {
          zoom: 15,
          center: [this.lng, this.lat],
        })
        this.map.on('click', (e) => {
          this.getClickAddress(e.lnglat.lng, e.lnglat.lat)
          this.initMark(e.lnglat.lng, e.lnglat.lat)
          this.initCircle(e.lnglat.lng, e.lnglat.lat)
        })
        this.initSearch()
        this.initMark(this.lng, this.lat)
        this.initCircle(this.lng, this.lat)
      }).catch(e => {
        console.log(e)
      })
    },
    initSearch() {
      this.placeSearch = new AMap.PlaceSearch({
        map: this.map,
        city: '苏州'
      })
      //注册监听，当选中某条记录时会触发
      this.placeSearch.on("markerClick", (e) => {
        this.lat = e.data.location.lat
        this.lng = e.data.location.lng
        this.currentAddress = e.data.address
        this.initMark(e.data.location.lng, e.data.location.lat)
        this.initCircle(e.data.location.lng, e.data.location.lat);
      });
    },
    searchPlace() {
      this.placeSearch.search(this.keyword)
    },
    initMark(lng, lat) {
      if (this.marker != null) {
        this.map.remove(this.marker);
      }
      this.marker = new AMap.Marker({
        position: new AMap.LngLat(lng, lat)
      });
      this.map.add(this.marker);
    },
    initCircle(lng, lat) {
      if (this.circle != null) {
        this.map.remove(this.circle)
      }
      this.circle = new AMap.Circle({
        center: [lng, lat],
        radius: this.radius, //半径
        borderWeight: 2,
        strokeColor: "#FF33FF",
        strokeOpacity: 0,
        strokeWeight: 6,
        fillOpacity: 0.2,
        strokeStyle: 'dashed',
        strokeDasharray: [10, 10],
        // 线样式还支持 'dashed'
        fillColor: '#1791fc',
        zIndex: 50,
      })
      this.circle.on('click', (e) => {
        this.initMark(e.lnglat.lng, e.lnglat.lat)
        this.initCircle(e.lnglat.lng, e.lnglat.lat)
        this.getClickAddress(e.lnglat.lng, e.lnglat.lat)
      })
      this.map.add(this.circle);
    },
    getClickAddress(lng, lat) {
      var geocoder = new AMap.Geocoder({
        city: '0512'
      });
      geocoder.getAddress([lng, lat], (status, result) => {
        if (status === 'complete' && result.info === 'OK') {
          if (result && result.regeocode) {
            console.log(result.formattedAddress)
            this.lat = lat
            this.lng = lng
            this.currentAddress = result.regeocode.formattedAddress
          }
        }
      });
    },
    closeMap() {
      this.$emit('update:address', this.currentAddress)
      this.$emit('update:latitude', this.lat)
      this.$emit('update:longitude', this.lng)
      this.$emit('update:mapVisible', false)
      this.$emit('handleMapClose')
    }
  }
}
</script>
<style lang="scss" scoped>
.map {
  position: relative;

  .search {
    width: 300px;
    position: absolute;
    top: 30px;
    left: 30px;
    z-index: 9999;
  }

  .address {
    display: flex;
    margin: 15px 0;
    align-items: center;

    .el-button {
      margin-left: auto;
    }
  }
}
</style>
