<template>
  <div>
    <el-select
        class="width185"
        v-model="selectValue"
        autocomplete
        :clearable="clearable"
        :disabled="disabled"
        :placeholder="placeholder"
        filterable
        style="width: 100%"
        :multiple="multiple"
        :multiple-limit="multipleLimit">
      <el-option
        v-for="item in options"
        :key="item[props.value]"
        :label="item[props.label]"
        :value="item[props.value]">
      </el-option>
    </el-select>
  </div>
</template>

<script>
  export default {
    name: 'el-dict',
    componentName: 'ElDict',
    data () {
      return {
        options: [],
        selectValue: ''
      }
    },
    props: {
      disabled: {
        type: Boolean,
        default: false
      },
      clearable: {
        type: Boolean,
        default: true
      },
      // 导入的url地址（兼容旧版本）
      code: {
        type: String
      },
      // 自定义请求URL
      customUrl: {
        type: String,
        default: ''
      },
      // 自定义请求参数
      customParams: {
        type: Object,
        default: () => ({})
      },
      // 字段映射配置对象
      props: {
        type: Object,
        default: () => ({
          label: 'name',  // 兼容旧版本
          value: 'code'   // 兼容旧版本
        })
      },
      // 接受外部v-model传入的值，必须使用value
      value: {
      },
      multiple: {
        type: Boolean,
        default: false
      },
      // 多选数量限制
      multipleLimit: {
        type: Number,
        default: 0  // 0表示无限制
      },
      placeholder: {
        type: String,
        default: '请选择'
      },
      // 是否默认选中第一个选项
      defaultFirst: {
        type: Boolean,
        default: false
      }
    },
    watch: {
      // 判断下拉框的值是否有改变
      selectValue (val, oldVal) {
        if (val !== oldVal) {
          this.$emit('input', this.selectValue)
        }
      },
      value (val) {
        if (typeof val === 'number') {
          this.selectValue = val.toString()
        } else {
          this.selectValue = val
        }
      }
    },
    mounted () {
      // 远程请求回来的数据
      this.selectValue = this.value
      this.loadData()
    },
    methods: {
      loadData() {
        let url, params

        if (this.customUrl) {
          // 使用自定义URL和参数
          url = this.$http.adornUrl(this.customUrl)
          params = this.$http.adornParams(this.customParams)
        } else {
          // 兼容旧版本，使用默认的字典接口
          url = this.$http.adornUrl(`/admin/dict/parent`)
          params = this.$http.adornParams({ code: this.code })
        }

        this.$http({
          url: url,
          method: 'get',
          params: params
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.options = data.obj || []
            this.$emit('filterDictData')

            // 如果启用了默认选中第一个选项，且当前没有值，则选中第一个
            if (this.defaultFirst && !this.selectValue && this.options.length > 0) {
              this.selectValue = this.options[0][this.props.value]
              this.$emit('input', this.selectValue)
            }
          }
        })
      }
    }
  }
</script>
