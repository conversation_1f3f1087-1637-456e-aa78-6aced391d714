<template>
  <div>
    <el-dialog
      title="二维码预览"
      :close-on-click-modal="false"
      :visible.sync="visible">
      <div class="text-right">
        <el-button type="primary"  @click="downloadQrCodeHandle()">下载</el-button>
      </div>
      <div class="qr-list" id="qr-list">
        <div class="item" >
          <div :id="demandId" class="qrcode-img"></div>
          <div class="info"><br></div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  // 二维码
  import QRCode from 'qrcodejs2'
  import { downloadFile } from '@/utils'
  import domtoimage from 'dom-to-image'
  import moment from 'moment'
  export default {
    data () {
      return {
        visible: false,
        demandId: '',
        qrBaseUrlCode: '' // 数据字典code值 获取二维码前缀
      }
    },
    components: {

    },
    methods: {
      init (data, qrBaseUrlCode, url) {
        this.visible = true
        this.demandId = data
        this.url = url
        this.qrBaseUrlCode = qrBaseUrlCode
        this.getQrCodeBaseUrl(() => {
          this.$nextTick(() => {
            this.getQrImgSrc(this.demandId)
          })
        })
      },
      initWithPdfUrl (data) {
        this.visible = true
        this.demandId = data
        // 生成pdf预览接口地址，接口回传预览页面
        this.qrBaseUrl = window.SITE_CONFIG['baseUrl'] + '/newest/path?name='
        this.$nextTick(() => {
          this.getQrImgSrc(this.demandId)
        })
      },
      // 下载二维码
      downloadQrCodeHandle () {
        this.$nextTick(() => {
          let node = document.getElementById('qr-list')
          domtoimage.toPng(node, {width: 980, height: 400})
            .then((dataUrl) => {
              console.log(dataUrl)
              downloadFile(dataUrl, '二维码' + moment().format('MMDDHHmm') + '.png')
            })
            .catch(function (error) {
              console.error('oops, something went wrong!', error)
            })
        })
      },
      // 获取二维码路径前缀
      getQrCodeBaseUrl (callback) {
        this.$http({
          url: this.$http.adornUrl('/admin/dict/code'),
          method: 'get',
          params: this.$http.adornParams({'code': this.qrBaseUrlCode})
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.qrBaseUrl = data.obj.value
            if (callback) callback()
          }
        })
      },
      // 生成二维码
      getQrImgSrc (id) {
        document.getElementById(id).innerHTML = ''
        this.$nextTick(() => {
          this.qrUrl = this.qrBaseUrl + id // 二维码地址
          // eslint-disable-next-line no-new
          new QRCode(id, {
            width: 200,
            height: 200,
            text: this.qrUrl,
            colorDark: '#000',
            colorLight: '#fff'
          })
        })
      }
    }
  }
</script>
<style lang="scss" scoped>
.qr-list{
  overflow: hidden;
  padding: 20px 0;
  margin-left: 34%;
 .item{
   width: 20%;
   float: left;
   text-align: center;
   margin-bottom: 20px;
   .qrcode-img{
     width: 132px;
     margin: 0 auto;
   }
   .info{
    padding: 10px 0;
    overflow:hidden;
    text-overflow:ellipsis;
    white-space:nowrap;
     font-weight: bold;
     font-size: 16px;
   }
   img{
     width: 128px;
     height: 128px;
   }
 }
}
.text-right{
  text-align: right;
}
.hidden{
  // display: none;
}
</style>
