<template>
  <div>
    <div class="tinymce-editor">
<!--      &lt;!&ndash; world 导入按钮 &ndash;&gt;-->
<!--      <el-upload class="pos-a" v-if="!disabled"-->
<!--                 :headers="myHeaders"-->
<!--                 ref="upload"-->
<!--                 :action="this.$http.adornUrl('/admin/editor/wordToHtml')"-->
<!--                 :file-list="fileList"-->
<!--                 :before-upload="function (file,fileList){return wordBeforeUpload(file,fileList, 'doc')}"-->
<!--                 :on-success="function (res,file,fileList){return wordUploadSuccess(res,file,fileList, 'doc')}"-->
<!--                 :limit="1">-->
<!--        <label class="my-button">word导入</label>-->
<!--      </el-upload>-->

      <editor v-model="myValue" v-if="isMounted"
              ref="myEditor"
              :init="init"
              :disabled="disabled" >
      </editor>
    </div>


  </div>

</template>
<script>
import tinymce from 'tinymce/tinymce'
import Editor from '@tinymce/tinymce-vue'
import 'tinymce/icons/default/icons'
import 'tinymce/themes/silver'
// 编辑器插件plugins
// 更多插件参考：https://www.tiny.cloud/docs/plugins/
import 'tinymce/plugins/code'//
import 'tinymce/plugins/image'// 插入上传图片插件
import 'tinymce/plugins/media'// 插入视频插件
import 'tinymce/plugins/table'// 插入表格插件
import 'tinymce/plugins/lists'// 列表插件
import 'tinymce/plugins/wordcount'// 字数统计插件
import 'tinymce/plugins/fullscreen'
import 'tinymce/plugins/link'
import Vue from 'vue'
// 全屏
export default {
  components: {
    Editor
  },
  props: {
    uploadServerCode: {
      type: String,
      default: 'LocalServer'
    },
    uploadMedia: {
      type: Boolean,
      default: false
    },
    value: {
      type: String,
      default: ''
    },
    disabled: {
      type: Boolean,
      default: false
    },
    plugins: {
      type: [String, Array],
      default: 'code lists image media table wordcount fullscreen link'
    },
    toolbar: {
      type: [String, Array],
      default: ' undo redo |  formatselect | bold italic forecolor backcolor | alignleft aligncenter alignright alignjustify | bullist numlist outdent indent | lists image media table | removeformat link'
    }
  },
  data () {
    return {
      isMounted: false,
      quillUpdateImg: false,
      myHeaders: {Authorization: Vue.cookie.get('Authorization')},
      fileList: [],
      init: {
        convert_urls: false,
        // relative_urls: false,
        language_url: 'tinymce/zh_CN.js',
        language: 'zh_CN',
        skin_url: 'tinymce/skins/ui/oxide',
        height: 300,
        plugins: this.plugins,  // 父组件传入 或者 填写个默认的插件 要选用什么插件都可以 去官网可以查到
        toolbar: this.toolbar,  // 工具栏 我用到的也就是lists image media table wordcount 这些 根据需求而定
        images_upload_url: this.$http.adornUrl('/admin/oss/upload'), // 上传路径
        // 此处为图片上传处理函数，这个直接用了base64的图片形式上传图片，
        // 如需ajax上传可参考https://www.tiny.cloud/docs/configure/file-image-upload/#images_upload_handler

        // 官网抄的图片上传 项目如果用了vue-resource可以用$http 因为比较懒就没改
        images_upload_handler: (blobInfo, success, failure) => {
          var xhr, formData
          let that = this
          xhr = new XMLHttpRequest()
          xhr.withCredentials = false
          // begin 修改----上传路径-头部
          xhr.open('POST', this.$http.adornUrl('/admin/oss/upload'))
          xhr.setRequestHeader('Authorization', this.$cookie.get('Authorization'))
          // end
          xhr.onload = function () {
            var json
            if (xhr.status !== 200) {
              failure('HTTP Error: ' + xhr.status)
              return
            }
            json = JSON.parse(xhr.responseText)

            if (!json || !json.success || typeof json.obj.path !== 'string') {
              failure('Invalid JSON: ' + xhr.responseText)
              return
            }
            // 修改 拼接图片全路径
            success(that.$http.adornAttachmentUrl(json.obj.path))
          }

          formData = new FormData()
          // end
          console.info(blobInfo)
          formData.append('file', blobInfo.blob(), blobInfo.filename())
          formData.append('serverCode', that.uploadServerCode)
          formData.append('media', that.uploadMedia)
          xhr.send(formData)
        }
      },
      myValue: this.value
    }
  },
  mounted () {
    this.isMounted = true
    tinymce.init({})
    setTimeout(() => {
      window.addEventListener('scroll', this.handleScroll, true)
    }, 200)
  },
  methods: {
    // el-dialog 富文本弹框定位问题处理 TODO
    handleScroll () {
      let _menuDom = document.getElementsByClassName('tox-selected-menu')[0]
      let _toolbar = document.getElementsByClassName('tox-toolbar__overflow')[0]
      if (_menuDom) {
        _menuDom.style.display = 'none'
      }
      if (_toolbar) {
        _toolbar.style.display = 'none'
      }
    },
    wordBeforeUpload (file, fileList, index) {
      let FileExt = file.name.replace(/.+\./, '')
      if (index === 'doc') {
        if (['doc', 'docx'].indexOf(FileExt.toLowerCase()) === -1) {
          this.$message({
            type: '上传失败',
            message: '请上传后缀名为doc、docx的附件！'
          })
          return false
        }
      } else {
        this.$message({
          type: '上传失败',
          message: '请上传正确格式的附件！'
        })
        return false
      }
    },
    wordUploadSuccess (res, file, fileList, index) {
      var that = this
      if (res.success) {
        // eslint-disable-next-line no-useless-escape
        var newContent = res.obj.replace(/src=[\'\"]?([^\'\"]*)[\'\"]?/gi, function (match, capture) {
          var newStr = 'src=" ' + that.$http.adornUrl('') + capture + '"'
          return newStr
        })
        // console.log(newContent)
        that.myValue = newContent
        that.fileList = []
      } else {
        this.$message.error(res.msg + '.请检查文件并重新上传')
        this.fileList = []
      }
    }
  },
  watch: {
    value (newValue) {
      this.myValue = newValue
    },
    myValue (newValue) {
      this.$emit('changeEditorValue', newValue)
    }
  }
}
</script>
<style lang="scss">
/**
* Copyright (c) Tiny Technologies, Inc. All rights reserved.
* Licensed under the LGPL or a commercial license.
* For LGPL see License.txt in the project root for license information.
* For commercial licenses see https://www.tiny.cloud/
*/

.tinymce-editor table {
  border-collapse: collapse;
}
.tinymce-editor table th,
.tinymce-editor table td {
  border: 1px solid #ccc;
  padding: 0.4rem;
}
.tinymce-editor figure {
  display: table;
  margin: 1rem auto;
}
.tinymce-editor figure figcaption {
  color: #999;
  display: block;
  margin-top: 0.25rem;
  text-align: center;
}
.tinymce-editor hr {
  border-color: #ccc;
  border-style: solid;
  border-width: 1px 0 0 0;
}
.tinymce-editor code {
  background-color: #e8e8e8;
  border-radius: 3px;
  padding: 0.1rem 0.2rem;
}
.tinymce-editor .mce-content-body:not([dir=rtl]) blockquote {
  border-left: 2px solid #ccc;
  margin-left: 1.5rem;
  padding-left: 1rem;
}
.tinymce-editor .mce-content-body[dir=rtl] blockquote {
  border-right: 2px solid #ccc;
  margin-right: 1.5rem;
  padding-right: 1rem;
}
.tinymce-editor .tox .tox-statusbar {
  display: none;
}
.h-0{
  height: 0;
}
.tox-tinymce-aux{
  z-index: 3000!important;
}
.tinymce-editor{
  position: relative;
}
.my-button{
  position: absolute;
  top: 0;
  left: 315px;
  cursor: pointer;
  font-weight: 400;
  z-index: 2;
  align-items: center;
  background: 0 0;
  border: 0;
  border-radius: 3px;
  box-shadow: none;
  color: #222f3e;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  height: 34px;
  justify-content: center;
  margin: 2px 0 3px 0;
  outline: 0;
  overflow: hidden;
  padding: 0 4px;
  text-transform: normal;
  width: 80px;
}
.my-button:hover{
  background-color: #DEE0E2;
}
.pos-a{
  position: absolute;
}
</style>
