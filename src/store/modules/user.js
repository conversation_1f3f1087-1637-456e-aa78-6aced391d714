export default {
  namespaced: true,
  state: {
    id: 0,
    name: ''
  },
  mutations: {
    updateId (state, id) {
      state.id = id
    },
    updateName (state, name) {
      state.name = name
    },
    updateNickName (state, name) {
      state.nickName = name
    },
    updatePhone (state, name) {
      state.phone = name
    },
    updateTeamId (state, name) {
      state.teamId = name
    },
    updateManagerCapacity (state, name) {
      state.managerCapacity = name
    },
    updateManagerCapacityName (state, name) {
      state.managerCapacityName = name
    },
    updateOrgCode (state, name) {
      state.orgCode = name
    }
  }
}
