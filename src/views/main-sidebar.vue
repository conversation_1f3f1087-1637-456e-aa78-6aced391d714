<template>
  <aside class="site-sidebar" :class="'site-sidebar--' + sidebarLayoutSkin">
    <div class="site-sidebar__inner">
      <el-menu
        :default-active="menuActiveName || 'home'"
        :collapse="sidebarFold"
        :collapseTransition="false"
        class="site-sidebar__menu">
        <el-menu-item index="home" @click="$router.push({ name: 'home' })">
          <icon-svg name="shouye" class="site-sidebar__menu-icon"></icon-svg>
          <span slot="title">首页</span>
        </el-menu-item>
        <!--<el-submenu index="demo">-->
          <!--<template slot="title">-->
            <!--<icon-svg name="shoucang" class="site-sidebar__menu-icon"></icon-svg>-->
            <!--<span>demo</span>-->
          <!--</template>-->
          <!--<el-menu-item index="demo-echarts" @click="$router.push({ name: 'demo-echarts' })">-->
            <!--<icon-svg name="tubiao" class="site-sidebar__menu-icon"></icon-svg>-->
            <!--<span slot="title">echarts</span>-->
          <!--</el-menu-item>-->
          <!--<el-menu-item index="demo-ueditor" @click="$router.push({ name: 'demo-ueditor' })">-->
            <!--<icon-svg name="editor" class="site-sidebar__menu-icon"></icon-svg>-->
            <!--<span slot="title">ueditor</span>-->
          <!--</el-menu-item>-->
        <!--</el-submenu>-->
        <sub-menu
          v-for="menu in menuList"
          :key="menu.menuId"
          :menu="menu"
          :dynamicMenuRoutes="dynamicMenuRoutes">
        </sub-menu>
      </el-menu>
    </div>
  </aside>
</template>

<script>
  import SubMenu from './main-sidebar-sub-menu'
  import { isURL } from '@/utils/validate'
  export default {
    data () {
      return {}
    },
    components: {
      SubMenu
    },
    computed: {
      sidebarLayoutSkin: {
        get () { return this.$store.state.common.sidebarLayoutSkin }
      },
      sidebarFold: {
        get () { return this.$store.state.common.sidebarFold }
      },
      menuList: {
        get () { return this.$store.state.common.menuList },
        set (val) { this.$store.commit('common/updateMenuList', val) }
      },
      menuActiveName: {
        get () { return this.$store.state.common.menuActiveName },
        set (val) { this.$store.commit('common/updateMenuActiveName', val) }
      },
      mainTabs: {
        get () { return this.$store.state.common.mainTabs },
        set (val) { this.$store.commit('common/updateMainTabs', val) }
      },
      mainTabsActiveName: {
        get () { return this.$store.state.common.mainTabsActiveName },
        set (val) { this.$store.commit('common/updateMainTabsActiveName', val) }
      },
      dynamicMenuRoutes: {
        get () { return this.$store.state.common.dynamicMenuRoutes }
      }
    },
    watch: {
      $route: 'routeHandle'
    },
    created () {
      // 初始化菜单数据
      this.$store.commit('common/initMenuData')
      this.routeHandle(this.$route)
    },
    methods: {
      // 路由操作
      async routeHandle (route) {
        if (route.meta.isTab) {
          // tab选中, 不存在先添加
          var tab = this.mainTabs.filter(item => item.name === route.name)[0]
          if (!tab) {
            if (route.meta.isDynamic) {
              route = this.dynamicMenuRoutes.filter(item => item.name === route.name)[0]
              if (!route) {
                return console.error('未能找到可用标签页!')
              }
            }
            let type = isURL(route.meta.iframeUrl) ? 'iframe' : 'module'
            let iframeUrl = route.meta.iframeUrl || ''
            //外部页面带个token过去
            if(type === 'iframe'){
              if(route.meta.iframeUrl.startsWith('https://wmyq.sipac.gov.cn')) {
                let {data} = await this.$http({
                  url: this.$http.adornUrl('/admin/sso/wmyq/login'),
                  method: 'post',
                  params: this.$http.adornParams()
                })
                if(iframeUrl && data.code === 0){
                  iframeUrl = iframeUrl + (iframeUrl.includes('?') ? '&' : '?') + 'token=' + data.obj['access_token']
                }
              } else {
                let {data} = await this.$http({
                  url: this.$http.adornUrl('/admin/user/tokenToUserInfoCode'),
                  method: 'get',
                  params: this.$http.adornParams()
                })
                if(iframeUrl && data.code === 0){
                  iframeUrl = iframeUrl + (iframeUrl.includes('?') ? '&' : '?') + 'userInfoCode=' + data.obj
                }
              }
            }
            tab = {
              menuId: route.meta.menuId || route.name,
              name: route.name,
              title: route.meta.title,
              type: type,
              iframeUrl: iframeUrl,
              params: route.params,
              query: route.query
            }
            this.mainTabs = this.mainTabs.concat(tab)
          }
          this.menuActiveName = tab.menuId + ''
          this.mainTabsActiveName = tab.name
        }
      }
    }
  }
</script>
