<template>
  <el-dialog title="强制修改密码" :visible.sync="visible" :append-to-body="true"   :before-close="close" :close-on-click-modal="false" :close-on-press-escape="false">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmit()"
             label-width="90px">
      <el-form-item label-width="20px">
        <span style="color: red;">*注意：首次登录，请重新设置密码！</span>
      </el-form-item>
      <el-form-item label="用户账号:">
        <span>{{ dataForm.username }}</span>
      </el-form-item>
      <el-form-item label="新密码:" prop="newPassword">
        <el-input type="password" v-model="dataForm.newPassword"></el-input>
      </el-form-item>
      <el-form-item label="确认密码:" prop="confirmPassword">
        <el-input type="password" v-model="dataForm.confirmPassword"></el-input>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="close">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import {clearLoginInfo} from '@/utils'

export default {
  data() {
    var validateConfirmPassword = (rule, value, callback) => {
      if (this.dataForm.newPassword !== value) {
        callback(new Error('确认密码与新密码不一致'))
      } else {
        callback()
      }
    };
    var validateNewPassword = (rule, value, callback) => {
      let reg = /(?=.*[A-Z])(?=.*[a-z])(?=.*[0-9])(?=.*[\W_]).{8,20}/
      if (value === '') {
        callback(new Error("新密码不能为空"));
      } else {
        if (!reg.test(value)) {
          callback(new Error('密码须包含大小写数字特殊字符且长度8~20位'))
        } else {
          callback()
        }
      }
    }
    return {
      visible: false,
      needLogout: false,
      dataForm: {
        password: '',
        newPassword: '',
        confirmPassword: '',
        username: ''
      },
      dataRule: {
        newPassword: [
          {required: true, validator: validateNewPassword, trigger: 'blur'}
        ],
        confirmPassword: [
          {required: true, message: '确认密码不能为空', trigger: 'blur'},
          {validator: validateConfirmPassword, trigger: 'blur'}
        ]
      }
    }
  },
  computed: {
    mainTabs: {
      get() {
        return this.$store.state.common.mainTabs
      },
      set(val) {
        this.$store.commit('common/updateMainTabs', val)
      }
    }
  },
  methods: {
    // 初始化
    init(username, needLoginOut = false) {
      this.visible = true
      this.needLogout = needLoginOut
      this.dataForm.username = username
      this.$nextTick(() => {
        this.$refs['dataForm']?.resetFields()
      })
    },
    // 关闭
    close() {
      this.visible = false
      clearLoginInfo()
      this.$router.replace({name: 'login'})
    },
    // 表单提交
    dataFormSubmit() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.$http({
            url: this.$http.adornUrl('/admin/user/forceUpdatePwd'),
            method: 'post',
            params: this.$http.adornParams({
              'username': this.dataForm.username,
              'newPassWord': this.dataForm.newPassword
            })
          }).then(({data}) => {
            if (data && data.code === 0) {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 1500,
                onClose: () => {
                  this.visible = false
                  this.$nextTick(() => {
                    if (!this.needLogout) {
                      return
                    }
                    this.mainTabs = []
                    clearLoginInfo()
                    this.$router.replace({name: 'login'})
                  })
                }
              })
            } else {
              this.$message.error(data.msg)
            }
          })
        }
      })
    }
  }
}
</script>

