<template>
  <div
      class="site-wrapper"
      :class="{ 'site-sidebar--fold': sidebarFold }"
      v-loading.fullscreen.lock="loading"
      element-loading-text="拼命加载中">
    <template v-if="!loading">
      <main-navbar/>
      <main-sidebar/>
      <div class="site-content__wrapper" :style="{ 'min-height': documentClientHeight + 'px' }">
        <main-content v-if="!$store.state.common.contentIsNeedRefresh"/>
      </div>
    </template>
      <notice v-if="noticeVisible" ref="notice"></notice>
  </div>
</template>

<script>
import MainNavbar from './main-navbar'
import MainSidebar from './main-sidebar'
import MainContent from './main-content'
import Notice from './notice'

export default {
  provide() {
    return {
      // 刷新
      refresh() {
        this.$store.commit('common/updateContentIsNeedRefresh', true)
        this.$nextTick(() => {
          this.$store.commit('common/updateContentIsNeedRefresh', false)
        })
      }
    }
  },
  data() {
    return {
      loading: true,
      noticeVisible: false,

    }
  },
  components: {
    MainNavbar,
    MainSidebar,
    MainContent,
    Notice
  },
  computed: {
    documentClientHeight: {
      get() {
        return this.$store.state.common.documentClientHeight
      },
      set(val) {
        this.$store.commit('common/updateDocumentClientHeight', val)
      }
    },
    sidebarFold: {
      get() {
        return this.$store.state.common.sidebarFold
      }
    },
    userId: {
      get() {
        return this.$store.state.user.id
      },
      set(val) {
        this.$store.commit('user/updateId', val)
      }
    },
    userName: {
      get() {
        return this.$store.state.user.name
      },
      set(val) {
        this.$store.commit('user/updateName', val)
      }
    },
    nickName: {
      get() {
        return this.$store.state.user.nickName
      },
      set(val) {
        this.$store.commit('user/updateNickName', val)
      }
    },
    phone: {
      get() {
        return this.$store.state.user.phone
      },
      set(val) {
        this.$store.commit('user/updatePhone', val)
      }
    },
    managerCapacity: {
      get() {
        return this.$store.state.user.managerCapacity
      },
      set(val) {
        this.$store.commit('user/updateManagerCapacity', val)
      }
    },
    teamId: {
      get() {
        return this.$store.state.user.teamId
      },
      set(val) {
        this.$store.commit('user/updateTeamId', val)
      }
    },
    managerCapacityName: {
      get() {
        return this.$store.state.user.managerCapacityName
      },
      set(val) {
        this.$store.commit('user/updateManagerCapacityName', val)
      }
    },
    orgCode: {
      get() {
        return this.$store.state.user.orgCode
      },
      set(val) {
        this.$store.commit('user/updateOrgCode', val)
      }
    }
  },
  created() {
    this.getUserInfo()

  },
  mounted() {
    this.resetDocumentClientHeight()
  },
  methods: {
    // 重置窗口可视高度
    resetDocumentClientHeight() {
      this.documentClientHeight = document.documentElement['clientHeight']
      window.onresize = () => {
        this.documentClientHeight = document.documentElement['clientHeight']
      }
    },
    // 获取当前管理员信息
    getUserInfo() {
      this.$http({
        url: this.$http.adornUrl('/admin/user/loginUser'),
        method: 'get',
        params: this.$http.adornParams()
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.loading = false
          this.userId = data.obj.id
          this.userName = data.obj.username
          this.managerCapacityName = data.obj.managerCapacityName
          this.managerCapacity = data.obj.managerCapacity
          this.phone = data.obj.mobile
          this.teamId = data.obj.teamId
          this.nickName = data.obj.nickName
          this.orgCode = data.obj.orgCode
          this.getNoticeFlag()
        } else {
          this.$router.push({name: 'login'})
        }
      })
    },
    getNoticeFlag() {
          this.$http({
              url: this.$http.adornUrl('/admin/user/noticeFlag'),
              method: 'get',
              params:  this.$http.adornParams({ userId: this.userId })
          }).then(({data}) => {
              if (data && data.code === 0 && data.obj === true ) {
                  this.noticeHandle()
              } else {

              }
          })
      },
    noticeHandle() {
          this.noticeVisible = true
          this.$nextTick(() => {
              this.$refs.notice.init()
          })
      },

  }
}
</script>
