<template>
    <el-dialog
            title="温馨提示"
            :close-on-click-modal="false"
            width="70%"
            height="600px"
            append-to-body
            :visible.sync="visible">
        <el-form :model="dataForm" ref="dataForm"
                 label-width="140px">
            <div v-html="dataForm.content" class="dialog-body"></div>
        </el-form>
        <span slot="footer" class="dialog-footer">
       <el-button @click="visible = false">确定</el-button>

    </span>
    </el-dialog>
</template>

<script>
export default {
    data() {
        return {
            visible: false,
            dataForm: {
                content: null
            }
        }
    },
    components: {},
    methods: {
        init() {
            this.visible = true
            this.getContent()
        },
        getContent() {
            this.$http({
                url: this.$http.adornUrl('/api/sys/description/getByCode'),
                method: 'get',
                params: this.$http.adornParams({code: 'MANAGEMENT-RULES'})
            }).then(({data}) => {
                if (data && data.code === 0 ) {
                    this.dataForm.content = data.obj.content
                } else {

                }
            })
        },
    }
}
</script>
<style scoped>
.dialog-body {
    height: 555px; /* 设置内容高度 */
    overflow-y: auto; /* 超出滚动 */
}
</style>