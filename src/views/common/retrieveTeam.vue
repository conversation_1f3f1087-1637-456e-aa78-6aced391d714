<template>
  <el-dialog title="找回团队管理员权限" :visible.sync="visible" :append-to-body="true" width="35%" :close-on-click-modal="false">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmit()"
             label-width="100px" style="margin: 10px">
      <!--      <el-form-item label="手机号" prop="phone">-->
      <!--        <el-input v-model="dataForm.phone" placeholder="请输入手机号，此手机号对应的志愿者用户需要通过实名"></el-input>-->
      <!--      </el-form-item>-->
      <!--      <el-form-item label="短信验证码" prop="code">-->
      <!--        <el-input v-model="dataForm.code" placeholder="短信验证码">-->
      <!--          <el-button type="text" @click="getVerificationCode()" slot="suffix" :disabled="countdown > 0">-->
      <!--            {{ countdown > 0 ? `${countdown} 秒后重新发送` : '获取验证码' }}-->
      <!--          </el-button>-->
      <!--        </el-input>-->
      <!--      </el-form-item>-->
      <el-form-item prop="usernameOrEmailAddress" label="旧平台账号">
        <el-input v-model="dataForm.usernameOrEmailAddress" placeholder="旧平台账号"></el-input>
      </el-form-item>
<!--      <el-form-item label="旧平台密码" prop="password">-->
<!--        <el-input v-model="dataForm.password" placeholder="旧平台密码" type="password"></el-input>-->
<!--      </el-form-item>-->
      <div v-if="this.qrCodeLoading" id="qrCodeDiv" ref="qrCodeDiv"
           style="display: flex; flex-direction: column; align-items: center;height: 80%;padding-bottom: 30px">
        <span
            style="margin: 30px; color: red; font-size: medium">检测到您还不是志愿者或尚未实名认证！<br/>请先扫描下方二维码进入小程序注册完善信息或实名认证。</span>
        <img :src="qrCodeUrl" style="width: 30%;margin:0px 50px 20px 50px" crossOrigin="anonymous"/>
        <span style="font-size: 8px;white-space: pre-line">温馨提示：<span
            style="font-size: 8px;font-weight: bold;white-space: pre-line;color: red">注册完善信息或实名认证后，再次点击【确定】进行团队管理员找回</span></span>
      </div>
    </el-form>
    <span slot="footer" class="dialog-footer">
			<el-button @click="visible = false">取消</el-button>
			<el-button type="primary" @click="dataFormSubmit()" :loading="dataListLoading">确定</el-button>
		</span>
  </el-dialog>
</template>
<script>
import moment from "moment";
import {encrypt} from "@/utils/cryptoUtil";
import _ from 'lodash'

export default {
  data() {
    return {
      visible: false,
      countdown: 0,
      dataList: [],
      qrCodeUrl: '',
      qrCodeLoading: false,
      dataListLoading: false,
      dataForm: {
        testCode: 800,
        password: '',
        phone: null,
        code: null,
        usernameOrEmailAddress: null
      },
      dataRule: {
        phone: [{
          required: true,
          message: '手机号码不能为空',
          trigger: 'blur'
        }],
        // password: [{
        //   required: true,
        //   message: '老系统密码不能为空',
        //   trigger: 'blur'
        // }],
        usernameOrEmailAddress: [{
          required: true,
          message: '老系统账号不能为空',
          trigger: 'blur'
        }],
        code: [{
          required: true,
          message: '短信验证码不能为空',
          trigger: 'blur'
        }]
      },
      goodsCountError: null
    }
  },
  methods: {
    init(id) {
      this.qrCodeLoading = false
      this.qrCodeText = ''
      this.qrCodeUrl = ''
      this.$refs['dataForm']?.resetFields()
      let content = `<span style="color: red;">2023年9月前已注册的团队如无法登录需完善信息后方可登录， 需完善信息详见说明：<br>
                          https://www.yuque.com/ccbb/gxosh6/bz1yacpl8ogncmni?singleDoc#  完善信息后致电0512-68235283完成团队找回，如有疑问请致电联系<br>
                        </span>`
      this.$alert(content, '旧平台团队找回指引', {
        dangerouslyUseHTMLString: true,
        showConfirmButton:false,
        callback: () => {
          this.visible = false
        }
      })
    },
    // 展示二维码
    showQrCode() {
      // 调用接口生成
      this.$http({
        url: this.$http.adornUrl('/api/retrieve/team/createQrCode'),
        method: 'get',
        params: this.$http.adornParams({
          'appId': window.SITE_CONFIG['wechatAppID']
        })
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.qrCodeUrl = this.$http.adornAttachmentUrl(data.obj)
          this.qrCodeLoading = true
        } else {
          this.$message.error(data.msg)
          this.qrCodeLoading = false
        }
      }).catch(() => {
        this.qrCodeLoading = false
      })
    },
    getVerificationCode() {
      var valid = this.dataForm.phone !== ''
      if (!valid) {
        this.$message.error('请输入手机号')
      }
      if (valid) {
        this.countdown = 60
        const timer = setInterval(() => {
          if (this.countdown > 0) {
            this.countdown--
          } else {
            clearInterval(timer)
          }
        }, 1000)
        this.canGetVerifyCode = false
        this.$http({
          url: this.$http.adornUrl('/verify/verifyCode'),
          method: 'post',
          params: this.$http.adornParams({
            'mobile': this.dataForm.phone,
            'key': 'CHECK_PHONE',
          })
        }).then(({
                   data
                 }) => {
          if (!data || data.code !== 0) {
            this.$message.error(data.msg)
          }
        })
      }
    },
    // 表单提交
    dataFormSubmit() {
      this.$refs['dataForm'].validate((valid) => {
        this.dataListLoading = true
        let submitForm = _.cloneDeep(this.dataForm)
        submitForm.password = encrypt(submitForm.password, window.SITE_CONFIG['aseKey'])
        if (valid) {
          this.$http({
            url: this.$http.adornUrl('/api/retrieve/team/retrieveTeam'),
            method: 'post',
            data: this.$http.adornData(submitForm)
          }).then(({data}) => {
            if (data && data.code === 0) {
              if (data.obj.retrieveResultCode !== 800) {
                this.showQrCode()
                this.dataListLoading = false
              } else {
                this.dataListLoading = false
                this.$emit('retrieveSubmitCallback', data.obj)
                this.visible = false
              }
            } else {
              this.dataListLoading = false
              this.$message.error(data.msg)
            }
          })
        } else {
          this.dataListLoading = false
        }
      })
    }
  }
}
</script>