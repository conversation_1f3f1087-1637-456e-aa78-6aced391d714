<template>
  <div class="login-wait-page">
    <div class="form-container" v-loading="loginLoading">
<!--      <el-form :model="dataForm" ref="dataForm" label-width="80px" class="centered-form">-->
<!--        <el-row :gutter="20">-->
<!--          <el-col :span="24">-->
<!--            <el-form-item label="code：" prop="code">-->
<!--              <el-input v-model="dataForm.code"></el-input>-->
<!--            </el-form-item>-->
<!--          </el-col>-->
<!--        </el-row>-->
<!--        <el-row :gutter="20">-->
<!--          <el-col :span="24">-->
<!--            <el-form-item label="token：" prop="token">-->
<!--              <el-input v-model="dataForm.token"></el-input>-->
<!--            </el-form-item>-->
<!--          </el-col>-->
<!--        </el-row>-->
<!--      </el-form>-->
    </div>
  </div>
</template>

<script>
import {encrypt} from "@/utils/cryptoUtil";

export default {
  name: 'BlankPage',
  data () {
    return {
      dataForm: {
        code: null
      },
      loginLoading: false
    }
  },
  created() {
    this.processLogin();
  },

  methods: {
    processLogin() {
      this.loginLoading = true;
      this.dataForm.code = this.$route.query.code
      console.log(this.dataForm.code)
      if (this.dataForm.code && this.dataForm.code !== '') {
        this.$http({
          url: this.$http.adornUrl("/fykj/zsqCodeToOneCode"),
          method: "get",
          params: this.$http.adornParams({
            code: this.dataForm.code
          })
        }).then(({ data }) => {
          if (data && data.code === 0) {
            const oneCodeMap = data.obj
            this.$http({
              url: this.$http.adornUrl('/fykj/login'),
              method: 'post',
              data: this.$http.adornData({
                'username': oneCodeMap.username,
                'password': encrypt(oneCodeMap.oneCode, window.SITE_CONFIG['aseKey']),
                'loginType': 'one_code'
              })
            }).then(({data}) => {
              if (data && data.code === 0) {
                this.loginLoading = false
                this.$message.info('登录成功')
                // oauth2用的token
                this.$cookie.set(
                    "Authorization",
                    data.obj.token_type + " " + data.obj.access_token
                );
                // 把token对象放在sessionStorage中
                sessionStorage.setItem("oAuthToken", data.obj);
                this.$router.replace({ name: "home" });
              } else {
                this.submitDisabled = false
                this.$message.error(data.msg)
              }
            })
          } else {
            this.loginLoading = false
            this.$message.error(data.msg);
          }
        });
      } else {
        this.loginLoading = false
        this.$message.error('No code data received');
        console.error('No code data received');
      }
    }
  }
}
</script>

<style scoped lang="scss">
.login-wait-page {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh; /* 确保容器占据整个视口高度 */
  width: 100%;
}

.form-container {
  width: 80%; /* 保持你原来的宽度设置 */
}

.centered-form {
  /* 如果需要可以添加额外的表单样式 */
}
</style>
