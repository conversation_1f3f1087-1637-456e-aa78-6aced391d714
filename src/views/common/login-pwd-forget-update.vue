<template>
  <el-dialog :close-on-click-modal="false" :visible.sync="visible" width="30%" class="pwd_forget_dialog">
    <span slot="title" class="dialog-title" style="font-size: 18px; font-weight: 400; display: flex;width: 100%; justify-content: space-between; align-items: center;">
      <span>忘记密码</span>
      <span style="position: absolute;left: 50%;transform: translateX(-50%);font-size: 16px;margin-top: 20px">{{ step === 1 ? '信 息 验 证' : '密 码 修 改' }}</span>
    </span>
    <el-form v-if="step === 1" :model="validForm" :rules="validFormRule" ref="validForm" label-width="100px" class="pwd_forget_form">
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="用户名" prop="username">
            <el-input v-model="validForm.username" placeholder="请输入用户名"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="手机号" prop="mobile">
            <el-input v-model="validForm.mobile" placeholder="请输入手机号"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="图形验证码" prop="captcha">
            <el-input v-model="validForm.captcha" placeholder="请输入图形验证码">
              <img :src="captchaPath" @click="getCaptcha()" slot="suffix">
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="短信验证码" prop="vCode">
            <el-input v-model="validForm.vCode" placeholder="请输入短信验证码">
              <el-button :disabled="countdown > 0" type="text" @click="getSmsVCode()" slot="suffix">{{ countdown > 0 ? `${countdown}秒后重新获取` : '获取验证码' }}</el-button>
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <el-form v-if="step === 2" :model="updateForm" :rules="updateFormRule" ref="updateForm" label-width="80px" class="pwd_forget_form">
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="新密码" prop="newPassword">
            <el-input v-model="updateForm.newPassword" show-password placeholder="请输入新密码"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="密码确认" prop="confirmPassword">
            <el-input v-model="updateForm.confirmPassword" show-password placeholder="请确认密码"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button v-if="step === 1" type="primary" @click="nextStep()">下一步</el-button>
      <el-button v-if="step === 2" type="primary" @click="submit()">提交</el-button>
    </span>
  </el-dialog>
</template>

<script>
import {uuid} from "@/utils";
import {encrypt} from '@/utils/cryptoUtil'

export default {
  data () {
    const validateConfirmPassword = (rule, value, callback) => {
      if (this.updateForm.newPassword !== value) {
        callback(new Error('确认密码与新密码不一致'))
      } else {
        callback()
      }
    };
    const validateNewPassword = (rule, value, callback) => {
      let reg = /(?=.*[A-Z])(?=.*[a-z])(?=.*[0-9])(?=.*[\W_]).{8,20}/
      if (!value || value === '') {
        callback(new Error("新密码不能为空"));
      } else {
        if (!reg.test(value)) {
          callback(new Error('密码须包含大小写数字特殊字符且长度8~20位'))
        } else {
          callback()
        }
      }
    }
    return {
      step: 1,
      visible: false,
      captchaPath: null,
      countdown: 0,
      timer: null,
      validForm: {
        username: null,
        mobile: null,
        uuid: null,
        captcha: null,
        vCode: null
      },
      validResUUID: '123',
      updateForm: {
        username: null,
        mobile: null,
        newPassword: null,
        confirmPassword: null,
        data: null
      },
      validFormRule: {
        username: [
          { required: true, message: '用户名不能为空', trigger: 'change' }
        ],
        mobile: [
          { required: true, message: '手机号不能为空', trigger: 'change' }
        ],
        captcha: [
          { required: true, message: '图片验证码不能为空', trigger: 'change' }
        ],
        vCode: [
          { required: true, message: '短信验证码不能为空', trigger: 'change' }
        ]
      },
      updateFormRule: {
        newPassword: [
          {required: true, validator: validateNewPassword, trigger: 'change'}
        ],
        confirmPassword: [
          {required: true, message: '确认密码不能为空', trigger: 'change'},
          {validator: validateConfirmPassword, trigger: 'change'}
        ]
      }
    }
  },
  // 组件销毁时清除定时器
  beforeDestroy() {
    if (this.timer) {
      clearInterval(this.timer);
    }
  },
  methods: {
    init () {
      this.step = 1
      this.validResUUID = null
      this.visible = true
      this.$refs['validForm']?.resetFields()
      this.getCaptcha()
    },
    getCaptcha() {
      this.validForm.uuid = uuid()
      this.captchaPath = this.$http.adornUrl(`/fykj/captcha?uuid=${this.validForm.uuid}`)
    },
    getSmsVCode(){
      let formValidErr = false;
      this.$refs['validForm'].validateField(['mobile', 'captcha'], err => {
        if (err) {
          formValidErr = true
          return
        }
      });
      if (!formValidErr) {
        this.$http({
          url: this.$http.adornUrl('/verify/verifyCodeNeedCaptchaValidate'),
          method: 'post',
          params: this.$http.adornParams({
            'mobile': this.validForm.mobile,
            'key': 'PWD_FORGET_UPDATE',
            'uuid': this.validForm.uuid,
            'captcha': this.validForm.captcha
          })
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.$message.success('验证码已发送');
            this.startCountdown();
          } else {
            this.$message.error(data.msg)
          }
        })
      }
    },
    startCountdown() {
      this.countdown = 60; // 设置倒计时60秒
      this.timer = setInterval(() => {
        this.countdown--;
        if (this.countdown <= 0) {
          clearInterval(this.timer);
        }
      }, 1000);
    },
    nextStep () {
      this.$refs['validForm'].validate((valid) => {
        if (valid) {
          this.$http({
            url: this.$http.adornUrl('/fykj/passwordForgetValidate'),
            method: 'post',
            data: this.$http.adornData({
              'username': this.validForm.username,
              'mobile': this.validForm.mobile,
              'smsVerificationCode': this.validForm.vCode
            })
          }).then(({data}) => {
            if (data && data.code === 0) {
              this.validResUUID = data.obj
              this.step = 2
              this.$nextTick(() => {
                this.$refs['updateForm']?.resetFields()
              })
            } else {
              this.validResUUID = null
              this.$message.error(data.msg)
            }
          })
        }
      })
    },
    submit () {
      this.$refs['updateForm'].validate((valid) => {
        if (valid) {
          let data = JSON.stringify({
            uuid: this.validResUUID,
            username: this.validForm.username,
            mobile: this.validForm.mobile,
            password: this.updateForm.newPassword,
            timestamp: new Date().getTime()
          })
          this.$http({
            url: this.$http.adornUrl('/fykj/passwordForgetUpdate'),
            method: 'post',
            data: this.$http.adornData({
              'username': this.validForm.username,
              'mobile': this.validForm.mobile,
              'data': encrypt(data, 'FengYunSciTecEnt')
            })
          }).then(({data}) => {
            if (data && data.code === 0) {
              this.$message.success('密码修改成功');
              this.visible = false;
            } else {
              this.$message.error(data.msg)
            }
          })
        }
      });
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep .el-dialog {
  border-radius: 21px 21px 21px 21px !important;
}
::v-deep .pwd_forget_form {
  .el-input input {
    border-bottom: 1px solid #e6dfdf;
  }

  .el-input__inner {
    padding-left: 30px;
    border: none;
    border-radius: 0;
    background: transparent !important;
    color: black;
    font-weight: bolder;
  }

  .el-input__inner::placeholder {
    color: #818796;
  }

  .icon-input {
    position: absolute;
    top: 10px;
    left: 5px;
  }

  .el-input__suffix {
    right: 0px;

    .el-input__suffix-inner img {
      height: 100%;
      padding-bottom: 3px;
      border-radius: 5%;
    }
  }

  .el-form-item__error {
    padding-left: 30px;
    font-weight: bolder;
  }
}
</style>
