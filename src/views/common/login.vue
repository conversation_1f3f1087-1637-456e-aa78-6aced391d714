<template>
  <div class="site-wrapper site-page--login" id="login_page">
    <div class="site-content__wrapper">
      <div class="site-content">
        <!--        <div class="brand-info">-->
        <!--          <h2 class="brand-info__text">新时代文明实践志愿服务平台</h2>-->
        <!--          <p class="brand-info__intro">新时代文明实践志愿服务平台</p>-->
        <!--        </div>-->
        <div class="login-main">
          <h2 style="line-height: 10px;">苏州工业园区</h2>
          <h2>新时代文明实践中心建设工作服务平台</h2>
          <el-tabs v-model="loginType" @tab-click="handleClick" stretch>
            <el-tab-pane class="login-title" label="短信登录" name="sms_code"><h3/></el-tab-pane>
            <el-tab-pane class="login-title" label="密码登录" name="password"><h3/></el-tab-pane>
            <el-tab-pane class="login-title" label="扫码登录" name="wx_qrcode"><h3/></el-tab-pane>
            <el-tab-pane v-if="nodeEnv !== 'prod'" class="login-title" label="政务微信登录" name="gov_wx_qrcode"><h3/></el-tab-pane>
          </el-tabs>
          <!--          <h3>管理员登录</h3>-->
          <el-form v-show="loginType === 'password'" class="login-form" :model="dataForm" :rules="dataRule"
                   ref="dataForm"
                   @keyup.enter.native="dataFormSubmit()" status-icon>
            <h6 v-if="this.unionIdBindKey" style="color:red; margin: 0px 0px 20px 0px">
              您的微信账号暂未绑定用户，请使用账号密码登录进行绑定，之后可直接扫码登录</h6>
            <el-form-item prop="userName">
              <el-input v-model="dataForm.userName" placeholder="用户名"></el-input>
              <img src="~@/assets/img/icon-user-new.png" class="icon-input" alt="" style="width: 21px; height: 21px">
            </el-form-item>
            <el-form-item prop="password">
              <el-input v-model="dataForm.password" type="password" placeholder="密码">
                <el-button type="text" @click="passwordForget" alt="" slot="suffix">忘记密码</el-button>
              </el-input>
              <img src="~@/assets/img/icon-password-new.png" class="icon-input" alt=""
                   style="width: 21px; height: 21px">
            </el-form-item>
            <el-form-item prop="captcha">
              <!--              <el-row>-->
              <!--                <el-col :span="14">-->
              <el-input v-model="dataForm.captcha" placeholder="验证码">
                <img :src="captchaPath" @click="getCaptcha()" alt="" slot="suffix">
              </el-input>
              <img src="~@/assets/img/icon-valid-code-new.png" class="icon-input" alt=""
                   style="width: 21px; height: 21px">
              <!--                </el-col>-->
              <!--                <el-col :span="10" class="login-captcha">-->
              <!--                  <img :src="captchaPath" @click="getCaptcha()" alt="">-->
              <!--                </el-col>-->
              <!--              </el-row>-->
            </el-form-item>
            <el-form-item prop="csrLogin">
              <el-checkbox v-model="dataForm.csrLogin" label="企业责任联盟登录" style="float: left"></el-checkbox>
              <el-button type="text" style="float: right" @click="csrRegister">企业注册</el-button>
            </el-form-item>
            <el-form-item>
              <el-button :disabled="submitDisabled" class="login-btn-submit" type="primary" @click="dataFormSubmit()"
                         style="background-color: #42aefa; border-width:2px; border-color: #42aefa; opacity: 0.9">
                登录
              </el-button>
              <el-button class="find_team" style="color: red;font-weight: bold;margin-top: 10px" type="text"
                         @click="retrieveTeam()">
                点我找回旧平台所管理的团队
              </el-button>
            </el-form-item>
            <!--            <el-form-item>-->
            <!--              <el-button class="login-btn-change" type="text" @click="changeLoginType()">账号或者手机号登录</el-button>-->
            <!--            </el-form-item>-->
          </el-form>
          <el-form v-show="loginType === 'sms_code'" class="login-form" :model="dataForm" :rules="dataRule"
                   ref="dataForm"
                   @keyup.enter.native="dataFormSubmit()" status-icon
                   :style="this.unionIdBindKey ? {'padding-top': '7px'} : {'padding-top': '50px'}">
            <h6 v-if="this.unionIdBindKey" style="color:red; ">您的微信账号暂未绑定用户，请使用账号密码登录进行绑定，之后可直接扫码登录</h6>
            <el-form-item prop="userName">
              <el-input v-model="dataForm.userName" placeholder="手机号"></el-input>
              <img src="~@/assets/img/icon-user-new.png" class="icon-input" alt="" style="width: 21px; height: 21px">
            </el-form-item>
            <el-form-item prop="validateCode" style="padding-top: 40px;margin-bottom: 25px">
              <el-input v-model="dataForm.validateCode" placeholder="短信验证码">
                <el-button type="text" @click="getVerificationCode()" slot="suffix" :disabled="countdown > 0">
                  {{ countdown > 0 ? `${countdown} 秒后重新发送` : '获取验证码' }}
                </el-button>
              </el-input>
              <img src="~@/assets/img/icon-valid-code-new.png" class="icon-input" alt=""
                   style="width: 21px; height: 21px">
            </el-form-item>
            <el-form-item prop="csrLogin" style="margin-bottom: 25px">
              <el-checkbox v-model="dataForm.csrLogin" label="企业责任联盟登录" style="float: left"></el-checkbox>
              <el-button type="text" style="float: right" @click="csrRegister">企业注册</el-button>
            </el-form-item>
            <el-form-item>
              <el-button :disabled="submitDisabled" class="login-btn-submit" type="primary" @click="dataFormSubmit()"
                         style="background-color: #42aefa;  border-width:2px; border-color: #42aefa; opacity: 0.9">
                登录
              </el-button>
              <el-button class="find_team" style="color: red;font-weight: bold;margin-top: 10px" type="text"
                         @click="retrieveTeam()">
                点我找回旧平台所管理的团队
              </el-button>
            </el-form-item>
            <!--            <el-form-item>-->
            <!--              <el-button class="login-btn-change" type="text" @click="changeLoginType()">账号或者手机号登录</el-button>-->
            <!--            </el-form-item>-->
          </el-form>
          <el-form v-show="loginType === 'wx_qrcode'" class="login-form" :model="dataForm" :rules="dataRule"
                   style="margin-top: -20px"
                   ref="dataForm"
                   @keyup.enter.native="dataFormSubmit()" status-icon>
            <div id="wechat-qrcode"></div>
            <!--            <el-form-item>-->
            <!--              <el-button class="login-btn-change" type="text" @click="changeLoginType()">切换登录方式</el-button>-->
            <!--            </el-form-item>-->
          </el-form>

          <el-form v-show="loginType === 'gov_wx_qrcode'" class="login-form" :model="dataForm" :rules="dataRule"
                   style="margin-top: -20px"
                   ref="dataForm"
                   @keyup.enter.native="dataFormSubmit()" status-icon>
            <div id="gov-wechat-qrcode"></div>
          </el-form>

        </div>
      </div>
    </div>
    <retrieve-team v-if="addOrUpdateVisible" ref="retrieveTeam"
                   @retrieveSubmitCallback="retrieveSubmitCallback"></retrieve-team>
    <team-retrieve-list v-if="teamRetrieveListVisible" ref="teamRetrieveList"></team-retrieve-list>
    <pwd-forget-update v-if="pwdForgetUpdateVisible" ref="pwdForgetUpdate"></pwd-forget-update>
  </div>
</template>

<script>
import {clearLoginInfo, uuid} from '@/utils'
import {encrypt} from '@/utils/cryptoUtil'
import '../../utils/wxLogin'
import RetrieveTeam from './retrieveTeam'
import TeamRetrieveList from "./team-retrieve-list";
import _ from 'lodash';
import PwdForgetUpdate from './login-pwd-forget-update.vue';

// 引入政务微信登录JS文件
const govWxLoginScript = document.createElement('script')
govWxLoginScript.setAttribute('src', 'http://sipzwt.sipac.gov.cn/js/sso/wwLogin-1.0.0.js')
document.head.appendChild(govWxLoginScript)

export default {
  data() {
    return {
      nodeEnv: window.SITE_CONFIG['nodeEnv'],
      countdown: 0,
      submitDisabled: false,
      captchaPath: '',
      canGetVerifyCode: true,
      addOrUpdateVisible: false,
      teamRetrieveListVisible: false,
      unionIdBindKey: this.$route.query.unionIdBindKey || '',
      loginType: this.$route.query.loginType || 'sms_code',
      isProject: this.$route.query.isProject || null,
      dataForm: {
        userName: '',
        password: '',
        validateCode: '',
        captcha: '',
        uuid: '',
        csrLogin: false
      },
      dataRule: {
        userName: [
          {required: true, message: '帐号不能为空', trigger: 'blur'}
        ],
        password: [
          {validator: this.passwordValidate, trigger: 'blur'}
        ],
        validateCode: [
          {validator: this.passwordValidate, trigger: 'blur'}
        ],
        captcha: [
          {required: true, message: '验证码不能为空', trigger: 'blur'}
        ]
      },
      pwdForgetUpdateVisible: false
    }
  },
  created() {
    this.getCaptcha()
    // this.popPublicReport()
  },
  mounted() {
    this.initWeChatLoginQRCode();
    this.popPublicReport()
    // 处理根路径访问登录页面时，无法跳转的bug
    window.addEventListener('hashchange', (event) => {
      console.log(event)
      if (event.newURL.includes('login-wait')) {
        setTimeout(() => {
          if (document.getElementById('wechat-qrcode')) {
            this.$router.go(0)
          }
        }, 1500)
      }
    })
  },
  components: {
    RetrieveTeam,
    TeamRetrieveList,
    PwdForgetUpdate
  },
  methods: {
    popPublicReport() {
      this.$http({
        url: this.$http.adornUrl('/api/dict/code'),
        method: 'get',
        params: this.$http.adornParams({
          'code': 'PUBLIC_REPORT',
          'throwEx': false
        })
      }).then(({data}) => {
        if (data && data.code === 0) {
          if (data.obj && data.obj.value && data.obj.value !== '') {
            this.$alert(`<div style="word-wrap: break-word">${data.obj.value}</div>`, data.obj.name, {
              customClass: 'public_report',
              dangerouslyUseHTMLString: true,
              confirmButtonText: '关闭',
              showClose: false,
              beforeClose: (action, ctx, close) => {
                close()
                setTimeout(() => {
                  let prAlerts = document.getElementsByClassName('el-message-box')
                  if (prAlerts && prAlerts.length > 0) {
                    _.forEach(prAlerts, item => {
                      item.style.width = '420px'
                    })
                  }
                }, 1000);
                return
              }
            })
            this.$nextTick(() => {
              let prAlert = document.getElementsByClassName('public_report')[0]
              if (prAlert) {
                prAlert.style.width = '620px'
              }
            })
          }
        } else {
          // this.$message.error("公告获取失败！")
        }
      })
    },
    passwordValidate(rule, value, callback) {
      if (value && value.length > 0) {
        callback()
      } else {
        callback(new Error(this.loginType === 'password' ? '请输入密码' : '请输入短信验证码'))
      }
    },
    handleClick() {
      this.$refs['dataForm']?.resetFields()
      this.dataForm.captcha = ''
      this.dataForm.password = ''
      this.dataForm.userName = ''
      this.dataForm.uuid = ''
      this.dataForm.csrLogin = false
      if (this.loginType === 'wx_qrcode') {
        this.$nextTick(() => {
          this.initWeChatLoginQRCode()
        })
      } else if (this.loginType === 'gov_wx_qrcode') {
        this.$nextTick(() => {
          this.initGovWeChatLoginQRCode()
        })
      }
      if (this.loginType === 'password') {
        this.getCaptcha()
      }
    },
    retrieveTeam(id) {
      this.addOrUpdateVisible = true
      this.$nextTick(() => {
        this.$refs.retrieveTeam.init(id)
      })
    },
    initWeChatLoginQRCode() {
      const result = new window.WxLogin({
        self_redirect: false,
        id: 'wechat-qrcode',
        appid: window.SITE_CONFIG['appID'],
        scope: 'snsapi_login',
        redirect_uri: encodeURIComponent(window.SITE_CONFIG['openRedirectUrl']),
        state: 'STATE#wechat_redirect',
        response_type: 'code',
      });
    },
    initGovWeChatLoginQRCode() {
      const result = new window.WwLogin({
        id: 'gov-wechat-qrcode',
        host:  window.SITE_CONFIG['govWxHost'],
        appid: window.SITE_CONFIG['govWxAppID'],
        agentid: window.SITE_CONFIG['govWxAgentID'],
        redirect_uri: encodeURIComponent(window.SITE_CONFIG['govWxRedirectUrl']),
        state: 'STATE#wechat_redirect',
      });
    },
    // 获取验证码
    getCaptcha() {
      this.dataForm.uuid = uuid()
      this.captchaPath = this.$http.adornUrl(`/fykj/captcha?uuid=${this.dataForm.uuid}`)
    },
    // 提交表单
    dataFormSubmit() {
      this.submitDisabled = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          if (this.loginType === 'sms_code') {
            this.dataForm.password = this.dataForm.validateCode
          }
          this.$http({
            url: this.$http.adornUrl('/fykj/login'),
            method: 'post',
            data: this.$http.adornData({
              'username': this.dataForm.userName,
              'password': encrypt(this.dataForm.password, window.SITE_CONFIG['aseKey']),
              'captcha': this.dataForm.captcha,
              'uuid': this.dataForm.uuid,
              'unionIdBindKey': this.unionIdBindKey,
              'loginType': this.loginType,
              'csrLogin': this.dataForm.csrLogin
            })
          }).then(({data}) => {
            if (data && data.code === 0) {
              if (this.dataForm.csrLogin) {
                const encodedData = encodeURIComponent(JSON.stringify(data));
                const platform = 'zyz';
                window.location.href = `${window.SITE_CONFIG['csrBackendBlankPage']}?data=${encodedData}&platform=${platform}`;
              } else {
                // oauth2用的token
                this.$cookie.set('Authorization', data.obj.token_type + ' ' + data.obj.access_token)
                // 把token对象放在sessionStorage中
                sessionStorage.setItem('oAuthToken', JSON.stringify(data.obj))
                this.dataForm.userName = null
                this.dataForm.password = null
                this.$http({
                  url: this.$http.adornUrl('/admin/user/loginUser'),
                  method: 'get',
                  params: this.$http.adornParams()
                }).then(({data}) => {
                  if (data && data.code === 0) {
                    if (data.obj.managerCapacity === 'ASSOCIATION_ADMIN' || data.obj.managerCapacity === 'SUB_ASSOCIATION_ADMIN' || data.obj.managerCapacity === 'COMMUNITY_ADMIN' || data.obj.managerCapacity === 'TEAM_ADMIN') {
                      this.$message.info('登录成功')
                      console.log('888888888888' + this.isProject)
                      if (this.isProject) {
                        this.$router.push('project-project')
                      } else {
                        this.$router.push('dashboard-index')
                      }
                    } else if (data.obj.roleCodes.length === 1 && data.obj.roleCodes[0] === 'VOLUNTEER') {
                      this.$message.error('您不属于平台管理员，无法进入管理平台！')
                      clearLoginInfo()
                      this.$router.push({name: 'login'})
                      this.getCaptcha()
                    } else {
                      this.$message.info('登录成功')
                      this.$router.replace({name: 'home'})
                    }
                  }
                })
              }
              // this.$router.replace({name: 'home'})
            } else {
              this.submitDisabled = false
              this.getCaptcha()
              if (data && data.code === 404) {
                this.$message.error(data.msg)
              } else {
                this.$message.error(data.msg)
              }
            }
          })
        } else {
          this.submitDisabled = false
        }
      })
    },
    getVerificationCode() {
      var valid = this.dataForm.userName !== ''
      if (!valid) {
        this.$message.error('请输入手机号')
      }
      if (valid) {
        this.countdown = 60
        const timer = setInterval(() => {
          if (this.countdown > 0) {
            this.countdown--
          } else {
            clearInterval(timer)
          }
        }, 1000)
        this.canGetVerifyCode = false
        this.$http({
          url: this.$http.adornUrl('/verify/verifyCode'),
          method: 'post',
          params: this.$http.adornParams({
            'mobile': this.dataForm.userName,
            'key': 'LOGIN',
          })
        }).then(({data}) => {
          if (!data || data.code !== 0) {
            this.$message.error(data.msg)
          }
        })
      }
    },
    retrieveSubmitCallback(data) {
      console.log(data)
      if (data.retrieveResultCode === 801) {
        // 展示小程序二维码
        this.$message.warning('您还不是志愿者，请先去小程序注册成为志愿者！')
      } else if (data.retrieveResultCode === 802) {
        // 展示小程序实名认证二维码
        this.$message.warning('您的志愿者身份尚未实名认证，请先去小程序进行实名认证！')
      } else {
        // 展示找回团队列表
        this.teamRetrieveListVisible = true
        this.$nextTick(() => {
          this.$refs.teamRetrieveList.init(data)
        })
      }
    },
    passwordForget() {
      this.pwdForgetUpdateVisible = true
      this.$nextTick(() => {
        this.$refs.pwdForgetUpdate.init()
      })
    },
    csrRegister() {
      window.location.href = `${window.SITE_CONFIG['csrBackendLoginPage']}?register=true}`;
    }
  }
}
</script>

<style lang="scss" scoped>
#login_page ::v-deep.site-wrapper.site-page--login {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background-color: rgba(38, 50, 56, .6);
  overflow: hidden;

  &:before {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 2;
    width: 100%;
    height: 100%;
    content: "";
    background-image: url(~@/assets/img/login_bg.jpg);
    background-size: 100% 100%;
  }

  .site-content__wrapper {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    padding: 0;
    margin: 0;
    overflow-x: hidden;
    overflow-y: auto;
    background-color: transparent;
  }

  .site-content {
    min-height: 100%;
    padding: 30px 500px 30px 30px;
  }

  .brand-info {
    z-index: 2;
    position: absolute;
    margin: 12% 100px 0 11%;
    font-weight: bolder;
  }

  .brand-info__text {
    margin: 0 0 22px 0;
    font-size: 55px;
    font-weight: 400;
    text-transform: uppercase;
    font-family: Caflisch, serif;
    color: #031259;
  }

  .brand-info__intro {
    margin: 10px 0;
    font-size: 16px;
    line-height: 1.58;
    opacity: .8;
    font-family: Caflisch, serif;
    color: #031259;
  }

  .login-main {
    text-align: center;
    height: 580px;
    position: absolute;
    z-index: 2;
    top: 15%;
    right: 10%;
    bottom: 10%;
    padding: 34px 55px 53px;
    background: rgba(255, 255, 255, 0.75);
    box-shadow: 0px 12px 14px 1px rgba(1, 28, 60, 0.4);
    border-radius: 21px 21px 21px 21px;
    opacity: 1;
    border-image: linear-gradient(135deg, rgba(255, 255, 255, 1), rgba(255, 255, 255, 0)) 1 1;
  }

  .login-title {
    font-size: 16px;
  }

  .login-captcha {
    overflow: hidden;

    > img {
      width: 100%;
      cursor: pointer;
    }
  }

  .login-btn-submit {
    width: 100%;
    //margin-top: 40px;
  }

  .login-btn-submit:hover {
    border: 2px solid #acd8e8 !important;
  }

  .find_team:hover {
    color: #081f7e !important;
  }

  .login-btn-vericate {
    padding-left: 280px;
  }

  .login-btn-change {
    padding-left: 260px;
  }

  .login-form {
    width: 350px;

    .el-form-item__content {
      position: relative;
    }

    .el-form-item {
      margin-bottom: 35px;
    }

    .el-input input {
      border-bottom: 1px solid #9b9898;
    }

    .el-input__inner {
      padding-left: 30px;
      border: none;
      border-radius: 0;
      background: transparent !important;
      color: black;
      font-weight: bolder;
    }

    .el-input__inner::placeholder {
      color: #818796;
    }

    .icon-input {
      position: absolute;
      top: 10px;
      left: 5px;
    }

    .el-input__suffix {
      right: 0px;

      .el-input__suffix-inner img {
        height: 100%;
        padding-bottom: 3px;
        border-radius: 5%;
      }
    }

    .el-form-item__error {
      padding-left: 30px;
      font-weight: bolder;
    }
  }
}

#wechat-qrcode, #gov-wechat-qrcode {
  text-align: center;
}
</style>
