<template>
  <el-dialog
      title="找回团队列表"
      :close-on-click-modal="false"
      width="80%"
      append-to-body
      :visible.sync="visible">
      <div>
        <el-button @click="getDataList" style="float: right; margin-bottom: 20px">刷新</el-button>
<!--        <div v-if="dataForm.type === 'add'" style="color: red; margin-left: 20px; margin-bottom: 20px">{{`新平台账号为您当前手机号，密码为: ${dataForm.password}，请妥善保管。也可以使用短信/微信方式登录系统`}}</div>-->
<!--        <div v-else style="color: red;  margin-left: 20px; margin-bottom: 20px">请使用您手机号登录系统，建议使用短信或微信方式登录</div>-->
      </div>
      <el-table
          :data="dataList"
          border
          v-loading="dataListLoading"
          style="width: 100%;">
        <el-table-column
            prop="name"
            header-align="center"
            align="center"
            min-width="250"
            show-overflow-tooltip
            label="团队名称">
          <template slot-scope="scope">
            <a style="cursor: pointer" @click="getTeamDetail(scope.row.id)">{{ scope.row.name }}</a>
          </template>
        </el-table-column>
        <el-table-column
            prop="createDate"
            header-align="center"
            align="center"
            width="160"
            label="申请时间">
        </el-table-column>
        <el-table-column
            header-align="center"
            align="center"
            label="管理员信息">
          <el-table-column
              prop="adminName"
              header-align="center"
              align="center"
              width="80"
              label="姓名">
          </el-table-column>
          <el-table-column
              prop="adminContact"
              header-align="center"
              align="center"
              width="120"
              label="联系方式">
          </el-table-column>
        </el-table-column>
        <el-table-column
            header-align="center"
            align="center"
            label="负责人信息">
          <el-table-column
              prop="curatorName"
              header-align="center"
              align="center"
              width="80"
              label="姓名">
          </el-table-column>
          <el-table-column
              prop="curatorContact"
              header-align="center"
              align="center"
              width="120"
              label="联系方式">
          </el-table-column>
        </el-table-column>
        <el-table-column
            prop="orgName"
            header-align="center"
            width="180"
            align="center"
            label="上级组织">
        </el-table-column>
        <el-table-column
            prop="founded"
            header-align="center"
            align="center"
            width="120"
            label="成立时间">
        </el-table-column>
        <el-table-column
            prop="teamStatusText"
            header-align="center"
            align="center"
            width="100"
            label="审核状态">
        </el-table-column>
        <el-table-column
            prop="auditOrgName"
            header-align="center"
            width="180"
            align="center"
            label="当前审核组织">
        </el-table-column>
        <el-table-column
            fixed="right"
            header-align="center"
            align="center"
            width="100"
            label="操作">
          <template slot-scope="scope">
            <el-button v-if="scope.row.teamStatus !== 'team_audit_pass' && scope.row.teamStatus !== 'team_audit_waiting'" type="text" size="small"
                       @click="teamRetrieveCheck(scope.row.id)">核实
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    <team-retrieve-detail v-if="teamRetrieveDetailVisible" ref="teamRetrieveDetail"></team-retrieve-detail>
    <team-retrieve-add-or-update v-if="teamRetrieveAddOrUpdateVisible" ref="teamRetrieveAddOrUpdate" @refreshDataList="getDataList"></team-retrieve-add-or-update>
  </el-dialog>
</template>

<script>
import TeamRetrieveDetail from "./team-retrieve-detail";
import TeamRetrieveAddOrUpdate from "./team-retrieve-add-or-update";
import _ from 'lodash';

export default {
  data() {
    return {
      visible: false,
      dataList: [],
      dataListLoading: false,
      // dataForm: {
      //   volunteerId: null,
      //   phone: null,
      //   type: null,
      //   password: null
      // },
      teamRetrieveDetailVisible: false,
      teamRetrieveAddOrUpdateVisible: false
    }
  },
  components: {
    TeamRetrieveDetail,
    TeamRetrieveAddOrUpdate
  },
  methods: {
    init(data) {
      this.visible = true
      // this.dataForm.volunteerId = data.volunteerId
      // this.dataForm.phone = data.phone
      // this.dataForm.password = data.password
      // this.dataForm.type = data.type
      this.dataList = data.teams
    },
    getDataList() {
      if (this.dataList.length <= 0) {
        return
      }
      this.dataListLoading = true
      this.$http({
        url: this.$http.adornUrl('/api/zyz/team/retrieve/getList'),
        method: 'post',
        data: this.$http.adornData({
          'retrieveIds': _.map(this.dataList, 'id')
        })
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.dataList = data.obj
        } else {
          this.dataList = []
        }
        this.dataListLoading = false
      })
    },
    getTeamDetail(id) {
      this.teamRetrieveDetailVisible = true
      this.$nextTick(() => {
        this.$refs.teamRetrieveDetail.init(id)
      })
    },
    teamRetrieveCheck(id) {
      this.teamRetrieveAddOrUpdateVisible = true
      this.$nextTick(() => {
        this.$refs.teamRetrieveAddOrUpdate.init(id)
      })
    }
  }
}
</script>
