<template>
  <el-dialog
      title="团队详情"
      :close-on-click-modal="false"
      width="70%"
      append-to-body
      :visible.sync="visible">
    <el-form :model="dataForm" ref="dataForm"
             label-width="140px">
      <el-card class="box-card">
        <div slot="header" class="clearfix"><span>团队基本信息</span></div>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="团队名称" prop="name">
              <el-input disabled v-model="dataForm.name" placeholder="团队名称"/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="团队预计成员人数" prop="expectTeamNum">
              <el-input-number style="width: 100%" v-model="dataForm.expectTeamNum" disabled controls-position="right" :min="0"/>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="团队性质" prop="civilRegistration">
              <el-radio-group disabled v-model="dataForm.civilRegistration">
                <el-radio :label="true">民政注册团队</el-radio>
                <el-radio :label="false">非民政注册团队</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="dataForm.civilRegistration && dataForm.civilRegistration === true ? '注册时间' :'成立时间'" prop="founded">
              <el-date-picker
                  disabled
                  style="width: 100%"
                  v-model="dataForm.founded"
                  value-format="yyyy-MM-dd"
                  type="date"
                  clearable
                  placeholder="成立时间"/>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="业务主管单位" prop="businessSupervisoryUnit">
              <el-input disabled v-model="dataForm.businessSupervisoryUnit" placeholder="业务主管单位" clearable/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="dataForm.civilRegistration && dataForm.civilRegistration === true ? '注册地点' : '所在地(街道/社区)'" prop="registerPlace">
              <el-input disabled v-model="dataForm.registerPlace" :placeholder="dataForm.civilRegistration && dataForm.civilRegistration === true ? '注册地点' : '所在地（街道/社区）'" clearable/>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item :label="dataForm.civilRegistration && dataForm.civilRegistration === true ? '附件（营业执照）' : '附件'" prop="attachmentList">
              <el-upload
                  disabled
                  class="upload-demo"
                  :action="this.$http.adornUrl('/admin/oss/upload')"
                  :data="uploadData"
                  :headers="myHeaders"
                  :on-success="function (res,file,fileList){return uploadSuccess(res,file,fileList)}"
                  :file-list="dataForm.attachmentList"
                  :on-preview="download"
                  :on-remove="function (file,fileList){return handleRemove(file,fileList)}"
                  :before-upload="function (file,fileList){return beforeUpload(file,fileList)}">
<!--                <el-button size="small" type="primary" plain>点击上传<i class="el-icon-upload el-icon&#45;&#45;left"/></el-button>-->
                <span style="font-size: xx-small; margin-left: 20px">{{dataForm.hurry && dataForm.hurry === true ? '(加急处理可上传授权函)' : '(主管单位盖章批复文件)'}}</span>

                <!--                <div slot="tip" class="el-upload__tip">支持上传word、excel、pdf、图片</div>-->
              </el-upload>
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-form-item label="" prop="hurry" label-width="30px">
              <el-checkbox v-model="dataForm.hurry" style="color: red">是否加急处理</el-checkbox>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="上级组织" v-if="this.$store.state.user.managerCapacity !== 'TEAM_ADMIN' " prop="orgCodeList">
              <el-cascader
                  disabled
                  placeholder="上级组织"
                  v-model="dataForm.orgCodeList"
                  :options="orgList"
                  :show-all-levels="false"
                  :props="{checkStrictly: true,label: 'name',value: 'code'}"
                  @change="handleChange"/>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="是否有注册号" prop="hasRegist">
              <el-radio-group disabled v-model="dataForm.hasRegist">
                <el-radio :label="true">是</el-radio>
                <el-radio :label="false">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item v-if="dataForm.hasRegist" label="团队注册号" prop="registerCard">
              <el-input disabled v-model="dataForm.registerCard" placeholder="注册号" clearable/>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="所属领域" prop="belongFieldList">
              <el-select disabled style="width: 100%" v-model="dataForm.belongFieldList" multiple clearable placeholder="请选择" @change="belongFieldsSelect">
                <el-option
                    v-for="item in belongFieldList"
                    :key="item.typeId"
                    :label="item.typeName"
                    :value="item.typeId">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="办公地点" prop="workAddress">
              <el-input disabled type="textarea" v-model="dataForm.workAddress" placeholder="办公地点" rows="5"/>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="团队介绍" prop="introduction">
              <el-input disabled type="textarea" v-model="dataForm.introduction" placeholder="团队介绍" rows="5"/>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="报名条件" prop="joinRequires">
              <el-input disabled type="textarea" v-model="dataForm.joinRequires" placeholder="报名条件" rows="5"/>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="服务内容" prop="services">
              <el-input disabled type="textarea" v-model="dataForm.services" placeholder="服务内容" rows="5"/>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="团队头像" prop="teamPhoto">
              <el-image
                  :src="$http.adornAttachmentUrl(dataForm.teamPhoto)"
                  :preview-src-list="[$http.adornAttachmentUrl(dataForm.teamPhoto)]"
                  class="avatar"/>
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>
      <el-card class="box-card" style="margin-top: 20px;">
        <div slot="header" class="clearfix"><span>团队负责人信息</span></div>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="负责人姓名" prop="curatorName">
              <el-input disabled v-model="dataForm.curatorName" placeholder="负责人姓名" clearable/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="证件号码" prop="curatorCard">
              <el-input disabled v-model="dataForm.curatorCard" placeholder="证件号码" clearable/>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="联系电话" prop="curatorContact">
              <el-input disabled v-model="dataForm.curatorContact" placeholder="联系电话" clearable/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="是否党员" prop="curatorPartyMember">
              <el-radio-group disabled v-model="dataForm.curatorPartyMember">
                <el-radio :label="true">是</el-radio>
                <el-radio :label="false">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="所在单位" prop="curatorDepartment">
              <el-input disabled v-model="dataForm.curatorDepartment" placeholder="所在单位" clearable/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="职位" prop="curatorJob">
              <el-input disabled v-model="dataForm.curatorJob" placeholder="职位" clearable/>
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>
      <el-card class="box-card" style="margin-top: 20px;">
        <div slot="header" class="clearfix"><span>团队管理员信息</span></div>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="管理员姓名" prop="adminName">
              <el-input disabled v-model="dataForm.adminName" placeholder="管理员姓名" clearable/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="证件号码" prop="adminCard">
              <el-input disabled v-model="dataForm.adminCard" placeholder="证件号码" clearable/>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="联系电话" prop="adminContact">
              <el-input disabled v-model="dataForm.adminContact" placeholder="联系电话" clearable/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="是否党员" prop="adminPartyMember">
              <el-radio-group disabled v-model="dataForm.adminPartyMember">
                <el-radio :label="true">是</el-radio>
                <el-radio :label="false">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="所在单位" prop="adminDepartment">
              <el-input disabled v-model="dataForm.adminDepartment" placeholder="所在单位" clearable/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="职位" prop="adminJob">
              <el-input disabled v-model="dataForm.adminJob" placeholder="职位" clearable/>
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>
    </el-form>
  </el-dialog>
</template>

<script>
import moment from 'moment'
import Vue from 'vue'
import {getFathersById} from "@/utils";


export default {
  data() {
    return {
      visible: false,
      dataList: [],
      orgList: [],
      belongFieldList: [],
      fileList: [],
      serverCode: 'LocalServer',
      myHeaders: {Authorization: Vue.cookie.get('Authorization')},
      uploadData: {serverCode: 'LocalServer', media: false},
      dataForm: {
        name: null,
        expectTeamNum: 0,
        civilRegistration: false,
        founded: null,
        businessSupervisoryUnit: null,
        registerPlace: null,
        attachmentList: [],
        hurry: false,
        orgCodeList: [],
        orgCode: null,
        hasRegist: false,
        registerCard: null,
        belongFieldList: [],
        belongFields: null,
        workAddress: null,
        introduction: null,
        joinRequires: null,
        services: null,
        teamPhoto: null,
        curatorName: null,
        curatorCard: null,
        curatorContact: null,
        curatorPartyMember: false,
        curatorDepartment: null,
        curatorJob: null,
        adminName: null,
        adminCard: null,
        adminContact: null,
        adminPartyMember: false,
        adminDepartment: null,
        adminJob: null
      }
    }
  },
  components: {
    moment
  },
  methods: {
    async init(id) {
      this.dataForm.id = id || null
      await this.getOrg()
      await this.getBelongFields()
      await this.getInitDate()
    },
   async getInitDate() {
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm']?.resetFields()
        if (this.dataForm.id) {
          this.$http({
            url: this.$http.adornUrl(`/api/zyz/team/retrieve/getById`),
            method: 'get',
            params: this.$http.adornParams({id: this.dataForm.id})
          }).then(({data}) => {
            if (data && data.code === 0) {
              data.obj.orgCodeList = getFathersById(data.obj.orgCode, this.orgList)
              this.dataForm = data.obj
            }
          })
        }
      })
    },
    //获取所属区域
   async getOrg() {
     await this.$http({
        url: this.$http.adornUrl(`/api/sys/org/getOrgTree`),
        method: 'get',
        params: this.$http.adornParams()
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.orgList = this.getTreeData(data.obj)
        } else {
          this.orgList = []
        }
      })
    },
    //获取所属领域
    async getBelongFields() {
      await this.$http({
        url: this.$http.adornUrl(`/api/zyz/sync-belong-filed-dict/getTopBelongField`),
        method: 'get'
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.belongFieldList = data.obj
        } else {
          this.belongFieldList = []
        }
      })
    },
    handleChange(value) {
      this.dataForm.orgCode = value && value.length > 0 ? value[value.length - 1] : null
      // console.log(this.dataForm.orgCode)
    },
    belongFieldsSelect(value) {
      this.dataForm.belongFields = value && value.length > 0 ? value.join(',') : null
      // console.log(this.dataForm.belongFields)
    },
    // *处理点位分类最后children数组为空的状态
    getTreeData: function (data) {
      let that = this
      // 循环遍历json数据
      data.forEach(function (e) {
        if (!e.children || e.children.length < 1) {
          e.children = undefined
        } else {
          // children若不为空数组，则继续 递归调用 本方法
          that.getTreeData(e.children)
        }
      })
      return data
    },
    download(file) {
      window.open(this.$http.adornAttachmentUrl(file.path), '_blank')
    }
  }
}
</script>
