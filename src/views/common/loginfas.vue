<template>
    <div class="site-wrapper site-page--login" id="login_page">
        <div class="site-content__wrapper">
            <div class="site-content">
                <!--        <div class="brand-info">-->
                <!--          <h2 class="brand-info__text">新时代文明实践志愿服务平台</h2>-->
                <!--          <p class="brand-info__intro">新时代文明实践志愿服务平台</p>-->
                <!--        </div>-->
                <div class="login-main">
                    <h2 style="line-height: 10px;"></h2>
                    <h2>新时代文明实践中心建设工作服务平台</h2>
                    <el-tabs v-model="loginType" @tab-click="handleClick" stretch>

                        <el-tab-pane class="login-title" label="密码登录" name="password"><h3/></el-tab-pane>

                    </el-tabs>
                    <!--          <h3>管理员登录</h3>-->
                    <el-form v-show="loginType === 'password'" class="login-form" :model="dataForm" :rules="dataRule"
                             ref="dataForm"
                             @keyup.enter.native="dataFormSubmit()" status-icon>
                        <h6 v-if="this.unionIdBindKey" style="color:red; margin: 0px 0px 20px 0px">
                            您的微信账号暂未绑定用户，请使用账号密码登录进行绑定，之后可直接扫码登录</h6>
                        <el-form-item prop="userName">
                            <el-input v-model="dataForm.userName" placeholder="用户名"></el-input>
                            <img src="~@/assets/img/icon-user-new.png" class="icon-input" alt="" style="width: 21px; height: 21px">
                        </el-form-item>
                        <el-form-item prop="password">
                            <el-input v-model="dataForm.password" type="password" placeholder="密码"></el-input>
                            <img src="~@/assets/img/icon-password-new.png" class="icon-input" alt=""
                                 style="width: 21px; height: 21px">
                        </el-form-item>
                        <el-form-item prop="captcha">
                            <!--              <el-row>-->
                            <!--                <el-col :span="14">-->
                            <el-input v-model="dataForm.captcha" placeholder="验证码">
                                <img :src="captchaPath" @click="getCaptcha()" alt="" slot="suffix">
                            </el-input>
                            <img src="~@/assets/img/icon-valid-code-new.png" class="icon-input" alt=""
                                 style="width: 21px; height: 21px">
                            <!--                </el-col>-->
                            <!--                <el-col :span="10" class="login-captcha">-->
                            <!--                  <img :src="captchaPath" @click="getCaptcha()" alt="">-->
                            <!--                </el-col>-->
                            <!--              </el-row>-->
                        </el-form-item>
                        <el-form-item>
                            <el-button :disabled="submitDisabled" class="login-btn-submit" type="primary" @click="dataFormSubmit()"
                                       style="background-color: #42aefa; border-width:2px; border-color: #42aefa; opacity: 0.9">
                                登录
                            </el-button>
                        </el-form-item>
                        <!--            <el-form-item>-->
                        <!--              <el-button class="login-btn-change" type="text" @click="changeLoginType()">账号或者手机号登录</el-button>-->
                        <!--            </el-form-item>-->
                    </el-form>


                </div>
            </div>
        </div>
    </div>
</template>

<script>
import {clearLoginInfo, uuid} from '@/utils'
import {encrypt} from '@/utils/cryptoUtil'
import '../../utils/wxLogin'
import _ from 'lodash';

export default {
    data() {
        return {
            countdown: 0,
            submitDisabled: false,
            captchaPath: '',
            canGetVerifyCode: true,
            addOrUpdateVisible: false,
            teamRetrieveListVisible: false,
            unionIdBindKey: this.$route.query.unionIdBindKey || '',
            loginType: this.$route.query.loginType || 'password',
            isProject: this.$route.query.isProject || null,
            code: this.$route.query.code || this.getQueryString('code') || '',
            dataForm: {
                userName: '',
                password: '',
                validateCode: '',
                captcha: '',
                uuid: ''
            },
        }
    },
    created() {

        if (this.code) {
            this.dataFormSubmit()
        } else {
            window.open('http://fyde.cnsaas.com/fytec/fas/#/authorize?client_id=zyz-test&response_type=code&scope=&redirect_uri=https://sungent.fyxmt.com/volunteer_fas', '_self')
        }
        // this.popPublicReport()
        //this.dataFormSubmit()
    },
    mounted() {
        // //this.dataFormSubmit()
        // // 处理根路径访问登录页面时，无法跳转的bug
        // window.addEventListener('hashchange', (event) => {
        //     console.log(event)
        //     if (event.newURL.includes('login-wait')) {
        //         setTimeout(() => {
        //             if (document.getElementById('wechat-qrcode')) {
        //                 this.$router.go(0)
        //             }
        //         }, 1500)
        //     }
        // })
    },
    components: {

    },
    methods: {
        getQueryString (name) {
            console.log('getCode---window', window.location.href)
            const href = window.location.href.split('#')[0]
            console.log('getCode---code', href)
            const result = href.match(new RegExp('[?&]' + name + '=([^&]+)', 'i'))
            console.log('getCode---result', result)
            if (result == null || result.length < 1) {
                return ''
            }
            return result[1]
        },
        handleClick() {
            this.$refs['dataForm']?.resetFields()
            this.dataForm.captcha = ''
            this.dataForm.password = ''
            this.dataForm.userName = ''
            this.dataForm.uuid = ''
            if (this.loginType === 'wx_qrcode') {
                this.$nextTick(() => {
                    this.initWeChatLoginQRCode()
                })
            }
            if (this.loginType === 'password') {
                this.getCaptcha()
            }
        },

        // 获取验证码
        getCaptcha() {
            this.dataForm.uuid = uuid()
            this.captchaPath = this.$http.adornUrl(`/fykj/captcha?uuid=${this.dataForm.uuid}`)
        },
        // 提交表单
        dataFormSubmit() {
            this.$http({
                url: this.$http.adornUrl('/fykj/fasLogin'),
                method: 'post',
                data: this.$http.adornData({
                    'uuid': this.dataForm.uuid,
                    'unionIdBindKey': this.unionIdBindKey,
                    'code':this.code
                })
            }).then(({data}) => {
                if (data && data.code === 0) {
                    // oauth2用的token
                    this.$cookie.set('Authorization', data.obj.token_type + ' ' + data.obj.access_token)
                    // 把token对象放在sessionStorage中
                    sessionStorage.setItem('oAuthToken', JSON.stringify(data.obj))
                    this.dataForm.userName = null
                    this.dataForm.password = null
                    this.$http({
                        url: this.$http.adornUrl('/admin/user/loginUser'),
                        method: 'get',
                        params: this.$http.adornParams()
                    }).then(({data}) => {
                        if (data && data.code === 0) {
                            if (data.obj.managerCapacity === 'ASSOCIATION_ADMIN' || data.obj.managerCapacity === 'SUB_ASSOCIATION_ADMIN' || data.obj.managerCapacity === 'COMMUNITY_ADMIN' || data.obj.managerCapacity === 'TEAM_ADMIN') {
                                this.$message.info('登录成功')
                                console.log('888888888888'+this.isProject)
                                this.$router.push('dashboard-index')
                            } else if (data.obj.roleCodes.length === 1 && data.obj.roleCodes[0] === 'VOLUNTEER') {
                                this.$message.error('您不属于平台管理员，无法进入管理平台！')
                                clearLoginInfo()
                                this.$router.push({name: 'login'})
                                this.getCaptcha()
                            } else {
                                this.$message.info('登录成功')
                                this.$router.replace({name: 'home'})
                            }
                        }
                    })
                    // this.$router.replace({name: 'home'})
                } else {
                    this.submitDisabled = false
                    this.$message.error(data.msg)
                    this.getCaptcha()
                }
            })
        },

    }
}
</script>

<style lang="scss" scoped>
#login_page ::v-deep.site-wrapper.site-page--login {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background-color: rgba(38, 50, 56, .6);
  overflow: hidden;

  &:before {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 2;
    width: 100%;
    height: 100%;
    content: "";
    background-image: url(~@/assets/img/login_bg.jpg);
    background-size: 100% 100%;
  }

  .site-content__wrapper {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    padding: 0;
    margin: 0;
    overflow-x: hidden;
    overflow-y: auto;
    background-color: transparent;
  }

  .site-content {
    min-height: 100%;
    padding: 30px 500px 30px 30px;
  }

  .brand-info {
    z-index: 2;
    position: absolute;
    margin: 12% 100px 0 11%;
    font-weight: bolder;
  }

  .brand-info__text {
    margin: 0 0 22px 0;
    font-size: 55px;
    font-weight: 400;
    text-transform: uppercase;
    font-family: Caflisch, serif;
    color: #031259;
  }

  .brand-info__intro {
    margin: 10px 0;
    font-size: 16px;
    line-height: 1.58;
    opacity: .8;
    font-family: Caflisch, serif;
    color: #031259;
  }

  .login-main {
    text-align: center;
    height: 580px;
    position: absolute;
    z-index: 2;
    top: 15%;
    right: 10%;
    bottom: 10%;
    padding: 34px 55px 53px;
    background: rgba(255, 255, 255, 0.75);
    box-shadow: 0px 12px 14px 1px rgba(1, 28, 60, 0.4);
    border-radius: 21px 21px 21px 21px;
    opacity: 1;
    border-image: linear-gradient(135deg, rgba(255, 255, 255, 1), rgba(255, 255, 255, 0)) 1 1;
  }

  .login-title {
    font-size: 16px;
  }

  .login-captcha {
    overflow: hidden;

    > img {
      width: 100%;
      cursor: pointer;
    }
  }

  .login-btn-submit {
    width: 100%;
    margin-top: 40px;
  }

  .login-btn-submit:hover {
    border: 2px solid #acd8e8 !important;
  }

  .find_team:hover {
    color: #081f7e !important;
  }

  .login-btn-vericate {
    padding-left: 280px;
  }

  .login-btn-change {
    padding-left: 260px;
  }

  .login-form {
    width: 350px;

    .el-form-item__content {
      position: relative;
    }

    .el-form-item {
      margin-bottom: 35px;
    }

    .el-input input {
      border-bottom: 1px solid #9b9898;
    }

    .el-input__inner {
      padding-left: 30px;
      border: none;
      border-radius: 0;
      background: transparent !important;
      color: black;
      font-weight: bolder;
    }

    .el-input__inner::placeholder {
      color: #818796;
    }

    .icon-input {
      position: absolute;
      top: 10px;
      left: 5px;
    }

    .el-input__suffix {
      right: 0px;

      .el-input__suffix-inner img {
        height: 100%;
        padding-bottom: 3px;
        border-radius: 5%;
      }
    }

    .el-form-item__error {
      padding-left: 30px;
      font-weight: bolder;
    }
  }
}

#wechat-qrcode {
  text-align: center;
}
</style>
