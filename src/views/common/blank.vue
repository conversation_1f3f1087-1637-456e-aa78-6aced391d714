<template>
  <div
      class="pages-auto-login"
      v-loading.fullscreen.lock="load"
      element-loading-text="登录中..."
      element-loading-spinner="el-icon-loading"
      element-loading-background="rgba(255, 255, 255, 0.8)">
    <!--    <h3>登录所需信息</h3>
        <el-form :model="dataForm" ref="dataForm" label-position="left">
          <el-form-item  label="政务通授权（code）：" prop="code">
            <el-input v-model="dataForm.code" placeholder="" disabled>
              <el-button slot="append" icon="el-icon-document-copy" @click="copyToClipboard(dataForm.code)"/>
            </el-input>
          </el-form-item>
          <el-form-item label="登录验证码（verifyCode）：" prop="verifyCode">
            <el-input v-model="dataForm.verifyCode" placeholder="" disabled>
              <el-button slot="append" icon="el-icon-document-copy" @click="copyToClipboard(dataForm.verifyCode)"/>
            </el-input>
          </el-form-item>
        </el-form>-->
  </div>
</template>

<script>
import {encrypt} from "@/utils/cryptoUtil";
export default {
  components: {},
  data () {
    return {
      load: true,
      dataForm: {
        code: this.$route.query.code || this.getQueryString('code') || '',
        verifyCode: encrypt(new Date().getTime(), "e6d7a45af67b586e")
      }
    }
  },
  mounted () {
    this.getToken()
  },
  methods: {
    getQueryString (name) {
      // var result = window.location.search.match(new RegExp("[\?\&]" + name + "=([^\&]+)", "i"));
      const href = window.location.href.split('#')[0]
      const result = href.match(new RegExp('[?&]' + name + '=([^&]+)', 'i'))
      if (result == null || result.length < 1) {
        return ''
      }
      return result[1]
    },
    /*copyToClipboard (text) {
      let clipboard = new Clipboard('.el-icon-document-copy', {
        text: function () {
          return text
        }
      })
      clipboard.on('success', () => {
        this.$message.success({
          message: '复制成功'
        })
        clipboard.destroy()
      })
      clipboard.on('error', () => {
        this.$message.warning({
          message: '该浏览器不支持自动复制'
        })
        clipboard.destroy()
      })
    },*/
    getToken () {
      this.$http({
        url: this.$http.adornUrl('/zwt-docking/govLogin'),
        method: 'get',
        params: this.$http.adornParams(this.dataForm)
      }).then(({data}) => {
        if (data && data.code === 0) {
          // oauth2用的token
          this.$cookie.set('Authorization', data.obj.token_type + ' ' + data.obj.access_token)
          sessionStorage.setItem('oAuthToken', JSON.stringify(data.obj))
          this.$router.replace({name: 'home'})
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.pages-auto-login {
  position: absolute; // 使用绝对定位
  top: 30%;
  left: 50%;
  transform: translate(-50%, -50%); // 通过平移实现居中
}
h3 {
  text-align: center;
  margin-bottom: 20px;
}
:deep(.el-form-item__label) {
  width: 100%;
}
.el-form-item {
  min-width: 350px; // 根据实际需求调整
}
.el-input {
  width: 100%; // 使其占据父容器的全部宽度，或者设置一个固定宽度
  white-space: nowrap; // 防止文本换行（通常输入框默认就是这样）
  overflow: visible; // 确保内容可见，如果需要滚动条则改为 auto
  :deep(.el-input__inner) {
    background-color: white;
    color: black;
  }
}
</style>
