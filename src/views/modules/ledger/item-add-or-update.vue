<template>
  <el-dialog class="common-dialog" :title="!dataForm.id ? '新增' : '修改'" :visible.sync="visible"
             :close-on-click-modal="false" :modal-append-to-body="false" width="60%">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmit()" label-width="120px">
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="指标名称" prop="indexName">
            <el-input v-model="dataForm.indexName" placeholder="请输入" clearable disabled/>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="台账项名称" prop="name">
            <el-input v-model="dataForm.name" placeholder="请输入" clearable/>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="关联局办" prop="departmentIdList">
            <el-select style="width: 100%" v-model="dataForm.departmentIdList" placeholder="请选择" filterable multiple>
              <el-option
                  v-for="item in departmentList"
                  :key="item.id"
                  :label="item.text"
                  :value="item.id">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="相关要求" prop="relatedRequirements">
            <el-input v-model="dataForm.relatedRequirements" :rows="5" type="textarea" placeholder="相关要求"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()" :disabled="dataFormLoading">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
  import _ from 'lodash'
  import editMixin from '@/mixins/edit-mixins'
  export default {
    mixins: [editMixin],
    data () {
      return {
        editOptions: {
          initUrl: '/admin/ledger/indexItem'
        },
        dataForm: {},
        departmentList: [],
        initForm: {
          id: null,
          version: null,
          name: null,
          indexId: null,
          indexName: null,
          departmentIdList: [],
          relatedRequirements: null
        },
        dataRule: {
          name: [
            { required: true, message: '台账项名称不能为空', trigger: 'change' }
          ],
          departmentIdList: [
            { required: true, message: '关联局办不能为空', trigger: 'change' }
          ],
          relatedRequirements: [
            { required: true, message: '相关要求不能为空', trigger: 'change' }
          ]
        }
      }
    },
    methods: {
      init (id, indexId, indexName) {
        this.dataForm = _.cloneDeep(this.initForm)
        this.dataForm.id = id || null
        this.dataForm.departmentIdList = []
        this.getDepartmentList()
        this.visible = true
        this.$nextTick(() => {
          this.$refs['dataForm']?.resetFields()
          if (this.dataForm.id) {
            this.getData()
          } else {
            this.dataForm.indexId = indexId
            this.dataForm.indexName = indexName
          }
        })
      },
      submitBeforeHandle() {
        this.dataForm.departmentId = this.dataForm.departmentIdList.join(',')
      },
      getDepartmentList() {
        this.$http({
          url: this.$http.adornUrl('/admin/ledger/department/selectList'),
          method: 'get'
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.departmentList = data.obj || []
          } else {
            this.departmentList = []
          }
        })
      }
    }
  }
</script>

<style lang="scss" scoped>
</style>
