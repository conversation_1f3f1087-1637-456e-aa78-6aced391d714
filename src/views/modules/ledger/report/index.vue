<template>
  <div class="ledger-manage">
    <el-row :gutter="20" style="height: 100%;">
      <!-- 左侧树结构区域 -->
      <el-col :span="8" style="height: 100%;">
        <tree-panel @node-click="handleNodeClick" />
      </el-col>

      <!-- 右侧表格 -->
      <el-col :span="16" style="height: 100%;">
        <report ref="report"/>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import TreePanel from './TreePanel.vue'
import Report from './report.vue'

export default {
  components: {
    TreePanel,
    Report
  },
  methods: {
    handleNodeClick(node) {
      this.$nextTick(() => {
        this.$refs.report.init(node.id, node.name)
      });
    }
  }
}
</script>