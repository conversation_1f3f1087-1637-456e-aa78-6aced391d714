<template>
  <el-dialog class="common-dialog" :title="'上传'" :visible.sync="visible"
             :close-on-click-modal="false" :modal-append-to-body="false" width="60%">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" label-width="90px">
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="台账项名称" prop="indexItemName">
            <el-input v-model="dataForm.indexItemName" disabled/>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="相关要求" prop="relatedRequirements">
            <el-input v-model="dataForm.relatedRequirements" :rows="5" type="textarea" disabled></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="上报状态" prop="status">
            <el-radio-group v-model="dataForm.status">
              <el-radio :label="true">已上报</el-radio>
              <el-radio :label="false">未上报</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="附件" prop="attachmentList">
            <el-upload
                class="upload-demo"
                :action="this.$http.adornUrl('/admin/oss/upload')"
                :data="uploadData"
                :headers="myHeaders"
                :on-success="function (res,file,fileList){return uploadSuccess(res,file,fileList)}"
                :file-list="dataForm.attachmentList"
                :on-preview="download"
                :limit="1"
                :on-exceed="handleExceed"
                :on-remove="function (file,fileList){return handleRemove(file,fileList)}"
                :before-upload="function (file,fileList){return beforeUpload(file,fileList)}">
              <el-button size="small" type="primary" plain>点击上传<i class="el-icon-upload el-icon--left"/></el-button>
              <div slot="tip" class="el-upload__tip">支持上传word、excel、pdf、图片</div>
            </el-upload>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
  import _ from "lodash";
  import Vue from 'vue'
  export default {
    data () {
      return {
        visible: false,
        uploadUrl: this.$http.adornUrl('/admin/oss/upload'),
        myHeaders: {Authorization: Vue.cookie.get('Authorization')},
        uploadData: {serverCode: 'LocalServer', media: false},
        fileExits: 'zip/rar/7zip/doc/docx/xls/xlsx/pdf/bmp/jpg/png/jpeg',
        dataForm: {
          attachmentList: [],
          indexItemName: '',
          relatedRequirements: '',
          indexItemId: null,
          departmentId: null,
          attachmentUrl: '',
          attachmentName: '',
          status: null
        },
        dataRule: {
          attachmentList: [
            { required: true, message: '附件地址不能为空', trigger: 'change' }
          ],
          status: [
            { required: true, message: '上报状态不能为空', trigger: 'change' }
          ]
        }
      }
    },
    methods: {
      init (data, departmentId) {
        this.dataForm = _.cloneDeep(data)
        this.dataForm.departmentId = departmentId
        this.dataForm.attachmentList = []
        if (data.attachmentUrl) {
          this.dataForm.attachmentList.push({
            'name': data.attachmentName,
            'url': data.attachmentUrl
          })
        }
        this.visible = true
      },
      dataFormSubmit () {
        this.$refs['dataForm'].validate((valid) => {
          if (valid) {
            this.$http({
              url: this.$http.adornUrl('/admin/ledger/report/save'),
              method: 'post',
              data: this.$http.adornData(this.dataForm)
            }).then(({data}) => {
              if (data && data.code === 0) {
                this.$message({
                  message: '操作成功',
                  type: 'success',
                  duration: 1500,
                  onClose: () => {
                    this.visible = false
                    this.$emit('refreshDataList')
                  }
                })
              } else if (data && data.code === 303) {
                for (let it of data.obj) {
                  this[`${it.field}Error`] = it.message
                }
              } else {
                this.$message.error(data.msg)
              }
            })
          }
        })
      },
      // 上传成功
      uploadSuccess(res, file, fileList) {
        if (res.success) {
          this.$message({
            message: '操作成功',
            type: 'success',
            duration: 1500
          })
          let file = {
            name: res.obj.fileName,
            path: res.obj.path
          }
          this.dataForm.attachmentList.push(file)
          this.dataForm.attachmentName = res.obj.fileName
          this.dataForm.attachmentUrl = res.obj.path
        } else {
          this.$message.error('上传失败')
        }
      },
      handleExceed(files, fileList) {
        this.$message.warning(`只能上传 1 个文件，请先删除已有文件再上传新文件`)
      },
      handleRemove(file) {
        this.dataForm.attachmentList = []
        this.dataForm.attachmentName = null
        this.dataForm.attachmentUrl = null
      },
      beforeUpload(file) {
        const isLt2M = file.size / 1024 / 1024 < 2
        if (!isLt2M) {
          this.$message.error('上传文件大小不能超过 2M!')
          return false
        }
        let originalName = file.name
        if (originalName.indexOf('.') === -1) {
          this.$message.error('不支持的文件格式')
        }
        let fileExt = originalName.substring(originalName.lastIndexOf('.') + 1).toLowerCase()
        let allowTypes = `/${this.fileExits}/`
        if (allowTypes.indexOf(`/${fileExt}/`) === -1) {
          this.$message.error(`上传资源只能是${this.fileExits}格式!`)
          return false
        }
      },
      download() {
        window.open(this.$http.adornAttachmentUrl(this.dataForm.attachmentUrl), '_blank')
      }
    }
  }
</script>

<style lang="scss" scoped>
</style>
