<template>
  <el-card class="tree-card" shadow="always">
    <div slot="header" class="clearfix">
      <span>台账指标</span>
    </div>
  <el-tree
      ref="tree"
      :data="treeData"
      :props="props"
      node-key="id"
      accordion
      highlight-current
      v-loading="loading"
      @node-click="handleNodeClick">
    <div class="custom-tree-node" slot-scope="{ node, data }">
      <el-tooltip effect="dark" :content="data.name" placement="top">
        <div class="tree-node-label">{{ data.name }}</div>
      </el-tooltip>
    </div>
  </el-tree>
  </el-card>
</template>

<script>
export default {
  data() {
    return {
      treeData: [],
      loading: false,
      props: {
        key: 'id',
        label: 'name',
        children: 'children'
      },
    };
  },
  created() {
    this.loadTreeData();
  },
  components: {
  },
  methods: {
    loadTreeData() {
      this.loading = true
      this.$http({
        url: this.$http.adornUrl('/admin/ledger/index/tree'),
        method: 'get',
        params: this.$http.adornParams()
      }).then(({data}) => {
        this.treeData = data.obj
        this.$nextTick(() => {
          if (this.treeData.length > 0) {
            const firstNode = this.treeData[0];
            this.$refs.tree.setCurrentKey(firstNode.id);
            this.$emit('node-click', firstNode);
          }
          this.loading = false
        });
      })
    },
    handleNodeClick(node) {
      this.$refs.tree.setCurrentKey(node.id);
      this.$emit('node-click', node);
    }
  }
};
</script>
<style scoped>
.tree-card {
  height: 100%;
  overflow: auto;
}

.full-width-tree ::v-deep .el-tree-node__content {
  width: 100%;
}

.custom-tree-node {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  overflow: hidden;
}

.tree-node-label {
  flex: 1;
  min-width: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  margin-right: 10px;
}

.tree-node-ops {
  display: flex;
  flex-shrink: 0;
  gap: 4px;
}
</style>

