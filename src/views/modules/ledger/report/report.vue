<template>
  <el-card class="table-panel">
    <div slot="header" class="clearfix">
      <span>{{this.indexName}}-台账项维护查询</span>
    </div>
    <el-form :inline="true" ref="dataForm" :model="dataForm">
      <el-row :gutter="20">
        <el-form-item label="局办名称" prop="departmentId">
          <el-select v-model="dataForm.departmentId" placeholder="请选择局办" filterable>
            <el-option
                v-for="item in departmentList"
                :key="item.id"
                :label="item.departmentName"
                :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button icon="el-icon-search" type="warning" @click="loadTable()">查询</el-button>
        </el-form-item>
      </el-row>
    </el-form>
    <el-table :data="dataList" border stripe v-loading="dataListLoading">
      <el-table-column type="index" label="排序" width="60" />
      <el-table-column prop="indexItemName" label="台账项名称" />
      <el-table-column prop="relatedRequirements" label="相关要求" show-overflow-tooltip/>
      <el-table-column prop="status" label="上报状态">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.status === false" size="small" type="danger">未上报</el-tag>
          <el-tag v-if="scope.row.status === true" size="small">已上报</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="attachmentName" label="附件">
        <template slot-scope="scope">
          <a style="cursor: pointer" @click="download(scope.row.attachmentUrl)">{{scope.row.attachmentName}}</a>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="120">
        <template slot-scope="scope">
          <el-button type="text" @click="addOrUpdateHandle(scope.row)">标记上报状态</el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="loadTable"></add-or-update>
  </el-card>
</template>

<script>
import AddOrUpdate from './add-or-update.vue'
export default {
  name: 'TablePanel',
  data() {
    return {
      dataList: [],
      addOrUpdateVisible: false,
      departmentList: [],
      indexId: null,
      indexName: null,
      dataListLoading: false,
      dataForm: {
        departmentId: null
      },
    };
  },
  components: {
    AddOrUpdate
  },
  methods: {
    async init(indexId, indexName) {
      this.indexId = indexId
      this.indexName = indexName
      if (this.dataForm.departmentId) {
        this.loadTable()
      } else {
        await this.getDepartmentList();
        await this.loadTable()
      }
    },
    loadTable() {
      this.dataListLoading = true;
      this.$http({
        url: this.$http.adornUrl('/admin/ledger/indexItem/getDepartmentReport'),
        method: 'get',
        params: this.$http.adornParams({
          'indexId': this.indexId,
          'departmentId': this.dataForm.departmentId
        })
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.dataList = data.obj
        } else {
          this.dataList = []
        }
        this.dataListLoading = false
      })
    },
    async getDepartmentList() {
      await this.$http({
        url: this.$http.adornUrl('/admin/ledger/department/all'),
        method: 'get'
      }).then(({data}) => {
        if (data) {
          this.departmentList = data
          if (this.departmentList.length > 0) {
            this.dataForm.departmentId = this.departmentList[0].id
          }
        } else {
          this.departmentList = []
        }
      })
    },
    addOrUpdateHandle(data) {
      this.addOrUpdateVisible = true
      this.$nextTick(() => {
        this.$refs.addOrUpdate.init(data, this.dataForm.departmentId)
      })
    },
    download(url) {
      window.open(this.$http.adornAttachmentUrl(url), '_blank')
    }
  }
};
</script>
<style scoped>
.table-panel {
  height: 100%;
  overflow: auto;
}
</style>
