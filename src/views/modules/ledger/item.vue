<template>
  <el-card class="table-panel">
    <div slot="header" class="clearfix">
      <span>{{this.indexName}}-台账项维护管理</span>
      <div style="float: right;">
        <el-button type="primary" @click="addOrUpdateHandle()">新增项</el-button>
        <el-button type="success" @click="importHandle()">导入指标项</el-button>
        <el-button icon="el-icon-download" type="success"
                   @click="exportExcel()">导出全部指标</el-button>
      </div>
    </div>

    <el-table :data="dataList" border stripe v-loading="dataListLoading">
      <el-table-column type="index" label="排序" width="60" />
      <el-table-column prop="name" label="台账项名称" />
      <el-table-column prop="departmentName" label="关联局办" />
      <el-table-column prop="relatedRequirements" label="相关要求" show-overflow-tooltip/>
      <el-table-column label="操作" width="120">
        <template slot-scope="scope">
          <el-button type="text" size="mini" @click="addOrUpdateHandle(scope.row.id)">编辑</el-button>
          <el-button type="text" size="mini" @click="deleteRow(scope.row.id)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="loadTable"></add-or-update>
    <!-- 导入弹窗 -->
    <item-import v-if="true" ref="itemImport" @refreshDataList="loadTable"></item-import>
  </el-card>
</template>

<script>
import AddOrUpdate from './item-add-or-update.vue'
import ItemImport from './item-import.vue'

export default {
  name: 'TablePanel',
  data() {
    return {
      dataList: [],
      indexId: null,
      indexName: null,
      addOrUpdateVisible: false,
      dataListLoading: false
    };
  },
  components: {
    ItemImport,
    AddOrUpdate
  },
  methods: {
    init(indexId, indexName) {
      this.indexId = indexId
      this.indexName = indexName
      this.loadTable()
    },
    loadTable() {
      this.dataListLoading = true;
      this.$http({
        url: this.$http.adornUrl('/admin/ledger/indexItem/getByIndexId'),
        method: 'get',
        params: this.$http.adornParams({'indexId': this.indexId})
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.dataList = data.obj
        } else {
          this.dataList = []
        }
        this.dataListLoading = false
      })
    },
    addOrUpdateHandle(id) {
      this.addOrUpdateVisible = true
      this.$nextTick(() => {
        this.$refs.addOrUpdate.init(id, this.indexId, this.indexName)
      })
    },
    importHandle () {
      this.$refs.itemImport.init()
    },
    deleteRow(id) {
      this.$confirm(`确定进行删除操作?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        closeOnClickModal: false
      }).then(() => {
        this.$http({
          url: this.$http.adornUrl(`/admin/ledger/indexItem/removeByIds`),
          method: 'get',
          params: this.$http.adornParams({'ids': id})
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.$message({
              message: '操作成功',
              type: 'success',
              duration: 1500,
              onClose: () => {
                this.loadTable()
              }
            })
          } else {
            this.$message.error(data.msg)
          }
        })
      })
    },
    // 导出
    exportExcel() {
      this.$http({
        url: this.$http.adornUrl('/admin/ledger/indexItem/exportExcel'),
        method: 'get',
        responseType: 'arraybuffer',
        params: this.$http.adornParams({'indexId': this.indexId})
      }).then(({data}) => {
        if (data.code && data.code !== 0) {
          this.$message.error('导出失败')
        } else {
          let blob = new Blob([data], {type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8'})
          let objectUrl = URL.createObjectURL(blob)
          let a = document.createElement('a')
          // window.location.href = objectUrl
          a.href = objectUrl
          a.download = this.indexName + '台账项列表.xlsx'
          a.click()
          URL.revokeObjectURL(objectUrl)
          this.$message({
            type: 'success',
            message: '导出成功'
          })
        }
      })
    }
  }
};
</script>
<style scoped>
.table-panel {
  height: 100%;
  overflow: auto;
}
</style>
