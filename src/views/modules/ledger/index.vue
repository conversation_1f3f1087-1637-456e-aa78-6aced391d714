<template>
  <div class="ledger-manage">
    <el-row :gutter="20" style="height: 100%;">
      <!-- 左侧树结构区域 -->
      <el-col :span="8" style="height: 100%;">
        <tree-panel @node-click="handleNodeClick" />
      </el-col>

      <!-- 右侧表格 -->
      <el-col :span="16" style="height: 100%;">
        <item ref="item"/>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import TreePanel from './TreePanel.vue'
import Item from './item.vue'

export default {
  components: {
    TreePanel,
    Item
  },
  methods: {
    handleNodeClick(node) {
      this.$nextTick(() => {
        this.$refs.item.init(node.id, node.name)
      });

    }
  }
}
</script>

<style scoped>
.el-row {
  height: 100%;
}
.el-col {
  height: 100%;
  overflow: auto;
}
</style>