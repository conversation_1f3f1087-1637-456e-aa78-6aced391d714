<template>
  <el-dialog
    id="excelImport"
    title="批量导入责任局办信息"
    :close-on-click-modal="false"
    :visible.sync="visible"
    width="40%"
    :show-close="!uploadLoading">
    <el-form :model="dataForm" ref="dataForm" :rules="dataRule" label-width="120px" v-loading="uploadLoading">
      <el-form-item label="第一步：" prop="prodName" style="background-color:rgba(242, 242, 242, 1); border: 1px solid rgba(215, 215, 215, 1); border-radius: 4px">
        <div>下载excel导入模板，按格式批量填写数据</div>
        <div>根据提示信息完善表格内容</div>
        <el-button type="text" @click="downloadTemplate()">
          <i class="el-icon-download el-icon--right"></i>
          {{this.dataForm.templateName}}
        </el-button>
      </el-form-item>
      <el-form-item label="第二步：" prop="excel" style="background-color:rgba(242, 242, 242, 1); border: 1px solid rgba(215, 215, 215, 1); border-radius: 4px">
        <div>上传完善后的表格</div>
        <el-upload
            ref="upload"
            drag
            name="excel"
            :action="this.$http.adornUrl(importPath)"
            :headers="myHeaders"
            :data="{date: this.dataForm.date}"
            :on-success="successHandle"
            :on-change="changeHandle"
            :limit=1
            :http-request="uploadFile"
            :on-exceed="handleExceed"
            :before-upload="function (file){return docBeforeUpload(file)}"
            :file-list="fileList"
            :auto-upload="false">
          <i class="el-icon-upload"></i>
          <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
          <div class="el-upload__text">支持格式：xls、xlsx</div>
        </el-upload>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false" :disabled="uploadLoading">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()" :disabled="uploadLoading">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import Vue from 'vue'

export default {
    data () {
      return {
        visible: false,
        dataForm: {
          templateName: '责任局办导入模板.xlsx',
          excel: null
        },
        templateFileName: '责任局办导入模板.xlsx',
        downloadPath: '/admin/ledger/department/template',
        importPath: '/admin/ledger/department/import',
        questionFileName: '责任局办导入问题数据.xlsx',
        dataRule: {
        },
        myHeaders: {Authorization: Vue.cookie.get('Authorization')},
        fileList: [],
        uploadLoading: false
      }
    },
    methods: {
      init () {
        this.visible = true
        this.fileList = []
        this.uploadLoading = false
        this.$nextTick(() => {
          this.$refs['dataForm']?.resetFields()
        })
      },
      docBeforeUpload (file) {
        let FileExt = file.name.replace(/.+\./, '')
        if (['xlsx', 'xls'].indexOf(FileExt.toLowerCase()) === -1) {
          this.$message({
            type: '上传失败',
            message: '请上传后缀名为xlsx, xls的附件！'
          })
          this.uploadLoading = false
          return false
        }
        const isLt50M = file.size / 1024 / 1024 < 50
        if (['xlsx', 'xls'].indexOf(FileExt.toLowerCase()) !== -1 && !isLt50M) {
          this.$message.error('附件大小不能超过 50M!')
          this.uploadLoading = false
          return false
        }
      },
      // 表单提交
      dataFormSubmit () {
        if (this.fileList.length === 0) {
          this.$message.error('请选择excel文件')
        }
        this.$refs['dataForm'].validate((valid) => {
          if (valid) {
            this.uploadLoading = true
            this.$refs.upload.submit()
          }
        })
      },
      // 上传成功
      successHandle (response) {
        if (response && response.code === 0) {
          this.$message({
            message: '操作成功',
            type: 'success',
            onClose: () => { }
          })
          this.uploadLoading = false
          this.$refs.upload.clearFiles()
          this.visible = false
          this.$emit('refreshDataList')
        } else {
          this.uploadLoading = false
          this.$refs.upload.clearFiles()
          this.$message.error(response.msg)
        }
      },
      changeHandle (file, fileList) {
        let FileExt = file.name.replace(/.+\./, '')
        const isLt50M = file.size / 1024 / 1024 < 50
        if (['xlsx', 'xls'].indexOf(FileExt.toLowerCase()) === -1) {
          this.$message({
            type: '上传失败',
            message: '请上传后缀名为xlsx或xls的文件！'
          })
          this.fileList = []
          return
        }
        if (!isLt50M) {
          this.$message.error('上传文件大小不能超过 50M!')
          this.fileList = []
          return
        }
        this.fileList = fileList
      },
      handleExceed () {
        this.$message.error('只能选择一个文件，如需替换请删除已选文件后重试')
      },
      // 下载模板文件
      downloadTemplate () {
        this.$http({
          url: this.$http.adornUrl(this.downloadPath),
          method: 'get',
          responseType: 'arraybuffer',
          params: this.$http.adornParams()
        }).then(({data}) => {
          if (data.code && data.code !== 0) {
            this.$message.error('下载失败')
          } else {
            let blob = new Blob([data], {type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8'})
            let objectUrl = URL.createObjectURL(blob)
            let a = document.createElement('a')
            // window.location.href = objectUrl
            a.href = objectUrl
            a.download = this.templateFileName
            a.click()
            URL.revokeObjectURL(objectUrl)
            this.$message({
              type: 'success',
              message: '下载模板成功'
            })
          }
        })
      },
      uploadFile (params) {
        let file = params.file
        let FileExt = file.name.replace(/.+\./, '')
        if (['xlsx', 'xls'].indexOf(FileExt.toLowerCase()) === -1) {
          this.$message({
            type: '上传失败',
            message: '请上传后缀名为xlsx, xls的附件！'
          })
          return false
        }
        const isLt50M = file.size / 1024 / 1024 < 50
        if (['xlsx', 'xls'].indexOf(FileExt.toLowerCase()) !== -1 && !isLt50M) {
          this.$message.error('附件大小不能超过 50M!')
          return false
        }
        this.$http({
          timeout: 600 * 1000,
          url: this.$http.adornUrl(this.importPath),
          method: 'post',
          data: this.$http.adornData({
            'excel': file
          }, true, 'file'),
          responseType: 'arraybuffer'
        }).then(({data}) => {
          var enc = new TextDecoder('utf-8')
          var str = enc.decode(new Uint8Array(data))
          if (!str) {
            this.$alert('所有数据导入成功', '成功', {
              type: 'success',
              confirmButtonText: '确定'
            })
          }
          else if (str.substring(0, 1) === '{') {
            var jsonData = JSON.parse(str)
            this.$alert(jsonData.msg, '出错了', {
              type: 'warning',
              confirmButtonText: '确定'
            })
          } else {
            let blob = new Blob([data], {type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8'})
            let objectUrl = URL.createObjectURL(blob)
            let a = document.createElement('a')
            a.href = objectUrl
            a.download = this.questionFileName
            a.click()
            URL.revokeObjectURL(objectUrl)
            this.$alert('操作成功，部分数据导入失败，请查看浏览器自动下载的EXCEL。纠正后再次导入', '部分数据有错误', {
              type: 'warning',
              confirmButtonText: '确定'
            })
          }
          this.uploadLoading = false
          this.$refs.upload.clearFiles()
          this.visible = false
          this.$emit('refreshDataList')
        })
      }
    }
  }
</script>
<style lang="scss" scoped>
</style> 
