<template>
  <div class="mod-list">
    <el-form :inline="true" :model="dataForm" ref="dataForm" @keyup.enter.native="getDataList()">
      <el-form-item prop="departmentName" label="局办名称">
        <el-input v-model="dataForm.departmentName" placeholder="请输入局办名称模糊查询" clearable></el-input>
      </el-form-item>
      <el-form-item prop="contactPerson" label="联系人">
        <el-input v-model="dataForm.contactPerson" placeholder="请输入联系人模糊查询" clearable></el-input>
      </el-form-item>
      <el-form-item prop="account" label="关联账号">
        <el-input v-model="dataForm.account" placeholder="请输入关联账号模糊查询" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-search" type="warning" @click="getDataList()">查询</el-button>
        <el-button @click="resetForm()"icon="el-icon-refresh-left">重置</el-button>
      </el-form-item>
    </el-form>
    <div class="f-s-c" style="padding: 15px 0; display: flex; justify-content: flex-end; align-items: center">
      <div>
        <el-button type="primary" @click="addOrUpdateHandle()" icon="el-icon-plus">新增</el-button>
        <el-button type="success" @click="importHandle()">导入</el-button>
      </div>
    </div>
    <el-table :data="dataList" border v-loading="dataListLoading" style="width: 100%;">
      <el-table-column prop="departmentName" header-align="center" align="center" label="局办名称"/>
      <el-table-column prop="contactPerson" header-align="center" align="center" label="联系人"/>
      <el-table-column prop="contact" header-align="center" align="center" label="联系方式"/>
      <el-table-column prop="userId" header-align="center" align="center" label="关联账号"><template slot-scope="scope">
        <span >{{scope.row.name + '--' + scope.row.mobile }}</span>
      </template>
      </el-table-column>
      <el-table-column prop="status" header-align="center" align="center" label="状态">
        <template slot-scope="scope">
          <el-tag v-if="!scope.row.status" size="small" type="danger">未上架</el-tag>
          <el-tag v-else size="small">已上架</el-tag>
        </template>
      </el-table-column>
        <el-table-column fixed="right" header-align="center" align="center" width="150" label="操作">
        <template slot-scope="scope">
          <el-button type="text" @click="addOrUpdateHandle(scope.row.id)">编辑</el-button>
          <el-button class="delete_btn" type="text" @click="deleteHandle(scope.row.id,scope.row.name)">删除</el-button>
          <el-button size="small" type="text" @click="setStatusHandle(scope.row.id, scope.row.status)" >{{ !scope.row.status ? '上架' : '下架' }}</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination @size-change="sizeChangeHandle" @current-change="currentChangeHandle" :current-page="pageIndex"
                   :page-sizes="[10, 20, 50, 100]" :page-size="pageSize" :total="totalPage" layout="total, sizes, prev, pager, next, jumper"/>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"/>
    <!-- 导入弹窗 -->
    <department-import v-if="true" ref="departmentImport" @refreshDataList="getDataList"></department-import>
  </div>
</template>

<script>
  import AddOrUpdate from './add-or-update.vue'
  import listMixin from '@/mixins/list-mixins'
  import DepartmentImport from './department-import.vue'
  export default {
    mixins: [listMixin],
    data () {
      return {
        mixinOptions: {
          dataUrl: '/admin/ledger/department/pages',
          deleteUrl: '/admin/ledger/department/removeByIds'
        },
        dataForm: {
          departmentName: null,
          account: null,
          contactPerson: null
        }
      }
    },
    components: {
      AddOrUpdate,
      DepartmentImport
    },
    methods: {
      resetForm() {
        this.$refs['dataForm']?.resetFields()
        this.getDataList()
      },
      importHandle () {
        this.$refs.departmentImport.init()
      },
      deleteHandle(id) {
        this.$confirm(`确定进行删除操作?`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
          closeOnClickModal: false
        }).then(() => {
          this.$http({
            url: this.$http.adornUrl(`/admin/ledger/department/deleteById`),
            method: 'get',
            params: this.$http.adornParams({'id': id})
          }).then(({data}) => {
            if (data && data.code === 0) {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 1500,
                onClose: () => {
                  this.getDataList()
                }
              })
            } else {
              this.$message.error(data.msg)
            }
          })
        })
      },
      // 上下架
      setStatusHandle(id, status) {
        this.$confirm(`确定进行[${!status ? '上架' : '下架'}]操作?`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
          closeOnClickModal: false
        }).then(() => {
          this.$http({
            url: this.$http.adornUrl('/admin/ledger/department/setStatus'),
            method: 'get',
            params: this.$http.adornParams({
              'id': id
            })
          }).then(({data}) => {
            if (data && data.code === 0) {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 1500,
                onClose: () => {
                  this.getDataList()
                }
              })
            } else {
              this.$message.error(data.msg)
            }
          })
        }).catch(() => {
        })
      },
    }
  }
</script>

<style lang="scss" scoped>
</style>
