<template>
  <el-dialog class="common-dialog" :title="!dataForm.id ? '新增' : '修改'" :visible.sync="visible"
             :close-on-click-modal="false" :modal-append-to-body="false" width="60%">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmit()" label-width="90px">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="局办名称" prop="departmentName">
            <el-input v-model="dataForm.departmentName" placeholder="请输入" clearable/>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="联系人" prop="contactPerson">
            <el-input v-model="dataForm.contactPerson" placeholder="请输入" clearable/>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="联系方式" prop="contact">
            <el-input v-model="dataForm.contact" placeholder="请输入" clearable/>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="状态" prop="status">
            <el-radio-group v-model="dataForm.status">
              <el-radio :label="true">上架</el-radio>
              <el-radio :label="false">下架</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="用户搜索" prop="searchKeyword">
            <el-select style="width: 100%" v-model="dataForm.searchKeyword" filterable remote placeholder="请输入用户名/手机号/姓名"
                       value-key="id" :remote-method="searchUser" :multiple-limit="1" :loading="searchLoading" @change="(value) => userSelected(value)" clearable no-data-text="该账号不存在，请先前往小程序注册！">
              <el-option
                  v-for="item in userList"
                  :key="item.id"
                  :label="item.name"
                  :value="item">
                <span style="float: left">{{ item.name }}</span>
                <span style="float: right; color: #8492a6; font-size: 13px">{{ item.mobile + '(手机号)--' + item.username + '(用户名)' }}</span>
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="用户名" prop="username">
            <el-input placeholder="用户名" v-model="dataForm.username" disabled></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="手机号" prop="mobile">
            <el-input placeholder="手机号" v-model="dataForm.mobile" disabled></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="姓名" prop="name">
            <el-input placeholder="姓名" v-model="dataForm.name" disabled></el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()" :disabled="dataFormLoading">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
  import _ from 'lodash'
  import editMixin from '@/mixins/edit-mixins'
  export default {
    mixins: [editMixin],
    data () {
      return {
        editOptions: {
          initUrl: '/admin/ledger/department'
        },
        dataForm: {},
        searchLoading: false,
        userList: [],
        initForm: {
          id: null,
          version: null,
          searchKeyword: null,
          departmentName: null,
          contactPerson: null,
          contact: null,
          userId: null,
          username: null,
          name: null,
          mobile: null,
          status: true
        },
        dataRule: {
          departmentName: [
            { required: true, message: '局办名称不能为空', trigger: 'change' }
          ],
          contactPerson: [
            { required: true, message: '联系人不能为空', trigger: 'change' }
          ],
          contact: [
            { required: true, message: '联系方式不能为空', trigger: 'change' }
          ],
          username: [
            { required: true, message: '请选择用户不能为空', trigger: 'change' }
          ],
          mobile: [
            { required: true, message: '请选择用户不能为空', trigger: 'change' }
          ],
          name: [
            { required: true, message: '请选择用户不能为空', trigger: 'change' }
          ],
          status: [
            { required: true, message: '状态不能为空', trigger: 'change' }
          ]
        }
      }
    },
    methods: {
      init (id) {
        this.dataForm = _.cloneDeep(this.initForm)
        this.dataForm.id = id || null
        this.dataForm.searchKeyword = null
        this.visible = true
        this.$nextTick(() => {
          this.$refs['dataForm']?.resetFields()
          if (this.dataForm.id) {
            this.getData()
          }
        })
      },
      searchUser(value) {
        if (!value || value === '') {
          this.userList = []
          return
        }
        this.$http({
          url: this.$http.adornUrl('/admin/user/searchUser'),
          method: 'get',
          params: this.$http.adornParams({'keyword': value})
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.userList = data.obj|| []
          } else {
            this.userList = []
          }
        })
      },
      userSelected(value) {
        if (!value || value === '') {
          this.dataForm.userId = null
          this.dataForm.username = null
          this.dataForm.mobile = null
          this.dataForm.name = null
          return
        }
        this.dataForm.userId = value.id
        this.dataForm.username = value.username
        this.dataForm.mobile = value.mobile
        this.dataForm.name = value.name
      }
    }
  }
</script>

<style lang="scss" scoped>
</style>
