<template>
  <el-card class="tree-card" shadow="always">
    <div slot="header" class="clearfix">
      <span>台账指标</span>
      <el-button style="float: right;" type="primary" @click="addOrUpdateHandle(null, null)">
        新增指标
      </el-button>
    </div>
  <el-tree
      ref="tree"
      :data="treeData"
      :props="props"
      node-key="id"
      accordion
      highlight-current
      v-loading="loading"
      @node-click="handleNodeClick">
    <div class="custom-tree-node" slot-scope="{ node, data }">
      <el-tooltip effect="dark" :content="data.name" placement="top">
        <div class="tree-node-label">{{ data.name }}</div>
      </el-tooltip>

      <div class="tree-node-ops">
        <el-button type="text" size="mini" v-if="data.parentId == null" @click.stop="addOrUpdateHandle(null, data.id, data.name)">新增</el-button>
        <el-button type="text" size="mini" @click.stop="addOrUpdateHandle(data.id)">编辑</el-button>
        <el-button type="text" size="mini" @click.stop="deleteNode(data.id)">删除</el-button>
      </div>
    </div>
  </el-tree>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="loadTreeData"></add-or-update>
  </el-card>
</template>

<script>
import AddOrUpdate from './index-add-or-update'
export default {
  data() {
    return {
      treeData: [],
      loading: false,
      addOrUpdateVisible: false,
      props: {
        key: 'id',
        label: 'name',
        children: 'children'
      },
    };
  },
  created() {
    this.loadTreeData();
  },
  components: {
    AddOrUpdate
  },
  methods: {
    loadTreeData() {
      this.loading = true
      this.$http({
        url: this.$http.adornUrl('/admin/ledger/index/tree'),
        method: 'get',
        params: this.$http.adornParams()
      }).then(({data}) => {
        this.treeData = data.obj
        this.$nextTick(() => {
          if (this.treeData.length > 0) {
            const firstNode = this.treeData[0];
            this.$refs.tree.setCurrentKey(firstNode.id);
            this.$emit('node-click', firstNode);
          }
          this.loading = false
        });
      })
    },
    handleNodeClick(node) {
      this.$refs.tree.setCurrentKey(node.id);
      this.$emit('node-click', node);
    },
    deleteNode(id) {
      this.$confirm(`确定进行删除操作?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        closeOnClickModal: false
      }).then(() => {
        this.$http({
          url: this.$http.adornUrl(`/admin/ledger/index/deleteById`),
          method: 'get',
          params: this.$http.adornParams({'id': id})
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.$message({
              message: '操作成功',
              type: 'success',
              duration: 1500,
              onClose: () => {
                this.loadTreeData()
                this.data = {}
              }
            })
          } else {
            this.$message.error(data.msg)
          }
        })
      })
    },
    addOrUpdateHandle(id, parentId, parentName) {
      this.addOrUpdateVisible = true
      this.$nextTick(() => {
        this.$refs.addOrUpdate.init(id, parentId, parentName)
      })
    }
  }
};
</script>
<style scoped>
.tree-card {
  height: 100%;
  overflow: auto;
}

.full-width-tree ::v-deep .el-tree-node__content {
  width: 100%;
}

.custom-tree-node {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  overflow: hidden;
}

.tree-node-label {
  flex: 1;
  min-width: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  margin-right: 10px;
}

.tree-node-ops {
  display: flex;
  flex-shrink: 0;
  gap: 4px;
}
</style>

