<template>
  <el-dialog class="common-dialog" :title="!dataForm.id ? '新增' : '修改'" :visible.sync="visible"
             :close-on-click-modal="false" :modal-append-to-body="false" width="60%">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmit()" label-width="90px">
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="上级指标" prop="parentName" v-if="dataForm.parentName">
            <el-input v-model="dataForm.parentName" placeholder="请输入" disabled/>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="名称" prop="name">
            <el-input v-model="dataForm.name" placeholder="请输入" clearable/>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="排序" prop="sequence">
            <el-input-number v-model="dataForm.sequence" controls-position="right" :min="0"/>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()" :disabled="dataFormLoading">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
  import _ from 'lodash'
  import editMixin from '@/mixins/edit-mixins'
  export default {
    mixins: [editMixin],
    data () {
      return {
        editOptions: {
          initUrl: '/admin/ledger/index'
        },
        dataForm: {},
        initForm: {
          id: null,
          parentId: null,
          version: null,
          name: null,
          parentName: null,
          sequence: null
        },
        dataRule: {
          name: [
            { required: true, message: '名称不能为空', trigger: 'change' }
          ],
          sequence: [
            { required: true, message: '排序不能为空', trigger: 'change' }
          ]
        }
      }
    },
    methods: {
      init (id, parentId,parentName) {
        this.dataForm = _.cloneDeep(this.initForm)
        this.dataForm.id = id || null
        this.dataForm.parentId = parentId
        this.dataForm.parentName = parentName
        this.visible = true
        this.$nextTick(() => {
          this.$refs['dataForm']?.resetFields()
          if (this.dataForm.id) {
            this.getData()
          }
          console.log(this.dataForm)
        })
      }
    }
  }
</script>

<style lang="scss" scoped>
</style>
