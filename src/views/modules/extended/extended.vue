<template>
  <div class="mod-config">
    <el-form :inline="true" ref="dataForm" :model="dataForm" @keyup.enter.native="getDataList()">
      <el-form-item prop="name">
        <el-input v-model="dataForm.name" placeholder="属性名称" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-button @click="getDataList()">查询</el-button>
        <el-button icon="el-icon-refresh" @click="resetForm()">重置</el-button>
        <el-button type="primary" @click="addOrUpdateHandle()">新增</el-button>
        <el-button type="primary" @click="deleteHandle()" :disabled="dataListSelections.length <= 0">批量删除</el-button>
      </el-form-item>
    </el-form>
    <el-table
      :data="dataList"
      border
      v-loading="dataListLoading"
      @selection-change="selectionChangeHandle"
      style="width: 100%;">
      <el-table-column
        type="selection"
        header-align="center"
        align="center"
        width="50">
      </el-table-column>
      <el-table-column
        prop="name"
        header-align="center"
        align="center"
        label="属性名称">
      </el-table-column>
      <el-table-column
        prop="status"
        header-align="center"
        align="center"
        label="状态">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.status === false" size="small" type="danger">禁用</el-tag>
          <el-tag v-else size="small">启用</el-tag>
        </template>
      </el-table-column>
      <el-table-column
        fixed="right"
        header-align="center"
        align="center"
        width="150"
        label="操作">
        <template slot-scope="scope">
          <el-button v-if="isAuth('cms:extended:update')" type="text" size="small" @click="addOrUpdateHandle(scope.row.id)" class="btn-control">修改</el-button>
          <el-button v-if="isAuth('cms:extended:delete')" type="text" size="small" @click="deleteHandle(scope.row.id)" class="btn-control">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      @size-change="sizeChangeHandle"
      @current-change="currentChangeHandle"
      :current-page="pageIndex"
      :page-sizes="[10, 20, 50, 100]"
      :page-size="pageSize"
      :total="totalPage"
      layout="total, sizes, prev, pager, next, jumper">
    </el-pagination>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"></add-or-update>
  </div>
</template>

<script>
  import AddOrUpdate from './extended-add-or-update'
  import listMixin from '@/mixins/list-mixins'
  export default {
    mixins: [listMixin],
    data () {
      return {
        mixinOptions: {
          dataUrl: '/admin/cms/extended/pages',
          deleteUrl: '/admin/cms/extended/removeByIds'
        },
        dataForm: {
          name: ''
        }
      }
    },
    components: {
      AddOrUpdate
    }
  }
</script>
