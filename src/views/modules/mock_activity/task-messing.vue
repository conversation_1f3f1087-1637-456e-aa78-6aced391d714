<template>
  <div>
    <el-dialog
        :title="onlyRead === true ? '详情' : (!dataForm.id ? '新增' : '修改')"
        :close-on-click-modal="false"
        :visible.sync="visible" width="50%">
      <el-form :model="dataForm"  ref="dataForm" label-width="200px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="总生成的活动数：" prop="taskAllActivitiesNums">
              <el-input v-model="dataForm.taskAllActivitiesNums" placeholder="请输入" clearable :disabled="onlyRead"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="同步过的活动数：" prop="syncActivitiesNums">
              <el-input v-model="dataForm.syncActivitiesNums" placeholder="请输入" clearable :disabled="onlyRead"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
          <el-form-item label="同步成功的活动数：" prop="successActivitiesNums">
            <el-input v-model="dataForm.successActivitiesNums" placeholder="请输入" clearable :disabled="onlyRead"></el-input>
          </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="导入的志愿者数量：" prop="importVolunteerNums">
              <el-input v-model="dataForm.importVolunteerNums" placeholder="请输入" clearable :disabled="onlyRead"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="同步成功的志愿者数：" prop="successVolunteerNums">
              <el-input v-model="dataForm.successVolunteerNums" placeholder="请输入" clearable :disabled="onlyRead"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="同步成功的总时长：" prop="realServiceLong">
              <el-input v-model="dataForm.realServiceLong" placeholder="请输入" clearable :disabled="onlyRead"></el-input>
            </el-form-item>
          </el-col>

        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="生成所有的活动招募数量：" prop="taskAllAppliesNums" >
              <el-input v-model="dataForm.taskAllAppliesNums" :disabled="onlyRead" ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="同步过的活动招募数量：" prop="syncAppliesNums" >
              <el-input v-model="dataForm.syncAppliesNums" :disabled="onlyRead" ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button v-if="!onlyRead || onlyRead === false" @click="visible = false">取消</el-button>
        <el-button v-if="onlyRead && onlyRead === true" @click="getData()">刷新</el-button>
        <el-button v-if="onlyRead && onlyRead === true" @click="visible = false">关闭</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>

export default {
  data() {
    return {
      visible: false,
      onlyRead: true,
      id:null,
      dataForm: {
        parameterId: null,
        realServiceLong:null,
        successActivitiesNums: null,
        successVolunteerNums: null,
        importVolunteerNums: null,
        taskAllAppliesNums: null,
        taskAllActivitiesNums: null,
        syncActivitiesNums: null,
        syncAppliesNums: null
      },
      dataList: [],
      totalPage: 0,
      dataListLoading: false,
      activityApplyDetailVisible: false,
    }
  },
  methods: {
    init(id) {
      this.dataForm.parameterId = id || null
      this.id=id ||null
      this.visible = true
      this.dataForm.id = id || null
      this.$nextTick(() => {
        this.$refs['dataForm']?.resetFields()
        if (this.dataForm.id) {
          this.$http({
            url: this.$http.adornUrl(`/admin/parameter/copy/activity/queryByParameterId`),
            method: 'get',
            params: this.$http.adornParams({
              'parameterId': id
            })
          }).then(({
                     data
                   }) => {
            if (data && data.code === 0) {
              this.dataForm = data.obj
            }
          })
        }
      })
    },
    getData(){
      this.$nextTick(() => {
        this.$refs['dataForm']?.resetFields()
          this.$http({
            url: this.$http.adornUrl(`/admin/parameter/copy/activity/queryByParameterId`),
            method: 'get',
            params: this.$http.adornParams({
              'parameterId': this.id
            })
          }).then(({
                     data
                   }) => {
            if (data && data.code === 0) {
              this.dataForm = data.obj
            }
          })
      })
    }
  }
}
</script>