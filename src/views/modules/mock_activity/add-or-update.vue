<template>
  <div>
    <el-dialog :title="'新增'" :close-on-click-modal="false" width="80%"
               :visible.sync="visible">
      <el-form :model="dataForm" :rules="dataRule" ref="dataForm"
               label-width="120px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="有效日期" prop="startEndTime">
              <el-date-picker
                  v-model="dataForm.startEndTime"
                  type="daterange"
                  value-format="yyyy-MM-dd"
                  range-separator="-"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期">
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="选中创建活动的时间段" prop="endTime">
              <span slot="label" style="color: red">* <span style="color: black">活动时间</span></span>
              <div style="width:100%; display: flex; justify-content: space-between">
                <el-time-select
                    placeholder="起始时间"
                    v-model="dataForm.startTime"
                    value-format="HH:mm"
                    style="width: 45%"
                    :picker-options="{
                  start: '00:00',
                  step: '00:15',
                  end: '24:00',
                  maxTime: dataForm.endTime}"/>
                <el-time-select
                    placeholder="结束时间"
                    style="width: 45%"
                    v-model="dataForm.endTime"
                    value-format="HH:mm"
                    :picker-options="{
                  start: '00:00',
                  step: '00:15',
                  end: '24:00',
                  minTime: dataForm.startTime}"/>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="时间段分组" prop="actTGroupSection">
              <el-input v-model="dataForm.actTGroupSection" placeholder="选中时间的使用情况最小单位小时以,分割"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="需要造的活动数" prop="activityNums">
              <el-input-number v-model="dataForm.activityNums" controls-position="right" :min="0"></el-input-number>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="最小志愿者人数" prop="minVolunteerNums">
              <el-input-number v-model="dataForm.minVolunteerNums" controls-position="right" :min="0"></el-input-number>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="最大志愿者人数" prop="maxVolunteerNums">
              <el-input-number v-model="dataForm.maxVolunteerNums" controls-position="right" :min="0"></el-input-number>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="活动名称自定义字段" prop="customFields">
              <el-input v-model="dataForm.customFields" placeholder="活动名称自定义字段"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <el-form :model="uploadForm" ref="uploadForm" label-width="145px">
        <el-form-item label="上传文件：" prop="excel">
          <el-upload
              ref="upload"
              name="excel"
              :action="this.$http.adornUrl(`/admin/parameter/copy/activity/uploadExcel`)"
              :data="{}"
              :headers="myHeaders"
              :on-success="successHandle"
              :on-change="changHandle"
              :file-list="fileList"
              :limit=1
              :on-exceed="handleExceed"
              :auto-upload="true">
            <el-button type="primary">选取文件</el-button>
            <div slot="tip" class="el-upload__tip">支持上传{{ fileExts }}文件</div>
          </el-upload>
        </el-form-item>
        <el-form-item label="Excel模板:" prop="prodName">
          <el-button type="text" @click="downloadTemplate()">
            <i class="el-icon-download el-icon--right"></i>
            导入模板下载.xlsx
          </el-button>
        </el-form-item>
      </el-form>

      <span slot="footer" class="dialog-footer">
				<el-button @click="visible = false">取消</el-button>
				<el-button type="primary" :disabled="isDisabled" @click="dataFormSubmit()">确定</el-button>
			</span>
    </el-dialog>
  </div>
</template>

<script>
import moment from 'moment'
import Vue from 'vue'

export default {
  data() {
    return {
      visible: false,
      isDisabled: false,
      fileList: [],
      uploadForm: {
        excel: null
      },
      fileExts: 'xls/xlsx',
      myHeaders: {Authorization: Vue.cookie.get('Authorization')},
      initForm: {
        id: null,
        version: null,
        startTime: '',
        endTime: '',
        actSection: null,
        actEndDay: null,
        actStartDay: null,
        startEndTime: null,
        actTimeSectionStartTime: null,
        actTimeSectionEndTime: null,
        actTGroupSection: null,
        activityNums: null,
        maxVolunteerNums: null,
        minVolunteerNums: null,
        customFields: null,
        complete: null,
        volunteerList: []
      },
      dataForm: {},
      dataRule: {
        actSection: [
          {required: true, message: '选中的日期时间段不能为空', trigger: 'blur'}
        ],
        actTSection: [
          {required: true, message: '选中的每天的时间段不能为空', trigger: 'blur'}
        ],
        actTGroupSection: [
          {required: true, message: '时间段分组不能为空', trigger: 'blur'}
        ],
        activityNums: [
          {required: true, message: '需要造的活动数不能为空', trigger: 'blur'}
        ],
        maxVolunteerNums: [
          {required: true, message: '最大志愿者人数不能为空', trigger: 'blur'}
        ],
        minVolunteerNums: [
          {required: true, message: '最小志愿者人数不能为空', trigger: 'blur'}
        ],
        complete: [
          {required: true, message: '任务是否完成不能为空', trigger: 'blur'}
        ]
      }
    }
  },
  components: {
    moment
  },
  methods: {
    async init(id) {
      this.dataForm = _.cloneDeep(this.initForm)
      this.dataForm.id = id || null
      this.visible = true
      this.fileList = []
      this.$nextTick(() => {
        this.$refs['dataForm']?.resetFields()
      })
    },
    // 上传成功
    successHandle(response) {
      if (response && response.code === 0) {
        this.$message({
          message: '操作成功',
          type: 'success',
          onClose: () => {
            this.dataForm.volunteerList = response.obj
            console.log(this.dataForm.volunteerList)
          }
        })
      } else {
        this.$message.error(response.msg)
      }
      this.fileList = []
    },
    handleExceed() {
      this.$message.error('只能选择一个文件，如需替换请删除已选文件后重试')
    },
    // 资源上传过滤
    beforeFileUpload() {
      let size = this.fileList.length
      if (size === 0) {
        this.$message.error('请选择资源文件')
        return false
      }
      if (size > 1) {
        this.$message.error('只能上传单个资源，请删除资源后再试')
        return false
      }
      let fileName = this.fileList[0].name
      if (fileName.indexOf('.') === -1) {
        this.$message.error('不支持的文件格式')
      }

      let fileExt = fileName.substring(fileName.lastIndexOf('.') + 1)
      let allowTypes = `/${this.fileExts}/`
      if (allowTypes.indexOf(`/${fileExt}/`) === -1) {
        this.$message.error(`上传资源只能是${this.fileExts}格式!`)
        return false
      }
      return true
    },
    changHandle(file, fileList) {
      this.fileList = fileList
    },
    close() {
      this.visible = false
    },
    // 下载模板文件
    downloadTemplate() {
      this.$http({
        url: this.$http.adornUrl('/admin/parameter/copy/activity/downloadTemplate'),
        method: 'get',
        responseType: 'arraybuffer',
        params: this.$http.adornParams()
      }).then(({data}) => {
        if (data.code && data.code !== 0) {
          this.$message.error('下载失败')
        } else {
          let blob = new Blob([data], {type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8'})
          let objectUrl = URL.createObjectURL(blob)
          let a = document.createElement('a')
          // window.location.href = objectUrl
          a.href = objectUrl
          a.download = '模板.xlsx'
          a.click()
          URL.revokeObjectURL(objectUrl)
          this.$message({
            type: 'success',
            message: '下载模板成功'
          })
        }
      })
    },
    // 表单提交
    dataFormSubmit() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          let s = this.dataForm.actTGroupSection.split(",")
          if (!(Array.isArray(s) && s.length)) {
            this.$message.error('时间段分组不能为空')
            return
          }
          let times = 0;
          for (var nums = 0; nums < s.length; nums++) {
            times += parseInt(s[nums])
          }
          let tim = (parseInt(this.dataForm.endTime) - parseInt(this.dataForm.startTime))
          if (tim < times) {
            this.$message.error('时间段分组大于选定时间')
            return
          }
          let totalDays, diffDate
          let myDate_1 = Date.parse(this.dataForm.startEndTime[1])
          let myDate_2 = Date.parse(this.dataForm.startEndTime[0])
          diffDate = Math.abs(myDate_1 - myDate_2)
          totalDays = Math.floor(diffDate / (1000 * 3600 * 24))
          let maxActNums = s.length * (totalDays + 1)
          if (this.dataForm.activityNums > maxActNums) {
            this.$message.error('活动数超出可造活动数')
            return
          }
          if (this.dataForm.maxVolunteerNums < this.dataForm.minVolunteerNums) {
            this.$message.error('最大志愿者人数小于最小志愿者人数')
            return
          }
          if (!(Array.isArray(this.dataForm.volunteerList) && this.dataForm.volunteerList.length)) {
            this.$message.error('导入的志愿者名单不能为空')
            return
          }
          this.dataForm.actStartDay = this.dataForm.startEndTime[0]
          this.dataForm.actEndDay = this.dataForm.startEndTime[1]
          this.dataForm.actTimeSectionStartTime = this.dataForm.startTime
          this.dataForm.actTimeSectionEndTime = this.dataForm.endTime
          this.isDisabled = true
          this.$http({
            url: this.$http.adornUrl(`/admin/parameter/copy/activity/save`),
            method: 'post',
            data: this.$http.adornData(this.dataForm)
          }).then(({
                     data
                   }) => {
            if (data && data.code === 0) {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 1500,
                onClose: () => {
                  this.visible = false
                  console.log(data)
                  if (!this.dataForm.id) {
                    this.$emit('refreshDataList')
                  } else {
                    this.$emit('refreshDataList')
                  }
                  this.visible = false
                  this.isDisabled = false
                }
              })
            } else if (data && data.code === 303) {
              for (let it of data.obj) {
                this.visible = true
                this[`${it.field}Error`] = it.message
              }
              this.visible = false
              this.isDisabled = false
            } else {
              this.visible = false
              this.isDisabled = false
              this.$message.error(data.msg)

            }
          })
        }
      })
    }
  }
}
</script>
