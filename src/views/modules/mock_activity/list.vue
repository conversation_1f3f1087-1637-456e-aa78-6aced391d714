<template>
  <el-dialog
      :title="'活动列表详情'"
      :close-on-click-modal="false"
      width="80%"
      append-to-body
      :visible.sync="visible">
    <el-form :inline="true" :model="dataForm" ref="dataForm" @keyup.enter="getDataList()">
      <el-form-item label="同步状态:" prop="sync" clearable>
        <el-dict :code="'sync_status'" v-model="dataForm.sync"></el-dict>
      </el-form-item>
      <el-form-item>
        <el-button @click="currentChangeHandle(1)">查询</el-button>
        <el-button type="warning" @click="resetForm()">重置</el-button>
      </el-form-item>
    </el-form>
    <el-table
        :data="dataList"
        border
        v-loading="dataListLoading"
        style="width: 100%;">
      <el-table-column
          prop="name"
          header-align="center"
          align="center"
          label="活动名称">
      </el-table-column>
      <el-table-column
          prop="recruitmentNum"
          header-align="center"
          align="center"
          label="招募总人数">
      </el-table-column>
      <el-table-column
          prop="sync"
          header-align="center"
          align="center"
          label="同步状态">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.sync === 'sync_failure'" size="small" type="danger">同步失败</el-tag>
          <el-tag v-if="scope.row.sync === 'sync_wait'" size="small" type="warning">待同步</el-tag>
          <el-tag v-if="scope.row.sync === 'sync_success'" size="small" type="success">同步成功</el-tag>
        </template>
      </el-table-column>
      <el-table-column
          prop="syncRemark"
          header-align="center"
          align="center"
          label="同步描述">
      </el-table-column>
      <el-table-column
          fixed="right"
          header-align="center"
          align="center"
          width="150"
          label="操作">
        <template #default="scope">
          <el-button type="text" size="small" v-if="scope.row.sync !== 'sync_success'"
                     @click="synchronousHandle(scope.row.activityId)">同步
          </el-button>
          <el-button type="text" size="small" @click="getActivityApplyDetail(scope.row.activityId)">招募详情</el-button>
        </template>
      </el-table-column>
    </el-table>
    <div class="com-pagination">
      <el-pagination
          @size-change="sizeChangeHandle"
          @current-change="currentChangeHandle"
          :current-page="dataForm.currentPage"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="dataForm.pageSize"
          :total="totalPage"
          layout="total, sizes, prev, pager, next, jumper"
      />
    </div>
    <!-- 弹窗, 新增 / 修改 -->
    <activity-apply-detail v-if="activityApplyDetailVisible" ref="activityApplyDetail"
                           @refreshDataList="getDataList"></activity-apply-detail>
  </el-dialog>
</template>

<script>
import ActivityApplyDetail from './apply-list'

export default {
  data() {
    return {
      visible: false,
      dataForm: {
        sync: null,
        parameterId: null,
        currentPage: 1,
        pageSize: 10
      },
      dataList: [],
      totalPage: 0,
      dataListLoading: false,
      activityApplyDetailVisible: false,
    }
  },
  components: {
    ActivityApplyDetail
  },
  activated() {
    this.getDataList()
  },
  methods: {
    init(id) {
      this.dataForm.parameterId = id || null
      this.visible = true
      this.getDataList()
    },

    // 获取数据列表
    getDataList() {
      this.dataListLoading = true
      this.$http({
        url: this.$http.adornUrl('/admin/parameter/copy/activity/getPages'),
        method: 'post',
        data: this.$http.adornData({
          'currentPage': this.pageIndex,
          'pageSize': this.pageSize,
          'orgCode': this.dataForm.orgCode,
          'sync': this.dataForm.sync,
          'parameterId': this.dataForm.parameterId
        })
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.dataList = data.obj.content
          this.totalPage = data.obj.totalElements
        } else {
          this.dataList = []
          this.totalPage = 0
        }
        this.dataListLoading = false
      })
    },
    // 每页数
    sizeChangeHandle(val) {
      this.dataForm.pageSize = val
      this.dataForm.currentPage = 1
      this.getDataList()
    },
    // 当前页
    currentChangeHandle(val) {
      this.dataForm.currentPage = val
      this.getDataList()
    },
    // 重置
    resetForm() {
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        this.getDataList()
      })
    },
    // 同步
    synchronousHandle(id) {
      let ids = id
      this.$confirm(`确定要进行同步吗，确定进行同步操作?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        closeOnClickModal: false
      }).then(() => {
        this.$http({
          url: this.$http.adornUrl('/admin/parameter/copy/activity/syncAct'),
          method: 'get',
          params: this.$http.adornParams({
            'actId': ids
          })
        }).then(({
                   data
                 }) => {
          if (data && data.code === 0) {
            this.$message({
              message: '操作成功',
              type: 'success',
              duration: 1500,
              onClose: () => {
                this.getDataList()
              }
            })
          } else {
            this.$message.error(data.msg)
          }
        })
      })
    },
    getActivityApplyDetail(id) {
      this.activityApplyDetailVisible = true
      this.$nextTick(() => {
        this.$refs.activityApplyDetail.init(id)
      })
    }
  }
}
</script>
