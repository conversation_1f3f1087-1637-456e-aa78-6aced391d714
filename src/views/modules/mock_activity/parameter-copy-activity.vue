<template>
  <div class="mod-config">
    <el-form :inline="true" :model="dataForm" @keyup.enter.native="queryPage()">
      <el-form-item label="任务名称关键字:">
        <el-input v-model="dataForm.key" placeholder="名称生成中包含当天日期yyyyMMdd" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-button @click="queryPage()">查询</el-button>
        <el-button type="primary" @click="addOrUpdateHandle()">新增</el-button>
      </el-form-item>
    </el-form>
    <el-table
        :data="dataList"
        border
        v-loading="dataListLoading"
        @selection-change="selectionChangeHandle"
        style="width: 100%;">
      <el-table-column
          prop="name"
          header-align="center"
          align="center"
          label="任务名称">
      </el-table-column>
      <el-table-column
          prop="actStartDay"
          header-align="center"
          align="center"
          label="日期段开始">
      </el-table-column>
      <el-table-column
          prop="actEndDay"
          header-align="center"
          align="center"
          label="日期段结束">
      </el-table-column>
      <el-table-column
          prop="actTimeSectionStartTime"
          header-align="center"
          align="center"
          label="时间段开始">
      </el-table-column>
      <el-table-column
          prop="actTimeSectionEndTime"
          header-align="center"
          align="center"
          label="时间段结束">
      </el-table-column>
      <el-table-column
          prop="actTGroupSection"
          header-align="center"
          align="center"
          label="时间段分组">
      </el-table-column>
      <el-table-column
          prop="activityNums"
          header-align="center"
          align="center"
          label="活动数">
      </el-table-column>
      <el-table-column
          prop="maxVolunteerNums"
          header-align="center"
          align="center"
          label="最大人数">
      </el-table-column>
      <el-table-column
          prop="minVolunteerNums"
          header-align="center"
          align="center"
          label="最小人数">
      </el-table-column>
      <el-table-column
          prop="complete"
          header-align="center"
          align="center"
          label="创建状态">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.complete == false" size="small" type="danger">进行中</el-tag>
          <el-tag v-else size="small">完成</el-tag>
        </template>
      </el-table-column>
      <el-table-column
          prop="syncTaskStatus"
          header-align="center"
          align="center"
          label="同步状态">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.syncTaskStatus === 'task_wait_begin'" size="small" type="danger">未开始</el-tag>
          <el-tag v-if="scope.row.syncTaskStatus === 'task_doing'" size="small" type="warning">进行中</el-tag>
          <el-tag v-if="scope.row.syncTaskStatus === 'task_ending'" size="small" type="success">已完成</el-tag>
        </template>
      </el-table-column>
      <el-table-column
          prop="estimateServiceLong"
          header-align="center"
          align="center"
          label="预计时长">
      </el-table-column>
      <el-table-column
          fixed="right"
          header-align="center"
          align="center"
          width="150"
          label="操作">
        <template slot-scope="scope">
          <el-button type="text" v-if="scope.row.complete == true" size="small"
                     @click="getTaskDetail(scope.row.id)">查看任务详情
          </el-button>
          <el-button type="text" v-if="scope.row.complete == true && scope.row.syncTaskStatus !== 'task_doing'" size="small"
                     @click="synchronousHandle(scope.row.id)">
            同步市平台
          </el-button>
          <el-button type="text" size="small" @click="getActivityDetail(scope.row.id)">活动详情</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
        @size-change="sizeChangeHandle"
        @current-change="currentChangeHandle"
        :current-page="pageIndex"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pageSize"
        :total="totalPage"
        layout="total, sizes, prev, pager, next, jumper">
    </el-pagination>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"></add-or-update>
    <activity-detail v-if="activityDetailVisible" ref="activityDetail"
                     @refreshDataList="getDataList"></activity-detail>
    <task-messing-detail v-if="taskMessingDetailVisible" ref="taskMessingDetail"
                         @refreshDataList="getDataList"></task-messing-detail>

  </div>
</template>

<script>
import AddOrUpdate from './add-or-update'
import ActivityDetail from './list'
import TaskMessingDetail from './task-messing'

export default {
  data() {
    return {
      dataForm: {
        key: ''
      },
      dataList: [],
      pageIndex: 1,
      pageSize: 10,
      totalPage: 0,
      dataListLoading: false,
      dataListSelections: [],
      activityDetailVisible: false,
      taskMessingDetailVisible: false,
      addOrUpdateVisible: false
    }
  },
  components: {
    ActivityDetail,
    TaskMessingDetail,
    AddOrUpdate
  },
  activated() {
    this.queryPage()
  },
  methods: {
    queryPage() {
      this.pageIndex = 1
      this.getDataList()
    },
    // 获取数据列表
    getDataList() {
      this.dataListLoading = true
      this.$http({
        url: this.$http.adornUrl('/admin/parameter/copy/activity/pages'),
        method: 'post',
        data: this.$http.adornData({
          'currentPage': this.pageIndex,
          'pageSize': this.pageSize,
          'key': this.dataForm.key
        })
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.dataList = data.obj.records
          this.totalPage = data.obj.total
        } else {
          this.dataList = []
          this.totalPage = 0
        }
        this.dataListLoading = false
      })
    },
    getActivityDetail(id) {
      this.activityDetailVisible = true
      this.$nextTick(() => {
        this.$refs.activityDetail.init(id)
      })
    },
    getTaskDetail(id) {
      this.taskMessingDetailVisible = true
      this.$nextTick(() => {
        this.$refs.taskMessingDetail.init(id)
      })
    },
    // 每页数
    sizeChangeHandle(val) {
      this.pageSize = val
      this.pageIndex = 1
      this.getDataList()
    },
    // 当前页
    currentChangeHandle(val) {
      this.pageIndex = val
      this.getDataList()
    },
    // 多选
    selectionChangeHandle(val) {
      this.dataListSelections = val
    },
    // 同步
    synchronousHandle(id) {
      let ids = id
      this.$confirm(`确定要进行同步吗，确定进行同步操作?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        closeOnClickModal: false
      }).then(() => {
        this.$http({
          url: this.$http.adornUrl('/admin/parameter/copy/activity/syncActByParameterId'),
          method: 'get',
          params: this.$http.adornParams({
            'parameterId': ids
          })
        }).then(({
                   data
                 }) => {
          if (data && data.code === 0) {
            this.$message({
              message: '操作成功',
              type: 'success',
              duration: 1500,
              onClose: () => {
                this.getDataList()
              }
            })
          } else {
            this.$message.error(data.msg)
          }
        })
      })
    },
    // 新增 / 修改
    addOrUpdateHandle(id) {
      this.addOrUpdateVisible = true
      this.$nextTick(() => {
        this.$refs.addOrUpdate.init(id)
      })
    }
  }
}
</script>
