<template>
  <el-dialog
      :title="'活动招募列表详情'"
      :close-on-click-modal="false"
      width="60%"
      append-to-body
      :visible.sync="visible">
    <el-form :inline="true" :model="dataForm" ref="dataForm" @keyup.enter="getDataList()">
      <el-form-item label="同步状态:" prop="sync" clearable>
        <el-dict :code="'sync_status'" v-model="dataForm.sync"></el-dict>
      </el-form-item>
      <el-form-item>
        <el-button @click="currentChangeHandle(1)">查询</el-button>
        <el-button type="warning" @click="resetForm()">重置</el-button>
      </el-form-item>
    </el-form>
    <el-table
        :data="dataList"
        border
        v-loading="dataListLoading"
        style="width: 100%;">
      <el-table-column
          prop="activityName"
          header-align="center"
          align="center"
          label="活动名称">
      </el-table-column>
      <el-table-column
          prop="volunteerName"
          header-align="center"
          align="center"
          label="人员名称">
      </el-table-column>
      <el-table-column
          prop="volunteerPhone"
          header-align="center"
          align="center"
          label="手机号码">
      </el-table-column>
      <el-table-column
          prop="applyIsSync"
          header-align="center"
          align="center"
          label="报名同步状态">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.applyIsSync === 'sync_failure'" size="small" type="danger">同步失败</el-tag>
          <el-tag v-if="scope.row.applyIsSync === 'sync_wait'" size="small" type="warning">待同步</el-tag>
          <el-tag v-if="scope.row.applyIsSync === 'sync_success'" size="small" type="success">同步成功</el-tag>
        </template>
      </el-table-column>
      <el-table-column
          prop="applySyncRemark"
          header-align="center"
          align="center"
          label="报名同步描述">
      </el-table-column>
      <el-table-column
          prop="serviceLongIsSync"
          header-align="center"
          align="center"
          label="服务时长同步状态">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.serviceLongIsSync === 'sync_failure'" size="small" type="danger">同步失败</el-tag>
          <el-tag v-if="scope.row.serviceLongIsSync === 'sync_wait'" size="small" type="warning">待同步</el-tag>
          <el-tag v-if="scope.row.serviceLongIsSync === 'sync_success'" size="small" type="success">同步成功</el-tag>
        </template>
      </el-table-column>
      <el-table-column
          prop="serviceLongSyncRemark"
          header-align="center"
          align="center"
          label="服务时长描述">
      </el-table-column>
    </el-table>
    <div class="com-pagination">
      <el-pagination
          @size-change="sizeChangeHandle"
          @current-change="currentChangeHandle"
          :current-page="dataForm.currentPage"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="dataForm.pageSize"
          :total="totalPage"
          layout="total, sizes, prev, pager, next, jumper"
      />
    </div>
    <!-- 弹窗, 新增 / 修改 -->
  </el-dialog>
</template>

<script>
export default {
  data() {
    return {
      visible: false,
      dataForm: {
        sync: null,
        activityId: null,
        currentPage: 1,
        pageSize: 10
      },
      dataList: [],
      totalPage: 0,
      dataListLoading: false,
    }
  },
  components: {},
  activated() {
    this.getDataList()
  },
  methods: {
    init(id) {
      this.dataForm.activityId = id || null
      this.visible = true
      this.getDataList()
    },

    // 获取数据列表
    getDataList() {
      this.dataListLoading = true
      this.$http({
        url: this.$http.adornUrl('/admin/parameter/copy/activity/getApplyPages'),
        method: 'post',
        data: this.$http.adornData({
          'currentPage': this.pageIndex,
          'pageSize': this.pageSize,
          'sync': this.dataForm.sync,
          'activityId': this.dataForm.activityId
        })
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.dataList = data.obj.content
          this.totalPage = data.obj.totalElements
        } else {
          this.dataList = []
          this.totalPage = 0
        }
        this.dataListLoading = false
      })
    },
    // 每页数
    sizeChangeHandle(val) {
      this.dataForm.pageSize = val
      this.dataForm.currentPage = 1
      this.getDataList()
    },
    // 当前页
    currentChangeHandle(val) {
      this.dataForm.currentPage = val
      this.getDataList()
    },
    // 重置
    resetForm() {
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        this.getDataList()
      })
    }
  }
}
</script>
