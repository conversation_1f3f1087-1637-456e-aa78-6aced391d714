<template>
    <div class="mod-config">
        <el-form :inline="true" :model="dataForm" ref="dataForm" @keyup.enter="queryPage()">
            <el-form-item label="推荐对象" prop="key">
                <el-input v-model="dataForm.key" placeholder="编号/推荐对象/联系电话/事迹简要/提交人手机号" clearable></el-input>
            </el-form-item>
            <el-form-item :label="'上级组织'" prop="publishOrgCode">
                <el-cascader placeholder="请选择" v-model="dataForm.publishOrgCode" :options="orgList"
                             :show-all-levels="false" clearable
                             :props="{ checkStrictly: true, value: 'code', label: 'name' }"
                             @change="(value) => { handleChange(value, 'org') }"></el-cascader>
            </el-form-item>
            <el-form-item label="状态" prop="status">
                <el-dict :code="'good_person_status'" v-model="dataForm.status"></el-dict>
            </el-form-item>
            <el-form-item label="提交时间" prop="timeRange">
                <el-date-picker v-model="dataForm.timeRange" clearable type="daterange" value-format="yyyy-MM-dd"
                                align="right" start-placeholder="开始时间" end-placeholder="结束时间">
                </el-date-picker>
            </el-form-item>
            <el-form-item>
                <el-button icon="el-icon-search" type="warning" @click="queryPage()">查询</el-button>
                <el-button @click="resetForm()" icon="el-icon-refresh-left">重置</el-button>
            </el-form-item>
        </el-form>
        <div class="f-s-c" style="padding: 15px 0;display: flex;justify-content:flex-end;align-items: center ">
            <div>
                <el-button icon="el-icon-monitor" type="primary" @click="assign(null)" :disabled="dataListSelections.length <= 0"  >批量转发街道核实</el-button>
                <el-button icon="el-icon-download" type="success" v-loading.fullscreen.lock="fullscreenLoading" @click="exportHandle()">导出</el-button>
            </div>
        </div>
        <el-table
                :data="dataList"
                border
                v-loading="dataListLoading"
                @selection-change="selectionChangeHandle"
                style="width: 100%;">
            <el-table-column
                type="selection"
                header-align="center"
                align="center"
                :selectable="selectable"
                width="50">
            </el-table-column>
            <el-table-column
                    prop="code"
                    header-align="center"
                    align="center"
                    width="80"
                    label="编号">
            </el-table-column>
            <el-table-column
                    prop="name"
                    header-align="center"
                    align="center"
                    width="100"
                    label="推荐对象">
                <template slot-scope="scope">
                    <a style="cursor: pointer" @click="detailHandle(scope.row.id, true)">{{ scope.row.name }}</a>
                </template>
            </el-table-column>

            <el-table-column
                    prop="linkPhone"
                    header-align="center"
                    align="center"
                    width="120"
                    label="联系电话">
            </el-table-column>
            <el-table-column
                    prop="publishStreetName"
                    header-align="center"
                    align="center"
                    width="250"
                    label="所属街道">
            </el-table-column>
            <el-table-column
                prop="publishCommunityName"
                header-align="center"
                align="center"
                width="250"
                label="所属社区">
            </el-table-column>
            <el-table-column
                prop="achievement"
                header-align="center"
                align="center"
                width="180"
                :show-overflow-tooltip="true"
                label="事迹简要">
            </el-table-column>
            <el-table-column
                prop="submitPhone"
                header-align="center"
                align="center"
                width="120"
                label="提交人手机号">
            </el-table-column>
            <el-table-column
                prop="createDate"
                header-align="center"
                align="center"
                width="180"
                label="提交时间">
            </el-table-column>
            <el-table-column
                    prop="statusText"
                    header-align="center"
                    align="center"
                    width="80"
                    label="状态">
            </el-table-column>
            <el-table-column
                    fixed="right"
                    header-align="center"
                    align="center"
                    width="250"
                    label="操作">
                <template slot-scope="scope">
                    <el-button  type="text" size="small"
                               @click="addOrUpdateHandle(scope.row.id, false)">
                        编辑
                    </el-button>
                    <el-button type="text" size="small" v-if="scope.row.status==='good_person_status_processing'"
                               @click="assign(scope.row.id)">转发街道核实
                    </el-button>
                    <el-button type="text" size="small" v-if="scope.row.status!=='good_person_status_processing'"
                               @click="reportHandle(scope.row.id)">提交核实信息
                    </el-button>
                    <el-button  type="text" size="small" @click="deleteHandle(scope.row.id)">删除</el-button>
                </template>
            </el-table-column>
        </el-table>
        <div class="com-pagination">
            <el-pagination
                    @size-change="sizeChangeHandle"
                    @current-change="currentChangeHandle"
                    :current-page="pageIndex"
                    :page-sizes="[10, 20, 50, 100]"
                    :page-size="pageSize"
                    :total="totalPage"
                    layout="total, sizes, prev, pager, next, jumper"
            />
            <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"></add-or-update>
            <detail v-if="detailVisible" ref="detail" @refreshDataList="getDataList"></detail>
            <report v-if="reportVisible" ref="report" @refreshDataList="getDataList"></report>
        </div>
    </div>
</template>

<script>
import Report from './report'
import AddOrUpdate from './add-or-update'
import Detail from './detail'
export default {
    data() {
        return {
            dataForm: {
                key: null,
                publishOrgCode: null,
                status: null,
                timeRange: [],
            },
            pageIndex: 1,
            pageSize: 10,
            totalPage: 0,
            dataList: [],
            dataListLoading: false,
            reportVisible: false,
            addOrUpdateVisible: false,
            detailVisible: false,
            dataListSelections: [],
            orgList: [],
            fullscreenLoading: false,
        }
    },
    components: {
        Report,
        AddOrUpdate,
        Detail
    },
    activated() {
        this.queryPage()
        this.getOrg()
    },
    methods: {
        queryPage() {
            this.pageIndex = 1
            this.getDataList()
        },
        //获取所属区域
        getOrg() {
            this.$http({
                url: this.$http.adornUrl(`/admin/org/getOrgTreeOnlyContainFiveSubAssociation`),
                method: 'get',
                params: this.$http.adornParams()
            }).then(({data}) => {
                if (data && data.code === 0) {
                    this.orgList = this.getTreeData(data.obj)
                } else {
                    this.orgList = []
                }
            })
        },
        // *处理点位分类最后children数组为空的状态
        getTreeData: function (data) {
            var that = this
            // 循环遍历json数据
            data.forEach(function (e) {
                if (!e.children || e.children.length < 1) {
                    e.children = undefined
                } else {
                    // children若不为空数组，则继续 递归调用 本方法
                    that.getTreeData(e.children)
                }
            })
            return data
        },
        // 获取数据列表
        getDataList() {
            this.dataListLoading = true
            this.$http({
                url: this.$http.adornUrl('/admin/yq/good/person/pagesForGoodPerson'),
                method: 'post',
                data: this.$http.adornData({
                    'currentPage': this.pageIndex,
                    'pageSize': this.pageSize,
                    'key': this.dataForm.key,
                    'publishOrgCode': this.dataForm.publishOrgCode,
                    'status': this.dataForm.status,
                    'startTime': this.dataForm.timeRange && this.dataForm.timeRange.length === 2 ? this.dataForm.timeRange[0] + ' 00:00:00' : null,
                    'endTime': this.dataForm.timeRange && this.dataForm.timeRange.length === 2 ? this.dataForm.timeRange[1] + ' 23:59:59' : null
                })
            }).then(({data}) => {
                if (data && data.code === 0) {
                    this.dataList = data.obj.records
                    this.totalPage = data.obj.total
                } else {
                    this.dataList = []
                    this.totalPage = 0
                }
                this.dataListLoading = false
            })
        },
        // 每页数
        sizeChangeHandle(val) {
            this.pageSize = val
            this.pageIndex = 1
            this.getDataList()
        },
        // 当前页
        currentChangeHandle(val) {
            this.pageIndex = val
            this.getDataList()
        },
        // 多选
        selectionChangeHandle(val) {
            this.dataListSelections = val
        },
        // 重置
        resetForm() {
            this.$refs['dataForm']?.resetFields()
            this.getDataList()
        },
        // 删除
        deleteHandle(id) {
            var ids = id ? [id] : this.dataListSelections.map(item => {
                return item.id
            })
            this.$confirm(`确定进行[${id ? '删除' : '批量删除'}]操作?`, '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning',
                closeOnClickModal: false
            }).then(() => {
                this.$http({
                    url: this.$http.adornUrl('/admin/yq/good/person/removeByIds'),
                    method: 'get',
                    params: this.$http.adornParams({
                        'ids': ids.join(',')
                    })
                }).then(({data}) => {
                    if (data && data.code === 0) {
                        this.$message({
                            message: '操作成功',
                            type: 'success',
                            duration: 1500,
                            onClose: () => {
                                this.getDataList()
                            }
                        })
                    } else {
                        this.$message.error(data.msg)
                    }
                })
            })
        },
        // 提交
        assign(id) {
            var ids = id ? [id] : this.dataListSelections.map(item => {
                return item.id
            })
            this.$confirm(`确定[${id ? '转发街道核实' : '批量转发街道核实'}]操作?`, '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning',
                closeOnClickModal: false
            }).then(() => {
                this.$http({
                    url: this.$http.adornUrl('/admin/yq/good/person/assignByIds'),
                    method: 'get',
                    params: this.$http.adornParams({
                        'ids': ids.join(',')
                    })
                }).then(({data}) => {
                    if (data && data.code === 0) {
                        this.$message({
                            message: '操作成功',
                            type: 'success',
                            duration: 1500,
                            onClose: () => {
                                this.getDataList()
                            }
                        })
                    } else {
                        this.$message.error(data.msg)
                    }
                })
            })
        },
        //资料/报告上传
        reportHandle(id) {
            this.reportVisible = true
            this.$nextTick(() => {
                this.$refs.report.init(id)
            })
        },
        addOrUpdateHandle(id, isRead) {
            this.addOrUpdateVisible = true
            this.$nextTick(() => {
                this.$refs.addOrUpdate.init(id,isRead)
            })
        },
        detailHandle(id, isRead) {
            this.detailVisible = true
            this.$nextTick(() => {
                this.$refs.detail.init(id,isRead)
            })
        },
        handleChange(value, type) {
            if (type === 'field') {
                this.dataForm.fieldId = value[value.length - 1]
            }
            if (type === 'org') {
                this.dataForm.publishOrgCode = value && value.length > 0 ? value[value.length - 1] : null
            }
        },
        // 设置列表禁用
        selectable (row, index) {
            if (row.status ==='good_person_status_processing') {
                return true
            } else {
                return false
            }
        },
        // 导出
        exportHandle() {
            this.fullscreenLoading = true;
            this.$http({
                url: this.$http.adornUrl('/admin/yq/good/person/export'),
                method: 'post',
                responseType: 'arraybuffer',
                data: this.$http.adornData({
                    'currentPage': this.pageIndex,
                    'pageSize': this.pageSize,
                    'key': this.dataForm.key,
                    'publishOrgCode': this.dataForm.publishOrgCode,
                    'status': this.dataForm.status,
                    'startTime': this.dataForm.timeRange && this.dataForm.timeRange.length === 2 ? this.dataForm.timeRange[0] + ' 00:00:00' : null,
                    'endTime': this.dataForm.timeRange && this.dataForm.timeRange.length === 2 ? this.dataForm.timeRange[1] + ' 23:59:59' : null
                })
            }).then(({data}) => {
                if (data.code && data.code !== 0) {
                    this.$message.error('导出失败')
                } else {
                    this.fullscreenLoading = false
                    let blob = new Blob([data], {
                        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8'
                    })
                    let objectUrl = URL.createObjectURL(blob)
                    let a = document.createElement('a')
                    // window.location.href = objectUrl
                    a.href = objectUrl
                    a.download = '园区好人.xlsx'
                    a.click()
                    URL.revokeObjectURL(objectUrl)
                    this.$message({
                        type: 'success',
                        message: '导出成功'
                    })
                }
            })
        },
    }
}
</script>
