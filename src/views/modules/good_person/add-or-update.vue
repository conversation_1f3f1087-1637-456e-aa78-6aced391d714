<template>
    <el-dialog :title="!isRead ? '编辑' : '详情'" :close-on-click-modal="false" :visible.sync="visible"
               width="60%" append-to-body>

        <el-form :model="dataForm" :rules="dataRule" ref="dataForm" label-width="150px">
            <el-row :gutter="20">
                <el-col :span="12">
                    <el-form-item label="编号" prop="code">
                        <el-input v-model="dataForm.code" placeholder="编号" readonly></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="推荐对象姓名" prop="name">
                        <el-input v-model="dataForm.name" placeholder="推荐对象姓名"></el-input>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row :gutter="20">
                <el-col :span="12">
                    <el-form-item label="联系电话" prop="linkPhone">
                        <el-input v-model="dataForm.linkPhone" placeholder="联系电话"></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="提交人手机号" prop="submitPhone">
                        <el-input v-model="dataForm.submitPhone" placeholder="提交人手机号" readonly></el-input>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row :gutter="20">

                <el-col :span="24">
                    <el-form-item label="所属街道/社区" prop="publishOrgCode">
                        <el-cascader placeholder="请选择" v-model="dataForm.publishOrgCode" :options="orgList"
                                     :show-all-levels="true" clearable
                                     :props="{ checkStrictly: true, value: 'code', label: 'name' }"
                                     @change="(value) => { handleChange(value, 'org') }" style="width: 100%"></el-cascader>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row :gutter="20">
                <el-col :span="24">
                    <el-form-item label="事迹简要" prop="achievement">
                        <el-input type="textarea" :rows="3" maxlength="1000" show-word-limit
                                  v-model="dataForm.achievement" placeholder="事迹简要"></el-input>
                    </el-form-item>
                </el-col>

            </el-row>
            <el-row :gutter="20">
                <el-col :span="12">
                    <el-form-item label="状态" prop="status">
                        <el-dict :code="'good_person_status'" v-model="dataForm.status" disabled="disabled"></el-dict>
                    </el-form-item>
                </el-col>

            </el-row>
            <el-row :gutter="20">
                <el-col :span="24">
                    <el-form-item label="图片" prop="picUrl">
                        <el-upload
                                list-type="picture-card"
                                :action="this.$http.adornUrl(`/admin/oss/upload`)"
                                :headers="myHeaders"
                                :data="{serverCode: this.serverCode,media:false}"
                                :show-file-list="true"
                                :multiple="true"
                                :file-list="fileList"
                                :on-preview="handlePictureCardPreview"
                                :on-remove="handleRemovePicture"
                                :disabled="isRead"
                                :on-success="function (res,file){return handleAvatarSuccess(res,file, 'pictureList')}"
                                :before-upload="beforeAvatarUpload"
                                :class=iconClasses()>
                            <i class="el-icon-plus"></i>
                            <div slot="tip" class="el-upload__tip" style="color: red">文件格式jpg、bmp或png，大小不超10M
                            </div>
                        </el-upload>
                        <el-dialog :visible.sync="dialogVisible" append-to-body>
                            <img width="100%" :src="dialogImageUrl" alt="">
                        </el-dialog>
                    </el-form-item>
                </el-col>
            </el-row>

        </el-form>
        <template #footer>
				<span class="dialog-footer">
				 <el-button @click="visible = false">取消</el-button>
         <el-button type="primary" v-if="!isRead" @click="dataFormSubmit()">确定</el-button>
				</span>
        </template>
    </el-dialog>
</template>

<script>
import Vue from 'vue';
import {is8lPhone, isMobile} from '@/utils/validate';
export default {
    data() {
        const validateLinkPhone = (rule, value, callback) => {
            if (!isMobile(value) && !is8lPhone(value)) {callback(new Error('联系电话须为手机号或区号+8位座机号例如051288888888或0512-88888888'))}
            else {callback()}
        }
        return {
            visible: false,
            isRead:false,
            posed: false,
            pictureList: [],
            fileList: [],
            dialogImageUrl: '',
            dialogVisible:false,
            serverCode: 'LocalServer',
            myHeaders: {Authorization: Vue.cookie.get('Authorization')},
            dataForm: {
                id: null,
                version: null,
                code: null,
                name: null,
                linkPhone: null,
                publishOrgCode: null,
                achievement: null,
                status: null,
                attachmentList: null,
                submitPhone: null
            },
            orgList: [],
            dataRule: {
                code: [
                    {required: true, message: '编号不能为空', trigger: 'blur'}
                ],
                name: [
                    {required: true, message: '推荐对象姓名不能为空', trigger: 'blur'}
                ],
                linkPhone: [
                    {required: true, message: '联系电话不能为空', trigger: 'blur'},
                    {validator: validateLinkPhone, trigger: 'change'}
                ],
                publishOrgCode: [
                    {required: true, message: '所属街道/社区不能为空', trigger: 'blur'}
                ],
                achievement: [
                    {required: true, message: '事迹简要不能为空', trigger: 'blur'}
                ],
                status: [
                    {required: true, message: '状态(待处理/已分派/已核实)不能为空', trigger: 'blur'}
                ],
                // picUrl: [
                //     {required: true, message: '图片不能为空', trigger: 'blur'}
                // ],
                submitPhone: [
                    {required: true, message: '提交人手机号不能为空', trigger: 'blur'}
                ]
            }
        }
    },
    components: {},
    methods: {
        init(id,isRead) {
            this.dataForm.id = id || null
            this.visible = true
            this.isRead= isRead
            this.dataForm.attachmentList = []
            this.getOrg()
            this.pictureList = []
            this.fileList = []
            this.$nextTick(() => {
                this.$refs['dataForm']?.resetFields()
                if (this.dataForm.id) {
                    this.$http({
                        url: this.$http.adornUrl(`/admin/yq/good/person`),
                        method: 'get',
                        params: this.$http.adornParams({id: this.dataForm.id})
                    }).then(({data}) => {
                        if (data && data.code === 0) {
                            this.dataForm = data.obj
                            if(this.dataForm.picUrl){
                                let picture = this.dataForm.picUrl.split(',')
                                this.pictureList = picture
                                picture.forEach(item => {
                                    let obj = {
                                        url: this.$http.adornAttachmentUrl(item)
                                    }
                                    this.fileList.push(obj)
                                })
                            }

                        }
                    })
                }
            })
        },
        // 表单提交
        dataFormSubmit() {
            console.log(this.dataForm)
            this.dataForm.picUrl = this.pictureList.join(',') || null
            this.$refs['dataForm'].validate((valid) => {
                if (valid) {
                    this.$http({
                        url: this.$http.adornUrl(`/admin/yq/good/person/${!this.dataForm.id ? 'save' : 'update'}`),
                        method: 'post',
                        data: this.$http.adornData(this.dataForm)
                    }).then(({data}) => {
                        if (data && data.code === 0) {
                            this.$message({
                                message: '操作成功',
                                type: 'success',
                                duration: 1500,
                                onClose: () => {
                                    this.visible = false
                                    this.$emit('refreshDataList')
                                }
                            })
                        } else if (data && data.code === 303) {
                            for (let it of data.obj) {
                                this[`${it.field}Error`] = it.message
                            }
                        } else {
                            this.$message.error(data.msg)
                        }
                    })
                }
            })
        },
        handlePictureCardPreview(file) {
            this.dialogImageUrl = file.url
            this.dialogVisible = true
        },
        handleAvatarSuccess(res, file, field) {
            if (res.success) {
                this.$message({
                    message: '操作成功',
                    type: 'success',
                    duration: 1500
                })
                this.pictureList.push(res.obj.path)
            } else {
                this.$message.error('上传失败')
            }
        },
        handleRemovePicture(file, fileList) {
            this.pictureList = []
            fileList.forEach(item => {
                console.log(item)
                try {
                    this.pictureList.push(item.url.slice(item.url.indexOf('vpath') - 1))
                } catch (e) {
                    this.pictureList.push(item.url)
                }
            })
        },
        beforeAvatarUpload: function (file) {
            var isJPG = file.type === 'image/jpeg'
            var isPNG = file.type === 'image/png'
            var isBMP = file.type === 'image/bmp'
            var isLt20M = file.size / 1024 / 1024 < 20

            if (!isJPG && !isPNG && !isBMP) {
                this.$message.error('上传图片只能是图片!')
            }
            if (!isLt20M) {
                this.$message.error('上传文件大小不能超过 20MB!')
            }
            return (isJPG || isPNG || isBMP) && isLt20M
        },

        //获取所属区域
        getOrg() {
            this.$http({
                url: this.$http.adornUrl(`/admin/org/getOrgTreeOnlyContainFiveSubAssociation`),
                method: 'get',
                params: this.$http.adornParams()
            }).then(({data}) => {
                if (data && data.code === 0) {
                    this.orgList = this.getTreeData(data.obj)
                } else {
                    this.orgList = []
                }
            })
        },
        // *处理点位分类最后children数组为空的状态
        getTreeData: function (data) {
            var that = this
            // 循环遍历json数据
            data.forEach(function (e) {
                if (!e.children || e.children.length < 1) {
                    e.children = undefined
                } else {
                    // children若不为空数组，则继续 递归调用 本方法
                    that.getTreeData(e.children)
                }
            })
            return data
        },
        handleChange(value, type) {
            if (type === 'field') {
                this.dataForm.fieldId = value[value.length - 1]
            }
            if (type === 'org') {
                this.dataForm.publishOrgCode = value && value.length > 0 ? value[value.length - 1] : null
            }
        },
        iconClasses () {
            return [ this.isRead ? 'hide' : '' ]
        },
    }
}
</script>
<style lang="scss">
.hide .el-upload--picture-card {
    display: none !important;
}
</style>
