<template>
    <el-dialog title='详情' :close-on-click-modal="false" :visible.sync="visible"
               width="60%" append-to-body>

        <el-form :model="dataForm" :rules="dataRule" ref="dataForm" label-width="150px">
            <el-row :gutter="20">
                <el-col :span="12">
                    <el-form-item label="编号" prop="code">
                        <el-input v-model="dataForm.code" placeholder="编号" readonly></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="推荐对象姓名" prop="name">
                        <el-input v-model="dataForm.name" placeholder="推荐对象姓名" readonly></el-input>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row :gutter="20">
                <el-col :span="12">
                    <el-form-item label="联系电话" prop="linkPhone">
                        <el-input v-model="dataForm.linkPhone" placeholder="联系电话" readonly></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="提交人手机号" prop="submitPhone">
                        <el-input v-model="dataForm.submitPhone" placeholder="提交人手机号" readonly></el-input>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row :gutter="20">

                <el-col :span="24">
                    <el-form-item label="所属街道/社区" prop="publishOrgCode">
                        <el-cascader placeholder="请选择" v-model="dataForm.publishOrgCode" :options="orgList"
                                     :show-all-levels="true" clearable
                                     :props="{ checkStrictly: true, value: 'code', label: 'name' }"
                                     @change="(value) => { handleChange(value, 'org') }"
                                     style="width: 100%"
                                     disabled="disabled"></el-cascader>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row :gutter="20">
                <el-col :span="24">
                    <el-form-item label="事迹简要" prop="achievement">
                        <el-input type="textarea" :rows="3" maxlength="1000" show-word-limit
                                  v-model="dataForm.achievement" placeholder="事迹简要" readonly></el-input>
                    </el-form-item>
                </el-col>

            </el-row>
            <el-row :gutter="20">
                <el-col :span="12">
                    <el-form-item label="状态" prop="status">
                        <el-dict :code="'good_person_status'" v-model="dataForm.status" disabled="disabled"></el-dict>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="排序" prop="sequence">
                        <el-input-number v-model="dataForm.sequence" placeholder="排序" disabled="disabled"/>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row :gutter="20">
                <el-col :span="12">
                    <el-form-item label="年份" prop="year">
                        <el-date-picker
                                v-model="dataForm.year"
                                type="year"
                                value-format="yyyy"
                                disabled="disabled"
                                placeholder="选择年">
                        </el-date-picker>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="前端是否展示" prop="display">
                        <el-radio-group v-model="dataForm.display" disabled>
                            <el-radio :label=true>是</el-radio>
                            <el-radio :label=false>否</el-radio>
                        </el-radio-group>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row :gutter="20">
                <el-col :span="24">
                    <el-form-item label="图片" prop="display">
                    <el-image
                        v-for="(item, index) in fileList"
                            style="width: 100px; height: 100px"
                            :src="item"
                        :key="index"
                            :preview-src-list="fileList">
                    </el-image>
                    </el-form-item>
                </el-col>
            </el-row>

        </el-form>
        <template #footer>
				<span class="dialog-footer">
				 <el-button @click="visible = false">取消</el-button>
				</span>
        </template>
    </el-dialog>
</template>

<script>
import Vue from 'vue';
import {is8lPhone, isMobile} from '@/utils/validate';
import moment from 'moment'
export default {
    data() {
        const validateLinkPhone = (rule, value, callback) => {
            if (!isMobile(value) && !is8lPhone(value)) {
                callback(new Error('联系电话须为手机号或区号+8位座机号例如051288888888或0512-88888888'))
            } else {
                callback()
            }
        }
        return {
            visible: false,
            posed: false,
            pictureList: [],
            fileList: [],
            currentImageIndex: -1, // 当前图片索引
            dialogImageUrl: '',
            dialogVisible: false,
            serverCode: 'LocalServer',
            myHeaders: {Authorization: Vue.cookie.get('Authorization')},
            dataForm: {
                id: null,
                version: null,
                code: null,
                name: null,
                linkPhone: null,
                publishOrgCode: null,
                achievement: null,
                status: null,
                attachmentList: null,
                submitPhone: null,
                sequence: 0,
                year: new Date().getFullYear().toString(),
                display: true
            },
            orgList: [],
            dataRule: {
                code: [
                    {required: true, message: '编号不能为空', trigger: 'blur'}
                ],
                name: [
                    {required: true, message: '推荐对象姓名不能为空', trigger: 'blur'}
                ],
                linkPhone: [
                    {required: true, message: '联系电话不能为空', trigger: 'blur'},
                    {validator: validateLinkPhone, trigger: 'change'}
                ],
                publishOrgCode: [
                    {required: true, message: '所属街道/社区不能为空', trigger: 'blur'}
                ],
                achievement: [
                    {required: true, message: '事迹简要不能为空', trigger: 'blur'}
                ],
                status: [
                    {required: true, message: '状态(待处理/已分派/已核实)不能为空', trigger: 'blur'}
                ],
                // picUrl: [
                //     {required: true, message: '图片不能为空', trigger: 'blur'}
                // ],
                submitPhone: [
                    {required: true, message: '提交人手机号不能为空', trigger: 'blur'}
                ]
            }
        }
    },
    components: {},
    methods: {
        init(id ) {
            this.dataForm.id = id || null
            this.visible = true
            this.dataForm.attachmentList = []
            this.getOrg()
            this.pictureList = []
            this.fileList = []
            this.$nextTick(() => {
                this.$refs['dataForm']?.resetFields()
                if (this.dataForm.id) {
                    this.$http({
                        url: this.$http.adornUrl(`/admin/yq/good/person`),
                        method: 'get',
                        params: this.$http.adornParams({id: this.dataForm.id})
                    }).then(({data}) => {
                        if (data && data.code === 0) {
                            this.dataForm = data.obj
                            if (this.dataForm.picUrl) {
                                let picture = this.dataForm.picUrl.split(',')
                                this.pictureList = picture
                                picture.forEach(item => {
                                    this.fileList.push(this.$http.adornAttachmentUrl(item))
                                })
                            }

                        }
                    })
                }
            })
        },
        //获取所属区域
        getOrg() {
            this.$http({
                url: this.$http.adornUrl(`/admin/org/getOrgTreeOnlyContainFiveSubAssociation`),
                method: 'get',
                params: this.$http.adornParams()
            }).then(({data}) => {
                if (data && data.code === 0) {
                    this.orgList = this.getTreeData(data.obj)
                } else {
                    this.orgList = []
                }
            })
        },
        // *处理点位分类最后children数组为空的状态
        getTreeData: function (data) {
            var that = this
            // 循环遍历json数据
            data.forEach(function (e) {
                if (!e.children || e.children.length < 1) {
                    e.children = undefined
                } else {
                    // children若不为空数组，则继续 递归调用 本方法
                    that.getTreeData(e.children)
                }
            })
            return data
        },
    }
}
</script>
