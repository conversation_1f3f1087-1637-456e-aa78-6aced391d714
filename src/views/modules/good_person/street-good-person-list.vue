<template>
    <div class="mod-config">
        <el-form :inline="true" :model="dataForm" ref="dataForm" @keyup.enter="queryPage()">
            <el-form-item label="查询条件" prop="key">
                <el-input v-model="dataForm.key" placeholder="编号/推荐对象/联系电话/事迹简要/提交人手机号" clearable></el-input>
            </el-form-item>
            <el-form-item label="提交时间" prop="timeRange">
                <el-date-picker v-model="dataForm.timeRange" clearable type="daterange" value-format="yyyy-MM-dd"
                                align="right" start-placeholder="开始时间" end-placeholder="结束时间">
                </el-date-picker>
            </el-form-item>
            <el-form-item>
                <el-button icon="el-icon-search" type="warning" @click="queryPage()">查询</el-button>
                <el-button @click="resetForm()" icon="el-icon-refresh-left">重置</el-button>
            </el-form-item>
        </el-form>
        <div class="f-s-c" style="padding: 15px 0;display: flex;justify-content:flex-end;align-items: center ">
            <div>
            </div>
        </div>
        <el-table
                :data="dataList"
                border
                v-loading="dataListLoading"
                style="width: 100%;">
            <el-table-column
                    prop="code"
                    header-align="center"
                    align="center"
                    width="80"
                    label="编号">
            </el-table-column>
            <el-table-column
                    prop="name"
                    header-align="center"
                    align="center"
                    width="100"
                    label="推荐对象">
                <template slot-scope="scope">
                    <a style="cursor: pointer" @click="detailHandle(scope.row.id, true)">{{ scope.row.name }}</a>
                </template>
            </el-table-column>

            <el-table-column
                    prop="linkPhone"
                    header-align="center"
                    align="center"
                    width="120"
                    label="联系电话">
            </el-table-column>
            <el-table-column
                prop="publishStreetName"
                header-align="center"
                align="center"
                width="250"
                label="所属街道">
            </el-table-column>
            <el-table-column
                prop="publishCommunityName"
                header-align="center"
                align="center"
                width="250"
                label="所属社区">
            </el-table-column>
            <el-table-column
                prop="achievement"
                header-align="center"
                align="center"
                width="180"
                :show-overflow-tooltip="true"
                label="事迹简要">
            </el-table-column>
            <el-table-column
                    prop="submitPhone"
                    header-align="center"
                    align="center"
                    width="120"
                    label="提交人手机号">
            </el-table-column>
            <el-table-column
                prop="createDate"
                header-align="center"
                align="center"
                width="180"
                label="提交时间">
            </el-table-column>
            <el-table-column
                    prop="statusText"
                    header-align="center"
                    align="center"
                    width="80"
                    label="状态">
            </el-table-column>
            <el-table-column
                    fixed="right"
                    header-align="center"
                    align="center"
                    label="操作">
                <template slot-scope="scope">
                    <el-button type="text" size="small" @click="reportHandle(scope.row.id)">提交核实信息
                    </el-button>
                </template>
            </el-table-column>
        </el-table>
        <div class="com-pagination">
            <el-pagination
                    @size-change="sizeChangeHandle"
                    @current-change="currentChangeHandle"
                    :current-page="pageIndex"
                    :page-sizes="[10, 20, 50, 100]"
                    :page-size="pageSize"
                    :total="totalPage"
                    layout="total, sizes, prev, pager, next, jumper"
            />
            <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"></add-or-update>
            <detail v-if="detailVisible" ref="detail" @refreshDataList="getDataList"></detail>
            <report v-if="reportVisible" ref="report" @refreshDataList="getDataList"></report>
        </div>
    </div>
</template>

<script>
import Report from './report'
import AddOrUpdate from './add-or-update'
import Detail from './detail'
export default {
    data() {
        return {
            dataForm: {
                key: null,
                publishOrgCode: null,
                status: null,
                timeRange: [],
            },
            pageIndex: 1,
            pageSize: 10,
            totalPage: 0,
            dataList: [],
            dataListLoading: false,
            reportVisible: false,
            addOrUpdateVisible: false,
            detailVisible: false,
            dataListSelections: [],
            orgList: [],
        }
    },
    components: {
        Report,
        AddOrUpdate,
        Detail
    },
    activated() {
        this.queryPage()
    },
    methods: {
        queryPage() {
            this.pageIndex = 1
            this.getDataList()
        },
        // 获取数据列表
        getDataList() {
            this.dataListLoading = true
            this.$http({
                url: this.$http.adornUrl('/admin/yq/good/person/pagesForGoodPerson'),
                method: 'post',
                data: this.$http.adornData({
                    'currentPage': this.pageIndex,
                    'pageSize': this.pageSize,
                    'key': this.dataForm.key,
                    'status': this.dataForm.status,
                    'startTime': this.dataForm.timeRange && this.dataForm.timeRange.length === 2 ? this.dataForm.timeRange[0] + ' 00:00:00' : null,
                    'endTime': this.dataForm.timeRange && this.dataForm.timeRange.length === 2 ? this.dataForm.timeRange[1] + ' 23:59:59' : null
                })
            }).then(({data}) => {
                if (data && data.code === 0) {
                    this.dataList = data.obj.records
                    this.totalPage = data.obj.total
                } else {
                    this.dataList = []
                    this.totalPage = 0
                }
                this.dataListLoading = false
            })
        },
        // 每页数
        sizeChangeHandle(val) {
            this.pageSize = val
            this.pageIndex = 1
            this.getDataList()
        },
        // 当前页
        currentChangeHandle(val) {
            this.pageIndex = val
            this.getDataList()
        },
        // 多选
        selectionChangeHandle(val) {
            this.dataListSelections = val
        },
        // 重置
        resetForm() {
            this.$refs['dataForm']?.resetFields()
            this.getDataList()
        },
        //资料/报告上传
        reportHandle(id) {
            this.reportVisible = true
            this.$nextTick(() => {
                this.$refs.report.init(id)
            })
        },
        addOrUpdateHandle(id, isRead) {
            this.addOrUpdateVisible = true
            this.$nextTick(() => {
                this.$refs.addOrUpdate.init(id,isRead)
            })
        },
        handleChange(value, type) {
            if (type === 'field') {
                this.dataForm.fieldId = value[value.length - 1]
            }
            if (type === 'org') {
                this.dataForm.publishOrgCode = value && value.length > 0 ? value[value.length - 1] : null
            }
        },
        detailHandle(id, isRead) {
            this.detailVisible = true
            this.$nextTick(() => {
                this.$refs.detail.init(id,isRead)
            })
        },
    }
}
</script>
