<template>
  <div class="mod-activation-form">
<!--    <span style="color: red">*筛选条件待定</span>-->
<!--    <el-form :inline="true" ref="dataForm" :model="dataForm" @keyup.enter.native="getDataList()" >-->
<!--      <el-form-item label="年度" prop="year">-->
<!--        <el-input v-model="dataForm.year" placeholder="请输入" clearable></el-input>-->
<!--      </el-form-item>-->
<!--      <el-form-item label="开始时间" prop="startTime">-->
<!--        <el-input v-model="dataForm.startTime" placeholder="请输入" clearable></el-input>-->
<!--      </el-form-item>-->
<!--      <el-form-item label="结束时间" prop="endTime">-->
<!--        <el-input v-model="dataForm.endTime" placeholder="请输入" clearable></el-input>-->
<!--      </el-form-item>-->
<!--      <el-form-item>-->
<!--        <el-button type="warning" icon="el-icon-search" @click="getDataList()">查询</el-button>-->
<!--        <el-button icon="el-icon-refresh-left" @click="resetForm()">重置</el-button>-->
<!--      </el-form-item>-->
<!--    </el-form>-->
    <div class="f-s-c" style="padding: 15px 0;display: flex; justify-content: flex-end">
      <div>
        <el-button :disabled="level <= 1" icon="el-icon-back" type="primary" @click="digReverse()">返回上一级</el-button>
        <el-button icon="el-icon-download" type="success" v-loading.fullscreen.lock="fullscreenLoading" @click="exportHandle(true)">导出当前层级</el-button>
        <el-button icon="el-icon-download" type="success" v-loading.fullscreen.lock="fullscreenLoading" @click="exportHandle(false)">导出所有层级</el-button>
        <el-button type="warning" icon="el-icon-refresh" @click="getDataList()">刷新</el-button>
      </div>
    </div>
    <el-table
        :data="dataList"
        border
        v-loading="dataListLoading"
        style="width: 100%;">
      <el-table-column
          prop="orgName"
          header-align="center"
          align="center"
          label="组织名称">
        <template slot-scope="scope">
          <a style="cursor: pointer" v-if="scope.row.children && scope.row.children.length > 0" @click="dig(scope.row.children, scope.row.orgCode)">{{ scope.row.orgName }}</a>
          <span v-else>{{ scope.row.orgName }}</span>
        </template>
      </el-table-column>
<!--      <el-table-column-->
<!--          prop="population"-->
<!--          header-align="center"-->
<!--          align="center"-->
<!--          label="常住人口">-->
<!--      </el-table-column>-->
      <el-table-column
          prop="volunteer"
          header-align="center"
          align="center"
          label="志愿人数">
      </el-table-column>
      <el-table-column
          prop="activePopulation"
          header-align="center"
          align="center"
          label="活跃人数">
      </el-table-column>
      <el-table-column
          prop="activation"
          header-align="center"
          align="center"
          label="活跃度">
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
import _ from "lodash";

export default {
  name: "activation",
  data () {
    return {
      dataForm: {
        year: null,
        startTime: null,
        endTime: null
      },
      dataList: [],
      level: 1,
      parentCode: null,
      originDataList: [],
      dataListLoading: false,
      fullscreenLoading: false
    }
  },
  mounted() {
    this.getDataList()
  },
  methods: {
    resetForm() {
      this.$refs['dataForm']?.resetFields()
      this.getDataList()
    },
    getDataList() {
      this.dataListLoading = true
      this.$http({
        url: this.$http.adornUrl('/admin/report_form/getActivationForm'),
        method: 'post',
        data: this.$http.adornData({
          year: this.dataForm.year,
          startTime: this.dataForm.startTime,
          endTime: this.dataForm.endTime,
          containSub: true,
          parentCode: 'top_dept',
          containSelf: true
        })
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.originDataList = data.obj
          this.level = 1
          this.parentCode = null
          this.dataList = data.obj
        } else {
          this.originDataList = []
          this.level = 1
          this.parentCode = null
          this.dataList = []
        }
        this.dataListLoading = false
      })
    },
    dig(children, pCode) {
      this.dataListLoading = true
      this.dataList = children
      this.parentCode = pCode
      this.level += 1
      this.dataListLoading = false
    },
    digReverse() {
      if (!this.parentCode) {
        return
      }
      this.digReverseHandle(null, this.originDataList, false)
    },
    digReverseHandle(pCode, dataList, flag) {
      let loopDataList = dataList
      let orgCodes = _.map(loopDataList, 'orgCode')
      for(let i = 0; i < loopDataList.length; i++) {
        if (orgCodes.indexOf(this.parentCode) >= 0) {
          this.dataList = loopDataList
          this.parentCode = pCode
          this.level -= 1
          flag = true
          break
        }
        flag = this.digReverseHandle(loopDataList[i].orgCode, loopDataList[i].children)
        if (flag) {
          break
        }
      }
      return flag
    },
    exportHandle(currentLevel) {
      this.fullscreenLoading = true;
      this.$http({
        url: this.$http.adornUrl('/admin/report_form/exportActivationForm'),
        method: 'post',
        responseType: 'arraybuffer',
        data: this.$http.adornData({
          'year': this.dataForm.year,
          'startTime': this.dataForm.startTime,
          'endTime': this.dataForm.endTime,
          'containSub': !currentLevel,
          'parentCode': currentLevel ? this.parentCode || 'top_dept' : 'top_dept',
          'containSelf': currentLevel ? !this.parentCode : true
        })
      }).then(({data}) => {
        if (data.code && data.code !== 0) {
          this.$message.error('导出失败')
        } else {
          this.fullscreenLoading = false;
          let blob = new Blob([data], {type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8'})
          let objectUrl = URL.createObjectURL(blob)
          let a = document.createElement('a')
          // window.location.href = objectUrl
          a.href = objectUrl
          a.download = '活跃度报表.xlsx'
          a.click()
          URL.revokeObjectURL(objectUrl)
          this.$message({
            type: 'success',
            message: '导出成功'
          })
        }
      })
    }
  }
}
</script>

<style scoped>

</style>
