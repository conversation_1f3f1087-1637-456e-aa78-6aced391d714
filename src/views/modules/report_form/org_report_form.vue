<template>
  <div>
    <el-form :inline="true" :model="dataForm" ref="dataForm">
      <el-form-item :label="'上级组织'" prop="publishOrgCodes" v-if="currentUserManagerCapacity === 'ASSOCIATION_ADMIN' || currentUserManagerCapacity === 'SUB_ASSOCIATION_ADMIN'">
        <el-cascader placeholder="请选择" v-model="dataForm.publishOrgCodes" :options="orgList" ref="orgTree" filterable
                     :show-all-levels="false" clearable :props="{ checkStrictly: true, value: 'code', label: 'name' }"
                     @change="(value) => { handleChange(value) }" style="width: 300px"/>
      </el-form-item>
      <el-card class="box-card" v-if="currentUserManagerCapacity === 'ASSOCIATION_ADMIN' || currentUserManagerCapacity === 'SUB_ASSOCIATION_ADMIN' || currentUserManagerCapacity === 'COMMUNITY_ADMIN'">
        <div slot="header" class="head">
          <div>
            <el-form-item>
              <span slot="label" class="title">组 织 数 据</span>
            </el-form-item>
            <el-form-item label="时间段" prop="orgDataTimeRange">
              <el-date-picker v-model="dataForm.orgDataTimeRange" clearable type="daterange"
                              value-format="yyyy-MM-dd" align="right" start-placeholder="开始" end-placeholder="结束"/>
            </el-form-item>
            <el-form-item>
              <el-button icon="el-icon-search" type="warning" @click="currentChangeHandle(1, 'orgData')">查询</el-button>
              <el-button @click="resetOrgDataForm()" icon="el-icon-refresh-left">重置</el-button>
              <el-button icon="el-icon-download" type="success" v-loading.fullscreen.lock="fullscreenLoading" @click="exportOrgDataHandle()">导出</el-button>
            </el-form-item>
              <div class="f-s-c" style="padding: 15px 0px 0px 0px;display: flex; justify-content: space-between">
                <div style="display: flex; justify-content: flex-start; color: red">*<div style="background-color: #f5d2a5; width: 40px; height: 16px"></div>标识的数据为选中上级组织或当前用户身份组织本级统计数据！</div>
              </div>
          </div>
        </div>
        <el-table :row-class-name="tableRowClassName" :data="orgDataList" border v-loading="orgDataListLoading" style="width: 100%" max-height="480">
          <el-table-column prop="orgName" header-align="center" align="center" :label="'组织' + (orgDataFirstColumnTitle || orgDataFirstColumnTitleOrigin || '')" width="280"/>
          <el-table-column prop="registerNum" header-align="center" align="center" label="注册人数(累计)"/>
          <el-table-column prop="actNum" header-align="center" align="center" label="活动总场次"/>
          <el-table-column prop="actJoinVolunteerNum" header-align="center" align="center" label="活动总人数"/>
          <el-table-column prop="actNumPublic" header-align="center" align="center" label="群众活动总场次"/>
          <el-table-column prop="actJoinVolunteerNumPublic" header-align="center" align="center" label="群众活动总人数"/>
          <el-table-column prop="activeNum" header-align="center" align="center" label="活跃人数"/>
          <el-table-column prop="totalServiceTime" header-align="center" align="center" label="总时长"/>
          <el-table-column prop="teamNum" header-align="center" align="center" label="新增团队数"/>
          <el-table-column prop="publishNewsNum" header-align="center" align="center" label="发布新闻数"/>
          <el-table-column prop="resourceNum" header-align="center" align="center" label="资源总数(累计)"/>
          <el-table-column prop="resourceDockingNum" header-align="center" align="center" label="资源使用数"/>
          <el-table-column prop="requirementNum" header-align="center" align="center" label="需求总数(累计)"/>
          <el-table-column prop="requirementDockingNum" header-align="center" align="center" label="需求使用数"/>
        </el-table>
<!--        <el-pagination-->
<!--            v-if="orgDataTotalPage > 10"-->
<!--            @size-change="(value) => sizeChangeHandle(value, 'orgData')"-->
<!--            @current-change="(value) => currentChangeHandle(value, 'orgData')"-->
<!--            :current-page="orgDataPageIndex"-->
<!--            :page-sizes="[10, 20, 50, 100]"-->
<!--            :page-size="orgDataPageSize"-->
<!--            :total="orgDataTotalPage"-->
<!--            layout="total, sizes, prev, pager, next, jumper">-->
<!--        </el-pagination>-->
      </el-card>
      <el-card class="box-card" style="margin-top: 10px">
        <div slot="header" class="head">
          <div>
            <el-form-item>
              <span slot="label" class="title">团 队 数 据</span>
            </el-form-item>
            <el-form-item label="团队名称" prop="teamName" v-if="currentUserManagerCapacity !== 'TEAM_ADMIN'">
              <el-input v-model="dataForm.teamName" clearable placeholder="请输入团队名称"/>
            </el-form-item>
            <el-form-item label="时间段" prop="teamDataTimeRange">
              <el-date-picker v-model="dataForm.teamDataTimeRange" clearable type="daterange"
                              value-format="yyyy-MM-dd" align="right" start-placeholder="开始" end-placeholder="结束">
              </el-date-picker>
            </el-form-item>
              <el-form-item label="所有下属团队" prop="all">
                  <el-checkbox v-model="dataForm.all" ></el-checkbox>
              </el-form-item>
            <el-form-item>
              <el-button icon="el-icon-search" type="warning" @click="currentChangeHandle(1, 'teamData')">查询</el-button>
              <el-button @click="resetTeamDataForm()" icon="el-icon-refresh-left">重置</el-button>
              <el-button icon="el-icon-download" type="success" v-loading.fullscreen.lock="fullscreenLoading" @click="exportTeamDataHandle()">导出</el-button>
            </el-form-item>
          </div>
        </div>
        <el-table :data="teamDataList" border v-loading="teamDataListLoading" style="width: 100%">
          <el-table-column prop="teamName" header-align="center" align="center" label="团队名称" width="280"/>
          <el-table-column prop="registerNum" header-align="center" align="center" label="注册人数(累计)"/>
          <el-table-column prop="actNum" header-align="center" align="center" label="活动总场次"/>
          <el-table-column prop="actJoinVolunteerNum" header-align="center" align="center" label="活动总人数"/>
          <el-table-column prop="actNumPublic" header-align="center" align="center" label="群众活动总场次"/>
          <el-table-column prop="actJoinVolunteerNumPublic" header-align="center" align="center" label="群众活动总人数"/>
          <el-table-column prop="activeNum" header-align="center" align="center" label="活跃人数"/>
          <el-table-column prop="totalServiceTime" header-align="center" align="center" label="总时长"/>
          <el-table-column prop="publishNewsNum" header-align="center" align="center" label="发布新闻数"/>
          <el-table-column prop="resourceNum" header-align="center" align="center" label="资源总数(累计)"/>
          <el-table-column prop="resourceDockingNum" header-align="center" align="center" label="资源使用数"/>
          <el-table-column prop="requirementNum" header-align="center" align="center" label="需求总数(累计)"/>
          <el-table-column prop="requirementDockingNum" header-align="center" align="center" label="需求使用数"/>
        </el-table>
        <el-pagination
            @size-change="(value) => sizeChangeHandle(value, 'teamData')"
            @current-change="(value) => currentChangeHandle(value, 'teamData')"
            :current-page="teamDataPageIndex"
            :page-sizes="[10, 20, 50, 100]"
            :page-size="teamDataPageSize"
            :total="teamDataTotalPage"
            layout="total, sizes, prev, pager, next, jumper">
        </el-pagination>
      </el-card>
    </el-form>
  </div>
</template>

<script>
export default {
  name: "org_report_form",
  data() {
    return {
      dataForm: {
        publishOrgCodes: [],
        publishOrgCode: null,
        orgDataTimeRange: [],
        teamName: null,
        teamDataTimeRange: [],
        all: null
      },
      orgList: [],
      orgDataList: [],
      orgDataListLoading: false,
      teamDataList: [],
      teamDataListLoading: false,
      orgDataPageIndex: 1,
      orgDataPageSize: 10,
      orgDataTotalPage: 0,
      teamDataPageIndex: 1,
      teamDataPageSize: 10,
      teamDataTotalPage: 0,
      currentUserManagerCapacity: null,
      currentUserOrgCode: null,
      orgDataFirstColumnTitle: null,
      orgDataFirstColumnTitleOrigin: null,
      fullscreenLoading: false
    }
  },
  created() {
    this.getOrg()
    this.getCurrentLoginUserInfo()
  },
  methods: {
    resetOrgDataForm() {
      this.dataForm.orgDataTimeRange = []
      this.getOrgDataList()
    },
    resetTeamDataForm() {
      this.dataForm.teamDataTimeRange = []
      this.dataForm.teamName = null
      this.getTeamDataList()
    },
    getDataList() {
      if (this.currentUserManagerCapacity !== 'TEAM_ADMIN') {
        this.getOrgDataList()
      }
      this.getTeamDataList()
    },
    // 每页数
    sizeChangeHandle(val, type) {
      if (type === 'orgData') {
        this.orgDataPageSize = val
        this.orgDataPageIndex = 1
        this.getOrgDataList()
      }
      if (type === 'teamData') {
        this.teamDataPageSize = val
        this.teamDataPageIndex = 1
        this.getTeamDataList()
      }
    },
    // 当前页
    currentChangeHandle(val, type) {
      if (type === 'orgData') {
        this.orgDataPageIndex = val
        this.getOrgDataList()
      }
      if (type === 'teamData') {
        this.teamDataPageIndex = val
        this.getTeamDataList()
      }
    },
    getOrgDataList() {
      this.orgDataListLoading = true
      this.$http({
        url: this.$http.adornUrl('/admin/report_form/getOrgDataList'),
        method: 'post',
        data: this.$http.adornData({
          parentOrgCode: this.dataForm.publishOrgCode,
          start: this.dataForm.orgDataTimeRange && this.dataForm.orgDataTimeRange.length === 2 ? this.dataForm.orgDataTimeRange[0] + ' 00:00:00' : null,
          end: this.dataForm.orgDataTimeRange && this.dataForm.orgDataTimeRange.length === 2 ? this.dataForm.orgDataTimeRange[1] + ' 23:59:59' : null,
          // currentPage: this.orgDataPageIndex,
          // pageSize: this.orgDataPageSize
        })
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.orgDataList = data.obj
          // this.orgDataTotalPage = data.obj.total
        } else {
          this.orgDataList = []
          // this.orgDataTotalPage = 0
        }
        this.orgDataListLoading = false
      })
    },
    tableRowClassName (row, rowIndex) {
      if (row.row.orgCode === this.dataForm.publishOrgCode || row.row.orgCode === this.currentUserOrgCode) {
        return 'warning-row';
      } else if (row.row.orgCode === 'sum') {
        return 'fixed-row';
      } else {
        return '';
      }
    },
    getTeamDataList() {
      this.teamDataListLoading = true
      this.$http({
        url: this.$http.adornUrl('/admin/report_form/getTeamDataList'),
        method: 'post',
        data: this.$http.adornData({
          parentOrgCode: this.dataForm.publishOrgCode,
          teamName: this.currentUserManagerCapacity !== 'TEAM_ADMIN' ? this.dataForm.teamName : null,
          start: this.dataForm.teamDataTimeRange && this.dataForm.teamDataTimeRange.length === 2 ? this.dataForm.teamDataTimeRange[0] + ' 00:00:00' : null,
          end: this.dataForm.teamDataTimeRange && this.dataForm.teamDataTimeRange.length === 2 ? this.dataForm.teamDataTimeRange[1] + ' 23:59:59' : null,
          currentPage: this.teamDataPageIndex,
          pageSize: this.teamDataPageSize,
          all: this.dataForm.all
        })
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.teamDataList = data.obj.records
          this.teamDataTotalPage = data.obj.total
        } else {
          this.teamDataList = []
          this.teamDataTotalPage = 0
        }
        this.teamDataListLoading = false
      })
    },
    getCurrentLoginUserInfo() {
      this.$http({
        url: this.$http.adornUrl('/admin/user/loginUser'),
        method: 'get',
        params: this.$http.adornParams()
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.currentUserManagerCapacity = data.obj.managerCapacity
          this.currentUserOrgCode = data.obj.orgCode
          this.orgDataFirstColumnTitleOrigin = data.obj.managerCapacity === 'ASSOCIATION_ADMIN' ? '（实践中心 + 实践所）' :
              (data.obj.managerCapacity === 'SUB_ASSOCIATION_ADMIN' ? '（本级实践所 + 实践站）' :
                  (data.obj.managerCapacity === 'COMMUNITY_ADMIN' ? '（实践站）' : null))
        } else {
          this.currentUserManagerCapacity = null
          this.orgDataFirstColumnTitle = null
        }
        this.getDataList()
      })
    },
    getOrg() {
      this.$http({
        url: this.$http.adornUrl(`/admin/org/getOrgTree`),
        method: 'get',
        params: this.$http.adornParams()
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.orgList = this.getTreeData(data.obj)
        } else {
          this.orgList = []
        }
      })
    },
    // *处理点位分类最后children数组为空的状态
    getTreeData: function (data) {
      var that = this
      // 循环遍历json数据
      data.forEach(function (e) {
        if (!e.children || e.children.length < 1) {
          e.children = undefined
        } else {
          // children若不为空数组，则继续 递归调用 本方法
          that.getTreeData(e.children)
        }
      })
      return data
    },
    handleChange(value) {
      this.dataForm.publishOrgCode = value && value.length > 0 ? value[value.length - 1] : null
      let checkedNode = this.$refs['orgTree'].getCheckedNodes()[0]
      let level = checkedNode ? checkedNode.data.level : null
      this.orgDataFirstColumnTitle = level === 1 ? '（实践中心 + 实践所）' : (level === 2 ? '（本级实践所 + 实践站）' : (level === 3 ? '（实践站）' : null))
      this.currentChangeHandle(1, 'orgData')
      this.currentChangeHandle(1, 'teamData')
    },
    exportOrgDataHandle() {
      this.fullscreenLoading = true;
      this.$http({
        url: this.$http.adornUrl('/admin/report_form/exportOrgDataForm'),
        method: 'post',
        responseType: 'arraybuffer',
        data: this.$http.adornData({
          parentOrgCode: this.dataForm.publishOrgCode,
          start: this.dataForm.orgDataTimeRange && this.dataForm.orgDataTimeRange.length === 2 ? this.dataForm.orgDataTimeRange[0] + ' 00:00:00' : null,
          end: this.dataForm.orgDataTimeRange && this.dataForm.orgDataTimeRange.length === 2 ? this.dataForm.orgDataTimeRange[1] + ' 23:59:59' : null
        })
      }).then(({data}) => {
        if (data.code && data.code !== 0) {
          this.$message.error('导出失败')
          this.fullscreenLoading = false
        } else {
          this.fullscreenLoading = false;
          let blob = new Blob([data], {type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8'})
          let objectUrl = URL.createObjectURL(blob)
          let a = document.createElement('a')
          // window.location.href = objectUrl
          a.href = objectUrl
          a.download = '组织架构数据报表-按组织统计.xlsx'
          a.click()
          URL.revokeObjectURL(objectUrl)
          this.$message({
            type: 'success',
            message: '导出成功'
          })
        }
      })
    },
    exportTeamDataHandle() {
      this.fullscreenLoading = true;
      this.$http({
        url: this.$http.adornUrl('/admin/report_form/exportTeamDataForm'),
        method: 'post',
        responseType: 'arraybuffer',
        data: this.$http.adornData({
          parentOrgCode: this.dataForm.publishOrgCode,
          teamName: this.currentUserManagerCapacity !== 'TEAM_ADMIN' ? this.dataForm.teamName : null,
          start: this.dataForm.teamDataTimeRange && this.dataForm.teamDataTimeRange.length === 2 ? this.dataForm.teamDataTimeRange[0] + ' 00:00:00' : null,
          end: this.dataForm.teamDataTimeRange && this.dataForm.teamDataTimeRange.length === 2 ? this.dataForm.teamDataTimeRange[1] + ' 23:59:59' : null,
          all: this.dataForm.all
        })
      }).then(({data}) => {
        if (data.code && data.code !== 0) {
          this.$message.error('导出失败')
          this.fullscreenLoading = false
        } else {
          this.fullscreenLoading = false
          let blob = new Blob([data], {type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8'})
          let objectUrl = URL.createObjectURL(blob)
          let a = document.createElement('a')
          // window.location.href = objectUrl
          a.href = objectUrl
          a.download = '组织架构数据报表-按团队统计.xlsx'
          a.click()
          URL.revokeObjectURL(objectUrl)
          this.$message({
            type: 'success',
            message: '导出成功'
          })
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.el-table::v-deep .warning-row {
  background: #f5d2a5 !important;
  position: sticky;
  position: -webkit-sticky;
  top: 0;
  z-index: 3;
}
.el-table::v-deep .fixed-row {
  position: sticky;
  background: #f5f7fa !important;
  bottom: 0;
  width: 100%;
}
.box-card {
  .head {
    div {
      .el-form-item {
        margin-bottom: 0;
      }
    }
    ::v-deep .title {
      background-color: #ece9e9;
      padding: 5px;
      font-size: larger;
      box-shadow: 3px 3px 3px rgba(0, 0, 0, .5);
      border-radius: 5px;
    }
  }
}
</style>
