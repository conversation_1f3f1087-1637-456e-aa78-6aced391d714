<template>
  <div class="mod-activation-form">
<!--    <span style="color: red">*筛选条件待定</span>-->
        <el-form :inline="true" ref="dataForm" :model="dataForm" @keyup.enter.native="getDataList()" >
          <el-form-item label="所属领域" prop="belongFieldList">
            <el-select style="width: 350px" v-model="dataForm.belongFieldList" multiple clearable placeholder="请选择">
              <el-option
                  v-for="item in belongFieldList"
                  :key="item.typeId"
                  :label="item.typeName"
                  :value="item.typeId">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="报表：" prop="timeType">
            <el-radio-group  v-model="timeType" @change="timeTypeChange">
            <el-radio  label="year">年报</el-radio>
            <el-radio  label="month">月报</el-radio>
            </el-radio-group>
          </el-form-item>
                    <el-form-item v-if="timeType === 'year'"  prop="year">
                      <el-date-picker
                          v-model="dataForm.year"
                          type="year"
                          value-format="yyyy"
                          placeholder="选择年">
                      </el-date-picker>
                    </el-form-item>
          <el-form-item v-if="timeType === 'month'"  prop="month">
            <el-date-picker
                v-model="dataForm.month"
                type="month"
                value-format="yyyy-MM"
                placeholder="选择月">
            </el-date-picker>
          </el-form-item>
<!--          <el-form-item label="活动时间" prop="timeRange">-->
<!--            <el-date-picker-->
<!--                v-model="dataForm.timeRange"-->
<!--                clearable-->
<!--                type="daterange"-->
<!--                value-format="yyyy-MM-dd"-->
<!--                align="right"-->
<!--                start-placeholder="开始时间"-->
<!--                end-placeholder="结束时间">-->
<!--            </el-date-picker>-->
<!--          </el-form-item>-->
          <el-form-item>
            <el-button type="warning" icon="el-icon-search" @click="query()">查询</el-button>
            <el-button icon="el-icon-refresh-left" @click="resetForm()">重置</el-button>
          </el-form-item>
        </el-form>
    <div class="f-s-c" style="padding: 15px 0;display: flex; justify-content: flex-end">
      <div>
        <el-button v-if="level === 2" icon="el-icon-back" type="primary" @click="backToBfSum()">返回</el-button>
        <el-button icon="el-icon-download" type="success" v-loading.fullscreen.lock="fullscreenLoading" @click="exportHandle()">导出</el-button>
        <el-button type="warning" icon="el-icon-refresh" @click="backToBfSum()">刷新</el-button>
<!--        <el-button type="danger" icon="el-icon-refresh-left" @click=" initActFormReport()">初始化</el-button>-->
      </div>
    </div>
    <el-table :key="tableId" v-if="level === 1" :data="dataList" border v-loading="dataListLoading" style="width: 100%">
<!--      <el-table-column type="expand">-->
<!--        <template slot-scope="scope">-->
<!--          <el-table :data="scope.row.children" border style="width: 100%">-->
<!--            <el-table-column prop="belongField" header-align="center" align="center" label="所属领域"/>-->
<!--            <el-table-column prop="actNumTotal" header-align="center" align="center" label="活动总数"/>-->
<!--            <el-table-column prop="actTimeTotal" header-align="center" align="center" label="活动总时长"/>-->
<!--            <el-table-column prop="actJoinNumTotal" header-align="center" align="center" label="总参与人数"/>-->
<!--            <el-table-column prop="actJoinNum" header-align="center" align="center" label="参与人次"/>-->
<!--            <el-table-column prop="actJoinTeamNum" header-align="center" align="center" label="参与团队数"/>-->
<!--          </el-table>-->
<!--        </template>-->
<!--      </el-table-column>-->
      <el-table-column prop="belongField" header-align="center" align="center" label="所属领域"/>
      <el-table-column prop="actNumTotal" header-align="center" align="center" label="活动总数">
        <template slot-scope="scope">
          <a style="cursor: pointer" @click="showActSumTable(scope.row.actBfTop, scope.row.belongField)">{{ scope.row.actNumTotal }}</a>
        </template>
      </el-table-column>
      <el-table-column prop="actTimeTotal" header-align="center" align="center" label="活动总时长（小时）"/>
      <el-table-column prop="actJoinNumTotal" header-align="center" align="center" label="总参与人数"/>
      <el-table-column prop="actJoinNum" header-align="center" align="center" label="参与人次"/>
      <el-table-column prop="actJoinTeamNum" header-align="center" align="center" label="参与团队数">
        <template slot-scope="scope">
          <a style="cursor: pointer" @click="showTeamSumTable(scope.row.actBfTop, scope.row.belongField)">{{ scope.row.actJoinTeamNum }}</a>
        </template>
      </el-table-column>
    </el-table>
    <el-table :key="tableId" v-if="level === 2 && subTable && subTable === 'act'" :data="dataList" border v-loading="dataListLoading" style="width: 100%">
      <el-table-column header-align="center" align="center" label="序号" type="index" width="50"/>
      <el-table-column prop="actName" header-align="center" align="center" label="活动名称" min-widht="180"/>
      <el-table-column prop="actStartEndTime" header-align="center" align="center" label="活动起止时间" min-width="120">
        <template slot-scope="scope">
          <span>{{scope.row.actStartTime + '至' + scope.row.actEndTime}}</span>
        </template>
      </el-table-column>
      <el-table-column prop="actPublisherName" header-align="center" align="center" label="发布单位"/>
      <el-table-column prop="actContactPerson" header-align="center" align="center" label="联系人"/>
      <el-table-column prop="actContactPhone" header-align="center" align="center" label="联系方式"/>
    </el-table>
    <el-table :key="tableId" v-if="level === 2 && subTable && subTable === 'team'" :data="dataList" border v-loading="dataListLoading" style="width: 100%">
      <el-table-column header-align="center" align="center" label="序号" type="index" width="50"/>
      <el-table-column prop="teamName" header-align="center" align="center" label="团队名称" min-width="180"/>
      <el-table-column prop="teamVolunteerNum" header-align="center" align="center" label="实际人数"/>
      <el-table-column prop="teamOrgName" header-align="center" align="center" label="上级组织"/>
      <el-table-column prop="teamThisYearLivelyVolunteerNum" header-align="center" align="center" label="当年活跃人数"/>
      <el-table-column prop="teamThisYearServiceDurationTotal" header-align="center" align="center" label="当年服务时长（小时）"/>
      <el-table-column prop="teamThisYearActNum" header-align="center" align="center" label="当年活动次数"/>
    </el-table>
    <el-pagination
        v-if="level === 2 && subTable && subTable !== ''"
        @size-change="sizeChangeHandle"
        @current-change="currentChangeHandle"
        :current-page="pageIndex"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pageSize"
        :total="totalPage"
        layout="total, sizes, prev, pager, next, jumper">
    </el-pagination>
  </div>
</template>

<script>
import {uuid} from '@/utils'

export default {
  name: "activity",
  data () {
    return {
      belongFieldList: [],
      timeType: 'year',
      dataForm: {
        belongFieldList: [],
        timeRange: [],
        year: null,
        month: null
      },
      tableId: null,
      pageIndex: 1,
      pageSize: 10,
      totalPage: 0,
      level: 1,
      subTable: null,
      belongField: null,
      currentBf: null,
      dataList: [],
      dataListLoading: false,
      fullscreenLoading: false
    }
  },
  mounted() {
    this.tableId = uuid()
    this.getBelongFields()
    this.getDataList()
  },
  methods: {
    resetForm() {
      this.$refs['dataForm']?.resetFields()
      this.tableId = uuid()
      this.level = 1
      this.subTable = null
      this.belongField = null
      this.pageIndex = 1
      this.pageSize = 10
      this.totalPage = 0
      this.dataList = []
      this.$nextTick(() => {
        this.getDataList()
      })
    },
    query() {
      this.tableId = uuid()
      this.level = 1
      this.subTable = null
      this.belongField = null
      this.pageIndex = 1
      this.pageSize = 10
      this.totalPage = 0
      this.dataList = []
      this.$nextTick(() => {
        this.getDataList()
      })
    },
    timeTypeChange() {
      this.dataForm.year = null
      this.dataForm.month = null
    },
    getDataList() {
      this.dataListLoading = true
      this.$http({
        url: this.$http.adornUrl('/admin/report_form/getActivityForm'),
        method: 'post',
        data: this.$http.adornData({
          bfIds: this.dataForm.belongFieldList,
          startTime: this.dataForm.timeRange && this.dataForm.timeRange.length === 2 ? this.dataForm.timeRange[0] + ' 00:00:00' : null,
          endTime: this.dataForm.timeRange && this.dataForm.timeRange.length === 2 ? this.dataForm.timeRange[1] + ' 23:59:59' : null,
          year: this.timeType === 'year'? this.dataForm.year: null,
          month: this.timeType === 'month'? this.dataForm.month: null
        })
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.dataList = data.obj
        } else {
          this.dataList = []
        }
        this.dataListLoading = false
      })
    },
    getBelongFields() {
      this.$http({
        url: this.$http.adornUrl(`/admin/zyz/platform/belongFieldDict/getTopBelongField`),
        method: 'get'
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.belongFieldList = data.obj
        } else {
          this.belongFieldList = []
        }
      })
    },
    backToBfSum() {
      this.tableId = uuid()
      this.level = 1
      this.subTable = null
      this.belongField = null
      this.pageIndex = 1
      this.pageSize = 10
      this.totalPage = 0
      this.dataList = []
      this.$nextTick(() => {
        this.getDataList()
      })
    },
    initActFormReport () {
      this.$confirm(`此过程将持续一小段时间初，始化结束后将自动更新报表数据，确定初始化活动报表数据？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        closeOnClickModal: false
      }).then(() => {
        this.$http({
          url: this.$http.adornUrl('/admin/report_form/initActFormReport'),
          method: 'get'
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.$message({
              message: '操作成功',
              type: 'success',
              duration: 1500,
              onClose: () => {
                this.backToBfSum()
              }
            })
          } else {
            this.$message.error(data.msg)
          }
        })
      })
    },
    showActSumTable(value, bf) {
      this.tableId = uuid()
      this.level = 2
      this.subTable = 'act'
      this.belongField = bf
      this.currentBf = value
      this.pageIndex = 1
      this.pageSize = 10
      this.totalPage = 0
      this.dataList = []
      this.$nextTick(() => {
        this.getActSumList()
      })
    },
    showTeamSumTable(value, bf) {
      this.tableId = uuid()
      this.level = 2
      this.subTable = 'team'
      this.belongField = bf
      this.currentBf = value
      this.pageIndex = 1
      this.pageSize = 10
      this.totalPage = 0
      this.dataList = []
      this.$nextTick(() => {
        this.getTeamSumList()
      })
    },
    // 每页数
    sizeChangeHandle(val) {
      this.pageSize = val
      this.pageIndex = 1
      if (this.subTable === 'act') {
        this.getActSumList()
      }
      if (this.subTable === 'team') {
        this.getTeamSumList()
      }
    },
    // 当前页
    currentChangeHandle(val) {
      this.pageIndex = val
      if (this.subTable === 'act') {
        this.getActSumList()
      }
      if (this.subTable === 'team') {
        this.getTeamSumList()
      }
    },
    getActSumList() {
      this.dataListLoading = true
      this.$http({
        url: this.$http.adornUrl('/admin/report_form/pageForActSum'),
        method: 'post',
        data: this.$http.adornData({
          belongField: this.currentBf,
          startTime: this.dataForm.timeRange && this.dataForm.timeRange.length === 2 ? this.dataForm.timeRange[0] + ' 00:00:00' : null,
          endTime: this.dataForm.timeRange && this.dataForm.timeRange.length === 2 ? this.dataForm.timeRange[1] + ' 23:59:59' : null,
          currentPage: this.pageIndex,
          pageSize: this.pageSize
        })
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.dataList = data.obj.records
          this.totalPage = data.obj.total
        } else {
          this.dataList = []
          this.totalPage = 0
        }
        this.dataListLoading = false
      })
    },
    getTeamSumList() {
      this.dataListLoading = true
      this.$http({
        url: this.$http.adornUrl('/admin/report_form/pageForTeamSum'),
        method: 'post',
        data: this.$http.adornData({
          belongField: this.currentBf,
          startTime: this.dataForm.timeRange && this.dataForm.timeRange.length === 2 ? this.dataForm.timeRange[0] + ' 00:00:00' : null,
          endTime: this.dataForm.timeRange && this.dataForm.timeRange.length === 2 ? this.dataForm.timeRange[1] + ' 23:59:59' : null,
          currentPage: this.pageIndex,
          pageSize: this.pageSize
        })
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.dataList = data.obj.records
          this.totalPage = data.obj.total
        } else {
          this.dataList = []
          this.totalPage = 0
        }
        this.dataListLoading = false
      })
    },
    exportHandle() {
      // this.$message.warning('功能待开发…………')
      this.fullscreenLoading = true
      this.$http({
        url: this.$http.adornUrl('/admin/report_form/exportActivityForm'),
        method: 'post',
        responseType: 'arraybuffer',
        data: this.$http.adornData({
          belongField: this.level === 1 ? this.dataForm.belongField : this.currentBf,
          bfIds: this.dataForm.belongFieldList,
          year: this.dataForm.year,
          startTime: this.dataForm.timeRange && this.dataForm.timeRange.length === 2 ? this.dataForm.timeRange[0] + ' 00:00:00' : null,
          endTime: this.dataForm.timeRange && this.dataForm.timeRange.length === 2 ? this.dataForm.timeRange[1] + ' 23:59:59' : null,
          level: this.level,
          subTable: this.subTable
        })
      }).then(({data}) => {
        if (data.code && data.code !== 0) {
          this.$message.error('导出失败')
        } else {
          this.fullscreenLoading = false;
          let blob = new Blob([data], {type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8'})
          let objectUrl = URL.createObjectURL(blob)
          let a = document.createElement('a')
          // window.location.href = objectUrl
          a.href = objectUrl
          a.download = this.level === 1 ? '领域活动报表.xlsx' : (this.subTable === 'act' ? this.belongField + '领域下活动信息表.xlsx' : this.belongField + '领域下参与团队信息表.xlsx')
          a.click()
          URL.revokeObjectURL(objectUrl)
          this.$message({
            type: 'success',
            message: '导出成功'
          })
        }
      })
    }
  }
}
</script>

<style scoped>

</style>
