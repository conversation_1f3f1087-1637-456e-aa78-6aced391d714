<template>
  <div>
    <el-form :inline="true" :model="dataForm" ref="dataForm">
      <el-form-item label="查询类型" prop="searchType" v-if="managerCapacity === 'ASSOCIATION_ADMIN' || managerCapacity === 'SUB_ASSOCIATION_ADMIN'">
        <el-radio-group v-model="dataForm.searchType" @change="(value) => { handleChange(value, 'type') }">
          <el-radio :label="'org'">按组织查询</el-radio>
          <el-radio :label="'team'">按团队查询</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item :label="'上级组织'" prop="publishOrgCodes" v-if="dataForm.searchType === 'org' && (managerCapacity === 'ASSOCIATION_ADMIN' || managerCapacity === 'SUB_ASSOCIATION_ADMIN')">
        <el-cascader placeholder="请选择" v-model="dataForm.publishOrgCodes" :options="orgList" ref="orgTree"
                     :show-all-levels="false" clearable :props="{ checkStrictly: true, value: 'code', label: 'name' }"
                     @change="(value) => { handleChange(value, 'org') }" style="width: 300px"/>
      </el-form-item>
      <el-form-item :label="'团队名称'" prop="publishOrgCodes" v-if="dataForm.searchType === 'team' && (managerCapacity === 'ASSOCIATION_ADMIN' || managerCapacity === 'SUB_ASSOCIATION_ADMIN' || managerCapacity === 'COMMUNITY_ADMIN')">
        <el-select v-model="dataForm.teamIdList" multiple filterable remote reserve-keyword placeholder="请输入团队名称"
                   :remote-method="searchTeamByName" :loading="teamSearchLoading" @change="(value) => { handleChange(value, 'team') }">
          <el-option v-for="item in teamSearchList" :key="item.id" :label="item.name" :value="item.id">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="活动名称" prop="actName">
        <el-input v-model="dataForm.actName" clearable placeholder="请输入团队名称"/>
      </el-form-item>
      <el-form-item label="活动时间" prop="actTimeRange">
        <el-date-picker v-model="dataForm.actTimeRange" clearable type="daterange"
                        value-format="yyyy-MM-dd" align="right" start-placeholder="开始" end-placeholder="结束"/>
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-search" type="warning" @click="getDataList()">查询</el-button>
        <el-button @click="resetForm()" icon="el-icon-refresh-left">重置</el-button>
      </el-form-item>
      <div class="f-s-c" style="padding: 15px 0;display: flex;justify-content:flex-end;align-items: center ">
        <div>
          <el-button icon="el-icon-download" type="success" v-loading.fullscreen.lock="fullscreenLoading" @click="exportHandle()">导出</el-button>
        </div>
      </div>
    </el-form>
    <el-table :data="dataList" border v-loading="dataListLoading" style="width: 100%" :row-class-name="tableRowClassName">
      <el-table-column prop="belongFieldName" header-align="center" align="center" label="所属领域"/>
      <el-table-column prop="joinTeamNum" header-align="center" align="center" label="参与团队数量" v-if="managerCapacity !== 'TEAM_ADMIN'"/>
      <el-table-column prop="joinVolunteerNum" header-align="center" align="center" label="参加志愿服务总人数"/>
      <el-table-column prop="actNum" header-align="center" align="center" label="开展活动场次"/>
      <el-table-column prop="totalServiceTime" header-align="center" align="center" label="总服务时长"/>
      <el-table-column prop="distinctJoinVolunteerNum" header-align="center" align="center" label="去重后的人数"/>
    </el-table>
  </div>
</template>

<script>
export default {
  name: "belong_field_report_form",
  data() {
    return {
      dataForm: {
        publishOrgCodes: [],
        publishOrgCode: null,
        queryType: null,
        actTimeRange: [],
        actName: null,
        teamIdList: [],
        teamIds: null,
        searchType: null
      },
      teamSearchList: [],
      orgList: [],
      dataList: [],
      teamSearchLoading: false,
      dataListLoading: false,
      managerCapacity: null,
      userInfo: null,
      fullscreenLoading: false
    }
  },
  created() {
    this.getOrg()
    this.getCurrentLoginUserInfo()
  },
  methods: {
    resetForm() {
      this.$refs['dataForm']?.resetFields()
      this.$nextTick(() => {
        this.getOrgDataList()
      })
    },
    getDataList() {
      this.dataListLoading = true
      this.$http({
        url: this.$http.adornUrl('/admin/report_form/getBelongFieldReportFormData'),
        method: 'post',
        data: this.$http.adornData({
          orgCode: this.dataForm.publishOrgCode,
          teamIds: this.dataForm.teamIdList && this.dataForm.teamIdList.length > 0 ? this.dataForm.teamIdList.join(',') : null,
          queryType: this.dataForm.queryType,
          start: this.dataForm.actTimeRange && this.dataForm.actTimeRange.length === 2 ? this.dataForm.actTimeRange[0] + ' 00:00:00' : null,
          end: this.dataForm.actTimeRange && this.dataForm.actTimeRange.length === 2 ? this.dataForm.actTimeRange[1] + ' 23:59:59' : null,
          actName: this.dataForm.actName
        })
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.dataList = data.obj
        } else {
          this.dataList = []
        }
        this.dataListLoading = false
      })
    },
    searchTeamByName(query) {
      this.teamSearchLoading = true
      this.$http({
        url: this.$http.adornUrl('/admin/zyz/team/getTeamListByName'),
        method: 'get',
        params: this.$http.adornParams({
          name: query
        })
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.teamSearchList = data.obj
        } else {
          this.teamSearchList = []
        }
        this.teamSearchLoading = false
      })
    },
    getCurrentLoginUserInfo() {
      this.$http({
        url: this.$http.adornUrl('/admin/user/loginUser'),
        method: 'get',
        params: this.$http.adornParams()
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.userInfo = data.obj
          this.managerCapacity = data.obj.managerCapacity
          this.dataForm.searchType = data.obj.managerCapacity === 'COMMUNITY_ADMIN' ? 'team' :
              (data.obj.managerCapacity === 'ASSOCIATION_ADMIN' || data.obj.managerCapacity === 'SUB_ASSOCIATION_ADMIN' ? 'org' : null)
          if (data.obj.managerCapacity === 'ASSOCIATION_ADMIN' || data.obj.managerCapacity === 'SUB_ASSOCIATION_ADMIN' || data.obj.managerCapacity === 'COMMUNITY_ADMIN') {
            this.dataForm.queryType = 'org'
            this.dataForm.publishOrgCode = data.obj.orgCode
          }
          if (data.obj.managerCapacity === 'TEAM_ADMIN') {
            this.dataForm.queryType = 'team'
            this.dataForm.teamIdList = [data.obj.teamId]
          }
        } else {
          this.userInfo = null
          this.managerCapacity = null
          this.dataForm.searchType = null
          this.dataForm.queryType = null
          this.dataForm.publishOrgCode = null
          this.dataForm.teamIdList = []
        }
        this.getDataList()
      })
    },
    getOrg() {
      this.$http({
        url: this.$http.adornUrl(`/admin/org/getOrgTree`),
        method: 'get',
        params: this.$http.adornParams()
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.orgList = this.getTreeData(data.obj)
        } else {
          this.orgList = []
        }
      })
    },
    // *处理点位分类最后children数组为空的状态
    getTreeData: function (data) {
      var that = this
      // 循环遍历json数据
      data.forEach(function (e) {
        if (!e.children || e.children.length < 1) {
          e.children = undefined
        } else {
          // children若不为空数组，则继续 递归调用 本方法
          that.getTreeData(e.children)
        }
      })
      return data
    },
    handleChange(value, type) {
      if (type === 'org') {
        this.dataForm.queryType = 'org'
        this.dataForm.publishOrgCode = value && value.length > 0 ? value[value.length - 1] : this.userInfo.orgCode
        this.dataForm.teamIdList = []
      }
      if (type === 'team') {
        this.dataForm.queryType = value && value.length > 0 ? 'team' : (this.managerCapacity === 'TEAM_ADMIN' ? 'team' : 'org')
        this.dataForm.teamIdList = value && value.length > 0 ? value : (this.managerCapacity === 'TEAM_ADMIN' ? [this.userInfo.teamId] : [])
        if (this.managerCapacity !== 'TEAM_ADMIN' && (!value || value.length === 0)) {
          this.dataForm.publishOrgCode = this.userInfo.orgCode
        } else {
          this.dataForm.publishOrgCode = null
        }
      }
      if (type === 'type') {
        this.dataForm.publishOrgCode = this.userInfo.orgCode
        this.dataForm.queryType = 'org'
      }
    },
    exportHandle() {
      this.fullscreenLoading = true
      this.$http({
        url: this.$http.adornUrl('/admin/report_form/exportBelongFieldFormData'),
        method: 'post',
        responseType: 'arraybuffer',
        data: this.$http.adornData({
          orgCode: this.dataForm.publishOrgCode,
          teamIds: this.dataForm.teamIdList && this.dataForm.teamIdList.length > 0 ? this.dataForm.teamIdList.join(',') : null,
          queryType: this.dataForm.queryType,
          start: this.dataForm.actTimeRange && this.dataForm.actTimeRange.length === 2 ? this.dataForm.actTimeRange[0] + ' 00:00:00' : null,
          end: this.dataForm.actTimeRange && this.dataForm.actTimeRange.length === 2 ? this.dataForm.actTimeRange[1] + ' 23:59:59' : null,
          actName: this.dataForm.actName
        })
      }).then(({data}) => {
        if (data.code && data.code !== 0) {
          this.$message.error('导出失败')
          this.fullscreenLoading = false
        } else {
          this.fullscreenLoading = false
          let blob = new Blob([data], {type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8'})
          let objectUrl = URL.createObjectURL(blob)
          let a = document.createElement('a')
          // window.location.href = objectUrl
          a.href = objectUrl
          a.download = '服务领域数据报表.xlsx'
          a.click()
          URL.revokeObjectURL(objectUrl)
          this.$message({
            type: 'success',
            message: '导出成功'
          })
        }
      })
    },
    tableRowClassName (row, rowIndex) {
      if (row.row.belongFieldName === '汇总') {
        return 'fixed-row';
      } else {
        return '';
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.el-table::v-deep .fixed-row {
  position: sticky;
  background: #f5f7fa !important;
  bottom: 0;
  width: 100%;
}
</style>
