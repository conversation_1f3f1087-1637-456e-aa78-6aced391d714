<template>
  <div class="mod-role">
    <el-form :inline="true" ref="dataForm" :model="dataForm" @keyup.enter.native="getDataList()" >
      <el-form-item label="关键字" prop="key" style="padding-right: 40px">
        <el-input v-model="dataForm.key" placeholder="资源名称/联系人/联系方式" clearable style="width: 120%"></el-input>
      </el-form-item>
      <el-form-item label="发布组织" prop="publishOrgCodes">
        <el-cascader
            placeholder="请选择"
            v-model="dataForm.publishOrgCodes"
            :options="orgList"
            :disabled="this.dataForm.id"
            :show-all-levels="false"
            clearable
            :props="{checkStrictly: true, value: 'code',label: 'name'}"
            @change="(value) => {handleChange(value, 'org')}"
        ></el-cascader>
      </el-form-item>
      <el-form-item label="资源类型" prop="type">
        <el-dict :code="'REQUIREMENT_TYPE'" v-model="dataForm.type"></el-dict>
      </el-form-item>
      <el-form-item label="所属领域" prop="fieldIds">
        <el-cascader
            placeholder="请选择"
            v-model="dataForm.fieldIds"
            :options="fields"
            @change="(value) => {handleChange(value, 'field')}"
            clearable
            :props="{checkStrictly: true, label: 'typeName', value: 'typeId'}"
        ></el-cascader>
      </el-form-item>
      <el-form-item label="发布时间" prop="timeRange">
        <el-date-picker
            v-model="dataForm.timeRange"
            clearable
            type="datetimerange"
            value-format="yyyy-MM-dd HH:mm:ss"
            align="right"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            :default-time="['00:00:00', '23:59:59']">
        </el-date-picker>
      </el-form-item>
      <el-form-item v-if="!dataForm.needAudit" label="审核状态" prop="status">
        <el-select v-model="dataForm.status" placeholder="请选择" clearable>
          <el-option
              v-for="item in [{label: '待审核', value: 'res_wait_audit'}, {label: '审核通过', value: 'res_audit_success'}, {label: '驳回', value: 'res_reject'}]"
              :key="item.value"
              :label="item.label"
              :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item  prop="needAudit">
        <el-checkbox v-model="dataForm.needAudit">需要我审核的</el-checkbox>
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-search" type="warning" @click="getDataList()">查询</el-button>
        <el-button icon="el-icon-refresh-left" @click="resetForm()">重置</el-button>
      </el-form-item>
    </el-form>
    <div class="f-s-c" style="padding: 15px 0;display: flex; justify-content: flex-end">
      <div>
        <el-button icon="el-icon-s-check" :disabled="dataListSelections.length <= 0" v-if="isAuth('resource:list:passBatch')" type="primary" @click="auditHandle(null, null, true)">批量通过</el-button>
        <el-button icon="el-icon-download" type="success" v-loading.fullscreen.lock="fullscreenLoading" @click="exportAudit()">全部导出</el-button>
      </div>
    </div>
    <el-table
        :data="dataList"
        border
        v-loading="dataListLoading"
        @selection-change="selectionChangeHandle"
        style="width: 100%;">
      <el-table-column
          type="selection"
          header-align="center"
          align="center"
          :selectable="(row, index) => {return row.status === 'res_wait_audit' && row.auditOrgCode === myOrgCode}"
          width="40">
      </el-table-column>
      <el-table-column
          type="index"
          label="序号"
          align="center"
          width="50">
      </el-table-column>
      <el-table-column
          prop="name"
          header-align="center"
          align="center"
          min-width="250"
          :show-overflow-tooltip="true"
          label="资源名称">
        <template slot-scope="scope">
          <a style="cursor: pointer" @click="resourceDetailHandler(scope.row.id)">{{scope.row.name}}</a>
        </template>
      </el-table-column>
      <el-table-column
          prop="typeText"
          header-align="center"
          min-width="80"
          align="center"
          label="资源类型">
      </el-table-column>
      <el-table-column
          prop="belongField"
          header-align="center"
          align="center"
          min-width="180"
          show-overflow-tooltip
          label="所属领域">
        <template slot-scope="scope">
          <span>{{scope.row.belongFieldNameTop && scope.row.belongFieldNameEnd ? scope.row.belongFieldNameTop + '-' + scope.row.belongFieldNameEnd : (scope.row.belongFieldNameTop || scope.row.belongFieldNameEnd)}}</span>
        </template>
      </el-table-column>
      <el-table-column
          prop="publishOrgName"
          header-align="center"
          min-width="150"
          :show-overflow-tooltip="true"
          align="center"
          show-overflow-tooltip
          label="发布组织">
      </el-table-column>
      <el-table-column
          prop="contactPerson"
          header-align="center"
          align="center"
          label="联系人">
      </el-table-column>
      <el-table-column
          prop="contactPhone"
          header-align="center"
          align="center"
          min-width="100"
          label="联系方式">
      </el-table-column>
      <el-table-column
          prop="timeRange"
          header-align="center"
          align="center"
          min-width="160"
          label="资源提供时间">
        <template slot-scope="scope">
          <span>{{(!scope.row.startTime || scope.row.startTime === '' || !scope.row.endTime || scope.row.endTime === '') ? '' : scope.row.startTime + '--' + scope.row.endTime}}</span>
        </template>
      </el-table-column>
      <el-table-column
          prop="publishTime"
          header-align="center"
          min-width="150"
          align="center"
          label="发布时间">
      </el-table-column>
      <el-table-column
          prop="auditOrgName"
          header-align="center"
          min-width="150"
          show-overflow-tooltip
          align="center"
          label="当前审核组织">
      </el-table-column>
      <el-table-column
          prop="statusText"
          header-align="center"
          align="center"
          label="审核状态">
      </el-table-column>
      <el-table-column
          v-if="isAuth('resource:list:audit')"
          fixed="right"
          header-align="center"
          align="center"
          min-width="100px"
          label="操作">
        <template slot-scope="scope">
          <el-button v-if="scope.row.status === 'res_wait_audit' && scope.row.auditOrgCode === myOrgCode" type="text" size="small" @click="auditHandle(scope.row.id, scope.row.name, true)">通过</el-button>
          <el-button v-if="scope.row.status === 'res_wait_audit' && scope.row.auditOrgCode === myOrgCode" type="text" size="small" @click="auditHandle(scope.row.id, scope.row.name, false)">驳回</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
        @size-change="sizeChangeHandle"
        @current-change="currentChangeHandle"
        :current-page="pageIndex"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pageSize"
        :total="totalPage"
        layout="total, sizes, prev, pager, next, jumper">
    </el-pagination>
    <!-- 需求详情 -->
    <resource-detail v-if="resourceDetailVisible" ref="resourceDetail" ></resource-detail>
  </div>
</template>

<script>
import listMixin from '@/mixins/list-mixins'
import ResourceDetail from './resource-detail'
export default {
  mixins: [listMixin],
  data () {
    return {
      mixinOptions: {
        dataUrl: '/admin/zyz/resource/pagesForAudit'
      },
      myOrgCode: this.$store.state.user.orgCode,
      dataForm: {
        key: null,
        publishOrgCodes: [],
        publishOrgCode: null,
        fieldIds: [],
        fieldId: null,
        type: null,
        status: '',
        timeRange: [],
        needAudit: true,
        publishStartTime: '',
        publishEndTime: ''
      },
      fields: [],
      orgList: [],
      resourceDetailVisible: false,
      fullscreenLoading: false
    }
  },
  components: {
    ResourceDetail
  },
  activated () {
    this.getOrg()
    this.getDataList()
    this.getFields()
  },
  methods: {
    resetForm () {
      this.$refs['dataForm']?.resetFields()
      this.dataForm.fieldIds = []
      this.dataForm.fieldId = ''
      this.dataForm.orgCode = ''
      this.dataForm.publishStartTime = null
      this.dataForm.publishEndTime = null
      this.dataForm.publishOrgCode = null
      this.getDataList()
    },
    queryBeforeHandle () {
      // 查询前操作
      this.dataForm.publishStartTime = this.dataForm.timeRange && this.dataForm.timeRange[0]
      this.dataForm.publishEndTime = this.dataForm.timeRange && this.dataForm.timeRange[1]
    },
    //获取所属区域
    getOrg (){
      this.$http({
        url: this.$http.adornUrl(`/admin/org/getOrgTree`),
        method: 'get',
        params: this.$http.adornParams()
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.orgList = this.getTreeData(data.obj)
        } else {
          this.orgList = []
        }
      })
    },
    // 下载文件
    exportAudit() {
      this.fullscreenLoading = true;
      this.$http({
        url: this.$http.adornUrl('/admin/zyz/resource/exportForAudit'),
        method: 'post',
        responseType: 'arraybuffer',
        data: this.$http.adornData({
          'key': this.dataForm.key,
          'type': this.dataForm.type,
          'auditStatus': this.dataForm.auditStatus,
          'publishOrgCode': this.dataForm.publishOrgCode,
          'publishStartTime': this.dataForm.timeRange != null ? this.dataForm.timeRange[0] : null,
          'publishEndTime': this.dataForm.timeRange != null ? this.dataForm.timeRange[1] : null,
          'fieldId': this.dataForm.fieldId
        })
      }).then(({ data }) => {
        if (data.code && data.code !== 0) {
          this.$message.error('导出失败')
        } else {
          this.fullscreenLoading = false
          let blob = new Blob([data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8' })
          let objectUrl = URL.createObjectURL(blob)
          let a = document.createElement('a')
          // window.location.href = objectUrl
          a.href = objectUrl
          a.download = '资源审核明细.xlsx'
          a.click()
          URL.revokeObjectURL(objectUrl)
          this.$message({
            type: 'success',
            message: '导出成功'
          })
        }
      })
    },
    getFields () {
      this.$http({
        url: this.$http.adornUrl('/admin/zyz/platform/belongFieldDict/cascade'),
        method: 'get'
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.fields = this.getTreeData(data.obj)
        } else {
          this.fields = []
        }
      })
    },
    // *处理点位分类最后children数组为空的状态
    getTreeData: function (data) {
      var that = this
      // 循环遍历json数据
      data.forEach(function (e) {
        if (!e.children || e.children.length < 1) {
          e.children = undefined
        } else {
          // children若不为空数组，则继续 递归调用 本方法
          that.getTreeData(e.children)
        }
      })
      return data
    },
    handleChange(value, type) {
      if (type === 'field') {
        this.dataForm.fieldId = value[value.length - 1]
      }
      if (type === 'org') {
        this.dataForm.publishOrgCode = value && value.length > 0 ? value[value.length - 1] : null
      }
    },
    resourceDetailHandler(resourceId) {
      this.resourceDetailVisible = true
      this.$nextTick(() => {
        this.$refs.resourceDetail.init(resourceId,'log')
      })
    },
    auditHandle(id, name, status) {
      if (!id && this.dataListSelections.length === 0) {
        this.$message.warning('未选中需要审核的需求记录！')
        return
      }
      if (!id) {
        let ids = this.dataListSelections.map(item => {
          return item.id
        })
        id = ids.join(',')
      }
      this.$confirm(`确定${status ? '审核通过' : '驳回'}` + (name ? '"' + name + '"' : '') + '需求？', '系统提示', {
        customClass: 'audit_pass_msg_box',
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        showInput: !status,
        inputPlaceholder: '请输入驳回意见……',
        inputType: 'textarea',
        closeOnClickModal: false
      }).then((val) => {
        console.info( '123213213',Array.isArray(id) ? id : [id])
        this.$http({
          url: this.$http.adornUrl('/admin/zyz/resource/audit'),
          method: 'get',
          params: this.$http.adornParams({
            'ids': id,
            'status': status,
            'remark': val.value
          })
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.$message.success(status ? '审核通过成功！' : '驳回成功！')
            this.query()
          } else {
            this.$message.error(status ? '审核通过失败！' : '驳回失败！' + data.msg)
          }
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '操作取消'
        });
      });
      setTimeout(() => {
        var content = document.getElementsByClassName('audit_pass_msg_box')[0].childNodes[1]
        content.style.paddingLeft = '60px'
        content.style.paddingRight = '60px'
        var submitBtn = document.getElementsByClassName('audit_pass_msg_box')[0].childNodes[2].childNodes[1]
        submitBtn.style.backgroundColor = '#F56C6C'
        submitBtn.style.borderColor = '#F56C6C'
      }, 50)
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
