<template>
  <div class="mod-config">
    <el-form :inline="true" :model="dataForm" ref="dataForm" @keyup.enter.native="queryPage()">
      <el-form-item label="关键字" prop="key" style="padding-right: 40px">
        <el-input v-model="dataForm.key" clearable placeholder="资源名称/联系人/联系电话" style="width: 120%"></el-input>
      </el-form-item>
      <el-form-item :label="'发布组织'" prop="publishOrgCodes">
        <el-cascader placeholder="请选择" v-model="dataForm.publishOrgCodes" :options="orgList"
                     :disabled="this.dataForm.id" :show-all-levels="false" clearable
                     :props="{ checkStrictly: true, value: 'code', label: 'name' }"
                     @change="(value) => { handleChange(value, 'org') }"></el-cascader>
      </el-form-item>
      <el-form-item label="资源类型" prop="type">
        <el-dict :code="'REQUIREMENT_TYPE'" v-model="dataForm.type"></el-dict>
      </el-form-item>
      <el-form-item label="所属领域" prop="fieldId">
        <el-cascader placeholder="请选择" v-model="dataForm.fieldIds" :options="fields"
                     @change="(value) => { handleChange(value, 'field') }" clearable
                     :props="{ checkStrictly: true, label: 'typeName', value: 'typeId' }"></el-cascader>
      </el-form-item>
      <el-form-item label="资源状态" prop="status">
        <el-dict :code="'resource_status'" v-model="dataForm.status"></el-dict>
      </el-form-item>
      <el-form-item label="发布时间" prop="publishTimeRange">
        <el-date-picker v-model="dataForm.publishTimeRange" clearable type="datetimerange"
                        value-format="yyyy-MM-dd HH:mm:ss" align="right" start-placeholder="开始时间" end-placeholder="结束时间"
                        :default-time="['00:00:00', '23:59:59']">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="资源提供时间" prop="timeRange">
        <el-date-picker v-model="dataForm.timeRange" clearable type="datetimerange"
                        value-format="yyyy-MM-dd HH:mm:ss" align="right" start-placeholder="开始时间" end-placeholder="结束时间"
                        :default-time="['00:00:00', '23:59:59']">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="上架状态" prop="autoStatus">
        <el-select v-model="dataForm.autoStatus" placeholder="请选择" clearable>
          <el-option v-for="item in [{ label: '上架', value: 'true' }, { label: '下架', value: 'false' }]"
                     :key="item.value" :label="item.label" :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
        <el-form-item  prop="self">
            <el-checkbox v-model="dataForm.self">本组织的</el-checkbox>
        </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-search" type="warning" @click="queryPage()">查询</el-button>
        <el-button @click="resetForm()" icon="el-icon-refresh-left">重置</el-button>
      </el-form-item>
    </el-form>
    <div class="f-s-c" style="padding: 15px 0;display: flex;justify-content:flex-end;align-items: center ">
      <div>
        <el-button icon="el-icon-plus" type="primary" @click="addOrUpdateHandle()">新增</el-button>
        <el-button icon="el-icon-download" type="success" v-loading.fullscreen.lock="fullscreenLoading" @click="exportResource()">全部导出
        </el-button>
        <el-button  v-if="isAuth('resource:batchautostatus')" icon="el-icon-monitor" type="primary" @click="updateAutoStatusByIds(null, false)" :disabled="dataListSelections.length <= 0"  >批量下架</el-button>
      </div>
    </div>

    <el-table :data="dataList" border v-loading="dataListLoading"      @selection-change="selectionChangeHandle"
              style="width: 100%;">
        <el-table-column type="selection" header-align="center" align="center" width="50"
                         :selectable="selectable"
        ></el-table-column>
      <el-table-column prop="name" header-align="center" align="center" min-width="250" label="资源名称" show-overflow-tooltip>
        <template slot-scope="scope">
          <a style="cursor: pointer" @click="resourceDetailHandler(scope.row.id)">{{ scope.row.name }}</a>
        </template>
      </el-table-column>
      <el-table-column prop="typeText" header-align="center" align="center" label="资源类型">
      </el-table-column>
      <el-table-column prop="belongField" header-align="center" align="center" min-width="180" label="所属领域"
                       show-overflow-tooltip>
        <template slot-scope="scope">
                    <span>{{
                        scope.row.belongFieldNameTop && scope.row.belongFieldNameEnd ? scope.row.belongFieldNameTop +
                            '-' + scope.row.belongFieldNameEnd : (scope.row.belongFieldNameTop ||
                            scope.row.belongFieldNameEnd)
                      }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="publishOrgName" header-align="center" align="center" min-width="180" label="发布组织"
                       show-overflow-tooltip>
      </el-table-column>
      <el-table-column prop="contactPerson" header-align="center" align="center" min-width="100" label="联系人">
      </el-table-column>
      <el-table-column prop="contactPhone" header-align="center" align="center" min-width="150" label="联系电话">
      </el-table-column>
      <el-table-column prop="publishTime" header-align="center" align="center" min-width="160" label="发布时间">
      </el-table-column>
      <el-table-column prop="timeRange" header-align="center" align="center" min-width="160" label="资源提供时间">
        <template slot-scope="scope">
          <span>{{
              (!scope.row.startTime || scope.row.startTime === '' || !scope.row.endTime || scope.row.endTime === '') ? '' : scope.row.startTime + '--' + scope.row.endTime
            }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="statusText" header-align="center" align="center" min-width="130" label="资源状态">
      </el-table-column>
      <el-table-column prop="appointmentNum" header-align="center" align="center" min-width="150" label="已对接次数">
        <template slot-scope="scope">
          <a v-if="scope.row.status === 'res_audit_success'" style="cursor: pointer" @click="getDockingActivity(scope.row.id)">{{ scope.row.hasAppointmentNum }}</a>
          <span v-else>{{ scope.row.hasAppointmentNum}}</span>
        </template>
      </el-table-column>
      <el-table-column prop="syncText" header-align="center" align="center" label="同步状态">
        <template slot-scope="scope">
          <!--          <div v-if="scope.row.isSyncText === '同步失败'" style="color: #f6070f;cursor:pointer">{{scope.row.isSyncText}}</div>-->
          <!--          <div v-if="scope.row.isSyncText === '待同步'" style="color: #eee98a;cursor:pointer">{{scope.row.isSyncText}}</div>-->
          <!--          <div v-if="scope.row.isSyncText === '已同步'" style="color: #03f303;cursor:pointer">{{scope.row.isSyncText}}</div>-->
          <el-popover trigger="hover" placement="top">
            <p style="text-align: center">
              {{
                '同步时间：' + (scope.row.syncTime ? scope.row.syncTime : '')
              }}<br>{{ scope.row.syncRemark }}</p>
            <div slot="reference" class="name-wrapper">
              <el-tag v-if="scope.row.sync === 'sync_failure'" type="danger">{{ scope.row.syncText }}</el-tag>
              <el-tag v-if="scope.row.sync === 'sync_wait'" type="warning">{{ scope.row.syncText }}</el-tag>
              <el-tag v-if="scope.row.sync === 'sync_success'" type="success">{{
                  scope.row.syncText
                }}
              </el-tag>
            </div>
          </el-popover>
        </template>
      </el-table-column>
      <el-table-column fixed="right" header-align="center" align="center" min-width="130" label="操作">
        <template slot-scope="scope">
          <el-button type="text" size="small" @click="addOrUpdateHandle(scope.row.id, true)">复制</el-button>
          <el-button
              v-if="scope.row.enable"
              type="text" size="small" @click="addOrUpdateHandle(scope.row.id, false)">修改
          </el-button>
          <el-button
              v-if="scope.row.enable"
              type="text" size="small" @click="render(scope.row.id)">提交
          </el-button>
          <el-button
              v-if="scope.row.enable"
              type="text" size="small" @click="deleteHandle(scope.row.id)">删除
          </el-button>
          <el-button v-if="scope.row.autoEnable" type="text" size="small"
                     @click="updateAutoStatus(scope.row.id, scope.row.autoStatus)">
            {{ !scope.row.autoStatus ? '上架' : '下架' }}
          </el-button>
          <el-button type="text" size="small" @click="syncHandle(scope.row.id)"
                     v-if="isAuth('resource:sync') && scope.row.syncEnable">同步
          </el-button>
          <el-button type="text" size="small" @click="adminUpdateHandle(scope.row.id)" v-if="isAuth('resource:adminUpdate') && scope.row.status !== 'res_draft' && scope.row.status !== 'res_wait_audit' && scope.row.status !== 'res_reject'">管理员修改</el-button>
          <el-button type="text" size="small" @click="adminSyncHandle(scope.row.id)" v-if="isAuth('resource:adminSync') && scope.row.status !== 'res_draft' && scope.row.status !== 'res_wait_audit' && scope.row.status !== 'res_reject'">管理员同步</el-button>
          <el-button type="text" size="small" v-if="scope.row.dockingZSQ"  @click="zsqDockingRecords(scope.row.id)">知社区对接记录</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination @size-change="sizeChangeHandle" @current-change="currentChangeHandle" :current-page="pageIndex"
                   :page-sizes="[10, 20, 50, 100]" :page-size="pageSize" :total="totalPage"
                   layout="total, sizes, prev, pager, next, jumper">
    </el-pagination>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"></add-or-update>
    <!-- 需求详情 -->
    <resource-detail v-if="resourceDetailVisible" ref="resourceDetail"></resource-detail>
    <!-- 需求详情 -->
    <resource-docking-list v-if="resourceDockingListVisible" ref="resourceDockingList"></resource-docking-list>
    <!-- 修改并同步 -->
    <resource-admin-update v-if="adminUpdateVisible" ref="adminUpdate"></resource-admin-update>
    <zsq-docking-records v-if="zsqDockingRecordsVisible" ref="zsqDockingRecords"></zsq-docking-records>
  </div>
</template>

<script>
import AddOrUpdate from './resource-add-or-update'
import ResourceDetail from './resource-detail'
import ResourceDockingList from "./resource-docking-list"
import ResourceAdminUpdate from './resource-admin-update.vue'
import ZsqDockingRecords from "./resource-zsq-docking-records.vue"

export default {
  data() {
    return {
      dataForm: {
        key: '',
        type: '',
        serviceObj: '',
        status: '',
        autoStatus: null,
        timeRange: [],
        publishTimeRange: [],
        publishOrgCodes: [],
        publishOrgCode: null,
        fieldIds: [],
        fieldId: null,
        self: true
      },
      orgList: [],
      fields: [],
      dataList: [],
      pageIndex: 1,
      pageSize: 10,
      totalPage: 0,
      dataListLoading: false,
      fullscreenLoading: false,
      dataListSelections: [],
      addOrUpdateVisible: false,
      resourceDetailVisible: false,
      resourceDockingListVisible: false,
      adminUpdateVisible: false,
      zsqDockingRecordsVisible: false,
    }
  },
  components: {
    ZsqDockingRecords,
    AddOrUpdate,
    ResourceDetail,
    ResourceDockingList,
    ResourceAdminUpdate
  },
  activated() {
    this.getOrg()
    this.queryPage()
    this.getFields()
  },
  methods: {
    queryPage() {
      this.pageIndex = 1
      this.getDataList()
    },
    // 获取数据列表
    getDataList() {
      this.dataListLoading = true
      this.$http({
        url: this.$http.adornUrl('/admin/zyz/resource/pagesForResource'),
        method: 'post',
        data: this.$http.adornData({
          'currentPage': this.pageIndex,
          'pageSize': this.pageSize,
          'key': this.dataForm.key,
          'status': this.dataForm.status,
          'type': this.dataForm.type,
          'autoStatus': this.dataForm.autoStatus,
          'publishOrgCode': this.dataForm.publishOrgCode,
          'startTime': this.dataForm.timeRange != null ? this.dataForm.timeRange[0] : null,
          'endTime': this.dataForm.timeRange != null ? this.dataForm.timeRange[1] : null,
          'publishStartTime': this.dataForm.publishTimeRange != null ? this.dataForm.publishTimeRange[0] : null,
          'publishEndTime': this.dataForm.publishTimeRange != null ? this.dataForm.publishTimeRange[1] : null,
          'fieldId': this.dataForm.fieldId,
          'self': this.dataForm.self
        })
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.dataList = data.obj.records
          this.totalPage = data.obj.total
          this.dataList.forEach(item => {
            //删除，提交，修改 条件判断
            var enable = item.own && (item.status === 'res_draft' || item.status === 'res_reject')
            item.enable = enable
            //判断
            var autoEnable = item.status === 'res_audit_success'
            item.autoEnable = autoEnable
            //同步判断
            var syncEnable = item.sync !== 'sync_success' && item.status === 'res_audit_success'
            item.syncEnable = syncEnable
          })
        } else {
          this.dataList = []
          this.totalPage = 0
        }
        this.dataListLoading = false
      })
    },
    // 重置
    resetForm() {
      this.$refs['dataForm']?.resetFields()
      this.dataForm.fieldId = null
      this.dataForm.fieldIds = []
      this.dataForm.publishOrgCode = null
      this.getDataList()
    },
    resourceDetailHandler(resourceId) {
      this.resourceDetailVisible = true
      this.$nextTick(() => {
        this.$refs.resourceDetail.init(resourceId, 'log')
      })
    },
    handleChange(value, type) {
      if (type === 'field') {
        this.dataForm.fieldId = value[value.length - 1]
      }
      if (type === 'org') {
        this.dataForm.publishOrgCode = value && value.length > 0 ? value[value.length - 1] : null
      }
    },
    // 每页数
    sizeChangeHandle(val) {
      this.pageSize = val
      this.pageIndex = 1
      this.getDataList()
    },
    // 当前页
    currentChangeHandle(val) {
      this.pageIndex = val
      this.getDataList()
    },
    // 多选
    selectionChangeHandle(val) {
      this.dataListSelections = val
    },
    // 新增 / 修改
    addOrUpdateHandle(id, isCopy) {
      this.addOrUpdateVisible = true
      this.$nextTick(() => {
        this.$refs.addOrUpdate.init(id, isCopy)
      })
    },
    // 修改并同步
    adminUpdateHandle(id) {
      this.adminUpdateVisible = true
      this.$nextTick(() => {
        this.$refs.adminUpdate.init(id)
      })
    },
    adminSyncHandle(id) {
      this.$confirm(`确定进行同步操作?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        closeOnClickModal: false
      }).then(() => {
        this.$http({
          url: this.$http.adornUrl('/admin/zyz/resource/reSyncForAdmin'),
          method: 'get',
          params: this.$http.adornParams({
            'id': id
          })
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.$message({
              message: '操作成功',
              type: 'success',
              duration: 1500,
              onClose: () => {
                this.getDataList()
              }
            })
          } else {
            this.$message.error(data.msg)
          }
        })
      })
    },
    // 提交
    render(id) {
      this.$confirm(`确定进行提交操作? 提交后无法撤回！`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        closeOnClickModal: false
      }).then(() => {
        this.$http({
          url: this.$http.adornUrl('/admin/zyz/resource/renderById'),
          method: 'get',
          params: this.$http.adornParams({
            'id': id
          })
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.$message({
              message: '操作成功',
              type: 'success',
              duration: 1500,
              onClose: () => {
                this.getDataList()
              }
            })
          } else {
            this.$message.error(data.msg)
          }
        })
      })
    },
    // 上下架
    updateAutoStatus(id, autoStatus) {
      this.$confirm(`确定进行[${!autoStatus ? '上架' : '下架'}]操作?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        closeOnClickModal: false
      }).then(() => {
        this.$http({
          url: this.$http.adornUrl('/admin/zyz/resource/updateAutoStatus'),
          method: 'get',
          params: this.$http.adornParams({
            'id': id
          })
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.$message({
              message: '操作成功',
              type: 'success',
              duration: 1500,
              onClose: () => {
                this.getDataList()
              }
            })
          } else {
            this.$message.error(data.msg)
          }
        })
      })
    },
      //批量上下架
    updateAutoStatusByIds(id, autoStatus) {
        if (!id && this.dataListSelections.length === 0) {
            this.$message.warning('未选中需要审核的申报记录！')
            return
        }
        if (!id) {
            let ids = this.dataListSelections.map(item => {
                return item.id
            })
            id = ids.join(',')
        }
        this.$confirm(`确定进行批量[${!autoStatus ? '上架' : '下架'}]操作?`, '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
            closeOnClickModal: false
        }).then(() => {
            this.$http({
                url: this.$http.adornUrl('/admin/zyz/resource/updateAutoStatusByIds'),
                method: 'get',
                params: this.$http.adornParams({
                    'ids': id,
                    'autoStatus':autoStatus
                })
            }).then(({data}) => {
                if (data && data.code === 0) {
                    this.$message({
                        message: '操作成功',
                        type: 'success',
                        duration: 1500,
                        onClose: () => {
                            this.getDataList()
                        }
                    })
                } else {
                    this.$message.error(data.msg)
                }
            })
        })
    },
    //获取所属区域
    getOrg() {
      this.$http({
        url: this.$http.adornUrl(`/admin/org/getOrgTree`),
        method: 'get',
        params: this.$http.adornParams()
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.orgList = this.getTreeData(data.obj)
        } else {
          this.orgList = []
        }
      })
    },
    getFields() {
      this.$http({
        url: this.$http.adornUrl('/admin/zyz/platform/belongFieldDict/cascade'),
        method: 'get'
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.fields = this.getTreeData(data.obj)
        } else {
          this.fields = []
        }
      })
    },
    // *处理点位分类最后children数组为空的状态
    getTreeData: function (data) {
      var that = this
      // 循环遍历json数据
      data.forEach(function (e) {
        if (!e.children || e.children.length < 1) {
          e.children = undefined
        } else {
          // children若不为空数组，则继续 递归调用 本方法
          that.getTreeData(e.children)
        }
      })
      return data
    },
    // 下载文件
    exportResource() {
      this.fullscreenLoading = true;
      this.$http({
        url: this.$http.adornUrl('/admin/zyz/resource/exportResource'),
        method: 'post',
        responseType: 'arraybuffer',
        data: this.$http.adornData({
          'key': this.dataForm.key,
          'status': this.dataForm.status,
          'type': this.dataForm.type,
          'autoStatus': this.dataForm.autoStatus,
          'publishOrgCode': this.dataForm.publishOrgCode,
          'startTime': this.dataForm.timeRange != null ? this.dataForm.timeRange[0] : null,
          'endTime': this.dataForm.timeRange != null ? this.dataForm.timeRange[1] : null,
          'publishStartTime': this.dataForm.publishTimeRange != null ? this.dataForm.publishTimeRange[0] : null,
          'publishEndTime': this.dataForm.publishTimeRange != null ? this.dataForm.publishTimeRange[1] : null,
          'fieldId': this.dataForm.fieldId,
          'self': this.dataForm.self
        })
      }).then(({data}) => {
        if (data.code && data.code !== 0) {
          this.$message.error('导出失败')
        } else {
          this.fullscreenLoading = false
          let blob = new Blob([data], {type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8'})
          let objectUrl = URL.createObjectURL(blob)
          let a = document.createElement('a')
          // window.location.href = objectUrl
          a.href = objectUrl
          a.download = '资源列表.xlsx'
          a.click()
          URL.revokeObjectURL(objectUrl)
          this.$message({
            type: 'success',
            message: '导出成功'
          })
        }
      })
    },
    // 删除
    deleteHandle(id) {
      var ids = id ? [id] : this.dataListSelections.map(item => {
        return item.id
      })
      this.$confirm(`确定进行[${id ? '删除' : '批量删除'}]操作?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        closeOnClickModal: false
      }).then(() => {
        this.$http({
          url: this.$http.adornUrl('/admin/zyz/resource/removeByIds'),
          method: 'get',
          params: this.$http.adornParams({
            'ids': ids.join(',')
          })
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.$message({
              message: '操作成功',
              type: 'success',
              duration: 1500,
              onClose: () => {
                this.getDataList()
              }
            })
          } else {
            this.$message.error(data.msg)
          }
        })
      })
    },
    // 同步
    syncHandle(id) {
      this.$confirm(`确定要进行同步嘛，请勿重复操作，否则可能会带来不可预知的后果`, '确认同步？', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        closeOnClickModal: false
      }).then(() => {
        this.dataListLoading = true
        this.$http({
          url: this.$http.adornUrl('/admin/zyz/platform-sync/syncResource'),
          method: 'post',
          params: this.$http.adornParams({
            'resourceId': id
          })
        }).then(({data}) => {
          this.dataListLoading = false
          if (data && data.code === 0) {
            this.$message({
              message: '请求同步成功，市平台返回：（' + data.obj.message + '）',
              type: data.obj.resultCode === 'SUCCESS' ? 'success' : 'warning',
              duration: 3000,
              onClose: () => {
                this.getDataList()
              }
            })
          } else {
            this.$message.error(data.msg)
          }
        })
      })
    },
    getDockingActivity(resourceId) {
      this.resourceDockingListVisible = true
      this.$nextTick(() => {
        this.$refs.resourceDockingList.init(resourceId)
      })
    },
    // 设置列表禁用
    selectable (row, index) {
        if (row.autoEnable && row.autoStatus) {
            return true
        } else {
            return false
        }
    },
      zsqDockingRecords(id) {
          this.zsqDockingRecordsVisible = true
          this.$nextTick(() => {
              this.$refs.zsqDockingRecords.init(id)
          })
      }

  }
}
</script>
