<template>
  <div>
    <el-dialog title="知社区对接记录" :close-on-click-modal="false" :visible.sync="visible" width="70%" @close="clearTimer">
      <el-table :data="dataList" border v-loading="dataListLoading" style="width: 100%;" max-height="600px">
        <el-table-column prop="dockingTime" header-align="center" align="center" label="对接时间"/>
        <el-table-column prop="dockingType" header-align="center" align="center" label="对接类型">
          <template slot-scope="scope">
            <el-tag v-if="scope.row.dockingType === 'push'" size="small" type="primary">推送</el-tag>
            <el-tag v-else-if="scope.row.dockingType === 'receive'" size="small" type="success">接收</el-tag>
            <el-tag v-else size="small" type="warning">未知</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="dockingOperation" header-align="center" align="center" label="操作类型">
          <template slot-scope="scope">
            <span v-if="scope.row.dockingOperation === 'CREATE'">资源创建</span>
            <span v-if="scope.row.dockingOperation === 'UPDATE'">资源编辑</span>
            <span v-if="scope.row.dockingOperation === 'DELETE'">资源删除</span>
            <span v-if="scope.row.dockingOperation === 'ON_OFF'">资源上/下架</span>
            <span v-if="scope.row.dockingOperation === 'AUDIT'">资源审核</span>
          </template>
        </el-table-column>
        <el-table-column prop="dockingStatus" header-align="center" align="center" label="对接状态">
          <template slot-scope="scope">
            <el-tag v-if="scope.row.dockingStatus === 0" size="small" type="warning">进行中</el-tag>
            <el-tag v-else-if="scope.row.dockingStatus === 1" size="small" type="success">成功</el-tag>
            <el-tag v-else-if="scope.row.dockingStatus === 2" size="small" type="danger">失败</el-tag>
            <el-tag v-else size="small" type="info">未知</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="retry" header-align="center" align="center" label="重试记录">
          <template slot-scope="scope">
            <el-tag v-if="scope.row.retry" size="small" type="warning">是</el-tag>
            <el-tag v-else size="small" type="info">否</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="dockingMsg" header-align="center" align="center" label="对接信息" show-overflow-tooltip min-width="200px"/>
        <el-table-column fixed="right" header-align="center" align="center" label="操作">
          <template slot-scope="scope">
            <el-button v-if="scope.$index === 0 && scope.row.dockingStatus === 2" type="text" size="small" @click="retry(scope.row.id)">重试</el-button>
            <el-button type="text" size="small" @click="detail(scope.row.id)">明细</el-button>
          </template>
        </el-table-column>
      </el-table>
      <span slot="footer" class="dialog-footer">
        <el-button @click="visible = false">关闭</el-button>
      </span>
    </el-dialog>
    <resource-zsq-docking-record-detail ref="resourceZsqDockingRecordDetail" v-if="resourceZsqDockingRecordDetailVisible"></resource-zsq-docking-record-detail>
  </div>
</template>

<script>
  import ResourceZsqDockingRecordDetail from './resource-zsq-docking-record-detail.vue'
  export default {
    components: {ResourceZsqDockingRecordDetail},
    data () {
      return {
        visible: false,
        dataList: [],
        dataListLoading: false,
        dataForm: {
          resourceId: null
        },
        resourceZsqDockingRecordDetailVisible: false,
        refreshTimer: null // 新增定时器变量
      }
    },
    methods: {
      init(id) {
        this.dataForm.resourceId = id || null
        this.visible = true
        this.getDataList()
        // 启动定时器
        this.startTimer()
      },
      getDataList() {
        this.dataListLoading = true
        this.$http({
          url: this.$http.adornUrl('/admin/zsq_docking_resource_record/getResourceDockingRecords'),
          method: 'get',
          params: this.$http.adornParams({
            "resourceId": this.dataForm.resourceId
          })
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.dataList = data.obj
          } else {
            this.$message.error('获取资源对接记录失败！')
          }
          this.dataListLoading = false
        })
      },
      retry(id) {
        this.$confirm('确定要重试该对接记录吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.$http({
            url: this.$http.adornUrl('/admin/zsq_docking_resource_record/retry'),
            method: 'get',
            params: this.$http.adornParams({
              "resourceId": this.dataForm.resourceId,
              "recordId": id
            })
          }).then(({data}) => {
            if (data && data.code === 0) {
              this.$message({
                type: 'success',
                message: '操作成功！'
              })
              this.getDataList()
            } else {
              this.$message.error('操作失败：' + data.msg)
            }
          })
        }).catch(() => {})
      },
      detail(id) {
        this.resourceZsqDockingRecordDetailVisible = true
        this.$nextTick(() => {
          this.$refs.resourceZsqDockingRecordDetail.init(id)
        })
      },
      // 新增方法：启动定时器
      startTimer() {
        this.clearTimer() // 先清除可能存在的旧定时器
        this.refreshTimer = setInterval(() => {
          this.getDataList()
        }, 5000)
      },
      // 新增方法：清除定时器
      clearTimer() {
        if (this.refreshTimer) {
          clearInterval(this.refreshTimer)
          this.refreshTimer = null
        }
      }
    },
    // 组件销毁时清除定时器
    beforeDestroy() {
      this.clearTimer()
    }
  }
</script>
