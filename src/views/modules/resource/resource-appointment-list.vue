<template>
  <div class="mod-role">
    <el-form :inline="true" ref="dataForm" :model="dataForm" @keyup.enter.native="getDataList()">
      <el-form-item label="关键字" prop="key" style="padding-right: 40px">
        <el-input v-model="dataForm.key" placeholder="资源名称/联系人/联系方式" clearable style="width: 120%"></el-input>
      </el-form-item>
      <el-form-item label="发布组织" prop="publishOrgCodes">
        <el-cascader
            placeholder="请选择"
            v-model="dataForm.publishOrgCodes"
            :options="orgList"
            :disabled="this.dataForm.id"
            :show-all-levels="false"
            clearable
            :props="{checkStrictly: true, value: 'code',label: 'name'}"
            @change="(value) => {handleChange(value, 'org')}"
        ></el-cascader>
      </el-form-item>
      <el-form-item label="资源类型" prop="type">
        <el-dict :code="'REQUIREMENT_TYPE'" v-model="dataForm.type"></el-dict>
      </el-form-item>
      <el-form-item label="资源提供时间" prop="timeRange">
        <el-date-picker
            v-model="dataForm.timeRange"
            clearable
            type="datetimerange"
            value-format="yyyy-MM-dd HH:mm:ss"
            align="right"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            :default-time="['00:00:00', '23:59:59']">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="所属领域" prop="fieldIds">
        <el-cascader
            placeholder="请选择"
            v-model="dataForm.fieldIds"
            :options="fields"
            @change="(value) => {handleChange(value, 'field')}"
            clearable
            :props="{checkStrictly: true, label: 'typeName', value: 'typeId'}"
        ></el-cascader>
      </el-form-item>
      <el-form-item>
        <el-button @click="getDataList()">查询</el-button>
        <el-button class="el-icon-refresh" type="warning" @click="resetForm()">重置</el-button>
      </el-form-item>
    </el-form>
    <div class="f-s-c" style="padding: 15px 0;display: flex; justify-content: flex-end">
      <div>
        <el-button :loading="exportLoading" v-if="isAuth('resource:appointment:export')" type="success"
                   @click="exportHandle()">导出
        </el-button>
      </div>
    </div>
    <el-table
        :data="dataList"
        border
        v-loading="dataListLoading"
        style="width: 100%;">
      <el-table-column
          prop="name"
          header-align="center"
          align="center"
          min-width="300"
          :show-overflow-tooltip="true"
          label="资源名称">
        <template slot-scope="scope">
          <a style="cursor: pointer" @click="resourceDetailHandler(scope.row.id)">{{ scope.row.name }}</a>
        </template>
      </el-table-column>
      <el-table-column
          prop="typeText"
          header-align="center"
          align="center"
          label="资源类型">
      </el-table-column>
      <el-table-column
          prop="belongField"
          header-align="center"
          show-overflow-tooltip
          align="center"
          min-width="180"
          label="所属领域">
        <template slot-scope="scope">
          <span>{{ scope.row.belongFieldNameTop && scope.row.belongFieldNameEnd ? scope.row.belongFieldNameTop + '-' + scope.row.belongFieldNameEnd : (scope.row.belongFieldNameTop || scope.row.belongFieldNameEnd) }}</span>
        </template>
      </el-table-column>
      <el-table-column
          prop="publishOrgName"
          header-align="center"
          min-width="150"
          :show-overflow-tooltip="true"
          align="center"
          label="发布组织">
      </el-table-column>
      <el-table-column
          prop="contactPerson"
          header-align="center"
          align="center"
          label="联系人">
      </el-table-column>
      <el-table-column
          prop="contactPhone"
          header-align="center"
          align="center"
          min-width="130"
          label="联系方式">
      </el-table-column>
      <el-table-column
          prop="timeRange"
          header-align="center"
          align="center"
          min-width="160"
          label="资源提供时间">
        <template slot-scope="scope">
          <span>{{ (!scope.row.startTime || scope.row.startTime === '' || !scope.row.endTime || scope.row.endTime === '') ? '' : scope.row.startTime + '--' + scope.row.endTime }}</span>
        </template>
      </el-table-column>
      <el-table-column
          prop="hasAppointmentNum"
          header-align="center"
          min-width="100"
          align="center"
          label="已预约次数">
      </el-table-column>
      <el-table-column
          prop="enableAppointTimes"
          header-align="center"
          min-width="110"
          align="center"
          label="剩余可约次数">
        <template slot-scope="scope">
          <span>{{ scope.row.appointmentNum - scope.row.hasAppointmentNum > 0 ? scope.row.appointmentNum - scope.row.hasAppointmentNum : 0 }}</span>
        </template>
      </el-table-column>
      <el-table-column
          fixed="right"
          header-align="center"
          align="center"
          label="操作">
        <template slot-scope="scope">
          <!--          <el-button v-if="isAuth('resource:appointment:calendar')" type="text" size="small" @click="resourceCalendar(scope.row.id)">资源日历</el-button>-->
          <el-button
              v-if="isAuth('resource:list:appointment') && scope.row.appointmentNum - scope.row.hasAppointmentNum > 0"
              type="text" size="small" @click="appointHandle(scope.row.id, scope.row.startTime, scope.row.endTime)">预约
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
        @size-change="sizeChangeHandle"
        @current-change="currentChangeHandle"
        :current-page="pageIndex"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pageSize"
        :total="totalPage"
        layout="total, sizes, prev, pager, next, jumper">
    </el-pagination>
    <!-- 需求详情 -->
    <resource-detail v-if="resourceDetailVisible" ref="resourceDetail"></resource-detail>
    <!-- 需求预约 -->
    <resource-appoint-add v-if="resourceAppointAddVisible" ref="resourceAppointAdd"
                          @refreshDataList="getDataList"></resource-appoint-add>
  </div>
</template>

<script>
import listMixin from '@/mixins/list-mixins'
import ResourceAppointAdd from './resource-appointment-add'
import ResourceDetail from './resource-detail'

export default {
  mixins: [listMixin],
  data() {
    return {
      mixinOptions: {
        dataUrl: '/admin/zyz/resource/pagesForAppoint',
        exportUrl: '/admin/zyz/resource/exportForAppoint',              // 导出接口，API地址
        exportFileName: '资源预约表'
      },
      dataForm: {
        key: null,
        publishOrgCodes: [],
        publishOrgCode: null,
        fieldIds: [],
        fieldId: null,
        type: null,
        timeRange: [],
        startTime: '',
        endTime: ''
      },
      fields: [],
      orgList: [],
      exportLoading: false,
      resourceDetailVisible: false,
      resourceAppointAddVisible: false
    }
  },
  components: {
    ResourceAppointAdd,
    ResourceDetail
  },
  activated() {
    this.getOrg()
    this.getDataList()
    this.getFields()
  },
  methods: {
    resetForm() {
      this.$refs['dataForm']?.resetFields()
      this.dataForm.fieldIds = []
      this.dataForm.fieldId = ''
      this.dataForm.orgCode = ''
      this.dataForm.startTime = null
      this.dataForm.endTime = null
      this.dataForm.publishOrgCode = null
      this.getDataList()
    },
    queryBeforeHandle() {
      // 查询前操作
      this.dataForm.startTime = this.dataForm.timeRange && this.dataForm.timeRange[0]
      this.dataForm.endTime = this.dataForm.timeRange && this.dataForm.timeRange[1]
    },
    //获取所属区域
    getOrg() {
      this.$http({
        url: this.$http.adornUrl(`/admin/org/getOrgTreeOfAll`),
        method: 'get',
        params: this.$http.adornParams()
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.orgList = this.getTreeData(data.obj)
        } else {
          this.orgList = []
        }
      })
    },
    getFields() {
      this.$http({
        url: this.$http.adornUrl('/admin/zyz/platform/belongFieldDict/cascade'),
        method: 'get'
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.fields = this.getTreeData(data.obj)
        } else {
          this.fields = []
        }
      })
    },
    // *处理点位分类最后children数组为空的状态
    getTreeData: function (data) {
      var that = this
      // 循环遍历json数据
      data.forEach(function (e) {
        if (!e.children || e.children.length < 1) {
          e.children = undefined
        } else {
          // children若不为空数组，则继续 递归调用 本方法
          that.getTreeData(e.children)
        }
      })
      return data
    },
    handleChange(value, type) {
      if (type === 'field') {
        this.dataForm.fieldId = value[value.length - 1]
      }
      if (type === 'org') {
        this.dataForm.publishOrgCode = value.join(',')
      }
    },
    resourceDetailHandler(resourceId) {
      this.resourceDetailVisible = true
      this.$nextTick(() => {
        this.$refs.resourceDetail.init(resourceId, 'appointment', true)
      })
    },
    appointHandle(resourceId, resourceStartTime, resourceEndTime) {
      // this.$http({
      //   url: this.$http.adornUrl('/admin/zyz/resource/appointment/validateAppointed'),
      //   method: 'get',
      //   params: this.$http.adornParams({
      //     'resourceId': resourceId
      //   })
      // }).then(({data}) => {
      //   if (data && data.code === 0) {
      //     if (data.obj === true) {
      this.resourceAppointAddVisible = true
      this.$nextTick(() => {
        this.$refs.resourceAppointAdd.init(resourceId, resourceStartTime, resourceEndTime)
      })
      //     } else {
      //       this.$message.error('您已预约过该资源，无法重复预约！')
      //     }
      //   } else {
      //     this.$message.error(data.msg)
      //   }
      // })
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
