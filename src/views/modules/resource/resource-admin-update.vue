<template>
  <el-dialog :title="!dataForm.id ? (initFromAct ? '资源创建' : '新增') : (initFromAct ? '资源编辑' : '修改')"
             :close-on-click-modal="false" append-to-body width="80%" :visible.sync="visible">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm"
             label-width="140px" style="margin-right: 40px">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="资源类型" prop="type">
            <el-dict :code="'REQUIREMENT_TYPE'" v-model="dataForm.type"></el-dict>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="资源名称" prop="name">
            <el-input v-model="dataForm.name" placeholder="资源名称"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="所属领域" prop="fieldIds">
            <el-cascader style="width: 100%" placeholder="请选择" v-model="dataForm.fieldIds" :options="fields"
                         @change="(value) => { handleChange(value) }" clearable
                         :props="{ label: 'typeName', value: 'typeId' }"></el-cascader>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="资源使用时间" prop="timeRange">
            <el-date-picker
                style="width: 100%"
                v-model="dataForm.timeRange"
                clearable
                type="datetimerange"
                value-format="yyyy-MM-dd HH:mm:ss"
                align="right"
                start-placeholder="开始时间"
                end-placeholder="结束时间"
                :default-time="['00:00:00', '23:59:59']">
            </el-date-picker>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="联系人" prop="contactPerson">
            <el-input v-model="dataForm.contactPerson" placeholder="联系人"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="联系电话" prop="contactPhone">
            <el-input v-model="dataForm.contactPhone" placeholder="联系电话"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item class="address-item" label="资源地址" prop="address">
            <el-input v-model="dataForm.address" placeholder="资源地址" clearable @clear="addressClear"/><el-button type="primary" @click="showMap">地图定位</el-button>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="资源详情" prop="detail">
            <el-input type="textarea" v-model="dataForm.detail" placeholder="资源详情" rows="5" maxlength="1000" show-word-limit></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20" v-if="dataForm.type === 'RT_video'">
        <el-col :span="24">
          <el-form-item label="视频链接" prop="videoUrl">
            <el-input v-model="dataForm.videoUrl" placeholder="视频链接" ></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="图片" prop="picture">
            <el-upload class="avatar-uploader" :action="this.$http.adornUrl(`/admin/oss/upload`)" :headers="myHeaders"
                       :data="{ serverCode: this.serverCode, media: false }" :show-file-list="false"
                       :on-success="handleAvatarSuccess" :before-upload="beforeAvatarUpload">
              <img v-if="dataForm.picture" :src="$http.adornAttachmentUrl(dataForm.picture)" class="avatar">
              <i v-else class="el-icon-plus avatar-uploader-icon"></i>
              <div slot="tip" class="el-upload__tip" style="color: red">尺寸建议600x1000像素，文件格式jpg、bmp或png，大小不超2M</div>
            </el-upload>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item prop="autoEnable">
            <el-checkbox v-model="dataForm.autoEnable">审核通过后立即上架</el-checkbox>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button :disabled="isDisabled" type="success" @click="dataFormSubmit()">保存</el-button>
    </span>
    <el-dialog class="map-dialog" title="地图" width="70%" :close-on-click-modal="false" :visible.sync="mapVisible" :modal-append-to-body="false" :append-to-body="true">
      <AMapInfo :mapVisible.sync="mapVisible" :address.sync="dataForm.address" :latitude.sync="dataForm.latitude" :longitude.sync="dataForm.longitude"/>
    </el-dialog>
  </el-dialog>
</template>

<script>
import Vue from 'vue'
import _ from 'lodash'
import {is8lPhone, isMobile} from "@/utils/validate";
import AMapInfo from "@/components/map/a-map-info";

export default {
  data() {
    const validateContactPhone = (rule, value, callback) => {
      if (!isMobile(value) && !is8lPhone(value)) {
        callback(new Error('联系电话须为手机号或区号+8位座机号例如051288888888或0512-88888888'))
      } else {
        callback()
      }
    }
    const validateName = (rule, value, callback) => {
        let reg = /^[\u4e00-\u9fa5a-zA-Z0-9]+$/
        if (value === '') {
            callback(new Error("名称不能为空"))
        } else {
            if (!reg.test(value)) {
                callback(new Error('名称不能含有特殊字符（只能包含数字/字母/中文）'))
            } else {
                callback()
            }
        }
    }
    const validateAddress = (rule, value, callback) => {
        if (!this.dataForm.longitude || this.dataForm.longitude === '' || !this.dataForm.latitude || this.dataForm.latitude === '') {
            callback(new Error('需要资源地址经纬度，请通过地图定位选择地址！'))}
        else {callback()}
    }
    return {
      visible: false,
      isDisabled: false,
      serverCode: 'LocalServer',
      myHeaders: {Authorization: Vue.cookie.get('Authorization')},
      uploadData: {serverCode: 'LocalServer', media: false},
      fields: [],
      dataForm: {},
      initForm: {
        id: null,
        version: null,
        name: '',
        contactPerson: this.$store.state.user.nickName,
        contactPhone: this.$store.state.user.phone,
        address: null,
        longitude: null,
        latitude: null,
        type: '',
        belongFieldTop: '',
        belongFieldEnd: '',
        startTime: '',
        endTime: '',
        picture: '',
        detail: '',
        videoUrl: '',
        fieldIds: [],
        fieldId: null,
        timeRange: [],
        autoEnable: true
      },
      dataRule: {
        contactPerson: [
          {required: true, message: '联系人不能为空', trigger: 'blur'}
        ],
        contactPhone: [
          {required: true, message: '联系电话不能为空', trigger: 'blur'},
          {validator: validateContactPhone, trigger: 'blur'}
        ],
        type: [
          {required: true, message: '资源类型不能为空', trigger: 'blur'}
        ],
        fieldIds: [
          {required: true, message: '所属领域不能为空', trigger: 'blur'}
        ],
        picture: [
          {required: true, message: '图片不能为空', trigger: 'blur'}
        ],
        detail: [
          {required: true, message: '资源详情不能为空', trigger: 'blur'}
        ],
        videoUrl: [
          {required: true, message: '视频链接不能为空', trigger: 'blur'}
        ],
        timeRange: [
          {required: true, message: '资源提供时间不能为空', trigger: 'blur'}
        ],
        name: [
          {required: true, message: '资源名称不能为空', trigger: 'blur'},
          {validator: validateName, trigger: 'change'}
        ],
        address: [
          {required: true, message: '资源地址不能为空', trigger: 'change'},
          {validator: validateAddress, trigger: 'change'}
        ]
      },
      mapVisible: false
    }
  },
  components: {AMapInfo},
  methods: {
    init(id, isCopy) {
      this.dataForm = _.cloneDeep(this.initForm)
      this.dataForm.id = id || null
      this.visible = true
      this.getFields()
      this.$nextTick(() => {
        this.$refs['dataForm']?.resetFields()
        if (this.dataForm.id) {
          this.$http({
            url: this.$http.adornUrl(`/admin/zyz/resource/getResourceForUpdateSync`),
            method: 'get',
            params: this.$http.adornParams({id: this.dataForm.id})
          }).then(({data}) => {
            if (data && data.code === 0) {
              this.dataForm = data.obj
              this.dataForm.fieldIds = [data.obj.belongFieldTop, data.obj.belongFieldEnd]
              this.$set(this.dataForm, 'timeRange', [data.obj.startTime, data.obj.endTime])
            }
          })
        }
      })
    },
    getFields() {
      this.$http({
        url: this.$http.adornUrl('/admin/zyz/platform/belongFieldDict/cascade'),
        method: 'get'
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.fields = this.getTreeData(data.obj)
        } else {
          this.fields = []
        }
      })
    },
    // *处理点位分类最后children数组为空的状态
    getTreeData: function (data) {
      var that = this
      // 循环遍历json数据
      data.forEach(function (e) {
        if (!e.children || e.children.length < 1) {
          e.children = undefined
        } else {
          // children若不为空数组，则继续 递归调用 本方法
          that.getTreeData(e.children)
        }
      })
      return data
    },
    handleChange(value, type) {
      this.dataForm.fieldId = value[value.length - 1]
    },
    handleAvatarSuccess(res, file) {
      if (res.success) {
        this.$message({
          message: '操作成功',
          type: 'success',
          duration: 1500
        })
        this.dataForm.picture = res.obj.path
      } else {
        this.$message.error('上传失败')
      }
    },
    beforeAvatarUpload: function (file) {
      let isAccept = ['image/jpg', 'image/jpeg', 'image/png', 'image/bmp'].indexOf(file.type) !== -1
      let isLt2M = file.size / 1024 / 1024 < 2

      if (!isAccept) {
        this.$message.error('上传图片只能是图片!')
      }
      if (!isLt2M) {
        this.$message.error('上传文件大小不能超过 2MB!')
      }
      return isAccept && isLt2M
    },
    dataFormSubmit() {
      this.dataForm.startTime = this.dataForm.timeRange && this.dataForm.timeRange[0]
      this.dataForm.endTime = this.dataForm.timeRange && this.dataForm.timeRange[1]
      this.dataForm.belongFieldTop = this.dataForm.fieldIds[0]
      this.dataForm.belongFieldEnd = this.dataForm.fieldIds[1]
      let method = '/admin/zyz/resource/updateForReSync'
      this.$confirm(`确定保存本次修改? 提交后无法撤回！`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        closeOnClickModal: false
      }).then(() => {
        this.submitFun(method, 'refreshDataList')
      })
    },
    submitFun(method, callback) {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.isDisabled = true
          this.$http({
            url: this.$http.adornUrl(method),
            method: 'post',
            data: this.$http.adornData(this.dataForm)
          }).then(({data}) => {
            if (data && data.code === 0) {
              this.visible = false
              this.isDisabled = false
              this.$emit(callback)
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 1500
              })
            } else {
              this.$message.error(data.msg)
              this.isDisabled = false
            }
          })
        }
      })
    },
    showMap() {
      this.mapVisible = true
    },
    addressClear() {
      this.dataForm.address = null
      this.dataForm.longitude = null
      this.dataForm.latitude = null
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .map-dialog {
  .el-dialog__body {
    padding: 15px 20px 30px;
  }
}
::v-deep .address-item {
  .el-form-item__content {
    display: flex;
    flex-direction: row;

    .el-button {
      margin-left: 15px;
    }
  }
}
</style>
