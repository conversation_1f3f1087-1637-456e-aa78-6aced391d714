<template>
  <el-dialog :title="'系统提示'" :close-on-click-modal="false" width="40%" append-to-body :visible.sync="visible">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" label-width="120px">
      <el-form-item label="预约确认时间" prop="timeAppointmentRange">
        <el-date-picker v-model="dataForm.timeAppointmentRange" clearable type="datetimerange"
          value-format="yyyy-MM-dd HH:mm:ss" align="right" start-placeholder="开始时间" end-placeholder="结束时间"
          :default-time="['00:00:00', '23:59:59']">
        </el-date-picker>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="passAudit()">通过审核</el-button>
    </span>
  </el-dialog>
</template>

<script>
import moment from 'moment'

export default {
  data() {
    return {
      visible: false,
      dataForm: {
        id: null,
        version: null,
        timeAppointmentRange: []
      },
      dataRule: {
        timeAppointmentRange: [
          { required: true, message: '预约确定时间不能为空', trigger: 'blur' }
        ]
      },
      remarksError: null
    }
  },
  components: {
    moment
  },
  methods: {
    init(id, auditStartTime, auditEndTime) {
      this.dataForm.id = id || null
      this.dataForm.timeAppointmentRange = [auditStartTime,auditEndTime]
      console.log(this.dataForm.timeAppointmentRange)
      this.visible = true
    },
    passAudit() {
      this.$http({
        url: this.$http.adornUrl('/admin/zyz/resource/appointment/audit'),
        method: 'post',
        data: this.$http.adornData({
          'ids': [this.dataForm.id],
          'auditStatus': true,
          'auditStartTime': this.dataForm.timeAppointmentRange != null ? this.dataForm.timeAppointmentRange[0] : null,
          'auditEndTime': this.dataForm.timeAppointmentRange != null ? this.dataForm.timeAppointmentRange[1] : null,
        })
      }).then(({ data }) => {
        if (data && data.code === 0) {
          this.$message({
            message: '操作成功',
            type: 'success',
            duration: 1500,
            onClose: () => {
              this.visible = false
              this.$emit('refreshDataList')
            }
          })
        } else {
          this.$message.error(data.msg)
        }
      })
    }
  }
}
</script>
