<template>
  <div>
    <el-dialog title="资源详情" :close-on-click-modal="false" :visible.sync="visible" width="80%">
      <el-form :model="dataForm" ref="dataForm" label-width="130px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="资源名称" prop="name">
              <el-input style="width: 100%" disabled v-model="dataForm.name" placeholder="资源名称"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="资源类型" prop="type">
              <el-dict style="width: 100%" :code="'REQUIREMENT_TYPE'" disabled v-model="dataForm.type"></el-dict>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="所属领域" prop="fieldIds">
              <el-cascader  style="width: 100%" disabled placeholder="请选择" v-model="dataForm.fieldIds" :options="fields"
                @change="(value) => { handleChange(value, 'field') }" clearable
                :props="{ label: 'typeName', value: 'typeId' }"></el-cascader>
            </el-form-item>
          </el-col>
          <el-col :span="12" v-if="!dockingCreate || dockingCreate === false">
            <el-form-item label="资源状态" prop="status">
              <el-dict style="width: 100%" :code="'resource_status'" disabled v-model="dataForm.status"></el-dict>
            </el-form-item>
          </el-col>
          <el-col :span="12" v-if="dockingCreate && dockingCreate === true">
            <el-form-item label="资源使用时间" prop="timeRange">
              <el-date-picker
                  disabled
                  style="width: 100%"
                  v-model="dataForm.timeRange"
                  clearable
                  type="datetimerange"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  align="right"
                  start-placeholder="开始时间"
                  end-placeholder="结束时间"
                  :default-time="['00:00:00', '23:59:59']">
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20" v-if="!dockingCreate || dockingCreate === false">
          <el-col :span="12">
            <el-form-item label="资源开始时间" prop="startTime">
              <el-date-picker style="width: 100%" v-model="dataForm.startTime" disabled value-format="yyyy-MM-dd HH:mm:ss" type="datetime"
                placeholder="资源开始时间">
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="资源截止时间" prop="endTime">
              <el-date-picker style="width: 100%" v-model="dataForm.endTime" disabled value-format="yyyy-MM-dd HH:mm:ss" type="datetime"
                placeholder="资源截止时间">
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="预约次数" prop="appointmentNum">
              <el-input-number disabled v-model="dataForm.appointmentNum" :min="0" label="预约次数"></el-input-number>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="同一时间多次预约" prop="allowAppointmentSameTime">
              <el-radio-group disabled v-model="dataForm.allowAppointmentSameTime">
                <el-radio :label=true>是</el-radio>
                <el-radio :label=false>否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="联系人" prop="contactPerson">
              <el-input style="width: 100%" v-model="dataForm.contactPerson" disabled placeholder="联系人"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="联系电话" prop="contactPhone">
              <el-input style="width: 100%" v-model="dataForm.contactPhone" disabled placeholder="联系电话"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="资源地址" prop="address">
              <el-input style="width:100%" disabled v-model="dataForm.address" placeholder="资源地址"/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="经纬度" prop="longitudeAndLatitude">
              <el-input style="width:100%" disabled v-model="dataForm.longitudeAndLatitude" controls-position="right"/>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="资源详情" prop="detail">
              <el-input style="width: 100%" type="textarea" v-model="dataForm.detail" disabled placeholder="资源详情" rows="5"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20" v-if="dataForm.type === 'RT_video'">
          <el-col :span="24">
            <el-form-item label="视频链接" prop="videoUrl">
              <el-input style="width: 100%" v-model="dataForm.videoUrl" disabled placeholder="视频链接"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="图片" prop="picture">
              <el-image
                  :src="$http.adornAttachmentUrl(dataForm.picture)"
                  :preview-src-list="[$http.adornAttachmentUrl(dataForm.picture)]"
                  class="avatar">
              </el-image>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-col :span="24" v-if="!dockingCreate || dockingCreate === false">
              <el-form-item label="上下架状态" prop="autoStatus">
                <el-radio-group v-model="dataForm.autoStatus" disabled>
                  <el-radio :label=false>下架</el-radio>
                  <el-radio :label=true>上架</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col :span="24" v-if="!dockingCreate || dockingCreate === false">
              <el-form-item label="已约次数" prop="hasAppointmentNum">
                <el-input-number disabled v-model="dataForm.hasAppointmentNum" :min="0" label="预约次数"></el-input-number>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item prop="autoEnable" >
                <el-checkbox v-model="dataForm.autoEnable" disabled>审核通过后立即上架</el-checkbox>
              </el-form-item>
            </el-col>
          </el-col>
        </el-row>
<!--        <el-row :gutter="20">-->
<!--          <el-col :span="12">-->
<!--            <el-form-item label="图片" prop="picture">-->
<!--              <el-image-->
<!--                  :src="$http.adornAttachmentUrl(dataForm.picture)"-->
<!--                  :preview-src-list="[$http.adornAttachmentUrl(dataForm.picture)]"-->
<!--                  class="avatar"-->
<!--              >-->
<!--              </el-image>-->
<!--            </el-form-item>-->
<!--          </el-col>-->
<!--        </el-row>-->
<!--        <el-row :gutter="20" v-if="dockingCreate && dockingCreate === true">-->
<!--          <el-col :span="12">-->
<!--            <el-form-item prop="autoEnable" >-->
<!--              <el-checkbox v-model="dataForm.autoEnable" disabled>审核通过后立即上架</el-checkbox>-->
<!--            </el-form-item>-->
<!--          </el-col>-->
<!--        </el-row>-->
      </el-form>
      <el-tabs v-model="activeName" type="card" ref="tabs" v-if="!dockingCreate || dockingCreate === false">
        <el-tab-pane  label="日志记录" name="log">
          <el-table :data="logList" border style="width: 100%;">
            <el-table-column type="index" align="center" label="序号" width="50">
            </el-table-column>
            <el-table-column align="center" label="日志记录">
              <el-table-column prop="operateTypeText" header-align="center" width="180" :show-overflow-tooltip="true"
                align="center" label="操作类型">
              </el-table-column>
              <el-table-column prop="operatorName" header-align="center" align="center" width="160" label="操作人">
              </el-table-column>
              <el-table-column prop="operatorOrgName" header-align="center" align="center" width="200" label="所在组织">
              </el-table-column>
              <el-table-column prop="operateTime" header-align="center" align="center" width="180" label="操作时间">
              </el-table-column>
              <el-table-column prop="remark" header-align="center" align="center" label="操作结果">
              </el-table-column>
            </el-table-column>
          </el-table>
        </el-tab-pane>
<!--        <el-tab-pane label="已预约记录" name="appointment">-->
<!--          <el-table :data="appointmentList" border style="width: 100%;">-->
<!--            <el-table-column type="index" align="center" label="序号" width="50">-->
<!--            </el-table-column>-->
<!--            <el-table-column align="center" label="已约记录">-->
<!--              <el-table-column prop="orgName" header-align="center" width="180" :show-overflow-tooltip="true"-->
<!--                align="center" label="预约组织">-->
<!--              </el-table-column>-->
<!--              <el-table-column prop="applyTime" header-align="center" align="center" width="160" label="申请时间">-->
<!--              </el-table-column>-->
<!--              <el-table-column prop="contactPerson" header-align="center" align="center" width="150" label="联系人">-->
<!--              </el-table-column>-->
<!--              <el-table-column prop="contactPhone" header-align="center" align="center" width="160" label="联系电话">-->
<!--              </el-table-column>-->
<!--              <el-table-column prop="timeRange" header-align="center" align="center" label="预约确定时间">-->
<!--                <template slot-scope="scope">-->
<!--                  <span>{{(!scope.row.auditStartTime || scope.row.auditStartTime === '' || !scope.row.auditEndTime || scope.row.auditEndTime === '') ? '' : scope.row.auditStartTime + '&#45;&#45;' + scope.row.auditEndTime}}</span>-->
<!--                </template>-->
<!--              </el-table-column>-->
<!--            </el-table-column>-->
<!--          </el-table>-->
<!--        </el-tab-pane>-->
      </el-tabs>
      <span slot="footer" class="dialog-footer">
        <el-button @click="visible = false">关闭</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
export default {
  data() {
    return {
      visible: false,
      logList: [],
      appointmentList: [],
      dockingCreate: null,
      activeName: '',
      fields: [],
      isShow: false,
      dataForm: {
        id: null,
        version: null,
        name: '',
        status: '',
        publishOrgCode: '',
        publishOrgName: '',
        teamPublish: '',
        teamId: '',
        contactPerson: this.$store.state.user.nickName,
        contactPhone: this.$store.state.user.phone,
        address: null,
        longitude: null,
        latitude: null,
        longitudeAndLatitude: null,
        type: '',
        belongFieldTop: '',
        belongFieldNameTop: '',
        belongFieldEnd: '',
        belongFieldNameEnd: '',
        appointmentNum: '',
        hasAppointmentNum: '',
        startTime: '',
        endTime: '',
        picture: '',
        detail: '',
        videoUrl: '',
        auditStatus: '',
        auditOrgCode: '',
        auditRemark: '',
        allowAppointmentSameTime: '',
        fieldIds: [],
        fieldId: null,
        timeRange: []
      }
    }
  },
  methods: {
    init(id, activeName, isShow) {
      this.dataForm.id = id || null
      this.activeName = activeName
      this.isShow = isShow
      this.dockingCreate = false
      this.getLogs()
      this.getFields()
      // this.getAppointment()
      this.hideTabs()
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm']?.resetFields()
        if (this.dataForm.id) {
          this.$http({
            url: this.$http.adornUrl(`/admin/zyz/resource`),
            method: 'get',
            params: this.$http.adornParams({ id: this.dataForm.id })
          }).then(({ data }) => {
            if (data && data.code === 0) {
              this.dataForm = data.obj
              this.dataForm.fieldIds = [data.obj.belongFieldTop, data.obj.belongFieldEnd]
              this.dataForm.longitudeAndLatitude = data.obj.longitude + ',' + data.obj.latitude
            }
          })
        }
      })
    },
    initFromAct(id) {
      this.dataForm.id = id || null
      this.activeName = null
      this.isShow = null
      this.dockingCreate = true
      this.getFields()
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm']?.resetFields()
        if (this.dataForm.id) {
          this.$http({
            url: this.$http.adornUrl(`/admin/zyz/resource_temp`),
            method: 'get',
            params: this.$http.adornParams({id: this.dataForm.id})
          }).then(({data}) => {
            if (data && data.code === 0) {
              this.dataForm = data.obj
              this.dataForm.fieldIds = [data.obj.belongFieldTop, data.obj.belongFieldEnd]
              this.$set(this.dataForm, 'timeRange', [data.obj.startTime, data.obj.endTime])
              this.dataForm.longitudeAndLatitude = data.obj.longitude + ',' + data.obj.latitude
            }
          })
        }
      })
    },
    hideTabs() {
      if (this.isShow) {
        this.$nextTick(() => {
          this.$refs.tabs.$children[0].$refs.tabs[0].style.display = "none";
        })
      }
  },
  //获取日志
  getLogs() {
    this.$http({
      url: this.$http.adornUrl(`/admin/zyz/resource/log/getLogsByResourceId`),
      method: 'get',
      params: this.$http.adornParams({ resourceId: this.dataForm.id })
    }).then(({ data }) => {
      if (data && data.code === 0) {
        this.logList = data.obj
      } else {
        this.logList = []
      }
    })
  },
  //获取预约记录
  // getAppointment() {
  //   this.$http({
  //     url: this.$http.adornUrl(`/admin/zyz/resource/appointment/getAppointmentByResourceId`),
  //     method: 'get',
  //     params: this.$http.adornParams({ resourceId: this.dataForm.id })
  //   }).then(({ data }) => {
  //     if (data && data.code === 0) {
  //       this.appointmentList = data.obj
  //     } else {
  //       this.appointmentList = []
  //     }
  //   })
  // },
  getFields() {
    this.$http({
      url: this.$http.adornUrl('/admin/zyz/platform/belongFieldDict/cascade'),
      method: 'get'
    }).then(({ data }) => {
      if (data && data.code === 0) {
        this.fields = this.getTreeData(data.obj)
      } else {
        this.fields = []
      }
    })
  },
  // *处理点位分类最后children数组为空的状态
  getTreeData: function (data) {
    var that = this
    // 循环遍历json数据
    data.forEach(function (e) {
      if (!e.children || e.children.length < 1) {
        e.children = undefined
      } else {
        // children若不为空数组，则继续 递归调用 本方法
        that.getTreeData(e.children)
      }
    })
    return data
  }
}
}
</script>
<style lang="scss"></style>
