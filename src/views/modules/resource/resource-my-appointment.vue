<template>
  <div class="mod-role">
    <el-form :inline="true" ref="dataForm" :model="dataForm" @keyup.enter.native="getDataList()">
      <el-form-item label="关键字" prop="key" style="padding-right: 40px">
        <el-input v-model="dataForm.key" placeholder="资源名称/联系人/联系电话" clearable style="width: 120%"></el-input>
      </el-form-item>
      <el-form-item label="发布组织" prop="publishOrgCodes">
        <el-cascader placeholder="请选择" v-model="dataForm.publishOrgCodes" :options="orgList"
                     :disabled="this.dataForm.id" :show-all-levels="false" clearable
                     :props="{checkStrictly: true, value: 'code',label: 'name'}"
                     @change="(value) => {handleChange(value, 'org')}"></el-cascader>
      </el-form-item>
      <el-form-item label="资源类型" prop="type">
        <el-dict :code="'REQUIREMENT_TYPE'" v-model="dataForm.type"></el-dict>
      </el-form-item>
      <el-form-item label="所属领域" prop="fieldIds">
        <el-cascader placeholder="请选择" v-model="dataForm.fieldIds" :options="fields"
                     @change="(value) => {handleChange(value, 'field')}" clearable
                     :props="{checkStrictly: true, label: 'typeName', value: 'typeId'}"></el-cascader>
      </el-form-item>
      <el-form-item label="服务对象" prop="serviceObj">
        <el-dict :code="'SERVICE_OBJECT'" v-model="dataForm.serviceObj"></el-dict>
      </el-form-item>
      <el-form-item label="资源预约时间" prop="timeRange">
        <el-date-picker v-model="dataForm.timeRange" clearable type="datetimerange"
                        value-format="yyyy-MM-dd HH:mm:ss" align="right" start-placeholder="开始时间" end-placeholder="结束时间"
                        :default-time="['00:00:00', '23:59:59']">
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button @click="getDataList()">查询</el-button>
        <el-button class="el-icon-refresh" type="warning" @click="resetForm()">重置</el-button>
      </el-form-item>
    </el-form>
    <div class="f-s-c" style="padding: 15px 0;display: flex; justify-content: flex-end">
      <div>
        <el-button v-if="isAuth('resource-my-appointment:list:export')" type="success"
                   v-loading.fullscreen.lock="exportLoading" @click="exportHandle()">
          导出
        </el-button>
      </div>
    </div>
    <el-table :data="dataList" border v-loading="dataListLoading" style="width: 100%;">
      <el-table-column prop="name" header-align="center" align="center" min-width="250" :show-overflow-tooltip="true"
                       label="资源名称">
        <template slot-scope="scope">
          <a style="cursor: pointer"
             @click="resourceDetailHandler(scope.row.appointmentId)">{{ scope.row.name }}</a>
        </template>
      </el-table-column>
      <el-table-column prop="typeText" header-align="center" align="center" label="资源类型">
      </el-table-column>
      <el-table-column prop="belongField" header-align="center" align="center" min-width="180" label="所属领域"
                       show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{
              scope.row.belongFieldNameTop && scope.row.belongFieldNameEnd ? scope.row.belongFieldNameTop + '-' + scope.row.belongFieldNameEnd : (scope.row.belongFieldNameTop || scope.row.belongFieldNameEnd)
            }}</span>
        </template>
        yy
      </el-table-column>
      <el-table-column prop="publishOrgName" header-align="center" min-width="150" :show-overflow-tooltip="true"
                       align="center" label="发布组织">
      </el-table-column>
      <el-table-column prop="contactPerson" header-align="center" align="center" label="联系人">
      </el-table-column>
      <el-table-column prop="contactPhone" header-align="center" align="center" min-width="130" label="联系方式">
      </el-table-column>
      <el-table-column prop="orgName" header-align="center" min-width="150" :show-overflow-tooltip="true"
                       align="center" label="预约组织">
      </el-table-column>
      <el-table-column prop="timeRange" header-align="center" align="center" min-width="160" label="预约时间">
        <template slot-scope="scope">
          <span>{{
              (!scope.row.appointmentStartTime || scope.row.appointmentStartTime === '' || !scope.row.appointmentEndTime || scope.row.appointmentEndTime === '') ? '' : scope.row.appointmentStartTime + '--' + scope.row.appointmentEndTime
            }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="auditEndTime" header-align="center" min-width="160" align="center" label="确认预约时间">
        <template slot-scope="scope">
          <span>{{
              (!scope.row.auditStartTime || scope.row.auditStartTime === '' || !scope.row.auditEndTime || scope.row.auditEndTime === '') ? '' : scope.row.auditStartTime + '--' + scope.row.auditEndTime
            }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="statusText" header-align="center" min-width="120" align="center" label="审核状态">
      </el-table-column>
<!--      <el-table-column prop="linkActivityName" header-align="center" min-width="180" :show-overflow-tooltip="true"-->
<!--                       align="center" label="关联活动">-->
<!--        <template slot-scope="scope">-->
<!--          <a style="cursor: pointer"-->
<!--             @click="activityDetailHandler(scope.row.linkActivityId)">{{ scope.row.linkActivityName }}</a>-->
<!--        </template>-->
<!--      </el-table-column>-->
<!--      <el-table-column prop="activityStatusText" header-align="center" align="center" label="活动状态">-->
<!--      </el-table-column>-->
      <el-table-column fixed="right" header-align="center" align="center" label="操作" min-width="150">
        <template slot-scope="scope">
          <el-button
              v-if="scope.row.auditStatus === 'res_appointment_audit_success' && (!scope.row.linkActivityId || scope.row.linkActivityId === '')"
              type="text" size="small" @click="cancelAppointment(scope.row.id,scope.row.appointmentId)">取消预约
          </el-button>
<!--          <el-button-->
<!--              v-if="isAuth('resource-my-appointment:list:create_activity') && !(scope.row.linkActivityId && scope.row.linkActivityId !== '') && scope.row.auditStatus === 'res_appointment_audit_success'"-->
<!--              type="text" size="small"-->
<!--              @click="activityCreateHandler(scope.row)">创建活动-->
<!--          </el-button>-->
          <el-button type="text" size="small" v-if="scope.row.activityStatus === 'act_audit_success'"
                     @click="remarkHandle(scope.row.appointmentId,scope.row.linkActivityName)">评价
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination @size-change="sizeChangeHandle" @current-change="currentChangeHandle" :current-page="pageIndex"
                   :page-sizes="[10, 20, 50, 100]" :page-size="pageSize" :total="totalPage"
                   layout="total, sizes, prev, pager, next, jumper">
    </el-pagination>
    <!-- 活动详情 -->
    <activity-detail v-if="activityDetailVisible" ref="activityDetail"></activity-detail>
    <!-- 活动创建 -->
    <activity-create v-if="activityCreateVisible" ref="activityCreate" @refreshDataList="query"></activity-create>
    <!-- 需求详情 -->
    <resource-detail v-if="resourceDetailVisible" ref="resourceDetail"></resource-detail>
    <!-- 评价 -->
    <remark v-if="remarkVisible" ref="remark" @refreshDataList="getDataList"></remark>
  </div>
</template>

<script>
import ActivityDetail from '../activity/activity-detail'
import ResourceDetail from './resource-appointment-detail.vue'
import ActivityCreate from '../activity/activity-add-or-update'
import listMixin from '@/mixins/list-mixins'
import Remark from './res-evaluate-remark'

export default {
  mixins: [listMixin],
  data() {
    return {
      mixinOptions: {
        dataUrl: '/admin/zyz/resource/pagesForMyAppointment',
        exportUrl: '/admin/zyz/resource/exportForMyAppointment', // 导出接口，API地址
        exportFileName: '我对接的资源列表'
      },
      dataForm: {
        key: null,
        publishOrgCodes: [],
        publishOrgCode: null,
        fieldIds: [],
        fieldId: null,
        type: null,
        serviceObj: null,
        timeRange: [],
        startTime: '',
        endTime: ''
      },
      fields: [],
      orgList: [],
      activityDetailVisible: false,
      resourceDetailVisible: false,
      activityCreateVisible: false,
      exportLoading: false,
      remarkVisible: false
    }
  },
  components: {
    ActivityDetail,
    ResourceDetail,
    ActivityCreate,
    Remark
  },
  activated() {
    this.getOrg()
    this.getDataList()
    this.getFields()
  },
  methods: {
    resetForm() {
      this.$refs['dataForm']?.resetFields()
      this.dataForm.fieldIds = []
      this.dataForm.fieldId = ''
      this.dataForm.orgCode = ''
      this.dataForm.startTime = null
      this.dataForm.endTime = null
      this.dataForm.publishOrgCode = null
      this.getDataList()
    },
    queryBeforeHandle() {
      // 查询前操作
      this.dataForm.startTime = this.dataForm.timeRange && this.dataForm.timeRange[0]
      this.dataForm.endTime = this.dataForm.timeRange && this.dataForm.timeRange[1]
      this.dataForm.publishOrgCode = this.dataForm.publishOrgCodes.join(',')
    },
    //获取所属区域
    getOrg() {
      this.$http({
        url: this.$http.adornUrl(`/admin/org/getOrgTreeOfAll`),
        method: 'get',
        params: this.$http.adornParams()
      }).then(({
                 data
               }) => {
        if (data && data.code === 0) {
          this.orgList = this.getTreeData(data.obj)
        } else {
          this.orgList = []
        }
      })
    },
    getFields() {
      this.$http({
        url: this.$http.adornUrl('/admin/zyz/platform/belongFieldDict/cascade'),
        method: 'get'
      }).then(({
                 data
               }) => {
        if (data && data.code === 0) {
          this.fields = this.getTreeData(data.obj)
        } else {
          this.fields = []
        }
      })
    },
    // *处理点位分类最后children数组为空的状态
    getTreeData: function (data) {
      var that = this
      // 循环遍历json数据
      data.forEach(function (e) {
        if (!e.children || e.children.length < 1) {
          e.children = undefined
        } else {
          // children若不为空数组，则继续 递归调用 本方法
          that.getTreeData(e.children)
        }
      })
      return data
    },
    handleChange(value, type) {
      if (type === 'field') {
        this.dataForm.fieldId = value[value.length - 1]
      }
      if (type === 'org') {
        this.dataForm.publishOrgCode = value[value.length - 1]
      }
    },
    // 评价
    remarkHandle(id, linkActivityName) {
      this.remarkVisible = true
      this.$nextTick(() => {
        this.$refs.remark.init(id, linkActivityName)
      })
    },
    // 取消预约
    cancelAppointment(id, appointmentId) {
      this.$confirm(`确定要进行取消预约吗，请谨慎操作；确定进行取消预约操作?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        closeOnClickModal: false
      }).then(() => {
        this.$http({
          url: this.$http.adornUrl('/admin/zyz/resource/cancelAppointment'),
          method: 'post',
          params: this.$http.adornParams({
            'id': id,
            'appointmentId': appointmentId
          })
        }).then(({
                   data
                 }) => {
          if (data && data.code === 0) {
            this.$message({
              message: '操作成功',
              type: 'success',
              duration: 1500,
              onClose: () => {
                this.getDataList()
              }
            })
          } else {
            this.$message.error(data.msg)
          }
        })
      })
    },
    activityDetailHandler(activityId) {
      this.activityDetailVisible = true
      this.$nextTick(() => {
        this.$refs.activityDetail.init(activityId)
      })
    },
    // activityCreateHandler(row) {
    //   this.activityCreateVisible = true
    //   this.$nextTick(() => {
    //     this.$refs.activityCreate.initFromReqOrRes(row, 'res')
    //   })
    // },
    resourceDetailHandler(appointmentId) {
      this.resourceDetailVisible = true
      this.$nextTick(() => {
        this.$refs.resourceDetail.init(appointmentId)
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.docking_msg_box {
  .el-message-box__content {
    padding: 0px;
  }
}

#dockingForm ::v-deep .el-form-item__error {
  top: 26px;
  font-size: 8px;
}
</style>
