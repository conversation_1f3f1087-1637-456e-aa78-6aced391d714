<template>
  <div class="mod-role">
    <el-form :inline="true" ref="dataForm" :model="dataForm" @keyup.enter.native="getDataList()">
      <el-form-item label="关键字" prop="key" style="padding-right: 40px">
        <el-input v-model="dataForm.key" placeholder="资源名称/联系人/联系方式" clearable style="width: 120%"></el-input>
      </el-form-item>
      <el-form-item v-if="!dataForm.needAudit" label="审核状态" prop="auditStatus">
        <el-select v-model="dataForm.auditStatus" placeholder="请选择" clearable>
          <el-option
              v-for="item in [{ label: '待审核', value: 'res_appointment_wait_audit' }, { label: '审核通过', value: 'res_appointment_audit_success' }, { label: '驳回', value: 'res_appointment_reject' }]"
              :key="item.value" :label="item.label" :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="预约组织" prop="orgCodes">
        <el-cascader placeholder="请选择" v-model="dataForm.orgCodes" :options="orgList" :disabled="this.dataForm.id"
                     :show-all-levels="false" clearable :props="{ checkStrictly: true, value: 'code', label: 'name' }"
                     @change="(value) => { handleChange(value, 'org') }"></el-cascader>
      </el-form-item>
      <el-form-item label="资源类型" prop="type">
        <el-dict :code="'REQUIREMENT_TYPE'" v-model="dataForm.type"></el-dict>
      </el-form-item>
      <el-form-item label="所属领域" prop="fieldIds">
        <el-cascader placeholder="请选择" v-model="dataForm.fieldIds" :options="fields"
                     @change="(value) => { handleChange(value, 'field') }" clearable
                     :props="{ checkStrictly: true, label: 'typeName', value: 'typeId' }"></el-cascader>
      </el-form-item>
      <el-form-item label="预约时间" prop="timeRange">
        <el-date-picker v-model="dataForm.timeRange" clearable type="datetimerange" value-format="yyyy-MM-dd HH:mm:ss"
                        align="right" start-placeholder="开始时间" end-placeholder="结束时间"
                        :default-time="['00:00:00', '23:59:59']">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="确定预约时间" prop="auditTimeRange">
        <el-date-picker v-model="dataForm.auditTimeRange" clearable type="datetimerange"
                        value-format="yyyy-MM-dd HH:mm:ss" align="right" start-placeholder="开始时间" end-placeholder="结束时间"
                        :default-time="['00:00:00', '23:59:59']">
        </el-date-picker>
      </el-form-item>
      <el-form-item  prop="needAudit">
        <el-checkbox v-model="dataForm.needAudit">需要我审核的</el-checkbox>
      </el-form-item>
      <el-form-item>
        <el-button @click="getDataList()">查询</el-button>
        <el-button class="el-icon-refresh" type="warning" @click="resetForm()">重置</el-button>
      </el-form-item>
    </el-form>
    <div class="f-s-c" style="padding: 15px 0;display: flex; justify-content: flex-end">
      <div>
        <el-button v-if="isAuth('resource:appointment_audit:export')" type="success"
                   v-loading.fullscreen.lock="fullscreenLoading" @click="exportAudit()">全部导出
        </el-button>
        <el-button :disabled="dataListSelections.length <= 0" v-if="isAuth('resource:appointment:passBatch')"
                   type="primary"
                   @click="auditHandle(null, null, true)">批量通过
        </el-button>
      </div>
    </div>
    <el-table :data="dataList" border v-loading="dataListLoading" @selection-change="selectionChangeHandle"
              style="width: 100%;">
      <el-table-column type="selection" header-align="center" align="center"
                       :selectable="(row, index) => { return row.auditStatus === 'res_appointment_wait_audit' }" width="40">
      </el-table-column>
      <el-table-column type="index" label="序号" align="center" width="50">
      </el-table-column>
      <el-table-column prop="resourceName" header-align="center" align="center" min-width="250"
                       :show-overflow-tooltip="true"
                       label="资源名称">
        <template slot-scope="scope">
          <a style="cursor: pointer" @click="resourceDetailHandler(scope.row.id)">{{ scope.row.resourceName }}</a>
        </template>
      </el-table-column>
      <el-table-column prop="resTypeText" header-align="center" align="center" label="资源类型">
      </el-table-column>
      <el-table-column prop="belongField" header-align="center" align="center" min-width="180" label="所属领域" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{
              scope.row.resFiledNameTop && scope.row.resFiledNameEnd ? scope.row.resFiledNameTop + '-' +
                  scope.row.resFiledNameEnd : (scope.row.resFiledNameTop || scope.row.resFiledNameEnd)
            }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="orgName" header-align="center" min-width="150" :show-overflow-tooltip="true" align="center"
                       label="预约组织">
      </el-table-column>
      <el-table-column prop="contactPerson" header-align="center" align="center" label="预约人">
      </el-table-column>
      <el-table-column prop="contactPhone" header-align="center" align="center" min-width="130" label="联系方式">
      </el-table-column>
      <el-table-column prop="applyTime" header-align="center" min-width="180" align="center" label="申请时间">
      </el-table-column>
      <el-table-column prop="timeRange" header-align="center" align="center" min-width="160" label="预约时间">
        <template slot-scope="scope">
          <span>{{ (!scope.row.appointmentStartTime || scope.row.appointmentStartTime === '' || !scope.row.appointmentEndTime || scope.row.appointmentEndTime === '') ? '' : scope.row.appointmentStartTime + '--' + scope.row.appointmentEndTime }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="timeRange" header-align="center" align="center" min-width="160" label="确定预约时间">
        <template slot-scope="scope">
          <span>{{ (!scope.row.auditStartTime || scope.row.auditStartTime === '' || !scope.row.auditEndTime || scope.row.auditEndTime === '') ? '' : scope.row.auditStartTime + '--' + scope.row.auditEndTime }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="auditStatus" header-align="center" :fixed="isAuth('resource:list:audit') ? false : 'right'"
                       align="center" label="审核状态">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.auditStatus === 'res_appointment_wait_audit'" type="warning">待审核</el-tag>
          <el-tag v-else-if="scope.row.auditStatus === 'res_appointment_audit_success'" type="success">审核通过</el-tag>
          <el-tag v-else type="danger">驳回</el-tag>
        </template>
      </el-table-column>
      <el-table-column v-if="isAuth('resource:appointment:audit')" fixed="right" header-align="center" align="center"
                       min-width="100px" label="操作">
        <template slot-scope="scope">
          <el-button v-if="scope.row.auditStatus === 'res_appointment_wait_audit'" type="text" size="small"
                     @click="auditPassHandle(scope.row.id, scope.row.appointmentStartTime, scope.row.appointmentEndTime)">
            通过
          </el-button>
          <el-button v-if="scope.row.auditStatus === 'res_appointment_wait_audit'" type="text" size="small"
                     @click="auditHandle(scope.row.id, scope.row.resourceName, false)">驳回
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination @size-change="sizeChangeHandle" @current-change="currentChangeHandle" :current-page="pageIndex"
                   :page-sizes="[10, 20, 50, 100]" :page-size="pageSize" :total="totalPage"
                   layout="total, sizes, prev, pager, next, jumper">
    </el-pagination>
    <!-- 需求详情 -->
    <resource-detail v-if="resourceDetailVisible" ref="resourceDetail"></resource-detail>
    <!-- 通过审核 -->
    <audit-pass v-if="auditPassVisible" ref="auditPass" @refreshDataList="getDataList"></audit-pass>
  </div>
</template>

<script>
import listMixin from '@/mixins/list-mixins'
import ResourceDetail from './appointment-audit-detail'
import AuditPass from './appointment-audit-pass'

export default {
  mixins: [listMixin],
  data() {
    return {
      mixinOptions: {
        dataUrl: '/admin/zyz/resource/appointment/pagesForAudit'
      },
      dataForm: {
        key: null,
        orgCodes: [],
        orgCode: null,
        fieldIds: [],
        fieldId: null,
        type: null,
        auditStatus: 'res_appointment_wait_audit',
        timeRange: [],
        auditTimeRange: [],
        auditStartTime: '',
        auditEndTime: '',
        startTime: '',
        needAudit: true,
        endTime: ''
      },
      fields: [],
      orgList: [],
      fullscreenLoading: false,
      resourceDetailVisible: false,
      auditPassVisible: false
    }
  },
  components: {
    ResourceDetail,
    AuditPass
  },
  activated() {
    this.getOrg()
    this.getDataList()
    this.getFields()
  },
  methods: {
    resetForm() {
      this.$refs['dataForm']?.resetFields()
      this.dataForm.fieldIds = []
      this.dataForm.fieldId = ''
      this.dataForm.orgCode = ''
      this.dataForm.publishStartTime = null
      this.dataForm.publishEndTime = null
      this.dataForm.orgCode = null
      this.getDataList()
    },
    queryBeforeHandle() {
      // 查询前操作
      this.dataForm.auditStartTime = this.dataForm.auditTimeRange[0]
      this.dataForm.auditEndTime = this.dataForm.auditTimeRange[1]
      this.dataForm.startTime = this.dataForm.timeRange && this.dataForm.timeRange[0]
      this.dataForm.endTime = this.dataForm.timeRange && this.dataForm.timeRange[1]
    },
    //获取所属区域
    getOrg() {
      this.$http({
        url: this.$http.adornUrl(`/admin/org/getOrgTreeOfAll`),
        method: 'get',
        params: this.$http.adornParams()
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.orgList = this.getTreeData(data.obj)
        } else {
          this.orgList = []
        }
      })
    },
    getFields() {
      this.$http({
        url: this.$http.adornUrl('/admin/zyz/platform/belongFieldDict/cascade'),
        method: 'get'
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.fields = this.getTreeData(data.obj)
        } else {
          this.fields = []
        }
      })
    },
    // 下载文件
    exportAudit() {
      this.fullscreenLoading = true;
      this.$http({
        url: this.$http.adornUrl('/admin/zyz/resource/appointment/exportForAppointmentAudit'),
        method: 'post',
        responseType: 'arraybuffer',
        data: this.$http.adornData({
          'key': this.dataForm.key,
          'type': this.dataForm.type,
          'orgCode': this.dataForm.orgCode,
          'auditStatus': this.dataForm.auditStatus,
          'startTime': this.dataForm.timeRange != null ? this.dataForm.timeRange[0] : null,
          'endTime': this.dataForm.timeRange != null ? this.dataForm.timeRange[1] : null,
          'auditStartTime': this.dataForm.auditTimeRange != null ? this.dataForm.auditTimeRange[0] : null,
          'auditEndTime': this.dataForm.auditTimeRange != null ? this.dataForm.auditTimeRange[1] : null,
          'fieldId': this.dataForm.fieldId
        })
      }).then(({data}) => {
        if (data.code && data.code !== 0) {
          this.$message.error('导出失败')
        } else {
          this.fullscreenLoading = false
          let blob = new Blob([data], {type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8'})
          let objectUrl = URL.createObjectURL(blob)
          let a = document.createElement('a')
          // window.location.href = objectUrl
          a.href = objectUrl
          a.download = '资源预约审核明细.xlsx'
          a.click()
          URL.revokeObjectURL(objectUrl)
          this.$message({
            type: 'success',
            message: '导出成功'
          })
        }
      })
    },
    // *处理点位分类最后children数组为空的状态
    getTreeData: function (data) {
      var that = this
      // 循环遍历json数据
      data.forEach(function (e) {
        if (!e.children || e.children.length < 1) {
          e.children = undefined
        } else {
          // children若不为空数组，则继续 递归调用 本方法
          that.getTreeData(e.children)
        }
      })
      return data
    },
    handleChange(value, type) {
      if (type === 'field') {
        this.dataForm.fieldId = value[value.length - 1]
      }
      if (type === 'org') {
        this.dataForm.orgCode = value.join(',')
      }
    },
    resourceDetailHandler(resourceId) {
      this.resourceDetailVisible = true
      this.$nextTick(() => {
        this.$refs.resourceDetail.init(resourceId)
      })
    },
    auditPassHandle(id, auditStartTime, auditEndTime) {
      this.auditPassVisible = true
      this.$nextTick(() => {
        this.$refs.auditPass.init(id, auditStartTime, auditEndTime)
      })
    },
    auditHandle(id, name, status) {
      if (!id && this.dataListSelections.length === 0) {
        this.$message.warning('未选中需要审核的需求记录！')
        return
      }
      if (!id) {
        let ids = this.dataListSelections.map(item => {
          return item.id
        })
        id = ids
      }
      this.$confirm(`确定${status ? '批量审核通过' : '驳回'}` + (name ? '"' + name + '"' : '') + `预约？${status ? '预约时间无法更改' : ''}`, '系统提示', {
        customClass: 'audit_pass_msg_box',
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        showInput: !status,
        inputPlaceholder: '请输入驳回意见……',
        inputType: 'textarea',
        closeOnClickModal: false
      }).then((val) => {
        this.$http({
          url: this.$http.adornUrl('/admin/zyz/resource/appointment/audit'),
          method: 'post',
          data: this.$http.adornData({
            'ids': Array.isArray(id) ? id : [id],
            'auditStatus': status,
            'remark': val.value
          })
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.$message.success(status ? '审核通过成功！' : '驳回成功！')
            this.query()
          } else {
            this.$message.error(status ? '审核通过失败！' : '驳回失败！' + data.msg)
          }
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '操作取消'
        });
      });
      setTimeout(() => {
        var content = document.getElementsByClassName('audit_pass_msg_box')[0].childNodes[1]
        content.style.paddingLeft = '60px'
        content.style.paddingRight = '60px'
        var submitBtn = document.getElementsByClassName('audit_pass_msg_box')[0].childNodes[2].childNodes[1]
        submitBtn.style.backgroundColor = '#F56C6C'
        submitBtn.style.borderColor = '#F56C6C'
      }, 50)
    }
  }
}
</script>

<style lang="scss" scoped></style>
