<template>
  <div>
    <el-dialog title="资源对接列表" :close-on-click-modal="false" :visible.sync="visible" width="80%">
      <el-form :inline="true" :model="dataForm" ref="dataForm" @keyup.enter.native="queryPage()">
        <el-form-item label="活动年月" prop="actMonths">
          <el-date-picker
              style="width: 400px"
              type="months"
              v-model="dataForm.actMonths"
              value-format="yyyy-MM-01"
              placeholder="选择活动年月，可多选">
          </el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-button icon="el-icon-search" type="warning" @click="queryPage()">查询</el-button>
          <el-button @click="resetForm()" icon="el-icon-refresh-left">重置</el-button>
        </el-form-item>
      </el-form>
      <div class="f-s-c" style="padding: 15px 0;display: flex;justify-content:flex-end;align-items: center ">
        <div>
          <el-button icon="el-icon-download" type="success" v-loading.fullscreen.lock="fullscreenLoading" @click="exportActivity()">全部导出
          </el-button>
        </div>
      </div>
      <el-table :data="dataList" border v-loading="dataListLoading" style="width: 100%;">
        <el-table-column prop="name" header-align="center" align="center" min-width="150" label="活动名称"/>
        <el-table-column prop="timeRange" header-align="center" align="center" min-width="180" label="活动起止时间"/>
        <el-table-column prop="activityStatusText" header-align="center" align="center" label="活动状态"/>
        <el-table-column prop="recruitTargetsText" header-align="center" align="center" min-width="160" label="招募对象"/>
        <el-table-column prop="belongField" header-align="center" align="center" min-width="200" label="所属领域">
          <template slot-scope="scope">
          <span>{{scope.row.belongFieldNameTop && scope.row.belongFieldNameEnd ?
              scope.row.belongFieldNameTop + '-' + scope.row.belongFieldNameEnd :
              (scope.row.belongFieldNameTop || scope.row.belongFieldNameEnd)}}</span>
          </template>
        </el-table-column>
        <el-table-column prop="publishOrgName" header-align="center" align="center" min-width="150" show-overflow-tooltip label="发布单位"/>
        <el-table-column prop="contactPerson" header-align="center" align="center" min-width="140" label="联系人"/>
        <el-table-column prop="contactPhone" header-align="center" align="center" min-width="160" label="联系电话"/>
        <el-table-column prop="submitTime" header-align="center" align="center" min-width="160" label="提交时间"/>
      </el-table>
      <el-pagination @size-change="sizeChangeHandle"
                     @current-change="currentChangeHandle"
                     :current-page="pageIndex"
                     :page-sizes="[10, 20, 50, 100]"
                     :page-size="pageSize"
                     :total="totalPage"
                     layout="total, sizes, prev, pager, next, jumper"/>
      <span slot="footer" class="dialog-footer"><el-button @click="visible = false">关闭</el-button></span>
    </el-dialog>
  </div>
</template>
<script>
import moment from 'moment'
import _ from 'lodash'

export default {
  data() {
    return {
      visible: false,
      dataForm: {
        resourceId: null,
        actMonths: []
      },
      dataList: [],
      pageIndex: 1,
      pageSize: 10,
      totalPage: 0,
      dataListLoading: false,
      fullscreenLoading: false,
    }
  },
  methods: {
    init(id) {
      this.dataForm.resourceId = id || null
      this.dataForm.actMonths = []
      this.visible = true
      this.$nextTick(() => {
        this.queryPage()
      })
    },
    queryPage() {
      this.pageIndex = 1
      this.getDataList()
    },
    resetForm() {
      this.$refs['dataForm']?.resetFields()
      this.getDataList()
    },
    // 获取数据列表
    getDataList() {
      this.dataListLoading = true
      let actMonthStartEndTime = []
      if (this.dataForm.actMonths && this.dataForm.actMonths.length > 0) {
        _.forEach(this.dataForm.actMonths, it => {
          let startEndTime = {startTime: null, endTime: null}
          startEndTime.startTime = moment(it).startOf('month').format('YYYY-MM-DD') + ' 00:00:00'
          startEndTime.endTime = moment(it).endOf('month').format('YYYY-MM-DD') + ' 23:59:59'
          actMonthStartEndTime.push(startEndTime)
        })
      }
      this.$http({
        url: this.$http.adornUrl('/admin/zyz/activity/pagesForActivity'),
        method: 'post',
        data: this.$http.adornData({
          'currentPage': this.pageIndex,
          'pageSize': this.pageSize,
          'auditStatus': 'act_audit_success',
          'resourceId': this.dataForm.resourceId,
          'actMonths': actMonthStartEndTime,
        })
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.dataList = data.obj.records
          _.forEach(this.dataList, item => {
            item.timeRange = moment(item.startTime).format('YYYY-MM-DD') + '--' + moment(item.endTime).format('YYYY-MM-DD')
          })
          this.totalPage = data.obj.total
        } else {
          this.dataList = []
          this.totalPage = 0
        }
        this.dataListLoading = false
      })
    },
    // 每页数
    sizeChangeHandle(val) {
      this.pageSize = val
      this.pageIndex = 1
      this.getDataList()
    },
    // 当前页
    currentChangeHandle(val) {
      this.pageIndex = val
      this.getDataList()
    },
    // 导出
    exportActivity() {
      this.fullscreenLoading = true
      let actMonthStartEndTime = []
      if (this.dataForm.actMonths && this.dataForm.actMonths.length > 0) {
        _.forEach(this.dataForm.actMonths, it => {
          let startEndTime = {startTime: null, endTime: null}
          startEndTime.startTime = moment(it).startOf('month').format('YYYY-MM-DD') + ' 00:00:00'
          startEndTime.endTime = moment(it).endOf('month').format('YYYY-MM-DD') + ' 23:59:59'
          actMonthStartEndTime.push(startEndTime)
        })
      }
      this.$http({
        url: this.$http.adornUrl('/admin/zyz/activity/exportActivity'),
        method: 'post',
        responseType: 'arraybuffer',
        data: this.$http.adornData({
          'auditStatus': 'act_audit_success',
          'resourceId': this.dataForm.resourceId,
          'actMonths': actMonthStartEndTime,
        })
      }).then(({data}) => {
        if (data.code && data.code !== 0) {
          this.$message.error('导出失败')
        } else {
          this.fullscreenLoading = false
          let blob = new Blob([data], {type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8'})
          let objectUrl = URL.createObjectURL(blob)
          let a = document.createElement('a')
          // window.location.href = objectUrl
          a.href = objectUrl
          a.download = '活动明细.xlsx'
          a.click()
          URL.revokeObjectURL(objectUrl)
          this.$message({
            type: 'success',
            message: '导出成功'
          })
        }
      })
    }
  }
}
</script>
