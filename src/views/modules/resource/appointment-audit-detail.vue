<template>
  <div>
    <el-dialog title="预约审核详情" :close-on-click-modal="false" :visible.sync="visible" width="90%">
      <el-form :model="dataForm" ref="dataForm" label-width="130px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="资源名称" prop="name">
              <el-input disabled v-model="dataForm.resourceName" placeholder="资源名称"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="预约组织" prop="type">
              <el-input disabled v-model="dataForm.orgName" placeholder="预约组织"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="预约时间" prop="timeRange">
              <el-date-picker v-model="dataForm.timeRange" clearable type="datetimerange" disabled
                value-format="yyyy-MM-dd HH:mm:ss" align="right" start-placeholder="开始时间" end-placeholder="结束时间"
                :default-time="['00:00:00', '23:59:59']">
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="确定预约时间" prop="auditTimeRange">
              <el-date-picker v-model="dataForm.auditTimeRange"  type="datetimerange" disabled
                value-format="yyyy-MM-dd HH:mm:ss" align="right" start-placeholder="开始时间" end-placeholder="结束时间"
                :default-time="['00:00:00', '23:59:59']">
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="活动地址" prop="name">
              <el-input disabled v-model="dataForm.activityAddress" placeholder="活动地址"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="联系人" prop="contactPerson">
              <el-input v-model="dataForm.contactPerson" disabled placeholder="联系人"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="联系电话" prop="contactPhone">
              <el-input v-model="dataForm.contactPhone" disabled placeholder="联系电话"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="17">
            <el-form-item label="预约说明" prop="appointmentRemark">
              <el-input type="textarea" v-model="dataForm.appointmentRemark" disabled placeholder="预约说明"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="17">
            <el-form-item label="审核状态" prop="auditStatus">
              <el-select disabled v-model="dataForm.auditStatus" placeholder="请选择" clearable>
                <el-option
                  v-for="item in [{ label: '待审核', value: 'res_appointment_wait_audit' }, { label: '审核通过', value: 'res_appointment_audit_success' }, { label: '驳回', value: 'res_appointment_reject' }]"
                  :key="item.value" :label="item.label" :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="17">
            <el-form-item v-if="dataForm.auditStatus === 'res_reject'" label="驳回理由" prop="appointmentRemark">
              <el-input  type="textarea" v-model="dataForm.auditRemark" disabled placeholder="驳回理由"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="visible = false">关闭</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
export default {
  data() {
    return {
      visible: false,
      dataList: [],
      fields: [],
      dataForm: {
        id: null,
        version: null,
        resourceName: null,
        orgName: null,
        auditTimeRange: [],
        timeRange: [],
        activityAddress: '',
        contactPerson: '',
        contactPhone: '',
        appointmentRemark: '',
        auditStatus: '',
        auditRemark: ''
      }
    }
  },
  methods: {
    init(id) {
      this.dataForm.id = id || null
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm']?.resetFields()
        if (this.dataForm.id) {
          this.$http({
            url: this.$http.adornUrl(`/admin/zyz/resource/appointment`),
            method: 'get',
            params: this.$http.adornParams({ id: this.dataForm.id })
          }).then(({ data }) => {
            if (data && data.code === 0) {
              this.dataForm = data.obj
              // this.dataForm.timeRange = [data.obj.appointmentStartTime, data.obj.appointmentEndTime]
              this.$set(this.dataForm, 'timeRange', [data.obj.appointmentStartTime, data.obj.appointmentEndTime])
              if(data.obj.auditStartTime !=null && data.obj.auditEndTime !=null) {
                this.$set(this.dataForm, 'auditTimeRange', [data.obj.auditStartTime, data.obj.auditEndTime])
              }
              // this.dataForm.auditTimeRange = [data.obj.auditStartTime, data.obj.auditEndTime]
            }
          })
        }
      })
    }
  }
}
</script>
<style lang="scss"></style>
