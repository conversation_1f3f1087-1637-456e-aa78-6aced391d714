<template>
  <div>
    <el-dialog
        title="资源预约"
        :close-on-click-modal="false"
        :visible.sync="visible" width="70%">
      <el-form :model="dataForm" :rules="dataRule" ref="dataForm" label-width="100px">
        <el-row :gutter="20">
          <el-col>
            <el-form-item label-width="0px">
              <span style="font-size: medium">请输入预约信息：</span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="联系人：" prop="contactPerson" :error="errors['contactPerson']">
              <el-input v-model="dataForm.contactPerson" placeholder="请输入" clearable></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="联系电话：" prop="contactPhone" :error="errors['contactPhone']">
              <el-input v-model="dataForm.contactPhone" placeholder="请输入" clearable></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="预约时间：" prop="timeRange">
              <el-date-picker
                  style="width: 100%"
                  v-model="dataForm.timeRange"
                  clearable
                  type="datetimerange"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  align="right"
                  start-placeholder="开始时间"
                  end-placeholder="结束时间"
                  :default-time="['00:00:00', '23:59:59']">
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label-width="50px">
              <span style="font-size: small; color: red">资源提供时间段：{{resourceStartTime + '至' + resourceEndTime}}</span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="活动地址：" prop="activityAddress" :error="errors['activityAddress']">
              <el-input type="textarea" rows="2" v-model="dataForm.activityAddress" placeholder="请输入"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="预约说明：" prop="appointmentRemark" :error="errors['appointmentRemark']">
              <el-input type="textarea" rows="3" v-model="dataForm.appointmentRemark" placeholder="请输入"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button style="background: #d9021a; color: white" @click="dataFormSubmit()">确认</el-button>
        <el-button @click="visible = false">取消</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import _ from "lodash";
import {is8lPhone, isMobile} from "@/utils/validate";

export default {
  data () {
    const validateTimeSelectable = (rule, value, callback) => {
      if (!(value[0] >= this.resourceStartTime && value[1] <= this.resourceEndTime)) {
        callback(new Error('资源预约时间须选择在资源提供时间段内！'))
      } else {
        callback()
      }
    }
    const validateContactPhone = (rule, value, callback) => {
      if (!isMobile(value) && !is8lPhone(value)) {
        callback(new Error('联系电话须为手机号或区号+8位座机号例如051288888888或0512-88888888'))
      } else {
        callback()
      }
    }
    return {
      visible: false,
      dataForm: {
        resourceId: null,
        contactPerson: null,
        contactPhone: null,
        activityAddress: null,
        appointmentRemark: null,
        timeRange: [],
        appointmentStartTime: '',
        appointmentEndTime: ''
      },
      resourceStartTime: null,
      resourceEndTime: null,
      errors: {},
      dataRule: {
        contactPerson: [
          { required: true, message: '联系人不能为空', trigger: 'blur' }
        ],
        contactPhone: [
          {required: true, message: '联系电话不能为空', trigger: 'blur'},
          {validator: validateContactPhone, trigger: 'blur'}
        ],
        activityAddress: [
          {required: true, message: '活动地址不能为空', trigger: 'blur'}
        ],
        timeRange: [
          {required: true, message: '预约时间段不能为空', trigger: 'change'},
          {validator: validateTimeSelectable, trigger: 'change'}
        ],
        appointmentRemark: [
          { required: true, message: '预约说明不能为空', trigger: 'blur' }
        ]
      },
    }
  },
  methods: {
    init (id, resourceStartTime, resourceEndTime) {
      this.dataForm.resourceId = id || null
      this.resourceStartTime = resourceStartTime
      this.resourceEndTime = resourceEndTime
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm']?.resetFields()
        this.dataForm.timeRange = []
        this.dataForm.appointmentStartTime = ''
        this.dataForm.appointmentEndTime = ''
      })
    },
    dataFormSubmit() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.clearErrors()
          this.dataForm.appointmentStartTime = this.dataForm.timeRange[0]
          this.dataForm.appointmentEndTime = this.dataForm.timeRange[1]
          this.$http({
            url: this.$http.adornUrl('/admin/zyz/resource/appointment/saveAppointment'),
            method: 'post',
            data: this.$http.adornData(this.dataForm)
          }).then(({data}) => {
            if (data && data.code === 0) {
              return this.$message({
                message: '操作成功',
                type: 'success',
                duration: 1000,
                onClose: () => {
                  this.visible = false
                  this.$emit('refreshDataList')
                }
              })
            }
            if (data && data.code === 303) {
              let errors = {}
              for (let it of data.obj) {
                errors[`${it.field}`] = it.message
              }
              this.errors = _.cloneDeep(errors)
            } else {
              this.$message.error(data.msg)
            }
          }).catch(() => {
          })
        }
      })
    },
    clearErrors () {
      this.errors = {}
    }
  }
}
</script>

<style scoped>

</style>
