<template>
  <div>
    <el-dialog title="资源预约详情" :close-on-click-modal="false" :visible.sync="visible" width="70%">
      <el-form :model="dataForm" ref="dataForm" label-width="130px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="资源名称" prop="resourceName">
              <el-input disabled v-model="dataForm.resourceName" placeholder="资源名称"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="资源类型" prop="resType">
              <el-dict :code="'REQUIREMENT_TYPE'" disabled v-model="dataForm.resType"></el-dict>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="所属领域" prop="fieldIds">
              <el-cascader disabled placeholder="请选择" v-model="dataForm.fieldIds" :options="fields"
                @change="(value) => { handleChange(value, 'field') }" clearable
                :props="{ label: 'typeName', value: 'typeId' }"></el-cascader>
            </el-form-item>
          </el-col>
          <el-col :span="7">
            <el-form-item label="资源状态" prop="status">
              <!-- <el-dict :code="'resource_status'" disabled v-model="dataForm.status"></el-dict> -->
              <el-input disabled v-model="dataForm.auditStatusText" placeholder="资源状态"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="资源开始时间" prop="resStartTime">
              <el-date-picker v-model="dataForm.resStartTime" disabled value-format="yyyy-MM-dd HH:mm:ss" type="datetime"
                placeholder="资源开始时间">
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="资源截止时间" prop="resEndTime">
              <el-date-picker v-model="dataForm.resEndTime" disabled value-format="yyyy-MM-dd HH:mm:ss" type="datetime"
                placeholder="资源截止时间">
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="联系人" prop="contactPerson">
              <el-input v-model="dataForm.resContactPerson" disabled placeholder="联系人"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="联系电话" prop="contactPhone">
              <el-input v-model="dataForm.resContactPhone" disabled placeholder="联系电话"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="17">
            <el-form-item label="资源详情" prop="resDetail">
              <el-input type="textarea" v-model="dataForm.resDetail" disabled placeholder="需求详情"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="预约组织" prop="orgName">
              <el-input v-model="dataForm.orgName" disabled placeholder="联系人"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="预约联系人" prop="contactPerson">
              <el-input v-model="dataForm.contactPerson" disabled placeholder="联系电话"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="联系电话" prop="contactPhone">
              <el-input v-model="dataForm.contactPhone" disabled placeholder="联系电话"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
             <el-form-item label="预约时间" prop="timeRange">
              <el-date-picker disabled v-model="dataForm.timeRange" clearable type="datetimerange"
                value-format="yyyy-MM-dd HH:mm:ss" align="right" start-placeholder="开始时间" end-placeholder="结束时间"
                :default-time="['00:00:00', '23:59:59']">
              </el-date-picker>
              </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="确定预约时间" prop="auditTimeRange">
              <el-date-picker disabled v-model="dataForm.auditTimeRange" clearable type="datetimerange"
                value-format="yyyy-MM-dd HH:mm:ss" align="right" start-placeholder="开始时间" end-placeholder="结束时间"
                :default-time="['00:00:00', '23:59:59']">
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="17">
            <el-form-item label="预约说明" prop="appointmentRemark">
              <el-input type="textarea" v-model="dataForm.appointmentRemark" disabled placeholder="预约说明"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="图片" prop="picture">
              <el-upload class="avatar-uploader" :action="this.$http.adornUrl(`/admin/oss/upload`)" :headers="myHeaders"
                :data="{ serverCode: this.serverCode, media: false }" :show-file-list="false"
                :on-success="handleAvatarSuccess" :before-upload="beforeAvatarUpload">
                <img v-if="dataForm.picture" :src="$http.adornAttachmentUrl(dataForm.picture)" class="avatar">
                <i v-else class="el-icon-plus avatar-uploader-icon"></i>
                <div slot="tip" class="el-upload__tip" style="color: red">尺寸建议600x1000像素，文件格式jpg、bmp或png，大小不超2M</div>
              </el-upload>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="17">
            <el-form-item label="活动地址" prop="activityAddress">
              <el-input type="textarea" v-model="dataForm.activityAddress" disabled placeholder="活动地址"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="预约申请时间" prop="applyTime">
              <el-input v-model="dataForm.applyTime" disabled placeholder="预约申请时间"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="审核时间" prop="auditTime">
              <el-input v-model="dataForm.auditTime" disabled placeholder="审核时间"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="17">
            <el-form-item label="关联活动" prop="activityName">
              <el-input type="textarea" v-model="dataForm.activityName" disabled placeholder="关联活动"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="活动起止时间" prop="activityTimeRange">
              <el-date-picker disabled v-model="dataForm.activityTimeRange" clearable type="datetimerange"
                value-format="yyyy-MM-dd HH:mm:ss" align="right" start-placeholder="开始时间" end-placeholder="结束时间"
                :default-time="['00:00:00', '23:59:59']">
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="评价时间" prop="evaluateTime">
              <el-input  v-model="dataForm.evaluateTime" disabled placeholder="评价时间"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="评价星级" prop="evaluateStar" :error="detailError">
              <el-rate disabled v-model="dataForm.evaluateStar" text-color="#ff9900">
              </el-rate>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="活动状态" prop="activityStatus">
              <el-input v-model="dataForm.activityStatus" disabled placeholder="活动状态"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="17">
            <el-form-item label="评价内容" prop="evaluateRemark">
              <el-input type="textarea" v-model="dataForm.evaluateRemark" disabled placeholder="评价内容"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="visible = false">关闭</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
export default {
  data() {
    return {
      visible: false,
      logList: [],
      appointmentList: [],
      activeName: '',
      fields: [],
      isShow: false,
      dataForm: {
        id: null,
        version: null,
        resourceName: '',
        orgName: '',
        applyTime: '',
        contactPerson: '',
        auditStatusText: '',
        contactPhone: '',
        auditTime: '',
        activityAddress: '',
        hasAppointmentNum: '',
        startTime: '',
        endTime: '',
        picture: '',
        evaluateStar: '',
        evaluateTime: '',
        evaluateRemark: '',
        auditRemark: '',
        resTypeText: '',
        fieldIds: [],
        fieldId: null,
        timeRange: [],
        auditTimeRange: [],
        activityTimeRange: [],
        resContactPhone: '',
        resDetail: '',
        activityName: '',
        activityStatus: '',
        resContactPerson: ''
      }
    }
  },
  methods: {
    init(appointmentId) {
      this.dataForm.id = appointmentId || null
      this.getFields()
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm']?.resetFields()
        if (this.dataForm.id) {
          this.$http({
            url: this.$http.adornUrl(`/admin/zyz/resource/appointment/getAppointmentById`),
            method: 'get',
            params: this.$http.adornParams({ id: this.dataForm.id })
          }).then(({ data }) => {
            if (data && data.code === 0) {
              this.dataForm = data.obj
              this.dataForm.fieldIds = [data.obj.resFiledTop, data.obj.resFiledEnd]
              this.dataForm.timeRange = [data.obj.appointmentStartTime, data.obj.appointmentEndTime]
              this.dataForm.auditTimeRange = [data.obj.auditStartTime, data.obj.auditEndTime]
              this.dataForm.activityTimeRange = [data.obj.activityStartTime, data.obj.activityEndTime]
            }
          })
        }
      })
    },
    getFields() {
      this.$http({
        url: this.$http.adornUrl('/admin/zyz/platform/belongFieldDict/cascade'),
        method: 'get'
      }).then(({ data }) => {
        if (data && data.code === 0) {
          this.fields = this.getTreeData(data.obj)
        } else {
          this.fields = []
        }
      })
    },
    // *处理点位分类最后children数组为空的状态
    getTreeData: function (data) {
      var that = this
      // 循环遍历json数据
      data.forEach(function (e) {
        if (!e.children || e.children.length < 1) {
          e.children = undefined
        } else {
          // children若不为空数组，则继续 递归调用 本方法
          that.getTreeData(e.children)
        }
      })
      return data
    }
  }
}
</script>
<style lang="scss"></style>
