<template>
  <el-dialog
      :title="'评价'"
      :close-on-click-modal="false"
      width="40%"
      append-to-body
      :visible.sync="visible">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" label-width="120px">
      <el-form-item label="活动名称:" prop="activityName" >
<!--            <el-input  disabled v-model="activityName" placeholder="活动名称"></el-input>-->
        <div>{{activityName}}</div>
      </el-form-item>
      <el-form-item label="活动星级" prop="evaluateStar">
            <el-rate
                v-model="dataForm.evaluateStar"
                text-color="#ff9900">
              </el-rate>
      </el-form-item>
      <el-form-item label="评价内容" prop="evaluateRemark">
            <el-input  type="textarea" v-model="dataForm.evaluateRemark" placeholder="评价内容"></el-input>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="updateRemark()">提交</el-button>
    </span>
  </el-dialog>
</template>

<script>
import moment from 'moment'

export default {
  data() {
    return {
      visible: false,
      activityName: '',
      dataForm: {
        id: null,
        evaluateStar: null,
        evaluateRemark: ''
      },
      dataRule: {
        evaluateRemark: [
          {required: true, message: '评价内容不能为空', trigger: 'blur'}
        ],
        evaluateStar: [
          {required: true, message: '活动不能为空', trigger: 'blur'}
        ]
      },
      livelyError: null
    }
  },
  components: {
    moment
  },
  methods: {
    init(id,linkActivityName) {
      this.dataForm.id = id || null
      this.activityName = linkActivityName || null
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm']?.resetFields()
        if (this.dataForm.id) {
          this.$http({
            url: this.$http.adornUrl(`/admin/zyz/resource/appointment`),
            method: 'get',
            params: this.$http.adornParams({
              id: this.dataForm.id })
          }).then(({data}) => {
            if (data && data.code === 0) {
              this.dataForm = data.obj
            }
          })
        }
      })
    },
    updateRemark(){
        this.$http({
          url: this.$http.adornUrl(`/admin/zyz/resource/appointment/updateRemark`),
          method: 'get',
          params: this.$http.adornParams({
            'id': this.dataForm.id,
            'evaluateRemark': this.dataForm.evaluateRemark,
            'evaluateStar': this.dataForm.evaluateStar
          })
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.$message({
              message: '操作成功',
              type: 'success',
              duration: 1500,
              onClose: () => {
                this.visible = false
                this.$emit('refreshDataList')
              }
            })
          } else {
            this.$message.error(data.msg)
          }
        })
    }
  }
}
</script>
