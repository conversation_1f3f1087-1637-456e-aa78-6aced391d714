<template>
  <el-dialog
      :title="!dataForm.id ? '新增' : '修改'"
      :close-on-click-modal="false"
      :visible.sync="visible">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmit()"
             label-width="120px">
      <el-form-item label="测评项名称" prop="name" :error="errors['name']">
        <el-input v-model="dataForm.name" placeholder="测评项名称"></el-input>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import editMixin from '@/mixins/edit-mixins'

export default {
  mixins: [editMixin],
  data() {
    return {
      editOptions: {
        initUrl: '/admin/manager/advanced-unit/item'
      },
      initForm: {
        standardId: '',
        name: ''
      },
      dataRule: {
        name: [
          {required: true, message: '测评项名称不能为空', trigger: 'blur'}
        ]
      }
    }
  },
  methods: {
    // 重写 init 方法，支持传入 standardId
    init(id, standardId) {
      this.dataForm = { ...this.initForm, id: id }
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()

        // 如果是新增模式且传入了standardId，则设置standardId
        if (!this.dataForm.id && standardId) {
          this.dataForm.standardId = standardId
        }

        if (this.dataForm.id) {
          // 编辑模式，加载数据
          this.$http({
            url: this.$http.adornUrl(`${this.editOptions.initUrl}`),
            method: 'get',
            params: this.$http.adornParams({id: this.dataForm.id})
          }).then(({data}) => {
            if (data && data.code === 0) {
              this.dataForm = data.obj
            }
          })
        }
      })
    }
  }
}
</script>
