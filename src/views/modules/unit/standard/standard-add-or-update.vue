<template>
  <el-dialog
      :title="!dataForm.id ? '新增' : '修改'"
      :close-on-click-modal="false"
      :visible.sync="visible">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmit()"
             label-width="120px">
      <!-- 测评版本名称 (占满一行) -->
      <el-form-item label="测评版本名称" prop="name" :error="errors['name']">
        <el-input v-model="dataForm.name" placeholder="测评版本名称"></el-input>
      </el-form-item>
      
      <!-- 所属年份 (占满一行) -->
      <el-form-item label="所属年份" prop="year" :error="errors['year']">
        <el-input-number v-model="dataForm.year" controls-position="right" :min="0" style="width: 100%"></el-input-number>
      </el-form-item>
      
      <!-- 状态 (占满一行), 用切换按钮 -->
      <el-form-item label="状态" prop="status" :error="errors['status']">
        <el-switch v-model="dataForm.status" :active-value="true" :inactive-value="false"></el-switch>
      </el-form-item>
      
      <!-- 测评说明 (占满一行) -->
      <el-form-item label="测评说明" prop="description" :error="errors['description']">
        <el-input v-model="dataForm.description" type="textarea" :rows="3" placeholder="测评说明"></el-input>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import editMixin from '@/mixins/edit-mixins'

export default {
  mixins: [editMixin],
  data() {
    return {
      editOptions: {
        initUrl: '/admin/manager/advanced-unit/standard'
      },
      initForm: {
        year: '', status: false, name: '', description: ''
      },
      dataForm: {
        year: '', status: false, name: '', description: ''
      },
      dataRule: {
        status: [
          {required: true, message: '展示状态不能为空', trigger: 'blur'}
        ], name: [
          {required: true, message: '测评版本名称不能为空', trigger: 'blur'}
        ], description: [
          {required: true, message: '测评说明不能为空', trigger: 'blur'}
        ]
      }
    }
  },
  methods: {}
}
</script>
