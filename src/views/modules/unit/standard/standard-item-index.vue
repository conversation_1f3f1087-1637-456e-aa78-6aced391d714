<template>
  <div class="mod-config">
    <!-- 顶部标准选择区域 -->
    <div class="standard-select-area">
      <el-form :inline="true" :model="standardForm" ref="standardForm">
        <el-form-item label="测评版本">
          <!-- 宽一点 -->
          <el-select v-model="currentStandardId" placeholder="请选择测评版本" @change="handleStandardChange" style="width: 350px;">
            <el-option
              v-for="item in standardList"
              :key="item.id"
              :label="item.name"
              :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="warning" icon="el-icon-search" @click="loadItemList">查询</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 内容区域（左右布局） -->
    <el-row :gutter="20" class="content-area" v-if="currentStandardId">
      <!-- 左侧项目列表 -->
      <el-col :span="6">
        <el-card class="box-card">
          <div slot="header" class="card-header">
            <span>测评项目指标管理</span>
            <el-button
              type="primary"
              size="mini"
              icon="el-icon-plus"
              @click="addNewItem"
              style="float: right;">
              新增
            </el-button>
          </div>
          <div class="item-list">
            <!-- 替换 el-menu 为自定义列表 -->
            <div class="custom-item-list">
              <div
                v-for="item in itemList"
                :key="item.id"
                :class="['item-row', {'item-active': currentItemId == item.id}]"
                @click="handleItemSelect(item.id)">
                <span class="item-name">{{ item.name }}</span>
                <div class="item-actions">
                  <el-button type="text" size="small" @click.stop="editItem(item.id)">
                    <i class="el-icon-edit"></i>
                  </el-button>
                  <el-button type="text" size="small" @click.stop="deleteItem(item.id)">
                    <i class="el-icon-delete"></i>
                  </el-button>
                </div>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>

      <!-- 右侧指标列表 (使用 listMixin) -->
      <el-col :span="18">
        <el-card class="box-card">
          <div slot="header" class="card-header">
            <span>测评标准分值管理</span>
            <div>
              <el-button
                v-if="currentItemId"
                type="primary"
                icon="el-icon-plus"
                @click="addNewIndex"
                >新增</el-button>
            </div>
          </div>
          <div v-if="!currentItemId" class="empty-tip">
            <el-empty description="请先选择左侧测评项目"></el-empty>
          </div>
          <div v-else class="index-list">
            <el-table
              :data="dataList"
              border
              v-loading="dataListLoading"
              style="width: 100%;">
              <!-- 显示六行文本 -->
              <el-table-column
                type="index"
                label="序号"
                width="80"
                align="center">
              </el-table-column>
              <el-table-column
                prop="name"
                label="测评标准"
                min-width="300"
                show-overflow-tooltip>
                <template slot-scope="scope">
                  <div class="multi-line-content">{{ scope.row.name }}</div>
                </template>
              </el-table-column>
              <el-table-column
                prop="score"
                label="分值（分）"
                width="100"
                align="center">
              </el-table-column>
              <el-table-column
                prop="content"
                label="测评方法及内容"
                min-width="300">
                <template slot-scope="scope">
                  <div class="multi-line-content">{{ scope.row.content }}</div>
                </template>
              </el-table-column>
              <el-table-column
                fixed="right"
                header-align="center"
                align="center"
                width="150"
                label="操作">
                <template slot-scope="scope">
                  <el-button type="text" size="small" @click="editIndex(scope.row.id)">编辑</el-button>
                  <el-button type="text" size="small" @click="deleteHandle(scope.row.id)">删除</el-button>
                </template>
              </el-table-column>
            </el-table>
            <el-pagination
              @size-change="sizeChangeHandle"
              @current-change="currentChangeHandle"
              :current-page="pageIndex"
              :page-sizes="[10, 20, 50, 100]"
              :page-size="pageSize"
              :total="totalPage"
              layout="total, sizes, prev, pager, next, jumper">
            </el-pagination>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <div v-if="!currentStandardId" class="empty-standard-tip">
      <el-empty description="请先选择测评版本"></el-empty>
    </div>

    <!-- 项目弹窗组件 -->
    <item-add-or-update v-if="itemVisible" ref="itemAddOrUpdate" @refreshDataList="loadItemList"></item-add-or-update>

    <!-- 指标弹窗组件 -->
    <index-add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"></index-add-or-update>
  </div>
</template>

<script>
import ItemAddOrUpdate from '../item/item-add-or-update.vue'
import IndexAddOrUpdate from '../index/index-add-or-update.vue'
import listMixin from '@/mixins/list-mixins'
import {isAuth} from '@/utils'

export default {
  mixins: [listMixin],
  components: {
    ItemAddOrUpdate,
    IndexAddOrUpdate
  },
  data() {
    return {
      // listMixin 配置
      mixinOptions: {
        dataUrl: '/admin/manager/advanced-unit/index/pages',
        getDataListIsAuto: false, // 不自动获取数据
        deleteUrl: '/admin/manager/advanced-unit/index/removeByIds'
      },

      // 标准相关
      standardForm: {},
      standardList: [],
      currentStandardId: '',

      // 项目相关
      itemList: [],
      currentItemId: '',
      itemVisible: false,

      // 指标相关 (为与 listMixin 兼容而重命名)
      dataForm: {
        itemId: ''
      }
    }
  },
  created() {
    this.loadStandardList()
  },
  methods: {
    isAuth,
    // 加载标准列表
    loadStandardList() {
      this.$http({
        url: this.$http.adornUrl('/admin/manager/advanced-unit/standard/list'),
        method: 'get',
        params: this.$http.adornParams({})
      }).then(({data}) => {
        if (data) {
          this.standardList = data.obj || []
          if (this.standardList.length > 0) {
            this.currentStandardId = this.standardList[0].id
            this.loadItemList()
          }
        }
      })
    },

    // 标准切换
    handleStandardChange(standardId) {
      this.currentStandardId = standardId
      this.currentItemId = ''
      this.loadItemList()
    },

    // 加载项目列表
    loadItemList() {
      if (!this.currentStandardId) return

      this.$http({
        url: this.$http.adornUrl('/admin/manager/advanced-unit/item/list'),
        method: 'post',
        data: this.$http.adornData({
          standardId: this.currentStandardId
        })
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.itemList = data.obj || []
          if (this.itemList.length > 0 && !this.currentItemId) {
            this.currentItemId = this.itemList[0].id
            this.dataForm.itemId = this.itemList[0].id
            this.getDataList()
          } else if (this.itemList.length === 0) {
            this.currentItemId = ''
            this.dataForm.itemId = ''
            this.dataList = []
          }
        }
      })
    },

    // 项目选择
    handleItemSelect(itemId) {
      this.currentItemId = itemId
      this.dataForm.itemId = itemId
      this.getDataList()
    },
    // 添加新项目
    addNewItem() {
      this.itemVisible = true
      this.$nextTick(() => {
        this.$refs.itemAddOrUpdate.init(null, this.currentStandardId)
      })
    },

    // 添加新指标（使用 listMixin 方式）
    addNewIndex() {
      this.addOrUpdateHandle(null, this.currentItemId)
    },

    // 编辑指标（使用 listMixin 方式）
    editIndex(id) {
      this.addOrUpdateHandle(id)
    },

    // 重写 addOrUpdateHandle 方法以传递 itemId 和 standardId
    addOrUpdateHandle(id, itemId) {
      this.addOrUpdateVisible = true
      this.$nextTick(() => {
        this.$refs.addOrUpdate.init(id, itemId || this.currentItemId, this.currentStandardId)
      })
    },

    // 编辑项目
    editItem(id) {
      this.itemVisible = true
      this.$nextTick(() => {
        this.$refs.itemAddOrUpdate.init(id, this.currentStandardId)
      })
    },

    // 删除项目
    deleteItem(id) {
      let ids = id ? [id] : this.dataListSelections.map(item => {
        return item.id
      })
      this.$confirm(`确定进行${id ? '删除' : '批量删除'}操作?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        closeOnClickModal: false
      }).then(() => {
        this.$http({
          url: this.$http.adornUrl('/admin/manager/advanced-unit/item/removeByIds'),
          method: 'get',
          params: this.$http.adornParams({'ids': ids.join(',')}, false)
        }).then(({data}) => {
          if (data.code !== 0) {
            return this.$message.error(data.msg)
          }
          this.$message({
            message: '操作成功',
            type: 'success',
            duration: 1000,
            onClose: () => {
              this.loadItemList()
            }
          })
        }).catch(() => {
        })
      }).catch(() => {
      })
    }
  }
}
</script>

<style scoped>
.mod-config {
  padding: 10px 20px;
}
.standard-select-area {
  padding: 5px 15px;
  border-radius: 4px;
}
.content-area {
  margin-top: 5px;
}
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.item-list {
  max-height: 600px;
  overflow-y: auto;
}
.item-menu {
  border-right: none;
}
.empty-tip, .empty-standard-tip {
  padding: 30px 0;
}
/* 新增多行内容显示样式 */
.multi-line-content {
  line-height: 1.5;
  max-height: 9em; /* 6行文字的高度 = 行高 * 6 */
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 6; /* 显示6行 */
  -webkit-box-orient: vertical;
  text-overflow: ellipsis;
  white-space: normal;
  word-break: break-all;
}
/* 自定义项目列表样式 */
.custom-item-list {
  width: 100%;
}
.item-row {
  padding: 14px 20px;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: background-color 0.3s;
  border-bottom: 1px solid #f0f0f0;
  position: relative;
}
.item-row:hover {
  background-color: #f5f7fa;
}
.item-active {
  background-color: #ecf5ff;
  color: #409EFF;
}
.item-name {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.item-actions {
  display: none;
  position: absolute;
  right: 10px;
}
.item-row:hover .item-actions {
  display: flex;
}
</style>
