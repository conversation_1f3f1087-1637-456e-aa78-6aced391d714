<template>
  <div class="mod-config">
    <!-- <el-form :inline="true" :model="dataForm" ref="dataForm" @keyup.enter.native="getDataList()">
      <el-form-item prop="key" label="关键字：">
        <el-input v-model="dataForm.key" placeholder="关键字" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-button @click="getDataList()">查询</el-button>
        <el-button icon="el-icon-refresh" @click="resetForm()">重置</el-button>
      </el-form-item>
    </el-form> -->
    <div class="f-s-c"
         style="padding: 5px 0;margin-bottom:15px;display: flex; justify-content: space-between;">
      <div style="font-weight: bold;">为您查询到{{ totalPage || 0 }}条数据</div>
      <div>
        <el-button icon="el-icon-plus" type="primary" @click="addOrUpdateHandle()">
          新增
        </el-button>
        <el-button v-if="isAuth('zyz:au:standard:delete')" type="danger" @click="deleteHandle()"
                   :disabled="dataListSelections.length <= 0">批量删除
        </el-button>
      </div>
    </div>
    <el-table
        :data="dataList"
        border
        v-loading="dataListLoading"
        @selection-change="selectionChangeHandle"
        style="width: 100%;">
      <el-table-column
          type="selection"
          header-align="center"
          align="center"
          width="50">
      </el-table-column>
      <el-table-column
          prop="year"
          header-align="center"
          align="center"
          label="所属年份">
      </el-table-column>
      <el-table-column
          prop="status"
          header-align="center"
          align="center"
          label="展示状态">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.status == false" size="small" type="danger">已关闭</el-tag>
          <el-tag v-else size="small">开启</el-tag>
        </template>
      </el-table-column>
      <el-table-column
          prop="name"
          header-align="center"
          align="center"
          label="测评版本名称">
      </el-table-column>
      <el-table-column
          prop="description"
          header-align="center"
          align="center"
          label="测评说明">
      </el-table-column>
      <el-table-column
          fixed="right"
          header-align="center"
          align="center"
          width="150"
          label="操作">
        <template slot-scope="scope">
          <!-- 上下架 -->
          <el-button
            type="text"
            size="small"
            :style="{ color: scope.row.status ? '#F56C6C' : '' }"
            @click="changeStatus(scope.row.id)">
            {{ scope.row.status ? '下架' : '上架' }}
          </el-button>
          <el-button type="text" size="small" @click="addOrUpdateHandle(scope.row.id)">编辑</el-button>
          <el-button type="text" size="small" @click="deleteHandle(scope.row.id)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
        @size-change="sizeChangeHandle"
        @current-change="currentChangeHandle"
        :current-page="pageIndex"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pageSize"
        :total="totalPage"
        layout="total, sizes, prev, pager, next, jumper">
    </el-pagination>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"></add-or-update>
  </div>
</template>

<script>
import AddOrUpdate from './standard-add-or-update.vue'
import listMixin from '@/mixins/list-mixins'

export default {
  mixins: [listMixin],
  data() {
    return {
      mixinOptions: {
        dataUrl: '/admin/manager/advanced-unit/standard/pages',
        deleteUrl: '/admin/manager/advanced-unit/standard/removeByIds'
      },
      switchStatusUrl: '/admin/manager/advanced-unit/standard/status/switch',
      dataForm: {
        orders: [
          {column: 'year', sort: 'desc'}
        ]
      }
    }
  },
  components: {
    AddOrUpdate
  },
  methods: {
    // 上下架
    changeStatus(id) {
      this.$http({
        url: this.$http.adornUrl(this.switchStatusUrl),
        method: 'post',
        params: this.$http.adornParams({id}),
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.$message.success('操作成功')
          this.getDataList()
        } else {
          this.$message.error(data.msg)
        }
      })
    }
  },
  mounted() {
    this.getDataList()
  }
}
</script>
