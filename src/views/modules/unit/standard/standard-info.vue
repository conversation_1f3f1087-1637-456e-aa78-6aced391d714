<template>
  <el-dialog
      :close-on-click-modal="false"
      width="960"
      :visible.sync="visible">
    <el-descriptions title="详情" :column="3" border>
      <template slot="extra">
        <el-button type="primary" size="small">编辑</el-button>
      </template>
      <el-descriptions-item label="所属年份">{{ dataForm.year }}</el-descriptions-item>
      <el-descriptions-item label="展示状态">{{ dataForm.status }}</el-descriptions-item>
      <el-descriptions-item label="测评版本名称">{{ dataForm.name }}</el-descriptions-item>
      <el-descriptions-item label="测评说明">{{ dataForm.description }}</el-descriptions-item>
    </el-descriptions>
  </el-dialog>
</template>

<script>
export default {
  data() {
    return {
      visible: false,
      dataForm: {},
      initForm: {
        id: null,
        version: null,
        year: '',
        status: '',
        name: '',
        description: ''
      },
      dataRule: {
        status: [
          {required: true, message: '展示状态不能为空', trigger: 'blur'}
        ],
        name: [
          {required: true, message: '测评版本名称不能为空', trigger: 'blur'}
        ],
        description: [
          {required: true, message: '测评说明不能为空', trigger: 'blur'}
        ]
      },
      yearError: null,
      statusError: null,
      nameError: null,
      descriptionError: null
    }
  },
  methods: {
    init(id) {
      this.dataForm.id = id || null
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.$http({
            url: this.$http.adornUrl(`/admin/manager/advanced-unit/standard`),
            method: 'get',
            params: this.$http.adornParams({id: this.dataForm.id})
          }).then(({data}) => {
            if (data && data.code === 0) {
              this.dataForm = data.obj
            }
          })
        }
      })
    },
    // 表单提交
    dataFormSubmit() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.$http({
            url: this.$http.adornUrl(`/austandard/${!this.dataForm.id ? 'save' : 'update'}`),
            method: 'post',
            data: this.$http.adornData(this.dataForm)
          }).then(({data}) => {
            if (data && data.code === 0) {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 1500,
                onClose: () => {
                  this.visible = false
                  this.$emit('refreshDataList')
                }
              })
            } else if (data && data.code === 303) {
              for (let it of data.obj) {
                this[`${it.field}Error`] = it.message
              }
            } else {
              this.$message.error(data.msg)
            }
          })
        }
      })
    }
  }
}
</script>
