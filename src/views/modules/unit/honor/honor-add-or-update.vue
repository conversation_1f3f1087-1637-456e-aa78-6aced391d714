<template>
  <el-dialog
      :title="!dataForm.id ? '新增' : '修改'"
      :close-on-click-modal="false"
      append-to-body
      :visible.sync="visible">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmit()"
             label-width="80px">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="荣誉名称" prop="name" :error="errors['name']">
            <el-input v-model="dataForm.name" placeholder="请输入荣誉名称"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="顺序" prop="orderNum" :error="errors['orderNum']">
            <el-input-number v-model="dataForm.orderNum" controls-position="right" :min="0" :max="99999999" placeholder="请输入顺序"></el-input-number>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="图片" prop="image" :error="errors['image']">
            <el-upload
                class="avatar-uploader"
                :action="uploadUrl"
                :headers="myHeaders" 
                :show-file-list="false"
                :data="uploadData"
                :on-success="handleImageSuccess"
                :before-upload="beforeImageUpload"
            >
              <img v-if="dataForm.image" :src="$http.adornAttachmentUrl(dataForm.image)" class="avatar">
              <i v-else class="el-icon-plus avatar-uploader-icon"/>
              <div slot="tip" class="el-upload__tip" style="color: red">
                支持jpg.png等图片格式，图片大小控制在合理范围，建议300KB-5MB范围内              
              </div>
            </el-upload>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button
          type="primary"
          @click="dataFormSubmit()"
          :loading="dataFormLoading"
          :disabled="dataFormLoading">
        确定
      </el-button>
    </span>
  </el-dialog>
</template>

<script>
import editMixin from '@/mixins/edit-mixins'
import Vue from 'vue'

export default {
  mixins: [editMixin],
  data() {
    return {
      dataForm: {
        id: null,
        orgId: '',
        name: '',
        orderNum: '',
        image: '' 
      },
      serverCode: 'LocalServer',
      // 上传相关配置示例
      uploadUrl: this.$http.adornUrl('/admin/oss/upload'),
      myHeaders: {Authorization: Vue.cookie.get('Authorization')},
      uploadData: {serverCode: 'LocalServer', media: false},
      editOptions: {
        initUrl: '/admin/manager/advanced-unit/unit/honor'
      },
      initForm: {
        orgId: '', name: '', orderNum: '', image: '', id: ''
      },
      dataRule: {
        name: [
          {required: true, message: '荣誉名称不能为空', trigger: 'blur'}
        ], orderNum: [
          {required: true, message: '顺序不能为空', trigger: 'blur'}
        ], image: [
          {required: true, message: '图片不能为空', trigger: 'blur'}
        ]
      }
    }
  },
  methods: {
    async init (id, orgId) {
      this.dataForm = this.initForm
      this.dataForm.id = id || null
      this.visible = true
      this.dataForm.orgId = orgId
      
      this.$nextTick(async() => {
        this.$refs['dataForm'].resetFields()
        
        await this.initBeforeHandle()
        this.visible = true
        this.$nextTick(() => {
          this.$refs['dataForm']?.resetFields()
          if (this.dataForm.id) {
            this.getData()
          }
        })
      })
    },
    // 上传图片前校验
    beforeImageUpload(file) {
      const isImage =
          file.type === 'image/jpeg' ||
          file.type === 'image/png' ||
          file.type === 'image/bmp'
      const isLt2M = file.size / 1024 / 1024 < 5
      if (!isImage) {
        this.$message.error('上传图片只能是 JPG/PNG/BMP 格式!')
      }
      if (!isLt2M) {
        this.$message.error('上传图片大小不能超过 2MB!')
      }
      return isImage && isLt2M
    },
    // 上传成功
    handleImageSuccess(res, file) {
      if (res.success) {
        this.dataForm.image = res.obj.path
        this.$message.success('上传成功')
      } else {
        this.$message.error('上传失败')
      }
    },
  }
}
</script>
