<template>
  <el-dialog
      :title="!dataForm.id ? '新增测评标准分值' : '修改测评标准分值'"
      :close-on-click-modal="false"
      :visible.sync="visible">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmit()"
             label-width="120px">
      <el-form-item label="测评标准名称" prop="name" :error="errors['name']">
        <el-input v-model="dataForm.name" placeholder="请输入测评标准名称"></el-input>
      </el-form-item>
      
      <el-form-item label="排序" prop="orderNum" :error="errors['orderNum']">
        <el-input-number v-model="dataForm.orderNum" controls-position="right" :min="0" :max="9999"></el-input-number>
      </el-form-item>
      
      <el-form-item label="分值（分）" prop="score" :error="errors['score']">
        <el-input-number v-model="dataForm.score" controls-position="right" :min="0"></el-input-number>
      </el-form-item>
      
      <el-form-item label="测评方法及内容" prop="content" :error="errors['content']">
        <el-input 
          type="textarea" 
          v-model="dataForm.content" 
          placeholder="请输入测评方法及内容"
          :rows="6">
        </el-input>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">保存</el-button>
    </span>
  </el-dialog>
</template>

<script>
import editMixin from '@/mixins/edit-mixins'

export default {
  mixins: [editMixin],
  data() {
    return {
      editOptions: {
        initUrl: '/admin/manager/advanced-unit/index'
      },
      initForm: {
        standardId: '',
        itemId: '', 
        name: '', 
        content: '', 
        score: 100, 
        orderNum: 1
      },
      dataRule: {
        name: [
          {required: true, message: '测评标准名称不能为空', trigger: 'blur'}
        ],
        content: [
          {required: true, message: '测评方法及内容不能为空', trigger: 'blur'}
        ],
        score: [
          {required: true, message: '分值不能为空', trigger: 'blur'}
        ],
        orderNum: [
          {required: true, message: '排序不能为空', trigger: 'blur'}
        ]
      }
    }
  },
  methods: {
    // 重写 init 方法，支持传入 standardId 和 itemId
    init(id, itemId, standardId) {
      this.dataForm = { ...this.initForm, id: id }
      this.visible = true
      
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        
        // 设置关联的itemId和standardId
        if (itemId) {
          this.dataForm.itemId = itemId
        }
        
        if (standardId) {
          this.dataForm.standardId = standardId
        }
        
        if (this.dataForm.id) {
          // 编辑模式，加载数据
          this.$http({
            url: this.$http.adornUrl(`${this.editOptions.initUrl}`),
            method: 'get',
            params: this.$http.adornParams({id: this.dataForm.id})
          }).then(({data}) => {
            if (data && data.code === 0) {
              this.dataForm = data.obj
            }
          })
        }
      })
    }
  }
}
</script>

<style>
.el-dialog__body {
  padding: 20px 30px;
}
</style>
