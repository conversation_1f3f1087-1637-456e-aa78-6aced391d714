<template>
  <el-dialog
      :close-on-click-modal="false"
      :visible.sync="visible"
      title="单位荣誉管理"
      width="70%">
    <div class="f-s-c"
         style="padding: 5px 0;margin-bottom:15px;display: flex; justify-content: space-between;">
      <div style="font-weight: bold;">为您查询到{{ totalPage || 0 }}条数据</div>
      <div>
        <el-button icon="el-icon-plus" type="primary" @click="addOrUpdateHandle()">
          新增
        </el-button>
        <el-button v-if="isAuth('zyz:au:honor:delete')" :disabled="dataListSelections.length <= 0" type="danger"
                   @click="deleteHandle()">批量删除
        </el-button>
      </div>
    </div>
    <el-table
        v-loading="dataListLoading"
        :data="dataList"
        border
        style="width: 100%;">
      <!-- el table 自动序号 -->
      <el-table-column
          align="center"
          header-align="center"
          label="序号"
          width="80"
          type="index">
      </el-table-column>
      <el-table-column
          align="center"
          header-align="center"
          label="荣誉名称"
          prop="name">
      </el-table-column>
      <el-table-column
          align="center"
          header-align="center"
          label="顺序"
          prop="orderNum">
      </el-table-column>
      <el-table-column
          align="center"
          header-align="center"
          label="图片"
          prop="image">
        <template slot-scope="scope">
          <!-- 限高不限宽 -->
          <el-image
              v-if="scope.row.image"
              :src="$http.adornAttachmentUrl(scope.row.image)"
              style="height: 50px;">
          </el-image>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column
          align="center"
          fixed="right"
          header-align="center"
          label="操作"
          width="150">
        <template slot-scope="scope">
          <el-button size="small" type="text" @click="addOrUpdateHandle(scope.row.id)">编辑</el-button>
          <el-button size="small" type="text" @click="deleteHandle(scope.row.id)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
        :current-page="pageIndex"
        :page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="totalPage"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="sizeChangeHandle"
        @current-change="currentChangeHandle">
    </el-pagination>
    <!-- 荣誉添加/修改弹窗 -->
    <honor-add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate"
                         @refreshDataList="getDataList"></honor-add-or-update>
  </el-dialog>
</template>

<script>
import HonorAddOrUpdate from '../honor/honor-add-or-update.vue'
import listMixin from '@/mixins/list-mixins'
import {isAuth} from "@/utils";

export default {
  mixins: [listMixin],
  data() {
    return {
      visible: false,
      orgId: null, // 单位ID，修改为orgId
      mixinOptions: {
        dataUrl: '/admin/manager/advanced-unit/unit/honor/pages',
        deleteUrl: '/admin/manager/advanced-unit/unit/honor/removeByIds'
      },
      templateFileName: '文明单位导入模板.v0.1.2025.03.28.xlsx',
      downloadPath: '/admin/zyz/volunteer/template',
      dataForm: {
        key: '',
        orgId: null,
      }
    }
  },
  components: {
    HonorAddOrUpdate
  },
  methods: {
    isAuth,
    // 修改方法参数和内部引用
    init(orgId) {
      this.orgId = orgId
      this.dataForm.orgId = orgId
      this.visible = true
      this.getDataList()
    },
    // 新增 / 修改，传递orgId
    addOrUpdateHandle(id) {
      this.addOrUpdateVisible = true
      this.$nextTick(() => {
        this.$refs.addOrUpdate.init(id, this.orgId)
      })
    }
  }
}
</script>
