<template>
  <div class="mod-config">
    <el-form :inline="true" :model="dataForm" ref="dataForm" @keyup.enter.native="getDataList()">
      <el-form-item prop="unitName" label="单位名称: ">
        <el-input v-model="dataForm.unitName" placeholder="请输入单位名称" clearable></el-input>
      </el-form-item>
      <!-- 单位性质 -->
      <el-form-item prop="unitNature" label="单位性质: ">
        <el-dict :code="'AU_NATURE'" v-model="dataForm.unitNature"></el-dict>
      </el-form-item>
      <!-- 联系人 -->
      <el-form-item prop="contactPerson" label="联系人: ">
        <el-input v-model="dataForm.contactPerson" placeholder="请输入联系人" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-search" type="warning" @click="getDataList()">查询</el-button>
        <el-button @click="resetForm()" icon="el-icon-refresh-left">重置</el-button>
      </el-form-item>
    </el-form>
    <div class="f-s-c"
         style="padding: 5px 0;margin-bottom:15px;display: flex ;justify-content: space-between;">
      <div style="font-weight: bold;">为您查询到{{ totalPage || 0 }}条数据</div>
      <div>
        <el-button icon="el-icon-plus" type="primary" @click="addOrUpdateHandle()">
          新增
        </el-button>
        <!-- 导入 -->
        <el-button icon="el-icon-upload2" type="success" @click="importHandle()">
          批量导入
        </el-button>
        <!-- 导出 -->
        <el-button icon="el-icon-download" type="success" @click="exportHandle()">
          导出
        </el-button>
        <!-- 批量上架/下架 -->
        <el-button icon="el-icon-sort" type="primary" @click="switchStatusHandle()">
          批量上架/下架
        </el-button>
      </div>
    </div>
    <el-table
        :data="dataList"
        border
        v-loading="dataListLoading"
        @selection-change="selectionChangeHandle"
        style="width: 100%;">
      <el-table-column
          type="selection"
          header-align="center"
          align="center"
          width="50">
      </el-table-column>
      <el-table-column
          prop="unitLogo"
          header-align="center"
          align="center"
          label="单位logo">
          <template slot-scope="scope">
            <!-- 如果图片路径为空, 显示- -->
            <el-image v-if="scope.row.unitLogo" :src="$http.adornAttachmentUrl(scope.row.unitLogo)" style="width: 50px;height: 50px;"></el-image>
            <span v-else>-</span>
          </template>
      </el-table-column>
      <el-table-column
          prop="unitName"
          header-align="center"
          align="center"
          width="180"
          label="单位名称">
          <template slot-scope="scope">
            <a style="cursor: pointer" @click="viewDetailHandle(scope.row.id)">{{ scope.row.unitName }}</a>
          </template>
      </el-table-column>
      <el-table-column
          prop="creditCode"
          header-align="center"
          align="center"
          width="180"
          label="统一社会信用代码">
      </el-table-column>
      <el-table-column
          prop="contactPerson"
          header-align="center"
          align="center"
          label="联系人">
      </el-table-column>
      <el-table-column
          prop="contactPhone"
          header-align="center"
          align="center"
          label="联系方式">
      </el-table-column>
      <el-table-column
          prop="unitNatureName"
          header-align="center"
          align="center"
          label="单位性质">
      </el-table-column>
      <el-table-column
          prop="unitTypeName"
          header-align="center"
          align="center"
          label="单位类型">
      </el-table-column>
      <el-table-column
          prop="address"
          header-align="center"
          align="center"
          min-width="180"
          label="单位地址">
      </el-table-column>
      <el-table-column
          prop="createDate"
          header-align="center"
          align="center"
          min-width="150"
          label="注册时间">
      </el-table-column>
      <el-table-column
          prop="showStatus"
          header-align="center"
          align="center"
          label="状态">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.showStatus === false" size="small" type="danger">已下架</el-tag>
          <el-tag v-else size="small">已上架</el-tag>
        </template>
      </el-table-column>
      <el-table-column
          fixed="right"
          header-align="center"
          align="center"
          width="180"
          label="操作">
        <template slot-scope="scope">
          <el-button type="text" size="small" @click="addOrUpdateHandle(scope.row.id)" :disabled="scope.row.showStatus">编辑</el-button>
          <!-- 红色删除按钮 -->
          <el-button type="text" size="small" @click="deleteHandle(scope.row.id)" :disabled="scope.row.showStatus">删除</el-button>
          <!-- 动态颜色, 上架为正常颜色, 下架为红色 -->
          <el-button type="text" size="small" @click="switchStatusHandle(scope.row.id)" :style="{ color: !scope.row.showStatus ? '' : '#F56C6C' }">
            {{ scope.row.showStatus ? '下架' : '上架' }}
          </el-button>
          <!-- 荣誉管理, 打开弹窗 -->
          <el-button type="text" size="small" @click="honorManageHandle(scope.row.id)">荣誉管理</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
        @size-change="sizeChangeHandle"
        @current-change="currentChangeHandle"
        :current-page="pageIndex"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pageSize"
        :total="totalPage"
        layout="total, sizes, prev, pager, next, jumper">
    </el-pagination>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"></add-or-update>
    <!-- 详情弹窗 -->
    <add-or-update v-if="detailVisible" ref="viewDetail" :is-view="true" @refreshDataList="getDataList"></add-or-update>
    <!-- 导入弹窗 -->
    <unit-import v-if="true" ref="unitImport" @refreshDataList="getDataList"></unit-import>
    <!-- 荣誉管理弹窗 -->
    <unit-honor-manage ref="unitHonorManage"></unit-honor-manage>
  </div>
</template>

<script>
import UnitAddOrUpdate from './unit-add-or-update.vue'
import UnitImport from './unit-import.vue'
import UnitHonorManage from './unit-honor-manage.vue'
import listMixin from '@/mixins/list-mixins'

export default {
  mixins: [listMixin],
  data() {
    return {
      mixinOptions: {
        dataUrl: '/admin/manager/advanced-unit/unit/page',
        deleteUrl: '/admin/manager/advanced-unit/unit/removeByIds',
        exportUrl: '/admin/manager/advanced-unit/unit/export',
        exportFileName: '单位信息导出'
      },
      switchStatusUrl: '/admin/manager/advanced-unit/unit/status/switch',
      dataForm: {
        unitName: '',
        contactPerson: '',
        unitNature: '',
        auditStatus: 'UNIT_PASS'
      },
      initForm: {
        unitName: '',
        contactPerson: '',
        unitNature: '',
        auditStatus: 'UNIT_PASS'
      },
      importVisible: false,
      detailVisible: false
    }
  },
  activated() {
    this.getDataList()
  },
  components: {
    AddOrUpdate: UnitAddOrUpdate,
    UnitImport: UnitImport,
    UnitHonorManage: UnitHonorManage
  },
  methods: {
    exportHandle () {
      this.exportLoading = true
      let params = {...this.dataForm}
      // 如果有选中的数据，则导出选中的数据
      if (this.dataListSelections && this.dataListSelections.length > 0) {
        params.ids = this.dataListSelections.map(item => item.id)
      }
      this.$http({
        url: this.$http.adornUrl(this.mixinOptions.exportUrl),
        method: 'post',
        responseType: 'arraybuffer',
        data: this.$http.adornData(params)
      }).then(({data}) => {
        this.downloadExcel(data, this.mixinOptions.exportFileName)
        this.exportLoading = false
      })
    },
    resetForm() {
      this.dataForm = {...this.initForm}
      this.getDataList()
    },
    importHandle () {
      this.$refs.unitImport.init()
    },
    switchStatusHandle (id) {
      // 判断是否选中
      if (!id && this.dataListSelections.length === 0) {
        this.$message.warning('请选择要批量上下架的单位')
        return
      } 
      // 弹出确认框
      this.$confirm('确定要切换状态吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 传入ids, 接口switchStatusUrl
        const ids = id? id : this.dataListSelections.map(item => item.id).join(',')
        this.$http({
          url: this.$http.adornUrl(this.switchStatusUrl),
          method: 'post',
          params: this.$http.adornParams({ id: ids })
        }).then(({data}) => {
            this.getDataList()
        })
      })
    },
    // 查看详情
    viewDetailHandle (id) {
      this.detailVisible = true
      this.$nextTick(() => {
        this.$refs.viewDetail.init(id)
      })
    },
    // 荣誉管理
    honorManageHandle (unitId) {
      this.$refs.unitHonorManage.init(unitId)
    }
  }
}
</script>

<style>
.el-dropdown-link {
  cursor: pointer;
  color: #409EFF;
}

.header-new-drop li {
  color: #409EFF;
}

.el-icon-arrow-down {
  font-size: 9px;
}
</style>
