<template>
  <div class="mod-config">
    <el-form :inline="true" :model="dataForm" ref="dataForm" @keyup.enter.native="getDataList()">
      <el-form-item prop="unitName" label="单位名称: ">
        <el-input v-model="dataForm.unitName" placeholder="请输入单位名称" clearable></el-input>
      </el-form-item>
      <!-- 审核状态 -->
      <el-form-item prop="auditStatus" label="审核状态: ">
        <el-dict :code="'AU_AUDIT_STATUS'" v-model="dataForm.auditStatus"></el-dict>
      </el-form-item>

      <!-- 联系人 -->
      <el-form-item prop="contactPerson" label="联系人: ">
        <el-input v-model="dataForm.contactPerson" placeholder="请输入联系人" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-search" type="warning" @click="getDataList()">查询</el-button>
        <el-button @click="resetForm()" icon="el-icon-refresh-left">重置</el-button>
      </el-form-item>
    </el-form>
    <div class="f-s-c"
         style="padding: 5px 0;margin-bottom:15px;display: flex ;justify-content: space-between;">
      <div style="font-weight: bold;">为您查询到{{ totalPage || 0 }}条数据</div>
      <div>
        <!-- 未勾选数据时, 置灰 -->
        <el-button icon="el-icon-s-check" type="primary" @click="mutiAuditHandle()" :disabled="dataListSelections.length <= 0">
          批量审核
        </el-button>
      </div>
    </div>
    <el-table
        :data="dataList"
        border
        v-loading="dataListLoading"
        @selection-change="selectionChangeHandle"
        style="width: 100%;">
      <el-table-column
          type="selection"
          header-align="center"
          align="center"
          width="50"
          :selectable="row => row.auditStatus === 'UNIT_PENDING'">
      </el-table-column>
      <el-table-column
          prop="unitLogo"
          header-align="center"
          align="center"
          label="单位logo">
          <template slot-scope="scope">
            <el-image v-if="scope.row.unitLogo" :src="$http.adornAttachmentUrl(scope.row.unitLogo)" style="width: 50px;height: 50px;"></el-image>
            <span v-else>-</span>
          </template>
      </el-table-column>
      <el-table-column
          prop="unitName"
          header-align="center"
          align="center"
          width="180"
          label="单位名称">
      </el-table-column>
      <el-table-column
          prop="creditCode"
          header-align="center"
          align="center"
          width="180"
          label="统一社会信用代码">
      </el-table-column>
      <el-table-column
          prop="contactPerson"
          header-align="center"
          align="center"
          label="联系人">
      </el-table-column>
      <el-table-column
          prop="contactPhone"
          header-align="center"
          align="center"
          label="联系方式">
      </el-table-column>
      <el-table-column
          prop="unitNatureName"
          header-align="center"
          align="center"
          label="单位性质">
      </el-table-column>
      <el-table-column
          prop="unitTypeName"
          header-align="center"
          align="center"
          label="单位类型">
      </el-table-column>
      <el-table-column
          prop="createDate"
          header-align="center"
          align="center"
          min-width="150"
          label="注册时间">
      </el-table-column>
      <el-table-column
          prop="showStatus"
          header-align="center"
          align="center"
          label="审核状态">
        <template slot-scope="scope">
          <el-tag size="small" :type="(scope.row.auditStatus === 'UNIT_PENDING')? 'danger' : null">
            {{ scope.row['auditStatusName'] }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column
          fixed="right"
          header-align="center"
          align="center"
          width="180"
          label="操作">
        <template slot-scope="scope">
          <div v-if="scope.row.auditStatus === 'UNIT_PENDING'">
            <el-button
              type="text"
              size="small"
              @click="auditHandle(scope.row.id, 'PASS')">
            通过
          </el-button>
          <el-button
              type="text"
              size="small"
              @click="auditHandle(scope.row.id, 'REJECT')">
            驳回
          </el-button>
          </div>
          <div v-else>
            -
          </div>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
        @size-change="sizeChangeHandle"
        @current-change="currentChangeHandle"
        :current-page="pageIndex"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pageSize"
        :total="totalPage"
        layout="total, sizes, prev, pager, next, jumper">
    </el-pagination>

    <!-- 批量审核弹框 -->
    <el-dialog
      title="批量审核"
      :visible.sync="mutiAuditVisible"
      width="400px"
      :close-on-click-modal="false"
      center>
      <div class="audit-dialog-content">
        <div class="audit-info">
          <i class="el-icon-warning-outline warning-icon"></i>
          <p>已选择 {{ dataListSelections.length }} 条记录</p>
          <p>请选择审核操作</p>
        </div>
        <div class="audit-actions">
          <el-button type="primary" @click="handleMutiAudit('PASS')">通过</el-button>
          <el-button type="danger" @click="handleMutiAudit('REJECT')">驳回</el-button>
          <el-button @click="mutiAuditVisible = false">取消</el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import listMixin from '@/mixins/list-mixins'

export default {
  mixins: [listMixin],
  data() {
    return {
      mixinOptions: {
        dataUrl: '/admin/manager/advanced-unit/unit/page'
      },
      dataForm: {
        unitName: '',
        contactPerson: '',
        auditStatus: 'UNIT_PENDING'
      },
      initForm: {
        unitName: '',
        contactPerson: '',
        auditStatus: ''
      },
      auditUrl: '/admin/manager/advanced-unit/unit/audit',
      mutiAuditVisible: false, // 批量审核弹框显示控制
    }
  },
  activated() {
    this.getDataList()
  },
  components: {
  },
  methods: {
    resetForm() {
      this.$refs['dataForm'].resetFields()
      // 重置为默认查询条件
      this.dataForm = {...this.initForm}
      this.getDataList()
    },
    auditHandle(id, auditOperation) {
      // 确认框
      this.$confirm('确定要' + (auditOperation === 'PASS'? '通过' : '驳回') + '吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$http.post(this.$http.adornUrl(this.auditUrl), {
          ids: [id],
          auditOperation: auditOperation
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.$message.success('操作成功') 
            this.getDataList()
          } else {
            this.$message.warning(data.msg || '操作失败')
          }
        })
      })
    },
    mutiAuditHandle() {
      if (!this.dataListSelections || this.dataListSelections.length === 0) {
        this.$message({
          message: '请选择要审核的记录',
          type: 'warning'
        })
        return
      }
      this.mutiAuditVisible = true
    },
    
    handleMutiAudit(auditOperation) {
      const ids = this.dataListSelections.map(item => item.id)
      this.$http({
        url: this.$http.adornUrl(this.auditUrl),
        method: 'post',
        data: {
          ids: ids,
          auditOperation: auditOperation
        }
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.$message({
            message: '批量审核成功',
            type: 'success',
            duration: 1500
          })
          this.mutiAuditVisible = false // 关闭弹框
          this.getDataList()
        } else {
          this.$message.warning(data.msg || '操作失败')
        }
      }).catch(() => {
        this.$message.error('操作失败，请重试')
      })
    }
  }
}
</script>

<style>
/* 批量审核弹框样式 */
.audit-dialog-content {
  padding: 20px 0;
}

.audit-info {
  text-align: center;
  margin-bottom: 30px;
}

.audit-info .warning-icon {
  font-size: 32px;
  color: #E6A23C;
  margin-bottom: 15px;
}

.audit-info p {
  margin: 5px 0;
  font-size: 14px;
  color: #606266;
}

.audit-info p:first-of-type {
  font-size: 16px;
  font-weight: bold;
  color: #303133;
}

.audit-actions {
  text-align: center;
}

.audit-actions .el-button {
  margin: 0 10px;
  min-width: 80px;
}

/* 调整弹框样式 */
.el-dialog__header {
  padding: 15px 20px;
  border-bottom: 1px solid #eee;
}

.el-dialog__body {
  padding: 0;
}

.el-dialog__title {
  font-size: 16px;
  font-weight: bold;
}
</style>
