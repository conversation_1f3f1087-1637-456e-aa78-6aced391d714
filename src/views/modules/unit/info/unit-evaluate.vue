<template>
  <div class="mod-config">
    <el-form :inline="true" :model="dataForm" ref="dataForm" @keyup.enter.native="getDataList()">
      <el-form-item prop="standardId" label="指标版本:">
        <el-select v-model="dataForm.standardId" placeholder="请选择指标版本" style="width: 350px;">
          <el-option
            v-for="item in standardList"
            :key="item.id"
            :label="item.name"
            :value="item.id">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item prop="unitName" label="单位名称: ">
        <el-input v-model="dataForm.unitName" placeholder="请输入单位名称" clearable></el-input>
      </el-form-item>
      <el-form-item prop="creditCode" label="统一社会信用代码: ">
        <el-input v-model="dataForm.creditCode" placeholder="请输入统一社会信用代码" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-search" type="warning" @click="getDataList()">查询</el-button>
        <el-button icon="el-icon-refresh-left" @click="resetForm()">重置</el-button>
      </el-form-item>
    </el-form>
    
    <!-- <div class="f-s-c" style="padding: 5px 0;margin-bottom:15px;display: flex; justify-content: space-between;">
      <div style="font-weight: bold;">为您查询到{{ totalPage || 0 }}条数据</div>
      <div>
        <el-button type="primary" @click="exportHandle()">导出</el-button>
      </div>
    </div> -->
    
    <el-table
      :data="dataList"
      border
      v-loading="dataListLoading"
      style="width: 100%;">
      <el-table-column
        type="index"
        header-align="center"
        align="center"
        width="60"
        label="序号">
      </el-table-column>
      <el-table-column
        prop="unitLogo"
        header-align="center"
        align="center"
        min-width="100"
        label="单位logo">
        <template slot-scope="scope">
          <el-image 
            v-if="scope.row.unitLogo" 
            :src="$http.adornAttachmentUrl(scope.row.unitLogo)" 
            style="width: 50px;height: 50px;">
          </el-image>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column
        prop="unitName"
        header-align="center"
        align="center"
        min-width="180"
        label="单位名称">
      </el-table-column>
      <el-table-column
        prop="creditCode"
        header-align="center"
        align="center"
        min-width="180"
        label="统一社会信用代码">
      </el-table-column>
      <el-table-column
        prop="selfScore"
        header-align="center"
        align="center"
        min-width="100"
        label="自评总分">
      </el-table-column>
      <el-table-column
        prop="selfCount"
        header-align="center"
        align="center"
        min-width="100"
        label="自评次数">
      </el-table-column>
      <el-table-column
        prop="finalScore"
        header-align="center"
        align="center"
        min-width="100"
        label="复评总分">
      </el-table-column>
      <el-table-column
        prop="finalCount"
        header-align="center"
        align="center"
        min-width="100"
        label="复评次数">
      </el-table-column>
      <el-table-column
        prop="updateDate"
        header-align="center"
        align="center"
        min-width="160"
        label="最近测评时间">
      </el-table-column>
      <el-table-column
        fixed="right"
        header-align="center"
        align="center"
        min-width="150"
        label="操作">
        <template slot-scope="scope">
          <el-button type="text" size="small" @click="evaluateHandle(scope.row)">评测</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <el-pagination
      @size-change="sizeChangeHandle"
      @current-change="currentChangeHandle"
      :current-page="pageIndex"
      :page-sizes="[10, 20, 50, 100]"
      :page-size="pageSize"
      :total="totalPage"
      layout="total, sizes, prev, pager, next, jumper">
    </el-pagination>
    
    <!-- 评测详情页面浮层 -->
    <div class="evaluate-detail-overlay" v-if="showEvaluateDetail">
      <unit-evaluate-detail 
        ref="evaluateDetail"
        :org-id="currentEvaluateParams.orgId"
        :standard-id="currentEvaluateParams.standardId"
        @close="closeEvaluateDetail">
      </unit-evaluate-detail>
    </div>
  </div>
</template>

<script>
import listMixin from '@/mixins/list-mixins'
import UnitEvaluateDetail from './unit-evaluate-detail'

export default {
  mixins: [listMixin],
  components: {
    UnitEvaluateDetail
  },
  data() {
    return {
      mixinOptions: {
        dataUrl: '/admin/manager/advanced-unit/unit/evaluate/page',
        exportUrl: '/admin/manager/advanced-unit/unit/evaluate/export',
        exportFileName: '单位自评数据.xlsx',
        getDataListIsAuto: false
      },
      dataForm: {
        standardId: '',
        unitName: '',
        creditCode: ''
      },
      initForm: {
        standardId: '',
        unitName: '',
        creditCode: ''
      },
      standardList: [],
      // 评测详情显示控制
      showEvaluateDetail: false,
      currentEvaluateParams: {
        orgId: '',
        standardId: ''
      }
    }
  },
  created() {
    this.loadStandardList()
  },
  methods: {
    loadStandardList() {
      this.$http({
        url: this.$http.adornUrl('/admin/manager/advanced-unit/standard/list'),
        method: 'get',
        params: this.$http.adornParams({})
      }).then(({data}) => {
        if (data) {
          this.standardList = data.obj || []
          // 默认选中第一个
          if (this.standardList.length > 0) {
            this.dataForm.standardId = this.standardList[0].id
            this.getDataList()
          }
        }
      })
    },
    
    resetForm() {
      // standardId不能清空
      this.dataForm = {...this.initForm}
      this.dataForm.standardId = this.standardList[0].id

      this.getDataList()
    },
    
    exportHandle() {
      this.exportLoading = true
      this.$http({
        url: this.$http.adornUrl(this.mixinOptions.exportUrl),
        method: 'post',
        responseType: 'arraybuffer',
        data: this.$http.adornData(this.dataForm)
      }).then(({data}) => {
        this.downloadExcel(data, this.mixinOptions.exportFileName)
        this.exportLoading = false
      }).catch(() => {
        this.exportLoading = false
      })
    },
    
    // 修改评测处理方法
    evaluateHandle(row) {
      // 设置当前评测参数
      this.currentEvaluateParams = {
        orgId: row.orgId,
        standardId: this.dataForm.standardId
      }
      
      // 显示评测详情页面
      this.showEvaluateDetail = true
    },
    
    // 关闭评测详情
    closeEvaluateDetail() {
      this.showEvaluateDetail = false
      // 评测完成后刷新列表数据
      this.getDataList()
    }
  }
}
</script>

<style scoped>
.mod-config {
  padding: 20px;
  position: relative;
  height: 100%; /* 确保整个容器有高度 */
}

/* 评测详情浮层样式 - 修改为只覆盖内容区域 */
.evaluate-detail-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #fff;
  z-index: 100;
  overflow: auto;
}
</style> 
