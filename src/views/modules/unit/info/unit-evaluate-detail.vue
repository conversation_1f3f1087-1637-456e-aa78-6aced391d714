<template>
  <div class="detail-container">
    <!-- 顶部信息栏 -->
    <div class="top-info">
      <div class="back-btn" @click="closeDetail">
        <i class="el-icon-arrow-left"></i> 退出评测
      </div>
      <div class="unit-info">
        <span class="label">企业名称</span>
        <span class="value">{{ unitInfo.unitName }}</span>
      </div>
      <div class="score-info">
        <span class="label">自评总分</span>
        <span class="value highlight">{{ unitInfo.selfScore }}</span>
        <span class="label">自评次数</span>
        <span class="value">{{ unitInfo.selfCount }}</span>
        <span class="label">复评总分</span>
        <span class="value highlight">{{ unitInfo.finalScore }}</span>
        <span class="label">复评次数</span>
        <span class="value">{{ unitInfo.finalCount }}</span>
      </div>
    </div>

    <!-- 内容区域（左右布局） -->
    <el-row :gutter="20" class="content-area">
      <!-- 左侧项目列表 -->
      <el-col :span="6">
        <el-card class="box-card">
          <div slot="header" class="card-header">
            <span>测评项目指标</span>
          </div>
          <div class="item-list">
            <div class="custom-item-list">
              <div 
                v-for="item in itemList" 
                :key="item.id"
                :class="['item-row', {'item-active': currentItemId == item.id}]"
                @click="handleItemSelect(item.id)">
                <span class="item-name">{{ item.name }}</span>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>

      <!-- 右侧指标列表 -->
      <el-col :span="18">
        <el-card class="box-card">
          <div slot="header" class="card-header">
            <span>测评标准分值</span>
          </div>
          <div v-if="!currentItemId" class="empty-tip">
            <el-empty description="请先选择左侧测评项目"></el-empty>
          </div>
          <div v-else class="index-list">
            <el-table
              :data="dataList"
              stripe
              v-loading="dataListLoading"
              style="width: 100%;">
              <el-table-column
                type="index"
                label="序号"
                width="60"
                align="center">
              </el-table-column>
              <el-table-column
                prop="name"
                label="测评标准"
                min-width="200"
                show-overflow-tooltip>
              </el-table-column>
              <el-table-column
                label="分值(分)"
                width="80"
                align="center">
                <template slot-scope="scope">
                  <span>{{ scope.row.score }}</span>
                </template>
              </el-table-column>
              <el-table-column
                prop="content"
                label="测评方法及内容"
                min-width="300">
                <template slot-scope="scope">
                  <div class="multi-line-content">{{ scope.row.content }}</div>
                </template>
              </el-table-column>
              <el-table-column
                label="自评打分"
                min-width="100"
                align="center">
                <template slot-scope="scope">
                  <el-input-number 
                    v-model="scope.row.selfScore"
                    :disabled="!isAuth('zyz:au:unit:evaluate:self')"
                    :min="0" 
                    :precision="0"
                    :controls="false"
                    controls-position="right"
                    class="score-input"
                    @change="handleScoreChange">
                  </el-input-number>
                </template>
              </el-table-column>
              <el-table-column
                label="复评打分"
                min-width="100"
                align="center">
                <template slot-scope="scope">
                  <el-input-number
                    :disabled="!isAuth('zyz:au:unit:evaluate:final')"
                    v-model="scope.row.finalScore" 
                    :min="0" 
                    :precision="0"
                    :controls="false"
                    controls-position="right"
                    class="score-input"
                    @change="handleScoreChange">
                  </el-input-number>
                </template>
              </el-table-column>
              <el-table-column
                label="附件"
                width="180"
                align="center">
                <template slot-scope="scope">
                  <div v-if="scope.row.attachment && scope.row.attachment.length > 0" class="attachment-list">
                    <div v-for="(file, index) in scope.row.attachment" :key="index" class="attachment-item">
                      <i :class="getFileIcon(file.path)" class="file-icon"></i>
                      <a :href="$http.adornAttachmentUrl(file.path)" target="_blank" class="attachment-name">
                        {{ file.name }}
                      </a>
                      <i class="el-icon-delete" @click="removeAttachment(scope.row, index)"></i>
                    </div>
                  </div>
                  <el-tooltip 
                    effect="dark"
                    placement="bottom-end"
                    popper-class="file-upload-tooltip">
                    <div slot="content">
                      <p>支持以下文件格式：</p>
                      <p>• 文档：txt, pdf, doc, docx, xlsx, xls (≤10MB)</p>
                      <p>• 压缩：zip, rar (≤10MB)</p>
                      <p>• 图片：jpg, jpeg, png, gif (≤10MB)</p>
                      <p>• 音频：mp3, wma (≤10MB)</p>
                      <p>• 视频：mp4, avi (≤100MB)</p>
                    </div>
                    <el-upload
                      class="upload-demo"
                      :action="uploadUrl"
                      :show-file-list="false"
                      :headers="uploadHeaders"
                      :data="{serverCode: 'LocalServer', media: false}"
                      :on-success="(res, file) => handleUploadSuccess(res, file, scope.row)"
                      :before-upload="beforeUpload"
                      :multiple="false">
                      <el-button size="mini" type="text" icon="el-icon-upload2">上传附件</el-button>
                    </el-upload>
                  </el-tooltip>
                </template>
              </el-table-column>
            </el-table>
            
            <!-- 添加分页组件 -->
            <el-pagination
              @size-change="sizeChangeHandle"
              @current-change="currentChangeHandle"
              :current-page="pageIndex"
              :page-sizes="[10, 20, 50, 100]"
              :page-size="pageSize"
              :total="totalPage"
              layout="total, sizes, prev, pager, next, jumper">
            </el-pagination>
            
            <!-- 底部保存按钮 -->
            <div class="action-bar">
              <el-button type="primary" @click="saveEvaluate">保存</el-button>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import Vue from 'vue'
import listMixin from '@/mixins/list-mixins'
import fileUploadMixin from '@/mixins/file-upload-mixins'
import {isAuth} from "@/utils";

export default {
  mixins: [listMixin, fileUploadMixin],
  props: {
    orgId: {
      type: String,
      required: true
    },
    standardId: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      mixinOptions: {
        getDataListIsAuto: false, // 不自动加载数据
        dataUrl: '/admin/manager/advanced-unit/unit/evaluate/index/page', // 使用新的分页接口
      },
      unitInfo: {
        unitName: '',
        selfScore: 0,
        selfCount: 0,
        finalScore: 0,
        finalCount: 0
      },
      itemList: [],
      currentItemId: '',
      canSelfEvaluate: true,
      uploadUrl: this.$http.adornUrl('/admin/oss/upload'),
      uploadHeaders: {
        Authorization: Vue.cookie.get('Authorization')
      },
      uploadOptions: {
        serverCode: 'LocalServer',
        fieldName: 'attachment'
      },
    }
  },
  created() {
    // 初始化页面数据
    this.loadUnitInfo()
    this.loadItemList()
  },
  watch: {
    orgId() {
      this.resetData()
      this.loadUnitInfo()
      this.loadItemList()
    },

    standardId() {
      this.resetData()
      this.loadUnitInfo()
      this.loadItemList()
    }
  },
  methods: {
    isAuth,
    resetData() {
      this.unitInfo = {
        unitName: '',
        selfScore: 0,
        selfCount: 0,
        finalScore: 0,
        finalCount: 0
      }
      this.itemList = []
      this.currentItemId = ''
      this.dataList = [] // 使用 mixins 中的 dataList 替代 indexList
    },
    
    closeDetail() {
      this.$emit('close')
    },
    
    loadUnitInfo() {
      this.$http({
        url: this.$http.adornUrl('/admin/manager/advanced-unit/unit/evaluate/count'),
        method: 'post',
        data: this.$http.adornData({
          orgId: this.orgId,
          standardId: this.standardId
        })
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.unitInfo = data.obj || {}
        }
      })
    },
    
    loadItemList() {
      this.$http({
        url: this.$http.adornUrl('/admin/manager/advanced-unit/item/list'),
        method: 'post',
        data: this.$http.adornData({
          standardId: this.standardId
        })
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.itemList = data.obj || []
          if (this.itemList.length > 0) {
            this.currentItemId = this.itemList[0].id
            this.getIndexList()
          }
        }
      })
    },
    
    handleItemSelect(itemId) {
      this.currentItemId = itemId
      this.getIndexList()
    },
    
    getIndexList() {
      if (!this.currentItemId) return
      
      // 设置查询参数
      this.dataForm = {
        itemId: this.currentItemId,
        orgId: this.orgId
      }
      
      // 调用 mixins 中的获取数据列表方法
      this.getDataList()
    },
    
    handleScoreChange() {
      console.log("分数已更改")
    },
    
    beforeUpload(file) {
      const FileExt = file.name.replace(/.+\./, '')
      const allowedExts = 'txt,zip,rar,pdf,doc,docx,xlsx,xls,mp4,avi,mp3,wma,jpg,jpeg,png,gif'
      
      if (allowedExts.indexOf(FileExt.toLowerCase()) === -1) {
        this.$message.error('上传失败，不支持的文件格式')
        return false
      }
      
      // 判断文件类型并限制大小
      const isDocOrAudio = 'txt,zip,rar,pdf,doc,docx,xlsx,xls,mp3,wma,jpg,jpeg,png,gif'.indexOf(FileExt.toLowerCase()) !== -1
      const isVideo = 'mp4,avi'.indexOf(FileExt.toLowerCase()) !== -1
      
      if (isDocOrAudio && file.size / 1024 / 1024 > 10) {
        this.$message.error('文档、图片或音频文件大小不能超过 10MB')
        return false
      }
      
      if (isVideo && file.size / 1024 / 1024 > 100) {
        this.$message.error('视频文件大小不能超过 100MB')
        return false
      }
      
      return true
    },
    
    handleUploadSuccess(res, file, row) {
      if (res && res.code === 0) {
        // 初始化附件数组
        if (!row.attachment) {
          row.attachment = []
        }
        
        // 添加新上传的附件
        row.attachment.push({
          name: res.obj.fileName,
          path: res.obj.path
        })
        
        this.$message({
          message: '附件上传成功',
          type: 'success',
          duration: 1500
        })

      } else {
        this.$message.error('上传失败：' + (res.msg || '未知错误'))
      }
    },
    
    removeAttachment(row, index) {
      this.$confirm('确定要删除此附件吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        row.attachment.splice(index, 1)
        this.$message({
          type: 'success',
          message: '删除成功!'
        })
      }).catch(() => {})
    },
    
    saveEvaluate() {
      // 修改为使用 dataList
      const scoreData = this.dataList.map(index => {
        return {
          orgId: this.orgId,
          standardId: index.standardId,
          indexId: index.indexId,
          itemId: index.itemId,
          selfScore: index.selfScore || 0,
          finalScore: index.finalScore || 0,
          attachment: index.attachment || []
        }
      })
      console.log(scoreData)
      this.$http({
        url: this.$http.adornUrl('/admin/manager/advanced-unit/unit/evaluate/save-batch'),
        method: 'post',
        data: scoreData
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.$message.success('保存成功')
          this.loadUnitInfo()
        } else {
          this.$message.error(data.msg || '保存失败')
        }
      }).catch(() => {
        this.$message.error('保存失败，请稍后再试')
      })
    },
    
    getFileIcon(path) {
      const FileExt = path.replace(/.+\./, '').toLowerCase()
      switch (FileExt) {
        case 'txt':
        case 'doc':
        case 'docx':
        case 'xlsx':
        case 'xls':
          return 'el-icon-document'
        case 'pdf':
          return 'el-icon-pdf'
        case 'zip':
        case 'rar':
          return 'el-icon-zip'
        case 'jpg':
        case 'jpeg':
        case 'png':
        case 'gif':
          return 'el-icon-picture'
        case 'mp3':
        case 'wma':
          return 'el-icon-audio'
        case 'mp4':
        case 'avi':
          return 'el-icon-video'
        default:
          return 'el-icon-document'
      }
    }
  }
}
</script>

<style scoped>
/* 调整样式适应内容区域 */
.detail-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  background-color: #fff;
}

/* 顶部信息栏样式 */
.top-info {
  display: flex;
  align-items: center;
  padding: 10px 15px;
  border-bottom: 1px solid #e4e7ed;
  background-color: #f5f7fa;
}

.back-btn {
  cursor: pointer;
  padding: 0 10px;
  color: #409EFF;
}

.unit-info {
  flex: 1;
  margin-left: 15px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.score-info {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}

.label {
  color: #606266;
  margin-right: 5px;
  margin-left: 10px;
}

.value {
  font-weight: bold;
  margin-right: 10px;
}

.highlight {
  color: #409EFF;
  font-size: 16px;
}

/* 主体内容区样式 */
.content-area {
  margin-top: 15px;
  flex: 1;
  padding: 0 15px;
  overflow: auto;
  height: calc(100% - 50px);
}

.card-header {
  padding: 10px;
  font-size: 14px;
}

/* 左侧项目列表样式 */
.item-list {
  max-height: 500px; /* 限制最大高度，适应内容区 */
  overflow-y: auto;
}

.custom-item-list {
  width: 100%;
}

.item-row {
  padding: 10px 15px;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: background-color 0.3s;
  border-bottom: 1px solid #f0f0f0;
  font-size: 13px;
}

.item-row:hover {
  background-color: #f5f7fa;
}

.item-active {
  background-color: #ecf5ff;
  color: #409EFF;
}

.item-name {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 多行内容显示样式 */
.multi-line-content {
  line-height: 1.4;
  max-height: 8.4em; /* 6行文字 */
}

/* 附件样式修改 */
.attachment-list {
  margin-bottom: 0px;
  max-height: 80px;
  overflow-y: auto;
}

.attachment-item {
  display: flex;
  align-items: center;
  margin-bottom: 5px;
  padding: 2px 0;
}

.attachment-name {
  margin-right: 5px;
  font-size: 12px;
  color: #409EFF;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 120px;
}

.attachment-item i {
  cursor: pointer;
  color: #F56C6C;
  font-size: 12px;
  margin-left: auto;
}

.el-upload__tip {
  line-height: 1.2;
  font-size: 12px;
  color: #909399;
  margin: 2px 0 0;
}

.upload-demo {
  margin-top: 0px;
}

/* 底部操作栏 */
.action-bar {
  text-align: center;
  margin-top: 15px;
  padding: 5px 0;
}

/* 空提示 */
.empty-tip {
  padding: 30px 0;
}

/* 表格区域样式调整 */
.el-table {
  font-size: 13px;
}

.el-card {
  margin-bottom: 15px;
}

/* 修复分数输入框圆角显示问题 */
.score-input {
  margin-right: 0px;
  width: 90%;
}

/* 文件上传工具提示样式 */
:global(.file-upload-tooltip) {
  max-width: 300px !important;
  padding: 12px !important;
}

:global(.file-upload-tooltip p) {
  margin: 0;
  line-height: 1.5;
  font-size: 13px;
  text-align: left;
}

:global(.file-upload-tooltip p:first-child) {
  margin-bottom: 5px;
  font-weight: bold;
}
</style> 
