<template>
  <el-dialog
      :title="isView ? '详情查看' : !dataForm.id ? '新增' : '修改'"
      :close-on-click-modal="false"
      width="60%"
      :visible.sync="visible">
    <el-form :model="dataForm" :rules="isView ? {} : dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmit()"
             label-width="140px" :class="{ 'view-mode': isView }">
      <el-form-item label="单位名称" prop="unitName" :error="errors['unitName']">
        <el-input v-model="dataForm.unitName" placeholder="单位名称" :disabled="isView"></el-input>
      </el-form-item>
      <el-form-item label="统一社会信用代码" prop="creditCode" :error="errors['creditCode']">
        <el-input v-model="dataForm.creditCode" placeholder="统一社会信用代码" :disabled="isView"></el-input>
      </el-form-item>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="联系人" prop="contactPerson" :error="errors['contactPerson']">
            <el-input v-model="dataForm.contactPerson" placeholder="联系人" :disabled="isView"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="联系方式" prop="contactPhone" :error="errors['contactPhone']">
            <el-input v-model="dataForm.contactPhone" placeholder="联系方式" :disabled="isView"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-form-item label="详细地址" prop="address" :error="errors['address']">
        <!-- 输入框, 尾部加上地图图标, 点击输入框打开地图   -->
        <el-input v-model="dataForm.address" placeholder="点击选择地址" readonly :disabled="isView" @click.native="openMapView">
          <template #suffix>
            <i class="el-icon-map-location" style="cursor: pointer"></i>
          </template>
        </el-input>
      </el-form-item>
      <el-form-item label="经纬度" prop="coordinates" :error="errors['coordinates']">
        <el-input v-model="dataForm.coordinates" placeholder="请从地图中选择位置" readonly :disabled="isView" @click.native="openMapView">
          <template #suffix>
            <i class="el-icon-map-location" style="cursor: pointer"></i>
          </template>
        </el-input>
      </el-form-item>
      <el-form-item label="荣誉等级" prop="honorLevel" :error="errors['honorLevel']">
        <el-dict :code="'AU_HONOR_LEVEL'" v-model="dataForm.honorLevel" placeholder="荣誉等级" :disabled="isView"></el-dict>
      </el-form-item>
      <el-form-item label="单位性质" prop="unitNature" :error="errors['unitNature']">
        <el-dict :code="'AU_NATURE'" v-model="dataForm.unitNature" placeholder="单位性质" :disabled="isView"></el-dict>
      </el-form-item>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="单位类型" prop="unitType" :error="errors['unitType']">
            <el-dict :code="'AU_TYPE'" v-model="dataForm.unitType" placeholder="单位类型" :disabled="isView"></el-dict>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="主管部门" prop="orgCodeList" :error="errors['orgCodeList']">
            <el-cascader
                placeholder="主管部门"
                v-model="dataForm.orgCodeList"
                :options="orgList"
                style="width: 100%"
                :show-all-levels="false"
                :disabled="isView"
                :props="{checkStrictly: true,label: 'name',value: 'code'}"/>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="关联团队" prop="relatedTeams" :error="errors['relatedTeams']">
            <!-- 自定义select框 -->
            <el-select
                v-model="dataForm.relatedTeams"
                placeholder="关联团队"
                filterable
                :disabled="isView"
                style="width: 100%">
              <el-option
                  v-for="item in relatedTeamsOptions"
                  :key="item.value"
                  :label="item.text"
                  :value="item.id"/>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-form-item label="单位简介" prop="introduction" :error="errors['introduction']">
        <el-input v-model="dataForm.introduction" type="textarea" :rows="3" placeholder="单位简介" :disabled="isView"></el-input>
      </el-form-item>
      <el-form-item label="单位logo" prop="unitLogo" :error="errors['unitLogo']">
        <el-upload
            class="avatar-uploader"
            :action="uploadUrl"
            :headers="myHeaders"
            :show-file-list="false"
            :data="uploadData"
            :on-success="handleImageSuccess"
            :disabled="isView"
        >
          <img v-if="dataForm.unitLogo" :src="$http.adornAttachmentUrl(dataForm.unitLogo)" class="avatar">
          <i v-else-if="!isView" class="el-icon-plus avatar-uploader-icon"/>
          <div slot="tip" class="el-upload__tip" style="color: red" v-if="!isView">jpg，png的图片，并且大小不能超过2M， 建议尺寸 500 * 300</div>
        </el-upload>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">{{ isView ? '关闭' : '取消' }}</el-button>
      <el-button type="primary" @click="dataFormSubmit()" v-if="!isView">确定</el-button>
    </span>
    <!-- 地图弹窗 -->
    <el-dialog
        class="map-dialog"
        title="地图"
        width="80%"
        :visible.sync="mapVisible"
        :close-on-click-modal="false"
        :append-to-body="true"
    >
      <!-- 这里示例使用自定义 AMapInfo 组件，如不需要可自行移除 -->
      <AMapInfo
          :mapVisible.sync="mapVisible"
          :address.sync="dataForm.address"
          :latitude.sync="dataForm.latitude"
          :longitude.sync="dataForm.longitude"
          :radius="500"
          :showHeaderTip="false"
          @handleMapClose="handleMapClose"
          @update:longitude="handleCoordinatesChange"
          @update:latitude="handleCoordinatesChange"
      />
    </el-dialog>
  </el-dialog>
</template>

<script>
import moment from 'moment'
import editMixin from '@/mixins/edit-mixins'
import AMapInfo from '@/components/map/a-map-info'
import Vue from 'vue'

export default {
  mixins: [editMixin],
  props: {
    isView: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      editOptions: {
        initUrl: '/admin/manager/advanced-unit/unit/detail',
        submitUrl: '/admin/manager/advanced-unit/unit',
        saveSuffix: 'submit',
        updateSuffix: 'edit',
      },
      mapVisible: false,
      uploadUrl: this.$http.adornUrl('/admin/oss/upload'),
      myHeaders: {Authorization: Vue.cookie.get('Authorization')},
      uploadData: {serverCode: 'LocalServer', media: false},
      relatedTeamsOptions: [],
      orgList: [],
      teamUrl: '/admin/zyz/team/teamIdTextList',
      initForm: {
        unitName: '',
        creditCode: '',
        contactPerson: '',
        contactPhone: '',
        address: '',
        coordinates: '',
        honorLevel: '',
        unitNature: '',
        unitType: '',
        governingDepartment: '',
        introduction: '',
        civilizationYear: '',
        relatedTeams: '',
        unitLogo: '',
        auditStatus: '',
        auditAt: '',
        auditUser: '',
        showStatus: '',
        id: ''
      },
      dataRule: {
        unitName: [
          {required: true, message: '单位名称不能为空', trigger: 'blur'}
        ],
        creditCode: [
          {required: true, message: '统一社会信用代码不能为空', trigger: 'blur'},
          {pattern: /^[0-9A-HJ-NPQRTUWXY]{18}$/, message: '统一社会信用代码格式不正确', trigger: 'blur'}
        ], 
        contactPerson: [
          // 联系人非必填，但可以添加格式验证
          {min: 2, max: 20, message: '联系人长度在2-20个字符之间', trigger: 'blur'}
        ], 
        contactPhone: [
          // 联系方式非必填，但可以添加格式验证
          {pattern: /^1[3-9]\d{9}$/, message: '手机号格式不正确', trigger: 'blur'}
        ], 
        address: [
          {required: true, message: '详细地址不能为空', trigger: 'blur'}
        ],
        coordinates: [
          {required: true, message: '请从地图中选择位置获取经纬度', trigger: 'blur'}
        ],
        honorLevel: [
          {required: true, message: '荣誉等级不能为空', trigger: 'blur'}
        ], 
        unitNature: [
          {required: true, message: '单位性质不能为空', trigger: 'blur'}
        ], 
        unitType: [
          {required: true, message: '单位类型不能为空', trigger: 'blur'}
        ],
        orgCodeList: [
          // 主管部门非必填
        ], 
        introduction: [
          {required: true, message: '单位简介不能为空', trigger: 'blur'}
        ],
        relatedTeams: [
          {required: true, message: '关联团队不能为空', trigger: 'blur'}
        ], 
        unitLogo: [
          // Logo非必填
        ]
      }
    }
  },
  components: {
    moment,
    AMapInfo
  },
  computed: {
    coordinatesDisplay() {
      if (this.dataForm.longitude && this.dataForm.latitude) {
        return `${this.dataForm.longitude}, ${this.dataForm.latitude}`
      }
      return ''
    }
  },
  methods: {
    openMapView() {
      this.mapVisible = true
    },
    handleCoordinatesChange() {
      // 当经纬度变化时，更新coordinates并触发校验
      this.$nextTick(() => {
        this.dataForm.coordinates = this.dataForm.longitude && this.dataForm.latitude ? 
          `${this.dataForm.longitude},${this.dataForm.latitude}` : ''
        this.$refs['dataForm'].validateField('coordinates')
      })
    },
    submitBeforeHandle() {
      this.dataForm.governingDepartment = this.dataForm.orgCodeList && this.dataForm.orgCodeList.length > 0
          ? this.dataForm.orgCodeList[this.dataForm.orgCodeList.length - 1]
          : null
    },
    handleImageSuccess(res, file) {
      if (res.success) {
        this.dataForm.unitLogo = res.obj.path
        this.$message.success('上传成功')
      } else {
        this.$message.error('上传失败')
      }
    },
    beforeImageUpload(file) {
      const isImage =
          file.type === 'image/jpeg' ||
          file.type === 'image/png' ||
          file.type === 'image/bmp'
      const isLt2M = file.size / 1024 / 1024 < 2
      
      if (!isImage) {
        this.$message.error('上传图片只能是JPG/PNG/BMP格式!')
      }
      if (!isLt2M) {
        this.$message.error('上传图片大小不能超过2MB!')
      }
      
      return isImage && isLt2M
    },
    async getRelatedTeams() {
      /* 仿照this.$http({
            url: this.$http.adornUrl(url),
            method: 'post',
            data: this.$http.adornData(this.dataForm)
          }) */
      const { data } = await this.$http({
        url: this.$http.adornUrl(this.teamUrl),
        method: 'get',
        params: this.$http.adornParams({orgId: this.dataForm.id})
      })
      if (data && data.code === 0) {
        this.relatedTeamsOptions = data.obj
      }
    },
    initBeforeHandle() {
      if (!this.dataForm.id) {
        this.dataForm = { ...this.initForm, id: this.dataForm.id }
      }
    },
    handleMapClose() {
      // 校验地图必填
      this.$refs['dataForm'].validateField('address')
      this.$refs['dataForm'].validateField('coordinates')
    },
    getOrg() {
      this.$http({
        url: this.$http.adornUrl(`/admin/org/getOrgTree`),
        method: 'get',
        params: this.$http.adornParams()
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.orgList = data.obj
        } else {
          this.orgList = []
        }
      })
    },
    validateCoordinates(rule, value, callback) {
      if (!this.dataForm.longitude || !this.dataForm.latitude) {
        callback(new Error('为获取经纬度, 请先从地图中选择地址'))
      } else {
        callback()
      }
    },
    initCallback (data) {
      // 初始化回调函数
      this.dataForm = data
      // 如果存在 governingDepartment，将其转换为 orgCodeList 数组
      if (data && data.governingDepartment) {
        // 从组织树中查找完整的组织路径
        const findOrgPath = (orgList, targetCode, path = []) => {
          for (const org of orgList) {
            if (org.code === targetCode) {
              return [...path, org.code]
            }
            if (org.children) {
              const result = findOrgPath(org.children, targetCode, [...path, org.code])
              if (result) {
                return result
              }
            }
          }
          return null
        }
        
        // 查找并设置完整的组织路径
        const orgPath = findOrgPath(this.orgList, data.governingDepartment)
        this.dataForm.orgCodeList = orgPath || [data.governingDepartment]
      } else {
        this.dataForm.orgCodeList = []
      }
    },
    dataFormSubmit() {
      if (this.isView) {
        this.visible = false
        return
      }
      
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.clearErrors()
          this.submitBeforeHandle()
          let submitUrl = this.editOptions.submitUrl || this.editOptions.initUrl
          if (!this.editOptions.isUniteUrl) {
            submitUrl = `${submitUrl}/${!this.dataForm.id ? this.editOptions.saveSuffix : this.editOptions.updateSuffix}`
          }
          this.dataFormLoading = true
          this.$http({
            url: this.$http.adornUrl(submitUrl),
            method: 'post',
            data: this.$http.adornData(this.dataForm)
          }).then(({data}) => {
            if (data && data.code === 0) {
              return this.$message({
                message: '操作成功',
                type: 'success',
                duration: 1000,
                onClose: () => {
                  this.dataFormLoading = false
                  this.visible = false
                  if(this.editOptions.isEmit) {
                    this.$emit(this.editOptions.emit)
                  }
                }
              })
            }
            if (data && data.code === 303) {
              let errors = {}
              for (let it of data.obj) {
                errors[`${it.field}`] = it.message
              }
              this.errors = _.cloneDeep(errors)
            } else {
              this.$message.error(data.msg)
            }
            this.dataFormLoading = false
          }).catch(() => {
            this.dataFormLoading = false
          })
        }
      })
    },
    async init (id) {
      this.dataForm.id = id || null
      if (!id) {
        this.dataForm.createDate = null
        this.dataForm.creator = null
        this.dataForm.version = 0
      }
      await this.initBeforeHandle()
      this.visible = true
      this.$nextTick(() => {
        if (this.isView) {
          // 详情模式不需要重置表单，只加载数据
          if (this.dataForm.id) {
            this.getData()
          }
        } else {
          // 编辑模式需要重置表单
          this.$refs['dataForm']?.resetFields()
          if (this.dataForm.id) {
            this.getData()
          }
        }
      })
    },
  },
  mounted() {
    this.getRelatedTeams()
    this.getOrg()
  },
  activated() {
  }
}
</script>

<style>
.avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}
.avatar-uploader .el-upload:hover {
  border-color: #409EFF;
}
.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  line-height: 178px;
  text-align: center;
}
.avatar {
  width: 178px;
  height: 178px;
  display: block;
}

/* 详情模式下的样式 */
.view-mode .el-input.is-disabled .el-input__inner,
.view-mode .el-textarea.is-disabled .el-textarea__inner,
.view-mode .el-radio.is-disabled,
.view-mode .el-checkbox.is-disabled,
.view-mode .el-select .el-input.is-disabled .el-input__inner,
.view-mode .el-cascader.is-disabled .el-input .el-input__inner,
.view-mode .el-input-number.is-disabled .el-input__inner {
  background-color: #fff;
  color: #606266;
  border-color: #EBEEF5;
}
</style>
