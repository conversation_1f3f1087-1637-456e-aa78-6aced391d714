<template>
  <el-dialog
      :close-on-click-modal="false"
      width="960"
      :visible.sync="visible">
  <el-descriptions title="详情" :column="3" border>
    <template slot="extra">
      <el-button type="primary" size="small">编辑</el-button>
    </template>
    <el-descriptions-item label="单位名称">{{dataForm.unitName}}</el-descriptions-item>
    <el-descriptions-item label="统一社会信用代码（18位）">{{dataForm.creditCode}}</el-descriptions-item>
    <el-descriptions-item label="联系人">{{dataForm.contactPerson}}</el-descriptions-item>
    <el-descriptions-item label="联系方式">{{dataForm.contactPhone}}</el-descriptions-item>
    <el-descriptions-item label="详细地址">{{dataForm.address}}</el-descriptions-item>
    <el-descriptions-item label="经纬度（经度,纬度）">{{dataForm.coordinates}}</el-descriptions-item>
    <el-descriptions-item label="荣誉等级">{{dataForm.honorLevel}}</el-descriptions-item>
    <el-descriptions-item label="单位性质（党政机关/事业单位等）">{{dataForm.unitNature}}</el-descriptions-item>
    <el-descriptions-item label="单位类型">{{dataForm.unitType}}</el-descriptions-item>
    <el-descriptions-item label="主管部门">{{dataForm.governingDepartment}}</el-descriptions-item>
    <el-descriptions-item label="单位简介">{{dataForm.introduction}}</el-descriptions-item>
    <el-descriptions-item label="文明单位年份">{{dataForm.civilizationYear}}</el-descriptions-item>
    <el-descriptions-item label="关联团队">{{dataForm.relatedTeams}}</el-descriptions-item>
    <el-descriptions-item label="单位logo">{{dataForm.unitLogo}}</el-descriptions-item>
    <el-descriptions-item label="审核状态">{{dataForm.auditStatus}}</el-descriptions-item>
    <el-descriptions-item label="审核时间">{{dataForm.auditAt}}</el-descriptions-item>
    <el-descriptions-item label="审核人">{{dataForm.auditUser}}</el-descriptions-item>
    <el-descriptions-item label="展示状态">{{dataForm.showStatus}}</el-descriptions-item>
  </el-descriptions>
  </el-dialog>
</template>

<script>
import moment from 'moment'
  export default {
    data () {
      return {
        visible: false,
        dataForm: {},
        initForm: {
          id: null,
          version: null,
          unitName: '',
          creditCode: '',
          contactPerson: '',
          contactPhone: '',
          address: '',
          coordinates: '',
          honorLevel: '',
          unitNature: '',
          unitType: '',
          governingDepartment: '',
          introduction: '',
          civilizationYear: '',
          relatedTeams: '',
          unitLogo: '',
          auditStatus: '',
          auditAt: '',
          auditUser: '',
          showStatus: ''
        },
        dataRule: {
          creditCode: [
            { required: true, message: '统一社会信用代码（18位）不能为空', trigger: 'blur' }
          ],
          contactPerson: [
            { required: true, message: '联系人不能为空', trigger: 'blur' }
          ],
          contactPhone: [
            { required: true, message: '联系方式不能为空', trigger: 'blur' }
          ],
          address: [
            { required: true, message: '详细地址不能为空', trigger: 'blur' }
          ],
          coordinates: [
            { required: true, message: '经纬度（经度,纬度）不能为空', trigger: 'blur' }
          ],
          honorLevel: [
            { required: true, message: '荣誉等级不能为空', trigger: 'blur' }
          ],
          unitNature: [
            { required: true, message: '单位性质（党政机关/事业单位等）不能为空', trigger: 'blur' }
          ],
          unitType: [
            { required: true, message: '单位类型不能为空', trigger: 'blur' }
          ],
          governingDepartment: [
            { required: true, message: '主管部门不能为空', trigger: 'blur' }
          ],
          introduction: [
            { required: true, message: '单位简介不能为空', trigger: 'blur' }
          ],
          civilizationYear: [
            { required: true, message: '文明单位年份不能为空', trigger: 'blur' }
          ],
          relatedTeams: [
            { required: true, message: '关联团队不能为空', trigger: 'blur' }
          ],
          unitLogo: [
            { required: true, message: '单位logo不能为空', trigger: 'blur' }
          ],
          auditStatus: [
            { required: true, message: '审核状态不能为空', trigger: 'blur' }
          ],
          auditAt: [
            { required: true, message: '审核时间不能为空', trigger: 'blur' }
          ],
          auditUser: [
            { required: true, message: '审核人不能为空', trigger: 'blur' }
          ],
          showStatus: [
            { required: true, message: '展示状态不能为空', trigger: 'blur' }
          ]
        },
        unitNameError: null,
        creditCodeError: null,
        contactPersonError: null,
        contactPhoneError: null,
        addressError: null,
        coordinatesError: null,
        honorLevelError: null,
        unitNatureError: null,
        unitTypeError: null,
        governingDepartmentError: null,
        introductionError: null,
        civilizationYearError: null,
        relatedTeamsError: null,
        unitLogoError: null,
        auditStatusError: null,
        auditAtError: null,
        auditUserError: null,
        showStatusError: null
      }
    },
    components: {
        moment
    },
    methods: {
      init (id) {
        this.dataForm.id = id || null
        this.visible = true
        this.$nextTick(() => {
          this.$refs['dataForm'].resetFields()
          if (this.dataForm.id) {
            this.$http({
              url: this.$http.adornUrl(`/auunit`),
              method: 'get',
              params: this.$http.adornParams({ id: this.dataForm.id })
            }).then(({data}) => {
              if (data && data.code === 0) {
                this.dataForm = data.obj
              }
            })
          }
        })
      },
      // 表单提交
      dataFormSubmit () {
        this.$refs['dataForm'].validate((valid) => {
          if (valid) {
            this.$http({
              url: this.$http.adornUrl(`/auunit/${!this.dataForm.id ? 'save' : 'update'}`),
              method: 'post',
              data: this.$http.adornData(this.dataForm)
            }).then(({data}) => {
              if (data && data.code === 0) {
                this.$message({
                  message: '操作成功',
                  type: 'success',
                  duration: 1500,
                  onClose: () => {
                    this.visible = false
                    this.$emit('refreshDataList')
                  }
                })
              } else if (data && data.code === 303) {
                for (let it of data.obj) {
                  this[`${it.field}Error`] = it.message
                }
              } else {
                this.$message.error(data.msg)
              }
            })
          }
        })
      }
    }
  }
</script>
