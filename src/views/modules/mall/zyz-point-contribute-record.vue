<template>
	<div class="mod-config">
		<el-form :inline="true" ref="dataForm" :model="dataForm" @keyup.enter.native="queryPage()">
			<el-form-item label="志愿者名称" prop="name">
				<el-input v-model="dataForm.name" placeholder="志愿者名称" clearable></el-input>
			</el-form-item>
			<el-form-item label="志愿者证件编号" prop="certificateId">
				<el-input v-model="dataForm.certificateId" placeholder="志愿者证件编号" clearable></el-input>
			</el-form-item>
			<el-form-item label="积分" prop="minPoint">
				<el-input v-model="dataForm.minPoint" placeholder="积分最小值"></el-input>
			</el-form-item>
			<el-form-item label="~" prop="maxPoint">
				<el-input v-model="dataForm.maxPoint" placeholder="积分最大值"></el-input>
			</el-form-item>
      <el-form-item label="捐赠时间" prop="dateRange">
        <el-date-picker v-model="dataForm.dateRange" clearable type="daterange" value-format="yyyy-MM-dd"
                        align="right" start-placeholder="开始" end-placeholder="结束">
        </el-date-picker>
      </el-form-item>
			<el-form-item>
        <el-button type="warning" icon="el-icon-search" @click="queryPage()">查询</el-button>
        <el-button icon="el-icon-refresh-left" @click="resetField()">重置</el-button>
			</el-form-item>
      <el-form-item style="float:right;">
        <div style="float: right;background-color: #efefef; border-radius: 5px">
          <div style="float: right; font-size: 18px; font-weight:bolder; padding: 10px">
            <span style="margin-right: 20px">捐赠数量(人次/人数)：{{ sumInfo ? sumInfo.personNum : '--' }}/{{ sumInfo ? sumInfo.contributeSum : '--' }}</span>
            <span>累计捐赠积分数量：{{ sumInfo ? sumInfo.contributePointSum : '--' }}</span>
          </div>
        </div>
      </el-form-item>
		</el-form>
		<el-table :data="dataList" border v-loading="dataListLoading" @selection-change="selectionChangeHandle"
			style="width: 100%;">
			<el-table-column type="index" header-align="center" align="center" width="50" label="序号">
			</el-table-column>
			<el-table-column prop="volunteerName" header-align="center" align="center" label="志愿者用户名称">
			</el-table-column>
			<el-table-column prop="certificateId" header-align="center" align="center" label="志愿者证件编号">
			</el-table-column>
			<el-table-column prop="phone" header-align="center" align="center" label="志愿者联系方式">
			</el-table-column>
			<el-table-column prop="contributePoint" header-align="center" align="center" label="捐赠积分">
			</el-table-column>
			<el-table-column prop="orgName" header-align="center" align="center" label="受捐机构">
			</el-table-column>
			<el-table-column prop="contributeDate" header-align="center" align="center" label="捐赠时间">
			</el-table-column>
		</el-table>
		<el-pagination @size-change="sizeChangeHandle" @current-change="currentChangeHandle" :current-page="pageIndex"
			:page-sizes="[10, 20, 50, 100]" :page-size="pageSize" :total="totalPage"
			layout="total, sizes, prev, pager, next, jumper">
		</el-pagination>
		<!-- 弹窗, 新增 / 修改 -->
	</div>
</template>

<script>
	export default {
		data() {
			return {
				dataForm: {
					name: '',
					maxPoint: '',
					minPoint: '',
					certificateId: '',
          dateRange: null
				},
        sumInfo: null,
				dataList: [],
				pageIndex: 1,
				pageSize: 10,
				totalPage: 0,
				dataListLoading: false,
				dataListSelections: [],
				addOrUpdateVisible: false
			}
		},
		components: {

		},
		activated() {
			this.queryPage()
		},
		methods: {
      resetField () {
        this.$refs['dataForm']?.resetFields()
        this.getDataList()
        this.getSum()
      },
			queryPage() {
				this.pageIndex = 1
				this.getDataList()
        this.getSum()
			},
      // 获取数据列表
      getSum() {
        this.$http({
          url: this.$http.adornUrl('/admin/point/contribute-record/getSum'),
          method: 'post',
          data: this.$http.adornData({
            'currentPage': this.pageIndex,
            'pageSize': this.pageSize,
            'name': this.dataForm.name,
            'certificateId': this.dataForm.certificateId,
            'maxPoint': this.dataForm.maxPoint,
            'minPoint': this.dataForm.minPoint,
            'start': this.dataForm.dateRange && this.dataForm.dateRange.length === 2 ? this.dataForm.dateRange[0] : null,
            'end': this.dataForm.dateRange && this.dataForm.dateRange.length === 2 ? this.dataForm.dateRange[1] : null,
          })
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.sumInfo = data.obj
          } else {
            this.sumInfo = null
            this.$message.error(data.msg)
          }
        })
      },
			// 获取数据列表
			getDataList() {
				this.dataListLoading = true
				this.$http({
					url: this.$http.adornUrl('/admin/point/contribute-record/getPages'),
					method: 'post',
					data: this.$http.adornData({
						'currentPage': this.pageIndex,
						'pageSize': this.pageSize,
						'name': this.dataForm.name,
						'certificateId': this.dataForm.certificateId,
						'maxPoint': this.dataForm.maxPoint,
						'minPoint': this.dataForm.minPoint,
            'start': this.dataForm.dateRange && this.dataForm.dateRange.length === 2 ? this.dataForm.dateRange[0] : null,
            'end': this.dataForm.dateRange && this.dataForm.dateRange.length === 2 ? this.dataForm.dateRange[1] : null,
					})
				}).then(({
					data
				}) => {
					if (data && data.code === 0) {
						this.dataList = data.obj.records
						this.totalPage = data.obj.total
					} else {
						this.dataList = []
						this.totalPage = 0
					}
					this.dataListLoading = false
				})
			},
			// 每页数
			sizeChangeHandle(val) {
				this.pageSize = val
				this.pageIndex = 1
				this.getDataList()
			},
			// 当前页
			currentChangeHandle(val) {
				this.pageIndex = val
				this.getDataList()
			},
			// 多选
			selectionChangeHandle(val) {
				this.dataListSelections = val
			},
			// 新增 / 修改
			addOrUpdateHandle(id) {
				this.addOrUpdateVisible = true
				this.$nextTick(() => {
					this.$refs.addOrUpdate.init(id)
				})
			},
			// 删除
			deleteHandle(id) {
				var ids = id ? [id] : this.dataListSelections.map(item => {
					return item.id
				})
				this.$confirm(`确定进行[${id ? '删除' : '批量删除'}]操作?`, '提示', {
					confirmButtonText: '确定',
					cancelButtonText: '取消',
					type: 'warning',
					closeOnClickModal: false
				}).then(() => {
					this.$http({
						url: this.$http.adornUrl('/zyzpointcontributerecord/removeByIds'),
						method: 'get',
						params: this.$http.adornParams({
							'ids': ids.join(',')
						})
					}).then(({
						data
					}) => {
						if (data && data.code === 0) {
							this.$message({
								message: '操作成功',
								type: 'success',
								duration: 1500,
								onClose: () => {
									this.getDataList()
								}
							})
						} else {
							this.$message.error(data.msg)
						}
					})
				})
			}
		}
	}
</script>
