<template>
  <div class="mod-config">
    <el-form :inline="true" ref="dataForm" :model="dataForm" @keyup.enter.native="queryPage()">
      <el-form-item label="关键字" prop="keyWord">
        <el-input v-model="dataForm.keyWord" placeholder="商品名称/兑换人姓名/联系电话" clearable></el-input>
      </el-form-item>
      <el-form-item label="订单编号" prop="orderNum">
        <el-input v-model="dataForm.orderNum" placeholder="订单编号" clearable></el-input>
      </el-form-item>
      <el-form-item :label="'所属组织机构'" prop="orgCode">
        <el-select placeholder="所属组织机构" v-model="dataForm.orgCode" clearable>
          <el-option v-for="item in orgList"
                     :key="item.code" :label="item.name" :value="item.code"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item :label="'状态'" prop="status">
        <el-dict :code="'mall_exchange_status'" v-model="dataForm.status"></el-dict>
      </el-form-item>
      <el-form-item :label="'兑换来源'" prop="exchangeType">
        <el-dict :code="'mall_exchange_type'" v-model="dataForm.exchangeType"></el-dict>
      </el-form-item>
      <el-form-item :label="'邮政状态'" prop="emsStatus">
        <el-dict :code="'mall_exchange_ems_status'" v-model="dataForm.emsStatus"></el-dict>
      </el-form-item>
      <el-form-item label="兑换时间" prop="timeRange">
          <el-date-picker v-model="dataForm.timeRange" clearable type="datetimerange"
                          value-format="yyyy-MM-dd HH:mm:ss" align="right" start-placeholder="开始时间" end-placeholder="结束时间"    :default-time="['00:00:00', '23:59:59']">
          </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="warning" icon="el-icon-search" @click="queryPage()">查询</el-button>
        <el-button icon="el-icon-refresh-left" @click="resetField()">重置</el-button>
      </el-form-item>
    </el-form>
    <div class="f-s-c"
         style="padding: 15px 0;display: flex;justify-content:space-between;align-items: center;float:right ">
      <el-button type="warning" @click="batchEMSOrder()">EMS批量下单</el-button>
      <el-button icon="el-icon-download" type="success" v-loading.fullscreen.lock="fullscreenLoading" @click="exportExchange()">导出</el-button>
      <el-button icon="el-icon-refresh"type="info" @click="getDataList()">刷新</el-button>
    </div>
    <el-table :data="dataList" border v-loading="dataListLoading" @selection-change="selectionChangeHandle"
              style="width: 100%;">
      <el-table-column prop="exchangeTypeText" header-align="center" align="center" width="130" label="兑换来源">
      </el-table-column>
      <el-table-column prop="orderNum" header-align="center" align="center" width="170" label="订单编号"
                       show-overflow-tooltip>
      </el-table-column>
      <el-table-column prop="exchangeUserName" header-align="center" align="center" width="100" label="兑换人">
      </el-table-column>
      <el-table-column prop="exchangeUserLinkPhone" header-align="center" align="center" width="130" label="联系方式">
      </el-table-column>
      <el-table-column prop="exchangeTime" header-align="center" align="center" width="200" label="兑换时间">
      </el-table-column>
      <el-table-column prop="goodsName" header-align="center" align="center" :show-overflow-tooltip="true"
                       label="商品名称">
      </el-table-column>
      <el-table-column prop="orgName" header-align="center" align="center" width="200" label="组织机构">
      </el-table-column>
      <el-table-column prop="exchangePoint" header-align="center" align="center" width="80" label="兑换积分">
      </el-table-column>
      <el-table-column prop="exchangeAfterScore" header-align="center" align="center" width="80" label="剩余积分">
      </el-table-column>
      <el-table-column prop="statusText" header-align="center" align="center" width="80" label="状态">
      </el-table-column>
      <el-table-column prop="emsStatusText" header-align="center" align="center" width="80" label="快递状态">
        <template slot-scope="scope">
          <el-popover trigger="hover" placement="top">
            <p style="text-align: center">
              {{
                (scope.row.emsRemark ? scope.row.emsRemark : '')
              }}
            </p>
            <div slot="reference" class="name-wrapper">
              <el-tag v-if="scope.row.emsStatus === 'mall_exchange_ems_status_delivery'" type="warning">
                {{ scope.row.emsStatusText }}
              </el-tag>
              <el-tag v-if="scope.row.emsStatus === 'mall_exchange_ems_status_ess_failure'" type="danger">
                {{ scope.row.emsStatusText }}
              </el-tag>
              <el-tag v-if="scope.row.emsStatus === 'mall_exchange_ems_status_ess_cancel'" type="warning">
                {{ scope.row.emsStatusText }}
              </el-tag>
              <el-tag v-if="scope.row.emsStatus === 'mall_exchange_status_sent'" type="success">
                {{ scope.row.emsStatusText }}
              </el-tag>
            </div>
          </el-popover>
        </template>
      </el-table-column>
      <el-table-column prop="emsCode" header-align="center" align="center" label="运单号" width="150"
                       show-overflow-tooltip>
      </el-table-column>
      <el-table-column fixed="right" header-align="center" align="center" width="150" label="操作">
        <template slot-scope="scope">
          <el-button type="text" v-if="scope.row.emsCode !== ''&&scope.row.emsCode !==null" size="small"
                     @click="downloadFile(scope.row.id,scope.row.emsCode)">下载面单
          </el-button>
          <el-button type="text" v-if="scope.row.emsCode && scope.row.emsCode !== ''" size="small"
                     @click="logisticsSearch(scope.row.emsCode)">物流查询
          </el-button>
          <el-button type="text"
                     v-if="(scope.row.status === 'mall_exchange_status_success'&&scope.row.emsStatus !=='mall_exchange_status_sent'&&scope.row.emsStatus!==null)||
						scope.row.status === 'mall_exchange_status_written_exchange' || (scope.row.status === 'mall_exchange_status_success' && scope.row.exchangeType === 'mall_exchange_type_pad')"
                     size="small" @click="updateStatus(scope.row.id)">取消兑换
          </el-button>
          <el-button type="text" v-if="scope.row.status === 'mall_exchange_status_written_exchange'"
                     size="small" @click="wrtieOff(scope.row.id)">核销
          </el-button>

        </template>
      </el-table-column>
    </el-table>
    <el-pagination @size-change="sizeChangeHandle" @current-change="currentChangeHandle" :current-page="pageIndex"
                   :page-sizes="[10, 20, 50, 100]" :page-size="pageSize" :total="totalPage"
                   layout="total, sizes, prev, pager, next, jumper">
    </el-pagination>
    <!-- 弹窗, 新增 / 修改 -->
    <wrtie-off v-if="wrtieOffVisable" ref="wrtieOff" @refreshDataList="getDataList"></wrtie-off>
    <logistics-search v-if="logisticsSearchVisible" ref="logisticsSearch"></logistics-search>
  </div>
</template>

<script>
import {
  downloadFile
} from '@/utils'
import WrtieOff from './mall-exchange-wrtie-off'
import LogisticsSearch from './mall-ems-logistics-search.vue'

export default {
  data() {
    return {
      dataForm: {
        key: '',
        orderNum: '',
        orgCode: '',
        orgCodeList: [],
        keyWord: '',
        status: '',
        exchangeType: '',
        emsStatus: '',
        timeRange: [],
        startTime:'',
        endTime:''
      },
      dataList: [],
      pageIndex: 1,
      pageSize: 10,
      totalPage: 0,
      fullscreenLoading: false,
      wrtieOffVisable: false,
      dataListLoading: false,
      dataListSelections: [],
      orgList: [],
      addOrUpdateVisible: false,
      logisticsSearchVisible: false
    }
  },
  components: {
    WrtieOff,
    LogisticsSearch
  },
  activated() {
    this.queryPage()
    this.getOrg()
  },
  methods: {
    resetField() {
      this.$refs['dataForm']?.resetFields()
      this.getDataList()
    },
    queryPage() {
      this.pageIndex = 1
      this.getDataList()
    },
    // 获取数据列表
    getDataList() {
      this.dataListLoading = true
      // 查询前操作
      this.dataForm.startTime = this.dataForm.timeRange && this.dataForm.timeRange[0]
      this.dataForm.endTime = this.dataForm.timeRange && this.dataForm.timeRange[1]
      this.$http({
        url: this.$http.adornUrl('/admin/mall/exchange/getPages'),
        method: 'post',
        data: this.$http.adornData({
          'currentPage': this.pageIndex,
          'pageSize': this.pageSize,
          'key': this.dataForm.key,
          'orgCode': this.dataForm.orgCode,
          'keyWord': this.dataForm.keyWord,
          'orderNum': this.dataForm.orderNum,
          'status': this.dataForm.status,
          'exchangeType': this.dataForm.exchangeType,
          'emsStatus': this.dataForm.emsStatus,
          'startTime': this.dataForm.startTime,
          'endTime': this.dataForm.endTime
        })
      }).then(({
                 data
               }) => {
        if (data && data.code === 0) {
          this.dataList = data.obj.records
          this.totalPage = data.obj.total
        } else {
          this.dataList = []
          this.totalPage = 0
        }
        this.dataListLoading = false
      })
    },
    // 下载面单
    downloadFile(id, fileName) {
      this.$confirm(`确定要进行下载面单吗?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        closeOnClickModal: false
      }).then(() => {
        this.$http({
          url: this.$http.adornUrl('/admin/mall/exchange/getNoddleList'),
          method: 'get',
          params: this.$http.adornParams({
            'id': id
          })
        }).then(({
                   data
                 }) => {
          if (data && data.code === 0) {
            downloadFile(":pdf" + ";base64," + data.obj, fileName + '.pdf')
            this.$message({
              message: '操作成功',
              type: 'success',
              duration: 1500,
              onClose: () => {
                this.getDataList()
              }
            })
          } else {
            this.$message.error(data.msg)
          }
        })
      })
    },
    logisticsSearch(emsCode) {
      this.logisticsSearchVisible = true
      this.$nextTick(() => {
        this.$refs.logisticsSearch.init(emsCode)
      })
    },
    wrtieOff(id) {
      this.wrtieOffVisable = true
      this.$nextTick(() => {
        this.$refs.wrtieOff.init(id)
      })
    },
    // 取消兑换
    updateStatus(id) {
      this.$confirm(`确定要进行取消兑换吗，请谨慎操作；确定进行取消兑换操作?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        closeOnClickModal: false
      }).then(() => {
        this.$http({
          url: this.$http.adornUrl('/admin/mall/exchange/cancelExchange'),
          method: 'get',
          params: this.$http.adornParams({
            'id': id
          })
        }).then(({
                   data
                 }) => {
          if (data && data.code === 0) {
            this.$message({
              message: '操作成功',
              type: 'success',
              duration: 1500,
              onClose: () => {
                this.getDataList()
              }
            })
          } else {
            this.$message.error(data.msg)
          }
        })
      })
    },
    exportExchange() {
      this.fullscreenLoading = true;
      this.$http({
        url: this.$http.adornUrl('/admin/mall/exchange/exportExchange'),
        method: 'post',
        responseType: 'arraybuffer',
        data: this.$http.adornData({
          'key': this.dataForm.key,
          'orgCode': this.dataForm.orgCode,
          'keyWord': this.dataForm.keyWord,
          'status': this.dataForm.status,
          'exchangeType': this.dataForm.exchangeType,
          'emsStatus': this.dataForm.emsStatus,
          'orderNum': this.dataForm.orderNum,
          'startTime': this.dataForm.startTime,
          'endTime': this.dataForm.endTime
        })
      }).then(({
                 data
               }) => {
        if (data.code && data.code !== 0) {
          this.$message.error('导出失败')
        } else {
          this.fullscreenLoading = false
          let blob = new Blob([data], {
            type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8'
          })
          let objectUrl = URL.createObjectURL(blob)
          let a = document.createElement('a')
          // window.location.href = objectUrl
          a.href = objectUrl
          a.download = '兑换记录数据.xlsx'
          a.click()
          URL.revokeObjectURL(objectUrl)
          this.$message({
            type: 'success',
            message: '导出成功'
          })
        }
      })
    },
    // 每页数
    sizeChangeHandle(val) {
      this.pageSize = val
      this.pageIndex = 1
      this.getDataList()
    },
    // 当前页
    currentChangeHandle(val) {
      this.pageIndex = val
      this.getDataList()
    },
    // 多选
    selectionChangeHandle(val) {
      this.dataListSelections = val
    },
    // 新增 / 修改
    addOrUpdateHandle(id) {
      this.addOrUpdateVisible = true
      this.$nextTick(() => {
        this.$refs.addOrUpdate.init(id)
      })
    },
    // 删除
    deleteHandle(id) {
      var ids = id ? [id] : this.dataListSelections.map(item => {
        return item.id
      })
      this.$confirm(`确定进行[${id ? '删除' : '批量删除'}]操作?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        closeOnClickModal: false
      }).then(() => {
        this.$http({
          url: this.$http.adornUrl('/admin/mall/exchange/removeByIds'),
          method: 'get',
          params: this.$http.adornParams({
            'ids': ids.join(',')
          })
        }).then(({
                   data
                 }) => {
          if (data && data.code === 0) {
            this.$message({
              message: '操作成功',
              type: 'success',
              duration: 1500,
              onClose: () => {
                this.getDataList()
              }
            })
          } else {
            this.$message.error(data.msg)
          }
        })
      })
    },
    //获取所属区域
    getOrg() {
      this.$http({
        url: this.$http.adornUrl(`/admin/org/getFiveAreaSubAssociationOrg`),
        method: 'get',
        params: this.$http.adornParams({withTop: true})
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.orgList = data.obj
        } else {
          this.orgList = []
        }
      })
    },
    // *处理点位分类最后children数组为空的状态
    getTreeData: function (data) {
      let that = this
      // 循环遍历json数据
      data.forEach(function (e) {
        if (!e.children || e.children.length < 1) {
          e.children = undefined
        } else {
          // children若不为空数组，则继续 递归调用 本方法
          that.getTreeData(e.children)
        }
      })
      return data
    },
    batchEMSOrder(){
      this.$message.warning("系统工作日每天凌晨2点会对待发货的兑换订单进行批量EMS下单……")
    }
  }
}
</script>
