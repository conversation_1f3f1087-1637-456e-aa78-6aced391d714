<template>
    <div class="mod-config">
        <el-form :inline="true" :model="dataForm" ref="dataForm" @keyup.enter="queryPage()">
            <el-form-item label="关键字" prop="keyWord">
                <el-input v-model="dataForm.keyWord" placeholder="关键字" clearable></el-input>
            </el-form-item>
            <el-form-item label="兑换时间" prop="timeRange">
                <el-date-picker v-model="dataForm.timeRange" clearable type="datetimerange"
                                value-format="yyyy-MM-dd HH:mm:ss" align="right" start-placeholder="开始时间" end-placeholder="结束时间"    :default-time="['00:00:00', '23:59:59']">
                </el-date-picker>
            </el-form-item>
            <el-form-item>
                <el-button type="warning" @click="queryPage()"icon="el-icon-search">查询</el-button>
                <el-button  @click="resetForm()"icon="el-icon-refresh-left">重置</el-button>
            </el-form-item>
        </el-form>
        <div class="f-s-c"
             style="padding: 15px 0;display: flex;justify-content:space-between;align-items: center;float:right ">
            <el-button icon="el-icon-download" type="success" v-loading.fullscreen.lock="fullscreenLoading" @click="exportApply()">导出
            </el-button>
        </div>
        <el-table
                :data="dataList"
                border
                v-loading="dataListLoading"
                style="width: 100%;">
            <el-table-column
                    prop="goodsName"
                    header-align="center"
                    align="center"
                    width="200"
                    label="商品名称">
            </el-table-column>
            <el-table-column
                    prop="exchangeTime"
                    header-align="center"
                    align="center"
                    width="200"
                    label="兑换时间">
            </el-table-column>
            <el-table-column
                    prop="exchangePoint"
                    header-align="center"
                    align="center"
                    width="200"
                    label="消费积分">
            </el-table-column>
            <el-table-column
                    prop="volunteerName"
                    header-align="center"
                    align="center"
                    width="100"
                    label="志愿者姓名">
            </el-table-column>
            <el-table-column
                    prop="volunteerPhone"
                    header-align="center"
                    align="center"
                    width="120"
                    label="志愿者联系方式">
            </el-table-column>
            <el-table-column
                    prop="volunteerAddress"
                    header-align="center"
                    align="center"
                    width="180"
                    label="志愿者户籍所在地">
            </el-table-column>
            <el-table-column
                    prop="volunteerCertificateId"
                    header-align="center"
                    align="center"
                    width="180"
                    label="志愿者身份证号码">
            </el-table-column>
            <el-table-column
                    prop="elderName"
                    header-align="center"
                    align="center"
                    width="180"
                    label="老人姓名">
            </el-table-column>
            <el-table-column
                    prop="elderPhone"
                    header-align="center"
                    align="center"
                    width="120"
                    label="老人电话">
            </el-table-column>
            <el-table-column
                    prop="elderAddress"
                    header-align="center"
                    align="center"
                    width="150"
                    label="老人户籍所在地">
            </el-table-column>
            <el-table-column
                    prop="elderCertificateId"
                    header-align="center"
                    align="center"
                    width="180"
                    label="老年人身份证号码">
            </el-table-column>
            <el-table-column
                    fixed="right"
                    header-align="center"
                    align="center"
                    width="150"
                    label="操作">
                <template #default="scope">
                    <el-button type="text" size="small" @click="addOrUpdateHandle(scope.row.id)">修改</el-button>
                </template>
            </el-table-column>
        </el-table>
        <div class="com-pagination">
            <el-pagination
                    @size-change="sizeChangeHandle"
                    @current-change="currentChangeHandle"
                    :current-page="pageIndex"
                    :page-sizes="[10, 20, 50, 100]"
                    :page-size="pageSize"
                    :total="totalPage"
                    layout="total, sizes, prev, pager, next, jumper"
            />
        </div>
        <!-- 弹窗, 新增 / 修改 -->
        <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"></add-or-update>
    </div>
</template>

<script>
import AddOrUpdate from './mall-score-goods-elder-apply-add-or-update.vue'
export default {
    data() {
        return {
            dataForm: {
                keyWord: null,
                timeRange: [],
                startTime:'',
                endTime:''
            },
            dataList: [],
            pageIndex: 1,
            pageSize: 10,
            totalPage: 0,
            dataListLoading: false,
            addOrUpdateVisible: false,
            fullscreenLoading: false
        }
    },
    components: {
        AddOrUpdate
    },
    activated() {
        this.queryPage()
    },
    methods: {
        queryPage() {
            this.pageIndex = 1
            this.getDataList()
        },
        // 获取数据列表
        getDataList() {
            this.dataListLoading = true
            // 查询前操作
            this.dataForm.startTime = this.dataForm.timeRange && this.dataForm.timeRange[0]
            this.dataForm.endTime = this.dataForm.timeRange && this.dataForm.timeRange[1]
            this.$http({
                url: this.$http.adornUrl('/admin/mall/score/goods/elder/apply/getPages'),
                method: 'post',
                data: this.$http.adornData({
                    'currentPage': this.pageIndex,
                    'pageSize': this.pageSize,
                    'keyWord': this.dataForm.keyWord,
                    'startTime': this.dataForm.startTime,
                    'endTime': this.dataForm.endTime
                })
            }).then(({data}) => {
                if (data && data.code === 0) {
                    this.dataList = data.obj.records
                    this.totalPage = data.obj.total
                } else {
                    this.dataList = []
                    this.totalPage = 0
                }
                this.dataListLoading = false
            })
        },
        // 每页数
        sizeChangeHandle(val) {
            this.pageSize = val
            this.pageIndex = 1
            this.getDataList()
        },
        // 当前页
        currentChangeHandle(val) {
            this.pageIndex = val
            this.getDataList()
        },
        // 多选
        selectionChangeHandle(val) {
            this.dataListSelections = val
        },
        // 重置
        resetForm() {
            this.$nextTick(() => {
                this.$refs['dataForm'].resetFields()
                this.getDataList()
            })
        },
        // 新增 / 修改
        addOrUpdateHandle(id) {
            this.addOrUpdateVisible = true
            this.$nextTick(() => {
                this.$refs.addOrUpdate.init(id)
            })
        },
        exportApply() {
            this.fullscreenLoading = true;
            this.$http({
                url: this.$http.adornUrl('/admin/mall/score/goods/elder/apply/exportApply'),
                method: 'post',
                responseType: 'arraybuffer',
                data: this.$http.adornData({
                    'key': this.dataForm.key,
                    'orgCode': this.dataForm.orgCode,
                    'keyWord': this.dataForm.keyWord
                })
            }).then(({
                         data
                     }) => {
                if (data.code && data.code !== 0) {
                    this.$message.error('导出失败')
                } else {
                    this.fullscreenLoading = false
                    let blob = new Blob([data], {
                        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8'
                    })
                    let objectUrl = URL.createObjectURL(blob)
                    let a = document.createElement('a')
                    // window.location.href = objectUrl
                    a.href = objectUrl
                    a.download = '报名记录数据.xlsx'
                    a.click()
                    URL.revokeObjectURL(objectUrl)
                    this.$message({
                        type: 'success',
                        message: '导出成功'
                    })
                }
            })
        },

    }
}
</script>
