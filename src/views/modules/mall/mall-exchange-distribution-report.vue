<template>
	<div class="mod-config">
		<el-form :inline="true" ref="dataForm" :model="dataForm" @keyup.enter.native="queryPage()">
			<el-form-item :label="'年度'" prop="year">
				<el-select v-model="dataForm.year" placeholder="年度" @visible-change="yearChange($event)">
					<el-option v-for="item in years" :key="item.value" :label="item.label" :value="item.value">
					</el-option>
				</el-select>
			</el-form-item>
			<el-form-item>
				<el-button type="warning" icon="el-icon-search" @click="queryPage()">查询</el-button>
        <el-button icon="el-icon-refresh-left" @click="resetFields()">重置</el-button>
			</el-form-item>
		</el-form>
		<div class="f-s-c"
			style="padding: 15px 0;display: flex;justify-content:space-between;align-items: center;float:right ">
			<el-button icon="el-icon-download" type="success" v-loading.fullscreen.lock="fullscreenLoading" @click="exportGoods()">全部导出
			</el-button>
		</div>
		<el-table :data="dataList" border show-summary :summary-method="getSummaries" v-loading="dataListLoading"
			@selection-change="selectionChangeHandle" style="width: 100%;">
			<el-table-column prop="year" header-align="center" align="center" label="所属年度">
			</el-table-column>
			<el-table-column prop="serverTime" header-align="center" align="center" label="服务时长(小时)">
			</el-table-column>
			<el-table-column prop="qualifiedPersonnelNum" header-align="center" align="center" label="达标人数">
			</el-table-column>
			<el-table-column prop="totalServerTime" header-align="center" align="center" label="时长">
			</el-table-column>
			<el-table-column prop="totalPoint" header-align="center" align="center" label="产生积分">
			</el-table-column>
			<el-table-column prop="totalExchangePoint" header-align="center" align="center" label="兑换积分">
			</el-table-column>
			<el-table-column prop="totalExchangeNum" header-align="center" align="center" label="兑换人次">
			</el-table-column>
		</el-table>
		<el-pagination @size-change="sizeChangeHandle" @current-change="currentChangeHandle" :current-page="pageIndex"
			:page-sizes="[10, 20, 50, 100]" :page-size="pageSize" :total="totalPage"
			layout="total, sizes, prev, pager, next, jumper">
		</el-pagination>
		<!-- 弹窗, 新增 / 修改 -->
	</div>
</template>

<script>
	export default {
		data() {
			return {
				years: [],
				dataForm: {
					year: new Date().getFullYear()-1
				},
				dataList: [],
				pageIndex: 1,
				pageSize: 10,
				totalPage: 0,
				dataListLoading: false,
				dataListSelections: [],
			}
		},
		components: {

		},
		activated() {
			this.queryPage()
		},
		methods: {
      resetFields () {
        let myDate = new Date()
        this.dataForm.year = myDate.getFullYear() - 1
        this.getDataList()
      },
			queryPage() {
				this.pageIndex = 1
				this.getDataList()
			},
			// 获取数据列表
			getDataList() {
				this.dataListLoading = true
				this.$http({
					url: this.$http.adornUrl('/admin/mall/exchange-distribution-report/getPages'),
					method: 'post',
					data: this.$http.adornData({
						'currentPage': this.pageIndex,
						'pageSize': this.pageSize,
						'year': this.dataForm.year
					})
				}).then(({
					data
				}) => {
					if (data && data.code === 0) {
						this.dataList = data.obj.records
						this.totalPage = data.obj.total
					} else {
						this.dataList = []
						this.totalPage = 0
					}
					this.dataListLoading = false
				})
			},
			yearChange() {
				var myDate = new Date()
				var startYear = myDate.getFullYear() - 5 // 起始年份
				var endYear = myDate.getFullYear() // 结束年份

				this.years = []
				for (var i = startYear; i <= endYear; i++) {
					this.years.push({
						value: (i),
						label: (i)
					})
				}
			},
			// 下载模板文件
			exportGoods() {
				this.fullscreenLoading = true;
				this.$http({
					url: this.$http.adornUrl('/admin/mall/exchange-distribution-report/export'),
					method: 'post',
					responseType: 'arraybuffer',
					data: this.$http.adornData({
						'year': this.dataForm.year
					})
				}).then(({
					data
				}) => {
					if (data.code && data.code !== 0) {
						this.$message.error('导出失败')
					} else {
						this.fullscreenLoading = false

						let blob = new Blob([data], {
							type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8'
						})
						let objectUrl = URL.createObjectURL(blob)
						let a = document.createElement('a')
						// window.location.href = objectUrl
						a.href = objectUrl
						a.download = '历年兑换列表.xlsx'
						a.click()
						URL.revokeObjectURL(objectUrl)
						this.$message({
							type: 'success',
							message: '导出成功'
						})
					}
				})
			},
			// 表格总计的时间
			getPaperNum(param) {
				const {
					columns,
					data
				} = param;
				const sums = [];
				columns.forEach((column, index) => {
					if (index === 0) {
						sums[index] = '合计';
						return;
					}
					const values = data.map(item => Number(item[column.property]));
					if (column.property === 'qualifiedPersonnelNum') {
						sums[index] = values.reduce((prev, curr) => {
							const value = Number(curr);
							if (!isNaN(value)) {
								return prev + curr;
							} else {
								return prev;
							}
						}, 0);
						sums[index];
					} else {
						sums[index] = '--';
					}
					if (column.property === 'totalServerTime') {
						sums[index] = values.reduce((prev, curr) => {
							const value = Number(curr);
							if (!isNaN(value)) {
								return prev + curr;
							} else {
								return prev;
							}
						}, 0);
						sums[index];
					} else {
						sums[index] = '--';
					}
					if (column.property === 'totalPoint') {
						sums[index] = values.reduce((prev, curr) => {
							const value = Number(curr);
							if (!isNaN(value)) {
								return prev + curr;
							} else {
								return prev;
							}
						}, 0);
						sums[index];
					} else {
						sums[index] = '--';
					}
					if (column.property === 'totalExchangePoint') {
						sums[index] = values.reduce((prev, curr) => {
							const value = Number(curr);
							if (!isNaN(value)) {
								return prev + curr;
							} else {
								return prev;
							}
						}, 0);
						sums[index];
					} else {
						sums[index] = '--';
					}
					if (column.property === 'totalExchangeNum') {
						sums[index] = values.reduce((prev, curr) => {
							const value = Number(curr);
							if (!isNaN(value)) {
								return prev + curr;
							} else {
								return prev;
							}
						}, 0);
						sums[index];
					} else {
						sums[index] = '--';
					}
				});

				return sums;
			},

			// 每页数
			sizeChangeHandle(val) {
				this.pageSize = val
				this.pageIndex = 1
				this.getDataList()
			},
			// 当前页
			currentChangeHandle(val) {
				this.pageIndex = val
				this.getDataList()
			},
			// 多选
			selectionChangeHandle(val) {
				this.dataListSelections = val
			}
		}
	}
</script>
