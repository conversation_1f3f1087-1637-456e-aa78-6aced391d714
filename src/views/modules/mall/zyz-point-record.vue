<template>
  <div class="mod-config">
    <el-form :inline="true" ref="dataForm" :model="dataForm" @keyup.enter.native="queryPage()">
      <el-form-item label="姓名" prop="name">
        <el-input v-model="dataForm.name" placeholder="姓名" clearable></el-input>
      </el-form-item>
      <el-form-item label="证件编号" prop="certificateId">
        <el-input v-model="dataForm.certificateId" placeholder="证件编号" clearable></el-input>
      </el-form-item>
      <el-form-item label="原始积分" prop="minExchangeBeforePoint">
        <el-input style="width:100px" v-model="dataForm.minExchangeBeforePoint" placeholder=""></el-input>
      </el-form-item>
      <el-form-item label="~" prop="maxExchangeBeforePoint">
        <el-input style="width:100px" v-model="dataForm.maxExchangeBeforePoint" placeholder=""></el-input>
      </el-form-item>
      <el-form-item label="当前积分" prop="minExchangeAfterPoint">
        <el-input style="width:100px" v-model="dataForm.minExchangeAfterPoint" placeholder=""></el-input>
      </el-form-item>
      <el-form-item label="~" prop="maxExchangeAfterPoint">
        <el-input style="width:100px" v-model="dataForm.maxExchangeAfterPoint" placeholder=""></el-input>
      </el-form-item>
      <el-form-item label="业务类型" prop="bizType">
          <el-dict :code="'zyz_point_record_biz_type'" v-model="dataForm.bizType"></el-dict>
      </el-form-item>
      <el-form-item label="变动时间" prop="timeRange">
          <el-date-picker v-model="dataForm.timeRange" clearable type="datetimerange"
                          value-format="yyyy-MM-dd HH:mm:ss" align="right" start-placeholder="开始时间" end-placeholder="结束时间"    :default-time="['00:00:00', '23:59:59']">
          </el-date-picker>
      </el-form-item>
      <el-form-item label="交易说明" prop="remark">
          <el-input v-model="dataForm.remark" placeholder="交易说明" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-button type="warning" icon="el-icon-search" @click="queryPage()">查询</el-button>
        <el-button icon="el-icon-refresh-left" @click="resetField()">重置</el-button>
      </el-form-item>
    </el-form>
      <div class="f-s-c"
           style="padding: 15px 0;display: flex;justify-content:space-between;align-items: center;float:right ">
          <el-button icon="el-icon-download" type="success" v-loading.fullscreen.lock="fullscreenLoading" @click="exportExchange()">导出
          </el-button>
      </div>
    <el-table :data="dataList" border v-loading="dataListLoading" @selection-change="selectionChangeHandle"
              style="width: 100%;">
      <el-table-column prop="bizTypeText" header-align="center" align="center" width="130" label="业务类型">
      </el-table-column>
      <el-table-column prop="typeText" header-align="center" align="center" width="130" label="记录类型">
      </el-table-column>
      <el-table-column prop="volunteerName" header-align="center" align="center" width="100" label="志愿者名称">
      </el-table-column>
      <el-table-column prop="certificateId" header-align="center" align="center" width="220" label="志愿者证件编号">
      </el-table-column>
      <el-table-column prop="phone" header-align="center" align="center" width="130" label="志愿者联系方式">
      </el-table-column>
      <el-table-column prop="exchangeTime" header-align="center" align="center" width="200" label="变动时间">
      </el-table-column>
      <el-table-column prop="exchangeBeforePoint" header-align="center" align="center" width="120" label="原始积分">
      </el-table-column>
      <el-table-column prop="exchangePoint" header-align="center" align="center" width="120" label="变动积分">
        <template slot-scope="scope">
          {{
            scope.row.type === 'zyz_point_record_type_sub' ? '-' + scope.row.exchangePoint : '+' + scope.row.exchangePoint
          }}
        </template>
      </el-table-column>
      <el-table-column prop="exchangeAfterPoint" header-align="center" align="center" width="120" label="剩余积分">
      </el-table-column>
      <el-table-column :show-overflow-tooltip="true" prop="remark" header-align="center" min-width="300" align="center"
                       label="交易说明">
      </el-table-column>
    </el-table>
    <el-pagination @size-change="sizeChangeHandle" @current-change="currentChangeHandle" :current-page="pageIndex"
                   :page-sizes="[10, 20, 50, 100]" :page-size="pageSize" :total="totalPage"
                   layout="total, sizes, prev, pager, next, jumper">
    </el-pagination>
    <!-- 弹窗, 新增 / 修改 -->
  </div>
</template>

<script>
export default {
  data() {
    return {
      dataForm: {
        name: '',
        maxExchangeBeforePoint: '',
        minExchangeBeforePoint: '',
        maxExchangeAfterPoint: '',
        minExchangeAfterPoint: '',
        certificateId: '',
        bizType:'',
        timeRange: [],
        startTime:'',
        endTime:'',
        remark:''
      },
      dataList: [],
      pageIndex: 1,
      pageSize: 10,
      totalPage: 0,
      dataListLoading: false,
      dataListSelections: [],
      addOrUpdateVisible: false,
      fullscreenLoading: false
    }
  },
  components: {},
  activated() {
    this.queryPage()
  },
  methods: {
    resetField () {
      this.$refs['dataForm']?.resetFields()
      this.getDataList()
    },
    queryPage() {
      this.pageIndex = 1
      this.getDataList()
    },
    // 获取数据列表
    getDataList() {
      this.dataListLoading = true
      // 查询前操作
      this.dataForm.startTime = this.dataForm.timeRange && this.dataForm.timeRange[0]
      this.dataForm.endTime = this.dataForm.timeRange && this.dataForm.timeRange[1]
      this.$http({
        url: this.$http.adornUrl('/admin/point/record/getPages'),
        method: 'post',
        data: this.$http.adornData({
          'currentPage': this.pageIndex,
          'pageSize': this.pageSize,
          'name': this.dataForm.name,
          'certificateId': this.dataForm.certificateId,
          'maxExchangeBeforePoint': this.dataForm.maxExchangeBeforePoint,
          'minExchangeBeforePoint': this.dataForm.minExchangeBeforePoint,
          'maxExchangeAfterPoint': this.dataForm.maxExchangeAfterPoint,
          'minExchangeAfterPoint': this.dataForm.minExchangeAfterPoint,
          'bizType': this.dataForm.bizType,
          'startTime': this.dataForm.startTime,
          'endTime': this.dataForm.endTime,
          'remark': this.dataForm.remark
        })
      }).then(({
                 data
               }) => {
        if (data && data.code === 0) {
          this.dataList = data.obj.records
          this.totalPage = data.obj.total
        } else {
          this.dataList = []
          this.totalPage = 0
        }
        this.dataListLoading = false
      })
    },
    // 每页数
    sizeChangeHandle(val) {
      this.pageSize = val
      this.pageIndex = 1
      this.getDataList()
    },
    // 当前页
    currentChangeHandle(val) {
      this.pageIndex = val
      this.getDataList()
    },
    // 多选
    selectionChangeHandle(val) {
      this.dataListSelections = val
    },
    // 新增 / 修改
    addOrUpdateHandle(id) {
      this.addOrUpdateVisible = true
      this.$nextTick(() => {
        this.$refs.addOrUpdate.init(id)
      })
    },
    // 删除
    deleteHandle(id) {
      var ids = id ? [id] : this.dataListSelections.map(item => {
        return item.id
      })
      this.$confirm(`确定进行[${id ? '删除' : '批量删除'}]操作?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        closeOnClickModal: false
      }).then(() => {
        this.$http({
          url: this.$http.adornUrl('/zyzpointrecord/removeByIds'),
          method: 'get',
          params: this.$http.adornParams({
            'ids': ids.join(',')
          })
        }).then(({
                   data
                 }) => {
          if (data && data.code === 0) {
            this.$message({
              message: '操作成功',
              type: 'success',
              duration: 1500,
              onClose: () => {
                this.getDataList()
              }
            })
          } else {
            this.$message.error(data.msg)
          }
        })
      })
    },
    exportExchange() {
        this.fullscreenLoading = true;
        this.dataForm.startTime = this.dataForm.timeRange && this.dataForm.timeRange[0]
        this.dataForm.endTime = this.dataForm.timeRange && this.dataForm.timeRange[1]
        this.$http({
            url: this.$http.adornUrl('/admin/point/record/exportExchange'),
            method: 'post',
            responseType: 'arraybuffer',
            data: this.$http.adornData({
                'name': this.dataForm.name,
                'certificateId': this.dataForm.certificateId,
                'maxExchangeBeforePoint': this.dataForm.maxExchangeBeforePoint,
                'minExchangeBeforePoint': this.dataForm.minExchangeBeforePoint,
                'maxExchangeAfterPoint': this.dataForm.maxExchangeAfterPoint,
                'minExchangeAfterPoint': this.dataForm.minExchangeAfterPoint,
                'bizType': this.dataForm.bizType,
                'startTime': this.dataForm.startTime,
                'endTime': this.dataForm.endTime,
                'remark': this.dataForm.remark
            })
        }).then(({
                     data
                 }) => {
            if (data.code && data.code !== 0) {
                this.$message.error('导出失败')
            } else {
                this.fullscreenLoading = false
                let blob = new Blob([data], {
                    type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8'
                })
                let objectUrl = URL.createObjectURL(blob)
                let a = document.createElement('a')
                // window.location.href = objectUrl
                a.href = objectUrl
                a.download = '积分记录数据.xlsx'
                a.click()
                URL.revokeObjectURL(objectUrl)
                this.$message({
                    type: 'success',
                    message: '导出成功'
                })
            }
        })
      },
  }
}
</script>
