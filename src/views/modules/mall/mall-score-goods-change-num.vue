<template>
	<el-dialog title="入库数量" :visible.sync="visible" :append-to-body="true" width="30%">
		<el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmit()"
			label-width="120px">
			<el-form-item label="增加的数量" prop="goodsCount">
				<el-input-number v-model="dataForm.goodsCount"></el-input-number>
			</el-form-item>
		</el-form>
		<span slot="footer" class="dialog-footer">
			<el-button @click="visible = false">取消</el-button>
			<el-button type="primary" @click="dataFormSubmit()">确定</el-button>
		</span>
		<div style="padding-left: 30px;font-weight: bold;color: red">
			如果为出库请在数量前加-号</div>
	</el-dialog>
</template>

<script>
	export default {
		data() {
			return {
				visible: false,
				dataList: [],
				dataListLoading: false,
				dataForm: {
					goodsId: null,
					goodsCount: 1
				},
				dataRule: {
					goodsCount: [{
						required: true,
						message: '商品数量不能为空',
						trigger: 'blur'
					}]
				},
				goodsCountError: null
			}
		},

		methods: {
			init(id) {
				this.dataForm.goodsId = id || null
				this.visible = true
				this.$refs['dataForm']?.resetFields()
			},
			// 表单提交
			dataFormSubmit() {
				this.$refs['dataForm'].validate((valid) => {
					if (valid) {
						this.$http({
							url: this.$http.adornUrl('/admin/mall/score-goods/goodsStorageIn'),
							method: 'post',
							params: this.$http.adornParams({
								'goodsId': this.dataForm.goodsId,
								'goodsNum': this.dataForm.goodsCount
							})
						}).then(({
							data
						}) => {
							if (data && data.code === 0) {
								this.$message({
									message: '操作成功',
									type: 'success',
									duration: 1000,
									onClose: () => {
										this.visible = false
										this.$emit('refreshDataList')
									}
								})
							} else {
								this.$message.error(data.msg)
							}
						})
					}
				})
			}
		}
	}
</script>
