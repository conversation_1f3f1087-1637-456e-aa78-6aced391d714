<template>
  <div>
    <el-dialog
        :title="enableEdit === false ? '兑换机补货计划详情' : (!dataForm.id ? '兑换机补货计划新增' : '兑换机补货计划修改')"
        :close-on-click-modal="false"
        top="8vh"
        :visible.sync="visible" width="90%">
      <el-form :model="dataForm" :rules="enableEdit === true ? dataRule : null" ref="dataForm" label-width="100px" label-position="right" style="margin: 0px 20px 0px 20px">
        <el-row :gutter="20" style="margin-bottom: 20px">
          <el-col :span="24">
            <span style="font-weight: bold; font-size: large">兑换机月度补货明细</span>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="兑换机：" prop="machineId"
                          :style="{color: !dataForm.machineId || dataForm.machineId === '' ? 'black' :
                          (!lodash.find(machineList, ['id', dataForm.machineId]) ? 'red' :
                          (lodash.find(machineList, ['id', dataForm.machineId]).status === 'mall_self_machine_status_normal' ? 'green' :
                          (lodash.find(machineList, ['id', dataForm.machineId]).status === 'mall_self_machine_status_maintain' ? 'orange' :
                          'red')))}">
              <el-select v-if="enableEdit === true" v-model="dataForm.machineId" placeholder="请选择" filterable clearable style="margin-right: 10px; width: 80%" @change="() => {dataForm.machineNo = (dataForm.machineId && dataForm.machineId !== '' ? lodash.find(machineList, ['id', dataForm.machineId]).machineNo : null)}">
                <el-option
                    v-for="item in machineList"
                    :disabled="!item.enableSelect || item.enableSelect === false"
                    :key="item.id"
                    :label="item.machineNo"
                    :value="item.id">
                  <span style="float: left">{{ item.machineNo }}</span>
                  <span v-if="item.enableSelect && item.enableSelect === true" style="float: right; color: #8492a6; font-size: 6px">{{item.orgName}}</span>
                  <span v-else style="float: right; color: #C0C4CC; font-size: 6px">{{item.orgName}}</span>
                </el-option>
              </el-select>
              <span v-if="enableEdit === false" style="color: black">{{!dataForm.machineNo ? '' : dataForm.machineNo}}</span>
              {{dataForm.machineId && dataForm.machineId !== '' ? (!lodash.find(machineList, ['id', dataForm.machineId]) ? '' : lodash.find(machineList, ['id', dataForm.machineId]).statusText) : ''}}
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="所属单位：" v-if="dataForm.machineId && dataForm.machineId !== ''">
              <span>{{!lodash.find(machineList, ['id', dataForm.machineId]) ? '' : lodash.find(machineList, ['id', dataForm.machineId]).orgName}}</span>
            </el-form-item>
          </el-col>
          <el-col :span="10">
            <el-form-item label="地址：" v-if="dataForm.machineId && dataForm.machineId !== ''">
              <span :title="!lodash.find(machineList, ['id', dataForm.machineId]) ? '' : lodash.find(machineList, ['id', dataForm.machineId]).address" style="white-space: nowrap; display: block; width: 100%; overflow: hidden; text-overflow: ellipsis">{{!lodash.find(machineList, ['id', dataForm.machineId]) ? '' : lodash.find(machineList, ['id', dataForm.machineId]).address}}</span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="预算年度：" prop="planYear">
              <el-date-picker
                  v-if="enableEdit === true"
                  v-model="dataForm.planYear"
                  type="year"
                  clearable
                  value-format="yyyy"
                  style="width: 80%"
                  placeholder="请选择">
              </el-date-picker>
              <span v-if="enableEdit === false">{{!dataForm.planYear ? '' : dataForm.planYear + ' 年度'}}</span>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="预算金额：" prop="budgetAmount">
              <el-input-number v-if="enableEdit === true" v-model="dataForm.budgetAmount" :min="0" controls-position="right"></el-input-number>
              {{enableEdit === true ? '（元）' : ''}}
              <span v-if="enableEdit === false">{{dataForm.budgetAmount >= 10000 ? dataForm.budgetAmount/10000 + ' 万元' : dataForm.budgetAmount + ' 元'}}</span>
            </el-form-item>
          </el-col>
          <el-col :span="8" v-if="enableEdit === true">
            <el-button style="float: right" id="plan_generate" type="primary" plain @click="generatePlan">月度计划生成</el-button>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-form :model="detailForm" ref="detailForm" :rules="detailForm.detailRules">
            <el-table
                id="machine_detail_table"
                ref="machineDetailTable"
                :key="detailTableKey"
                :data="detailForm.detailList"
                v-loading="detailListLoading"
                :height="enableEdit === true ? '530px' : null"
                max-height="530px"
                show-summary
                :summary-method="detailSummary"
                :row-style="{height:'10px'}"
                :cell-style="{padding:'0px'}"
                :lazy="true"
                style="width: 100%; font-size: 8px"
                :row-class-name="specialRowDeal">
              <el-table-column
                  fixed
                  type="index"
                  header-align="center"
                  label="序号"
                  align="center"
                  width="50"
              >
              </el-table-column>
              <el-table-column
                  fixed
                  prop="stockName"
                  header-align="center"
                  align="center"
                  width="140"
                  label="物料名称">
                <template slot-scope="scope">
                  <el-form-item :prop="`detailList.${scope.$index}.stockId`"
                                :rules='detailForm.detailRules.stockId' style="margin-bottom: 0"
                                v-if="enableEdit === true && (!scope.row.stockDeleted || scope.row.stockDeleted === false)">
                    <el-select :style="enableStockNew === true ? {width: '85%'} : {}" size="mini" v-model="scope.row.stockId" clearable v-if="enableEdit === true" @change="(value) => changeStock(value, scope.row)" @clear="clearStock(scope.row)" filterable>
                      <el-option
                          v-for="item in stockList"
                          :disabled="detailForm.detailList.map(detail => {return detail.stockId}).indexOf(item.id) >= 0"
                          :key="item.id"
                          :label="item.name"
                          :value="item.id">
                      </el-option>
                    </el-select>
                    <el-button v-if="enableStockNew === true" style="width: 16px;float: right;border: 0px;padding: 0px;font-size: 16px;margin-top: 12px;" class="el-icon-circle-plus-outline stock_new" title="新增物料" @click="newStockHandle(scope.$index, scope.row)"></el-button>
                  </el-form-item>
                  <span v-if="enableEdit === false || (scope.row.stockDeleted && scope.row.stockDeleted === true)">{{scope.row.stockName}}</span>
                </template>
              </el-table-column>
              <el-table-column
                  fixed
                  prop="stockSku"
                  header-align="center"
                  align="center"
                  width="100"
                  label="物料编号">
              </el-table-column>
              <el-table-column
                  fixed
                  prop="unitPrice"
                  header-align="center"
                  align="center"
                  width="80"
                  label="单价">
              </el-table-column>
              <el-table-column
                  fixed
                  label="预算(中心)"
                  header-align="center"
                  :width="enableEdit === true ? 220 : 160"
                  align="center">
                <el-table-column
                    fixed
                    prop="replenishQuantity"
                    header-align="center"
                    align="center"
                    :width="enableEdit === true ? 120 : 60"
                    label="数量">
                  <template slot-scope="scope">
                    <el-form-item style="margin-bottom: 0"
                                  :prop="`detailList.${scope.$index}.replenishQuantity`"
                                  :rules='detailForm.detailRules.replenishQuantity'
                                  v-if="enableEdit === true && scope.row.stockId && scope.row.stockId !== ''">
                      <el-input-number size="mini" style="width: 100%" v-model="scope.row.replenishQuantity" controls-position="right" :min="0" @change="(value) => changeReplenishQuantity(value, scope.row)"/>
                    </el-form-item>
                    <span v-if="enableEdit === false">{{scope.row.replenishQuantity}}</span>
                  </template>
                </el-table-column>
                <el-table-column
                    fixed
                    prop="replenishAmount"
                    header-align="center"
                    align="center"
                    width="100"
                    label="金额">
                </el-table-column>
              </el-table-column>
              <el-table-column
                  fixed
                  label="实际计划"
                  header-align="center"
                  align="center"
                  :width="enableEdit === true ? 280 : 240">
                <el-table-column
                    fixed
                    prop="actualUnitQuantity"
                    header-align="center"
                    align="center"
                    :width="enableEdit === true ? 120 : 80"
                    label="单位数量">
                  <template slot-scope="scope">
                    <el-form-item style="margin-bottom: 0"
                                  :prop="`detailList.${scope.$index}.actualUnitQuantity`"
                                  :rules='detailForm.detailRules.actualUnitQuantity'
                                  v-if="enableEdit === true && scope.row.stockId && scope.row.stockId !== ''">
                      <el-input-number size="mini" style="width: 100%" v-model="scope.row.actualUnitQuantity" controls-position="right" :min="0" @change="(curVal, oldVal) => changeActualUnitQuantity(curVal, oldVal, scope.row)"/>
                    </el-form-item>
                    <span v-if="enableEdit === false">{{scope.row.actualUnitQuantity}}</span>
                  </template>
                </el-table-column>
                <el-table-column
                    fixed
                    prop="actualTotalQuantity"
                    header-align="center"
                    align="center"
                    width="60"
                    label="总量">
                </el-table-column>
                <el-table-column
                    fixed
                    prop="actualTotalAmount"
                    header-align="center"
                    align="center"
                    width="100"
                    label="金额">
                </el-table-column>
              </el-table-column>
              <el-table-column
                  :label="item.name"
                  header-align="center"
                  v-for="item in monthList"
                  :key="item.code"
                  :width="enableEdit === true ? 200 : 140"
                  align="center">
                <el-table-column
                    :prop="`${item.code}Quantity`"
                    header-align="center"
                    align="center"
                    :width="enableEdit === true ? 120 : 60"
                    label="数量">
                  <template slot-scope="scope">
                    <el-form-item style="margin-bottom: 0"
                                  :prop="`detailList.${scope.$index}.${item.code}Quantity`"
                                  :rules="[{ required: true, message: `${item.name}数量不能为空`, trigger: 'change' }]"
                                  v-if="enableEdit === true && scope.row.actualTotalQuantity && scope.row.actualTotalQuantity > 0 && scope.row.stockId && scope.row.stockId !== ''">
                      <el-input-number size="mini" style="width: 100%" v-model="scope.row[`${item.code}Quantity`]" controls-position="right" :min="0" @change="(value) => changeMonthQuantity(value, scope.row, item)"/>
                    </el-form-item>
                    <span v-if="enableEdit === false && scope.row.actualTotalQuantity && scope.row.actualTotalQuantity > 0">{{scope.row[`${item.code}Quantity`]}}</span>
<!--                    <span>{{scope.row[`${item.code}Quantity`]}}</span>-->
                  </template>
                </el-table-column>
                <el-table-column
                    :prop="`${item.code}Amount`"
                    header-align="center"
                    align="center"
                    width="80"
                    label="金额">
                  <template slot-scope="scope">
                    <span v-if="scope.row.actualTotalQuantity && scope.row.actualTotalQuantity > 0 && scope.row.stockId && scope.row.stockId !== ''">{{scope.row[`${item.code}Amount`]}}</span>
                  </template>
                </el-table-column>
              </el-table-column>
              <el-table-column
                  fixed = "right"
                  label="差额"
                  header-align="center"
                  align="center"
                  width="180">
                <el-table-column
                    fixed
                    prop="differenceQuantity"
                    header-align="center"
                    align="center"
                    width="80"
                    label="数量">
                  <template slot-scope="scope">
                    <span :style="scope.row.differenceQuantity === 0 ? {color: 'black'} : (scope.row.differenceQuantity > 0 ? {color: '#31e134'} : {color: 'red'})">{{scope.row.differenceQuantity}}</span>
                  </template>
                </el-table-column>
                <el-table-column
                    fixed
                    prop="differenceAmount"
                    header-align="center"
                    align="center"
                    width="100"
                    label="金额">
                  <template slot-scope="scope">
                    <span :style="Number(scope.row.differenceAmount) === 0.00 ? {color: 'black'} : (scope.row.differenceAmount > 0 ? {color: '#31e134'} : {color: 'red'})">{{scope.row.differenceAmount}}</span>
                  </template>
                </el-table-column>
              </el-table-column>
              <el-table-column
                  v-if="enableEdit === true"
                  fixed="right"
                  header-align="center"
                  align="center"
                  width="70"
                  label="操作">
                <template slot-scope="scope">
                  <el-button size="mini" class="el-icon-circle-plus" id="add_record" @click="addAfterCurr(scope.row, scope.$index)"></el-button>
                  <el-button size="mini" :disabled="!detailForm.detailList || detailForm.detailList.length === 0 || (detailForm.detailList.length === 1 && (!detailForm.detailList[0].stockId || detailForm.detailList[0].stockId === ''))" class="el-icon-remove" id="remove_record" @click="removeCurr(scope.row, scope.$index)"></el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-form>
        </el-row>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button v-if="enableEdit === true" @click="visible = false">取消</el-button>
        <el-button v-if="enableEdit === false" @click="visible = false">关闭</el-button>
        <el-button v-if="enableEdit === true" type="primary" @click="dataFormSubmit('draft')" :disabled="dataFormLoading">保存草稿</el-button>
        <el-button v-if="enableEdit === true" type="success" @click="dataFormSubmit('submit')" :disabled="dataFormLoading">提交</el-button>
      </span>
    </el-dialog>
    <stock-new ref="newStock" @refreshDataList="newStockEmit" v-if="newStockVisible"></stock-new>
  </div>
</template>

<script>
import StockNew from './mall-stock-add-or-update'
export default {
  name: "mall-stock-machine-replenish-plan-add-or-update",
  data() {
    return {
      enableEdit: true,
      enableStockNew: true,
      visible: false,
      dataFormLoading: false,
      lodash: _,
      machineList: [],
      monthList: [
        {code: 'jan', name: '1月'},
        {code: 'feb', name: '2月'},
        {code: 'mar', name: '3月'},
        {code: 'apr', name: '4月'},
        {code: 'may', name: '5月'},
        {code: 'jun', name: '6月'},
        {code: 'jul', name: '7月'},
        {code: 'aug', name: '8月'},
        {code: 'sep', name: '9月'},
        {code: 'oct', name: '10月'},
        {code: 'nov', name: '11月'},
        {code: 'dec', name: '12月'}
      ],
      dataForm: {
        id: null,
        planYear: null,
        budgetAmount: 0,
        actualAmount: 0,
        machineId: null,
        machineNo: null,
        type: 'srpt_machine'
      },
      dataRule: {
        machineId: [
          { required: true, message: '兑换机不能为空', trigger: 'change' }
        ],
        planYear: [
          { required: true, message: '预算年度不能为空', trigger: 'change' }
        ],
        budgetAmount: [
          { required: true, message: '预算金额不能为空', trigger: 'blur' }
        ]
      },
      stockList: [],
      detailForm: {
        detailList: [],
        detailRules: {
          stockId: [
            // { required: true, message: '物料不能为空', trigger: 'change' }
          ],
          replenishQuantity: [
            { required: true, message: '预算数量不能为空', trigger: 'change' }
          ],
          actualUnitQuantity: [
            { required: true, message: '实际计划单位数量不能为空', trigger: 'change' }
          ]
        }
      },
      detailListLoading: false,
      detailTableKey: null,
      currentNewStockRowIndex: null,
      newStockVisible: false
    }
  },
  components: {
    StockNew
  },
  updated() {
    this.$nextTick(()=>{
      this.$refs.machineDetailTable.doLayout();
    })
  },
  methods: {
    init (id, enableEdit) {
      this.dataForm.id = id || null
      this.enableEdit = enableEdit
      this.dataFormLoading = false
      this.detailTableKey = (new Date()).valueOf()
      this.visible = true
      this.getMachineList()
      this.getStockList()
      this.detailForm.detailList = []
      this.$nextTick(() => {
        this.$refs['dataForm']?.resetFields()
        this.detailListLoading = true
        if (this.dataForm.id) {
          this.getReplenishPlanData()
        } else {
          this.dataForm.planYear = new Date().getFullYear() + ''
          if ((!this.detailForm.detailList || this.detailForm.detailList.length === 0) && this.enableEdit === true) {
            let detail = {
              stockId: null,
              stockName: null,
              stockSku: null,
              unitPrice: null,
              replenishQuantity: null,
              replenishAmount: null,
              actualUnitQuantity: null,
              actualTotalQuantity: null,
              actualTotalAmount: null,
              janQuantity: null, janAmount: null,
              febQuantity: null, febAmount: null,
              marQuantity: null, marAmount: null,
              aprQuantity: null, aprAmount: null,
              mayQuantity: null, mayAmount: null,
              junQuantity: null, junAmount: null,
              julQuantity: null, julAmount: null,
              augQuantity: null, augAmount: null,
              sepQuantity: null, sepAmount: null,
              octQuantity: null, octAmount: null,
              novQuantity: null, novAmount: null,
              decQuantity: null, decAmount: null,
              monthNumSet: null,
              differenceQuantity: null,
              differenceAmount: null
            }
            this.detailForm.detailList.push(detail)
          }
          this.detailListLoading = false
        }
      })
    },
    addAfterCurr(row, index) {
      let detail = {
        stockId: null,
        stockName: null,
        stockSku: null,
        unitPrice: null,
        replenishQuantity: null,
        replenishAmount: null,
        actualUnitQuantity: null,
        actualTotalQuantity: null,
        actualTotalAmount: null,
        janQuantity: null, janAmount: null,
        febQuantity: null, febAmount: null,
        marQuantity: null, marAmount: null,
        aprQuantity: null, aprAmount: null,
        mayQuantity: null, mayAmount: null,
        junQuantity: null, junAmount: null,
        julQuantity: null, julAmount: null,
        augQuantity: null, augAmount: null,
        sepQuantity: null, sepAmount: null,
        octQuantity: null, octAmount: null,
        novQuantity: null, novAmount: null,
        decQuantity: null, decAmount: null,
        monthNumSet: null,
        differenceQuantity: null,
        differenceAmount: null
      }
      this.detailForm.detailList.splice(index + 1, 0, detail)
    },
    removeCurr(row, index) {
      this.detailForm.detailList.splice(index, 1)
      if (!this.detailForm.detailList || this.detailForm.detailList.length === 0) {
        let detail = {
          stockId: null,
          stockName: null,
          stockSku: null,
          unitPrice: null,
          replenishQuantity: null,
          replenishAmount: null,
          actualUnitQuantity: null,
          actualTotalQuantity: null,
          actualTotalAmount: null,
          janQuantity: null, janAmount: null,
          febQuantity: null, febAmount: null,
          marQuantity: null, marAmount: null,
          aprQuantity: null, aprAmount: null,
          mayQuantity: null, mayAmount: null,
          junQuantity: null, junAmount: null,
          julQuantity: null, julAmount: null,
          augQuantity: null, augAmount: null,
          sepQuantity: null, sepAmount: null,
          octQuantity: null, octAmount: null,
          novQuantity: null, novAmount: null,
          decQuantity: null, decAmount: null,
          monthNumSet: null,
          differenceQuantity: null,
          differenceAmount: null
        }
        this.detailForm.detailList.push(detail)
      }
    },
    getReplenishPlanData () {
      this.$http({
        url: this.$http.adornUrl('/admin/mall/stock_replenish_plan/getReplenishPlanById'),
        method: 'get',
        params: this.$http.adornParams({
          id: this.dataForm.id
        })
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.dataForm.planYear = data.obj.planYear
          this.dataForm.budgetAmount = data.obj.budgetAmount
          this.dataForm.machineId = data.obj.machineId
          this.dataForm.machineNo = data.obj.machineNo
          if ((!data.obj.detailList || data.obj.detailList.length === 0) && this.enableEdit === true) {
            let detail = {
              stockId: null,
              stockName: null,
              stockSku: null,
              unitPrice: null,
              replenishQuantity: null,
              replenishAmount: null,
              actualUnitQuantity: null,
              actualTotalQuantity: null,
              actualTotalAmount: null,
              janQuantity: null, janAmount: null,
              febQuantity: null, febAmount: null,
              marQuantity: null, marAmount: null,
              aprQuantity: null, aprAmount: null,
              mayQuantity: null, mayAmount: null,
              junQuantity: null, junAmount: null,
              julQuantity: null, julAmount: null,
              augQuantity: null, augAmount: null,
              sepQuantity: null, sepAmount: null,
              octQuantity: null, octAmount: null,
              novQuantity: null, novAmount: null,
              decQuantity: null, decAmount: null,
              monthNumSet: null,
              differenceQuantity: null,
              differenceAmount: null
            }
            this.detailForm.detailList.push(detail)
            this.detailListLoading = false
          } else {
            var that = this
            _.forEach(data.obj.detailList, function (item) {
              that.$set(item, 'unitPrice', item.unitPrice ? item.unitPrice.toFixed(2) : null)
              that.$set(item, 'replenishAmount', (item.replenishQuantity * item.unitPrice).toFixed(2))
              const monthDataSet = item.monthNumSet && item.monthNumSet !== '' ? item.monthNumSet.split(',') : []
              that.$set(item, 'actualTotalQuantity', monthDataSet.length !== 12 ? item.actualUnitQuantity * 17 : _.sum(_.map(monthDataSet, (item) => {return Number(item)})))
              that.$set(item, 'monthDataEdit', !!(item.actualTotalQuantity && item.actualTotalQuantity > 0))
              that.$set(item, 'actualTotalAmount', (item.actualTotalQuantity * item.unitPrice).toFixed(2))
              var that2 = that
              _.forEach(that.monthList, function (m) {
                let quantity = monthDataSet.length !== 12 ? null : monthDataSet[_.indexOf(that2.monthList, m)]
                that2.$set(item, m.code + 'Quantity', quantity)
                that2.$set(item, m.code + 'Amount', quantity ? (quantity * item.unitPrice).toFixed(2) :  null)
              })
              that.$set(item, 'differenceQuantity', item.replenishQuantity - item.actualTotalQuantity)
              that.$set(item, 'differenceAmount', (item.differenceQuantity * item.unitPrice).toFixed(2))
            })
            this.detailForm.detailList = data.obj.detailList
            this.detailListLoading = false
          }
        } else {
          this.$message.error(data.msg)
          this.detailListLoading = false
        }
      })
    },
    getMachineList() {
      this.$http({
        url: this.$http.adornUrl('/admin/mall/self-machine/getMachinesByCurrOrg'),
        method: 'get'
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.machineList = this.enableEdit === true ? data.obj.filter(item => item.enableSelect && item.enableSelect === true) : data.obj
        } else {
          this.$message.error(data.msg)
        }
      })
    },
    getStockList(needChange, id) {
      this.$http({
        url: this.$http.adornUrl('/admin/mall/stock/getStockByOrg'),
        method: 'get',
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.stockList = data.obj
          if (needChange && needChange === true) {
            this.$set(this.detailForm.detailList[this.currentNewStockRowIndex], 'stockId', id)
            this.changeStock(id, this.detailForm.detailList[this.currentNewStockRowIndex])
          }
        } else {
          this.$message.error(data.msg)
        }
      })
    },
    specialRowDeal (row, rowIndex) {
      if (row.row.stockDeleted && row.row.stockDeleted === true) {
        this.existRemoveStock = true
        return 'stock_remove'
      }
    },
    detailSummary (param) {
      const { columns, data } = param;
      const sums = [];
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = '合计';
          return;
        }
        if (column.property !== 'replenishQuantity' && column.property !== 'replenishAmount' && column.property !== 'actualTotalAmount') {
          sums[index] = '';
          return;
        }
        const values = data.map(item => Number(item[column.property]));
        if (!values.every(value => isNaN(value))) {
          sums[index] = values.reduce((prev, curr) => {
            const value = Number(curr);
            if (!isNaN(value)) {
              return prev + curr;
            } else {
              return prev;
            }
          }, 0);
          if (column.property === 'replenishAmount' || column.property === 'actualTotalAmount' || column.property === 'replenishQuantity') {
            if (column.property !== 'replenishQuantity') {
              sums[index] = sums[index].toFixed(2)
            }
            if (column.property === 'actualTotalAmount') {
              this.dataForm.actualAmount = sums[index]
            }
            if (sums[index] > this.dataForm.budgetAmount && column.property !== 'replenishQuantity') {
              const tds_fix = document.querySelectorAll(
                  "#machine_detail_table .el-table__fixed-footer-wrapper tr>td"
              );
              tds_fix[column.property === 'replenishQuantity' ? 2 : (column.property === 'replenishAmount' ? 3 : 6)].childNodes[0].style.color = 'red'
              tds_fix[column.property === 'replenishQuantity' ? 2 : (column.property === 'replenishAmount' ? 3 : 6)].childNodes[0].style.fontWeight = "bold";
              // sums[index] += ((sums[index] + '').indexOf('超预算') >= 0 ? '' : '(超预算)')
            } else {
              const tds_fix = document.querySelectorAll(
                  "#machine_detail_table .el-table__fixed-footer-wrapper tr>td"
              );
              tds_fix[column.property === 'replenishQuantity' ? 2 : (column.property === 'replenishAmount' ? 3 : 6)].childNodes[0].style.color = column.property === 'replenishQuantity' ? 'black' : '#31e134'
              tds_fix[column.property === 'replenishQuantity' ? 2 : (column.property === 'replenishAmount' ? 3 : 6)].childNodes[0].style.fontWeight = "bold";
            }
          }
          sums[index] += '';
        } else {
          sums[index] = '合计失败';
        }
      });
      this.$nextTick(() => {
        const tds_fix = document.querySelectorAll(
            "#machine_detail_table .el-table__fixed-footer-wrapper tr>td"
        );
        tds_fix[0].colSpan = 4;
        tds_fix[0].style.textAlign = "center";
        tds_fix[0].childNodes[0].style.fontWeight = "bold";
        tds_fix[0].style.backgroundColor = "#EBEEF5"
        tds_fix[1].style.display = "none";
        tds_fix[4].colSpan = 2
        tds_fix[4].style.backgroundColor = "#EBEEF5"
        tds_fix[5].style.display = "none";
        tds_fix[68].colSpan = 4;
        tds_fix[68].style.backgroundColor = "#EBEEF5"
        const tds = document.querySelectorAll(
            "#machine_detail_table .el-table__footer-wrapper tr>td"
        );
        tds[7].colSpan = 26
        tds[7].style.backgroundColor = "#EBEEF5"
      });
      return [sums[0]].concat(sums.slice(3));
    },
    changeStock (value, row) {
      if (!value || value === '') {
        return;
      }
      let stock = this.stockList.filter(it => it.id === value)[0]
      if (!stock) {
        this.$set(row, 'stockId', null)
      }
      this.$set(row, 'stockSku', stock ? stock.sku : null)
      this.$set(row, 'stockName', stock ? stock.name : null)
      this.$set(row, 'unitPrice', stock ? stock.unitPrice.toFixed(2) : null)
      this.$set(row, 'replenishQuantity', 0)
      this.$set(row, 'replenishAmount', 0)
      this.$set(row, 'actualUnitQuantity', 0)
      this.$set(row, 'actualTotalQuantity', 0)
      this.$set(row, 'actualTotalAmount', 0)
      this.$set(row, 'differenceQuantity', 0)
      this.$set(row, 'differenceAmount', 0)
      var that = this
      _.forEach(this.monthList, function (item) {
        that.$set(row, item.code + 'Quantity', null)
        that.$set(row, item.code + 'Amount', null)
      })
    },
    clearStock (row) {
      this.$set(row, 'stockSku', null)
      this.$set(row, 'stockName', null)
      this.$set(row, 'unitPrice', null)
      this.$set(row, 'replenishQuantity', null)
      this.$set(row, 'replenishAmount', null)
      this.$set(row, 'actualUnitQuantity', null)
      this.$set(row, 'actualTotalQuantity', null)
      this.$set(row, 'actualTotalAmount', null)
      this.$set(row, 'differenceQuantity', null)
      this.$set(row, 'differenceAmount', null)
      var that = this
      _.forEach(this.monthList, function (item) {
        that.$set(row, item.code + 'Quantity', null)
        that.$set(row, item.code + 'Amount', null)
      })
    },
    changeReplenishQuantity (value, row) {
      this.$set(row, 'replenishAmount', !value ? 0 : (value * row.unitPrice).toFixed(2))
      this.$set(row, 'differenceQuantity', !value && value !== 0 ? 'NaN' : value - row.actualTotalQuantity)
      this.$set(row, 'differenceAmount', (row.replenishAmount - row.actualTotalAmount).toFixed(2))
    },
    changeActualUnitQuantity (curVal, oldVal, row) {
      if (row.monthDataEdit && row.monthDataEdit === true) {
        this.$confirm(`系统检测到您编辑了该条记录的月份数据，若您继续修改实际计划的单位数量，将重新初始化该条记录的月份数据。是否确定修改？`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
          closeOnClickModal: false
        }).then(() => {
          this.actualUnitQuantityTrigger(curVal, row)
          this.$set(row, 'monthDataEdit', false)
        }).catch(() => {
          this.$set(row, 'actualUnitQuantity', oldVal)
        })
      } else {
        this.actualUnitQuantityTrigger(curVal, row)
      }
    },
    actualUnitQuantityTrigger (value, row) {
      this.$set(row, 'actualTotalQuantity', !value ? 0 : value * 17)
      this.$set(row, 'actualTotalAmount', !value ? 0 : (value * 17 * row.unitPrice).toFixed(2))
      var that = this
      _.forEach(this.monthList, function (item) {
        let quantity = !value ? null : (item.code !== 'oct' && item.code !== 'nov' && item.code !== 'dec' ? value : (item.code === 'oct' ? value * 2 : value * 3))
        that.$set(row, item.code + 'Quantity', quantity)
        that.$set(row, item.code + 'Amount', !value ? null : (quantity * row.unitPrice).toFixed(2))
      })
      this.$set(row, 'differenceQuantity', (!row.replenishQuantity && row.replenishQuantity !== 0)|| row.replenishQuantity === '' ? 'NaN' : row.replenishQuantity - row.actualTotalQuantity)
      this.$set(row, 'differenceAmount', (row.replenishAmount - row.actualTotalAmount).toFixed(2))
    },
    changeMonthQuantity(value, row, item) {
      this.$set(row, 'monthDataEdit', true)
      this.$set(row, item.code + 'Amount', !value ? 0 : (value * row.unitPrice).toFixed(2))
      if (!value && value !== 0) {
        return
      }
      let totalQuantity = 0
      _.forEach(this.monthList, function (m) {
        totalQuantity += row[`${m.code}Quantity`]
      })
      this.$set(row, 'actualTotalQuantity', totalQuantity)
      this.$set(row, 'actualTotalAmount', (totalQuantity * row.unitPrice).toFixed(2))
      this.$set(row, 'differenceQuantity', row.replenishQuantity - totalQuantity)
      this.$set(row, 'differenceAmount', (row.replenishAmount - row.actualTotalAmount).toFixed(2))
    },
    newStockHandle(index) {
      this.currentNewStockRowIndex = index
      this.newStockVisible = true
      this.$nextTick(() => {
        this.$refs.newStock.init()
      })
    },
    newStockEmit (id) {
      this.getStockList(true, id)
    },
    generatePlan () {
      this.$confirm(`生成月度补货计划成功后将刷新您已维护的计划列表。是否确定生成？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        closeOnClickModal: false
      }).then(() => {
        this.detailListLoading = true
        this.$http({
          url: this.$http.adornUrl('/admin/mall/stock_replenish_plan/generatePlan'),
          method: 'get',
          params: this.$http.adornParams({
            type: this.dataForm.type,
            budgetAmount: this.dataForm.budgetAmount,
            year: this.dataForm.planYear,
            machineId: this.dataForm.machineId
          })
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.dataForm.planYear = data.obj.planYear
            this.dataForm.budgetAmount = data.obj.budgetAmount
            this.dataForm.machineId = data.obj.machineId
            this.dataForm.machineNo = data.obj.machineNo
            if ((!data.obj.detailList || data.obj.detailList.length === 0) && this.enableEdit === true) {
              let detail = {
                stockId: null,
                stockName: null,
                stockSku: null,
                unitPrice: null,
                replenishQuantity: null,
                replenishAmount: null,
                actualUnitQuantity: null,
                actualTotalQuantity: null,
                actualTotalAmount: null,
                janQuantity: null, janAmount: null,
                febQuantity: null, febAmount: null,
                marQuantity: null, marAmount: null,
                aprQuantity: null, aprAmount: null,
                mayQuantity: null, mayAmount: null,
                junQuantity: null, junAmount: null,
                julQuantity: null, julAmount: null,
                augQuantity: null, augAmount: null,
                sepQuantity: null, sepAmount: null,
                octQuantity: null, octAmount: null,
                novQuantity: null, novAmount: null,
                decQuantity: null, decAmount: null,
                monthNumSet: null,
                differenceQuantity: null,
                differenceAmount: null
              }
              data.obj.detailList.push(detail)
            } else {
              var that = this
              _.forEach(data.obj.detailList, function (item) {
                that.$set(item, 'unitPrice', item.unitPrice.toFixed(2))
                that.$set(item, 'replenishAmount', (item.replenishQuantity * item.unitPrice).toFixed(2))
                const monthDataSet = item.monthNumSet && item.monthNumSet !== '' ? item.monthNumSet.split(',') : []
                that.$set(item, 'actualTotalQuantity', monthDataSet.length !== 12 ? item.actualUnitQuantity * 17 : _.sum(_.map(monthDataSet, (item) => {return Number(item)})))
                that.$set(item, 'monthDataEdit', !!(item.actualTotalQuantity && item.actualTotalQuantity > 0))
                that.$set(item, 'actualTotalAmount', (item.actualTotalQuantity * item.unitPrice).toFixed(2))
                var that2 = that
                _.forEach(that.monthList, function (m) {
                  let quantity = monthDataSet.length !== 12 ? null : monthDataSet[_.indexOf(that2.monthList, m)]
                  that2.$set(item, m.code + 'Quantity', quantity)
                  that2.$set(item, m.code + 'Amount', quantity ? (quantity * item.unitPrice).toFixed(2) :  null)
                })
                that.$set(item, 'differenceQuantity', item.replenishQuantity - item.actualTotalQuantity)
                that.$set(item, 'differenceAmount', (item.differenceQuantity * item.unitPrice).toFixed(2))
              })
            }
            this.$set(this.detailForm, 'detailList', data.obj.detailList)
            this.detailListLoading = false
          } else {
            this.$message.error(data.msg)
            this.detailListLoading = false
          }
        })
      }).catch(() => {
      })
    },
    dataFormSubmit(type) {
      this.$set(this.dataForm, 'detailList', this.detailForm.detailList)
      const api = (type === 'draft' ? '/admin/mall/stock_replenish_plan/saveOrUpdateDraft' : '/admin/mall/stock_replenish_plan/submit')
      if (type === 'submit') {
        this.$confirm(`提交后将无法修改，确定进行提交操作?`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
          closeOnClickModal: false
        }).then(() => {
          this.submitFun(api)
        })
      } else {
        this.submitFun(api)
      }
    },
    submitFun (api) {
      this.$refs['detailForm'].validate((valid) => {
        if (valid) {
          this.$refs['dataForm'].validate((valid) => {
            if (valid) {
              var that = this
              _.forEach(this.dataForm.detailList.filter(it => it.stockId && it.stockId !== ''), function (item) {
                if (item.actualUnitQuantity && item.actualUnitQuantity !== 0) {
                  const quantitySet = []
                  _.forEach(that.monthList, function (m) {
                    quantitySet.push(item[`${m.code}Quantity`])
                  })
                  item.monthNumSet = quantitySet.join(',')
                }
              })
              this.dataFormLoading = true
              this.$http({
                url: this.$http.adornUrl(api),
                method: 'post',
                data: this.$http.adornData(this.dataForm)
              }).then(({data}) => {
                if (data && data.code === 0) {
                  this.$message({
                    message: '操作成功',
                    type: 'success',
                    duration: 1500,
                    onClose: () => {
                      this.dataFormLoading = false
                      this.visible = false
                      this.$emit('refreshDataList')
                    }
                  })
                } else {
                  this.$message.error(data.msg)
                }
                this.dataFormLoading = false
              })
            }
          })
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
#add_record {
  padding: 0;
  border: 0;
  font-size: 18px;
  color: #606266;
  background-color: rgba(0,0,0,0);
}
#add_record:hover{
  color: #00aaf7;
}
#remove_record {
  padding: 0;
  border: 0;
  font-size: 18px;
  color: #606266;
  background-color: rgba(0,0,0,0);
}
#remove_record:hover{
  color: #ed0004;
}
#machine_detail_table ::v-deep .el-form-item__error {
  position: static;
  text-align: center;
}
#machine_detail_table ::v-deep .el-table__body-wrapper {
  z-index: 2;
}
#plan_generate {
  color: #409EFF;
  background: #ecf5ff;
  border-color: #b3d8ff;
}
#plan_generate:hover{
  color: #ecf5ff;
  background: #409EFF;
}
#machine_detail_table ::v-deep .stock_remove {
  background-color: #fc6e6e;
}
.stock_new:hover{
  background-color: rgba(0,0,0,0);
}
</style>
