<template>
  <div>
    <el-dialog :title="!dataForm.id ? '新增' : '修改'" :close-on-click-modal="false" width="80%"
               :visible.sync="visible">
      <el-form :model="dataForm" :rules="dataRule" ref="dataForm"
               label-width="120px">
        <el-row :gutter="20">
          <el-col :span="12" v-if="this.managerCapacity === 'ASSOCIATION_ADMIN'">
            <el-form-item :label="'所属组织架构'" prop="orgCode" :error="orgCodeError">
              <el-select style="width: 100%" placeholder="所属组织机构" v-model="dataForm.orgCode" clearable>
                <el-option v-for="item in orgList"
                           :key="item.code" :label="item.name" :value="item.code"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="物料名称" prop="name" :error="nameError">
              <el-input v-model="dataForm.name" placeholder="物料名称"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="物料编码唯一" prop="sku" :error="skuError">
              <el-input v-model="dataForm.sku" :disabled="dataForm.id !=undefined" placeholder="物料编码唯一"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="物料总量" prop="inventoryTotalQuantity">
              <el-input-number v-model="dataForm.inventoryTotalQuantity" controls-position="right" :min="0"
                               :disabled="dataForm.id !=undefined">
              </el-input-number>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="物料单价" prop="unitPrice" :error="unitPriceError">
              <el-input-number v-model="dataForm.unitPrice" placeholder="物料单价" :precision="2" controls-position="right"
                               :step="0.1"></el-input-number>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="物料描述" prop="description" :error="descriptionError">
              <el-input type="textarea" :rows="3" maxlength="200" show-word-limit v-model="dataForm.description"
                        placeholder="物料描述"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

      <span slot="footer" class="dialog-footer">
				<el-button @click="visible = false,locateVisible = false">取消</el-button>
				<el-button type="primary" :disabled="isDisabled" @click="dataFormSubmit()">确定</el-button>
			</span>
    </el-dialog>
  </div>
</template>

<script>
import moment from 'moment'

export default {
  data() {
    return {
      visible: false,
      orgList: [],
      typeList: [],
      isDisabled: false,
      managerCapacity: '',
      initForm: {
        id: null,
        version: null,
        name: null,
        sku: null,
        inventoryTotalQuantity: 0,
        unitPrice: null,
        description: null,
        orgCode: '',
      },
      dataForm: {},
      postForm: {
        detailedAddress: '',
        longitude: '',
        latitude: '',
      },
      dataRule: {
        name: [{
          required: true,
          message: '物料名称不能为空',
          trigger: 'blur'
        }],
        sku: [{
          required: true,
          message: '物料编码不能为空',
          trigger: 'blur'
        }],
        unitPrice: [{
          required: true,
          message: '物料单价不能为空',
          trigger: 'blur'
        }],
        description: [{
          required: true,
          message: '物料描述不能为空',
          trigger: 'blur'
        }],
        orgCode: [{
          required: true,
          message: '所属组织架构不能为空',
          trigger: 'blur'
        }]
      },
      nameError: null,
      descriptionError: null,
      skuError: null,
      unitPriceError: null,
      orgCodeError: null
    }
  },
  components: {
    moment
  },
  methods: {
    async init(id) {
      this.dataForm = _.cloneDeep(this.initForm)
      this.dataForm.id = id || null
      this.managerCapacity = this.$store.state.user.managerCapacity
      this.dataForm.orgCode = this.$store.state.user.orgCode
      this.visible = true
      await this.getOrg()
      this.$nextTick(() => {
        this.$refs['dataForm']?.resetFields()
        if (this.dataForm.id) {
          this.$http({
            url: this.$http.adornUrl(`/admin/mall/stock`),
            method: 'get',
            params: this.$http.adornParams({
              id: this.dataForm.id
            })
          }).then(({
                     data
                   }) => {
            if (data && data.code === 0) {
              this.dataForm = data.obj
            }
          })
        }
      })
    },
    close() {
      this.locateVisible = false
    },
    //获取所属区域
    async getOrg() {
      const {
        data
      } = await this.$http({
        url: this.$http.adornUrl(`/admin/org/getFiveAreaSubAssociationOrg`),
        method: 'get',
        params: this.$http.adornParams({withTop: true})
      })
      if (data && data.code === 0) {
        this.orgList = data.obj
      } else {
        this.orgList = []
      }
    },
    // 表单提交
    dataFormSubmit() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          if (this.dataForm.unitPrice == null || this.dataForm.unitPrice == '') {
            this.$message.error('物料单价不能为空')
            return
          }
          this.isDisabled = true
          this.$http({
            url: this.$http.adornUrl(
                `/admin/mall/stock/${!this.dataForm.id ? 'saveMallStock' : 'update'}`),
            method: 'post',
            data: this.$http.adornData(this.dataForm)
          }).then(({
                     data
                   }) => {
            if (data && data.code === 0) {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 1500,
                onClose: () => {
                  this.visible = false
                  debugger
                  console.log(data)
                  if (!this.dataForm.id) {
                    this.$emit('refreshDataList', data.obj.id)
                  } else {
                    this.$emit('refreshDataList')
                  }
                  this.isDisabled = false
                }
              })
            } else if (data && data.code === 303) {
              for (let it of data.obj) {
                this[`${it.field}Error`] = it.message
              }
              this.isDisabled = false
            } else {
              this.$message.error(data.msg)
              this.isDisabled = false
            }
          })
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
:deep(.map-dialog) {
  .el-dialog__body {
    padding: 15px 20px 30px;
  }
}

:deep(.address-item) {
  .el-form-item__content {
    display: flex;
    flex-direction: row;

    .el-button {
      margin-left: 15px;
    }
  }
}
</style>
