<template>
	<div>
		<el-dialog title="机器商品兑换记录" :close-on-click-modal="false" :visible.sync="visible" width="80%">
			<el-table :data="dataList" border v-loading="dataListLoading" @selection-change="selectionChangeHandle"
				style="width: 100%;">
				<el-table-column type="selection" header-align="center" align="center" width="50">
				</el-table-column>
				<el-table-column type="index" header-align="center" align="center" width="50" label="序号">
				</el-table-column>

				<el-table-column prop="exchangeTypeText" header-align="center" align="center" label="兑换来源">
				</el-table-column>
				<el-table-column prop="exchangeUserName" header-align="center" align="center" label="兑换人">
				</el-table-column>
				<el-table-column prop="exchangeUserLinkPhone" header-align="center" align="center" label="联系方式">
				</el-table-column>
				<el-table-column prop="exchangeTime" header-align="center" align="center" label="兑换时间">
				</el-table-column>

				<el-table-column prop="goodsName" header-align="center" align="center" label="商品名称">
				</el-table-column>
				<el-table-column prop="goodsPoint" header-align="center" align="center" label="所需积分/件">
				</el-table-column>
				<el-table-column prop="exchangeBeforeScore" header-align="center" align="center" label="兑换前积分">
				</el-table-column>

				<el-table-column prop="exchangeNum" header-align="center" align="center" label="兑换数量">
				</el-table-column>
				<el-table-column prop="exchangePoint" header-align="center" align="center" label="兑换积分">
				</el-table-column>
				<el-table-column prop="exchangeAfterScore" header-align="center" align="center" label="剩余积分">
				</el-table-column>


				<el-table-column prop="statusText" header-align="center" align="center" label="状态">
				</el-table-column>

			</el-table>
			<el-pagination @size-change="sizeChangeHandle" @current-change="currentChangeHandle"
				:current-page="pageIndex" :page-sizes="[10, 20, 50, 100]" :page-size="pageSize" :total="totalPage"
				layout="total, sizes, prev, pager, next, jumper">
			</el-pagination>
		</el-dialog>
	</div>
</template>

<script>
	export default {
		data() {
			return {
				visible: false,
				dataList: [],
				dataListLoading: false,
				dataForm: {
					goodsId: null
				},
				pageIndex: 1, // 当前页
				pageSize: 10, // 分页大小
				totalPage: 0,
			}
		},
		methods: {
			init(machineNo) {
				this.dataForm.machineNo = machineNo || null
				this.visible = true
				this.getDataList()
			},
			getDataList() {
				if (!this.dataForm.machineNo) {
					this.$message.error('未获取到机器编码')
					return
				}
				this.dataListLoading = true
				this.$http({
					url: this.$http.adornUrl('/admin/mall/exchange/getPages'),
					method: 'post',
					data: this.$http.adornData({
						currentPage: this.pageIndex,
						pageSize: this.pageSize,
						machineNo: this.dataForm.machineNo,
						...this.dataForm
					})
				}).then(({
					data
				}) => {
					if (data && data.code === 0) {
						this.dataList = data.obj.records
						this.totalPage = data.obj.total
					} else {
						this.$message.error('获取机器商品兑换记录失败')
					}
					this.dataListLoading = false
				})
			},
			// 分页, 每页条数
			sizeChangeHandle(val) {
				this.pageSize = val
				this.pageIndex = 1
				this.getDataList()
			},
			// 分页, 当前页
			currentChangeHandle(val) {
				this.pageIndex = val
				this.getDataList()
			}
		}
	}
</script>
