<template>
  <div>
    <el-dialog :title="!dataForm.id ? '新增' : '修改'" :close-on-click-modal="false" width="80%"
               :visible.sync="visible">
      <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmit()"
               label-width="120px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="机器编号" prop="machineNo" :error="machineNoError">
              <el-input v-model="dataForm.machineNo" placeholder="机器编号"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="'所属组织架构'" prop="orgCodeList">
              <el-cascader style="width: 100%" placeholder="所属组织架构" v-model="dataForm.orgCodeList" :options="orgList"
                           :show-all-levels="false" :props="{checkStrictly: true,label: 'name',value: 'code'}">
              </el-cascader>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item class="address-item" label="地址" prop="address" :error="addressError">
              <el-input style="width: 80%" v-model="dataForm.address" placeholder="地址"></el-input>
              <el-button style="width: 15%; float: right" type="primary" @click="showMap">地图定位</el-button>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="'状态'" prop="status">
              <el-dict :code="'mall_self_machine_status'" v-model="dataForm.status"></el-dict>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="负责人名称" prop="linkPersonName" :error="linkPersonNameError">
              <el-input v-model="dataForm.linkPersonName" placeholder="负责人名称"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="负责人联系电话" prop="linkPhone" :error="linkPhoneError">
              <el-input v-model="dataForm.linkPhone" placeholder="负责人联系电话"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="备注" prop="remark" :error="remarkError">
              <el-input type="textarea"  :rows="3" maxlength="200" show-word-limit v-model="dataForm.remark" placeholder="备注"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="主图：" prop="machineLogo">
              <el-upload class="avatar-uploader" :action="this.$http.adornUrl(`/admin/oss/upload`)"
                         :headers="headers" :data="{serverCode: uploadOptions.serverCode, media: false}"
                         :show-file-list="false" :on-success="successHandle" :on-change="changHandle"
                         :on-exceed="exceedHandle" :before-upload="beforeUploadHandle">
                <img v-if="dataForm.machineLogo" :src="$http.adornAttachmentUrl(dataForm.machineLogo)"
                     class="avatar">
                <i v-else class="el-icon-plus avatar-uploader-icon"></i>
              </el-upload>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <span slot="footer" class="dialog-footer">
				<el-button @click="visible = false">取消</el-button>
				<el-button type="primary" @click="dataFormSubmit()">确定</el-button>
			</span>
      <amap :default-value="postForm.detailedAddress" ref="amap" @setLocation="setLocation"></amap>
    </el-dialog>
    <el-dialog class="map-dialog" title="地图" :close-on-click-modal="false" width="70%" :visible.sync="mapVisible">
      <AMapInfo v-if="mapVisible" :mapVisible.sync="mapVisible" :address.sync="dataForm.address"
                :latitude.sync="dataForm.latitude" :longitude.sync="dataForm.longitude"></AMapInfo>
    </el-dialog>
  </div>
</template>

<script>
import {
  getFathersById
} from '@/utils'
import editMixin from '@/mixins/edit-mixins'
import AMapInfo from '@/components/map/a-map-info.vue'
import fileUploadMixin from '@/mixins/file-upload-mixins'
import {is8lPhone, isMobile} from "@/utils/validate";

export default {
  mixins: [editMixin, fileUploadMixin],
  data() {
    const validateContactPhone = (rule, value, callback) => {
      if (!isMobile(value) && !is8lPhone(value)) {
        callback(new Error('负责人联系电话须为手机号或区号+8位座机号例如051288888888或0512-88888888'))
      } else {
        callback()
      }
    }
    return {
      visible: false,
      mapVisible: false,
      orgList: [],
      uploadOptions: {
        fieldName: 'machineLogo',
        maxSize: 2
      },
      initForm: {
        id: null,
        version: null,
        machineNo: '',
        orgCode: '',
        orgName: '',
        address: '',
        longitude: '',
        latitude: '',
        linkPersonName: '',
        linkPhone: '',
        status: '',
        machineLogo: '',
        remark: '',
        orgCodeList: []
      },
      dataForm: {},
      postForm: {
        detailedAddress: '',
        longitude: '',
        latitude: '',
      },
      dataRule: {
        machineNo: [{
          required: true,
          message: '机器编码不能为空',
          trigger: 'blur'
        }],
        orgCode: [{
          required: true,
          message: '组织机构code不能为空',
          trigger: 'blur'
        }],
        orgName: [{
          required: true,
          message: '组织机构名称不能为空',
          trigger: 'blur'
        }],
        address: [{
          required: true,
          message: '地址不能为空',
          trigger: 'blur'
        }],
        linkPersonName: [{
          required: true,
          message: '负责人名称不能为空',
          trigger: 'blur'
        }],
        linkPhone: [{
          required: true,
          message: '负责人联系电话不能为空',
          trigger: 'blur'
        }, {validator: validateContactPhone, trigger: 'blur'}],
        orgCodeList: [{
          required: true,
          message: '所属区域',
          trigger: 'blur'
        }],
        status: [{
          required: true,
          message: '状态数据字典值不能为空',
          trigger: 'blur'
        }]
      },
      machineNoError: null,
      orgCodeError: null,
      orgNameError: null,
      addressError: null,
      gomachineLogoError: null,
      longitudeError: null,
      latitudeError: null,
      linkPersonNameError: null,
      linkPhoneError: null,
      statusError: null,
      remarkError: null
    }
  },
  components: {
    AMapInfo
  },
  methods: {
    async init(id) {
      this.dataForm = _.cloneDeep(this.initForm)
      this.dataForm.id = id || null
      this.visible = true
      await this.getOrg()
      this.$nextTick(() => {
        this.$refs['dataForm']?.resetFields()
        if (this.dataForm.id) {
          this.$http({
            url: this.$http.adornUrl(`/admin/mall/self-machine`),
            method: 'get',
            params: this.$http.adornParams({
              id: this.dataForm.id
            })
          }).then(({
                     data
                   }) => {
            if (data && data.code === 0) {
              this.dataForm = data.obj
              this.dataForm['orgCodeList'] = getFathersById(data.obj.orgCode, this
                  .orgList)
            }
          })
        }
      })
    },
    //获取所属区域
    async getOrg() {
      const {
        data
      } = await this.$http({
        url: this.$http.adornUrl(`/admin/org/getOrgTree`),
        method: 'get',
        params: this.$http.adornParams()
      })
      if (data && data.code === 0) {
        this.orgList = this.getTreeData(data.obj)
        console.log(this.orgList)
      } else {
        this.orgList = []
      }
    },
    showMap() {
      this.mapVisible = true
    },
    // 表单提交
    dataFormSubmit() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.dataForm.orgCode = Array.isArray(this.dataForm.orgCodeList) ? this.dataForm
              .orgCodeList[this.dataForm.orgCodeList.length - 1] : this.dataForm.orgCodeList
          this.$http({
            url: this.$http.adornUrl(
                `/admin/mall/self-machine/${!this.dataForm.id ? 'save' : 'update'}`),
            method: 'post',
            data: this.$http.adornData(this.dataForm)
          }).then(({
                     data
                   }) => {
            if (data && data.code === 0) {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 1500,
                onClose: () => {
                  this.visible = false
                  this.$emit('refreshDataList')
                }
              })
            } else if (data && data.code === 303) {
              for (let it of data.obj) {
                this[`${it.field}Error`] = it.message
              }
            } else {
              this.$message.error(data.msg)
            }
          })
        }
      })
    },
    handleImgRemove() {
      this.dataForm.machineLogo = null
    },
    locate() {
      this.locateVisible = true
      this.$nextTick(() => {
        this.$refs?.amap.init()
      })
    },
    close() {
      this.locateVisible = false
    },
    setLocation({
                  address,
                  lat,
                  lng,
                  name
                }) {
      this.postForm.detailedAddress = address
      this.postForm.longitude = String(lng)
      this.postForm.latitude = String(lat)
      this.dataForm.checkInLocation = address
      this.locateVisible = false
    },
    // *处理点位分类最后children数组为空的状态
    getTreeData: function (data) {
      let that = this
      // 循环遍历json数据
      data.forEach(function (e) {
        if (!e.children || e.children.length < 1) {
          e.children = undefined
        } else {
          // children若不为空数组，则继续 递归调用 本方法
          that.getTreeData(e.children)
        }
      })
      return data
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep .map-dialog {
  .el-dialog__body {
    padding: 15px 20px 30px;
  }
}
</style>
