<template>
    <el-dialog :title="!dataForm.id ? '新增' : '编辑'" :close-on-click-modal="false"   :visible.sync="visible" width="60%">
        <el-form :model="dataForm" :rules="dataRule" ref="dataForm" label-width="150px">
            <el-row :gutter="20">
                <el-col :span="12">
                    <el-form-item label="志愿者姓名" prop="volunteerName">
                        <el-input v-model="dataForm.volunteerName" placeholder="志愿者姓名" readonly="readonly"></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="志愿者联系方式" prop="volunteerPhone">
                        <el-input v-model="dataForm.volunteerPhone" placeholder="志愿者联系方式" readonly="readonly"></el-input>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row :gutter="20">

                <el-col :span="12">
                    <el-form-item label="监护人姓名" prop="guardianName">
                        <el-input v-model="dataForm.guardianName" placeholder="监护人姓名"></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="监护人联系方式" prop="guardianPhone">
                        <el-input v-model="dataForm.guardianPhone" placeholder="监护人联系方式"></el-input>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row :gutter="20">

                <el-col :span="12">
                    <el-form-item label="监护人证件类型" prop="guardianCertificateType">
                        <el-dict :code="'certificate_type'" v-model="dataForm.guardianCertificateType"></el-dict>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="监护人证件号" prop="guardianCertificateId">
                        <el-input v-model="dataForm.guardianCertificateId" placeholder="监护人证件号"></el-input>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row :gutter="20">

                <el-col :span="12">
                    <el-form-item label="监护人工作单位" prop="guardianWorkUnit">
                        <el-input v-model="dataForm.guardianWorkUnit" placeholder="监护人工作单位"></el-input>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row :gutter="20">
                <el-col :span="12">
                    <el-form-item label="参加人姓名" prop="participantName">
                        <el-input v-model="dataForm.participantName" placeholder="参加人姓名"></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="参加人联系方式" prop="participantPhone">
                        <el-input v-model="dataForm.participantPhone" placeholder="参加人联系方式"></el-input>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row :gutter="20">
                <el-col :span="12">
                    <el-form-item label="参加人证件类型" prop="participantCertificateType">
                        <el-dict :code="'certificate_type'" v-model="dataForm.participantCertificateType"></el-dict>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="参加人证件号" prop="participantCertificateId">
                        <el-input v-model="dataForm.participantCertificateId" placeholder="参加人证件号" @blur="getSex()"></el-input>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row :gutter="20">
                <el-col :span="12">
                    <el-form-item label="参加人学校" prop="participantSchool">
                        <el-input v-model="dataForm.participantSchool" placeholder="参加人学校"></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="参加人年龄" prop="participantAge">
                        <el-input-number v-model="dataForm.participantAge "  controls-position="right"
                                         :min="0" ></el-input-number>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row :gutter="20">
                <el-col :span="12">
                    <el-form-item label="参加人性别" prop="participantSex">
                        <el-radio-group v-model="dataForm.participantSex"
                                        :disabled="this.dataForm.participantCertificateType === 'certificate_type_03'  ">
                            <el-radio :label="'m'">男</el-radio>
                            <el-radio :label="'f'">女</el-radio>
                        </el-radio-group>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>

        <span slot="footer" class="dialog-footer">
				<el-button @click="visible = false">取消</el-button>
				<el-button type="primary" @click="dataFormSubmit()">确定</el-button>
			</span>
    </el-dialog>
</template>

<script>
export default {
    data() {
        return {
            visible: false,
            isDisabled: false,
            initForm: {
                id: null,
                version: null,
                volunteerId: null,
                volunteerName: null,
                volunteerPhone: null,
                guardianName: null,
                guardianPhone: null,
                guardianCertificateType: null,
                guardianCertificateId: null,
                guardianWorkUnit: null,
                participantName: null,
                participantPhone: null,
                participantCertificateType: null,
                participantCertificateId: null,
                participantSchool: null,
                participantAge: null,
                participantSex: null
            },
            dataForm: {},
            dataRule: {

                volunteerPhone: [
                    {required: true, message: '志愿者联系方式不能为空', trigger: 'blur'}
                ],
                guardianName: [
                    {required: true, message: '监护人姓名不能为空', trigger: 'blur'}
                ],
                guardianPhone: [
                    {required: true, message: '监护人联系方式不能为空', trigger: 'blur'}
                ],
                guardianCertificateType: [
                    {required: true, message: '监护人证件类型不能为空', trigger: 'blur'}
                ],
                guardianCertificateId: [
                    {required: true, message: '监护人证件号不能为空', trigger: 'blur'}
                ],
                guardianWorkUnit: [
                    {required: true, message: '监护人工作单位不能为空', trigger: 'blur'}
                ],
                participantName: [
                    {required: true, message: '参加人姓名不能为空', trigger: 'blur'}
                ],
                participantPhone: [
                    {required: true, message: '参加人联系方式不能为空', trigger: 'blur'}
                ],
                participantCertificateType: [
                    {required: true, message: '参加人证件类型不能为空', trigger: 'blur'}
                ],
                participantCertificateId: [
                    {required: true, message: '参加人证件号不能为空', trigger: 'blur'}
                ],
                participantSchool: [
                    {required: true, message: '参加人学校不能为空', trigger: 'blur'}
                ],
                participantAge: [
                    {required: true, message: '参加人年龄不能为空', trigger: 'blur'}
                ],
                participantSex: [
                    {required: true, message: '参加人性别不能为空', trigger: 'blur'}
                ]
            }
        }
    },
    components: {},
    methods: {
        async init(id) {
            this.dataForm = _.cloneDeep(this.initForm)
            this.dataForm.id = id || null
            this.visible = true
            this.$nextTick(() => {
                this.$refs['dataForm']?.resetFields()
                if (this.dataForm.id) {
                    this.$http({
                        url: this.$http.adornUrl(`/admin/mall/score/goods/apply`),
                        method: 'get',
                        params: this.$http.adornParams({
                            id: this.dataForm.id
                        })
                    }).then(({data}) => {
                        if (data && data.code === 0) {
                            this.dataForm = data.obj
                        }
                    })
                }
            })
        },
        dataFormSubmit() {
            this.$refs['dataForm'].validate((valid) => {
                if (valid) {
                    this.$http({
                        url: this.$http.adornUrl(
                            `/admin/mall/score/goods/apply/${!this.dataForm.id ? 'save' : 'update'}`),
                        method: 'post',
                        data: this.$http.adornData(this.dataForm)
                    }).then(({
                                 data
                             }) => {
                        if (data && data.code === 0) {
                            this.$message({
                                message: '操作成功',
                                type: 'success',
                                duration: 1500,
                                onClose: () => {
                                    this.visible = false
                                    debugger
                                    console.log(data)
                                    if (!this.dataForm.id) {
                                        this.$emit('refreshDataList', data.obj.id)
                                    } else {
                                        this.$emit('refreshDataList')
                                    }
                                    this.isDisabled = false
                                }
                            })
                        } else if (data && data.code === 303) {
                            for (let it of data.obj) {
                                this[`${it.field}Error`] = it.message
                            }
                            this.isDisabled = false
                        } else {
                            this.$message.error(data.msg)
                            this.isDisabled = false
                        }
                    })
                }
            })
        },
        //根据身份证判断是否成年，以及性别
        getSex() {
            var IdCard = this.dataForm.participantCertificateId
            if (this.dataForm.participantCertificateType !== 'certificate_type_03') {
                return true;
            }
            if (parseInt(IdCard.substr(16, 1)) % 2 === 1) {
                this.dataForm.participantSex = "m"
            } else {
                this.dataForm.participantSex = "f"
            }
            var ageDate = new Date()
            var month = ageDate.getMonth() + 1
            var day = ageDate.getDate()
            var age = ageDate.getFullYear() - IdCard.substring(6, 10) - 1
            if (IdCard.substring(10, 12) < month || IdCard.substring(10, 12) === month && IdCard.substring(12, 14) <= day) {
                age++
            }
            this.dataForm.participantAge = age
        },
    }
}
</script>
