<template>
    <el-dialog :title="!dataForm.id ? '新增' : '编辑'" :close-on-click-modal="false"   :visible.sync="visible" width="60%">
        <el-form :model="dataForm" :rules="dataRule" ref="dataForm" label-width="150px">
            <el-row :gutter="20">
                <el-col :span="12">
                    <el-form-item label="志愿者姓名" prop="volunteerName">
                        <el-input v-model="dataForm.volunteerName" placeholder="志愿者姓名" readonly="readonly"></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="志愿者联系方式" prop="volunteerPhone">
                        <el-input v-model="dataForm.volunteerPhone" placeholder="志愿者联系方式" readonly="readonly"></el-input>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row :gutter="20">

                <el-col :span="12">
                    <el-form-item label="志愿者证件类型" prop="volunteerCertificateType">
                        <el-dict :code="'certificate_type'" v-model="dataForm.volunteerCertificateType" disabled="disabled"></el-dict>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="志愿者证件号" prop="volunteerCertificateId">
                        <el-input v-model="dataForm.volunteerCertificateId" placeholder="志愿者证件号" readonly="readonly"></el-input>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row :gutter="20">
                <el-col :span="24">
                    <el-form-item label="志愿者户籍所在地" prop="volunteerAddress">
                        <el-input v-model="dataForm.volunteerAddress" placeholder="志愿者户籍所在地"></el-input>
                    </el-form-item>
                </el-col>
            </el-row>

            <el-row :gutter="20">
                <el-col :span="12">
                    <el-form-item label="老人姓名" prop="elderName">
                        <el-input v-model="dataForm.elderName" placeholder="老人姓名"></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="老人电话" prop="elderPhone">
                        <el-input v-model="dataForm.elderPhone" placeholder="老人电话"></el-input>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row :gutter="20">
                <el-col :span="12">
                    <el-form-item label="老人身份证号码" prop="elderCertificateId">
                        <el-input v-model="dataForm.elderCertificateId" placeholder="老人身份证号码" ></el-input>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row :gutter="20">

                <el-col :span="24">
                    <el-form-item label="老人户籍所在地址" prop="elderAddress">
                        <el-input v-model="dataForm.elderAddress" placeholder="老人户籍所在地址"></el-input>
                    </el-form-item>
                </el-col>
            </el-row>

        </el-form>

        <span slot="footer" class="dialog-footer">
				<el-button @click="visible = false">取消</el-button>
				<el-button type="primary" @click="dataFormSubmit()">确定</el-button>
			</span>
    </el-dialog>
</template>

<script>
export default {
    data() {
        return {
            visible: false,
            isDisabled: false,
            initForm: {
                id: null,
                version: null,
                volunteerId: null,
                volunteerName: null,
                volunteerPhone: null,
                volunteerAddress: null,
                volunteerCertificateType:null,
                volunteerCertificateId:null,
                elderName: null,
                elderPhone: null,
                elderAddress: null,
                elderCertificateType:null,
                elderCertificateId:null
            },
            dataForm: {},
            dataRule: {

                volunteerPhone: [
                    {required: true, message: '志愿者联系方式不能为空', trigger: 'blur'}
                ],
                volunteerName: [
                    {required: true, message: '监护人姓名不能为空', trigger: 'blur'}
                ],

                volunteerCertificateType: [
                    {required: true, message: '志愿者证件类型不能为空', trigger: 'blur'}
                ],
                volunteerCertificateId: [
                    {required: true, message: '志愿者证件号不能为空', trigger: 'blur'}
                ],
                volunteerAddress: [
                    {required: true, message: '户籍所在地址不能为空', trigger: 'blur'}
                ],
                elderName: [
                    {required: true, message: '老人姓名不能为空', trigger: 'blur'}
                ],
                elderPhone: [
                    {required: true, message: '老人电话不能为空', trigger: 'blur'}
                ],
                elderCertificateId: [
                    {required: true, message: '老人身份证号码不能为空', trigger: 'blur'}
                ],
                elderAddress: [
                    {required: true, message: '老人户籍所在地址不能为空', trigger: 'blur'}
                ],
            }
        }
    },
    components: {},
    methods: {
        async init(id) {
            this.dataForm = _.cloneDeep(this.initForm)
            this.dataForm.id = id || null
            this.visible = true
            this.$nextTick(() => {
                this.$refs['dataForm']?.resetFields()
                if (this.dataForm.id) {
                    this.$http({
                        url: this.$http.adornUrl(`/admin/mall/score/goods/elder/apply`),
                        method: 'get',
                        params: this.$http.adornParams({
                            id: this.dataForm.id
                        })
                    }).then(({data}) => {
                        if (data && data.code === 0) {
                            this.dataForm = data.obj
                        }
                    })
                }
            })
        },
        dataFormSubmit() {
            this.$refs['dataForm'].validate((valid) => {
                if (valid) {
                    this.$http({
                        url: this.$http.adornUrl(
                            `/admin/mall/score/goods/elder/apply/${!this.dataForm.id ? 'save' : 'update'}`),
                        method: 'post',
                        data: this.$http.adornData(this.dataForm)
                    }).then(({
                                 data
                             }) => {
                        if (data && data.code === 0) {
                            this.$message({
                                message: '操作成功',
                                type: 'success',
                                duration: 1500,
                                onClose: () => {
                                    this.visible = false
                                    debugger
                                    console.log(data)
                                    if (!this.dataForm.id) {
                                        this.$emit('refreshDataList', data.obj.id)
                                    } else {
                                        this.$emit('refreshDataList')
                                    }
                                    this.isDisabled = false
                                }
                            })
                        } else if (data && data.code === 303) {
                            for (let it of data.obj) {
                                this[`${it.field}Error`] = it.message
                            }
                            this.isDisabled = false
                        } else {
                            this.$message.error(data.msg)
                            this.isDisabled = false
                        }
                    })
                }
            })
        },

    }
}
</script>
