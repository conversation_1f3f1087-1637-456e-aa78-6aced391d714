<template>
	<el-dialog title="核销" :visible.sync="visible" :append-to-body="true" width="20%">
		<el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmit()"
			label-width="80px">
			<el-form-item label="核销码" prop="verificationCode">
				<el-input v-model="dataForm.verificationCode"></el-input>
			</el-form-item>
		</el-form>
		<span slot="footer" class="dialog-footer">
			<el-button @click="visible = false">取消</el-button>
			<el-button type="primary" @click="dataFormSubmit()">确定</el-button>
		</span>
	</el-dialog>
</template>

<script>
	export default {
		data() {
			return {
				visible: false,
				dataList: [],
				dataListLoading: false,
				dataForm: {
					exchangeId: null,
					verificationCode: null
				},
				dataRule: {
					verificationCode: [{
						required: true,
						message: '核销码不能为空',
						trigger: 'blur'
					}]
				},
				verificationCodeError: null
			}
		},

		methods: {
			init(id) {
				this.dataForm.exchangeId = id || null
				this.visible = true
				this.$refs['dataForm']?.resetFields()
			},
			// 表单提交
			dataFormSubmit() {
				this.$refs['dataForm'].validate((valid) => {
					if (valid) {
						this.$http({
							url: this.$http.adornUrl('/admin/mall/exchange/writeOffVerificationCode'),
							method: 'post',
							params: this.$http.adornParams({
								'exchangeId': this.dataForm.exchangeId,
								'verificationCode': this.dataForm.verificationCode
							})
						}).then(({
							data
						}) => {
							if (data && data.code === 0) {
								this.$message({
									message: '操作成功',
									type: 'success',
									duration: 1000,
									onClose: () => {
										this.visible = false
										this.$emit('refreshDataList')
									}
								})
							} else {
								this.$message.error(data.msg)
							}
						})
					}
				})
			}
		}
	}
</script>
