<template>
  <div class="mod-config">
    <el-form :inline="true" ref="dataForm" :model="dataForm" @keyup.enter.native="queryPage()">
      <el-form-item label="关键字" prop="keyWord">
        <el-input v-model="dataForm.keyWord" placeholder="商品名称/负责人/联系电话" clearable></el-input>
      </el-form-item>
      <el-form-item :label="'商品类型'" prop="scoreGoodsType">
        <el-dict style="width: 100%" :code="'mall_score_goods_type'" v-model="dataForm.scoreGoodsType"></el-dict>
      </el-form-item>
      <el-form-item v-if='isSubAssociationAdmin' :label="'所属组织机构'" prop="orgCodeList">
        <el-select placeholder="所属组织机构" v-model="dataForm.orgCode" clearable>
          <el-option v-for="item in orgList"
                     :key="item.code" :label="item.name" :value="item.code"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="积分" prop="minRequiredScore">
        <el-input v-model="dataForm.minRequiredScore" placeholder="积分最小值"></el-input>
      </el-form-item>
      <el-form-item label="~" prop="maxRequiredScore">
        <el-input v-model="dataForm.maxRequiredScore" placeholder="积分最大值"></el-input>
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="dataForm.status" clearable>
          <el-option v-for="item in [{label: '上架', value: 'true'}, {label: '下架', value: 'false'}]"
                     :key="item.value" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </el-form-item>
        <el-form-item  label="是否是星光闪耀商品" prop="star">
            <el-select v-model="dataForm.star" placeholder="请选择" clearable>
                <el-option
                        :label="'是'"
                        :value="true">
                </el-option>
                <el-option
                        :label="'否'"
                        :value="false">
                </el-option>
            </el-select>
        </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-search" type="warning" @click="queryPage()">查询</el-button>
        <el-button @click="resetForm()" icon="el-icon-refresh-left">重置</el-button>
      </el-form-item>
    </el-form>
    <div class="f-s-c"
         style="padding: 15px 0;display: flex;justify-content:space-between;align-items: center;float:right ">
      <el-button icon="el-icon-plus" type="primary" @click="addOrUpdateHandle()">新增</el-button>
      <el-button icon="el-icon-download" type="success" v-loading.fullscreen.lock="fullscreenLoading" @click="exportGoods()">导出</el-button>
      <el-button icon="el-icon-refresh" type="info" @click="getDataList()">刷新</el-button>
    </div>
    <el-table :data="dataList" border v-loading="dataListLoading"
              style="width: 100%;">
      <el-table-column prop="goodsName" header-align="center" align="center" min-width="200" label="商品名称">
      </el-table-column>
      <el-table-column prop="scoreGoodsTypeText" header-align="center" align="center" min-width="200" label="商品类型">
      </el-table-column>
      <el-table-column prop="goodsCount" header-align="center" align="center" min-width="100" label="商品数量">
      </el-table-column>
      <el-table-column prop="orgName" header-align="center" align="center" min-width="200" label="所属组织机构">
      </el-table-column>
<!--      <el-table-column prop="exchangeAddress" header-align="center" align="center" min-width="200"-->
<!--                       :show-overflow-tooltip="true"-->
<!--                       label="兑换地址">-->
<!--      </el-table-column>-->
      <el-table-column prop="offlineScore" header-align="center" align="center" min-width="100" label="线下兑换积分">
      </el-table-column>
      <el-table-column prop="requiredScore" header-align="center" align="center" min-width="100" label="快递寄送积分">
      </el-table-column>
<!--      <el-table-column prop="linkPersonName" header-align="center" align="center" min-width="100" label="负责人名称">-->
<!--      </el-table-column>-->
<!--      <el-table-column prop="linkPhone" header-align="center" align="center" label="负责人联系电话" min-width="150">-->
<!--      </el-table-column>-->
      <el-table-column prop="status" header-align="center" align="center" min-width="100" label="状态">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.status == false" size="small" type="danger">下架</el-tag>
          <el-tag v-else size="small">上架</el-tag>
        </template>
      </el-table-column>
      <el-table-column fixed="right" header-align="center" align="center" label="操作" min-width="250">
        <template slot-scope="scope">
          <el-button type="text" size="small" @click="addOrUpdateHandle(scope.row.id)">修改</el-button>
          <el-button type="text" v-if="scope.row.status != false" size="small"
                     @click="updateStatus(scope.row.id,scope.row.status)">下架
          </el-button>
          <el-button type="text" v-else size="small" @click="updateStatus(scope.row.id,scope.row.status)">上架
          </el-button>
          <el-button type="text" size="small" @click="goodsStorageIn(scope.row.id)">出入库</el-button>
          <el-button type="text" size="small" @click="auditRecord(scope.row.id)">出入库记录</el-button>
          <el-button type="text" size="small" @click="deleteHandle(scope.row.id)">删除</el-button>
          <el-button type="text" size="small" @click="showQrCode(scope.row)" v-if="scope.row.scoreGoodsType === 'mall_score_goods_type_pad' && scope.row.status">二维码</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination @size-change="sizeChangeHandle" @current-change="currentChangeHandle" :current-page="pageIndex"
                   :page-sizes="[10, 20, 50, 100]" :page-size="pageSize" :total="totalPage"
                   layout="total, sizes, prev, pager, next, jumper">
    </el-pagination>
    <!-- 弹窗, 新增 / 修改 -->
    <change-num v-if="changeNumVisable" ref="changeNum" @refreshDataList="getDataList"></change-num>
    <audit-list v-if="numChangeVisible" ref="auditList"></audit-list>
    <change-status v-if="changeStatusVisible" ref="changeStatus" @refreshDataList="getDataList"></change-status>
    <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"></add-or-update>
    <el-dialog
        :title="'二维码展示'"
        :close-on-click-modal="false"
        min-width="40%"
        v-loading="qrCodeLoading"
        :visible.sync="qrCodeVisible">
      <div id="qrCodeDiv" ref="qrCodeDiv" style="display: flex; flex-direction: column; align-items: center;height: 80%;padding-bottom: 30px">
        <img :src="qrCodeUrl" style="width: 40%;margin: 50px 50px 20px 50px" crossOrigin="anonymous"/>
        <span style="font-size: 20px;font-weight: bold;white-space: pre-line">{{ qrCodeText }}</span>
      </div>
      <span slot="footer" class="dialog-footer">
          <el-button @click="qrCodeVisible = false">关闭</el-button>
          <el-button type="primary" @click="downloadQrCode()">下载二维码</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import AddOrUpdate from './mall-score-goods-add-or-update'
import AuditList from './mall-score-goods-num-change'
import ChangeNum from './mall-score-goods-change-num'
import ChangeStatus from './mall-score-goods-change-status'
import moment from "moment";
import html2canvas from "html2canvas";

export default {
  data() {
    return {
      dataForm: {
        keyWord: '',
        orgCode: '',
        status: null,
        scoreGoodsType: '',
        maxRequiredScore: '',
        minRequiredScore: '',
        star:null
      },
      dataList: [],
      pageIndex: 1,
      pageSize: 10,
      totalPage: 0,
      dataListLoading: false,
      dataListSelections: [],
      orgList: [],
      isSubAssociationAdmin: true,
      fullscreenLoading: false,
      numChangeVisible: false,
      addOrUpdateVisible: false,
      changeNumVisable: false,
      changeStatusVisible: false,
      qrCodeVisible: false,
      qrCodeLoading: false,
      qrCodeUrl: '',
      qrCodeText: ''
    }
  },
  components: {
    AddOrUpdate,
    ChangeNum,
    ChangeStatus,
    AuditList
  },
  activated() {
    if (this.$store.state.user.managerCapacity === 'SUB_ASSOCIATION_ADMIN') {
      this.isSubAssociationAdmin = false
    }
    this.queryPage()
    this.getOrg()
  },
  methods: {
    queryPage() {
      this.pageIndex = 1
      this.getDataList()
    },
    // 获取数据列表
    getDataList() {
      this.dataListLoading = true
      this.$http({
        url: this.$http.adornUrl('/admin/mall/score-goods/getPages'),
        method: 'post',
        data: this.$http.adornData({
          'currentPage': this.pageIndex,
          'pageSize': this.pageSize,
          'orgCode': this.dataForm.orgCode,
          'keyWord': this.dataForm.keyWord,
          'status': this.dataForm.status,
          'scoreGoodsType': this.dataForm.scoreGoodsType,
          'maxRequiredScore': this.dataForm.maxRequiredScore,
          'minRequiredScore': this.dataForm.minRequiredScore,
          'star': this.dataForm.star
        })
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.dataList = data.obj.records
          this.totalPage = data.obj.total
        } else {
          this.dataList = []
          this.totalPage = 0
        }
        this.dataListLoading = false
      })
    },
    auditRecord(id) {
      this.numChangeVisible = true
      this.$nextTick(() => {
        this.$refs.auditList.init(id)
      })
    },
    goodsStorageIn(id) {
      this.changeNumVisable = true
      this.$nextTick(() => {
        this.$refs.changeNum.init(id)
      })
    },
    // 每页数
    sizeChangeHandle(val) {
      this.pageSize = val
      this.pageIndex = 1
      this.getDataList()
    },
    //获取所属区域
    getOrg() {
      this.$http({
        url: this.$http.adornUrl(`/admin/org/getFiveAreaSubAssociationOrg`),
        method: 'get',
        params: this.$http.adornParams({withTop: true})
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.orgList = data.obj
        } else {
          this.orgList = []
        }
      })
    },
    // 下载模板文件
    exportGoods() {
      this.fullscreenLoading = true;
      this.$http({
        url: this.$http.adornUrl('/admin/mall/score-goods/exportGoods'),
        method: 'post',
        responseType: 'arraybuffer',
        data: this.$http.adornData({
          'orgCode': this.dataForm.orgCode,
          'keyWord': this.dataForm.keyWord,
          'maxRequiredScore': this.dataForm.maxRequiredScore,
          'status': this.dataForm.status,
          'scoreGoodsType': this.dataForm.scoreGoodsType,
          'minRequiredScore': this.dataForm.minRequiredScore
        })
      }).then(({
                 data
               }) => {
        if (data.code && data.code !== 0) {
          this.$message.error('导出失败')
        } else {
          this.fullscreenLoading = false

          let blob = new Blob([data], {
            type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8'
          })
          let objectUrl = URL.createObjectURL(blob)
          let a = document.createElement('a')
          // window.location.href = objectUrl
          a.href = objectUrl
          a.download = '商品数据.xlsx'
          a.click()
          URL.revokeObjectURL(objectUrl)
          this.$message({
            type: 'success',
            message: '导出成功'
          })
        }
      })
    },
    // 当前页
    currentChangeHandle(val) {
      this.pageIndex = val
      this.getDataList()
    },
    // 多选
    selectionChangeHandle(val) {
      this.dataListSelections = val
    },
    // 新增 / 修改
    addOrUpdateHandle(id) {
      this.addOrUpdateVisible = true
      this.$nextTick(() => {
        this.$refs.addOrUpdate.init(id)
      })
    },
    // 删除
    deleteHandle(id) {
      var ids = id ? [id] : this.dataListSelections.map(item => {
        return item.id
      })
      this.$confirm(`确定要进行删除吗，请谨慎操作，确定商品状态否则可能会带来不可预知的后果；确定进行[${id ? '删除' : '批量删除'}]操作?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        closeOnClickModal: false
      }).then(() => {
        this.$http({
          url: this.$http.adornUrl('/admin/mall/score-goods/removeByIds'),
          method: 'get',
          params: this.$http.adornParams({
            'ids': ids.join(',')
          })
        }).then(({
                   data
                 }) => {
          if (data && data.code === 0) {
            this.$message({
              message: '操作成功',
              type: 'success',
              duration: 1500,
              onClose: () => {
                this.getDataList()
              }
            })
          } else {
            this.$message.error(data.msg)
          }
        })
      })
    },
    //处理上架下架状态
    updateStatus(id, status) {
      console.log(status)
      this.changeStatusVisible = true
      this.$nextTick(() => {
        this.$refs.changeStatus.init(id, status)
      })
    },
    // *处理点位分类最后children数组为空的状态
    getTreeData: function (data) {
      let that = this
      // 循环遍历json数据
      data.forEach(function (e) {
        if (!e.children || e.children.length < 1) {
          e.children = undefined
        } else {
          // children若不为空数组，则继续 递归调用 本方法
          that.getTreeData(e.children)
        }
      })
      return data
    },
    resetForm() {
      this.$refs['dataForm']?.resetFields()
      this.getDataList()
    },
    // 展示二维码
    showQrCode(goods) {
      console.log(process.env.VUE_APP_NODE_ENV)
      let goodsId = goods.id
      this.qrCodeVisible = true
      // 调用接口生成
      if (!goods.stationGoodsQrcodeUrl || goods.stationGoodsQrcodeUrl === '') {
        this.qrCodeLoading = true
        this.$http({
          url: this.$http.adornUrl('/admin/mall/score-goods/createQrCode'),
          method: 'get',
          params: this.$http.adornParams({
            'goodsId': goodsId,
            'appId': window.SITE_CONFIG['wechatAppID'],
            'prod': process.env.VUE_APP_NODE_ENV === 'prod'
          })
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.qrCodeUrl = this.$http.adornAttachmentUrl(data.obj)
            this.qrCodeText = `商品名称：${goods.goodsName}`
            this.qrCodeLoading = false
          } else {
            this.$message.error(data.msg)
            this.qrCodeText = `商品名称：${goods.goodsName}`
            this.qrCodeLoading = false
          }
        }).catch(() => {
          this.qrCodeLoading = false
        })
      } else {
        this.qrCodeUrl = this.$http.adornAttachmentUrl(goods.stationGoodsQrcodeUrl)
        this.qrCodeText = `商品名称：${goods.goodsName}`
      }
    },
    // 下载二维码
    downloadQrCode() {
      html2canvas(document.querySelector('#qrCodeDiv'), {useCORS: true}).then(canvas => {
        let dataUrl = canvas.toDataURL("image/png")
        console.info(dataUrl)
        if (dataUrl) {
          let a = document.createElement('a')
          a.download = '二维码.png'
          a.href = dataUrl
          a.click()
        }
      })
    }
  }
}
</script>
