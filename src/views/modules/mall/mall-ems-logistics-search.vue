<template>
  <div>
    <el-dialog title="物流查询" :close-on-click-modal="false" width="75%" :visible.sync="visible">
      <el-form :model="dataForm" ref="dataForm" label-width="120px">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item label="物流运单号：">
              <span>{{dataForm.emsCode}}</span>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="运单状态：">
              <el-tag :type="dataForm.type" size="medium">
                {{ dataForm.status }}
              </el-tag>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="最新更新时间：">
              <span class="time-info">{{ dataForm.lastUpdateTime }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="">
              <el-button style="float: right" type="warning" class="el-icon-refresh" @click="refresh">刷新</el-button>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="物流信息如下：">
            </el-form-item>
              <div class="logistics-container" v-loading="logisticsInfoRefreshLoading">
                <div class="logistics-content">
                  <el-timeline>
                    <el-timeline-item
                        v-for="(item, index) in dataForm.logisticsInfo"
                        :key="index"
                        :timestamp="formatTime(item.opTime)"
                        placement="top"
                        :color="getStatusColor(item.opCode)"
                        :icon="getStatusIcon(item.opCode)">
<!--                        :hide-timestamp="index > 0 && isSameDay(item.opTime,  dataForm.logisticsInfo[index-1].opTime)"-->
<!--                    >-->
                      <el-card shadow="hover" class="logistics-card" :class="getCardClass(item)">
                        <div class="logistics-info">
                          <div class="logistics-title">
                            <span class="op-name">{{ getOperationName(item.opCode, item.opName) }}</span>
                            <el-tag :type="getTagType(item.opCode)" size="mini" class="status-tag">
                              {{ getStatusText(item.opCode) }}
                            </el-tag>
                          </div>
                          <div class="logistics-desc">
                            <p v-html="formatOpDesc(item.opDesc)"></p>
                            <p v-if="hasDelayInfo(item)" class="delay-info">
                              <i class="el-icon-warning"></i>
                              {{ extractDelayInfo(item.opDesc) }}
                            </p>
                          </div>
                          <div class="logistics-detail">
                            <span class="location">
                              <i class="el-icon-location-outline"></i>
                              {{ item.opOrgName }} ({{ item.opOrgProvName }}{{ item.opOrgCity }})
                            </span>
                            <span class="operator">
                              <i class="el-icon-user"></i>
                              操作员: {{ item.operatorName }} ({{ item.operatorNo }})
                            </span>
                          </div>
                          <div v-if="isCustomsRelated(item.opCode)" class="customs-info">
                            <i class="el-icon-s-flag"></i> 海关相关操作
                          </div>
                        </div>
                      </el-card>
                    </el-timeline-item>
                  </el-timeline>
                </div>
              </div>
<!--            </el-form-item>-->
          </el-col>
        </el-row>
      </el-form>
      <span slot="footer" class="dialog-footer">
				<el-button @click="visible = false">关闭</el-button>
			</span>
    </el-dialog>
  </div>
</template>

<script>
// 操作码分类配置
const OPERATION_CATEGORIES = {
  RECEIVING: ['203', '208', 'O_001'], // 收寄
  TRANSPORT: ['305', '306', '389', 'V_398', 'O_003', 'O_004', 'O_005', 'O_007', 'O_032', 'O_034', 'O_035', 'O_036'], // 运输
  PROCESSING: ['954', 'V_954', 'O_008', 'O_009', '356', '358', '360', '364', '365', '368', '370', '371', '374', '375', '376', '379', '394', '396', '399'], // 处理中心
  DELIVERY: ['702', '704', '705', '713', 'O_010', 'O_011', 'O_012', 'O_013', 'O_014', 'O_016', 'O_017', '747', '748'], // 投递
  CUSTOMS: ['456', 'C_001', 'C_002', 'C_003', 'C_004', 'C_005', 'C_006', 'C_007', 'C_008', 'C_009', 'C_010', 'C_011', 'C_012', 'C_032', 'C_033',
    'O_038', 'O_039', 'O_040', 'O_041', 'O_042', 'O_043', 'O_044', 'O_045', 'O_046', 'O_048'], // 海关
  INTERNATIONAL: ['423', '457', '458', '459', '460', '461', '462', '463', '465', '466', '467', '468', '469', '470', '481', '485', '486', '487', '488', '489',
    '490', '491', '492', '493', '494', '495', '496', '498', '542', '562', '563', '564', '565'], // 国际
  ABNORMAL: ['651', '704', '705', '711', '741', '987', '988', 'O_006', 'O_012', 'O_013', 'O_020', 'O_045', 'O_048'] // 异常
};
export default {
  data() {
    return {
      visible: false,
      lastUpdateTime: null,
      dataForm: {
        emsCode: null,
        type: null,
        status: null,
        logisticsInfo: null,
        lastUpdateTime: null
      },
      logisticsInfoRefreshLoading: false
    }
  },
  methods: {
    async init(emsCode) {
      this.dataForm.emsCode = emsCode
      this.visible = true
      this.$nextTick(() => {
        this.logisticsSearch()
      })
    },
    refresh() {
      this.logisticsInfoRefreshLoading = true
      this.dataForm.type = null
      this.dataForm.status = null
      this.dataForm.lastUpdateTime = null
      this.dataForm.logisticsInfo = null
      this.logisticsSearch()
    },
    logisticsSearch() {
      this.$http({
        url: this.$http.adornUrl('/admin/mall/exchange/logisticsSearch'),
        method: 'get',
        params: this.$http.adornParams({
          'emsCode': this.dataForm.emsCode
        })
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.dataForm.logisticsInfo = data.obj && data.obj.length > 0 ? data.obj.filter(item => {
            const opCodeStr = item.opCode.toString();
            return !opCodeStr.endsWith('D') && !item.opName.includes('删除');
          }) : [];
          this.dataForm.lastUpdateTime = data.obj && data.obj.length > 0 ? this.formatTime(data.obj[data.obj.length - 1].opTime) : ''
          this.dataForm.type = this.getSummaryTagType()
          this.dataForm.status = this.getSummaryStatus()
        } else {
          this.$message.error(data.msg)
        }
        this.logisticsInfoRefreshLoading = false
      })
    },
    formatTime(timeStr) {
      if (!timeStr) return '';
      const date = new Date(timeStr);
      return `${date.getFullYear()}-${this.padZero(date.getMonth() + 1)}-${this.padZero(date.getDate())} ${this.padZero(date.getHours())}:${this.padZero(date.getMinutes())}`;
    },
    padZero(num) {
      return num < 10 ? `0${num}` : num;
    },
    isSameDay(timeStr1, timeStr2) {
      if (!timeStr1 || !timeStr2) return false;
      return timeStr1.split('T')[0] === timeStr2.split('T')[0];
    },
    getOperationName(opCode, defaultName) {
      // 这里可以根据opCode返回更精确的操作名称
      // 实际项目中可以从操作码表中获取更准确的名称
      return defaultName;
    },
    getStatusColor(opCode) {
      const code = opCode.toString();
      // 306使用运输中的颜色
      if (code === '306') return '#409EFF';
      // 妥投/成功状态
      if (['704', '463', 'O_016', '748'].includes(code)) return '#67C23A';
      // 未妥投/失败状态
      if (['705', '711', '741', 'O_012', 'O_013', '542'].includes(code)) return '#F56C6C';
      // 海关状态
      if (this.isCustomsRelated(code)) return '#7232dd';
      // 国际运输状态
      if (this.isInternational(code)) return '#409EFF';
      // 收寄状态
      if (['203', '208', 'O_001'].includes(code)) return '#11a1a8';
      // 异常状态
      if (['651', '987', '988'].includes(code)) return '#e6a23c';
      return '#8a4848'; // 默认灰色
    },
    getStatusIcon(opCode) {
      const code = opCode.toString();
      // 306使用到达图标
      if (code === '306') return 'el-icon-location';
      if (['704', '463', 'O_016', '748'].includes(code)) return 'el-icon-success';
      if (['705', '711', '741', 'O_012', 'O_013', '542'].includes(code)) return 'el-icon-error';
      if (this.isCustomsRelated(code)) return 'el-icon-s-flag';
      if (this.isInternational(code)) return 'el-icon-s-promotion';
      if (['203', '208', 'O_001'].includes(code)) return 'el-icon-upload';
      if (['954', 'V_954', 'O_008', 'O_009'].includes(code)) return 'el-icon-office-building';
      return 'el-icon-refresh';
    },
    getTagType(opCode) {
      const code = opCode.toString();
      if (['704', '463', 'O_016', '748'].includes(code)) return 'success';
      if (['705', '711', '741', 'O_012', 'O_013', '542'].includes(code)) return 'danger';
      if (this.isCustomsRelated(code)) return '';
      if (this.isInternational(code)) return 'primary';
      if (['203', '208', 'O_001'].includes(code)) return 'info';
      return 'warning';
    },
    getStatusText(opCode) {
      const code = opCode.toString();
      // 新增306状态判断
      if (code === '306') return '到达目的地营业部';
      // 投递状态
      if (code === '704') return '已签收';
      if (code === '705') return '未妥投';
      if (code === '702') return '派送中';
      if (code === '713') return '已到达投递点';
      if (code === '747') return '快递柜已接收';
      if (code === '748') return '已取件';
      // 国际状态
      if (code === '463') return '境外妥投';
      if (code === '462') return '境外试投';
      if (code === '542') return '境外试投失败';
      if (code === '456' || code === '468') return '已完成清关';
      if (code === '457') return '航空公司启运';
      if (code === '458') return '到达境外航站';
      if (code === '459') return '到达境外互换局';
      if (code === '460') return '离开境外互换局';
      if (code === '461') return '到达境外投递局';
      // 海关状态
      if (code === 'C_002' || code === 'O_038') return '待清关';
      if (code === 'C_003' || code === 'C_032' || code === 'O_043') return '已通关';
      if (code === 'C_005' || code === 'O_039') return '待征税';
      if (code === 'C_007' || code === 'O_040') return '申报未通过';
      if (code === 'C_008' || code === 'O_044') return '已放行';
      if (code === 'C_011' || code === 'O_041') return '海关查扣';
      if (code === 'C_012' || code === 'O_042') return '清关延误';
      // 收寄状态
      if (code === '203') return '已收寄';
      if (code === '208') return '客户交接';
      // 处理中心
      if (code === '954' || code === 'V_954') return '到达处理中心';
      if (code === '389' || code === 'V_398') return '离开处理中心';
      // 运输状态
      if (code === '305') return '运输中';
      if (code === 'O_003') return '送往机场';
      if (code === 'O_004') return '已离港';
      if (code === 'O_005' || code === 'O_007' || code === 'O_036') return '转运中';
      // 异常状态
      if (code === '651') return '物流异常';
      if (code === '987' || code === '988') return '已赔付';
      return '处理中';
    },
    formatOpDesc(desc) {
      // 高亮重要信息
      if (!desc) return '';
      desc = desc.replaceAll(',', '&nbsp;&nbsp;&nbsp;&nbsp;')
      return desc
          .replace(/【(.*?)】/g, '<strong>【$1】</strong>')
          .replace(/(揽投员|投递员|电话):(.*?)(,|$)/g, '<span class="highlight">$1:$2</span>$3');
    },
    hasDelayInfo(item) {
      return item.opDesc && item.opDesc.includes('预计加时');
    },
    extractDelayInfo(desc) {
      const match = desc.match(/预计加时(\d+)天/);
      return match ? `预计延迟${match[1]}天送达` : '预计延迟送达';
    },
    isCustomsRelated(opCode) {
      const code = opCode.toString();
      return OPERATION_CATEGORIES.CUSTOMS.some(c => c === code);
    },
    isInternational(opCode) {
      const code = opCode.toString();
      return OPERATION_CATEGORIES.INTERNATIONAL.some(c => c === code);
    },
    getCardClass(item) {
      const code = item.opCode.toString();
      if (this.isCustomsRelated(code)) return 'customs-card';
      if (this.isInternational(code)) return 'international-card';
      if (['704', '463', 'O_016', '748'].includes(code)) return 'success-card';
      if (['705', '711', '741', 'O_012', 'O_013', '542'].includes(code)) return 'error-card';
      return '';
    },
    getSummaryStatus() {
      if (this.dataForm.logisticsInfo.length === 0) return '无物流信息';
      const lastItem = this.dataForm.logisticsInfo[this.dataForm.logisticsInfo.length - 1];
      const lastCode = lastItem.opCode.toString();
      if (['704', '463', 'O_016', '748'].includes(lastCode)) return '已签收';
      if (['705', '711', '741', 'O_012', 'O_013', '542'].includes(lastCode)) return '投递异常';
      if (this.isCustomsRelated(lastCode)) return '海关处理中';
      if (this.isInternational(lastCode)) return '国际运输中';
      if (['702', '713', '747'].includes(lastCode)) return '派送中';
      if (['203', '208', 'O_001'].includes(lastCode)) return '已收寄';
      return '运输中';
    },
    getSummaryTagType() {
      if (this.dataForm.logisticsInfo.length === 0) return 'info';
      const lastItem = this.dataForm.logisticsInfo[this.dataForm.logisticsInfo.length - 1];
      const lastCode = lastItem.opCode.toString();
      if (['704', '463', 'O_016', '748'].includes(lastCode)) return 'success';
      if (['705', '711', '741', 'O_012', 'O_013', '542'].includes(lastCode)) return 'danger';
      if (this.isCustomsRelated(lastCode)) return '';
      if (this.isInternational(lastCode)) return 'primary';
      return 'info';
    }
  }
}
</script>

<style lang="scss" scoped>
.logistics-container {
  border: 1px solid #ebeef5;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  overflow: hidden;
  background-color: #fff;
}

.time-info {
  font-size: 13px;
  color: #666;
}

.logistics-content {
  height: 550px;
  overflow-y: auto;
  padding: 20px;
}

.logistics-card {
  margin-bottom: 10px;
  border-radius: 4px;
  border-left: 4px solid #ebeef5;
}

.logistics-card.success-card {
  border-left-color: #67C23A;
}

.logistics-card.error-card {
  border-left-color: #F56C6C;
}

.logistics-card.customs-card {
  border-left-color: #7232dd;
}

.logistics-card.international-card {
  border-left-color: #409EFF;
}

.logistics-info {
  padding: 10px;
}

.logistics-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.op-name {
  font-weight: bold;
  font-size: 15px;
  color: #333;
}

.status-tag {
  margin-left: 10px;
}

.logistics-desc {
  margin-bottom: 8px;
  color: #666;
  font-size: 14px;
  line-height: 1.5;
}

.logistics-desc ::v-deep strong {
  color: #333;
}

.logistics-desc ::v-deep .highlight {
  color: #409EFF;
  font-weight: bold;
}

.delay-info {
  color: #e6a23c;
  margin-top: 5px;
  font-size: 13px;
  display: flex;
  align-items: center;
}

.delay-info i {
  margin-right: 5px;
}

.logistics-detail {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: #999;
  margin-top: 8px;
  flex-wrap: wrap;
  gap: 10px;
}

.location,
.operator {
  display: flex;
  align-items: center;
}

.el-icon-location-outline,
.el-icon-user {
  margin-right: 4px;
}

.customs-info {
  margin-top: 8px;
  padding-top: 8px;
  border-top: 1px dashed #eee;
  font-size: 12px;
  color: #7232dd;
  display: flex;
  align-items: center;
}

.customs-info i {
  margin-right: 4px;
}

/* 滚动条样式 */
.logistics-content::-webkit-scrollbar {
  width: 6px;
}

.logistics-content::-webkit-scrollbar-thumb {
  background-color: #c1c1c1;
  border-radius: 3px;
}

.logistics-content::-webkit-scrollbar-track {
  background-color: #f1f1f1;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .logistics-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .logistics-detail {
    flex-direction: column;
  }
}
</style>
