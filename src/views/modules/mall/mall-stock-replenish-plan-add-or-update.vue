<template>
  <div>
    <el-dialog
        :title="enableEdit === false ? '年度补货计划详情' : (!dataForm.id ? '年度补货计划新增' : '年度补货计划修改')"
        :close-on-click-modal="false"
        top="8vh"
        :visible.sync="visible" width="90%">
      <el-form :model="dataForm" :rules="enableEdit === true ? dataRule : null" ref="dataForm" label-width="100px" label-position="left" style="margin: 0px 20px 0px 20px">
        <el-row :gutter="20" style="margin-bottom: 20px">
          <el-col :span="24">
            <span style="font-weight: bold; font-size: large">年度补货明细</span>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="预算年度：" prop="planYear">
              <el-date-picker
                  v-if="enableEdit === true"
                  v-model="dataForm.planYear"
                  type="year"
                  clearable
                  value-format="yyyy"
                  placeholder="请选择">
              </el-date-picker>
              <span v-if="enableEdit === false">{{!dataForm.planYear ? '' : dataForm.planYear + ' 年度'}}</span>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="预算总额：" prop="budgetAmount">
              <el-input-number v-if="enableEdit === true" v-model="dataForm.budgetAmount" :min="0" controls-position="right"></el-input-number>
              {{enableEdit === true ? '（元）' : ''}}
              <span v-if="enableEdit === false">{{dataForm.budgetAmount >= 10000 ? dataForm.budgetAmount/10000 + ' 万元' : dataForm.budgetAmount + ' 元'}}</span>
            </el-form-item>
          </el-col>
          <el-col :span="8" v-if="enableEdit === true">
            <el-button style="float: right" id="plan_generate" type="primary" plain @click="generatePlan">计划生成</el-button>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-form :model="detailForm" ref="detailForm" :rules="detailForm.detailRules">
            <el-table
                id="detail_table"
                ref="detailTable"
                :key="detailTableKey"
                :data="detailForm.detailList"
                v-loading="detailListLoading"
                :height="enableEdit === true ? '580px' : null"
                max-height="580px"
                show-summary
                :summary-method="detailSummary"
                :lazy="true"
                style="width: 100%;"
                :row-class-name="specialRowDeal">
              <el-table-column
                  type="index"
                  header-align="center"
                  label="序号"
                  align="center"
                  width="50">
              </el-table-column>
              <el-table-column
                  prop="stockName"
                  header-align="center"
                  align="center"
                  width="auto"
                  min-width="20%"
                  label="物料名称">
                <template slot-scope="scope">
                  <el-form-item :prop="`detailList.${scope.$index}.stockId`"
                                :rules='detailForm.detailRules.stockId' style="margin-bottom: 0"
                                v-if="enableEdit === true && (!scope.row.stockDeleted || scope.row.stockDeleted === false)">
                    <el-select :style="enableStockNew === true ? {width: '80%'} : {width: '90%'}" v-model="scope.row.stockId" clearable v-if="enableEdit === true" @change="(value) => changeStock(value, scope.row)" @clear="clearStock(scope.row)" filterable>
                      <el-option
                          v-for="item in stockList"
                          :disabled="detailForm.detailList.map(detail => {return detail.stockId}).indexOf(item.id) >= 0"
                          :key="item.id"
                          :label="item.name"
                          :value="item.id">
                      </el-option>
                    </el-select>
                    <el-button v-if="enableStockNew === true"  style="width: 20px;float: right;border: 0px;padding: 0px;font-size: 20px;margin-top: 8px;" class="el-icon-circle-plus-outline stock_new" title="新增物料" @click="newStockHandle(scope.$index)"></el-button>
                  </el-form-item>
                  <span v-if="enableEdit === false || (scope.row.stockDeleted && scope.row.stockDeleted === true)">{{scope.row.stockName}}</span>
                </template>
              </el-table-column>
              <el-table-column
                  prop="stockSku"
                  header-align="center"
                  align="center"
                  width="auto"
                  min-width="12%"
                  label="物料编号">
              </el-table-column>
              <el-table-column
                  prop="unitPrice"
                  header-align="center"
                  align="center"
                  width="auto"
                  min-width="8%"
                  label="单价(元)">
              </el-table-column>
              <el-table-column
                  label="库存"
                  header-align="center"
                  align="center">
                <el-table-column
                    prop="inventoryQuantity"
                    header-align="center"
                    align="center"
                    width="auto"
                    min-width="10%"
                    label="数量(库中/总量)">
                  <template slot-scope="scope">
                    <span>{{(scope.row.inventoryQuantity ? scope.row.inventoryQuantity : '' ) + ' / ' + (scope.row.inventoryTotalQuantity ? scope.row.inventoryTotalQuantity : '')}}</span>
                  </template>
                </el-table-column>
                <el-table-column
                    prop="inventoryAmount"
                    header-align="center"
                    align="center"
                    width="auto"
                    min-width="16%"
                    label="金额(元)">
                  <template slot-scope="scope">
                    <span>{{(scope.row.inventoryAmount ? scope.row.inventoryAmount : '' ) + ' / ' + (scope.row.inventoryTotalAmount ? scope.row.inventoryTotalAmount : '')}}</span>
                  </template>
                </el-table-column>
              </el-table-column>
              <el-table-column
                  label="补货计划"
                  header-align="center"
                  width="auto"
                  align="center">
                <el-table-column
                    prop="replenishQuantity"
                    header-align="center"
                    align="center"
                    width="auto"
                    min-width="12%"
                    label="数量">
                  <template slot-scope="scope">
                    <el-form-item style="margin-bottom: 0"
                                  :prop="`detailList.${scope.$index}.replenishQuantity`"
                                  :rules='detailForm.detailRules.replenishQuantity'
                                  v-if="enableEdit === true && scope.row.stockId && scope.row.stockId !== ''">
                      <el-input-number style="width: 100%" v-model="scope.row.replenishQuantity" controls-position="right" :min="0" @change="(value) => changeReplenishQuantity(value, scope.row)"/>
                    </el-form-item>
                    <span v-if="enableEdit === false">{{scope.row.replenishQuantity}}</span>
                  </template>
                </el-table-column>
                <el-table-column
                    prop="replenishAmount"
                    header-align="center"
                    align="center"
                    width="auto"
                    min-width="14%"
                    label="金额(元)">
                </el-table-column>
              </el-table-column>
              <el-table-column
                  v-if="enableEdit === true"
                  header-align="center"
                  align="center"
                  width="auto"
                  min-width="8%"
                  label="操作">
                <template slot-scope="scope">
                  <el-button class="el-icon-circle-plus" id="add_record" @click="addAfterCurr(scope.row, scope.$index)"></el-button>
                  <el-button :disabled="!detailForm.detailList || detailForm.detailList.length === 0 || (detailForm.detailList.length === 1 && (!detailForm.detailList[0].stockId || detailForm.detailList[0].stockId === ''))" class="el-icon-remove" id="remove_record" @click="removeCurr(scope.row, scope.$index)"></el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-form>
        </el-row>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button v-if="enableEdit === true" @click="visible = false">取消</el-button>
        <el-button v-if="enableEdit === false" @click="visible = false">关闭</el-button>
        <el-button v-if="enableEdit === true" type="primary" @click="dataFormSubmit('draft')" :disabled="dataFormLoading">保存草稿</el-button>
        <el-button v-if="enableEdit === true" type="success" @click="dataFormSubmit('submit')" :disabled="dataFormLoading">提交</el-button>
      </span>
    </el-dialog>
    <stock-new ref="newStock" @refreshDataList="newStockEmit" v-if="newStockVisible"></stock-new>
  </div>
</template>

<script>
import StockNew from './mall-stock-add-or-update'
export default {
  name: "mall-stock-replenish-plan-add-or-update",
  data() {
    return {
      enableEdit: true,
      enableStockNew: true,
      visible: false,
      dataFormLoading: false,
      dataForm: {
        id: null,
        planYear: null,
        budgetAmount: 0,
        actualAmount: 0,
        type: 'srpt_common'
      },
      dataRule: {
        planYear: [
          { required: true, message: '预算年度不能为空', trigger: 'change' }
        ],
        budgetAmount: [
          { required: true, message: '预算金额不能为空', trigger: 'blur' }
        ]
      },
      stockList: [],
      detailForm: {
        detailList: [],
        detailRules: {
          stockId: [
            // { required: true, message: '物料不能为空', trigger: 'change' }
          ],
          replenishQuantity: [
            { required: true, message: '补货计划数量不能为空', trigger: 'change' }
          ]
        }
      },
      detailListLoading: false,
      detailTableKey: null,
      currentNewStockRowIndex: null,
      newStockVisible: false
    }
  },
  components: {
    StockNew
  },
  updated() {
    this.$nextTick(()=>{
      this.$refs.detailTable.doLayout();
    })
  },
  methods: {
    init (id, enableEdit) {
      this.dataForm.id = id || null
      this.enableEdit = enableEdit
      this.dataFormLoading = false
      this.detailTableKey = (new Date()).valueOf()
      this.visible = true
      this.getStockList()
      this.detailForm.detailList = []
      this.$nextTick(() => {
        this.$refs['dataForm']?.resetFields()
        this.detailListLoading = true
        if (this.dataForm.id) {
          this.getReplenishPlanData()
        } else {
          this.dataForm.planYear = new Date().getFullYear() + ''
          if ((!this.detailForm.detailList || this.detailForm.detailList.length === 0) && this.enableEdit === true) {
            let detail = {
              stockId: null,
              stockName: null,
              stockSku: null,
              unitPrice: null,
              inventoryQuantity: null,
              inventoryTotalQuantity: null,
              inventoryAmount: null,
              inventoryTotalAmount: null,
              replenishQuantity: null,
              replenishAmount: null
            }
            this.detailForm.detailList.push(detail)
          }
          this.detailListLoading = false
        }
      })
    },
    addAfterCurr(row, index) {
      let detail = {
        stockId: null,
        stockName: null,
        stockSku: null,
        unitPrice: null,
        inventoryQuantity: null,
        inventoryTotalQuantity: null,
        inventoryAmount: null,
        inventoryTotalAmount: null,
        replenishQuantity: null,
        replenishAmount: null
      }
      this.detailForm.detailList.splice(index + 1, 0, detail)
    },
    removeCurr(row, index) {
      this.detailForm.detailList.splice(index, 1)
      if (!this.detailForm.detailList || this.detailForm.detailList.length === 0) {
        let detail = {
          stockId: null,
          stockName: null,
          stockSku: null,
          unitPrice: null,
          inventoryQuantity: null,
          inventoryTotalQuantity: null,
          inventoryAmount: null,
          inventoryTotalAmount: null,
          replenishQuantity: null,
          replenishAmount: null
        }
        this.detailForm.detailList.push(detail)
      }
    },
    getReplenishPlanData () {
      this.$http({
        url: this.$http.adornUrl('/admin/mall/stock_replenish_plan/getReplenishPlanById'),
        method: 'get',
        params: this.$http.adornParams({
          id: this.dataForm.id
        })
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.dataForm.planYear = data.obj.planYear
          this.dataForm.budgetAmount = data.obj.budgetAmount
          if ((!data.obj.detailList || data.obj.detailList.length === 0) && this.enableEdit === true) {
            let detail = {
              stockId: null,
              stockName: null,
              stockSku: null,
              unitPrice: null,
              inventoryQuantity: null,
              inventoryTotalQuantity: null,
              inventoryAmount: null,
              inventoryTotalAmount: null,
              replenishQuantity: null,
              replenishAmount: null
            }
            this.detailForm.detailList.push(detail)
            this.detailListLoading = false
          } else {
            var that = this
            _.forEach(data.obj.detailList, function (item) {
              that.$set(item, 'unitPrice', item.unitPrice ? item.unitPrice.toFixed(2) : null)
              that.$set(item, 'inventoryAmount', (item.inventoryQuantity * item.unitPrice).toFixed(2))
              that.$set(item, 'inventoryTotalAmount', (item.inventoryTotalQuantity * item.unitPrice).toFixed(2))
              that.$set(item, 'replenishAmount', (item.replenishQuantity * item.unitPrice).toFixed(2))
            })
            this.detailForm.detailList = data.obj.detailList
            this.detailListLoading = false
          }
        } else {
          this.$message.error(data.msg)
          this.detailListLoading = false
        }
      })
    },
    getStockList(needChange, id) {
      this.$http({
        url: this.$http.adornUrl('/admin/mall/stock/getStockByOrg'),
        method: 'get',
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.stockList = data.obj
          if (needChange && needChange === true) {
            this.$set(this.detailForm.detailList[this.currentNewStockRowIndex], 'stockId', id)
            this.changeStock(id, this.detailForm.detailList[this.currentNewStockRowIndex])
          }
        } else {
          this.$message.error(data.msg)
        }
      })
    },
    specialRowDeal (row, rowIndex) {
      if (row.row.stockDeleted && row.row.stockDeleted === true) {
        this.existRemoveStock = true
        return 'stock_remove'
      }
    },
    detailSummary (param) {
      const { columns, data } = param;
      const sums = [];
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = '合计';
          return;
        }
        if (column.property !== 'inventoryQuantity' && column.property !== 'inventoryAmount' && column.property !== 'replenishAmount') {
          sums[index] = '';
          return;
        }
        const values = data.map(item => Number(item[column.property]));
        const inventoryTotalQuantityValues = data.map(item => Number(item['inventoryTotalQuantity']));
        const inventoryTotalAmountValues = data.map(item => Number(item['inventoryTotalAmount']));
        if (!values.every(value => isNaN(value))) {
          sums[index] = values.reduce((prev, curr) => {
            const value = Number(curr);
            if (!isNaN(value)) {
              return prev + curr;
            } else {
              return prev;
            }
          }, 0);
          if (column.property === 'replenishAmount') {
            sums[index] = sums[index].toFixed(2)
            this.dataForm.actualAmount = sums[index]
            if (sums[index] > this.dataForm.budgetAmount) {
              const tds = document.querySelectorAll(
                  "#detail_table .el-table__footer-wrapper tr>td"
              );
              tds[5].childNodes[0].style.color = 'red'
              sums[index] += ((sums[index] + '').indexOf('超预算') >= 0 ? '' : '(超预算)')
            } else {
              const tds = document.querySelectorAll(
                  "#detail_table .el-table__footer-wrapper tr>td"
              );
              tds[5].childNodes[0].style.color = '#31e134'
            }
          }
          sums[index] += '';
        } else {
          sums[index] = '合计失败';
        }
        if (column.property === 'inventoryQuantity') {
          if (!inventoryTotalQuantityValues.every(value => isNaN(value))) {
            let inventoryTotalQuantitySum = inventoryTotalQuantityValues.reduce((prev, curr) => {
              const value = Number(curr);
              if (!isNaN(value)) {
                return prev + curr;
              } else {
                return prev;
              }
            }, 0);
            sums[index] += (' / ' + inventoryTotalQuantitySum);
          } else {
            sums[index] += ' / 合计失败';
          }
        }
        if (column.property === 'inventoryAmount') {
          if (!isNaN(Number(sums[index]))) {
            sums[index] = Number(sums[index]).toFixed(2)
          }
          if (!inventoryTotalAmountValues.every(value => isNaN(value))) {
            let inventoryTotalAmountSum = inventoryTotalAmountValues.reduce((prev, curr) => {
              const value = Number(curr);
              if (!isNaN(value)) {
                return prev + curr;
              } else {
                return prev;
              }
            }, 0);
            inventoryTotalAmountSum = inventoryTotalAmountSum.toFixed(2)
            sums[index] += (' / ' + inventoryTotalAmountSum );
          } else {
            sums[index] += ' / 合计失败';
          }
        }
      });
      this.$nextTick(() => {
        const tds = document.querySelectorAll(
            "#detail_table .el-table__footer-wrapper tr>td"
        );
        tds[0].colSpan = 4;
        tds[0].style.textAlign = "center";
        tds[0].childNodes[0].style.fontWeight = "bold";
        tds[0].style.backgroundColor = "#EBEEF5"
        tds[1].style.display = "none";
        tds[2].childNodes[0].style.fontWeight = "bold";
        tds[3].childNodes[0].style.fontWeight = "bold";
        tds[4].style.backgroundColor = "#EBEEF5"
        tds[5].childNodes[0].style.fontWeight = "bold";
        if (this.enableEdit === true) {
          tds[6].style.backgroundColor = "#EBEEF5"
        }
      });
      return [sums[0]].concat(sums.slice(3));
    },
    changeStock (value, row) {
      if (!value || value === '') {
        return;
      }
      let stock = this.stockList.filter(it => it.id === value)[0]
      this.$set(row, 'stockSku', stock.sku)
      this.$set(row, 'stockName', stock.name)
      this.$set(row, 'unitPrice', stock.unitPrice.toFixed(2))
      this.$set(row, 'inventoryQuantity', stock.inventoryQuantity)
      this.$set(row, 'inventoryTotalQuantity', stock.inventoryTotalQuantity)
      this.$set(row, 'inventoryAmount', (stock.inventoryQuantity * stock.unitPrice).toFixed(2))
      this.$set(row, 'inventoryTotalAmount', (stock.inventoryTotalQuantity * stock.unitPrice).toFixed(2))
      this.$set(row, 'replenishQuantity', 0)
      this.$set(row, 'replenishAmount', 0)
    },
    clearStock (row) {
      this.$set(row, 'stockSku', null)
      this.$set(row, 'stockName', null)
      this.$set(row, 'unitPrice', null)
      this.$set(row, 'inventoryQuantity', null)
      this.$set(row, 'inventoryTotalQuantity', null)
      this.$set(row, 'inventoryAmount', null)
      this.$set(row, 'inventoryTotalAmount', null)
      this.$set(row, 'replenishQuantity', null)
      this.$set(row, 'replenishAmount', null)
    },
    changeReplenishQuantity (value, row) {
      this.$set(row, 'replenishAmount', !value ? 0 : (value * row.unitPrice).toFixed(2))
    },
    newStockHandle(index) {
      this.currentNewStockRowIndex = index
      this.newStockVisible = true
      this.$nextTick(() => {
        this.$refs.newStock.init()
      })
    },
    newStockEmit (id) {
      this.getStockList(true, id)
    },
    generatePlan () {
      this.$confirm(`生成补货计划成功后将刷新您已维护的计划列表。是否确定生成？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        closeOnClickModal: false
      }).then(() => {
        this.detailListLoading = true
        this.$http({
          url: this.$http.adornUrl('/admin/mall/stock_replenish_plan/generatePlan'),
          method: 'get',
          params: this.$http.adornParams({
            type: this.dataForm.type,
            budgetAmount: this.dataForm.budgetAmount,
            year: this.dataForm.planYear
          })
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.dataForm.planYear = data.obj.planYear
            this.dataForm.budgetAmount = data.obj.budgetAmount
            if ((!data.obj.detailList || data.obj.detailList.length === 0) && this.enableEdit === true) {
              let detail = {
                stockId: null,
                stockName: null,
                stockSku: null,
                unitPrice: null,
                inventoryQuantity: null,
                inventoryTotalQuantity: null,
                inventoryAmount: null,
                inventoryTotalAmount: null,
                replenishQuantity: null,
                replenishAmount: null
              }
              data.obj.detailList.push(detail)
            } else {
              var that = this
              _.forEach(data.obj.detailList, function (item) {
                that.$set(item, 'unitPrice', item.unitPrice.toFixed(2))
                that.$set(item, 'inventoryAmount', (item.inventoryQuantity * item.unitPrice).toFixed(2))
                that.$set(item, 'inventoryTotalAmount', (item.inventoryTotalQuantity * item.unitPrice).toFixed(2))
                that.$set(item, 'replenishAmount', (item.replenishQuantity * item.unitPrice).toFixed(2))
              })
            }
            this.$set(this.detailForm, 'detailList', data.obj.detailList)
            this.detailListLoading = false
          } else {
            this.$message.error(data.msg)
            this.detailListLoading = false
          }
        })
      }).catch(() => {
      })
    },
    dataFormSubmit(type) {
      this.$set(this.dataForm, 'detailList', this.detailForm.detailList)
      const api = (type === 'draft' ? '/admin/mall/stock_replenish_plan/saveOrUpdateDraft' : '/admin/mall/stock_replenish_plan/submit')
      if (type === 'submit') {
        this.$confirm(`提交后将无法修改，确定进行提交操作?`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
          closeOnClickModal: false
        }).then(() => {
          this.submitFun(api)
        })
      } else {
        this.submitFun(api)
      }
    },
    submitFun (api) {
      this.$refs['detailForm'].validate((valid) => {
        if (valid) {
          this.$refs['dataForm'].validate((valid) => {
            if (valid) {
              this.dataFormLoading = true
              this.$http({
                url: this.$http.adornUrl(api),
                method: 'post',
                data: this.$http.adornData(this.dataForm)
              }).then(({data}) => {
                if (data && data.code === 0) {
                  this.$message({
                    message: '操作成功',
                    type: 'success',
                    duration: 1500,
                    onClose: () => {
                      this.dataFormLoading = false
                      this.visible = false
                      this.$emit('refreshDataList')
                    }
                  })
                } else {
                  this.$message.error(data.msg)
                }
                this.dataFormLoading = false
              })
            }
          })
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
#add_record {
  padding: 0;
  border: 0;
  font-size: 25px;
  color: #606266;
  background-color: rgba(0,0,0,0);
}
#add_record:hover{
  color: #00aaf7;
}
#remove_record {
  padding: 0;
  border: 0;
  font-size: 25px;
  color: #606266;
  background-color: rgba(0,0,0,0);
}
#remove_record:hover{
  color: #ed0004;
}
#detail_table ::v-deep .el-form-item__error {
  position: static;
  text-align: center;
}
#plan_generate {
  color: #409EFF;
  background: #ecf5ff;
  border-color: #b3d8ff;
}
#plan_generate:hover{
  color: #ecf5ff;
  background: #409EFF;
}
#detail_table ::v-deep .stock_remove {
  background-color: #fc6e6e;
}
.stock_new:hover{
  background-color: rgba(0,0,0,0);
}
</style>
