<template>
	<div>
		<el-dialog title="库存变动记录" :close-on-click-modal="false" :visible.sync="visible" width="80%">
			<el-table :data="dataList" border v-loading="dataListLoading" style="width: 100%;">
				<el-table-column prop="changeTypeText" header-align="center" align="center" label="变动方向">
				</el-table-column>
				<el-table-column prop="changeBeforeNum" header-align="center" align="center" label="变动前数量">
				</el-table-column>
				<el-table-column prop="changeNum" header-align="center" align="center" label="变动数量">
				</el-table-column>
				<el-table-column prop="changeAfterNum" header-align="center" align="center" label="余量">
				</el-table-column>
				<el-table-column prop="remark" header-align="center" align="center" width="250"
					:show-overflow-tooltip="true" label="变动原因">
				</el-table-column>
				<el-table-column prop="operateUserName" header-align="center" align="center" width="250"
					:show-overflow-tooltip="true" label="操作人">
				</el-table-column>
				<el-table-column prop="operateDate" header-align="center" align="center" width="250"
					:show-overflow-tooltip="true" label="操作时间">
				</el-table-column>
			</el-table>
			<el-pagination @size-change="sizeChangeHandle" @current-change="currentChangeHandle"
				:current-page="pageIndex" :page-sizes="[10, 20, 50, 100]" :page-size="pageSize" :total="totalPage"
				layout="total, sizes, prev, pager, next, jumper">
			</el-pagination>
		</el-dialog>
	</div>
</template>

<script>
	export default {
		data() {
			return {
				visible: false,
				dataList: [],
				dataListLoading: false,
				dataForm: {
					goodsId: null
				},
				pageIndex: 1, // 当前页
				pageSize: 10, // 分页大小
				totalPage: 0,
			}
		},
		methods: {
			init(id) {
				this.dataForm.goodsId = id || null
				this.visible = true
				this.getDataList()
			},
			getDataList() {
				if (!this.dataForm.goodsId) {
					this.$message.error('为获取到内容id')
					return
				}
				this.dataListLoading = true
				this.$http({
					url: this.$http.adornUrl('/admin/mall/stock/num/change/getPages'),
					method: 'post',
					data: this.$http.adornData({
						currentPage: this.pageIndex,
						pageSize: this.pageSize,
						...this.dataForm
					})
				}).then(({
					data
				}) => {
					if (data && data.code === 0) {
						this.dataList = data.obj.records
						this.totalPage = data.obj.total
					} else {
						this.$message.error('获取出入库记录失败')
					}
					this.dataListLoading = false
				})
			},
			// 分页, 每页条数
			sizeChangeHandle(val) {
				this.pageSize = val
				this.pageIndex = 1
				this.getDataList()
			},
			// 分页, 当前页
			currentChangeHandle(val) {
				this.pageIndex = val
				this.getDataList()
			}
		}
	}
</script>
