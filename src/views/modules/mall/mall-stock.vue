<template>
  <div class="mod-config">
    <el-form :inline="true" ref="dataForm" :model="dataForm" @keyup.enter.native="queryPage()">
      <el-form-item label="关键字" prop="keyWord">
        <el-input v-model="dataForm.keyWord" placeholder="物料名称/物料描述" clearable></el-input>
      </el-form-item>
      <el-form-item v-if='isSubAssociationAdmin' :label="'所属组织机构'" prop="orgCode">
        <el-select placeholder="所属组织机构" v-model="dataForm.orgCode" clearable>
          <el-option v-for="item in orgList"
                     :key="item.code" :label="item.name" :value="item.code"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-search" type="warning" @click="queryPage()">查询</el-button>
        <el-button @click="resetForm()" icon="el-icon-refresh-left">重置</el-button>
      </el-form-item>
    </el-form>
    <div class="f-s-c"
         style="padding: 15px 0;display: flex;justify-content:space-between;align-items: center;float:right ">
      <el-button icon="el-icon-plus" type="primary" @click="addOrUpdateHandle()">新增</el-button>
      <el-button icon="el-icon-refresh" type="info" @click="getDataList()">刷新</el-button>
    </div>
    <el-table :data="dataList" border v-loading="dataListLoading"
              style="width: 100%;">
      <el-table-column prop="name" header-align="center" align="center" width="300" label="物料名称">
      </el-table-column>
      <el-table-column prop="sku" header-align="center" align="center" width="200" label="物料编码">
      </el-table-column>
      <el-table-column prop="orgName" header-align="center" align="center" width="200" label="所属组织">
      </el-table-column>
      <el-table-column prop="unitPrice" header-align="center" align="center" width="80" label="物料单价">
      </el-table-column>
      <el-table-column prop="inventoryQuantity" header-align="center" align="center" width="120" label="物料剩余数量">
      </el-table-column>
      <el-table-column prop="inventoryTotalQuantity" header-align="center" align="center" width="140" label="物料剩余总数量">
      </el-table-column>
      <el-table-column prop="description" header-align="center" align="center"
                       :show-overflow-tooltip="true"
                       label="物料描述">
      </el-table-column>
      <el-table-column fixed="right" header-align="center" align="center" label="操作">
        <template slot-scope="scope">
          <el-button type="text" size="small" @click="addOrUpdateHandle(scope.row.id)">修改</el-button>
          <el-button type="text" size="small" @click="goodsStorageIn(scope.row.id)">出入库</el-button>
          <el-button type="text" size="small" @click="auditRecord(scope.row.id)">出入库记录</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination @size-change="sizeChangeHandle" @current-change="currentChangeHandle" :current-page="pageIndex"
                   :page-sizes="[10, 20, 50, 100]" :page-size="pageSize" :total="totalPage"
                   layout="total, sizes, prev, pager, next, jumper">
    </el-pagination>
    <!-- 弹窗, 新增 / 修改 -->
    <change-num v-if="changeNumVisable" ref="changeNum" @refreshDataList="getDataList"></change-num>
    <audit-list v-if="numChangeVisible" ref="auditList"></audit-list>
    <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"></add-or-update>
  </div>
</template>

<script>
import AddOrUpdate from './mall-stock-add-or-update'
import AuditList from './mall-stock-num-change'
import ChangeNum from './mall-stock-change-num'

export default {
  data() {
    return {
      isSubAssociationAdmin: false,
      dataForm: {
        keyWord: '',
        orgCode: ''
      },
      orgList: [],
      dataList: [],
      pageIndex: 1,
      pageSize: 10,
      totalPage: 0,
      dataListLoading: false,
      dataListSelections: [],
      fullscreenLoading: false,
      numChangeVisible: false,
      addOrUpdateVisible: false,
      changeNumVisable: false
    }
  },
  components: {
    AddOrUpdate,
    ChangeNum,
    AuditList
  },
  activated() {
    this.isSubAssociationAdmin = this.$store.state.user.managerCapacity === 'ASSOCIATION_ADMIN'
    this.queryPage()
    this.getOrg()
  },
  methods: {
    //获取所属区域
    getOrg() {
      this.$http({
        url: this.$http.adornUrl(`/admin/org/getFiveAreaSubAssociationOrg`),
        method: 'get',
        params: this.$http.adornParams({withTop: true})
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.orgList = data.obj
        } else {
          this.orgList = []
        }
      })
    },
    queryPage() {
      this.pageIndex = 1
      this.getDataList()
    },
    // 获取数据列表
    getDataList() {
      this.dataListLoading = true
      this.$http({
        url: this.$http.adornUrl('/admin/mall/stock/getPages'),
        method: 'post',
        data: this.$http.adornData({
          'currentPage': this.pageIndex,
          'pageSize': this.pageSize,
          'orgCode': this.dataForm.orgCode,
          'keyWord': this.dataForm.keyWord
        })
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.dataList = data.obj.records
          this.totalPage = data.obj.total
        } else {
          this.dataList = []
          this.totalPage = 0
        }
        this.dataListLoading = false
      })
    },
    auditRecord(id) {
      this.numChangeVisible = true
      this.$nextTick(() => {
        this.$refs.auditList.init(id)
      })
    },
    goodsStorageIn(id) {
      this.changeNumVisable = true
      this.$nextTick(() => {
        this.$refs.changeNum.init(id)
      })
    },
    // 每页数
    sizeChangeHandle(val) {
      this.pageSize = val
      this.pageIndex = 1
      this.getDataList()
    },
    // 当前页
    currentChangeHandle(val) {
      this.pageIndex = val
      this.getDataList()
    },
    // 多选
    selectionChangeHandle(val) {
      this.dataListSelections = val
    },
    // 新增 / 修改
    addOrUpdateHandle(id) {
      this.addOrUpdateVisible = true
      this.$nextTick(() => {
        this.$refs.addOrUpdate.init(id)
      })
    },
    resetForm() {
      this.$refs['dataForm']?.resetFields()
      this.getDataList()
    }
  }
}
</script>
