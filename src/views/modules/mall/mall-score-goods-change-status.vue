<template>
	<el-dialog title="状态变更" :visible.sync="visible" :append-to-body="true" width="30%">
		<el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmit()"
			label-width="280px">
			<el-form-item v-if="dataForm.status" label="下架是否同时清空当前库存" prop="status">
			</el-form-item>
			<el-form-item v-else label="上架是否同时清空当前库存" prop="status">
			</el-form-item>
		</el-form>
		<span slot="footer" class="dialog-footer">
			<el-button v-if="dataForm.status" type="warning" @click="dataFormSubmit(dataForm.goodsId,false)">下架不清空库存
			</el-button>
			<el-button v-else type="warning" @click="dataFormSubmit(dataForm.goodsId,false)">上架不清空库存</el-button>
			<el-button v-if="dataForm.status" type="primary" @click="dataFormSubmit(dataForm.goodsId,true)">下架并清空库存
			</el-button>
			<el-button v-else type="primary" @click="dataFormSubmit(dataForm.goodsId,true)">上架并清空库存</el-button>
		</span>
	</el-dialog>
</template>

<script>
	export default {
		data() {
			return {
				visible: false,
				dataList: [],
				dataListLoading: false,
				dataForm: {
					status: null,
					goodsId: null
				},
				dataRule: {
					goodsCount: [{
						required: true,
						message: '商品数量不能为空',
						trigger: 'blur'
					}]
				},
				goodsCountError: null
			}
		},

		methods: {
			init(id, status) {
				this.dataForm.goodsId = id || null
				this.dataForm.status = status
				this.visible = true
			},
			// 表单提交
			dataFormSubmit(id, status) {
				this.$refs['dataForm'].validate((valid) => {
					if (valid) {
						this.$http({
							url: this.$http.adornUrl('/admin/mall/score-goods/updateStatus'),
							method: 'post',
							params: this.$http.adornParams({
								'goodsId': id,
								'isClearStorge': status
							})
						}).then(({
							data
						}) => {
							if (data && data.code === 0) {
								this.$message({
									message: '操作成功',
									type: 'success',
									duration: 1000,
									onClose: () => {
										this.visible = false
										this.$emit('refreshDataList')
									}
								})
							} else {
								this.$message.error(data.msg)
							}
						})
					}
				})
			}
		}
	}
</script>
