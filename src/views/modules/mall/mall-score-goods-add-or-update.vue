<template>
    <div>
        <el-dialog :title="!dataForm.id ? '新增' : '修改'" :close-on-click-modal="false" width="75%"
                   :visible.sync="visible">
            <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmit()"
                     label-width="150px">
                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="物料" prop="sku" :error="skuError">
                            <div style="display: flex; justify-content: space-between">
                                <el-select v-model="dataForm.sku" placeholder="请选择" filterable style="width: 60%"
                                           @change="(value) => changeStock(value)"
                                           :disabled="dataForm.id && dataForm.id !== ''">
                                    <el-option v-for="item in stockList" :key="item.sku" :label="item.name"
                                               :value="item.sku">
                                        <span style="float: left">{{ item.name }}</span>
                                        <span style="float: right; color: #C0C4CC; font-size: 10px">{{
                                            item.sku
                                            }}</span>
                                    </el-option>
                                </el-select>
                                <el-button v-if="!dataForm.id" type="success"
                                           @click="createStock">新建物料
                                </el-button>
                                <span v-if="dataForm.sku !== null && dataForm.sku !== ''&& !dataForm.id"
                                      style="font-size: small; color: red ;">可用库存：{{
                                    this.minTimeLimit + '个'
                                    }}</span>
                                <span v-if="dataForm.id && dataForm.id !== ''"
                                      style="color: red ;">SKU编码: {{ dataForm.sku }}
              </span>
                            </div>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item :label="'商品类型'" prop="scoreGoodsType">
                            <el-select v-model="dataForm.scoreGoodsType" placeholder="请选择" clearable
                                       style="width: 100%" :disabled="dataForm.id && dataForm.id !== ''"
                                       @change="goodsTypeChange">
                                <el-option
                                        v-for="item in goodsTypeList"
                                        :key="item.code"
                                        :label="item.name"
                                        :value="item.code"/>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="星光闪耀商品" prop="star">
                            <el-checkbox v-model="dataForm.star" @change="starChange"></el-checkbox>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="商品名称" prop="goodsName" :error="goodsNameError">
                            <el-input v-model="dataForm.goodsName" placeholder="商品名称"/>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="商品数量" prop="goodsCount" :error="goodsCountError">
                            <el-input-number v-model="dataForm.goodsCount" controls-position="right" :min="0"
                                             :disabled="dataForm.id && dataForm.id !== ''"/>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="状态" prop="status" :error="statusError">
                            <el-radio-group v-model="dataForm.status">
                                <el-radio :label="true">上架</el-radio>
                                <el-radio :label="false">下架</el-radio>
                            </el-radio-group>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="20" v-if="dataForm.scoreGoodsType === 'mall_score_goods_type_machine'">
                    <el-col :span="12">
                        <el-form-item label="兑换机" prop="machineNo">
                            <el-select v-model="dataForm.machineNo" placeholder="请选择" clearable
                                       style="margin-right: 10px; width: 100%">
                                <el-option
                                        v-for="item in machineList"
                                        :key="item.machineNo"
                                        :label="item.machineNo"
                                        :value="item.machineNo"/>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="兑换机商品编码" prop="machineGoodsCode">
                            <el-input style="width: 100%"
                                      v-model="dataForm.machineGoodsCode" placeholder="兑换机商品编码唯一"/>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="20" v-if="dataForm.scoreGoodsType === 'mall_score_goods_type_pad'">
                    <el-col :span="12">
                        <el-form-item label="pad兑换站点" prop="stationCodeList">
                            <el-select v-model="dataForm.stationCodeList" placeholder="请选择" clearable
                                       style="margin-right: 10px; width: 100%" multiple
                                       :disabled="dataForm.id && dataForm.id !== ''">
                                <el-option
                                        v-for="item in stationList"
                                        :key="item.code"
                                        :label="item.name"
                                        :value="item.code"/>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="站点商品编码" prop="stationGoodsCode">
                            <el-input style="width: 100%"
                                      v-model="dataForm.stationGoodsCode" placeholder="站点商品编码唯一"
                                      :disabled="dataForm.id && dataForm.id !== ''"/>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="20" v-if="dataForm.scoreGoodsType === 'mall_score_goods_type_shopping' || dataForm.scoreGoodsType === 'mall_score_goods_type_pad'
|| dataForm.scoreGoodsType === 'mall_score_goods_type_machine'">
                    <el-col :span="12">
                        <el-form-item label="兑换类型" prop="exchangeType" placeholder="请选择">
                            <el-checkbox-group v-model="dataForm.exchangeType"
                                               :disabled="dataForm.scoreGoodsType === 'mall_score_goods_type_pad'||dataForm.scoreGoodsType === 'mall_score_goods_type_machine'">
                                <el-checkbox v-for="item in typeList" :key="item.code" :label="item.code"
                                             :value="item.code">{{ item.name }}
                                </el-checkbox>
                            </el-checkbox-group>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="线下兑换积分" prop="offlineScore">
                            <el-input-number v-model="dataForm.offlineScore" placeholder="线下兑换积分" :precision="2"
                                             :step="0.1"/>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6"
                            v-if="dataForm.scoreGoodsType === 'mall_score_goods_type_shopping'  && !dataForm.star ">
                        <el-form-item label="快递兑换积分" prop="requiredScore">
                            <el-input-number v-model="dataForm.requiredScore" placeholder="线上兑换积分" :precision="2"
                                             :step="0.1"/>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="20" v-if="dataForm.star">
                    <el-col :span="12">
                        <el-form-item label="礼遇类别" prop="courteousType" :error="courteousTypeError">
                            <el-dict style="width: 100%" :code="'courteous_type'" v-model="dataForm.courteousType"/>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="支持单位" prop="supportUnit">
                            <el-input v-model="dataForm.supportUnit" placeholder="支持单位"/>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="20" v-if="dataForm.star">
                    <el-col :span="6">
                        <el-form-item label="星级要求" prop="serviceLong">
                            <el-select v-model="dataForm.serviceLong" placeholder="请选择" clearable
                                       style="width: 100%">
                                <el-option
                                        v-for="item in starList"
                                        :key="item.value"
                                        :label="item.name"
                                        :value="item.value"/>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="当年服务时长要求" prop="serviceLongThisYear">
                            <el-input-number v-model="dataForm.serviceLongThisYear" placeholder="当年服务时长要求"
                                             :precision="2"
                                             :step="0.5"/>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6"
                            v-if="dataForm.scoreGoodsType === 'mall_score_goods_type_shopping'  && dataForm.star ">
                        <el-form-item prop="needExchange">
                            <el-checkbox v-model="dataForm.needExchange">需要兑换</el-checkbox>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6"
                            v-if="dataForm.scoreGoodsType === 'mall_score_goods_type_shopping'  && dataForm.star ">
                        <el-form-item prop="needApply">
                            <el-checkbox v-model="dataForm.needApply">需要报名</el-checkbox>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="20" v-if="dataForm.star">
                    <el-col :span="12">
                        <el-form-item label="现有积分" prop="totalPoint">
                            <el-input-number v-model="dataForm.totalPoint" placeholder="现有积分" :precision="2"
                                             :step="0.1"/>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12"
                            v-if="dataForm.scoreGoodsType === 'mall_score_goods_type_shopping'  && dataForm.star   && dataForm.needApply ">
                        <el-form-item label="表单类别" prop="applyType" >
                            <el-dict style="width: 100%" :code="'star_apply_type'"
                                     v-model="dataForm.applyType"/>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="20" v-if="dataForm.star">

                    <el-col :span="24">
                        <el-form-item label="激励对象" prop="encourage">
                            <el-input v-model="dataForm.encourage" placeholder="激励对象" type="textarea"
                                      :rows="3"/>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="20" v-if="dataForm.star">
                    <el-col :span="24">
                        <el-form-item label="商品说明" prop="goodsDesc">
                            <el-input
                                    type="textarea"
                                    :rows="3"
                                    placeholder="请输入内容"
                                    v-model="dataForm.goodsDesc">
                            </el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="20" v-if="dataForm.star">
                    <el-col :span="24">
                        <el-form-item label="兑换成功信息" prop="successMsg">
                            <el-input
                                    type="textarea"
                                    :rows="2"
                                    placeholder="请输入内容"
                                    v-model="dataForm.successMsg">
                            </el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="20" v-if="dataForm.star">
                    <el-col :span="12">
                        <el-form-item label="商品展示状态" prop="displayStatus" :error="displayStatusError">
                            <el-dict style="width: 100%" :code="'goods_display_status'"
                                     v-model="dataForm.displayStatus"/>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="第三方平台" prop="thirdPlatformCode">
                                <el-dict style="width: 100%" :code="'zyz_point_record_biz_type'"
                                         v-model="dataForm.thirdPlatformCode"/>

                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="第三方商品编码" prop="thirdPlatformGoodsCode">
                            <el-input v-model="dataForm.thirdPlatformGoodsCode" placeholder="第三方商品编码"/>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="商品品牌" prop="goodsBrand" :error="goodsBrandError">
                            <el-input v-model="dataForm.goodsBrand" placeholder="商品品牌"/>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12" v-if="this.managerCapacity === 'ASSOCIATION_ADMIN'">
                        <el-form-item :label="'所属组织架构'" prop="orgCode" :error="orgCodeError">
                            <el-select style="width: 100%" placeholder="所属组织机构" v-model="dataForm.orgCode"
                                       clearable>
                                <el-option v-for="item in orgList"
                                           :key="item.code" :label="item.name" :value="item.code"/>
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="20">
                    <el-col :span="24">
                        <el-form-item class="address-item" label="地址" prop="exchangeAddress"
                                      :error="exchangeAddressError">
                            <el-input style="width: 85%" v-model="dataForm.exchangeAddress" placeholder="地址"/>
                            <el-button style="float: right" type="primary" @click="showMap">地图定位</el-button>
                        </el-form-item>
                    </el-col>
                    <!-- 				<el-col :span="6"><div class="grid-content bg-purple">
                             <el-form-item label="地址" prop="exchangeAddress">
                                <el-input v-model="dataForm.exchangeAddress"  size="mini" ></el-input>
                              </el-form-item>
                            </div></el-col>
                            <el-col :span="6"><div class="grid-content bg-purple">
                              <el-form-item>
                                <el-button  type="text" size="small" @click="locate()">定位</el-button>
                             </el-form-item>
                            </div></el-col> -->
                </el-row>
                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="负责人名称" prop="linkPersonName" :error="linkPersonNameError">
                            <el-input v-model="dataForm.linkPersonName" placeholder="负责人名称"/>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="负责人联系电话" prop="linkPhone" :error="linkPhoneError">
                            <el-input v-model="dataForm.linkPhone" placeholder="负责人联系电话"/>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="兑换频率" prop="frequency" :error="frequencyError">
                            <el-dict style="width: 100%" :code="'good_exchange_frequency'"
                                     v-model="dataForm.frequency"/>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="排序" prop="sequence" :error="sequenceError">
                            <el-input-number v-model="dataForm.sequence" placeholder="排序"/>
                            <span style="color: red ;"> 数值>=9999将会被置顶，且标注成【上新】</span>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="20">
                    <el-col :span="24">
                        <el-form-item label="商品详情介绍" prop="goodsContent">
                            <teditor style="width: 100%;" :value="dataForm.goodsContent" :disabled="false" ref="teditor"
                                     @changeEditorValue="changeAnswer"/>
                        </el-form-item>
                    </el-col>
                    <el-col :span="15">
                        <el-form-item label="主图" prop="goodsLogo">
                            <el-upload class="avatar-uploader" :action="this.$http.adornUrl(`/admin/oss/upload`)"
                                       :headers="headers" :data="{serverCode: uploadOptions.serverCode, media: false}"
                                       :show-file-list="false" :on-success="successHandle" :on-change="changHandle"
                                       :on-exceed="exceedHandle" :before-upload="beforeUploadHandle">
                                <img v-if="dataForm.goodsLogo" :src="$http.adornAttachmentUrl(dataForm.goodsLogo)"
                                     class="avatar">
                                <i v-else class="el-icon-plus avatar-uploader-icon"/>
                                <div slot="tip" class="el-upload__tip" style="color: red">
                                    尺寸建议600x1000像素，文件格式jpg、bmp或png，大小不超2M
                                </div>
                            </el-upload>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>

            <span slot="footer" class="dialog-footer">
				<el-button @click="visible = false,locateVisible = false">取消</el-button>
				<el-button type="primary" :disabled="isDisabled" @click="dataFormSubmit()">确定</el-button>
			</span>
        </el-dialog>
        <el-dialog class="map-dialog" title="地图" :close-on-click-modal="false" width="70%" :visible.sync="mapVisible">
            <AMapInfo v-if="mapVisible" :mapVisible.sync="mapVisible" :address.sync="dataForm.exchangeAddress"
                      :latitude.sync="dataForm.latitude" :longitude.sync="dataForm.longitude"/>
        </el-dialog>
        <stock-new ref="newStock" @refreshDataList="newStockEmit" v-if="newStockVisible"/>
    </div>
</template>

<script>
import moment from 'moment'
import Teditor from '@/components/tinymce'
import editMixin from '@/mixins/edit-mixins'
import AMapInfo from '@/components/map/a-map-info.vue'
import StockNew from './mall-stock-add-or-update'
import {
    getFathersById
} from '@/utils'
import fileUploadMixin from '@/mixins/file-upload-mixins'

export default {
    mixins: [editMixin, fileUploadMixin],
    data() {
        return {
            isAssociationAdmin: this.$store.state.user.managerCapacity === 'ASSOCIATION_ADMIN',
            visible: false,
            mapVisible: false,
            isDisabled: false,
            newStockVisible: false,
            orgList: [],
            stockList: [],
            machineList: [],
            stationList: [],
            typeList: [],
            goodsTypeList: [],
            starList: [],
            minTimeLimit: '',
            uploadOptions: {
                fieldName: 'goodsLogo',
                maxSize: 2
            },
            managerCapacity: '',
            initForm: {
                id: null,
                version: null,
                goodsName: '',
                goodsDesc: '',
                goodsContent: '',
                goodsLogo: null,
                scoreGoodsType: '',
                goodsType: '',
                startDate: '',
                sku: '',
                endDate: '',
                goodsCount: '',
                hot: '',
                viewCount: '',
                requiredScore: '',
                offlineScore: '',
                areaLevel: '',
                remark: '',
                exchangeAddress: '',
                exchangeType: [],
                longitude: '',
                latitude: '',
                goodsBrand: '',
                orgCode: '',
                orgName: '',
                status: true,
                linkPersonName: '',
                machineGoodsCode: '',
                machineNo: '',
                stationGoodsCode: '',
                stationCodeList: [],
                stationCode: '',
                linkPhone: '',
                frequency: 'good_exchange_frequency_none',
                sequence: 0,
                star: false,
                courteousType: '',
                encourage: '',
                supportUnit: '',
                serviceLong: '',
                serviceLongThisYear: null,
                needExchange: false,
                needApply: false,
                successMsg: '',
                displayStatus: '',
                thirdPlatformGoodsCode: '',
                totalPoint:null,
                applyType:''

            },
            dataForm: {},
            dataRule: {
                goodsLogo: [{
                    required: true,
                    message: '商品主图不能为空',
                    trigger: 'blur'
                }],
                sku: [{
                    required: true,
                    message: '物料编码不能为空',
                    trigger: 'blur'
                }],
                scoreGoodsType: [{
                    required: true,
                    message: '商品类型不能为空',
                    trigger: 'blur'
                }],
                exchangeType: [{
                    required: true,
                    message: '兑换途径不能为空',
                    trigger: 'blur'
                }],
                goodsCount: [{
                    required: true,
                    message: '商品数量不能为空',
                    trigger: 'blur'
                }],
                status: [{
                    required: true,
                    message: '状态不能为空',
                    trigger: 'blur'
                }],
                goodsName: [{
                    required: true,
                    message: '商品名称不能为空',
                    trigger: 'blur'
                }],
                orgCode: [{
                    required: true,
                    message: '请选择所属区域',
                    trigger: 'blur'
                }],
                courteousType: [{
                    required: true,
                    message: '请选择礼遇类别',
                    trigger: 'blur'
                }],
                // frequency: [{
                //     required: true,
                //     message: '请选择兑换频率',
                //     trigger: 'blur'
                // }],
                encourage: [{
                    required: true,
                    message: '请填写激励对象',
                    trigger: 'blur'
                }],
                supportUnit: [{
                    required: true,
                    message: '请填写支持单位',
                    trigger: 'blur'
                }],
                displayStatus: [{
                    required: true,
                    message: '请选择商品展示状态',
                    trigger: 'blur'
                }],
                linkPersonName: [{
                    required: true,
                    message: '负责人不能为空',
                    trigger: 'blur'
                }],
                linkPhone: [{
                    required: true,
                    message: '负责人电话不能为空',
                    trigger: 'blur'
                }]

            },
            goodsNameError: null,
            goodsDescError: null,
            goodsContentError: null,
            goodsLogoError: null,
            goodsTypeError: null,
            startDateError: null,
            endDateError: null,
            goodsCountError: null,
            hotError: null,
            viewCountError: null,
            areaLevelError: null,
            remarkError: null,
            exchangeAddressError: null,
            skuError: null,
            longitudeError: null,
            latitudeError: null,
            goodsBrandError: null,
            orgCodeError: null,
            orgNameError: null,
            statusError: null,
            linkPersonNameError: null,
            linkPhoneError: null,
            courteousTypeError: null,
            frequencyError: null,
            encourageError: null,
            supportUnitError: null,
            sequenceError: null,
            serviceLongError: null,
            displayStatusError: null,
            locateVisible: false
        }
    },
    components: {
        Teditor,
        moment,
        StockNew,
        AMapInfo
    },
    methods: {
        async init(id) {
            this.dataForm = _.cloneDeep(this.initForm)
            this.managerCapacity = this.$store.state.user.managerCapacity
            this.dataForm.orgCode = this.$store.state.user.orgCode
            this.dataForm.exchangeTypes = []
            this.dataForm.id = id || null
            this.changeAnswer('<p>实例：</p>\n' +
                '<p>1、 **积分可兑换；</p>\n' +
                '<p>2 、兑换方式；</p>\n' +
                '<p>3、 领取地址；</p>\n' +
                '<p>4、 联系电话。</p>\n' +
                '<p>&nbsp;</p>\n' +
                '<p>&nbsp;</p>')
            this.visible = true
            await this.getOrg()
            await this.getType()
            await this.getGoodsType()
            await this.getStarList()
            await this.getStock()
            await this.getMachineList()
            await this.getStationList()
            this.$nextTick(() => {
                this.$refs['dataForm']?.resetFields()
                if (this.dataForm.id) {
                    this.$http({
                        url: this.$http.adornUrl(`/admin/mall/score-goods`),
                        method: 'get',
                        params: this.$http.adornParams({
                            id: this.dataForm.id
                        })
                    }).then(({data}) => {
                        if (data && data.code === 0) {
                            data.obj.exchangeType = data.obj.exchangeType ? data.obj.exchangeType.split(',') : []
                            data.obj.stationCodeList = data.obj.stationCode ? data.obj.stationCode.split(',') : []
                            this.dataForm = data.obj
                            this.goodsTypeChange(this.dataForm.scoreGoodsType)
                        }
                    })
                }
            })
        },
        showMap() {
            this.mapVisible = true
        },
        //获取所属区域
        async getOrg() {
            const {
                data
            } = await this.$http({
                url: this.$http.adornUrl(`/admin/org/getFiveAreaSubAssociationOrg`),
                method: 'get',
                params: this.$http.adornParams({withTop: true})
            })
            if (data && data.code === 0) {
                this.orgList = data.obj
            } else {
                this.orgList = []
            }
        },
        async getGoodsType() {
            const {
                data
            } = await this.$http({
                url: this.$http.adornUrl(`/admin/dict/parent`),
                method: 'get',
                params: this.$http.adornParams({
                    'code': 'mall_score_goods_type'
                })
            })
            if (data && data.code === 0) {
                this.goodsTypeList = data.obj || []
            } else {
                this.goodsTypeList = []
            }
        },
        async getStarList() {
            const {
                data
            } = await this.$http({
                url: this.$http.adornUrl(`/admin/dict/parent`),
                method: 'get',
                params: this.$http.adornParams({
                    'code': 'volunteer_star'
                })
            })
            if (data && data.code === 0) {
                this.starList = data.obj || []
            } else {
                this.starList = []
            }
        },
        async getType() {
            const {
                data
            } = await this.$http({
                url: this.$http.adornUrl(`/admin/dict/parent`),
                method: 'get',
                params: this.$http.adornParams({
                    'code': 'mall_score_goods_exchange_type'
                })
            })
            if (data && data.code === 0) {
                this.typeList = data.obj || []
            } else {
                this.typeList = []
            }
        },
        async getStock() {
            const {
                data
            } = await this.$http({
                url: this.$http.adornUrl(this.isAssociationAdmin ? `/admin/mall/stock/allStock` : '/admin/mall/stock/getStockByOrg'),
                method: 'get',
                params: this.$http.adornParams()
            })
            if (data && data.code === 0) {
                this.stockList = data.obj || []
            } else {
                this.stockList = []
            }
        },
        async getMachineList() {
            const {
                data
            } = await this.$http({
                url: this.$http.adornUrl(`/admin/mall/self-machine/getMachinesByCurrOrg`),
                method: 'get',
                params: this.$http.adornParams()
            })
            if (data && data.code === 0) {
                this.machineList = data.obj || []
            } else {
                this.machineList = []
            }
        },
        async getStationList() {
            const {
                data
            } = await this.$http({
                url: this.$http.adornUrl(`/admin/mall/pad-exchange-station/getStationsByCurrOrg`),
                method: 'get',
                params: this.$http.adornParams()
            })
            if (data && data.code === 0) {
                this.stationList = data.obj || []
            } else {
                this.stationList = []
            }
        },
        goodsTypeChange(value) {
            if (value === 'mall_score_goods_type_pad' || value === 'mall_score_goods_type_machine') {
                this.$set(this.dataForm, 'exchangeType', ['mall_score_goods_exchange_type_offline'])
            }
        },
        starChange(value) {
            if (value === true && this.dataForm.scoreGoodsType === 'mall_score_goods_type_shopping') {
                this.$set(this.dataForm, 'exchangeType', ['mall_score_goods_exchange_type_online'])
            }
        },
        createStock() {
            this.newStockVisible = true
            this.$nextTick(() => {
                this.$refs.newStock.init()
            })
        },
        handleImgRemove() {
            this.dataForm.goodsLogo = null
        },
        newStockEmit(id) {
            this.getStock()
        },
        locate() {
            this.locateVisible = true
            this.$nextTick(() => {
                this.$refs?.amap.init()
            })
        },
        changeStock(value) {
            if (!value || value === '') {
                return;
            }
            let stock = this.stockList.filter(it => it.sku === value)[0]
            this.minTimeLimit = stock.inventoryQuantity
        },
        close() {
            this.locateVisible = false
        },
        // 富文本赋值
        changeAnswer(html) {
            this.dataForm.goodsContent = html
        },
        // 表单提交
        dataFormSubmit() {
            this.$refs['dataForm'].validate((valid) => {
                if (valid) {
                    if (this.dataForm.scoreGoodsType === 'mall_score_goods_type_shopping') {
                        if (this.dataForm.exchangeType.indexOf('mall_score_goods_exchange_type_offline') !== -1) {
                            if (!this.dataForm.offlineScore || this.dataForm.offlineScore === '') {
                                this.$message.error('选择线下兑换请输入线下兑换的所需的积分')
                                return
                            }
                        }
                        if (this.dataForm.exchangeType.indexOf('mall_score_goods_exchange_type_ems') !== -1) {
                            if (!this.dataForm.requiredScore || this.dataForm.requiredScore === '') {
                                this.$message.error('选择线上兑换请输入邮政兑换所需的积分')
                                return
                            }
                        }
                    }
                    if (this.dataForm.scoreGoodsType === 'mall_score_goods_type_machine') {
                        if (!this.dataForm.machineNo || this.dataForm.machineNo === '') {
                            this.$message.error('选择兑换机商品请输入兑换机所需的编码')
                            return
                        }
                        if (!this.dataForm.machineGoodsCode || this.dataForm.machineGoodsCode === '') {
                            this.$message.error('选择兑换机商品请输入兑换机商品所需的编码')
                            return
                        }
                    }
                    if (this.dataForm.scoreGoodsType === 'mall_score_goods_type_pad') {
                        if (!this.dataForm.stationCodeList || this.dataForm.stationCodeList.length === 0) {
                            this.$message.error('选择pad兑换商品请输入pad站点编码')
                            return
                        }
                        if (!this.dataForm.stationGoodsCode || this.dataForm.stationGoodsCode === '') {
                            this.$message.error('选择pad兑换商品请输入pad兑换商品唯一编码')
                            return
                        }
                        if (!this.dataForm.offlineScore || this.dataForm.offlineScore === '') {
                            this.$message.error('选择pad兑换商品请输入pad兑换商品所需的积分')
                            return
                        }
                    }
                    this.dataForm.exchangeType = this.dataForm.exchangeType.join(',')
                    this.dataForm.stationCode = this.dataForm.stationCodeList.join(',')
                    if (this.dataForm.star) {
                        this.dataForm.requiredScore = null
                    }
                    console.log(this.dataForm.orgCode)
                    this.isDisabled = true
                    this.$http({
                        url: this.$http.adornUrl(
                            `/admin/mall/score-goods/${!this.dataForm.id ? 'saveMallGoods' : 'updateMallGoods'}`
                        ),
                        method: 'post',
                        data: this.$http.adornData(this.dataForm)
                    }).then(({
                                 data
                             }) => {
                        if (data && data.code === 0) {
                            this.$message({
                                message: '操作成功',
                                type: 'success',
                                duration: 1500,
                                onClose: () => {
                                    this.visible = false
                                    this.$emit('refreshDataList')
                                    this.isDisabled = false
                                }
                            })
                        } else {
                            this.dataForm.exchangeType = this.dataForm.exchangeType ? this.dataForm.exchangeType.split(',') : []
                            this.$message.error(data.msg)
                            this.isDisabled = false
                        }
                    })
                }
            })
        },
        // *处理点位分类最后children数组为空的状态
        getTreeData: function (data) {
            let that = this
            // 循环遍历json数据
            data.forEach(function (e) {
                if (!e.children || e.children.length < 1) {
                    e.children = undefined
                } else {
                    // children若不为空数组，则继续 递归调用 本方法
                    that.getTreeData(e.children)
                }
            })
            return data
        }
    }
}
</script>

<style lang="scss" scoped>
::v-deep .map-dialog {
  .el-dialog__body {
    padding: 15px 20px 30px;
  }
}
</style>
