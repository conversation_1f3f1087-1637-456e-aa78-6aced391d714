<template>
  <div class="mall-stock-replenish-plan">
    <el-form :inline="true" ref="dataForm" :model="dataForm" @keyup.enter.native="getDataList()" >
      <el-form-item label="分协会" prop="orgCode" v-if="managerCapacity && managerCapacity === 'ASSOCIATION_ADMIN'">
        <el-select v-model="dataForm.orgCode" placeholder="请选择" clearable>
          <el-option
              v-for="item in subAssociationList"
              :key="item.code"
              :label="item.name"
              :value="item.code">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="计划类型" prop="type">
        <el-dict :code="'STOCK_REP_PLAN_TYPE'" v-model="dataForm.type"></el-dict>
      </el-form-item>
      <el-form-item label="年度" prop="year">
        <el-date-picker
            v-model="dataForm.year"
            type="year"
            clearable
            value-format="yyyy"
            placeholder="请选择">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="dataForm.status" placeholder="请选择" clearable>
          <el-option
              v-for="item in [{code: 0, name: '草稿'}, {code: 1, name: '已提交'}]"
              :key="item.code"
              :label="item.name"
              :value="item.code">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-search" type="warning" @click="getDataList()">查询</el-button>
        <el-button icon="el-icon-refresh-left" @click="resetForm()">重置</el-button>
      </el-form-item>
    </el-form>
    <div class="f-s-c" style="padding: 15px 0;display: flex; justify-content: flex-end">
      <div>
        <el-button icon="el-icon-tickets" type="info" @click="calendarYearExchangeDistribution">历年兑换分布</el-button>
        <el-button icon="el-icon-plus" v-if="isAuth('mall_stock:replenish:save')" type="primary" @click="addOrUpdateHandle(null, true)">新增年度补货计划</el-button>
        <el-button icon="el-icon-plus" v-if="isAuth('mall_stock:replenish:save')" type="primary" @click="machineAddOrUpdateHandle(null, true)">新增兑换机补货计划</el-button>
        <el-button icon="el-icon-delete" v-if="isAuth('mall_stock:replenish:remove')" type="danger" @click="deleteHandle()" :disabled="dataListSelections.length <= 0">批量删除</el-button>
      </div>
    </div>
    <el-row v-if="managerCapacity && (managerCapacity === 'ASSOCIATION_ADMIN' || managerCapacity === 'SUB_ASSOCIATION_ADMIN')" v-for="item in dataList" :key="item.orgCode">
      <el-card style="margin-bottom: 5px">
        <span style="margin-left: 30px; font-size: large" v-if="managerCapacity === 'ASSOCIATION_ADMIN'">{{item.orgName}}</span>
        <div style="display: flex; flex-wrap: wrap; flex-direction:row; margin: 10px 0px 10px 0px">
          <span style="width: 100%; text-align: center" v-if="!item.planList || item.planList.length === 0">暂无补货计划数据……</span>
          <div v-if="item.planList && item.planList.length > 0" style="width: 25%; margin-bottom: 20px;" v-for="plan in item.planList" :key="plan.id" >
            <el-card style="width: 90%; background-color: #f2f2f2; cursor: pointer; margin:auto" class="plan-card">
              <div class='mark_draft' v-if="!plan.submit || plan.submit === false">
                <span>草稿</span>
              </div>
              <div class='mark_submit' v-if="plan.submit && plan.submit === true">
                <span>已提交</span>
              </div>
              <div slot="header" style="height: 15px" @click="cardClickHandle(item, plan)">
                <el-checkbox style="float: right; top:0;" v-model="plan.selected"></el-checkbox>
              </div>
              <div style="display: flex; flex-direction: row; margin-top: -10px; margin-bottom: 5px; height: 72px" @click="cardClickHandle(item,plan)">
                <img style="width: 45px; height: 45px; margin: 10px 10px 10px 40px; border-radius: 3px;" :src="plan.type === 'srpt_common' ? commonPlanLogo : machinePlanLogo"/>
                <div style="margin-top: 10px; display: flex; flex-direction:column; flex-wrap: wrap; justify-content: space-evenly; width: 100%">
                  <span style="text-align: center; font-size: medium; font-weight: bold; margin-bottom: 4px">{{plan.planYear + ' 年度'}}</span>
                  <span style="text-align: center; font-size: small; margin-bottom: 4px" v-if="plan.type === 'srpt_machine'">{{'兑换机：' + plan.machineNo}}</span>
                  <span style="text-align: center; font-size: small; margin-bottom: 4px">{{'预算金额：' + (plan.budgetAmount >= 10000 ? plan.budgetAmount/10000 + '万元' : plan.budgetAmount + ' 元')}}</span>
                </div>
              </div>
              <div style="border-top: 2px solid white; display: flex; flex-direction: row; justify-content: space-evenly; padding: 3px">
                <el-button type="text" style="width:100%;" v-if="plan.submit !== true && !(managerCapacity === 'ASSOCIATION_ADMIN' && item.orgCode !== currentOrgCode)" @click="plan.type === 'srpt_common' ? addOrUpdateHandle(plan.id, true) : machineAddOrUpdateHandle(plan.id, true)">
                  修改
                </el-button>
                <div style="width: 2px;height: 40px; background: white;" v-if="plan.submit !== true && !(managerCapacity === 'ASSOCIATION_ADMIN' && item.orgCode !== currentOrgCode)"></div>
                <el-button type="text" style="width:100%;" v-if="plan.submit !== true && !(managerCapacity === 'ASSOCIATION_ADMIN' && item.orgCode !== currentOrgCode)" @click="submit(plan.id)" :disabled="submitLoading">
                  提交
                </el-button>
                <div style="width: 2px;height: 40px; background: white;" v-if="plan.submit !== true && !(managerCapacity === 'ASSOCIATION_ADMIN' && item.orgCode !== currentOrgCode)"></div>
                <el-button type="text" style="width:100%;" @click="plan.type === 'srpt_common' ? addOrUpdateHandle(plan.id, false) : machineAddOrUpdateHandle(plan.id, false)">
                  详情
                </el-button>
                <div style="width: 2px;height: 40px; background: white;" v-if="topDeptRemoveSubEnable || !(managerCapacity === 'ASSOCIATION_ADMIN' && item.orgCode !== currentOrgCode)"></div>
                <el-button type="text" style="width:100%;" v-if="topDeptRemoveSubEnable || !(managerCapacity === 'ASSOCIATION_ADMIN' && item.orgCode !== currentOrgCode)" @click="deleteHandle(plan.id)">
                  删除
                </el-button>
              </div>
            </el-card>
          </div>
        </div>
      </el-card>
    </el-row>
    <add-or-update ref="addOrUpdate" v-if="addOrUpdateVisible" @refreshDataList="getDataList"/>
    <machine-add-or-update ref="machineAddOrUpdate" v-if="machineAddOrUpdateVisible" @refreshDataList="getDataList"/>
  </div>
</template>

<script>
import _ from 'lodash'
import CommonPlanPic from '@/assets/img/common_plan.png'
import MachinePlanPic from '@/assets/img/machine_plan.png'
import AddOrUpdate from './mall-stock-replenish-plan-add-or-update'
import MachineAddOrUpdate from './mall-stock-machine-replenish-plan-add-or-update'
export default {
  name: "mall-stock-replenish-plan",
  data () {
    return {
      dataForm: {
        type: null,
        orgCode: null,
        year: null,
        status: null
      },
      topDeptRemoveSubEnable: true, // 协会是否可删除分协会创建计划的开关
      commonPlanLogo: CommonPlanPic,
      machinePlanLogo: MachinePlanPic,
      lodash: _,
      subAssociationList: [],
      managerCapacity: null,
      currentOrgCode: null,
      dataListSelections: [],
      dataList: [],
      addOrUpdateVisible: false,
      submitLoading: false,
      machineAddOrUpdateVisible: false
    }
  },
  components: {
    AddOrUpdate,
    MachineAddOrUpdate
  },
  activated () {
    this.getDataList()
    this.getUserInfo()
    this.getSubAssociationList()
  },
  methods: {
    resetForm() {
      this.$refs['dataForm']?.resetFields()
      this.getDataList()
    },
    // 获取当前管理员信息
    getUserInfo() {
      this.$http({
        url: this.$http.adornUrl('/admin/user/loginUser'),
        method: 'get',
        params: this.$http.adornParams()
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.managerCapacity = data.obj['managerCapacity']
          this.currentOrgCode = data.obj['orgCode']
        } else {
          this.$message.error(data.msg)
        }
      })
    },
    getSubAssociationList() {
      this.$http({
        url: this.$http.adornUrl('/admin/mall/stock_replenish_plan/getSubAssociationOrgList'),
        method: 'get',
        params: this.$http.adornParams()
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.subAssociationList = data.obj
        } else {
          this.$message.error(data.msg)
        }
      })
    },
    getDataList () {
      this.$http({
        url: this.$http.adornUrl('/admin/mall/stock_replenish_plan/getPlanList'),
        method: 'post',
        data: this.$http.adornData(this.dataForm)
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.dataList = data.obj
          var that = this
          _.forEach(this.dataList, function (item) {
            _.forEach(item.planList, function (plan){
              that.$set(plan, 'selected', false)
            })
          })
        } else {
          this.$message.error(data.msg)
        }
      })
    },
    cardClickHandle (org, plan) {
      if (!this.topDeptRemoveSubEnable && (this.managerCapacity === 'ASSOCIATION_ADMIN' && org.orgCode !== currentOrgCode)) {
        return
      }
      plan.selected = !plan.selected
      if (plan.selected === true) {
        this.dataListSelections.push(plan.id)
      } else {
        _.remove(this.dataListSelections, function (value) {
          return value === plan.id
        })
      }
    },
    calendarYearExchangeDistribution () {
      this.$router.push('mall-mall-exchange-distribution-report')
    },
    addOrUpdateHandle (id, enableEdit) {
      this.addOrUpdateVisible = true
      this.$nextTick(() => {
        this.$refs.addOrUpdate.init(id, enableEdit)
      })
    },
    machineAddOrUpdateHandle (id, enableEdit) {
      this.machineAddOrUpdateVisible = true
      this.$nextTick(() => {
        this.$refs.machineAddOrUpdate.init(id, enableEdit)
      })
    },
    deleteHandle (id) {
      let ids = id ? [id] : this.dataListSelections
      this.$confirm(`确定${id ? '删除' : '批量删除'}补货计划?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        closeOnClickModal: false
      }).then(() => {
        this.$http({
          url: this.$http.adornUrl('/admin/mall/stock_replenish_plan/removeByIds'),
          method: 'get',
          params: this.$http.adornParams({'ids': ids.join(',')}, false)
        }).then(({data}) => {
          if (data.code !== 0) {
            return this.$message.error(data.msg)
          }
          this.$message({
            message: '操作成功',
            type: 'success',
            duration: 1000,
            onClose: () => {
              this.getDataList()
            }
          })
        }).catch(() => {
        })
      }).catch(() => {
      })
    },
    submit (id) {
      this.$confirm(`提交后将无法修改，确定进行提交操作?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        closeOnClickModal: false
      }).then(() => {
        this.submitLoading = true
        this.$http({
          url: this.$http.adornUrl('/admin/mall/stock_replenish_plan/submitById'),
          method: 'get',
          params: this.$http.adornParams({
            id: id
          })
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.$message({
              message: '操作成功',
              type: 'success',
              duration: 1500,
              onClose: () => {
                this.submitLoading = false
                this.getDataList()
              }
            })
          } else {
            this.$message.error(data.msg)
            this.submitLoading = false
          }
        })
      })
    }
  }
}
</script>

<style scoped lang="scss">
.plan-card:hover{
  box-shadow: 0 2px 6px rgba(255, 255, 255, 0.932);
  border-color: #409EFF;
  transition: all 0.2s ease-in-out;
}
.plan-card ::v-deep .el-card__body {
    padding: 0px;
}
.plan-card ::v-deep .el-card__header {
  padding: 0px;
  border-bottom: 0px;
}
.plan-card ::v-deep .el-checkbox__input {
  vertical-align: top;
  cursor: default;
  width: 20px;
  .el-checkbox__inner {
    width: 100%;
    border: 0;
    background-color: #f2f2f2;
  }
  .el-checkbox__inner::after{
    left: 8px;
    border: 1px solid #f2f2f2;
    border-left: 0;
    border-top: 0;
  }
}
.plan-card ::v-deep .el-checkbox__input.is-checked {
  .el-checkbox__inner {
    background-color: #f63b3b;
  }
}
.plan-card ::v-deep .el-button:hover{
  box-shadow: 1px 1px 3px rgba(0,0,0,0.3);
  span{
    color: #1b53e3;
  }
}
.plan-card {
  position: relative;
  overflow: hidden;
}
.mark_submit {
  background-color: #31e134;
  overflow: hidden;
  white-space: nowrap;
  position: absolute;
  left: -50px;  // 根据实际调整即可
  top: 5px;   // 根据实际调整即可
  transform: rotate(-45deg);
  box-shadow: 0 0 10px #888;

  span {
    color: #fff;
    padding: 5px 50px;
    display: block;
    font-size: smaller;
  }
}
.mark_draft {
  background-color: #1890FF;
  overflow: hidden;
  white-space: nowrap;
  position: absolute;
  left: -50px;  // 根据实际调整即可
  top: 2px;   // 根据实际调整即可
  transform: rotate(-45deg);
  box-shadow: 0 0 10px #888;

  span {
    color: #fff;
    padding: 5px 50px;
    display: block;
    font-size: smaller;
  }
}
</style>
