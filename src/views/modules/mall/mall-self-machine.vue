<template>
  <div class="mod-config">
    <el-form :inline="true" ref="dataForm" :model="dataForm" @keyup.enter.native="queryPage()">
      <el-form-item label="兑换机编号" prop="machineNo">
        <el-input v-model="dataForm.machineNo" placeholder="兑换机编号" clearable></el-input>
      </el-form-item>
      <el-form-item label="联系电话" prop="linkPhone">
        <el-input v-model="dataForm.linkPhone" placeholder="联系电话" clearable></el-input>
      </el-form-item>
      <el-form-item :label="'所属组织机构'" prop="orgCode">
        <el-cascader placeholder="所属组织机构" v-model="dataForm.orgCodeList" :options="orgList"
                     :show-all-levels="false" clearable :props="{checkStrictly: true,value: 'code',label: 'name'}"
        ></el-cascader>
      </el-form-item>
      <el-form-item :label="'状态'" prop="status">
        <el-dict :code="'mall_self_machine_status'" v-model="dataForm.status"></el-dict>
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-search" type="warning" @click="queryPage()">查询</el-button>
        <el-button icon="el-icon-refresh-left" @click="resetField()">重置</el-button>
      </el-form-item>
    </el-form>
    <div class="f-s-c"
         style="padding: 15px 0;display: flex;justify-content:space-between;align-items: center;float:right ">
      <el-button icon="el-icon-plus" type="primary" @click="addOrUpdateHandle()">新增</el-button>
      <el-button icon="el-icon-plus" v-if="isAuth('mall:mallselfmachine:save')" type="primary" @click="addOrUpdateHandle()">新增
      </el-button>
      <el-button icon="el-icon-delete" v-if="isAuth('mall:mallselfmachine:delete')" type="danger" @click="deleteHandle()"
                 :disabled="dataListSelections.length <= 0">批量删除
      </el-button>
      <el-button icon="el-icon-refresh" type="info" @click="getDataList()">刷新</el-button>
    </div>
    <el-table :data="dataList" border v-loading="dataListLoading" style="width: 100%;">
      <el-table-column prop="machineNo" header-align="center" align="center" width="260" label="机器编号">
      </el-table-column>
      <el-table-column prop="orgName" header-align="center" align="center" width="200" label="组织机构名称">
      </el-table-column>
      <el-table-column prop="address" header-align="center" align="center" :show-overflow-tooltip="true" label="地址">
      </el-table-column>
      <el-table-column prop="linkPersonName" header-align="center" align="center" width="95" label="负责人名称">
      </el-table-column>
      <el-table-column prop="linkPhone" header-align="center" align="center" width="140" label="负责人联系电话">
      </el-table-column>
      <el-table-column prop="statusText" header-align="center" align="center" width="70" label="状态">
      </el-table-column>
      <el-table-column fixed="right" header-align="center" align="center" width="150" label="操作">
        <template slot-scope="scope">
          <el-button type="text" size="small" @click="addOrUpdateHandle(scope.row.id)">修改</el-button>
          <el-button type="text" size="small" @click="auditRecord(scope.row.machineNo)">兑换记录</el-button>
          <el-button type="text" size="small" @click="deleteHandle(scope.row.id)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination @size-change="sizeChangeHandle" @current-change="currentChangeHandle" :current-page="pageIndex"
                   :page-sizes="[10, 20, 50, 100]" :page-size="pageSize" :total="totalPage"
                   layout="total, sizes, prev, pager, next, jumper">
    </el-pagination>
    <machine-exchange v-if="machineExchangeVisible" ref="machineExchange"></machine-exchange>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"></add-or-update>
  </div>
</template>

<script>
import AddOrUpdate from './mall-self-machine-add-or-update'
import MachineExchange from './mall-self-machine-machine-exchange'

export default {
  data() {
    return {
      dataForm: {
        key: '',
        orgCode: '',
        orgCodeList: [],
      },
      dataList: [],
      pageIndex: 1,
      pageSize: 10,
      totalPage: 0,
      dataListLoading: false,
      dataListSelections: [],
      orgList: [],
      addOrUpdateVisible: false,
      machineExchangeVisible: false
    }
  },
  components: {
    AddOrUpdate,
    MachineExchange
  },
  activated() {
    this.queryPage()
    this.getOrg()
  },
  methods: {
    queryPage() {
      this.pageIndex = 1
      this.getDataList()
    },
    resetField() {
      this.$refs['dataForm']?.resetFields()
      this.dataForm.orgCodeList = []
      this.getDataList()
    },
    // 获取数据列表
    getDataList() {
      this.dataListLoading = true
      this.$http({
        url: this.$http.adornUrl('/admin/mall/self-machine/getPages'),
        method: 'post',
        data: this.$http.adornData({
          'currentPage': this.pageIndex,
          'pageSize': this.pageSize,
          'orgCode': this.dataForm.orgCodeList.join(','),
          'machineNo': this.dataForm.machineNo,
          'linkPhone': this.dataForm.linkPhone,
          'status': this.dataForm.status
        })
      }).then(({
                 data
               }) => {
        if (data && data.code === 0) {
          this.dataList = data.obj.records
          this.totalPage = data.obj.total
          console.log(this.dataList)
        } else {
          this.dataList = []
          this.totalPage = 0
        }
        this.dataListLoading = false
      })
    },
    // 每页数
    sizeChangeHandle(val) {
      this.pageSize = val
      this.pageIndex = 1
      this.getDataList()
    },
    // 当前页
    currentChangeHandle(val) {
      this.pageIndex = val
      this.getDataList()
    },
    // 多选
    selectionChangeHandle(val) {
      this.dataListSelections = val
    },
    // 新增 / 修改
    addOrUpdateHandle(id) {
      this.addOrUpdateVisible = true
      this.$nextTick(() => {
        this.$refs.addOrUpdate.init(id)
      })
    },
    auditRecord(machineNo) {
      this.machineExchangeVisible = true
      this.$nextTick(() => {
        this.$refs.machineExchange.init(machineNo)
      })
    },
    // 删除
    deleteHandle(id) {
      var ids = id ? [id] : this.dataListSelections.map(item => {
        return item.id
      })
      this.$confirm(`确定进行[${id ? '删除' : '批量删除'}]操作?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        closeOnClickModal: false
      }).then(() => {
        this.$http({
          url: this.$http.adornUrl('/admin/mall/self-machine/removeByIds'),
          method: 'get',
          params: this.$http.adornParams({
            'ids': ids.join(',')
          })
        }).then(({
                   data
                 }) => {
          if (data && data.code === 0) {
            this.$message({
              message: '操作成功',
              type: 'success',
              duration: 1500,
              onClose: () => {
                this.getDataList()
              }
            })
          } else {
            this.$message.error(data.msg)
          }
        })
      })
    },
    //获取所属区域
    getOrg() {
      this.$http({
        url: this.$http.adornUrl(`/admin/org/getOrgTree`),
        method: 'get',
        params: this.$http.adornParams()
      }).then(({
                 data
               }) => {
        if (data && data.code === 0) {
          this.orgList = this.getTreeData(data.obj)
        } else {
          this.orgList = []
        }
      })
    },
    // *处理点位分类最后children数组为空的状态
    getTreeData: function (data) {
      let that = this
      // 循环遍历json数据
      data.forEach(function (e) {
        if (!e.children || e.children.length < 1) {
          e.children = undefined
        } else {
          // children若不为空数组，则继续 递归调用 本方法
          that.getTreeData(e.children)
        }
      })
      return data
    }
  }
}
</script>
