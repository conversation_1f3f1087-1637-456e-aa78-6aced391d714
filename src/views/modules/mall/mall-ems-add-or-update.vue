<template>
    <el-dialog :title="!dataForm.id ? '新增' : '编辑'" :close-on-click-modal="false" :visible.sync="visible"
               width="60%">

        <el-form :model="dataForm" :rules="dataRule" ref="dataForm" label-width="150px">
            <el-row :gutter="20">
                <el-col :span="12">
                    <el-form-item label="志愿者姓名" prop="volunteerName" >
                        <el-input v-model="dataForm.volunteerName" placeholder="志愿者名称" :readonly="true" ></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="手机号码" prop="volunteerPhone">
                        <el-input v-model="dataForm.volunteerPhone" placeholder="志愿者手机号码" :readonly="true" ></el-input>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row :gutter="20">
                <el-col :span="12">
                    <el-form-item label="志愿者证件类型" prop="volunteerCertificateType">
                        <el-dict :code="'certificate_type'" v-model="dataForm.volunteerCertificateType" :disabled="true"></el-dict>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="志愿者证件号" prop="volunteerCertificateId">
                        <el-input v-model="dataForm.volunteerCertificateId" placeholder="志愿者证件号" :readonly="true" ></el-input>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row :gutter="20">
                <el-col :span="12">
                    <el-form-item label="收件人姓名" prop="recipientName">
                        <el-input v-model="dataForm.recipientName" placeholder="收件人姓名"></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="收件人手机号码 " prop="recipientPhone">
                        <el-input v-model="dataForm.recipientPhone" placeholder="收件人电话 "></el-input>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row :gutter="20">
                <el-col :span="12">
                    <el-form-item label="收件人证件类型" prop="recipientCertificateType">
                        <el-dict :code="'certificate_type'" v-model="dataForm.recipientCertificateType" ></el-dict>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="收件人证件号码" prop="recipientCertificateId">
                        <el-input v-model="dataForm.recipientCertificateId"
                                         :min="0"></el-input>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row :gutter="20">
                <el-col :span="12">
                    <el-form-item label="省市区" prop="address">
                        <el-input v-model="dataForm.address" placeholder="省市区"></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="详细地址" prop="addressDetail">
                        <el-input v-model="dataForm.addressDetail" placeholder="详细地址"></el-input>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row :gutter="20">
                <el-col :span="24">
                    <el-form-item label="备注" prop="remark">
                        <el-input v-model="dataForm.remark" placeholder="备注"></el-input>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <span slot="footer" class="dialog-footer">
				<el-button @click="visible = false">取消</el-button>
				<el-button type="primary" @click="dataFormSubmit()">确定</el-button>
			</span>
    </el-dialog>
</template>

<script>

export default {
    data() {
        return {
            visible: false,
            posed: false,
            initForm: {
                id: null,
                version: null,
                year: null,
                volunteerId: null,
                volunteerName: null,
                volunteerPhone: null,
                volunteerCertificateType: null,
                volunteerCertificateId: null,
                recipientName: null,
                recipientPhone: null,
                recipientCertificateType: null,
                recipientCertificateId: null,
                address: null,
                addressDetail: null,
                remark: null
            },
            dataForm: {},
            dataRule: {
                year: [
                    {required: true, message: '年份不能为空', trigger: 'blur'}
                ],
                volunteerId: [
                    {required: true, message: '志愿者id不能为空', trigger: 'blur'}
                ],
                volunteerName: [
                    {required: true, message: '志愿者名称不能为空', trigger: 'blur'}
                ],
                volunteerPhone: [
                    {required: true, message: '志愿者手机号码不能为空', trigger: 'blur'}
                ],
                volunteerCertificateType: [
                    {required: true, message: '志愿者证件类型不能为空', trigger: 'blur'}
                ],
                volunteerCertificateId: [
                    {required: true, message: '志愿者证件号不能为空', trigger: 'blur'}
                ],
                recipientName: [
                    {required: true, message: '收件人姓名不能为空', trigger: 'blur'}
                ],
                recipientPhone: [
                    {required: true, message: '收件人电话 不能为空', trigger: 'blur'}
                ],
                address: [
                    {required: true, message: '省市区不能为空', trigger: 'blur'}
                ],
                addressDetail: [
                    {required: true, message: '详细地址不能为空', trigger: 'blur'}
                ],
                remark: [
                    {required: true, message: '备注不能为空', trigger: 'blur'}
                ]
            }
        }
    },
    components: {},
    methods: {
        async init(id) {
            this.dataForm = _.cloneDeep(this.initForm)
            this.dataForm.id = id || null
            this.visible = true
            this.$nextTick(() => {
                this.$refs['dataForm']?.resetFields()
                if (this.dataForm.id) {
                    this.$http({
                        url: this.$http.adornUrl(`/admin/mall/ems/info`),
                        method: 'get',
                        params: this.$http.adornParams({
                            id: this.dataForm.id
                        })
                    }).then(({data}) => {
                        if (data && data.code === 0) {
                            this.dataForm = data.obj
                        }
                    })
                }
            })
        },
        // 表单提交
        dataFormSubmit() {
            this.$refs['dataForm'].validate((valid) => {
                if (valid) {
                    this.$http({
                        url: this.$http.adornUrl(`/admin/mall/ems/info/${!this.dataForm.id ? 'save' : 'update'}`),
                        method: 'post',
                        data: this.$http.adornData(this.dataForm)
                    }).then(({data}) => {
                        if (data && data.code === 0) {
                            this.$message({
                                message: '操作成功',
                                type: 'success',
                                duration: 1500,
                                onClose: () => {
                                    this.visible = false
                                    this.$emit('refreshDataList')
                                }
                            })
                        } else if (data && data.code === 303) {
                            for (let it of data.obj) {
                                this[`${it.field}Error`] = it.message
                            }
                        } else {
                            this.$message.error(data.msg)
                        }
                    })
                }
            })
        },
    }
}
</script>
