<template>
  <el-dialog
    :title="!dataForm.id ? '新增' : '修改'"
    :close-on-click-modal="false"
    :visible.sync="visible">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmit()"
             label-width="80px">
      <el-form-item label="数据编码" prop="code" :error="errors['code']">
        <el-input v-model="dataForm.code" placeholder="数据编码"></el-input>
      </el-form-item>
      <el-form-item label="数据名称" prop="name" :error="errors['name']">
        <el-input v-model="dataForm.name" placeholder="数据名称"></el-input>
      </el-form-item>
      <el-form-item label="数据值" prop="value" :error="errors['value']">
        <el-input type='textarea' :rows="2" autosize v-model="dataForm.value" placeholder="数据值"></el-input>
      </el-form-item>
      <el-form-item label="上级字典" prop="parentId" :error="errors['parentId']">
        <el-popover ref="dictListPopover" placement="bottom-start" trigger="click">
          <el-tree
            :data="treeData"
            :props="props"
            node-key="id"
            ref="dictTree"
            @current-change="currentChangeHandle"
            :default-expand-all="false"
            :highlight-current="true"
            :expand-on-click-node="false"
            style="height: 500px;overflow: auto"
          >
          </el-tree>
        </el-popover>
        <el-input v-model="dataForm.parentName" v-popover:dictListPopover :readonly="true" placeholder="点击选择上级字典"></el-input>
      </el-form-item>
      <el-form-item label="排序" prop="sequence" :error="errors['sequence']">
        <el-input v-model="dataForm.sequence" placeholder="排序"></el-input>
      </el-form-item>
      <el-form-item label="状态" prop="status" :error="errors['status']">
        <el-radio-group v-model="dataForm.status">
          <el-radio :label="true">启用</el-radio>
          <el-radio :label="false">禁用</el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
  import editMixin from '@/mixins/edit-mixins'
  export default {
    mixins: [editMixin],
    data () {
      return {
        editOptions: {
          initUrl: '/admin/dict'
        },
        treeData: [],
        props: {
          key: 'id',
          label: 'name',
          children: 'children'
        },
        dataForm: {
          code: null,
          name: null,
          value: null,
          sequence: 0,
          version: null,
          parentId: null,
          parentName: '',
          status: true
        },
        dataRule: {
          code: [
            {required: true, message: '数据编码不能为空', trigger: 'blur'}
          ],
          name: [
            {required: true, message: '数据名称不能为空', trigger: 'blur'}
          ],
          value: [
            {required: true, message: '数据值不能为空', trigger: 'blur'}
          ],
          sequence: [
            {required: true, message: '排序不能为空', trigger: 'blur'}
          ],
          status: [
            {required: true, message: '状态不能为空', trigger: 'blur'}
          ]
        },
        codeError: null,
        nameError: null,
        valueError: null,
        parentIdError: null,
        sequenceError: null,
        statusError: null
      }
    },
    methods: {
      init (id, pid) {
        this.treeData = this.$parent.treeData
        this.dataForm.id = id || null
        this.dataForm.parentId = pid || null
        this.visible = true
        this.$nextTick(() => {
          this.$refs['dataForm']?.resetFields()
          if (this.dataForm.id) {
            this.$http({
              url: this.$http.adornUrl(`/admin/dict`),
              method: 'get',
              params: this.$http.adornParams({'id': this.dataForm.id})
            }).then(({data}) => {
              if (data && data.code === 0) {
                this.dataForm = data.obj
              }
            })
          }
          this.dictListTreeSetCurrentNode()
        })
      },
      // 字典树选中
      currentChangeHandle (data, node) {
        this.dataForm.parentId = data.id
        this.dataForm.parentName = data.name
        this.$refs[`dictListPopover`].doClose()
      },
      // 字典树设置当前选中节点
      dictListTreeSetCurrentNode () {
        let key = this.dataForm.parentId
        if (key) {
          this.$refs.dictTree.setCurrentKey(key)
          this.dataForm.parentName = (this.$refs.dictTree.getCurrentNode() || {})['name']
        } else {
          this.$refs.dictTree.setCurrentKey([])
          this.dataForm.parentName = ''
        }
      }
    }
  }
</script>
