<template>
  <el-dialog
      :title="'新增展示年份'"
      :close-on-click-modal="false"
      :visible.sync="visible">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmit()" width="360px"
             label-width="80px">
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="年份" prop="year" :error="errors['year']">
            <el-input-number v-model="dataForm.year" controls-position="right" :min="0"
                             style="width: 100%"></el-input-number>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import editMixin from '@/mixins/edit-mixins'

export default {
  mixins: [editMixin],
  data() {
    return {
      editOptions: {
        initUrl: '/admin/zyz/project-year'
      },
      initForm: {
        year: ''
      },
      dataRule: {
        year: [{required: true, message: '请输入年份', trigger: 'change'}]
      }
    }
  },
  methods: {}
}
</script>
