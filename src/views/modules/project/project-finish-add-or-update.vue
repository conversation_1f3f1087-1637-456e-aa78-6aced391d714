<template>
  <el-dialog
      :title="'结项'"
      :close-on-click-modal="false"
      width="80%"
      :visible.sync="visible">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" label-width="160px">
      <div style="display: flex;">
        <div style="width: 5px;height: 15px;background-color: #00feff;margin-right: 10px"></div>
        <div style="margin-bottom: 30px;font-weight: bold">项目信息</div>
      </div>
      <el-row>
        <el-col :span="24">
          <el-form-item label="项目名称" prop="projectName">
            <el-input readonly v-model="dataForm.projectName" placeholder="项目名称"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="开始时间" prop="startTime">
            <el-input style="width: 100%" readonly v-model="dataForm.startTime" placeholder="开始时间"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="结束时间" prop="endTime">
            <el-input style="width: 100%" readonly v-model="dataForm.endTime" placeholder="结束时间"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="收到资金（元）" prop="fundsReceived" :error="fundsReceivedError">
            <el-input style="width: 100%" v-model="dataForm.fundsReceived" placeholder="收到资金"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="支出资金（元）" prop="fundsSpent" :error="fundsSpentError">
            <el-input style="width: 100%" v-model="dataForm.fundsSpent" placeholder="支出资金"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="参与人数" prop="participantsNum" :error="participantsError">
            <el-input-number style="width: 100%" v-model="dataForm.participantsNum" controls-position="right" :min="0"></el-input-number>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="惠及人数" prop="beneficiariesNum" :error="beneficiariesError">
            <el-input-number style="width: 100%" v-model="dataForm.beneficiariesNum" controls-position="right" :min="0"></el-input-number>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="是否培训志愿者" prop="trainedVolunteers" :error="trainedVolunteersError">
            <el-radio-group v-model="dataForm.trainedVolunteers">
              <el-radio :label="true">是</el-radio>
              <el-radio :label="false">否</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="是否有专职财务" prop="fullTimeFinance" :error="fullTimeFinanceError">
            <el-radio-group v-model="dataForm.fullTimeFinance">
              <el-radio :label="true">是</el-radio>
              <el-radio :label="false">否</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>
      <el-form-item label="项目概述及主要事绩" prop="projectSummaryFinish" :error="projectSummaryError">
        <el-input type="textarea"   :rows="3" v-model="dataForm.projectSummaryFinish" placeholder="项目概述及主要事绩" rows="3" maxlength="500" show-word-limit></el-input>
      </el-form-item>
      <el-form-item label="志愿者平台使用情况" prop="volunteerPlatformUsage" :error="volunteerPlatformUsageError">
        <el-input type="textarea" :rows="3" v-model="dataForm.volunteerPlatformUsage" placeholder="志愿者平台使用情况" rows="3" maxlength="500" show-word-limit></el-input>
      </el-form-item>
      <el-form-item label="项目成效分析" prop="projectOutcomes" :error="projectOutcomesError">
        <el-input type="textarea"  :rows="3" v-model="dataForm.projectOutcomes" placeholder="项目成效分析" rows="3" maxlength="500" show-word-limit></el-input>
      </el-form-item>
      <el-form-item label="媒体宣传情况" prop="mediaCoverage" :error="mediaCoverageError">
        <el-input type="textarea" :rows="3" v-model="dataForm.mediaCoverage" placeholder="媒体宣传情况" rows="3" maxlength="500" show-word-limit></el-input>
      </el-form-item>
      <el-form-item label="受表彰情况" prop="accolades" :error="accoladesError">
        <el-input type="textarea"  :rows="3" v-model="dataForm.accolades" placeholder="受表彰情况" rows="3" maxlength="500" show-word-limit></el-input>
      </el-form-item>
      <el-form-item label="图片" prop="pictureFinish" :error="pictureFinishError">
        <el-upload
            list-type="picture-card"
            :action="this.$http.adornUrl(`/admin/oss/upload`)"
            :headers="myHeaders"
            :data="{serverCode: this.serverCode,media:false}"
            :show-file-list="true"
            :multiple="true"
            :file-list="fileList"
            :on-preview="handlePictureCardPreview"
            :on-remove="handleRemovePicture"
            :on-success="function (res,file){return handleAvatarSuccess(res,file, 'pictureList')}"
            :before-upload="beforeAvatarUpload">
          <i class="el-icon-plus"></i>
        </el-upload>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">提交结项审核</el-button>
    </span>
  </el-dialog>
</template>


<script>
import Vue from 'vue'
import _ from 'lodash'


export default {
  data() {
    return {
      visible: false,
      pictureList: [],
      fileList: [],
      serverCode: 'LocalServer',
      myHeaders: {Authorization: Vue.cookie.get('Authorization')},
      dataForm: {},
      initForm: {
        id: null,
        version: null,
        projectName: '',
        startTime: '',
        endTime: '',
        fundsReceived: '',
        fundsSpent: '',
        participantsNum: '',
        beneficiariesNum: '',
        trainedVolunteers: '',
        fullTimeFinance: '',
        projectSummaryFinish: '',
        volunteerPlatformUsage: '',
        projectOutcomes: '',
        mediaCoverage: '',
        accolades: '',
        pictureFinish: ''
      },
      dataRule: {
        fundsSpent: [
          {required: true, message: '支出资金不能为空', trigger: 'blur'}
        ],
        participantsNum: [
          {required: true, message: '参与人数不能为空', trigger: 'blur'}
        ],
        beneficiariesNum: [
          {required: true, message: '惠及人数不能为空', trigger: 'blur'}
        ],
        trainedVolunteers: [
          {required: true, message: '是否培训志愿者不能为空', trigger: 'blur'}
        ],
        fullTimeFinance: [
          {required: true, message: '是否有专职财务不能为空', trigger: 'blur'}
        ],
        projectSummaryFinish: [
          {required: true, message: '项目概述及主要事绩不能为空', trigger: 'blur'}
        ],
        volunteerPlatformUsage: [
          {required: true, message: '志愿者平台使用情况不能为空', trigger: 'blur'}
        ],
        projectOutcomes: [
          {required: true, message: '项目成效分析不能为空', trigger: 'blur'}
        ],
        mediaCoverage: [
          {required: true, message: '媒体宣传情况不能为空', trigger: 'blur'}
        ],
        accolades: [
          {required: true, message: '受表彰情况不能为空', trigger: 'blur'}
        ],
          pictureFinish: [
          {required: true, message: '图片不能为空', trigger: 'blur'}
      ]
      },
      fundsReceivedError: null,
      fundsSpentError: null,
      participantsError: null,
      beneficiariesError: null,
      trainedVolunteersError: null,
      fullTimeFinanceError: null,
      projectSummaryError: null,
      volunteerPlatformUsageError: null,
      projectOutcomesError: null,
      mediaCoverageError: null,
      accoladesError: null,
        pictureFinishError: null
    }
  },
  methods: {
    init(id) {
      this.dataForm = _.cloneDeep(this.initForm)
      this.dataForm.id = id || null
      this.visible = true
      this.fileList = [] || null
      this.$nextTick(() => {
        this.$refs['dataForm']?.resetFields()
        if (this.dataForm.id) {
          this.$http({
            url: this.$http.adornUrl(`/admin/project`),
            method: 'get',
            params: this.$http.adornParams({id: this.dataForm.id})
          }).then(({data}) => {
            if (data && data.code === 0) {
              this.dataForm = data.obj
              let picture = this.dataForm.pictureFinish.split(',')
              this.pictureList = picture
              picture.forEach(item => {
                let obj = {
                  url: this.$http.adornAttachmentUrl(item)
                }
                this.fileList.push(obj)
              })
            }
          })
        }
      })
    },
    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.url
      this.dialogVisible = true
    },
    handleAvatarSuccess(res, file, field) {
      if (res.success) {
        this.$message({
          message: '操作成功',
          type: 'success',
          duration: 1500
        })
        this.pictureList.push(res.obj.path)
      } else {
        this.$message.error('上传失败')
      }
    },
    handleRemovePicture(file, fileList) {
      this.pictureList = []
      fileList.forEach(item => {
        console.log(item)
        try {
          this.pictureList.push(item.url.slice(item.url.indexOf('vpath') - 1))
        } catch (e) {
          this.pictureList.push(item.url)
        }
      })
    },
    beforeAvatarUpload: function (file) {
      var isJPG = file.type === 'image/jpeg'
      var isPNG = file.type === 'image/png'
      var isBMP = file.type === 'image/bmp'
      var isLt20M = file.size / 1024 / 1024 < 20

      if (!isJPG && !isPNG && !isBMP) {
        this.$message.error('上传图片只能是图片!')
      }
      if (!isLt20M) {
        this.$message.error('上传文件大小不能超过 20MB!')
      }
      return (isJPG || isPNG || isBMP) && isLt20M
    },
    // 表单提交
    dataFormSubmit() {
        this.dataForm.pictureFinish = this.pictureList.join(',') || null
        if(this.pictureList.length<10){
            this[`pictureFinishError`] = '至少需要上传10张图片'
            return
        }
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {

          this.dataForm.pictureFinish = this.pictureList.join(',') || null
          this.$http({
            url: this.$http.adornUrl(`/admin/project/finishSave`),
            method: 'post',
            data: this.$http.adornData(this.dataForm)
          }).then(({data}) => {
            if (data && data.code === 0) {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 1500,
                onClose: () => {
                  this.visible = false
                  this.$emit('refreshDataList')
                }
              })
            } else if (data && data.code === 303) {
              for (let it of data.obj) {
                this[`${it.field}Error`] = it.message
              }
            } else {
              this.$message.error(data.msg)
            }
          })
        }
      })
    }
  }
}
</script>
