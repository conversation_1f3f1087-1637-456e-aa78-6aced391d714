<template>
  <el-dialog
      title="月报管理"
      :close-on-click-modal="false"
      :visible.sync="visible"
      width="80%"
      :show-close="!uploadLoading">
    <div style="display: flex;align-items: center;justify-content: space-between">
      <div style="display: flex;align-items: center">
        <span style="font-weight: bold; font-size: large">月报记录</span>
      </div>
      <div style="display: flex;justify-content:flex-end;padding-bottom: 20px">
<!--        <el-button type="success" @click="importDialog()"><i class="-->
<!--el-icon-upload2 el-icon&#45;&#45;right"></i>上传-->
<!--        </el-button>-->
          <el-upload
              class="upload-demo"
              :action="this.$http.adornUrl('/admin/oss/upload')"
              :data="uploadData"
              :headers="myHeaders"
              :show-file-list="false"
              :on-success="function (res,file,fileList){return uploadSuccess(res,file,fileList)}"
              :before-upload="function (file,fileList){return beforeUpload(file,fileList)}">
            <el-button icon="el-icon-upload" type="primary">点击上传
            </el-button>
          </el-upload>
        <el-button style="margin-left: 10px"  @click="downloadTemplate()" icon="el-icon-download" type="info">{{ this.templateName }}</el-button>
      </div>
    </div>
    <el-table
        :data="this.dataForm.attachmentList"
        border
        style="width: 100%;">
      <el-table-column
          prop="name"
          header-align="center"
          align="center"
          label="名称">
      </el-table-column>
      <el-table-column
          prop="uploadTime"
          header-align="center"
          align="center"
          label="提交时间">
      </el-table-column>
      <el-table-column
          fixed="right"
          header-align="center"
          align="center"
          width="150"
          label="操作">
        <template slot-scope="scope">
          <el-button type="text" size="small" @click="download(scope.row.path,scope.row.name)">下载</el-button>
          <el-button type="text" size="small" @click="deleteHandle(scope.row.name)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false" :disabled="uploadLoading">取消</el-button>
    </span>
    <el-dialog
        :title="'导入'"
        :close-on-click-modal="false"
        width="40%"
        append-to-body
        :visible.sync="importVisible">
      <el-form :model="dataForm" ref="dataForm" label-width="120px" v-loading="uploadLoading">
        <el-form-item label="附件列表" prop="attachmentList">
          <el-upload
              class="upload-demo"
              :action="this.$http.adornUrl('/admin/oss/upload')"
              :data="uploadData"
              :headers="myHeaders"
              :show-file-list="false"
              :on-success="function (res,file,fileList){return uploadSuccess(res,file,fileList)}"
              :before-upload="function (file,fileList){return beforeUpload(file,fileList)}">
            <el-button size="small" type="primary" plain>点击上传<i class="el-icon-upload el-icon--left"></i>
            </el-button>
            <div slot="tip" class="el-upload__tip">支持上传word、excel、pdf、图片</div>
          </el-upload>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
          <el-button @click="importVisible = false">关闭</el-button>
    </span>
    </el-dialog>
  </el-dialog>
</template>

<script>
import Vue from 'vue'
import _ from "lodash";

export default {
  data() {
    return {
      visible: false,
      importVisible: false,
      dataList: [],
      templateName: '简报模板.docx',
      dataForm: {
        id: '',
        attachmentList: []
      },
      templateFileName: '简报导入模板.docx',
      downloadPath: '/admin/project/template',
      myHeaders: {Authorization: Vue.cookie.get('Authorization')},
      uploadData: {serverCode: 'LocalServer', media: false},
      uploadLoading: false
    }
  },
  methods: {
    init(id) {
      this.visible = true
      this.dataForm.id = id || ''
      this.attachmentList = []
      this.getReportByProjectId()
    },
    // 上传成功
    uploadSuccess(res, file, fileList) {
      if (res.success) {
        let file = {
          name: res.obj.fileName,
          path: res.obj.path
        }
        this.dataForm.attachmentList.push(file)
        this.dataFormSubmit()
      } else {
        this.$message.error('上传失败')
      }
    },
    deleteHandle(name) {
      this.$confirm('确定删除该文件吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.dataForm.attachmentList = this.dataForm.attachmentList.filter(item => item.name !== name)
        this.dataFormSubmit()
      })
    },
    beforeUpload(file) {
      const isLt2M = file.size / 1024 / 1024 < 2
      if (!isLt2M) {
        this.$message.error('上传文件大小不能超过 2M!')
        return false
      }
    },
    getReportByProjectId() {
      this.$http({
        url: this.$http.adornUrl(`/admin/project/getProjectById`),
        method: 'get',
        params: this.$http.adornParams({id: this.dataForm.id})
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.dataForm.attachmentList = data.obj.attachmentList || []
        } else {
          this.$message.error(data.msg)
        }
      })
    },
    download(path, name) {
      this.$http({
        url: this.$http.adornUrl(path),
        method: 'get',
        responseType: 'arraybuffer',
        params: this.$http.adornParams()
      }).then(({data}) => {
        console.info('asdasdsad', data)
        if (data.code && data.code !== 0) {
          this.$message.error('下载失败')
        } else {
          let blob = new Blob([data], {type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document;charset=utf-8'})
          let objectUrl = URL.createObjectURL(blob)
          let a = document.createElement('a')
          // window.location.href = objectUrl
          a.href = objectUrl
          a.download = name
          a.click()
          URL.revokeObjectURL(objectUrl)
        }
      })
    },
    // 表单提交
    dataFormSubmit() {
          this.$http({
            url: this.$http.adornUrl(`/admin/project/importReport`),
            method: 'post',
            data: this.$http.adornData({
              'id': this.dataForm.id,
              'attachmentList': this.dataForm.attachmentList
            })
          }).then(({data}) => {
            if (data && data.code === 0) {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 1500,
                onClose: () => {
                  this.getReportByProjectId()
                  this.importVisible = false
                }
              })
            } else if (data && data.code === 303) {
              for (let it of data.obj) {
                this[`${it.field}Error`] = it.message
              }
            } else {
              this.$message.error(data.msg)
            }
          })
    },
    importDialog() {
      this.importVisible = true
    },
    // 下载模板文件
    downloadTemplate() {
      this.$http({
        url: this.$http.adornUrl(this.downloadPath),
        method: 'get',
        responseType: 'arraybuffer',
        params: this.$http.adornParams()
      }).then(({data}) => {
        if (data.code && data.code !== 0) {
          this.$message.error('下载失败')
        } else {
          let blob = new Blob([data], {type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document;charset=utf-8'})
          let objectUrl = URL.createObjectURL(blob)
          let a = document.createElement('a')
          // window.location.href = objectUrl
          a.href = objectUrl
          a.download = this.templateFileName
          a.click()
          URL.revokeObjectURL(objectUrl)
          this.$message({
            type: 'success',
            message: '下载模板成功'
          })
        }
      })
    },
  }
}
</script>
<style lang="scss" scoped>
</style>
