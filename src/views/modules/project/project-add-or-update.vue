<template>
  <el-dialog
      :title="!baseInfoForm.id ? '申报' : '修改'"
      :close-on-click-modal="false"
      width="80%"
      :visible.sync="visible">
    <el-row>
      <el-col :span="12">
        <el-row>
          <el-form :model="baseInfoForm" :rules="baseInfoRule" ref="baseInfoForm" label-width="130px">
            <el-col :span="24">
              <el-form-item label-width="10px">
                <div style="display: flex; justify-content: flex-start">
                  <div style="width: 5px;background-color: #00feff;margin-right: 10px; margin-top: 6px; margin-bottom: 6px"></div>
                  <div style="font-weight: bold">项目基本信息</div>
                </div>
              </el-form-item>
            </el-col>
              <el-col :span="24">
                  <el-form-item label="已申报的项目"   pop="originalProjectId">
                      <el-select filterable style="width: 100%" ref="originalProjectId" v-model="originalProjectId"
                                 :disabled="baseInfoForm.id!=null"
                                 @change="projectChange" clearable>
                          <el-option
                                  v-for="item in projectList"
                                  :key="item.id"
                                  :label="item.projectName"
                                  :value="item.id">
                          </el-option>
                      </el-select>
                      <span style="color: red" v-if="!originalProjectId">如往年已申报，可通过选择项目名称自动填充部分信息,如未申报可忽略该字段</span>
                  </el-form-item>
              </el-col>
            <el-col :span="24">
                <el-form-item label="申报单位" prop="applyUnitName">
                    <el-input v-model="baseInfoForm.applyUnitName" placeholder="申报单位"></el-input>
                </el-form-item>
            </el-col>
              <el-col :span="24">
              <el-form-item label="项目名称" prop="projectName">
                <el-input v-model="baseInfoForm.projectName" placeholder="项目名称"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="实施地点" prop="implementationLocation">
                <el-input v-model="baseInfoForm.implementationLocation" placeholder="实施地点"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="单位所属区域" prop="orgCode">
                <el-dict :code="'project_org_code'" v-model="baseInfoForm.orgCode"></el-dict>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="项目类型" prop="type">
                <el-checkbox-group v-model="baseInfoForm.type">
                  <el-checkbox v-for="it in typeList" :label="it.code" :key="it.code">{{ it.name }}</el-checkbox>
                </el-checkbox-group>
              </el-form-item>
            </el-col>
<!--            <el-col :span="24">-->
<!--              <el-form-item label="受益人类别" prop="beneficiaryCategory">-->
<!--                <el-checkbox-group v-model="baseInfoForm.beneficiaryCategory">-->
<!--                  <el-checkbox v-for="it in beneficiaryCategoryList" :label="it.code" :key="it.code">{{ it.name }}-->
<!--                  </el-checkbox>-->
<!--                </el-checkbox-group>-->
<!--              </el-form-item>-->
<!--            </el-col>-->
            <el-col :span="24">
              <el-form-item label="项目受益人" prop="projectBeneficiaries">
                <el-input type="textarea" :rows="3"  maxlength="100" show-word-limit  v-model="baseInfoForm.projectBeneficiaries" placeholder="项目受益人"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="项目需求类型" prop="projectDemandType">
                <el-radio-group :disabled="!isNGO" v-model="baseInfoForm.projectDemandType">
                  <el-radio :label="'project_materials'">物资</el-radio>
                  <el-radio :label="'project_capital'">资金</el-radio>
                </el-radio-group>
                <span v-if="!isNGO" style="color: red; float: right">非民政团队只能申报物资项目</span>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="项目预算资金(元)" prop="fundsRequired">
                <el-input-number v-model="baseInfoForm.fundsRequired" placeholder="项目预算资金"></el-input-number>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="项目年度" prop="year">
                <el-dict :code="'pro_year'" v-model="baseInfoForm.year"></el-dict>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="项目周期" prop="timeRange">
                <el-date-picker
                    style="width: 100%"
                    v-model="baseInfoForm.timeRange"
                    clearable
                    type="daterange"
                    value-format="yyyy-MM-dd"
                    align="right"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期">
                </el-date-picker>
              </el-form-item>
            </el-col>
              <el-col :span="24">
                  <el-form-item label="排序" prop="sequence">
                      <el-input-number v-model="baseInfoForm.sequence" placeholder="排序"/>
                  </el-form-item>
              </el-col>
              <el-col :span="24">
                  <el-form-item label="资金/物资使用计划" prop="moneyMaterialPlan">
                      <el-input v-model="baseInfoForm.moneyMaterialPlan" placeholder="资金/物资使用计划"
                                maxlength="255"  show-word-limit  type="textarea" :rows="3"></el-input>
                  </el-form-item>
              </el-col>
          </el-form>
        </el-row>
        <el-row>
          <el-form :model="contactInfoForm" :rules="contactInfoRule" ref="contactInfoForm" label-width="130px">
            <el-col :span="24">
              <el-form-item label-width="10px">
                <div style="display: flex; justify-content: flex-start">
                  <div style="width: 5px;background-color: #00feff;margin-right: 10px; margin-top: 6px; margin-bottom: 6px"></div>
                  <div style="font-weight: bold">项目联系人信息</div>
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="姓名" prop="contactName">
                <el-input v-model="contactInfoForm.contactName" placeholder="联系人姓名"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="电话" prop="contactPhone">
                <el-input v-model="contactInfoForm.contactPhone" placeholder="联系人电话"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="身份证号码" prop="contactIdCard">
                <el-input v-model="contactInfoForm.contactIdCard" placeholder="联系人身份证号码"></el-input>
              </el-form-item>
            </el-col>
          </el-form>
        </el-row>
        <el-row style="margin-bottom: 20px">
          <el-form :model="budgetInfoForm" ref="budgetInfoForm" label-width="0px">
            <el-col :span="24">
              <el-form-item label-width="10px">
                <div style="display: flex; justify-content: space-between">
                  <div style="display: flex; justify-content: flex-start">
                    <div style="width: 5px;background-color: #00feff;margin-right: 10px; margin-top: 6px; margin-bottom: 6px"></div>
                    <div style="font-weight: bold">项目预算明细</div>
                    <span style="margin-left: 10px">预算金额：{{baseInfoForm.fundsRequired}}</span>
                  </div>
                  <el-button @click="addBudgetDetail" type="text" size="	medium " icon="el-icon-circle-plus-outline">添加</el-button>
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-table
                  :data="budgetInfoForm.projectBudgetDetailList"
                  border
                  height="180"
                  pop="projectBudgetDetailList"
                  style="width: 100%">
                <el-table-column prop="name" label="名称" align="center">
                  <template slot-scope="scope">
                    <span v-show="!scope.row.editFlag">{{scope.row.name}}</span>
                    <el-input v-show="scope.row.editFlag"
                              v-model="scope.row.name">
                    </el-input>
                  </template>
                </el-table-column>
                <el-table-column
                    prop="amount"
                    header-align="center"
                    align="center"
                    label="金额(元)">
                  <template slot-scope="scope">
                    <span v-show="!scope.row.editFlag">{{scope.row.amount}}</span>
                    <el-input-number v-show="scope.row.editFlag"
                              v-model="scope.row.amount">
                    </el-input-number>
                  </template>
                </el-table-column>
                <el-table-column
                    prop="description"
                    header-align="center"
                    align="center"
                    label="说明">
                  <template slot-scope="scope">
                    <span v-show="!scope.row.editFlag">{{scope.row.description}}</span>
                    <el-input v-show="scope.row.editFlag"
                              v-model="scope.row.description">
                    </el-input>
                  </template>
                </el-table-column>
                <el-table-column
                    fixed="right"
                    header-align="center"
                    align="center"
                    width="120"
                    label="操作">
                  <template slot-scope="scope">
                    <el-button type="text" size="small" @click="deleteHandle(scope.$index,'budgetDetail')">删除</el-button>
                  </template>
                </el-table-column>
              </el-table>
            </el-col>
          </el-form>
        </el-row>
<!--        <el-row style="margin-bottom: 20px">-->
<!--          <el-form :model="actPlanForm" ref="actPlanForm" label-width="0px">-->
<!--            <el-col :span="24">-->
<!--              <el-form-item label-width="10px">-->
<!--                <div style="display: flex; justify-content: space-between">-->
<!--                  <div style="display: flex; justify-content: flex-start">-->
<!--                    <div style="width: 5px;background-color: #00feff;margin-right: 10px; margin-top: 6px; margin-bottom: 6px"></div>-->
<!--                    <div style="font-weight: bold">活动计划安排</div>-->
<!--                  </div>-->
<!--                  <el-button @click="addActivityPlanListDetail" type="text" size="	medium " icon="el-icon-circle-plus-outline">添加</el-button>-->
<!--                </div>-->
<!--              </el-form-item>-->
<!--            </el-col>-->
<!--            <el-col :span="24">-->
<!--              <el-table-->
<!--                  :data="actPlanForm.projectActivityPlanList"-->
<!--                  border-->
<!--                  height="180"-->
<!--                  style="width: 100%">-->
<!--                <el-table-column-->
<!--                    prop="projectName"-->
<!--                    header-align="center"-->
<!--                    align="center"-->
<!--                    label="活动开展时间">-->
<!--                  <template slot-scope="scope">-->
<!--                    <el-date-picker-->
<!--                        v-if="scope.row.editFlag"-->
<!--                        ref="gain"-->
<!--                        v-model="scope.row.activityTime"-->
<!--                        type="date"-->
<!--                        format="yyyy-MM-dd"-->
<!--                        value-format="yyyy-MM-dd"-->
<!--                        style="width: 100%"-->
<!--                    />-->
<!--                    <span v-else>{{ scope.row.activityTime }}</span>-->
<!--                  </template>-->
<!--                </el-table-column>-->
<!--                <el-table-column-->
<!--                    header-align="center"-->
<!--                    align="center"-->
<!--                    label="受益人数(人)">-->
<!--                  <template slot-scope="scope">-->
<!--                    <span v-show="!scope.row.editFlag">{{scope.row.beneficiaryNum}}</span>-->
<!--                    <el-input v-show="scope.row.editFlag"-->
<!--                              v-model="scope.row.beneficiaryNum">-->
<!--                    </el-input>-->
<!--                  </template>-->
<!--                </el-table-column>-->
<!--                <el-table-column-->
<!--                    header-align="center"-->
<!--                    align="center"-->
<!--                    label="参与人数(人)">-->
<!--                  <template slot-scope="scope">-->
<!--                    <span v-show="!scope.row.editFlag">{{scope.row.participantNum}}</span>-->
<!--                    <el-input v-show="scope.row.editFlag"-->
<!--                              v-model="scope.row.participantNum">-->
<!--                    </el-input>-->
<!--                  </template>-->
<!--                </el-table-column>-->
<!--                <el-table-column-->
<!--                    fixed="right"-->
<!--                    header-align="center"-->
<!--                    align="center"-->
<!--                    width="120"-->
<!--                    label="操作">-->
<!--                  <template slot-scope="scope">-->
<!--                    <el-button type="text" size="small" @click="deleteHandle(scope.$index,'activityPlan')">删除</el-button>-->
<!--                  </template>-->
<!--                </el-table-column>-->
<!--              </el-table>-->
<!--            </el-col>-->
<!--          </el-form>-->
<!--        </el-row>-->
<!--        <el-row style="margin-bottom: 20px">-->
<!--          <el-form :model="materialsUseForm" ref="materialsUseForm" label-width="0px">-->
<!--            <el-col :span="24">-->
<!--              <el-form-item label-width="10px">-->
<!--                <div style="display: flex; justify-content: space-between">-->
<!--                  <div style="display: flex; justify-content: flex-start">-->
<!--                    <div style="width: 5px;background-color: #00feff;margin-right: 10px; margin-top: 6px; margin-bottom: 6px"></div>-->
<!--                    <div style="font-weight: bold">资金/物资使用计划</div>-->
<!--                  </div>-->
<!--                  <el-button @click="addMoneyMaterialPlanListDetail" type="text" size="	medium " icon="el-icon-circle-plus-outline">添加</el-button>-->
<!--                </div>-->
<!--              </el-form-item>-->
<!--            </el-col>-->
<!--            <el-col :span="24">-->
<!--              <el-table-->
<!--                  :data="materialsUseForm.projectMoneyMaterialPlanList"-->
<!--                  border-->
<!--                  height="180"-->
<!--                  style="width: 100%">-->
<!--                <el-table-column-->
<!--                    prop="activityDate"-->
<!--                    header-align="center"-->
<!--                    align="center"-->
<!--                    label="活动日期">-->
<!--                  <template slot-scope="scope">-->
<!--                    <el-date-picker-->
<!--                        v-if="scope.row.editFlag"-->
<!--                        ref="gain"-->
<!--                        v-model="scope.row.activityDate"-->
<!--                        type="date"-->
<!--                        format="yyyy-MM-dd"-->
<!--                        value-format="yyyy-MM-dd"-->
<!--                        style="width: 100%"-->
<!--                    />-->
<!--                    <span v-else>{{ scope.row.activityDate }}</span>-->
<!--                  </template>-->
<!--                </el-table-column>-->
<!--                <el-table-column-->
<!--                    header-align="center"-->
<!--                    align="center"-->
<!--                    label="支出(元)">-->
<!--                  <template slot-scope="scope">-->
<!--                    <span v-show="!scope.row.editFlag">{{scope.row.expenses}}</span>-->
<!--                    <el-input v-show="scope.row.editFlag"-->
<!--                              v-model="scope.row.expenses">-->
<!--                    </el-input>-->
<!--                  </template>-->
<!--                </el-table-column>-->
<!--                <el-table-column-->
<!--                    header-align="center"-->
<!--                    align="center"-->
<!--                    label="说明">-->
<!--                  <template slot-scope="scope">-->
<!--                    <span v-show="!scope.row.editFlag">{{scope.row.description}}</span>-->
<!--                    <el-input v-show="scope.row.editFlag"-->
<!--                              v-model="scope.row.description">-->
<!--                    </el-input>-->
<!--                  </template>-->
<!--                </el-table-column>-->
<!--                <el-table-column-->
<!--                    fixed="right"-->
<!--                    header-align="center"-->
<!--                    align="center"-->
<!--                    width="120"-->
<!--                    label="操作">-->
<!--                  <template slot-scope="scope">-->
<!--                    <el-button type="text" size="small" @click="deleteHandle(scope.$index,'moneyMaterialPlan')">删除</el-button>-->
<!--                  </template>-->
<!--                </el-table-column>-->
<!--              </el-table>-->
<!--            </el-col>-->
<!--          </el-form>-->

<!--        </el-row>-->
      </el-col>

      <el-col :span="12">
        <el-form :model="detailInfoForm" :rules="detailInfoRule" ref="detailInfoForm" label-width="130px">
          <el-col :span="24">
            <el-form-item label-width="10px">
              <div style="display: flex; justify-content: flex-start">
                <div style="width: 5px;background-color: #00feff;margin-right: 10px; margin-top: 6px; margin-bottom: 6px"></div>
                <div style="font-weight: bold">项目详细信息</div>
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="项目概况" prop="projectSummary">
              <el-input  maxlength="300"  show-word-limit  type="textarea" :rows="3" v-model="detailInfoForm.projectSummary" placeholder="项目概况"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="项目背景" prop="projectBackground">
              <el-input   maxlength="300"  show-word-limit type="textarea" :rows="3" v-model="detailInfoForm.projectBackground" placeholder="项目背景"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="项目目标" prop="overallObjective">
              <el-input   maxlength="300"  show-word-limit type="textarea" :rows="3" v-model="detailInfoForm.overallObjective" placeholder="项目目标"></el-input>
            </el-form-item>
          </el-col>
<!--          <el-col :span="24">-->
<!--            <el-form-item label="具体目标" prop="specificObjective">-->
<!--              <el-input    maxlength="300"  show-word-limit  type="textarea" :rows="3" v-model="detailInfoForm.specificObjective" placeholder="具体目标"></el-input>-->
<!--            </el-form-item>-->
<!--          </el-col>-->
          <el-col :span="24">
            <el-form-item label="项目实施计划" prop="prePlanningService">
              <el-input    maxlength="300"  show-word-limit  type="textarea" :rows="3" v-model="detailInfoForm.prePlanningService" placeholder="简述项目实施主要内容、具体做法等，包括前期策划服务、中期实施计划、后期评估机制等"></el-input>
           <span style="color: red" v-if="!originalProjectId">简述项目实施主要内容、具体做法等，包括前期策划服务、中期实施计划、后期评估机制等</span>
            </el-form-item>
          </el-col>
<!--          <el-col :span="24">-->
<!--            <el-form-item label="中期实施执行" prop="midTermImplementation">-->
<!--              <el-input   maxlength="300"  show-word-limit type="textarea" :rows="3" v-model="detailInfoForm.midTermImplementation"-->
<!--                          placeholder="中期实施执行"></el-input>-->
<!--            </el-form-item>-->
<!--          </el-col>-->
<!--          <el-col :span="24">-->
<!--            <el-form-item label="后期评估机制" prop="postEvaluationMechanism">-->
<!--              <el-input   maxlength="300"  show-word-limit type="textarea" :rows="3" v-model="detailInfoForm.postEvaluationMechanism"-->
<!--                          placeholder="后期评估机制"></el-input>-->
<!--            </el-form-item>-->
<!--          </el-col>-->
<!--          <el-col :span="24">-->
<!--            <el-form-item label="与捐赠方的关联" prop="donorRelationship">-->
<!--              <el-input   maxlength="300"  show-word-limit type="textarea" :rows="3" v-model="detailInfoForm.donorRelationship" placeholder="与捐赠方的关联"></el-input>-->
<!--            </el-form-item>-->
<!--          </el-col>-->
<!--          <el-col :span="24">-->
<!--            <el-form-item label="项目反馈机制" prop="projectFeedbackMechanism">-->
<!--              <el-input type="textarea" :rows="3" v-model="detailInfoForm.projectFeedbackMechanism"-->
<!--                        maxlength="300"  show-word-limit  placeholder="项目反馈机制"></el-input>-->
<!--            </el-form-item>-->
<!--          </el-col>-->
          <el-col :span="24">
            <el-form-item label="项目预期成效" prop="projectExpectedEffect">
              <el-input type="textarea" :rows="3" v-model="detailInfoForm.projectExpectedEffect"
                        maxlength="300"   show-word-limit   placeholder="简述项目实施的主要成效以及志愿者和服务对象的收获或改变"></el-input>
                <span style="color: red" v-if="!originalProjectId">简述项目实施的主要成效以及志愿者和服务对象的收获或改变</span>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="项目宣传" prop="projectPromotion">
              <el-input    maxlength="300"  show-word-limit  type="textarea" :rows="3" v-model="detailInfoForm.projectPromotion" placeholder="项目宣传"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="风险预案" prop="riskPlan">
              <el-input   maxlength="300"  show-word-limit type="textarea" :rows="3" v-model="detailInfoForm.riskPlan" placeholder="风险预案"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="封面图片" prop="picture">
              <el-upload
                  class="avatar-uploader"
                  :action="this.$http.adornUrl(`/admin/oss/upload`)"
                  :headers="myHeaders"
                  :data="{serverCode: this.serverCode, media: false}"
                  :show-file-list="false"
                  :on-success="handleAvatarSuccess"
                  :before-upload="beforeAvatarUpload">
                <img v-if="detailInfoForm.picture" :src="$http.adornAttachmentUrl(detailInfoForm.picture)" class="avatar">
                <i v-else class="el-icon-plus avatar-uploader-icon"></i>
                <div slot="tip" class="el-upload__tip" style="color: red">尺寸建议600x1000像素，文件格式jpg、bmp或png，大小不超3M</div>
              </el-upload>
            </el-form-item>
          </el-col>
        </el-form>
      </el-col>

    </el-row>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit(false)" v-if="isDraftOrReject">保存草稿</el-button>
         <el-button type="primary" @click="dataFormSubmit(false)" v-if="!isDraftOrReject">保存</el-button>
      <el-button type="success" @click="dataFormSubmit(true)" v-if="isDraftOrReject||this.baseInfoForm.id==null">提交申报</el-button>
    </span>
  </el-dialog>
</template>

<script>
import moment from 'moment'
import Vue from 'vue'

export default {
  data() {
    return {
      visible: false,
      dataListLoading: false,
      dataListSelections: [],
      selectListRow: [],
      isNGO: false,
      teamId: this.$store.state.user.teamId,
      beneficiaryCategoryList: [],
      serverCode: 'LocalServer',
      myHeaders: {Authorization: Vue.cookie.get('Authorization')},
      uploadData: {serverCode: 'LocalServer', media: false},
      typeList: [],
      projectList: [],
      isDraftOrReject: false,
      originalProjectId: null,
      baseInfoForm: {
        id: null,
        version: null,
        projectName: '',
        implementationLocation: '',
        orgCode: '',
        orgName: '',
        type: [],
        beneficiaryCategory: [],
        projectBeneficiaries: '',
        projectDemandType: 'project_materials',
        fundsRequired: '',
        year: new Date().getFullYear().toString(),
        timeRange: [],
        startTime: '',
        endTime: '',
        auditStatus: '',
        sequence:0,
        applyUnitName:'',
        moneyMaterialPlan:''
      },
      contactInfoForm: {
        contactName: '',
        contactPhone: '',
        contactIdCard: '',
      },
      budgetInfoForm: {
        projectBudgetDetailList: [],
      },
      actPlanForm: {
        projectActivityPlanList: [],
      },
      materialsUseForm: {
        projectMoneyMaterialPlanList: []
      },
      detailInfoForm: {
        projectSummary: '',
        projectBackground: '',
        overallObjective: '',
        specificObjective: '',
        prePlanningService: '',
        midTermImplementation: '',
        postEvaluationMechanism: '',
        donorRelationship: '',
        projectFeedbackMechanism: '',
        projectExpectedEffect: '',
        projectPromotion: '',
        riskPlan: '',
        picture: ''
      },
      baseInfoRule: {
        implementationLocation: [
          {required: true, message: '实施地点不能为空', trigger: 'change'}
        ],
        applyUnitName: [
            {required: true, message: '申报单位不能为空', trigger: 'change'}
        ],
        projectName: [
          {required: true, message: '项目名称不能为空', trigger: 'change'}
        ],
        orgCode: [
          {required: true, message: '所属区域不能为空', trigger: 'change'}
        ],
        beneficiaryCategory: [
          {required: true, message: '受益人类别不能为空', trigger: 'change'}
        ],
        type: [
          {required: true, message: '项目类型不能为空', trigger: 'change'}
        ],
        projectBeneficiaries: [
          {required: true, message: '项目受益人不能为空', trigger: 'change'}
        ],
        projectDemandType: [
          {required: true, message: '项目需求类型不能为空', trigger: 'change'}
        ],
        fundsRequired: [
          {required: true, message: '项目预算自己不能为空', trigger: 'change'}
        ],
        year: [
          {required: true, message: '项目年度不能为空', trigger: 'change'}
        ],
        timeRange: [
          {required: true, message: '项目周期不能为空', trigger: 'change'}
        ],
        moneyMaterialPlan: [
              {required: true, message: '资金/物资使用计划不能为空', trigger: 'change'}
          ]
      },
      contactInfoRule: {
        contactName: [
          {required: true, message: '联系人姓名不能为空', trigger: 'change'}
        ],
        contactPhone: [
          {required: true, message: '联系人电话不能为空', trigger: 'change'}
        ],
        contactIdCard: [
          {required: true, message: '联系人身份证号码不能为空', trigger: 'change'}
        ]
      },
      detailInfoRule: {
        projectSummary: [
          {required: true, message: '项目概况不能为空', trigger: 'change'}
        ],
        projectBackground: [
          {required: true, message: '项目背景不能为空', trigger: 'change'}
        ],
        overallObjective: [
          {required: true, message: '总体目标不能为空', trigger: 'change'}
        ],
        specificObjective: [
          {required: true, message: '具体目标不能为空', trigger: 'change'}
        ],
        prePlanningService: [
          {required: true, message: '项目实施计划服务不能为空', trigger: 'change'}
        ],
        // midTermImplementation: [
        //   {required: true, message: '中期实施执行不能为空', trigger: 'change'}
        // ],
        // postEvaluationMechanism: [
        //   {required: true, message: '后期评估机制不能为空', trigger: 'change'}
        // ],
        // donorRelationship: [
        //   {required: true, message: '与捐赠方的关联不能为空', trigger: 'change'}
        // ],
        // projectFeedbackMechanism: [
        //   {required: true, message: '项目反馈机制不能为空', trigger: 'change'}
        // ],
        projectExpectedEffect: [
          {required: true, message: '项目预期成效不能为空', trigger: 'change'}
        ],
        // projectPromotion: [
        //   {required: true, message: '项目宣传不能为空', trigger: 'change'}
        // ],
        // riskPlan: [
        //   {required: true, message: '风险预案不能为空', trigger: 'change'}
        // ],
        picture: [
          {required: true, message: '图片不能为空', trigger: 'change'}
        ]
      }
    }
  },
  components: {
    moment
  },
  methods: {
    init(id) {
      this.baseInfoForm.id = id || null
      this.originalProjectId=null
      this.getTypeList()
      this.getProjectList()
      this.getTeam()
      this.getBeneficiaryCategoryList("beneficiary_category")
      this.visible = true
      this.$nextTick(() => {
        this.$refs['baseInfoForm']?.resetFields()
        this.$refs['contactInfoForm']?.resetFields()
        this.$refs['detailInfoForm']?.resetFields()
        this.$refs['budgetInfoForm']?.resetFields()
        this.budgetInfoForm.projectBudgetDetailList = []
        this.actPlanForm. projectActivityPlanList = []
        this.materialsUseForm.projectMoneyMaterialPlanList = []
        if (this.baseInfoForm.id) {
          this.$http({
            url: this.$http.adornUrl(`/admin/project/getProjectById`),
            method: 'get',
            params: this.$http.adornParams({id: this.baseInfoForm.id})
          }).then(({data}) => {
            if (data && data.code === 0) {
              this.baseInfoForm.id = data.obj.id
              this.baseInfoForm.version = data.obj.version
              this.baseInfoForm.projectName = data.obj.projectName
              this.baseInfoForm.implementationLocation = data.obj.implementationLocation
              this.baseInfoForm.orgCode = data.obj.orgCode
              this.baseInfoForm.orgName = data.obj.orgName
              this.baseInfoForm.type = data.obj.type
              this.baseInfoForm.beneficiaryCategory = data.obj.beneficiaryCategory
              this.baseInfoForm.projectBeneficiaries = data.obj.projectBeneficiaries
              this.baseInfoForm.projectDemandType = data.obj.projectDemandType
              this.baseInfoForm.fundsRequired = data.obj.fundsRequired
              this.baseInfoForm.year = data.obj.year
              this.baseInfoForm.timeRange = data.obj.timeRange
              this.baseInfoForm.startTime = data.obj.startTime
              this.baseInfoForm.endTime = data.obj.endTime
              this.baseInfoForm.auditStatus = data.obj.auditStatus
              this.baseInfoForm.applyUnitName = data.obj.applyUnitName
              this.baseInfoForm.moneyMaterialPlan = data.obj.moneyMaterialPlan
              if(data.obj.startTime && data.obj.endTime){
                  this.$set(this.baseInfoForm, 'timeRange', [data.obj.startTime, data.obj.endTime])
              }
              else {
                  this.$set(this.baseInfoForm, 'timeRange', [])
              }
              this.contactInfoForm.contactName = data.obj.contactName
              this.contactInfoForm.contactPhone = data.obj.contactPhone
              this.contactInfoForm.contactIdCard = data.obj.contactIdCard
              this.$set(this.budgetInfoForm, 'projectBudgetDetailList', data.obj.projectBudgetDetailList)
              this.$set(this.actPlanForm, 'projectActivityPlanList', data.obj.projectActivityPlanList)
              this.$set(this.materialsUseForm, 'projectMoneyMaterialPlanList', data.obj.projectMoneyMaterialPlanList)
              this.detailInfoForm.projectSummary = data.obj.projectSummary
              this.detailInfoForm.projectBackground = data.obj.projectBackground
              this.detailInfoForm.overallObjective = data.obj.overallObjective
              this.detailInfoForm.specificObjective = data.obj.specificObjective
              this.detailInfoForm.prePlanningService = data.obj.prePlanningService
              this.detailInfoForm.midTermImplementation = data.obj.midTermImplementation
              this.detailInfoForm.postEvaluationMechanism = data.obj.postEvaluationMechanism
              this.detailInfoForm.donorRelationship = data.obj.donorRelationship
              this.detailInfoForm.projectFeedbackMechanism = data.obj.projectFeedbackMechanism
              this.detailInfoForm.projectExpectedEffect = data.obj.projectExpectedEffect
              this.detailInfoForm.projectPromotion = data.obj.projectPromotion
              this.detailInfoForm.riskPlan = data.obj.riskPlan
              this.detailInfoForm.picture = data.obj.picture
              this.baseInfoForm.sequence = data.obj.sequence
              this.isDraftOrReject = this.baseInfoForm.auditStatus === ''||this.baseInfoForm.auditStatus === 'pro_draft' || this.baseInfoForm.auditStatus === 'pro_reject'
            }
          })
        }
      })
    },
    getTeam() {
      this.$http({
        url: this.$http.adornUrl(`/admin/zyz/team/getTeamById`),
        method: 'get',
        params: this.$http.adornParams({id: this.teamId})
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.isNGO = data.obj.civilRegistration
        }
      })
    },
      // 删除
    deleteHandle(index,type) {
      if (type === 'budgetDetail'){
        this.$confirm('确定删除该项目预算吗？', '提示', {
          type: 'warning'
        }).then(() => {
              this.budgetInfoForm.projectBudgetDetailList.splice(index, 1)
        })
      }
      if (type === 'activityPlan'){
        this.$confirm('确定删除该活动计划安排吗？', '提示', {
          type: 'warning'
        }).then(() => {
              this.actPlanForm.projectActivityPlanList.splice(index,1)
        })
      }
      if (type === 'moneyMaterialPlan'){
        this.$confirm('确定删除该使用计划吗？', '提示', {
          type: 'warning'
        }).then(() => {
              this.materialsUseForm.projectMoneyMaterialPlanList.splice(index,1)
        })
      }
    },
    addBudgetDetail() {
      this.budgetInfoForm.projectBudgetDetailList.push({
        'rowNum': '',
        'name': '',
        'amount': '',
        'description': '',
        'editFlag': true,  // 可编辑标识
      })
    },
    addActivityPlanListDetail() {
      this.actPlanForm.projectActivityPlanList.push({
        'activityTime': '',
        'beneficiaryNum': '',
        'participantNum': '',
        'editFlag': true,  // 可编辑标识
      })
    },
    addMoneyMaterialPlanListDetail() {
      this.materialsUseForm.projectMoneyMaterialPlanList.push({
        'activityDate': '',
        'expenses': '',
        'description': '',
        'editFlag': true  // 可编辑标识
      })
    },
    //获取受益人类别
    getBeneficiaryCategoryList(code) {
      this.$http({
        url: this.$http.adornUrl(`/admin/dict/parent`),
        method: 'get',
        params: this.$http.adornParams({code: code})
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.beneficiaryCategoryList = data.obj || []
        }
      })
    },
    //获取受益人类别
    getTypeList() {
      this.$http({
        url: this.$http.adornUrl(`/admin/project/type/getTypeList`),
        method: 'get',
        params: this.$http.adornParams()
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.typeList = data.obj || []
        }
      })
    },
    handleAvatarSuccess(res, file) {
      if (res.success) {
        this.$message({
          message: '操作成功',
          type: 'success',
          duration: 1500
        })
        this.detailInfoForm.picture = res.obj.path
      } else {
        this.$message.error('上传失败')
      }
    },
    beforeAvatarUpload: function (file) {
      let isAccept = ['image/jpg', 'image/jpeg', 'image/png', 'image/bmp'].indexOf(file.type) !== -1
      let isLt3M = file.size / 1024 / 1024 < 3

      if (!isAccept) {
        this.$message.error('上传图片只能是图片!')
      }
      if (!isLt3M) {NP
        this.$message.error('上传文件大小不能超过 3MB!')
      }
      return isAccept && isLt3M
    },
    dataFormSubmit(isRender) {
      this.baseInfoForm.startTime = this.baseInfoForm.timeRange && this.baseInfoForm.timeRange[0]
      this.baseInfoForm.endTime = this.baseInfoForm.timeRange && this.baseInfoForm.timeRange[1]
      let method = ''
      if (isRender) {
        method = `/admin/project/${!this.baseInfoForm.id ? 'renderByProject ' : 'renderByUpdateProject'}`
      } else {
        method = `/admin/project/${!this.baseInfoForm.id ? 'saveProject' : 'updateProject'}`
      }
      if (isRender) {
        this.$confirm(`确定进行提交操作?`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
          closeOnClickModal: false
        }).then(() => {
          this.submitFun(method, 'refreshDataList', false)
        })
      } else {
        this.submitFun(method, 'refreshDataList', true)
      }
    },
    submitFun(method, callback, draft) {
      let validate = false
      this.$refs['baseInfoForm'].validate((valid) => {
        validate = valid
          if (validate ) {
              if (!draft) {
                  this.$refs['contactInfoForm'].validate((valid) => {
                      validate = valid
                      this.$refs['detailInfoForm'].validate((valid) => {
                          validate = valid
                      })
                  })
              }
          }

      })
      if (validate ) {
          // if(!this.baseInfoForm.id) {
          //     if(!this.budgetInfoForm.projectBudgetDetailList.length){
          //         this.$message.error('项目预算明细必填')
          //         return
          //     }
          // }

        this.isDisabled = true
        const dataForm = {...this.baseInfoForm, ...this.contactInfoForm, ...this.budgetInfoForm, ...this.actPlanForm, ...this.materialsUseForm, ...this.detailInfoForm}
        this.$http({
          url: this.$http.adornUrl(method),
          method: 'post',
          data: this.$http.adornData(dataForm)
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.$message({
              message: '操作成功',
              type: 'success',
              duration: 1500,
              onClose: () => {
                this.visible = false
                this.isDisabled = false
                this.$emit(callback)
              }
            })
          } else {
            this.$message.error(data.msg)
            this.isDisabled = false
          }
        })
      }
    },
    getProjectList() {
        this.$http({
            url: this.$http.adornUrl(`/admin/project/getTeamProjectList`),
            method: 'get',
            params: this.$http.adornParams()
        }).then(({data}) => {
            if (data && data.code === 0) {
                this.projectList = data.obj || []
            }
        })
    },
    projectChange(value){
        if (value){
            this.$confirm(`修改已申报的项目将会把所选项目的信息覆盖当前页面的已填写信息，是否继续?`, '提示', {
                confirmButtonText: '继续',
                cancelButtonText: '取消',
                type: 'warning',
                closeOnClickModal: false
            }).then(() => {
                this.$http({
                    url: this.$http.adornUrl(`/admin/project/getProjectById`),
                    method: 'get',
                    params: this.$http.adornParams({id: value})
                }).then(({data}) => {
                    if (data && data.code === 0) {
                        this.baseInfoForm.projectName = data.obj.projectName
                        this.baseInfoForm.implementationLocation = data.obj.implementationLocation
                        this.baseInfoForm.orgCode = data.obj.orgCode
                        this.baseInfoForm.orgName = data.obj.orgName
                        this.baseInfoForm.beneficiaryCategory = data.obj.beneficiaryCategory
                        this.baseInfoForm.projectBeneficiaries = data.obj.projectBeneficiaries
                        this.baseInfoForm.projectDemandType = data.obj.projectDemandType
                        this.baseInfoForm.fundsRequired = data.obj.fundsRequired
                        this.baseInfoForm.applyUnitName = data.obj.applyUnitName
                        this.baseInfoForm.moneyMaterialPlan = data.obj.moneyMaterialPlan
                        this.contactInfoForm.contactName = data.obj.contactName
                        this.contactInfoForm.contactPhone = data.obj.contactPhone
                        this.contactInfoForm.contactIdCard = data.obj.contactIdCard
                        this.$set(this.budgetInfoForm, 'projectBudgetDetailList', data.obj.projectBudgetDetailList)
                        this.detailInfoForm.projectSummary = data.obj.projectSummary
                        this.detailInfoForm.projectBackground = data.obj.projectBackground
                        this.detailInfoForm.overallObjective = data.obj.overallObjective
                        this.detailInfoForm.prePlanningService = data.obj.prePlanningService
                        this.detailInfoForm.projectExpectedEffect = data.obj.projectExpectedEffect
                        this.detailInfoForm.projectPromotion = data.obj.projectPromotion
                        this.detailInfoForm.riskPlan = data.obj.riskPlan
                        this.detailInfoForm.picture = data.obj.picture
                    }
                })

            }).catch(() => {

            }).finally(() => {
                this.$refs.originalProjectId.blur();
            })
        }
    }
  }
}
</script>
