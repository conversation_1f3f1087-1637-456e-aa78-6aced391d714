<template>
  <el-dialog
      :title="this.isDetail ? '完成详情 ' : '对接审核'"
      :close-on-click-modal="false"
      width="80%"
      :visible.sync="visible">
    <el-tabs v-model="activeName" stretch>
      <el-tab-pane label="对接记录" name="four">
        <div style="display: flex;">
          <div style="width: 5px;height: 15px;background-color: #00feff;margin-right: 10px"></div>
          <div style="margin-bottom: 30px;font-weight: bold">对接记录</div>
        </div>
        <el-table
            :data="dataList"
            border
            v-loading="dataListLoading"
            style="width: 100%;">
          <el-table-column
              prop="corpName"
              header-align="center"
              align="center"
              label="企业名称">
          </el-table-column>
          <el-table-column
              prop="socialCreditCode"
              header-align="center"
              align="center"
              label="统一社会信用代码">
          </el-table-column>
          <el-table-column
              prop="corpLinkMan"
              header-align="center"
              align="center"
              label="联系人">
          </el-table-column>
          <el-table-column
              prop="corpLinkPhone"
              header-align="center"
              align="center"
              label="联系电话">
          </el-table-column>
          <el-table-column
              prop="projectDemandTypeText"
              header-align="center"
              align="center"
              label="对接类型">
          </el-table-column>
          <el-table-column
              prop="amount"
              header-align="center"
              align="center"
              label="对接金额(元)">
          </el-table-column>
          <el-table-column
              prop="dockingTime"
              header-align="center"
              align="center"
              label="对接时间">
          </el-table-column>
        </el-table>
          <div style="height:20px">  </div>
          <div style="display: flex;justify-content: center" v-if="!isDetail">
              <el-button style="width: 150px" round type="primary" @click="dockingAudit(true)">通过</el-button>
              <div style="margin-left: 100px">
                  <el-button style="width: 150px" round type="warning"  @click="dockingAudit(false)">驳回</el-button></div>
          </div>
      </el-tab-pane>
    </el-tabs>
  </el-dialog>
</template>

<script>

import _ from 'lodash'

export default {
  data() {
    return {
      visible: false,
      isDetail: false,
      activeName: '',
      dataListLoading: false,
      dataList: [],
      logList: [],
      fileList: [],
      activityList: [],
      newList: [],
      attachmentList: [],
      typeList: [],
      projectId: '',
      dataForm: {},
      initForm: {
        id: null,
        version: null,
        projectName: '',
        startTime: '',
        endTime: '',
        fundsReceived: '',
        fundsSpent: '',
        participantsNum: '',
        beneficiariesNum: '',
        trainedVolunteers: '',
        fullTimeFinance: '',
        projectSummaryFinish: '',
        volunteerPlatformUsage: '',
        projectOutcomes: '',
        mediaCoverage: '',
        accolades: '',
        pictureFinish: ''
      },
    }
  },
  components: {},
  methods: {
    init(id,isDetail) {
      this.dataForm = _.cloneDeep(this.initForm)
      this.isDetail = isDetail
      this.fileList = []
      this.activeName = 'four'
      this.projectId = id || null
      this.dataForm.id = id || null
      console.log(this.dataForm.projectId)
      this.visible = true
      this.getReportByProjectId()
      this.getDataList()
      this.getFinish()
      this.getLogList()
      this.getProjectNewList()
      this.getProjectActivityList()
    },
    getReportByProjectId() {
      this.$http({
        url: this.$http.adornUrl(`/admin/project/getProjectById`),
        method: 'get',
        params: this.$http.adornParams({id: this.projectId})
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.attachmentList = data.obj.attachmentList || []
        } else {
          this.$message.error(data.msg)
        }
      })
    },
    getLogList () {
      this.$http({
        url: this.$http.adornUrl(`/admin/project/audit/log/getLogListByProjectId`),
        method: 'get',
        params: this.$http.adornParams({
          'projectId': this.projectId,
          'auditType': 'project_finish'
        })
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.logList = data.obj
        }
      })
    },
    getProjectActivityList() {
      this.$http({
        url: this.$http.adornUrl(`/admin/zyz/activity/listForProjectActivity`),
        method: 'get',
        params: this.$http.adornParams(
            {
              'projectId': this.projectId
            }
        )
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.activityList = data.obj || []
        }
      })
    },
    getProjectNewList() {
      this.$http({
        url: this.$http.adornUrl(`/admin/portal_website/news/getNewsByProjectId`),
        method: 'get',
        params: this.$http.adornParams(
            {
              'projectId':this.projectId
            }
        )
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.newList = data.obj || []
        }
      })
    },
      dockingAudit(status) {
      this.$confirm(`确定${status ? '审核通过' : '驳回'}` + (this.dataForm.projectName ? '"' + this.dataForm.projectName  + '"' : '') + '项目对接审核？', '系统提示', {
        customClass: 'audit_pass_msg_box',
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        showInput: !status,
        inputPlaceholder: '请输入驳回意见……',
        inputType: 'textarea',
        closeOnClickModal: false
      }).then((val) => {
        this.$http({
          url: this.$http.adornUrl('/admin/project/dockingAudit'),
          method: 'get',
          params: this.$http.adornParams({
            'id': this.projectId,
              'status': status,
            'remark': val.value
          })
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.$message.success(status ? '审核通过成功！' : '驳回成功！')
            this.getLogList()
            this.visible = false
            this.$emit('refreshDataList')
          } else {
            this.$message.error(status ? '审核通过失败！' : '驳回失败！' + data.msg)
          }
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '操作取消'
        });
      });
      setTimeout(() => {
        var content = document.getElementsByClassName('audit_pass_msg_box')[0].childNodes[1]
        content.style.paddingLeft = '60px'
        content.style.paddingRight = '60px'
        var submitBtn = document.getElementsByClassName('audit_pass_msg_box')[0].childNodes[2].childNodes[1]
        submitBtn.style.backgroundColor = '#F56C6C'
        submitBtn.style.borderColor = '#F56C6C'
      }, 50)
    },
    getFinish(){
      this.$http({
        url: this.$http.adornUrl(`/admin/project`),
        method: 'get',
        params: this.$http.adornParams({id: this.projectId})
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.dataForm = data.obj
          let picture = this.dataForm.pictureFinish.split(',')
          this.pictureList = picture
          picture.forEach(item => {
            let obj = {
              url: this.$http.adornAttachmentUrl(item)
            }
            this.fileList.push(obj)
          })
        }
      })
    },
    // 获取数据列表
    getDataList() {
      this.dataListLoading = true
      this.$http({
        url: this.$http.adornUrl('/admin/project/docking/getListByProjectId'),
        method: 'get',
        params: this.$http.adornParams({
          'projectId': this.projectId
        })
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.dataList = data.obj
        } else {
          this.dataList = []
        }
        this.dataListLoading = false
      })
    },
    download(path, name) {
      this.$http({
        url: this.$http.adornUrl(path),
        method: 'get',
        responseType: 'arraybuffer',
        params: this.$http.adornParams()
      }).then(({data}) => {
        console.info('asdasdsad', data)
        if (data.code && data.code !== 0) {
          this.$message.error('下载失败')
        } else {
          let blob = new Blob([data], {type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document;charset=utf-8'})
          let objectUrl = URL.createObjectURL(blob)
          let a = document.createElement('a')
          // window.location.href = objectUrl
          a.href = objectUrl
          a.download = name
          a.click()
          URL.revokeObjectURL(objectUrl)
        }
      })
    },
  }
}
</script>
