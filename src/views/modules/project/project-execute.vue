<template>
  <el-dialog
      title="执行"
      :close-on-click-modal="false"
      :visible.sync="visible"
      width="80%">
    <el-form :model="dataForm" ref="dataForm" :rules="dataRule" label-width="0px">
      <el-row>
        <el-col :span="24">
          <el-form-item>
            <div style="display: flex; justify-content: flex-start">
              <div style="width: 5px;background-color: #00feff;margin-right: 10px; margin-top: 6px; margin-bottom: 6px"></div>
              <div style="font-weight: bold">活动信息</div>
            </div>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-transfer
              class="project_execute_transfer"
              style="display: flex; justify-content: space-around"
              filterable
              :titles="['所有活动', '已关联活动']"
              filter-placeholder="请输入关键字"
              v-model="dataForm.activityIds"
              :data="dataTeamActivity">
          </el-transfer>
        </el-col>
      </el-row>
      <el-row style="margin-top: 20px">
        <el-col :span="24">
          <el-transfer
              class="project_execute_transfer"
              style="display: flex; justify-content: space-around"
              filterable
              :titles="['所有新闻', '已关联新闻']"
              filter-placeholder="请输入关键字"
              v-model="dataForm.newIds"
              :data="dataNew">
          </el-transfer>
        </el-col>
      </el-row>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import Vue from 'vue'

export default {
  data() {
    return {
      visible: false,
      teamActivityList: [],
      dataTeamActivity: [],
      dataNew: [],
      dataForm: {
        projectId: '',
        activityIds: [],
        newIds: []
      },
      dataRule: {},
    }
  },
  methods: {
    init(projectId) {
      this.visible = true
      this.teamActivityList = []
      this.dataTeamActivity = []
      this.dataNew = []
      this.dataForm.newIds = []
      this.dataForm.activityIds = []
      this.dataForm.projectId = projectId || null
      this.getProjectActivityList()
      this.getTeamActivityList()
      this.getNewList()
      this.getProjectNewList()
    },
    getTeamActivityList() {
      this.$http({
        url: this.$http.adornUrl(`/admin/zyz/activity/listForTeamActivity`),
        method: 'get',
        params: this.$http.adornParams({
              'projectId': this.dataForm.projectId
            }
        )
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.teamActivityList = data.obj || []
          this.teamActivityList.forEach((item) => {
            this.dataTeamActivity.push({
              label: item.name,
              key: item.id
            })
          })
        }
      })
    },
    getProjectActivityList() {
      this.$http({
        url: this.$http.adornUrl(`/admin/zyz/activity/listForProjectActivity`),
        method: 'get',
        params: this.$http.adornParams(
            {
              'projectId': this.dataForm.projectId
            }
        )
      }).then(({data}) => {
        if (data && data.code === 0) {
          let activityList = data.obj || []
          activityList.forEach((item) => {
            this.dataForm.activityIds.push(item.id)
          })
        }
      })
    },
    getProjectNewList() {
      this.$http({
        url: this.$http.adornUrl(`/admin/portal_website/news/getNewsByProjectId`),
        method: 'get',
        params: this.$http.adornParams(
            {
              'projectId': this.dataForm.projectId
            }
        )
      }).then(({data}) => {
        if (data && data.code === 0) {
          let newList = data.obj || []
          newList.forEach((item) => {
            this.dataForm.newIds.push(item.id)
          })
        }
      })
    },
    getNewList() {
      this.$http({
        url: this.$http.adornUrl(`/admin/portal_website/news/getNewsByTeam`),
        method: 'get',
        params: this.$http.adornParams(
            {
              'projectId': this.dataForm.projectId
            }
        )
      }).then(({data}) => {
        if (data && data.code === 0) {
          let newsList = data.obj || []
          newsList.forEach((item) => {
            this.dataNew.push({
              label: item.title,
              key: item.id
            })
          })

        }
      })
    },
    // 表单提交
    dataFormSubmit() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.$http({
            url: this.$http.adornUrl(`/admin/project/execute`),
            method: 'post',
            data: this.$http.adornData(this.dataForm)
          }).then(({data}) => {
            if (data && data.code === 0) {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 1500,
                onClose: () => {
                  this.visible = false
                  this.$emit('refreshDataList')
                }
              })
            } else if (data && data.code === 303) {
              for (let it of data.obj) {
                this[`${it.field}Error`] = it.message
              }
            } else {
              this.$message.error(data.msg)
            }
          })
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.project_execute_transfer ::v-deep .el-transfer__buttons {
  margin-top: 100px !important;
}
::v-deep .el-transfer-panel  {
  width: 500px;
}
</style>
