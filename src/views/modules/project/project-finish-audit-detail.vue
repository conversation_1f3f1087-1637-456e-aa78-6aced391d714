<template>
  <el-dialog
      :title="this.isDetail ? '完成详情 ' : '结项审核'"
      :close-on-click-modal="false"
      width="80%"
      :visible.sync="visible">
    <el-tabs v-model="activeName" stretch>
      <el-tab-pane label="总结" name="four">
        <div style="display: flex;">
          <div style="width: 5px;height: 15px;background-color: #00feff;margin-right: 10px"></div>
          <div style="margin-bottom: 30px;font-weight: bold">项目信息</div>
        </div>
        <el-form :model="dataForm"  ref="dataForm" label-width="160px">
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="项目名称" prop="projectName">
                <el-input readonly v-model="dataForm.projectName" placeholder="项目名称"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="开始时间" prop="startTime">
                <el-input readonly v-model="dataForm.startTime" placeholder="开始时间"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="结束时间" prop="endTime">
                <el-input readonly v-model="dataForm.endTime" placeholder="结束时间"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="收到资金（元）" prop="fundsReceived">
                <el-input readonly v-model="dataForm.fundsReceived" placeholder="收到资金"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="支出资金（元）" prop="fundsSpent" >
                <el-input readonly v-model="dataForm.fundsSpent" placeholder="支出资金"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="16">
            <el-col :span="12">
              <el-form-item label="参与人数" prop="participantsNum">
                <el-input-number :disabled="true" v-model="dataForm.participantsNum" controls-position="right"
                                 :min="0"></el-input-number>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="惠及人数" prop="beneficiariesNum">
                <el-input-number :disabled="true" v-model="dataForm.beneficiariesNum" controls-position="right"
                                 :min="0"></el-input-number>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="是否培训志愿者" prop="trainedVolunteers">
                <el-radio-group v-model="dataForm.trainedVolunteers" :disabled="true">
                  <el-radio :label="true">是</el-radio>
                  <el-radio :label="false">否</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="是否有专职财务" prop="fullTimeFinance">
                <el-radio-group v-model="dataForm.fullTimeFinance" :disabled="true">
                  <el-radio :label="true">是</el-radio>
                  <el-radio :label="false">否</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="项目概述及主要事绩" prop="projectSummaryFinish">
                <el-input readonly type="textarea" :rows="3" v-model="dataForm.projectSummaryFinish"
                          placeholder="项目概述及主要事绩"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="志愿者平台使用情况" prop="volunteerPlatformUsage">
                <el-input  readonly type="textarea" :rows="3" v-model="dataForm.volunteerPlatformUsage"
                           placeholder="志愿者平台使用情况"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="项目成效分析" prop="projectOutcomes">
                <el-input readonly type="textarea" :rows="3" v-model="dataForm.projectOutcomes" placeholder="项目成效分析"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="媒体宣传情况" prop="mediaCoverage">
                <el-input readonly type="textarea" :rows="3" v-model="dataForm.mediaCoverage" placeholder="媒体宣传情况"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="受表彰情况" prop="accolades">
                <el-input readonly type="textarea" :rows="3" v-model="dataForm.accolades" placeholder="受表彰情况"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="图片" prop="pictureFinish">
                <el-upload
                    :disabled = "true"
                    list-type="picture-card"
                    :action="this.$http.adornUrl(`/admin/oss/upload`)"
                    :show-file-list="true"
                    :file-list="fileList"
                >
                  <i class="el-icon-plus"></i>
                </el-upload>
              </el-form-item>
            </el-col>
          </el-row>
          <div style="display: flex;justify-content: center" v-if="!isDetail">
            <el-button style="width: 150px" round type="primary" @click="finishAudit(true)">通过</el-button>
            <div style="margin-left: 100px">
              <el-button style="width: 150px" round type="warning"  @click="finishAudit(false)">驳回</el-button></div>
          </div>
        </el-form>
        <div style="display: flex">
          <div style="width: 5px;height: 15px;background-color: #00feff;margin-right: 10px"></div>
          <div style="margin-bottom: 30px;font-weight: bold">审核记录</div>
        </div>
        <div>
          <el-table
              :data="logList"
              border
              v-loading="dataListLoading"
              style="width: 100%;">
            <el-table-column
                prop="operateTime"
                header-align="center"
                align="center"
                label="操作时间">
            </el-table-column>
            <el-table-column
                prop="operatorName"
                header-align="center"
                align="center"
                label="操作人">
            </el-table-column>
            <el-table-column
                prop="operateType"
                header-align="center"
                align="center"
                label="操作">
            </el-table-column>
            <el-table-column
                prop="remark"
                header-align="center"
                align="center"
                label="驳回理由">
            </el-table-column>
          </el-table>
        </div>
      </el-tab-pane>
      <el-tab-pane label="对接记录" name="first">
        <div style="display: flex;">
          <div style="width: 5px;height: 15px;background-color: #00feff;margin-right: 10px"></div>
          <div style="margin-bottom: 30px;font-weight: bold">对接记录</div>
        </div>
        <el-table
            :data="dataList"
            border
            v-loading="dataListLoading"
            style="width: 100%;">
          <el-table-column
              prop="corpName"
              header-align="center"
              align="center"
              label="企业名称">
          </el-table-column>
          <el-table-column
              prop="socialCreditCode"
              header-align="center"
              align="center"
              label="统一社会信用代码">
          </el-table-column>
          <el-table-column
              prop="corpLinkMan"
              header-align="center"
              align="center"
              label="联系人">
          </el-table-column>
          <el-table-column
              prop="corpLinkPhone"
              header-align="center"
              align="center"
              label="联系电话">
          </el-table-column>
          <el-table-column
              prop="projectDemandTypeText"
              header-align="center"
              align="center"
              label="对接类型">
          </el-table-column>
          <el-table-column
              prop="amount"
              header-align="center"
              align="center"
              label="对接金额(元)">
          </el-table-column>
          <el-table-column
              prop="dockingTime"
              header-align="center"
              align="center"
              label="对接时间">
          </el-table-column>
        </el-table>
      </el-tab-pane>
      <el-tab-pane label="执行记录" name="second">
        <div style="display: flex;">
          <div style="width: 5px;height: 15px;background-color: #00feff;margin-right: 10px"></div>
          <div style="margin-bottom: 30px;font-weight: bold">项目执行</div>
        </div>
        <div style="display: flex; justify-content: space-around">
          <div style="margin-right: 200px">
            <el-table
                border
                :data="activityList"
                style="width: 100%">
              <el-table-column
                  prop="name"
                  align="center"
                  label="已关联活动"
                  width="300">
              </el-table-column>
              <el-table-column
                  prop="startTime"
                  label="日期"
                  align="center"
                  width="200">
              </el-table-column>
            </el-table>
          </div>
          <div>
            <el-table
                border
                :data="newList"
                style="width: 100%">
              <el-table-column
                  prop="title"
                  label="已关联新闻"
                  align="center"
                  width="300">
              </el-table-column>
              <el-table-column
                  prop="createDate"
                  label="日期"
                  align="center"
                  width="200">
              </el-table-column>
            </el-table>
          </div>
        </div>
      </el-tab-pane>
      <el-tab-pane label="月报记录" name="third">
        <div style="display: flex;">
          <div style="width: 5px;height: 15px;background-color: #00feff;margin-right: 10px"></div>
          <div style="margin-bottom: 30px;font-weight: bold">双月简报</div>
        </div>
        <el-table
            :data="this.attachmentList"
            border
            style="width: 100%;">
          <el-table-column
              prop="name"
              header-align="center"
              align="center"
              label="名称">
          </el-table-column>
          <el-table-column
              prop="uploadTime"
              header-align="center"
              align="center"
              label="提交时间">
          </el-table-column>
          <el-table-column
              fixed="right"
              header-align="center"
              align="center"
              width="150"
              label="操作">
            <template slot-scope="scope">
              <el-button type="text" size="small" @click="download(scope.row.path,scope.row.name)">下载</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-tab-pane>
    </el-tabs>
  </el-dialog>
</template>

<script>

import _ from 'lodash'

export default {
  data() {
    return {
      visible: false,
      isDetail: false,
      activeName: '',
      dataListLoading: false,
      dataList: [],
      logList: [],
      fileList: [],
      activityList: [],
      newList: [],
      attachmentList: [],
      typeList: [],
      projectId: '',
      dataForm: {},
      initForm: {
        id: null,
        version: null,
        projectName: '',
        startTime: '',
        endTime: '',
        fundsReceived: '',
        fundsSpent: '',
        participantsNum: '',
        beneficiariesNum: '',
        trainedVolunteers: '',
        fullTimeFinance: '',
        projectSummaryFinish: '',
        volunteerPlatformUsage: '',
        projectOutcomes: '',
        mediaCoverage: '',
        accolades: '',
        pictureFinish: ''
      },
    }
  },
  components: {},
  methods: {
    init(id,isDetail) {
      this.dataForm = _.cloneDeep(this.initForm)
      this.isDetail = isDetail
      this.fileList = []
      this.activeName = 'four'
      this.projectId = id || null
      this.dataForm.id = id || null
      console.log(this.dataForm.projectId)
      this.visible = true
      this.getReportByProjectId()
      this.getDataList()
      this.getFinish()
      this.getLogList()
      this.getProjectNewList()
      this.getProjectActivityList()
    },
    getReportByProjectId() {
      this.$http({
        url: this.$http.adornUrl(`/admin/project/getProjectById`),
        method: 'get',
        params: this.$http.adornParams({id: this.projectId})
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.attachmentList = data.obj.attachmentList || []
        } else {
          this.$message.error(data.msg)
        }
      })
    },
    getLogList () {
      this.$http({
        url: this.$http.adornUrl(`/admin/project/audit/log/getLogListByProjectId`),
        method: 'get',
        params: this.$http.adornParams({
          'projectId': this.projectId,
          'auditType': 'project_finish'
        })
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.logList = data.obj
        }
      })
    },
    getProjectActivityList() {
      this.$http({
        url: this.$http.adornUrl(`/admin/zyz/activity/listForProjectActivity`),
        method: 'get',
        params: this.$http.adornParams(
            {
              'projectId': this.projectId
            }
        )
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.activityList = data.obj || []
        }
      })
    },
    getProjectNewList() {
      this.$http({
        url: this.$http.adornUrl(`/admin/portal_website/news/getNewsByProjectId`),
        method: 'get',
        params: this.$http.adornParams(
            {
              'projectId':this.projectId
            }
        )
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.newList = data.obj || []
        }
      })
    },
    finishAudit(status) {
      this.$confirm(`确定${status ? '审核通过' : '驳回'}` + (this.dataForm.projectName ? '"' + this.dataForm.projectName  + '"' : '') + '项目结项审核？', '系统提示', {
        customClass: 'audit_pass_msg_box',
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        showInput: !status,
        inputPlaceholder: '请输入驳回意见……',
        inputType: 'textarea',
        closeOnClickModal: false
      }).then((val) => {
        this.$http({
          url: this.$http.adornUrl('/admin/project/finishAudit'),
          method: 'get',
          params: this.$http.adornParams({
            'id': this.projectId,
            'status': status,
            'remark': val.value
          })
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.$message.success(status ? '审核通过成功！' : '驳回成功！')
            this.getLogList()
            this.visible = false
            this.$emit('refreshDataList')
          } else {
            this.$message.error(status ? '审核通过失败！' : '驳回失败！' + data.msg)
          }
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '操作取消'
        });
      });
      setTimeout(() => {
        var content = document.getElementsByClassName('audit_pass_msg_box')[0].childNodes[1]
        content.style.paddingLeft = '60px'
        content.style.paddingRight = '60px'
        var submitBtn = document.getElementsByClassName('audit_pass_msg_box')[0].childNodes[2].childNodes[1]
        submitBtn.style.backgroundColor = '#F56C6C'
        submitBtn.style.borderColor = '#F56C6C'
      }, 50)
    },
    getFinish(){
      this.$http({
        url: this.$http.adornUrl(`/admin/project`),
        method: 'get',
        params: this.$http.adornParams({id: this.projectId})
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.dataForm = data.obj
          let picture = this.dataForm.pictureFinish.split(',')
          this.pictureList = picture
          picture.forEach(item => {
            let obj = {
              url: this.$http.adornAttachmentUrl(item)
            }
            this.fileList.push(obj)
          })
        }
      })
    },
    // 获取数据列表
    getDataList() {
      this.dataListLoading = true
      this.$http({
        url: this.$http.adornUrl('/admin/project/docking/getListByProjectId'),
        method: 'get',
        params: this.$http.adornParams({
          'projectId': this.projectId
        })
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.dataList = data.obj
        } else {
          this.dataList = []
        }
        this.dataListLoading = false
      })
    },
    download(path, name) {
      this.$http({
        url: this.$http.adornUrl(path),
        method: 'get',
        responseType: 'arraybuffer',
        params: this.$http.adornParams()
      }).then(({data}) => {
        console.info('asdasdsad', data)
        if (data.code && data.code !== 0) {
          this.$message.error('下载失败')
        } else {
          let blob = new Blob([data], {type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document;charset=utf-8'})
          let objectUrl = URL.createObjectURL(blob)
          let a = document.createElement('a')
          // window.location.href = objectUrl
          a.href = objectUrl
          a.download = name
          a.click()
          URL.revokeObjectURL(objectUrl)
        }
      })
    },
  }
}
</script>
