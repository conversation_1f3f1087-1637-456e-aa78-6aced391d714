<template>
  <div class="mod-config">
    <el-form :inline="true" ref="dataForm" :model="dataForm" @keyup.enter.native="queryPage()">
      <el-form-item label="关键词" pros="key">
        <el-input v-model="dataForm.key" placeholder="企业名称/统一社会信用代码" clearable></el-input>
      </el-form-item>
      <el-form-item label="年度" pros="year">
        <el-select v-model="dataForm.year" placeholder="请选择" clearable style="width: 100%">
          <el-option
              v-for="item in yearList"
              :key="item"
              :label="item"
              :value="item">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="状态" pros="status">
        <el-select v-model="dataForm.status" placeholder="请选择" clearable style="width: 100%">
          <el-option
              :key="true"
              label="上架"
              :value="true">
          </el-option>
          <el-option
              :key="false"
              label="下架"
              :value="false">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="warning" icon="el-icon-search" @click="queryPage()">查询</el-button>
        <el-button icon="el-icon-refresh-left" @click="resetForm">重置</el-button>
      </el-form-item>
    </el-form>
    <div class="f-s-c"
         style="padding: 15px 0;display: flex;justify-content:space-between;align-items: center;float:right ">
      <el-button icon="el-icon-plus" type="primary" @click="addOrUpdateHandle()">新增企业
      </el-button>
      <el-button icon="el-icon-download" type="success" v-loading.fullscreen.lock="fullscreenLoading"
                 @click="selectYearVisible=true">按年导出公益伙伴参与计划
      </el-button>
      <el-button icon="el-icon-upload" type="danger" @click="projectCompanyImport()">导入企业
      </el-button>
    </div>
    <el-table
        :data="dataList"
        border
        v-loading="dataListLoading"
        style="width: 100%;">
      <el-table-column
          prop="corpName"
          header-align="center"
          min-width="180"
          show-overflow-tooltip
          align="center"
          label="企业名称">
      </el-table-column>
      <el-table-column
          prop="socialCreditCode"
          header-align="center"
          align="center"
          min-width="180"
          label="统一社会信用代码">
      </el-table-column>
      <el-table-column
          prop="corpType"
          header-align="center"
          align="center"
          min-width="150"
          label="企业类型">
      </el-table-column>
      <el-table-column
          prop="loveEnterpriseYears"
          show-overflow-tooltip
          header-align="center"
          min-width="100"
          align="center"
          label="年度">
      </el-table-column>
      <el-table-column
          prop="linkMan"
          header-align="center"
          align="center"
          label="联系人">
      </el-table-column>
      <el-table-column
          prop="linkPhone"
          header-align="center"
          align="center"
          min-width="120"
          label="联系电话">
      </el-table-column>
      <el-table-column
          prop="createDate"
          header-align="center"
          align="center"
          min-width="150"
          label="创建时间">
      </el-table-column>
      <el-table-column
          prop="status"
          header-align="center"
          align="center"
          label="启用状态">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.status === false" size="small" type="danger">否</el-tag>
          <el-tag v-else size="small">是</el-tag>
        </template>
      </el-table-column>
      <el-table-column
          fixed="right"
          header-align="center"
          align="center"
          min-width="180"
          label="操作">
        <template slot-scope="scope">
          <el-button type="text" size="small" @click="changeTopDisplay(scope.row.id, !scope.row.topDisplay)">
            {{ scope.row.topDisplay ? '取消置顶' : '置顶' }}
          </el-button>
          <el-button type="text" size="small" @click="changeStatus(scope.row.id, !scope.row.status)">
            {{ scope.row.status ? '下架' : '上架' }}
          </el-button>
          <el-button type="text" size="small" @click="bindProject(scope.row.id)">关联项目</el-button>
          <el-button type="text" size="small" @click="addOrUpdateHandle(scope.row.id)">修改</el-button>
          <el-button type="text" size="small" @click="deleteHandle(scope.row.id)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
        @size-change="sizeChangeHandle"
        @current-change="currentChangeHandle"
        :current-page="pageIndex"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pageSize"
        :total="totalPage"
        layout="total, sizes, prev, pager, next, jumper">
    </el-pagination>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"></add-or-update>
    <el-dialog
        width="480px"
        :title="'选择导出的年份'"
        :close-on-click-modal="false"
        :visible.sync="selectYearVisible">
      <el-form label-width="auto">
        <el-form-item label="年份" prop="year">
          <el-select v-model="year" placeholder="请选择" clearable style="width: 100%">
            <el-option
                v-for="item in yearList"
                :key="item"
                :label="item"
                :value="item">
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="selectYearVisible = false">取消</el-button>
        <el-button type="primary" @click="exportExchange()">确定</el-button>
      </span>
    </el-dialog>
    <project-bind v-if="bindProjectVisible" ref="projectBind"></project-bind>
    <project-company-import v-if="projectCompanyImportVisible" ref="projectCompanyImport"
                            @refreshDataList="getDataList"></project-company-import>
  </div>
</template>

<script>
import AddOrUpdate from './project-company-add-or-update'
import ProjectBind from './project-company-bind.vue'
import ProjectCompanyBind from "./project-company-bind.vue";
import ProjectCompanyImport from "./project-company-import.vue";

export default {
  data() {
    return {
      year: null,
      yearList: [],
      dataForm: {
        key: '',
        year: '',
        status: null
      },
      dataList: [],
      pageIndex: 1,
      pageSize: 10,
      totalPage: 0,
      dataListLoading: false,
      dataListSelections: [],
      addOrUpdateVisible: false,
      fullscreenLoading: false,
      selectYearVisible: false,
      bindProjectVisible: false,
      projectCompanyImportVisible: false
    }
  },
  components: {
    ProjectCompanyBind,
    AddOrUpdate,
    ProjectBind,
    ProjectCompanyImport
  },
  activated() {
    this.buildYearList()
    this.queryPage()
  },
  methods: {
    buildYearList() {
      let currentYear = new Date().getFullYear()
      this.year = currentYear
      const startYear = currentYear - 9
      for (let year = currentYear; year >= startYear; year--) {
        this.yearList.push(year)
      }
    },
    resetForm() {
      this.dataForm.key = null
      this.dataForm.year = null
      this.dataForm.status = null
      this.pageIndex = 1
      this.getDataList()
    },
    queryPage() {
      this.pageIndex = 1
      this.getDataList()
    },
    // 获取数据列表
    getDataList() {
      this.dataListLoading = true
      this.$http({
        url: this.$http.adornUrl('/admin/project/company/pages'),
        method: 'post',
        data: this.$http.adornData({
          'currentPage': this.pageIndex,
          'pageSize': this.pageSize,
          'key': this.dataForm.key,
          'year': this.dataForm.year,
          'status': this.dataForm.status
        })
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.dataList = data.obj.records
          this.totalPage = data.obj.total
        } else {
          this.dataList = []
          this.totalPage = 0
        }
        this.dataListLoading = false
      })
    },
    // 每页数
    sizeChangeHandle(val) {
      this.pageSize = val
      this.pageIndex = 1
      this.getDataList()
    },
    // 当前页
    currentChangeHandle(val) {
      this.pageIndex = val
      this.getDataList()
    },
    // 导出
    exportExchange() {
      this.fullscreenLoading = true;
      this.$http({
        url: this.$http.adornUrl('/admin/project/company/exportCompanyDockingList'),
        method: 'get',
        responseType: 'arraybuffer',
        params: this.$http.adornParams({
          'year': this.year
        })
      }).then(({data}) => {
        if (data.code && data.code !== 0) {
          this.$message.error('导出失败')
        } else {
          this.fullscreenLoading = false
          let blob = new Blob([data], {
            type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8'
          })
          let objectUrl = URL.createObjectURL(blob)
          let a = document.createElement('a')
          // window.location.href = objectUrl
          a.href = objectUrl
          a.download = '参与公益伙伴计划.xlsx'
          a.click()
          URL.revokeObjectURL(objectUrl)
          this.$message({
            type: 'success',
            message: '导出成功'
          })
        }
      })
    },
    // 新增 / 修改
    addOrUpdateHandle(id) {
      this.addOrUpdateVisible = true
      this.$nextTick(() => {
        this.$refs.addOrUpdate.init(id)
      })
    },
    // 删除
    deleteHandle(id) {
      var ids = id ? [id] : this.dataListSelections.map(item => {
        return item.id
      })
      this.$confirm(`确定进行[${id ? '删除' : '批量删除'}]操作?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$http({
          url: this.$http.adornUrl('/admin/project/company/removeByIds'),
          method: 'get',
          params: this.$http.adornParams({
            'ids': ids.join(',')
          })
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.$message({
              message: '操作成功',
              type: 'success',
              duration: 1500,
              onClose: () => {
                this.getDataList()
              }
            })
          } else {
            this.$message.error(data.msg)
          }
        })
      })
    },
    //置顶
    changeTopDisplay(id, topDisplay) {
      this.$confirm(`确定进行[${topDisplay ? '置顶' : '取消置顶'}]操作?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$http({
          url: this.$http.adornUrl('/admin/project/company/changeTopDisplay'),
          method: 'get',
          params: this.$http.adornParams({
            'id': id,
            'topDisplay': topDisplay
          })
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.$message({
              message: '操作成功',
              type: 'success',
              duration: 1500,
              onClose: () => {
                this.getDataList()
              }
            })
          }
        })
      })
    },
    //上下架
    changeStatus(id, status) {
      this.$confirm(`确定进行[${status ? '上架' : '下架'}]操作?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$http({
          url: this.$http.adornUrl('/admin/project/company/changeStatus'),
          method: 'get',
          params: this.$http.adornParams({
            'id': id,
            'status': status
          })
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.$message({
              message: '操作成功',
              type: 'success',
              duration: 1500,
              onClose: () => {
                this.getDataList()
              }
            })
          }
        })
      })
    },
    bindProject(id) {
      this.bindProjectVisible = true
      this.$nextTick(() => {
        this.$refs.projectBind.init(id)
      })
    },
    projectCompanyImport() {
      this.projectCompanyImportVisible = true
      this.$nextTick(() => {
        this.$refs.projectCompanyImport.init()
      })
    }
  }
}
</script>
