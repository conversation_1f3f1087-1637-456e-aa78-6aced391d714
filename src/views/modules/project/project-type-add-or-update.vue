<template>
  <el-dialog
      :title="!dataForm.id ? '新增' : '修改'"
      :close-on-click-modal="false"
      :visible.sync="visible">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmit()"
             label-width="120px">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="项目类型名称" prop="name" :error="nameError">
            <el-input v-model="dataForm.name" placeholder="项目类型名称"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="类型编码code" prop="code" :error="codeError">
            <el-input v-model="dataForm.code" placeholder="类型编码code"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="是否启用" prop="status" :error="statusError">
            <el-radio-group v-model="dataForm.status">
              <el-radio :label="true">启用</el-radio>
              <el-radio :label="false">禁用</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="排序" prop="sequence" :error="sequenceError">
            <el-input-number v-model="dataForm.sequence" controls-position="right" :min="0"></el-input-number>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  data() {
    return {
      visible: false,
      initForm: {
        id: null,
        version: null,
        name: '',
        status: true,
        code: '',
        sequence: ''
      },
      dataForm: {},
      dataRule: {
        name: [
          {required: true, message: '名称不能为空', trigger: 'blur'}
        ],
        status: [
          {required: true, message: '是否启用不能为空', trigger: 'blur'}
        ],
        code: [
          {required: true, message: '编码不能为空', trigger: 'blur'}
        ],
        sequence: [
          {required: true, message: '排序不能为空', trigger: 'blur'}
        ]
      },
      nameError: null,
      statusError: null,
      codeError: null,
      sequenceError: null
    }
  },
  methods: {
    init(id) {
      this.dataForm = _.cloneDeep(this.initForm)
      this.dataForm.id = id || null
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm']?.resetFields()
        if (this.dataForm.id) {
          this.$http({
            url: this.$http.adornUrl(`/admin/project/type`),
            method: 'get',
            params: this.$http.adornParams({id: this.dataForm.id})
          }).then(({data}) => {
            if (data && data.code === 0) {
              this.dataForm = data.obj
            }
          })
        }
      })
    },
    // 表单提交
    dataFormSubmit() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.$http({
            url: this.$http.adornUrl(`/admin/project/type/saveOrUpdateType`),
            method: 'post',
            data: this.$http.adornData(this.dataForm)
          }).then(({data}) => {
            if (data && data.code === 0) {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 1500,
                onClose: () => {
                  this.visible = false
                  this.$emit('refreshDataList')
                }
              })
            } else if (data && data.code === 303) {
              for (let it of data.obj) {
                this[`${it.field}Error`] = it.message
              }
            } else {
              this.$message.error(data.msg)
            }
          })
        }
      })
    }
  }
}
</script>
