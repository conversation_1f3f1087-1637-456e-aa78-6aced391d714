<template>
    <el-dialog
            :title="!dataForm.id ? '新增' : '修改'"
            :close-on-click-modal="false"
            append-to-body
            :visible.sync="visible">
        <el-form :model="dataForm" :rules="dataRule" ref="dataForm"
                 label-width="120px">
            <el-row :gutter="20">
                <el-col :span="16">
                    <el-form-item label="企业" prop="corpId">
                        <el-select class="width185" v-model="dataForm.corpId" clearable filterable style="width: 100%"
                                   @change="changeCorp">
                            <el-option
                                    v-for="item in companyList"
                                    :key="item.id"
                                    :label="item.corpName"
                                    :value="item.id">
                            </el-option>
                        </el-select>
                    </el-form-item>
                </el-col>
                <el-col :span="1">
                    <el-button icon="el-icon-plus" circle @click="companyAddOrUpdateHandle()"></el-button>
                </el-col>
            </el-row>
            <el-row :gutter="20">
                <el-col :span="16">
                    <el-form-item label="联系人" prop="corpLinkMan">
                        <el-input v-model="dataForm.corpLinkMan" placeholder="联系人"></el-input>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row :gutter="20">
                <el-col :span="16">
                    <el-form-item label="联系电话" prop="corpLinkPhone">
                        <el-input v-model="dataForm.corpLinkPhone" placeholder="联系电话"></el-input>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row :gutter="20">
                <el-col :span="16">
                    <el-form-item label="邮箱" prop="corpEmail">
                        <el-input v-model="dataForm.corpEmail" placeholder="邮箱"></el-input>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row :gutter="20">
                <el-col :span="16">
                    <el-form-item label="对接类型" prop="projectDemandType">
                        <el-radio-group v-model="dataForm.projectDemandType">
                            <el-radio :label="'project_capital'">资金</el-radio>
                            <el-radio :label="'project_materials'">物资</el-radio>
                        </el-radio-group>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row :gutter="20">
                <el-col :span="12">
                    <el-form-item label="签约时间" prop="signingDate" :error="signingDateError">
                        <el-date-picker
                                style="width: 100%"
                                v-model="dataForm.signingDate"
                                value-format="yyyy-MM-dd"
                                type="date"
                                clearable
                                placeholder="签约时间"/>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row :gutter="20">
                <el-col :span="12">
                    <el-form-item label="打款时间" prop="paymentDate" :error="paymentDateError">
                        <el-date-picker
                                style="width: 100%"
                                v-model="dataForm.paymentDate"
                                value-format="yyyy-MM-dd"
                                type="date"
                                clearable
                                placeholder="打款时间"/>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row :gutter="20">
                <el-col :span="16">
                    <el-form-item label="对接金额(元)" prop="amount" :error="amountError">
                        <el-input-number v-model="dataForm.amount" placeholder="对接金额"></el-input-number>
                    </el-form-item>
                </el-col>
            </el-row>

        </el-form>
        <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" :disabled="isDisabled" @click="dataFormSubmit()">确定</el-button>
    </span>
        <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="refreshCompanyCallback"></add-or-update>
    </el-dialog>
</template>

<script>
import moment from 'moment'
import AddOrUpdate from './project-company-add-or-update'

export default {
    data() {
        return {
            visible: false,
            dataForm: {},
            companyList: [],
            isDisabled: false,
            addOrUpdateVisible: false,
            initForm: {
                id: null,
                version: null,
                corpId: '',
                dockingTime: '',
                amount: '',
                projectId: '',
                corpEmail: '',
                corpLinkMan: '',
                corpLinkPhone: '',
                projectDemandType: '',
                signingDate: '',
                paymentDate: ''
            },
            dataRule: {
              corpId: [
                  {required: true, message: '企业不能为空', trigger: 'change'}
                ],
                dockingTime: [
                    {required: true, message: '对接时间不能为空', trigger: 'blur'}
                ],
                amount: [
                    {required: true, message: '对接金额不能为空', trigger: 'blur'}
                ],
                projectId: [
                    {required: true, message: '项目id不能为空', trigger: 'blur'}
                ],
                projectDemandType: [
                    {required: true, message: '项目需求类型不能为空', trigger: 'blur'}
                ],
                signingDate: [
                    {required: true, message: '签约时间不能为空', trigger: 'blur'}
                ],
                paymentDate: [
                    {required: true, message: '打款时间不能为空', trigger: 'blur'}
                ]
            },
            corpIdError: null,
            dockingTimeError: null,
            amountError: null,
            projectIdError: null,
            projectDemandTypeError: null,
            signingDateError: null,
            paymentDateError: null

        }
    },
    components: {
        moment,
        AddOrUpdate
    },
    methods: {
        init(id, projectId, projectDemandType) {
            this.dataForm = _.cloneDeep(this.initForm)
            this.dataForm.id = id || null
            this.dataForm.projectId = projectId || null
            this.dataForm.projectDemandType = projectDemandType || null
            this.getCompanyList()
            this.visible = true
            this.$nextTick(() => {
                this.$refs['dataForm']?.resetFields()
                if (this.dataForm.id) {
                    this.$http({
                        url: this.$http.adornUrl(`/admin/project/docking`),
                        method: 'get',
                        params: this.$http.adornParams({id: this.dataForm.id})
                    }).then(({data}) => {
                        if (data && data.code === 0) {
                            this.dataForm = data.obj
                        }
                    })
                }
            })
        },
        changeCorp(vale) {
            this.companyList.forEach(item => {
                if (item.id === vale) {
                    this.dataForm.corpLinkMan = item.linkMan
                    this.dataForm.corpLinkPhone = item.linkPhone
                    this.dataForm.corpEmail = item.email
                }
            })
        },
        //获取受益人类别
        getCompanyList() {
            this.$http({
                url: this.$http.adornUrl(`/admin/project/company/getCompanyList`),
                method: 'get',
                params: this.$http.adornParams()
            }).then(({data}) => {
                if (data && data.code === 0) {
                    this.companyList = data.obj || []
                }
            })
        },
        //获取受益人类别
        refreshCompanyCallback(id) {
            this.$http({
                url: this.$http.adornUrl(`/admin/project/company/getCompanyList`),
                method: 'get',
                params: this.$http.adornParams()
            }).then(({data}) => {
                if (data && data.code === 0) {
                    this.companyList = data.obj || []
                    this.dataForm.corpId=id
                    this.changeCorp(id)
                }
            })
        },
        // 新增 / 修改
        companyAddOrUpdateHandle(id) {
            this.addOrUpdateVisible = true
            this.$nextTick(() => {
                this.$refs.addOrUpdate.init(id)
            })
        },
        // 表单提交
        dataFormSubmit() {
            this.$refs['dataForm'].validate((valid) => {
                if (valid) {
                    this.isDisabled = true
                    this.$http({
                        url: this.$http.adornUrl(`/admin/project/docking/${!this.dataForm.id ? 'save' : 'update'}`),
                        method: 'post',
                        data: this.$http.adornData(this.dataForm)
                    }).then(({data}) => {
                        if (data && data.code === 0) {
                            this.$message({
                                message: '操作成功',
                                type: 'success',
                                duration: 1500,
                                onClose: () => {
                                    this.visible = false
                                    this.isDisabled = false
                                    this.$emit('refreshDataList')
                                }
                            })
                        } else if (data && data.code === 303) {
                            for (let it of data.obj) {
                                this[`${it.field}Error`] = it.message
                            }
                        } else {
                            this.$message.error(data.msg)
                            this.isDisabled = false
                        }
                    })
                }
            })
        }
    }
}
</script>
