<template>
  <div class="mod-config">
    <el-form :inline="true" :model="dataForm" ref="dataForm" @keyup.enter.native="getDataList()">
      <el-form-item prop="year" label="年份：">
        <el-input v-model="dataForm.year" placeholder="年份" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-search" type="warning" @click="getDataList()">查询</el-button>
        <el-button icon="el-icon-refresh-left" @click="resetField()">重置</el-button>
      </el-form-item>
    </el-form>
    <div class="f-s-c"
         style="padding: 15px 0;display: flex;justify-content:space-between;align-items: center;float:right ">
      <el-button icon="el-icon-plus" type="primary" @click="addOrUpdateHandle()">新增
      </el-button>
    </div>
    <el-table
        :data="dataList"
        border
        v-loading="dataListLoading"
        style="width: 100%;">
      <el-table-column
          prop="year"
          header-align="center"
          align="center"
          label="年份">
      </el-table-column>
      <el-table-column
          prop="createDate"
          header-align="center"
          align="center"
          label="创建时间">
      </el-table-column>
      <el-table-column
          fixed="right"
          header-align="center"
          align="center"
          width="150"
          label="操作">
        <template slot-scope="scope">
          <el-button type="text" size="small" @click="deleteHandle(scope.row.id)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
        @size-change="sizeChangeHandle"
        @current-change="currentChangeHandle"
        :current-page="pageIndex"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pageSize"
        :total="totalPage"
        layout="total, sizes, prev, pager, next, jumper">
    </el-pagination>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"></add-or-update>
  </div>
</template>

<script>
import ProjectYearAddOrUpdate from './project-year-add-or-update.vue'
import listMixin from '@/mixins/list-mixins'

export default {
  mixins: [listMixin],
  data() {
    return {
      mixinOptions: {
        dataUrl: '/admin/zyz/project-year/pages',
        deleteUrl: '/admin/zyz/project-year/removeByIds'
      },
      dataForm: {
        year: '',
        orders: [{column: 'year', sort: 'desc'}]
      }
    }
  },
  components: {
    AddOrUpdate: ProjectYearAddOrUpdate
  },
  activated() {
    this.getDataList()
  },
  methods: {
    resetField () {
      this.$refs['dataForm']?.resetFields()
      this.getDataList()
    }
  }
}
</script>
