<template>
  <el-dialog
      :title="'关联项目'"
      :close-on-click-modal="false"
      :visible.sync="visible"
      append-to-body>
    <el-form :inline="true" ref="dataForm" :model="dataForm" @keyup.enter.native="getDataList()">
      <el-form-item label="项目名称" pros="projectName">
        <el-input v-model="dataForm.projectName" placeholder="项目名称" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-button type="warning" icon="el-icon-search" @click="getDataList()">查询</el-button>
      </el-form-item>
    </el-form>
    <el-table
        :data="dataList"
        border
        v-loading="dataListLoading"
        style="width: 100%;">
      <el-table-column
          prop="projectName"
          show-overflow-tooltip
          header-align="center"
          min-width="200"
          align="center"
          label="项目名称">
      </el-table-column>
      <el-table-column
          prop="projectCode"
          show-overflow-tooltip
          header-align="center"
          min-width="100"
          align="center"
          label="项目编号">
      </el-table-column>
      <el-table-column
          prop="year"
          header-align="center"
          min-width="100"
          show-overflow-tooltip
          align="center"
          label="年度">
      </el-table-column>
      <el-table-column
          fixed="right"
          header-align="center"
          align="center"
          min-width="180"
          label="操作">
        <template slot-scope="scope">
          <el-button type="text" size="small" @click="cancelBind(scope.row.dockingId)">取消关联</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
        @size-change="sizeChangeHandle"
        @current-change="currentChangeHandle"
        :current-page="pageIndex"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pageSize"
        :total="totalPage"
        layout="total, sizes, prev, pager, next, jumper">
    </el-pagination>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">关闭</el-button>
    </span>
  </el-dialog>
</template>

<script>
import listMixin from '@/mixins/list-mixins'

export default {
  mixins: [listMixin],
  data() {
    return {
      mixinOptions: {
        dataUrl: '/admin/project/company/bindProjectPage'
      },
      visible: false,
      dataForm: {
        companyId: null,
        projectName: ''
      }
    }
  },
  methods: {
    init(id) {
      this.visible = true
      this.$nextTick(() => {
        this.dataForm = {
          companyId: id,
          projectName: ''
        }
        this.query()
      })
    },
    cancelBind(id) {
      this.$confirm(`确定要取消关联吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$http({
          url: this.$http.adornUrl("/admin/project/company/removeBind"),
          method: "get",
          params: this.$http.adornParams({
            dockingId: id
          })
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.$message({
              message: '操作成功',
              type: 'success',
              duration: 1500,
              onClose: () => {
                this.getDataList()
              }
            })
          }
        })
      })
    }
  }
}
</script>