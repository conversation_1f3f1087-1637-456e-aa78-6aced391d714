<template>
  <el-dialog
      :title="'对接记录'"
      :close-on-click-modal="false"
      width="80%"
      append-to-body
      :visible.sync="visible">
    <el-form :inline="true" :model="dataForm" ref="dataForm" @keyup.enter.native="queryPage()">
      <div class="f-s-c" style="padding: 15px 0;display: flex;justify-content:flex-end;align-items: center ">
        <div>
          <el-button icon="el-icon-plus" type="primary" @click="addOrUpdateHandle()">新增</el-button>
          <el-button icon="el-icon-back" type="info" @click="escHandle()">返回</el-button>
        </div>
      </div>
    </el-form>
    <el-table
      :data="dataList"
      border
      v-loading="dataListLoading"
      style="width: 100%;">
       <el-table-column
        prop="corpName"
        header-align="center"
        align="center"
        width="200px"
        label="企业名称">
      </el-table-column>
      <el-table-column
          prop="socialCreditCode"
          header-align="center"
          align="center"
          width="180px"
          label="统一社会信用代码">
      </el-table-column>
      <el-table-column
          prop="corpLinkMan"
          header-align="center"
          align="center"
          label="联系人">
      </el-table-column>
      <el-table-column
          prop="corpLinkPhone"
          header-align="center"
          align="center"
          width="160px"
          label="联系电话">
      </el-table-column>
      <el-table-column
          prop="projectDemandTypeText"
          header-align="center"
          align="center"
          label="对接类型">
      </el-table-column>
         <el-table-column
        prop="amount"
        header-align="center"
        align="center"
        label="对接金额">
      </el-table-column>
      <el-table-column
          prop="dockingTime"
          header-align="center"
          align="center"
          width="160px"
          label="对接时间">
      </el-table-column>
        <el-table-column
                prop="signingDate"
                header-align="center"
                align="center"
                width="160px"
                label="签约时间">
        </el-table-column>
        <el-table-column
                prop="paymentDate"
                header-align="center"
                align="center"
                width="160px"
                label="打款时间">
        </el-table-column>
        <el-table-column
        fixed="right"
        header-align="center"
        align="center"
        width="150"
        label="操作">
        <template slot-scope="scope">
          <el-button type="text" size="small" @click="addOrUpdateHandle(scope.row.id)">编辑</el-button>
          <el-button type="text" size="small" @click="deleteHandle(scope.row.id)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"></add-or-update>
  </el-dialog>
</template>

<script>
  import AddOrUpdate from './project-docking-add-or-update'
  export default {
    data () {
      return {
        visible: false,
        dataForm: {},
        initForm: {
          projectId: '',
          projectDemandType: '',
        },
        dataList: [],
        dataListLoading: false,
        addOrUpdateVisible: false
      }
    },
    components: {
      AddOrUpdate
    },
    methods: {
      init (id,projectDemandType) {
        this.dataForm = _.cloneDeep(this.initForm)
        this.dataForm.projectId = id || null
        this.dataForm.projectDemandType = projectDemandType || null
        this.queryPage()
        this.visible = true
        this.$nextTick(() => {
          this.$refs['dataForm']?.resetFields()
          if (this.dataForm.id) {
            this.$http({
              url: this.$http.adornUrl(`/admin/project/docking`),
              method: 'get',
              params: this.$http.adornParams({ id: this.dataForm.id })
            }).then(({data}) => {
              if (data && data.code === 0) {
                this.dataForm = data.obj
              }
            })
          }
        })
      },

      queryPage () {
        this.getDataList()
      },
      // 获取数据列表
      getDataList () {
        this.dataListLoading = true
        this.$http({
          url: this.$http.adornUrl('/admin/project/docking/getListByProjectId'),
          method: 'get',
          params: this.$http.adornParams({
            'projectId': this.dataForm.projectId
          })
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.dataList = data.obj
          } else {
            this.dataList = []
          }
          this.dataListLoading = false
        })
      },
      // 新增 / 修改
      addOrUpdateHandle (id) {
        this.addOrUpdateVisible = true
        this.$nextTick(() => {
          this.$refs.addOrUpdate.init(id,this.dataForm.projectId,this.dataForm.projectDemandType)
        })
      },
      escHandle () {
        this.visible = false
       this.$emit('refreshDataList')
      },
      // 删除
      deleteHandle (id) {
        var ids = id ? [id] : this.dataListSelections.map(item => {
          return item.id
        })
        this.$confirm(`确定进行[${id ? '删除' : '批量删除'}]操作?`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.$http({
            url: this.$http.adornUrl('/admin/project/docking/removeByIds'),
            method: 'get',
            params: this.$http.adornParams({
              'ids': ids.join(',')
            })
          }).then(({data}) => {
            if (data && data.code === 0) {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 1500,
                onClose: () => {
                  this.getDataList()
                }
              })
            } else {
              this.$message.error(data.msg)
            }
          })
        })
      }
    }
  }
</script>
