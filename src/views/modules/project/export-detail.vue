<template>
    <el-dialog
            title="下载"
            :close-on-click-modal="false"
            :visible.sync="visible"
            width="40%">
        <el-form :model="dataForm" :rules="dataRule" ref="dataForm"
                 label-width="120px">
            <el-row :gutter="20">
                <el-col :span="24">
                    <el-form-item label="下载内容" prop="type">
                        <el-radio-group v-model="dataForm.type">
                            <el-radio :label=1>对接</el-radio>
                            <el-radio :label=2>执行</el-radio>
                            <el-radio :label=3>总结</el-radio>
                            <el-radio :label=4>全部</el-radio>
                        </el-radio-group>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="downloadResultFile(dataForm.type)" :loading="loading">下载</el-button>
    </span>
    </el-dialog>
</template>

<script>
export default {
    data() {
        return {
            visible: false,
            loading: false,
            initForm: {
                type: 4,
                ids: null,
            },
            dataForm: {},
            dataRule: {
                name: [
                    {required: true, message: '名称不能为空', trigger: 'blur'}
                ],
                status: [
                    {required: true, message: '是否启用不能为空', trigger: 'blur'}
                ],
                code: [
                    {required: true, message: '编码不能为空', trigger: 'blur'}
                ],
                sequence: [
                    {required: true, message: '排序不能为空', trigger: 'blur'}
                ]
            },
            nameError: null,
            statusError: null,
            codeError: null,
            sequenceError: null
        }
    },
    methods: {
        init(ids) {
            this.dataForm = _.cloneDeep(this.initForm)
            this.dataForm.ids = ids || null
            this.visible = true
        },
        // 下载压缩文件
        downloadResultFile(type) {
            this.loading = true;
            let fileName = ''
            if (type == 1) {
                fileName = '项目对接.xlsx'
            } else if (type == 2) {
                fileName = '项目执行.xlsx'
            } else if (type == 3) {
                fileName = '项目总结.xlsx'
            } else if (type == 4) {
                fileName = '项目信息.zip'
            }
            this.$http({
                url: this.$http.adornUrl('/admin/project/downloadResultFile'),
                method: 'get',
                responseType: 'arraybuffer',
                params: this.$http.adornParams({'ids': this.dataForm.ids, 'type':type}, false)
            }).then(({data}) => {
                if (data.code && data.code !== 0) {
                    this.$message.error('下载失败')
                    this.dataListLoading = false
                    this.loading = false
                } else {
                    const blob = new Blob([data], {type: 'application/ostet-stream'})
                    const objectUrl = URL.createObjectURL(blob)
                    const a = document.createElement('a')
                    a.href = objectUrl
                    a.download = fileName
                    a.click()
                    URL.revokeObjectURL(objectUrl)
                    this.$message({
                        type: 'success',
                        message: '下载成功'
                    })
                    this.dataListLoading = false
                    this.loading = false
                }
            })
        },
    }
}
</script>
