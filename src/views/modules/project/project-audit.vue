<template>
  <div class="mod-config">
    <el-form :inline="true" :model="dataForm" ref="dataForm" @keyup.enter.native="queryPage()">
      <el-form-item label="项目名称或编号" prop="projectName">
        <el-input v-model="dataForm.projectName" placeholder="项目名称或编号" clearable></el-input>
      </el-form-item>
      <el-form-item label="项目需求类型" prop="projectDemandType">
        <el-dict :code="'project_demand_type'" v-model="dataForm.projectDemandType"></el-dict>
      </el-form-item>
      <el-form-item label="项目申报方" prop="teamName">
        <el-input v-model="dataForm.teamName" placeholder="项目申报方" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-search" type="warning" @click="queryPage()">查询</el-button>
        <el-button @click="resetForm()" icon="el-icon-refresh-left">重置</el-button>
      </el-form-item>
    </el-form>
    <el-table
        :data="dataList"
        border
        v-loading="dataListLoading"
        @selection-change="selectionChangeHandle"
        style="width: 100%;">
      <el-table-column
          type="selection"
          header-align="center"
          align="center"
          width="50">
      </el-table-column>
      <el-table-column
          prop="projectName"
          header-align="center"
          align="center"
          min-width="300"
          label="项目名称">
      </el-table-column>
        <el-table-column
                prop="projectCode"
                header-align="center"
                min-width="80"
                align="center"
                label="项目编号">
        </el-table-column>
      <el-table-column
          prop="projectDemandTypeText"
          header-align="center"
          align="center"
          min-width="150"
          label="项目需求类型">
      </el-table-column>
      <el-table-column
          prop="typeName"
          header-align="center"
          align="center"
          :show-overflow-tooltip="true"
          show-overflow-tooltip
          min-width="150"
          label="项目类型">
      </el-table-column>
      <el-table-column
          prop="teamName"
          header-align="center"
          align="center"
          min-width="150"
          label="项目申报方">
      </el-table-column>
      <el-table-column
          prop="contactName"
          header-align="center"
          align="center"
          min-width="100"
          label="联系人姓名">
      </el-table-column>
      <el-table-column
          prop="contactPhone"
          header-align="center"
          align="center"
          min-width="100"
          label="联系人电话">
      </el-table-column>
      <el-table-column
          prop="auditStatusText"
          header-align="center"
          align="center"
          min-width="100"
          label="项目状态">
      </el-table-column>
      <el-table-column
          fixed="right"
          header-align="center"
          align="center"
          min-width="100"
          label="操作">
        <template slot-scope="scope">
<!--          <el-button type="text" size="small" @click="auditHandle(scope.row.id, scope.row.name, true)">通过</el-button>-->
<!--          <el-button type="text" size="small" @click="auditHandle(scope.row.id, scope.row.name, false)">驳回</el-button>-->
          <el-button type="text" size="small" @click="detailHandle(scope.row.id)">审核</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
        @size-change="sizeChangeHandle"
        @current-change="currentChangeHandle"
        :current-page="pageIndex"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pageSize"
        :total="totalPage"
        layout="total, sizes, prev, pager, next, jumper">
    </el-pagination>
   <detail v-if="detailVisible" ref="detail" @refreshDataList="getDataList"></detail>
  </div>
</template>

<script>
import Detail from './project-detail.vue'

export default {
  data() {
    return {
      typeList: [],
      dataForm: {
        key: '',
        auditStatus: '',
        projectDemandType: '',
        contactName: '',
        contactPhone: '',
        year: '',
        teamName: '',
        typeName: '',
        projectName: ''
      },
      dataList: [],
      pageIndex: 1,
      pageSize: 10,
      totalPage: 0,
      dataListLoading: false,
      dataListSelections: [],
      addOrUpdateVisible: false,
      dockingVisible: false,
      executeVisible: false,
      detailVisible: false,
    }
  },
  components: {
    Detail,
  },
  activated() {
    this.queryPage()
    this.getTypeList()
  },
  methods: {
    queryPage() {
      this.pageIndex = 1
      this.getDataList()
    },
    // 获取数据列表
    getDataList() {
      this.dataListLoading = true
      this.$http({
        url: this.$http.adornUrl('/admin/project/getPages'),
        method: 'post',
        data: this.$http.adornData({
          'currentPage': this.pageIndex,
          'pageSize': this.pageSize,
          'auditStatus': 'pro_wait_audit',
          'projectDemandType': this.dataForm.projectDemandType,
          'teamName': this.dataForm.teamName,
          'projectName': this.dataForm.projectName
        })
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.dataList = data.obj.records
          this.totalPage = data.obj.total
        } else {
          this.dataList = []
          this.totalPage = 0
        }
        this.dataListLoading = false
      })
    },
    // 每页数
    sizeChangeHandle(val) {
      this.pageSize = val
      this.pageIndex = 1
      this.getDataList()
    },
    // 当前页
    currentChangeHandle(val) {
      this.pageIndex = val
      this.getDataList()
    },
    // 多选
    selectionChangeHandle(val) {
      this.dataListSelections = val
    },
    // 重置
    resetForm() {
      this.$refs['dataForm']?.resetFields()
      this.getDataList()
    },
    // 删除
    deleteHandle(id) {
      var ids = id ? [id] : this.dataListSelections.map(item => {
        return item.id
      })
      this.$confirm(`确定进行[${id ? '删除' : '批量删除'}]操作?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$http({
          url: this.$http.adornUrl('/admin/project/removeByIds'),
          method: 'get',
          params: this.$http.adornParams({
            'ids': ids.join(',')
          })
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.$message({
              message: '操作成功',
              type: 'success',
              duration: 1500,
              onClose: () => {
                this.getDataList()
              }
            })
          } else {
            this.$message.error(data.msg)
          }
        })
      })
    },
    detailHandle(id){
      this.detailVisible = true
      this.$nextTick(() => {
        this.$refs.detail.init(id,false)
      })
    },
    auditHandle(id, name, status) {
      if (!id && this.dataListSelections.length === 0) {
        this.$message.warning('未选中需要审核的申报记录！')
        return
      }
      if (!id) {
        let ids = this.dataListSelections.map(item => {
          return item.id
        })
        id = ids.join(',')
      }
      this.$confirm(`确定${status ? '审核通过' : '驳回'}` + (name ? '"' + name + '"' : '') + '项目申报？', '系统提示', {
        customClass: 'audit_pass_msg_box',
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        showInput: !status,
        inputPlaceholder: '请输入驳回意见……',
        inputType: 'textarea',
        closeOnClickModal: false
      }).then((val) => {
        this.$http({
          url: this.$http.adornUrl('/admin/project/audit'),
          method: 'get',
          params: this.$http.adornParams({
            'ids': id,
            'status': status,
            'remark': val.value
          })
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.$message.success(status ? '审核通过成功！' : '驳回成功！')
            this.queryPage()
          } else {
            this.$message.error(status ? '审核通过失败！' : '驳回失败！' + data.msg)
          }
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '操作取消'
        });
      });
      setTimeout(() => {
        var content = document.getElementsByClassName('audit_pass_msg_box')[0].childNodes[1]
        content.style.paddingLeft = '60px'
        content.style.paddingRight = '60px'
        var submitBtn = document.getElementsByClassName('audit_pass_msg_box')[0].childNodes[2].childNodes[1]
        submitBtn.style.backgroundColor = '#F56C6C'
        submitBtn.style.borderColor = '#F56C6C'
      }, 50)
    }
  }
}
</script>
