<template>
    <div class="mod-config">
        <el-form :inline="true" :model="dataForm" ref="dataForm" @keyup.enter.native="queryPage()">
            <el-form-item label="项目名称或编号" prop="projectName">
                <el-input v-model="dataForm.projectName" placeholder="项目名称或编号" clearable></el-input>
            </el-form-item>
            <el-form-item label="项目需求类型" prop="projectDemandType">
                <el-dict :code="'project_demand_type'" v-model="dataForm.projectDemandType"></el-dict>
            </el-form-item>
            <el-form-item label="项目类型" prop="typeName">
                <el-select class="width185" v-model="dataForm.typeName" clearable
                           :placeholder="placeholder" filterable style="width: 100%">
                    <el-option
                            v-for="item in typeList"
                            :key="item.code"
                            :label="item.name"
                            :value="item.name">
                    </el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="项目申报方" prop="teamName" v-if="this.managerCapacity === 'ASSOCIATION_ADMIN'">
                <el-input v-model="dataForm.teamName" placeholder="项目申报方" clearable></el-input>
            </el-form-item>
            <el-form-item label="项目年度" prop="year">
                <el-dict :code="'pro_year'" v-model="dataForm.year"></el-dict>
            </el-form-item>
            <el-form-item label="联系人" prop="contactName">
                <el-input v-model="dataForm.contactName" placeholder="请输入联系人" clearable></el-input>
            </el-form-item>
            <el-form-item label="联系人手机号" prop="contactPhone">
                <el-input v-model="dataForm.contactPhone" placeholder="请输入联系人手机号" clearable></el-input>
            </el-form-item>
            <el-form-item label="项目状态" prop="auditStatus">
                <el-select v-model="dataForm.auditStatus" clearable :placeholder="placeholder">
                    <el-option
                            v-for="item in auditStatusList"
                            :key="item.code"
                            :label="item.name"
                            :value="item.code">
                    </el-option>
                </el-select>
            </el-form-item>
            <el-form-item>
                <el-button icon="el-icon-search" type="warning" @click="queryPage()">查询</el-button>
                <el-button @click="resetForm()" icon="el-icon-refresh-left">重置</el-button>
            </el-form-item>
            <div class="f-s-c" style="padding: 15px 0;display: flex;justify-content:flex-end;align-items: center ">
                <div>
                    <el-button icon="el-icon-info"  type="primary"
                               @click="infoHandle()">申报说明
                    </el-button>
                    <el-button icon="el-icon-plus" v-if="isAuth('zyz:project:save')" type="primary"
                               @click="addOrUpdateHandle()">申报
                    </el-button>
                    <el-button icon="el-icon-download" type="success" v-loading.fullscreen.lock="fullscreenLoading"
                               @click="exportHandle()">导出公益伙伴参与计划
                    </el-button>
                    <el-button icon="el-icon-download" type="primary" @click="downloadZip()" :disabled="dataListSelections.length <= 0" v-if="isAuth('zyz:project:batch_download')" >批量下载</el-button>
                    <el-button icon="el-icon-monitor" type="primary" @click="displayHandle(null, '', true)" :disabled="dataListSelections.length <= 0"  >批量展示</el-button>
                </div>
            </div>
        </el-form>
        <el-table
                :data="dataList"
                border
                v-loading="dataListLoading"
                @selection-change="selectionChangeHandle"
                style="width: 100%;">
            <el-table-column
                type="selection"
                header-align="center"
                align="center"
                width="50">
            </el-table-column>
            <el-table-column
                    prop="projectName"
                    header-align="center"
                    align="center"
                    min-width="150"
                    label="项目名称">
                <template slot-scope="scope">
                    <a style="cursor: pointer" @click="getDetail(scope.row.id)">{{ scope.row.projectName }}</a>
                </template>
            </el-table-column>
            <el-table-column
                prop="projectCode"
                header-align="center"
                min-width="80"
                align="center"
                label="项目编号">
            </el-table-column>
            <el-table-column
                    prop="projectDemandTypeText"
                    header-align="center"
                    min-width="80"
                    align="center"
                    label="需求类型">
            </el-table-column>
            <el-table-column
                    prop="typeName"
                    header-align="center"
                    align="center"
                    min-width="200"
                    :show-overflow-tooltip="true"
                    label="项目类型">
            </el-table-column>
            <el-table-column
                    prop="applyUnitName"
                    header-align="center"
                    align="center"
                    :show-overflow-tooltip="true"
                    label="申报单位">
            </el-table-column>
            <el-table-column
                    prop="year"
                    header-align="center"
                    align="center"
                    min-width="80"
                    label="项目年度">
            </el-table-column>
            <el-table-column
                    prop="contactName"
                    header-align="center"
                    align="center"
                    min-width="80"
                    label="联系人">
            </el-table-column>
            <el-table-column
                    prop="contactPhone"
                    header-align="center"
                    align="center"
                    label="联系人电话">
            </el-table-column>
            <el-table-column
                    prop="auditStatusText"
                    header-align="center"
                    align="center"
                    label="项目状态">
            </el-table-column>
            <el-table-column
                    fixed="right"
                    header-align="center"
                    align="center"
                    width="250"
                    label="操作">
                <template slot-scope="scope">
                    <!--          <el-button type="text" size="small" @click="auditHandle(scope.row.id, scope.row.name, true)">通过</el-button>-->
                    <!--          <el-button type="text" size="small" @click="auditHandle(scope.row.id, scope.row.name, false)">驳回</el-button>-->
                    <!--          <el-button v-if="scope.row.updateEnable" type="text" size="small" @click="render(scope.row.id)">提交</el-button>-->
                    <el-button v-if="isAuth('zyz:project:docking') && scope.row.dockingEnable" type="text" size="small"
                               @click="dockingHandle(scope.row.id,scope.row.projectDemandType)">对接
                    </el-button>
                    <el-button v-if="isAuth('zyz:project:docking') && scope.row.dockingFinishEnable" type="text"
                               size="small"
                               @click="dockingAuditStartHandle(scope.row.id,scope.row.projectDemandType)">申请启动
                    </el-button>
                    <el-button v-if="isAuth('zyz:project:report') && scope.row.successEnable" type="text" size="small"
                               @click="reportHandle(scope.row.id)">月报
                    </el-button>
                    <el-button v-if="isAuth('zyz:project:execute') && scope.row.successEnable" type="text" size="small"
                               @click="executeHandle(scope.row.id)">执行
                    </el-button>
                    <el-button v-if="isAuth('zyz:project:summary') && scope.row.successEnable" type="text" size="small"
                               @click="finishHandle(scope.row.id)">总结
                    </el-button>
                    <el-button v-if="isAuth('zyz:project:finish_detail') && scope.row.finishDetailEnable" type="text"
                               size="small"
                               @click="finishDetailHandle(scope.row.id)">完成详情
                    </el-button>
                    <el-button v-if="(isAuth('zyz:project:update') && scope.row.updateEnable)|| isAuth('zyz:project:super_update')" type="text" size="small"
                               @click="addOrUpdateHandle(scope.row.id)">
                        修改
                    </el-button>
                    <el-button v-if="isAuth('zyz:project:delete') && scope.row.updateEnable" type="text" size="small"
                               @click="deleteHandle(scope.row.id)">删除
                    </el-button>
                    <el-button v-if="isAuth('zyz:project:apply_recall') && scope.row.recallEnable" type="text"
                               size="small" @click="recallHandle(scope.row.id,false)">
                        撤回
                    </el-button>
                    <el-button v-if="isAuth('zyz:project:summary_recall') && scope.row.recallFinishEnable" type="text"
                               size="small"
                               @click="recallHandle(scope.row.id,true)">撤回
                    </el-button>
                    <el-button v-if="isAuth('zyz:project:summary_recall') && scope.row.dockingAuditRecallEnable"
                               type="text" size="small"
                               @click="auditDockingRecallHandle(scope.row.id)">撤回
                    </el-button>
                    <el-button v-if="isAuth('zyz:project:display') && scope.row.display == true" type="text" size="small"
                               @click="displayHandle(scope.row.id, scope.row.name, false)">不展示
                    </el-button>
                    <el-button v-if="isAuth('zyz:project:display') && scope.row.display == false" type="text" size="small"
                               @click="displayHandle(scope.row.id, scope.row.name, true)">设为前端展示
                    </el-button>
                    <el-button v-if="isAuth('zyz:project:project_form_download')"
                               type="text" size="small"
                               @click="fromDownload(scope.row.id)">项目书下载
                    </el-button>
                </template>
            </el-table-column>
        </el-table>
        <el-pagination
                @size-change="sizeChangeHandle"
                @current-change="currentChangeHandle"
                :current-page="pageIndex"
                :page-sizes="[10, 20, 50, 100]"
                :page-size="pageSize"
                :total="totalPage"
                layout="total, sizes, prev, pager, next, jumper">
        </el-pagination>
        <!-- 弹窗, 新增 / 修改 -->
        <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"></add-or-update>
        <docking v-if="dockingVisible" ref="docking" @refreshDataList="getDataList"></docking>
        <execute v-if="executeVisible" ref="execute" @refreshDataList="getDataList"></execute>
        <report v-if="reportVisible" ref="report" @refreshDataList="getDataList"></report>
        <finish v-if="finishVisible" ref="finish" @refreshDataList="getDataList"></finish>
        <detail v-if="detailVisible" ref="detail" @refreshDataList="getDataList"></detail>
        <finish-detail v-if="finishDetailVisible" ref="finishDetail" @refreshDataList="getDataList"></finish-detail>
        <export-detail v-if="exportDetailVisible" ref="exportDetail" ></export-detail>
    </div>
</template>

<script>
import AddOrUpdate from './project-add-or-update'
import Docking from './project-docking'
import Execute from './project-execute'
import Report from './project-report'
import Finish from './project-finish-add-or-update'
import Detail from './project-detail'
import FinishDetail from './project-finish-audit-detail'
import ExportDetail from './export-detail'


export default {
    data() {
        return {
            typeList: [],
            auditStatusList: [],
            fullscreenLoading: false,
            managerCapacity: this.$store.state.user.managerCapacity,
            placeholder: '请选择',
            dataForm: {
                key: '',
                auditStatus: '',
                projectDemandType: '',
                contactName: '',
                contactPhone: '',
                year: '',
                teamName: '',
                typeName: '',
                projectName: ''
            },
            dataList: [],
            pageIndex: 1,
            pageSize: 10,
            totalPage: 0,
            dataListLoading: false,
            dataListSelections: [],
            addOrUpdateVisible: false,
            dockingVisible: false,
            reportVisible: false,
            executeVisible: false,
            finishVisible: false,
            detailVisible: false,
            finishDetailVisible: false,
            exportDetailVisible: false,
        }
    },
    components: {
        AddOrUpdate,
        Docking,
        Execute,
        Report,
        Finish,
        Detail,
        FinishDetail,
        ExportDetail
    },
    activated() {
        this.queryPage()
        this.getTypeList()
        this.getAuditStatusList()
    },
    methods: {
        queryPage() {
            this.pageIndex = 1
            this.getDataList()
        },
        // 获取数据列表
        getDataList() {
            this.dataListLoading = true
            this.$http({
                url: this.$http.adornUrl('/admin/project/getPages'),
                method: 'post',
                data: this.$http.adornData({
                    'currentPage': this.pageIndex,
                    'pageSize': this.pageSize,
                    'key': this.dataForm.key,
                    'auditStatus': this.dataForm.auditStatus,
                    'projectDemandType': this.dataForm.projectDemandType,
                    'contactName': this.dataForm.contactName,
                    'contactPhone': this.dataForm.contactPhone,
                    'year': this.dataForm.year,
                    'teamName': this.dataForm.teamName,
                    'typeName': this.dataForm.typeName,
                    'projectName': this.dataForm.projectName
                })
            }).then(({data}) => {
                if (data && data.code === 0) {
                    this.dataList = data.obj.records
                    this.dataList.forEach(item => {
                        //修改，删除,判断是否为草稿或者审核不通过
                        item.updateEnable = item.auditStatus === 'pro_draft' || item.auditStatus === 'pro_reject'
                        //总结，月报，执行，判断状态为对接审核通过之后的状态
                        item.successEnable = item.auditStatus === 'pro_docking_success' || item.auditStatus === 'pro_finish_audit'  || item.auditStatus === 'pro_finish'
                        //对接，总结，月报，执行，判断状态为审核通过之后的状态
                        item.dockingEnable = item.auditStatus === 'pro_wait_docking'
                        //申报撤回，判断状态为审核中
                        item.recallEnable = item.auditStatus === 'pro_wait_audit'
                        //结项撤回，判断状态为审核通过
                        item.recallFinishEnable = item.auditStatus === 'pro_finish_audit'
                        //完成详情，判断状态为结束
                        item.finishDetailEnable = item.auditStatus === 'pro_finish'
                        //对接审核(申请启动)，判断状态为等待对接
                        item.dockingFinishEnable = item.auditStatus === 'pro_wait_docking'
                        //对接审核撤回，判断状态为对接审核
                        item.dockingAuditRecallEnable = item.auditStatus === 'pro_docking_audit'
                    })
                    this.totalPage = data.obj.total
                } else {
                    this.dataList = []
                    this.totalPage = 0
                }
                this.dataListLoading = false
            })
        },
        getAuditStatusList() {
            this.$http({
                url: this.$http.adornUrl(`/admin/dict/parent`),
                method: 'get',
                params: this.$http.adornParams({
                    code: 'project_status'
                })
            }).then(({data}) => {
                if (data && data.code === 0) {
                    this.auditStatusList = data.obj || []
                    if (this.managerCapacity === 'ASSOCIATION_ADMIN') {
                        this.auditStatusList = this.auditStatusList.filter(it => it.code !== 'pro_draft' && it.code !== 'pro_wait_audit' && it.code !== 'pro_reject')
                    }
                }
            })
        },
        getTypeList() {
            this.$http({
                url: this.$http.adornUrl(`/admin/project/type/getTypeList`),
                method: 'get',
                params: this.$http.adornParams()
            }).then(({data}) => {
                if (data && data.code === 0) {
                    this.typeList = data.obj || []
                }
            })
        },
        // 每页数
        sizeChangeHandle(val) {
            this.pageSize = val
            this.pageIndex = 1
            this.getDataList()
        },
        // 当前页
        currentChangeHandle(val) {
            this.pageIndex = val
            this.getDataList()
        },
        // 多选
        selectionChangeHandle(val) {
            this.dataListSelections = val
        },
        // 新增 / 修改
        addOrUpdateHandle(id) {
            this.addOrUpdateVisible = true
            this.$nextTick(() => {
                this.$refs.addOrUpdate.init(id)
            })
        },
        executeHandle(id) {
            this.executeVisible = true
            this.$nextTick(() => {
                this.$refs.execute.init(id)
            })
        },
        finishHandle(id) {
            this.finishVisible = true
            this.$nextTick(() => {
                this.$refs.finish.init(id)
            })
        },
        finishDetailHandle(id) {
            this.finishDetailVisible = true
            this.$nextTick(() => {
                this.$refs.finishDetail.init(id, true)
            })
        },
        //对接
        dockingHandle(id, projectDemandType) {
            this.dockingVisible = true
            this.$nextTick(() => {
                this.$refs.docking.init(id, projectDemandType)
            })
        },
        //月报
        reportHandle(id) {
            this.reportVisible = true
            this.$nextTick(() => {
                this.$refs.report.init(id)
            })
        },
        getDetail(id) {
            this.detailVisible = true
            this.$nextTick(() => {
                this.$refs.detail.init(id, true)
            })
        },
        // 重置
        resetForm() {
            this.$refs['dataForm']?.resetFields()
            this.getDataList()
        },
        // 提交
        render(id) {
            this.$confirm(`确定进行提交操作?`, '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning',
                closeOnClickModal: false
            }).then(() => {
                this.$http({
                    url: this.$http.adornUrl('/admin/project/renderById'),
                    method: 'get',
                    params: this.$http.adornParams({
                        'id': id
                    })
                }).then(({data}) => {
                    if (data && data.code === 0) {
                        this.$message({
                            message: '操作成功',
                            type: 'success',
                            duration: 1500,
                            onClose: () => {
                                this.getDataList()
                            }
                        })
                    } else {
                        this.$message.error(data.msg)
                    }
                })
            })
        },
        recallHandle(id, isFinish) {
            this.$confirm(`确定进行撤回操作?`, '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning',
                closeOnClickModal: false
            }).then(() => {
                this.$http({
                    url: this.$http.adornUrl('/admin/project/recall'),
                    method: 'get',
                    params: this.$http.adornParams({
                        'id': id,
                        'isFinish': isFinish
                    })
                }).then(({data}) => {
                    if (data && data.code === 0) {
                        this.$message({
                            message: '操作成功',
                            type: 'success',
                            duration: 1500,
                            onClose: () => {
                                this.getDataList()
                            }
                        })
                    } else {
                        this.$message.error(data.msg)
                    }
                })
            })
        },
        dockingAuditStartHandle(id, isFinish) {
            this.$confirm(`提交申请启动将提交协会审核，请确认项目需求资金或物资是否已满足，
点击确认提交审核。`, '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning',
                closeOnClickModal: false
            }).then(() => {
                this.$http({
                    url: this.$http.adornUrl('/admin/project/dockingAuditStart'),
                    method: 'get',
                    params: this.$http.adornParams({
                        'id': id
                    })
                }).then(({data}) => {
                    if (data && data.code === 0) {
                        this.$message({
                            message: '操作成功',
                            type: 'success',
                            duration: 1500,
                            onClose: () => {
                                this.getDataList()
                            }
                        })
                    } else {
                        this.$message.error(data.msg)
                    }
                })
            })
        },
        auditDockingRecallHandle(id) {
            this.$confirm(`确定进行撤回操作?`, '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning',
                closeOnClickModal: false
            }).then(() => {
                this.$http({
                    url: this.$http.adornUrl('/admin/project/dockingAuditRecall'),
                    method: 'get',
                    params: this.$http.adornParams({
                        'id': id
                    })
                }).then(({data}) => {
                    if (data && data.code === 0) {
                        this.$message({
                            message: '操作成功',
                            type: 'success',
                            duration: 1500,
                            onClose: () => {
                                this.getDataList()
                            }
                        })
                    } else {
                        this.$message.error(data.msg)
                    }
                })
            })
        },
        // 导出
        exportHandle() {
            this.fullscreenLoading = true;
            this.$http({
                url: this.$http.adornUrl('/admin/project/exportProjectNew'),
                method: 'post',
                responseType: 'arraybuffer',
                data: this.$http.adornData({
                    'key': this.dataForm.key,
                    'auditStatus': this.dataForm.auditStatus,
                    'projectDemandType': this.dataForm.projectDemandType,
                    'contactName': this.dataForm.contactName,
                    'contactPhone': this.dataForm.contactPhone,
                    'year': this.dataForm.year,
                    'teamName': this.dataForm.teamName,
                    'typeName': this.dataForm.typeName,
                    'projectName': this.dataForm.projectName
                })
            }).then(({data}) => {
                if (data.code && data.code !== 0) {
                    this.$message.error('导出失败')
                } else {
                    this.fullscreenLoading = false
                    let blob = new Blob([data], {
                        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8'
                    })
                    let objectUrl = URL.createObjectURL(blob)
                    let a = document.createElement('a')
                    // window.location.href = objectUrl
                    a.href = objectUrl
                    a.download = '参与公益伙伴计划.xlsx'
                    a.click()
                    URL.revokeObjectURL(objectUrl)
                    this.$message({
                        type: 'success',
                        message: '导出成功'
                    })
                }
            })
        },
        // 删除
        deleteHandle(id) {
            var ids = id ? [id] : this.dataListSelections.map(item => {
                return item.id
            })
            this.$confirm(`确定进行[${id ? '删除' : '批量删除'}]操作?`, '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.$http({
                    url: this.$http.adornUrl('/admin/project/removeByIds'),
                    method: 'get',
                    params: this.$http.adornParams({
                        'ids': ids.join(',')
                    })
                }).then(({data}) => {
                    if (data && data.code === 0) {
                        this.$message({
                            message: '操作成功',
                            type: 'success',
                            duration: 1500,
                            onClose: () => {
                                this.getDataList()
                            }
                        })
                    } else {
                        this.$message.error(data.msg)
                    }
                })
            })
        },
        auditHandle(id, name, status) {
            if (!id && this.dataListSelections.length === 0) {
                this.$message.warning('未选中需要审核的申报记录！')
                return
            }
            if (!id) {
                let ids = this.dataListSelections.map(item => {
                    return item.id
                })
                id = ids.join(',')
            }
            this.$confirm(`确定${status ? '审核通过' : '驳回'}` + (name ? '"' + name + '"' : '') + '项目申报？', '系统提示', {
                customClass: 'audit_pass_msg_box',
                confirmButtonText: '确认',
                cancelButtonText: '取消',
                showInput: !status,
                inputPlaceholder: '请输入驳回意见……',
                inputType: 'textarea',
                closeOnClickModal: false
            }).then((val) => {
                this.$http({
                    url: this.$http.adornUrl('/admin/project/audit'),
                    method: 'get',
                    params: this.$http.adornParams({
                        'ids': id,
                        'status': status,
                        'remark': val.value
                    })
                }).then(({data}) => {
                    if (data && data.code === 0) {
                        this.$message.success(status ? '审核通过成功！' : '驳回成功！')
                        this.queryPage()
                    } else {
                        this.$message.error(status ? '审核通过失败！' : '驳回失败！' + data.msg)
                    }
                })
            }).catch(() => {
                this.$message({
                    type: 'info',
                    message: '操作取消'
                });
            });
            setTimeout(() => {
                var content = document.getElementsByClassName('audit_pass_msg_box')[0].childNodes[1]
                content.style.paddingLeft = '60px'
                content.style.paddingRight = '60px'
                var submitBtn = document.getElementsByClassName('audit_pass_msg_box')[0].childNodes[2].childNodes[1]
                submitBtn.style.backgroundColor = '#F56C6C'
                submitBtn.style.borderColor = '#F56C6C'
            }, 50)
        },
        // 下载压缩文件
        downloadZip () {
            var ids = this.dataListSelections.map(item => {
                return item.id
            })
            console.log(ids)
            this.exportDetailVisible = true
            this.$nextTick(() => {
                this.$refs.exportDetail.init(ids.join(','))
            })
        },
        displayHandle(id, name, status) {
            if (!id && this.dataListSelections.length === 0) {
                this.$message.warning('未选中需要修改为前端展示的记录！')
                return
            }
            if (!id) {
                let ids = this.dataListSelections.map(item => {
                    return item.id
                })
                id = ids.join(',')
            }
            this.$confirm(`确定${status ? '设为前端展示' : '设为前端不展示'}` + (name ? '"' + name + '"' : '') + '？', '系统提示', {
                confirmButtonText: '确认',
                cancelButtonText: '取消',
                closeOnClickModal: false
            }).then((val) => {
                this.$http({
                    url: this.$http.adornUrl('/admin/project/displayStatus'),
                    method: 'get',
                    params: this.$http.adornParams({
                        'ids': id,
                        'status': status
                    })
                }).then(({
                             data
                         }) => {
                    if (data && data.code === 0) {
                        this.$message.success(status ? '设为前端展示成功！' : '设为前端不展示成功！')
                        this.queryPage()
                    } else {
                        this.$message.error(status ? '设为前端展示失败！' : '设为前端不展示失败！' + data.msg)
                    }
                })
            }).catch(() => {
                this.$message({
                    type: 'info',
                    message: '操作取消'
                });
            });
            setTimeout(() => {
            }, 50)
        },
        fromDownload (id) {
            this.fullscreenLoading = true;
            this.$http({
                url: this.$http.adornUrl('/admin/project/projectApplicationForm'),
                method: 'get',
                responseType: 'arraybuffer',
                params: this.$http.adornParams({
                    'projectId': id
                })
            }).then(({data}) => {
                if (data.code && data.code !== 0) {
                    this.$message.error('导出失败')
                } else {
                    this.fullscreenLoading = false
                    let blob = new Blob([data], {
                        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8'
                    })
                    let objectUrl = URL.createObjectURL(blob)
                    let a = document.createElement('a')
                    // window.location.href = objectUrl
                    a.href = objectUrl
                    a.download = '项目申请书.docx'
                    a.click()
                    URL.revokeObjectURL(objectUrl)
                    this.$message({
                        type: 'success',
                        message: '导出成功'
                    })
                }
            })
        },
        infoHandle() {
            window.open('https://www.yuque.com/ccbb/gxosh6/uoiag1sovkc67ulq?singleDoc# ', '_blank')
        },
    }
}
</script>
