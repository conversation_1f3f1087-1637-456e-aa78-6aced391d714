<template>
  <el-dialog
      :title="!dataForm.id ? '新增' : '修改'"
      :close-on-click-modal="false"
      :visible.sync="visible"
      append-to-body>
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmit()"
             label-width="140px" style="margin-right: 30px; margin-left: -10px">
      <el-row :gutter="20">
        <el-col :span="11">
          <el-form-item label="企业名称" prop="corpName" :error="corpNameError">
            <el-input v-model="dataForm.corpName" placeholder="企业名称"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="1">
          <el-button icon="el-icon-search" circle @click="getInfo(dataForm.corpName)"></el-button>
        </el-col>
        <el-col :span="12">
          <el-form-item label="统一社会信用代码" prop="socialCreditCode">
            <el-input v-model="dataForm.socialCreditCode" placeholder="统一社会信用代码"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="企业类型" prop="corpType">
            <el-dict code="welfare_project_type" clearable v-model="dataForm.corpType"></el-dict>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="联系人" prop="linkMan" :error="linkManError">
            <el-input v-model="dataForm.linkMan" placeholder="联系人"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="联系电话" prop="linkPhone" :error="linkPhoneError">
            <el-input v-model="dataForm.linkPhone" placeholder="联系电话"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="邮箱" prop="email" :error="emailError">
            <el-input v-model="dataForm.email" placeholder="邮箱"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="启用状态" prop="status" :error="statusError">
            <el-radio-group v-model="dataForm.status">
              <el-radio :label="true">启用</el-radio>
              <el-radio :label="false">禁用</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="爱心企业年份标签" prop="years">
            <el-select v-model="dataForm.years" placeholder="请选择" :multiple="true" clearable style="width:100%">
              <el-option
                  v-for="item in yearsList"
                  :key="item.year"
                  :label="item.year"
                  :value="item.year">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import {is8lPhone, isMobile} from "@/utils/validate";

export default {
  data() {
    const validateContactPhone = (rule, value, callback) => {
      if (!isMobile(value) && !is8lPhone(value)) {
        callback(new Error('联系电话须为手机号或区号+8位座机号例如051288888888或0512-88888888'))
      } else {
        callback()
      }
    }
    return {
      visible: false,
      enterpriseList: [],
      yearsList: [],
      initForm: {
        id: null,
        version: null,
        corpName: '',
        socialCreditCode: '',
        corpType: '',
        linkMan: '',
        linkPhone: '',
        email: '',
        status: true,
        loveEnterpriseYears: '',
        years: []
      },
      dataForm: {},
      dataRule: {
        corpName: [
          {required: true, message: '企业名称不能为空', trigger: 'blur'}
        ],
        // socialCreditCode: [
        //   {required: true, message: '统一社会信用代码不能为空', trigger: 'blur'}
        // ],
        // corpType: [
        //   {required: true, message: '企业类型不能为空', trigger: 'blur'}
        // ],
        // linkMan: [
        //   {required: true, message: '联系人不能为空', trigger: 'blur'}
        // ],
        status: [
          {required: true, message: '启用状态不能为空', trigger: 'blur'}
        ]
      },
      corpNameError: null,
      socialCreditCodeError: null,
      corpTypeError: null,
      linkManError: null,
      linkPhoneError: null,
      emailError: null,
      statusError: null
    }
  },
  methods: {
    init(id) {
      this.dataForm = _.cloneDeep(this.initForm)
      this.dataForm.id = id || null
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm']?.resetFields()
        this.initYearsList()
        if (this.dataForm.id) {
          this.$http({
            url: this.$http.adornUrl(`/admin/project/company`),
            method: 'get',
            params: this.$http.adornParams({id: this.dataForm.id})
          }).then(({data}) => {
            if (data && data.code === 0) {
              data.obj.years = data.obj.loveEnterpriseYears ? data.obj.loveEnterpriseYears.split(',') : []
              this.dataForm = data.obj
            }
          })
        }
      })
    },
    // 表单提交
    dataFormSubmit() {
      this.dataForm.loveEnterpriseYears = this.dataForm.years && this.dataForm.years.length > 0 ? this.dataForm.years.join(',') : null
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.$http({
            url: this.$http.adornUrl(`/admin/project/company/${!this.dataForm.id ? 'saveCompany' : 'update'}`),
            method: 'post',
            data: this.$http.adornData(this.dataForm)
          }).then(({data}) => {
            if (data && data.code === 0) {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 1500,
                onClose: () => {
                  this.visible = false
                  this.$emit('refreshDataList')
                }
              })
            } else if (data && data.code === 303) {
              for (let it of data.obj) {
                this[`${it.field}Error`] = it.message
              }
            } else {
              this.$message.error(data.msg)
            }
          })
        }
      })
    },
    initYearsList() {
      this.$http({
        url: this.$http.adornUrl("/admin/zyz/project-year/yearsList"),
        method: "get"
      }).then(({data}) => {
        this.yearsList = data.obj
      })
    },
    getInfo(corpName) {
      if (!corpName) {
        this.$message.error('请输入社会统一信用代码或企业名称')
        return true
      }
      this.loading = true
      this.$http({
        url: this.$http.adornUrl("/admin/project/company/getCompanyInfo"),
        method: "get",
        params: this.$http.adornParams({
          corpName
        })
      }).then(res => {
        if (res.data.code == 0) {
          this.loading = false
          if (!res.obj) {
            console.log('data：' + res.data.corpName)
            this.dataForm.corpName = res.obj.corpName
            this.dataForm.socialCreditCode = res.obj.socialCreditCode
          } else {
            this.dataForm.corpName = ''
            this.dataForm.socialCreditCode = ''
            this.$message.error('未查询相关信息请输入正确社会统一信用代码或企业名称')
          }
        } else {
          this.loading = false
        }
      })
    }
  }
}
</script>
