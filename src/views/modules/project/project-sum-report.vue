<template>
    <div class="mod-config">
        <el-form :inline="true" :model="dataForm" ref="dataForm" @keyup.enter.native="queryPage()">
            <el-form-item label="项目类型" prop="typeName">
                <el-select class="width185" v-model="dataForm.typeName" clearable
                           :placeholder="placeholder" filterable style="width: 100%">
                    <el-option
                            v-for="item in typeList"
                            :key="item.code"
                            :label="item.name"
                            :value="item.name">
                    </el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="项目年度" prop="year">
                <el-dict :code="'pro_year'" v-model="dataForm.year"></el-dict>
            </el-form-item>
            <el-form-item label="项目状态" prop="auditStatus">
                <el-select v-model="dataForm.auditStatus" clearable :placeholder="placeholder">
                    <el-option
                            v-for="item in auditStatusList"
                            :key="item.code"
                            :label="item.name"
                            :value="item.code">
                    </el-option>
                </el-select>
            </el-form-item>
            <el-form-item>
                <el-button icon="el-icon-search" type="warning" @click="queryPage()">查询</el-button>
                <el-button @click="resetForm()" icon="el-icon-refresh-left">重置</el-button>
            </el-form-item>
            <div class="f-s-c" style="padding: 15px 0;display: flex;justify-content:flex-end;align-items: center ">
                <div>
                    <el-button icon="el-icon-download" type="success" v-loading.fullscreen.lock="fullscreenLoading"
                               @click="exportHandle()">导出</el-button>
                </div>
            </div>
        </el-form>

        <div>
            <!-- 循环生成 el-table -->
            <div v-for="(table,index) in tables" :key="table.tableName">
                <h3 :class="sstt[index]">{{ table.tableName }}</h3>
                <el-table :data="table.tableData" border>
                    <el-table-column
                        prop="projectName"
                        header-align="center"
                        align="center"
                        min-width="200"
                        label="项目名称">
                        <template slot-scope="scope">
                            <a style="cursor: pointer" @click="getDetail(scope.row.id)">{{ scope.row.projectName }}</a>
                        </template>
                    </el-table-column>
                    <el-table-column
                        prop="projectSummary"
                        header-align="center"
                        align="center"
                        min-width="150"
                        :show-overflow-tooltip="true"
                        label="项目概况">
                    </el-table-column>
                    <el-table-column
                        header-align="center"
                        align="center"
                        width="150"
                        :show-overflow-tooltip="true"
                        label="项目需求">
                        <template slot-scope="scope">
                            {{scope.row.projectDemandTypeText}}{{scope.row.fundsRequired}}{{'元'}}
                        </template>
                    </el-table-column>
                    <el-table-column
                        prop="applyUnitName"
                        header-align="center"
                        align="center"
                        min-width="80"
                        label="申报单位">
                    </el-table-column>
                    <el-table-column
                        prop="name"
                        header-align="center"
                        align="center"
                        min-width="80"
                        label="实施团队">
                    </el-table-column>
                    <el-table-column
                        prop="contactName"
                        header-align="center"
                        align="center"
                        min-width="120"
                        label="联系方式">
                        <template slot-scope="scope">
                            <p> {{scope.row.contactName}}</p>
                            <p>  {{scope.row.contactPhone}}</p>
                        </template>
                    </el-table-column>
                    <el-table-column
                        prop="amount"
                        header-align="center"
                        align="center"
                        label="对接金额(元）">
                    </el-table-column>
                    <el-table-column
                        prop="corpName"
                        header-align="center"
                        align="center"
                        min-width="150"
                        label="对接企业">
                    </el-table-column>
                    <el-table-column
                        prop="projectSummary"
                        header-align="center"
                        min-width="200"
                        align="center"
                        :show-overflow-tooltip="true"
                        label="团队服务内容">
                    </el-table-column>
                    <el-table-column
                        prop="introduction"
                        header-align="center"
                        align="center"
                        min-width="200"
                        :show-overflow-tooltip="true"
                        label="团队介绍">
                    </el-table-column>
                    <el-table-column
                        prop="founded"
                        header-align="center"
                        align="center"
                        min-width="100"
                    >
                        <template slot="header">
                            <div>成立时间/</div>
                            <div>是否民政局注册</div>
                        </template>
                        <template slot-scope="scope">
                            <p> {{scope.row.founded}}</p>
                            <p>  {{convertBool(scope.row.civilRegistration)}}</p>
                        </template>
                    </el-table-column>
                    <el-table-column
                        prop="projectCode"
                        header-align="center"
                        min-width="100"
                        align="center"
                        label="项目编号">
                    </el-table-column>

                    <el-table-column
                        prop="auditStatusText"
                        header-align="center"
                        align="center"
                        :show-overflow-tooltip="true"
                        label="项目状态">
                    </el-table-column>

<!--                    <el-table-column-->
<!--                        prop="display"-->
<!--                        header-align="center"-->
<!--                        align="center"-->
<!--                        label="是否前端展示">-->
<!--                    </el-table-column>-->
<!--                    <el-table-column-->
<!--                        prop="attachment"-->
<!--                        fixed="right"-->
<!--                        header-align="center"-->
<!--                        align="center"-->
<!--                        min-width="500"-->
<!--                        :show-overflow-tooltip="true"-->
<!--                        label="开展情况（月报上传情况）">-->
<!--                        <template slot-scope="scope">-->
<!--                            <div v-for="(item,index) in scope.row.attachment.split(';')" :key="index">-->
<!--                                {{item}}-->
<!--                            </div>-->
<!--                        </template>-->
<!--                    </el-table-column>-->
                </el-table>
            </div>
        </div>
        <detail v-if="detailVisible" ref="detail" @refreshDataList="getDataList"></detail>
    </div>
</template>

<script>

import Detail from './project-detail'
import moment from 'moment'
export default {
    data() {
        return {
            sstt:["ss1","ss2","ss3","ss4","ss5","ss6","ss7","ss8","ss9","ss10"],
            typeList: [],
            auditStatusList: [],
            fullscreenLoading: false,
            managerCapacity: this.$store.state.user.managerCapacity,
            placeholder: '请选择',
            dataForm: {
                auditStatus: '',
                year: moment().subtract(1,'year').format('YYYY'),
                typeName: '',

            },
            dataList: [],
            dataListLoading: false,
            tables: [], // 存储表格数据的数组
            returnMap:null,
            detailVisible: false,
        }
    },
    created() {

    },
    components: {
        Detail,
        moment
    },
    activated() {
        console.log('activated')
        this.getTypeList()
        this.getAuditStatusList()
        this.queryPage()

    },
    methods: {
        queryPage() {
            console.log('queryPage')
            this.getDataList()
        },
        // 获取数据列表
        getDataList() {
            this.dataListLoading = true
            this.$http({
                url: this.$http.adornUrl('/admin/project/getProjectSumReport'),
                method: 'post',
                data: this.$http.adornData({
                    'auditStatus': this.dataForm.auditStatus,
                    'year': this.dataForm.year,
                    'typeName': this.dataForm.typeName
                })
            }).then(({data}) => {
                if (data && data.code === 0) {
                    this.returnMap = data.obj
                    this.generateTables()
                } else {
                    this.returnMap = null
                }
                this.dataListLoading = false
            })
        },
        getAuditStatusList() {
            this.$http({
                url: this.$http.adornUrl(`/admin/dict/parent`),
                method: 'get',
                params: this.$http.adornParams({
                    code: 'project_status'
                })
            }).then(({data}) => {
                if (data && data.code === 0) {
                    this.auditStatusList = data.obj || []
                    if (this.managerCapacity === 'ASSOCIATION_ADMIN') {
                        this.auditStatusList = this.auditStatusList.filter(it => it.code !== 'pro_draft' && it.code !== 'pro_wait_audit' && it.code !== 'pro_reject')
                    }
                }
            })
        },
        getTypeList() {
            this.$http({
                url: this.$http.adornUrl(`/admin/project/type/getTypeList`),
                method: 'get',
                params: this.$http.adornParams()
            }).then(({data}) => {
                if (data && data.code === 0) {
                    this.typeList = data.obj || []
                }
            })
        },

        // 重置
        resetForm() {
            this.$refs['dataForm']?.resetFields()
            this.getDataList()
        },
        generateTables() {
            this.tables=[]
            Object.keys(this.returnMap).forEach((key, index) => {
                const tableName = key;
                console.log('tableData'+key)
                const tableData = this.returnMap[key];
                console.log('tableData'+tableData.length)
                this.tables.push({ tableName, tableData });
            });
        },
        getDetail(id) {
            this.detailVisible = true
            this.$nextTick(() => {
                this.$refs.detail.init(id, true)
            })
        },
        convertBool(value) {
            return value==true? '是':'否'
        },
        // 导出
        exportHandle() {
            this.fullscreenLoading = true;
            this.$http({
                url: this.$http.adornUrl('/admin/project/projectSumReportExport'),
                method: 'post',
                responseType: 'arraybuffer',
                data: this.$http.adornData({
                    'auditStatus': this.dataForm.auditStatus,
                    'year': this.dataForm.year,
                    'typeName': this.dataForm.typeName,
                })
            }).then(({data}) => {
                if (data.code && data.code !== 0) {
                    this.$message.error('导出失败')
                } else {
                    this.fullscreenLoading = false
                    let blob = new Blob([data], {
                        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8'
                    })
                    let objectUrl = URL.createObjectURL(blob)
                    let a = document.createElement('a')
                    // window.location.href = objectUrl
                    a.href = objectUrl
                    a.download = '公益伙伴项目.xlsx'
                    a.click()
                    URL.revokeObjectURL(objectUrl)
                    this.$message({
                        type: 'success',
                        message: '导出成功'
                    })
                }
            })
        },
    }
}
</script>
<style lang="scss" scoped>
.ss1{
    background-color: #ffefec;
}
.ss2{
    background-color:#e6f8f3;
}
.ss3{
    background-color: #f1e9ff;
}
.ss4{
    background-color: #fef9e3;
}
.ss5{
    background-color:#def5ff;
}
.ss6{
    background-color: #ffefec;
}
.ss7{
    background-color:#e6f8f3;
}
.ss8{
    background-color: #f1e9ff;
}
.ss9{
    background-color: #fef9e3;
}
.ss10{
    background-color:#def5ff;
}
</style>
