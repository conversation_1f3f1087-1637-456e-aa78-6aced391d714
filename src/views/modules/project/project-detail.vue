<template>
  <el-dialog
      :title="this.isDetail ? '项目详情 ' : '申报审核'"
      :close-on-click-modal="false"
      width="90%"
      :visible.sync="visible">
    <el-form :model="dataForm" ref="dataForm" @keyup.enter.native="dataFormSubmit()"
             label-width="130px">
      <el-row>
        <el-col :span="12">
          <el-row>
            <el-col :span="24">
              <el-form-item label-width="10px">
                <div style="display: flex; justify-content: flex-start">
                  <div style="width: 5px;background-color: #00feff;margin-right: 10px; margin-top: 6px; margin-bottom: 6px"></div>
                  <div style="font-weight: bold">项目基本信息</div>
                </div>
              </el-form-item>
            </el-col>
              <el-col :span="24">
                  <el-form-item label="申报单位" prop="applyUnitName" :error="applyUnitNameError">
                      <el-input readonly v-model="dataForm.applyUnitName" placeholder="申报单位"></el-input>
                  </el-form-item>
              </el-col>
            <el-col :span="24">
              <el-form-item label="项目名称" prop="projectName" :error="projectNameError">
                <el-input readonly v-model="dataForm.projectName" placeholder="项目名称"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="实施地点" prop="implementationLocation" :error="implementationLocationError">
                <el-input readonly v-model="dataForm.implementationLocation" placeholder="实施地点"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="单位所属区域" prop="orgCode" :error="orgCodeError">
                <el-dict :code="'project_org_code'" v-model="dataForm.orgCode" :disabled="true"></el-dict>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="项目类型" prop="type" :error="typeError">
                <el-checkbox-group v-model="dataForm.type" :disabled="true">
                  <el-checkbox v-for="it in typeList" :label="it.code" :key="it.code">{{ it.name }}</el-checkbox>
                </el-checkbox-group>
              </el-form-item>
            </el-col>
<!--            <el-col :span="24">-->
<!--              <el-form-item label="受益人类别" prop="beneficiaryCategory" :error="beneficiaryCategoryError">-->
<!--                <el-checkbox-group v-model="dataForm.beneficiaryCategory" :disabled="true">-->
<!--                  <el-checkbox v-for="it in beneficiaryCategoryList" :label="it.code" :key="it.code">{{ it.name }}-->
<!--                  </el-checkbox>-->
<!--                </el-checkbox-group>-->
<!--              </el-form-item>-->
<!--            </el-col>-->
            <el-col :span="24">
              <el-form-item label="项目受益人" prop="projectBeneficiaries" :error="projectBeneficiariesError">
                <el-input readonly v-model="dataForm.projectBeneficiaries" placeholder="项目受益人"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="项目需求类型" prop="projectDemandType">
                <el-radio-group v-model="dataForm.projectDemandType"  :disabled="true">
                  <el-radio :label="'project_materials'">物资</el-radio>
                  <el-radio :label="'project_capital'">资金</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
              <el-col :span="24">
                  <el-form-item label="项目预算资金(元)" prop="fundsRequired">
                      <el-input readonly v-model="dataForm.fundsRequired" placeholder="项目预算资金"></el-input>
                  </el-form-item>
              </el-col>
            <el-col :span="24">
              <el-form-item label="项目年度" prop="year" :error="yearError">
                <el-dict :code="'pro_year'" v-model="dataForm.year" :disabled="true"></el-dict>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="项目周期" prop="timeRange">
                <el-date-picker
                    style="width: 100%"
                    :disabled="true"
                    v-model="dataForm.timeRange"
                    clearable
                    type="daterange"
                    value-format="yyyy-MM-dd"
                    align="right"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期">
                </el-date-picker>
              </el-form-item>
            </el-col>
<!--              <el-col :span="24">-->
<!--                  <el-form-item label="排序" prop="sequence">-->
<!--                      <el-input-number v-model="dataForm.sequence" placeholder="排序"   :disabled="true"/>-->
<!--                  </el-form-item>-->
<!--              </el-col>-->
              <el-col :span="24">
                  <el-form-item label="资金/物资使用计划" prop="moneyMaterialPlan">
                      <el-input v-model="dataForm.moneyMaterialPlan" placeholder="资金/物资使用计划"
                                maxlength="255"  show-word-limit  type="textarea" :rows="3"></el-input>
                  </el-form-item>
              </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <el-form-item label-width="10px">
                <div style="display: flex; justify-content: flex-start">
                  <div style="width: 5px;background-color: #00feff;margin-right: 10px; margin-top: 6px; margin-bottom: 6px"></div>
                  <div style="font-weight: bold">项目联系人信息</div>
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="姓名" prop="contactName" :error="contactNameError">
                <el-input readonly v-model="dataForm.contactName" placeholder="联系人姓名"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="电话" prop="contactPhone" :error="contactPhoneError">
                <el-input readonly v-model="dataForm.contactPhone" placeholder="联系人电话"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="身份证号码" prop="contactIdCard" :error="contactIdCardError">
                <el-input readonly v-model="dataForm.contactIdCard" placeholder="联系人身份证号码"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row style="margin-bottom: 20px">
            <el-col :span="24">
              <el-form-item label-width="10px">
                <div style="display: flex; justify-content: flex-start">
                  <div style="width: 5px;background-color: #00feff;margin-right: 10px; margin-top: 6px; margin-bottom: 6px"></div>
                  <div style="font-weight: bold">项目预算明细</div>
                  <span style="margin-left: 10px">预算金额：{{dataForm.fundsRequired}}</span>
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-table
                  :data="dataForm.projectBudgetDetailList"
                  border
                  height="180"
                  style="width: 100%">
                <el-table-column prop="name" label="名称" align="center">
                  <template slot-scope="scope">
                    <span v-show="!scope.row.editFlag">{{scope.row.name}}</span>
                    <el-input v-show="scope.row.editFlag"
                              v-model="scope.row.name">
                    </el-input>
                  </template>
                </el-table-column>
                <el-table-column
                    prop="amount"
                    header-align="center"
                    align="center"
                    label="金额(元)">
                  <template slot-scope="scope">
                    <span v-show="!scope.row.editFlag">{{scope.row.amount}}</span>
                    <el-input v-show="scope.row.editFlag"
                              v-model="scope.row.amount">
                    </el-input>
                  </template>
                </el-table-column>
                <el-table-column
                    prop="description"
                    header-align="center"
                    align="center"
                    label="说明">
                  <template slot-scope="scope">
                    <span v-show="!scope.row.editFlag">{{scope.row.description}}</span>
                    <el-input v-show="scope.row.editFlag"
                              v-model="scope.row.description">
                    </el-input>
                  </template>
                </el-table-column>
              </el-table>
            </el-col>
          </el-row>
<!--          <el-row style="margin-bottom: 20px">-->
<!--            <el-col :span="24">-->
<!--              <el-form-item label-width="10px">-->
<!--                <div style="display: flex; justify-content: flex-start">-->
<!--                  <div style="width: 5px;background-color: #00feff;margin-right: 10px; margin-top: 6px; margin-bottom: 6px"></div>-->
<!--                  <div style="font-weight: bold">活动计划安排</div>-->
<!--                </div>-->
<!--              </el-form-item>-->
<!--            </el-col>-->
<!--            <el-col :span="24">-->
<!--              <el-table-->
<!--                  :data="dataForm.projectActivityPlanList"-->
<!--                  border-->
<!--                  height="180"-->
<!--                  style="width: 100%">-->
<!--                <el-table-column-->
<!--                    prop="projectName"-->
<!--                    header-align="center"-->
<!--                    align="center"-->
<!--                    label="活动开展时间">-->
<!--                  <template slot-scope="scope">-->
<!--                    <el-date-picker-->
<!--                        v-if="scope.row.editFlag"-->
<!--                        ref="gain"-->
<!--                        v-model="scope.row.activityTime"-->
<!--                        type="date"-->
<!--                        format="yyyy-MM-dd"-->
<!--                        value-format="yyyy-MM-dd"-->
<!--                        style="width: 100%"-->
<!--                    />-->
<!--                    <span v-else>{{ scope.row.activityTime }}</span>-->
<!--                  </template>-->
<!--                </el-table-column>-->
<!--                <el-table-column-->
<!--                    header-align="center"-->
<!--                    align="center"-->
<!--                    label="受益人数(人)">-->
<!--                  <template slot-scope="scope">-->
<!--                    <span v-show="!scope.row.editFlag">{{scope.row.beneficiaryNum}}</span>-->
<!--                    <el-input v-show="scope.row.editFlag"-->
<!--                              v-model="scope.row.beneficiaryNum">-->
<!--                    </el-input>-->
<!--                  </template>-->
<!--                </el-table-column>-->
<!--                <el-table-column-->
<!--                    header-align="center"-->
<!--                    align="center"-->
<!--                    label="参与人数(人)">-->
<!--                  <template slot-scope="scope">-->
<!--                    <span v-show="!scope.row.editFlag">{{scope.row.participantNum}}</span>-->
<!--                    <el-input v-show="scope.row.editFlag"-->
<!--                              v-model="scope.row.participantNum">-->
<!--                    </el-input>-->
<!--                  </template>-->
<!--                </el-table-column>-->
<!--              </el-table>-->
<!--            </el-col>-->
<!--          </el-row>-->
<!--          <el-row style="margin-bottom: 30px">-->
<!--            <el-col :span="24">-->
<!--              <el-form-item label-width="10px">-->
<!--                  <div style="display: flex; justify-content: flex-start">-->
<!--                    <div style="width: 5px;background-color: #00feff;margin-right: 10px; margin-top: 6px; margin-bottom: 6px"></div>-->
<!--                    <div style="font-weight: bold">资金/物资使用计划</div>-->
<!--                  </div>-->
<!--              </el-form-item>-->
<!--            </el-col>-->
<!--            <el-col :span="24">-->
<!--              <el-table-->
<!--                  :data="dataForm.projectMoneyMaterialPlanList"-->
<!--                  border-->
<!--                  height="180"-->
<!--                  style="width: 100%;">-->
<!--                <el-table-column-->
<!--                    prop="activityDate"-->
<!--                    header-align="center"-->
<!--                    align="center"-->
<!--                    label="活动日期">-->
<!--                  <template slot-scope="scope">-->
<!--                    <el-date-picker-->
<!--                        v-if="scope.row.editFlag"-->
<!--                        ref="gain"-->
<!--                        v-model="scope.row.activityDate"-->
<!--                        type="date"-->
<!--                        format="yyyy-MM-dd"-->
<!--                        value-format="yyyy-MM-dd"-->
<!--                        style="width: 100%"/>-->
<!--                    <span v-else>{{ scope.row.activityDate }}</span>-->
<!--                  </template>-->
<!--                </el-table-column>-->
<!--                <el-table-column-->
<!--                    header-align="center"-->
<!--                    align="center"-->
<!--                    label="支出(元)">-->
<!--                  <template slot-scope="scope">-->
<!--                    <span v-show="!scope.row.editFlag">{{scope.row.expenses}}</span>-->
<!--                    <el-input v-show="scope.row.editFlag"-->
<!--                              v-model="scope.row.expenses">-->
<!--                    </el-input>-->
<!--                  </template>-->
<!--                </el-table-column>-->
<!--                <el-table-column-->
<!--                    header-align="center"-->
<!--                    align="center"-->
<!--                    label="说明">-->
<!--                  <template slot-scope="scope">-->
<!--                    <span v-show="!scope.row.editFlag">{{scope.row.description}}</span>-->
<!--                    <el-input v-show="scope.row.editFlag"-->
<!--                              v-model="scope.row.description">-->
<!--                    </el-input>-->
<!--                  </template>-->
<!--                </el-table-column>-->
<!--              </el-table>-->
<!--            </el-col>-->
<!--          </el-row>-->
        </el-col>
        <el-col :span="12">
          <el-col :span="24">
            <el-form-item label-width="10px">
              <div style="display: flex; justify-content: flex-start">
                <div style="width: 5px;background-color: #00feff;margin-right: 10px; margin-top: 6px; margin-bottom: 6px"></div>
                <div style="font-weight: bold">项目详细信息</div>
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="项目概况" prop="projectSummary" :error="projectSummaryError">
              <el-input readonly type="textarea" :rows="3" v-model="dataForm.projectSummary" placeholder="项目概况"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="项目背景" prop="projectBackground" :error="projectBackgroundError">
              <el-input readonly type="textarea" :rows="3" v-model="dataForm.projectBackground" placeholder="项目背景"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="项目目标" prop="overallObjective" :error="overallObjectiveError">
              <el-input readonly type="textarea" :rows="3" v-model="dataForm.overallObjective" placeholder="项目目标"></el-input>
            </el-form-item>
          </el-col>
<!--          <el-col :span="24">-->
<!--            <el-form-item label="具体目标" prop="specificObjective" :error="specificObjectiveError">-->
<!--              <el-input readonly type="textarea" :rows="3" v-model="dataForm.specificObjective" placeholder="具体目标"></el-input>-->
<!--            </el-form-item>-->
<!--          </el-col>-->
          <el-col :span="24">
            <el-form-item label="项目实施计划" prop="prePlanningService" :error="prePlanningServiceError">
              <el-input readonly type="textarea" :rows="3" v-model="dataForm.prePlanningService" placeholder="简述项目实施主要内容、具体做法等，包括前期策划服务、中期实施计划、后期评估机制等"></el-input>
            </el-form-item>
          </el-col>
<!--          <el-col :span="24">-->
<!--            <el-form-item label="中期实施执行" prop="midTermImplementation" :error="midTermImplementationError">-->
<!--              <el-input readonly type="textarea" :rows="3" v-model="dataForm.midTermImplementation"-->
<!--                        placeholder="中期实施执行"></el-input>-->
<!--            </el-form-item>-->
<!--          </el-col>-->
<!--          <el-col :span="24">-->
<!--            <el-form-item label="后期评估机制" prop="postEvaluationMechanism" :error="postEvaluationMechanismError">-->
<!--              <el-input readonly type="textarea" :rows="3" v-model="dataForm.postEvaluationMechanism"-->
<!--                        placeholder="后期评估机制"></el-input>-->
<!--            </el-form-item>-->
<!--          </el-col>-->
<!--          <el-col :span="24">-->
<!--            <el-form-item label="与捐赠方的关联" prop="donorRelationship" :error="donorRelationshipError">-->
<!--              <el-input readonly type="textarea" :rows="3" v-model="dataForm.donorRelationship" placeholder="与捐赠方的关联"></el-input>-->
<!--            </el-form-item>-->
<!--          </el-col>-->
<!--          <el-col :span="24">-->
<!--            <el-form-item label="项目反馈机制" prop="projectFeedbackMechanism" :error="projectFeedbackMechanismError">-->
<!--              <el-input type="textarea" :rows="3" v-model="dataForm.projectFeedbackMechanism"-->
<!--                        placeholder="项目反馈机制"></el-input>-->
<!--            </el-form-item>-->
<!--          </el-col>-->
          <el-col :span="24">
            <el-form-item label="项目预期成效" prop="projectExpectedEffect" :error="projectExpectedEffectError">
              <el-input readonly type="textarea" :rows="3" v-model="dataForm.projectExpectedEffect"
                        placeholder="项目预期成效"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="项目宣传" prop="projectPromotion" :error="projectPromotionError">
              <el-input readonly type="textarea" :rows="3" v-model="dataForm.projectPromotion" placeholder="项目宣传"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="风险预案" prop="riskPlan" :error="riskPlanError">
              <el-input readonly type="textarea" :rows="3" v-model="dataForm.riskPlan" placeholder="风险预案"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="封面图片" prop="picture" :error="pictureError">
              <el-image
                  :src="$http.adornAttachmentUrl(dataForm.picture)"
                  :preview-src-list="[$http.adornAttachmentUrl(dataForm.picture)]"
                  class="avatar">
              </el-image>
            </el-form-item>
          </el-col>
        </el-col>
      </el-row>
      <el-row v-if="!this.isDetail" style="margin-bottom: 30px">
        <div style="display: flex;justify-content: center">
          <el-button style="width: 150px" round type="primary" @click="auditHandle(true)">通过</el-button>
            <el-button style="width: 150px" round type="warning" @click="auditHandle(false)">驳回</el-button>
            <el-button style="width: 150px" round type="danger"  @click="stopHandle()">终止</el-button></div>
          <div style="margin-left: 100px">

        </div>
      </el-row>
      <el-row>
        <div style="border: 1px solid gray; margin-bottom: 30px"></div>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item label-width="10px">
            <div style="display: flex; justify-content: flex-start">
              <div style="width: 5px;background-color: #00feff;margin-right: 10px; margin-top: 6px; margin-bottom: 6px"></div>
              <div style="font-weight: bold">审核记录</div>
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-table
              :data="logList"
              border
              style="width: 100%;">
            <el-table-column
                prop="operateTime"
                header-align="center"
                align="center"
                label="操作时间">
            </el-table-column>
            <el-table-column
                prop="operatorName"
                header-align="center"
                align="center"
                label="操作人">
            </el-table-column>
            <el-table-column
                prop="operateType"
                header-align="center"
                align="center"
                label="操作">
            </el-table-column>
            <el-table-column
                prop="remark"
                header-align="center"
                align="center"
                label="驳回理由">
            </el-table-column>
          </el-table>
        </el-col>
      </el-row>
    </el-form>
  </el-dialog>
</template>

<script>
import moment from 'moment'
import Vue from 'vue'
import _ from 'lodash'

export default {
  data() {
    return {
      visible: false,
      dataListLoading: false,
      dataListSelections: [],
      logList: [],
      isDetail: '',
      beneficiaryCategoryList: [],
      serverCode: 'LocalServer',
      myHeaders: {Authorization: Vue.cookie.get('Authorization')},
      uploadData: {serverCode: 'LocalServer', media: false},
      typeList: [],
      dataForm: {},
      initForm: {
        id: null,
        version: null,
        projectName: '',
        implementationLocation: '',
        orgCode: '',
        orgName: '',
        fundsRequired: '',
        beneficiaryCategory: [],
        type: [],
        timeRange: [],
        projectBeneficiaries: '',
        projectDemandType: '',
        contactName: '',
        contactPhone: '',
        contactIdCard: '',
        projectSummary: '',
        projectBackground: '',
        overallObjective: '',
        specificObjective: '',
        prePlanningService: '',
        midTermImplementation: '',
        postEvaluationMechanism: '',
        donorRelationship: '',
        projectFeedbackMechanism: '',
        projectExpectedEffect: '',
        projectPromotion: '',
        riskPlan: '',
        startTime: '',
        endTime: '',
        year: new Date().getFullYear().toString(),
        picture: '',
        auditStatus: '',
        projectBudgetDetailList: [],
        projectActivityPlanList: [],
        projectMoneyMaterialPlanList: [],
        moneyMaterialPlan: '',
        applyUnitName:''
      },
      projectNameError: null,
      implementationLocationError: null,
      orgCodeError: null,
      orgNameError: null,
      beneficiaryCategoryError: null,
      typeError: null,
      projectBeneficiariesError: null,
      projectDemandTypeError: null,
      contactNameError: null,
      contactPhoneError: null,
      contactIdCardError: null,
      projectSummaryError: null,
      projectBackgroundError: null,
      overallObjectiveError: null,
      specificObjectiveError: null,
      prePlanningServiceError: null,
      midTermImplementationError: null,
      postEvaluationMechanismError: null,
      donorRelationshipError: null,
      projectFeedbackMechanismError: null,
      projectExpectedEffectError: null,
      projectPromotionError: null,
      riskPlanError: null,
      startTimeError: null,
      endTimeError: null,
      yearError: null,
      pictureError: null,
      auditStatusError: null
    }
  },
  components: {
    moment
  },
  methods: {
    init(id,isDetail) {
      this.dataForm = _.cloneDeep(this.initForm)
      this.dataForm.id = id || null
      this.isDetail = isDetail || false
      this.getTypeList()
      this.getLogList()
      this.getBeneficiaryCategoryList("beneficiary_category")
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm']?.resetFields()
        if (this.dataForm.id) {
          this.$http({
            url: this.$http.adornUrl(`/admin/project/getProjectById`),
            method: 'get',
            params: this.$http.adornParams({id: this.dataForm.id})
          }).then(({data}) => {
            if (data && data.code === 0) {
              this.dataForm = data.obj
                if(data.obj.startTime && data.obj.endTime){
                    this.$set(this.dataForm, 'timeRange', [data.obj.startTime, data.obj.endTime])
                }
                else {
                    this.$set(this.dataForm, 'timeRange', [])
                }
            }
          })
        }
      })
    },
    getLogList () {
      let auditType = ''
      if (this.isDetail){
        auditType = null
      }else {auditType = 'project_add'}
      this.$http({
        url: this.$http.adornUrl(`/admin/project/audit/log/getLogListByProjectId`),
        method: 'get',
        params: this.$http.adornParams({
          'projectId': this.dataForm.id,
          'auditType': auditType
        })
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.logList = data.obj
        }
      })
    },
    auditHandle(status) {
      this.$confirm(`确定${status ? '审核通过' : '驳回'}` + (this.dataForm.projectName ? '"' + this.dataForm.projectName + '"' : '') + '项目申报？', '系统提示', {
        customClass: 'audit_pass_msg_box',
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        showInput: !status,
        inputPlaceholder: '请输入驳回意见……',
        inputType: 'textarea',
        closeOnClickModal: false
      }).then((val) => {
        this.$http({
          url: this.$http.adornUrl('/admin/project/audit'),
          method: 'get',
          params: this.$http.adornParams({
            'ids': this.dataForm.id,
            'status': status,
            'remark': val.value
          })
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.$message.success(status ? '审核通过成功！' : '驳回成功！')
            this.getLogList()
            this.visible = false
            this.$emit('refreshDataList')
          } else {
            this.$message.error(status ? '审核通过失败！' : '驳回失败！' + data.msg)
          }
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '操作取消'
        });
      });
      setTimeout(() => {
        var content = document.getElementsByClassName('audit_pass_msg_box')[0].childNodes[1]
        content.style.paddingLeft = '60px'
        content.style.paddingRight = '60px'
        var submitBtn = document.getElementsByClassName('audit_pass_msg_box')[0].childNodes[2].childNodes[1]
        submitBtn.style.backgroundColor = '#F56C6C'
        submitBtn.style.borderColor = '#F56C6C'
      }, 50)
    },
    // 删除
    deleteHandle(id,type) {
      if (type === 'budgetDetail'){
        this.$confirm('确定删除该项目预算吗？', '提示', {
          type: 'warning'
        }).then(() => {
        this.dataForm.projectBudgetDetailList.forEach((item,index)=>{
          if (item.id === id){
            this.dataForm.projectBudgetDetailList.splice(index,1)
          }
        })
        })
      }
      if (type === 'activityPlan'){
        this.$confirm('确定删除该活动计划安排吗？', '提示', {
          type: 'warning'
        }).then(() => {
          this.dataForm.projectActivityPlanList.forEach((item,index)=>{
            if (item.id === id){
              this.dataForm.projectActivityPlanList.splice(index,1)
            }
          })
        })
      }
      if (type === 'moneyMaterialPlan'){
        this.$confirm('确定删除该使用计划吗？', '提示', {
          type: 'warning'
        }).then(() => {
          this.dataForm.projectMoneyMaterialPlanList.forEach((item,index)=>{
            if (item.id === id){
              this.dataForm.projectMoneyMaterialPlanList.splice(index,1)
            }
          })
        })
      }
    },
    addBudgetDetail() {
      this.dataForm.projectBudgetDetailList.push({
        'name': '',
        'amount': '',
        'description': '',
        'editFlag': true,  // 可编辑标识
      })
    },
    addActivityPlanListDetail() {
      this.dataForm.projectActivityPlanList.push({
        'activityTime': '',
        'beneficiaryNum': '',
        'participantNum': '',
        'editFlag': true,  // 可编辑标识
      })
    },
    addMoneyMaterialPlanListDetail() {
      this.dataForm.projectMoneyMaterialPlanList.push({
        'activityDate': '',
        'expenses': '',
        'description': '',
        'editFlag': true  // 可编辑标识
      })
    },
    //获取受益人类别
    getBeneficiaryCategoryList(code) {
      this.$http({
        url: this.$http.adornUrl(`/admin/dict/parent`),
        method: 'get',
        params: this.$http.adornParams({code: code})
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.beneficiaryCategoryList = data.obj || []
        }
      })
    },
    //获取受益人类别
    getTypeList() {
      this.$http({
        url: this.$http.adornUrl(`/admin/project/type/getTypeList`),
        method: 'get',
        params: this.$http.adornParams()
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.typeList = data.obj || []
        }
      })
    },
    handleAvatarSuccess(res, file) {
      if (res.success) {
        this.$message({
          message: '操作成功',
          type: 'success',
          duration: 1500
        })
        this.dataForm.picture = res.obj.path
      } else {
        this.$message.error('上传失败')
      }
    },
    beforeAvatarUpload: function (file) {
      let isAccept = ['image/jpg', 'image/jpeg', 'image/png', 'image/bmp'].indexOf(file.type) !== -1
      let isLt2M = file.size / 1024 / 1024 < 2

      if (!isAccept) {
        this.$message.error('上传图片只能是图片!')
      }
      if (!isLt2M) {
        this.$message.error('上传文件大小不能超过 2MB!')
      }
      return isAccept && isLt2M
    },
    // 表单提交
    // dataFormSubmit() {
    //   this.$refs['dataForm'].validate((valid) => {
    //     if (valid) {
    //       this.$http({
    //         url: this.$http.adornUrl(`/admin/project/${!this.dataForm.id ? 'saveProject ' : 'updateProject'}`),
    //         method: 'post',
    //         data: this.$http.adornData(this.dataForm)
    //       }).then(({data}) => {
    //         if (data && data.code === 0) {
    //           this.$message({
    //             message: '操作成功',
    //             type: 'success',
    //             duration: 1500,
    //             onClose: () => {
    //               this.visible = false
    //               this.$emit('refreshDataList')
    //             }
    //           })
    //         } else if (data && data.code === 303) {
    //           for (let it of data.obj) {
    //             this[`${it.field}Error`] = it.message
    //           }
    //         } else {
    //           this.$message.error(data.msg)
    //         }
    //       })
    //     }
    //   })
    // },
    dataFormSubmit(isRender) {
      this.dataForm.startTime = this.dataForm.timeRange && this.dataForm.timeRange[0]
      this.dataForm.endTime = this.dataForm.timeRange && this.dataForm.timeRange[1]
      let method = ''
      if (isRender) {
        method = '/admin/project/renderByProject'
      } else {
        method = `/admin/project/${!this.dataForm.id ? 'saveProject' : 'updateProject'}`
      }
      if (isRender) {
        this.$confirm(`确定进行提交操作? 提交后无法撤回！`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
          closeOnClickModal: false
        }).then(() => {
          this.submitFun(method, 'refreshDataList')
        })
      } else {
        this.submitFun(method, 'refreshDataList')
      }
    },
    submitFun(method, callback) {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.isDisabled = true
          this.$http({
            url: this.$http.adornUrl(method),
            method: 'post',
            data: this.$http.adornData(this.dataForm)
          }).then(({data}) => {
            if (data && data.code === 0) {
              this.visible = false
              this.isDisabled = false
              if (callback === 'resCreateCallback') {
                this.$emit(callback, this.dataForm.name, data.obj || this.dataForm.id)
              } else {
                this.$emit(callback)
              }
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 1500
              })
            } else {
              this.$message.error(data.msg)
              this.isDisabled = false
            }
          })
        }
      })
    },
      stopHandle() {
          this.$confirm(`终止该项目后团队将无法继续编辑该信息申报，团队可新增其他项目继续申报，是否继续？`,'系统提示', {
              customClass: 'audit_pass_msg_box',
              confirmButtonText: '确认',
              cancelButtonText: '取消',
              showInput: !status,
              inputPlaceholder: '请输入终止原因……',
              inputType: 'textarea',
              closeOnClickModal: false
          }).then((val) => {
              this.$http({
                  url: this.$http.adornUrl('/admin/project/stop'),
                  method: 'get',
                  params: this.$http.adornParams({
                      'ids': this.dataForm.id,
                      'status': status,
                      'remark': val.value
                  })
              }).then(({data}) => {
                  if (data && data.code === 0) {
                      this.$message.success('终止成功！')
                      this.getLogList()
                      this.visible = false
                      this.$emit('refreshDataList')
                  } else {
                      this.$message.error( '终止失败！'  + data.msg)
                  }
              })
          }).catch(() => {
              this.$message({
                  type: 'info',
                  message: '操作取消'
              });
          });
          setTimeout(() => {
              var content = document.getElementsByClassName('audit_pass_msg_box')[0].childNodes[1]
              content.style.paddingLeft = '60px'
              content.style.paddingRight = '60px'
              var submitBtn = document.getElementsByClassName('audit_pass_msg_box')[0].childNodes[2].childNodes[1]
              submitBtn.style.backgroundColor = '#F56C6C'
              submitBtn.style.borderColor = '#F56C6C'
          }, 50)
      }
  }
}
</script>
