<template>
  <el-dialog
    :title="!dataForm.id ? '新增' : '修改'"
    :close-on-click-modal="false"
    :visible.sync="visible">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmit()" label-width="80px">
      <el-row :gutter="20">
        <el-col :span="12">
           <el-form-item label="标题" prop="title" :error="errors['title']">
                <el-input v-model="dataForm.title" placeholder="标题"></el-input>
            </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="链接" prop="link" :error="errors['link']">
            <el-input v-model="dataForm.link" placeholder="链接"></el-input>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="有效日期" prop="startEndTime">
            <el-date-picker
              v-model="dataForm.startEndTime"
              type="daterange"
              value-format="yyyy-MM-dd"
              range-separator="-"
              start-placeholder="开始日期"
              end-placeholder="结束日期">
            </el-date-picker>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="状态" prop="enabled" :error="errors['enabled']">
            <el-radio-group v-model="dataForm.enabled">
              <el-radio :label="true">启用</el-radio>
              <el-radio :label="false">禁用</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="12">
           <el-form-item label="排序" prop="sequence" :error="errors['sequence']">
                <el-input-number v-model="dataForm.sequence" controls-position="right" :min="0"></el-input-number>
            </el-form-item>
        </el-col>
        </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="应用图标" prop="imageUrl" :error="errors['imageUrl']">
            <el-upload
              class="avatar-uploader"
              :action="this.$http.adornUrl(`/admin/oss/upload`)"
              :headers="headers"
              :data="{serverCode: uploadOptions.serverCode, media: false}"
              :show-file-list="false"
              :on-success="successHandle"
              :on-change="changHandle"
              :on-exceed="exceedHandle"
              :before-upload="beforeUploadHandle">
              <img v-if="dataForm.imageUrl" :src="$http.adornAttachmentUrl(dataForm.imageUrl)" class="avatar">
              <i v-else class="el-icon-plus avatar-uploader-icon"></i>
            </el-upload>
          </el-form-item>
        </el-col>
      </el-row>
  </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import editMixin from '@/mixins/edit-mixins'
import fileUploadMixin from '@/mixins/file-upload-mixins'
export default {
  mixins: [editMixin, fileUploadMixin],
  data () {
    return {
      uploadOptions: {
        fieldName: 'imageUrl',
        maxSize: 2
      },
      editOptions: {
        initUrl: '/admin/cms/banner'
      },
      dataForm: {
        title: '',
        imageUrl: '',
        link: '',
        startEndTime: null,
        startTime: '',
        endTime: '',
        enabled: true,
        sequence: 0
      },
      dataRule: {
        title: [
          { required: true, message: '标题不能为空', trigger: 'blur' }
        ],
        imageUrl: [
          { required: true, message: '图片不能为空', trigger: 'blur' }
        ],
        link: [
          { required: true, message: '链接不能为空', trigger: 'blur' }
        ],
        startEndTime: [
          { required: true, message: '有效日期不能为空', trigger: 'blur' }
        ],
        enabled: [
          { required: true, message: '状态 0:禁用 1:启用不能为空', trigger: 'blur' }
        ],
        sequence: [
          { required: true, message: '排序不能为空', trigger: 'blur' }
        ]
      }
    }
  },
  methods: {
    initCallback (data) {
      // 初始化回调函数
      this.dataForm = data
      this.$set(this.dataForm, 'startEndTime', [
        data.startTime, data.endTime
      ])
    },
    // *处理点位分类最后children数组为空的状态
    getTreeData: function (data) {
      let that = this
      // 循环遍历json数据
      data.forEach(function (e) {
        if (!e.children || e.children.length < 1) {
          e.children = undefined
        } else {
          // children若不为空数组，则继续 递归调用 本方法
          that.getTreeData(e.children)
        }
      })
      return data
    },
    submitBeforeHandle () {
      // 提交前预处理函数
      this.dataForm.startTime = this.dataForm.startEndTime[0]
      this.dataForm.endTime = this.dataForm.startEndTime[1]
    }
  }
}
</script>
<style lang="scss">
  .avatar {
    width: 178px;
    height: 178px;
    display: block;
  }
  .avatar-uploader .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
  }

  .avatar-uploader .el-upload:hover {
    border-color: #409EFF;
  }

  .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 178px;
    height: 178px;
    line-height: 178px;
    text-align: center;
  }
</style>
