<template>
  <div class="mod-config">
    <el-form :inline="true" :model="dataForm" ref="dataForm" @keyup.enter.native="getDataList()">
      <el-form-item label="标题:" prop="title">
        <el-input v-model="dataForm.title" placeholder="标题" clearable></el-input>
      </el-form-item>
      <el-form-item label="所属栏目" prop="categoryId">
        <el-cascader
          placeholder="所属栏目"
          v-model="dataForm.categoryId"
          :options="categories"
          :props="{checkStrictly: true}"
        ></el-cascader>
      </el-form-item>
      <el-form-item>
        <el-button @click="getDataList()">查询</el-button>
        <el-button @click="resetForm()">重置</el-button>
        <el-button type="primary" @click="addOrUpdateHandle()">新增</el-button>
        <el-button type="primary" @click="deleteHandle()" :disabled="dataListSelections.length <= 0">批量删除</el-button>
      </el-form-item>
    </el-form>
    <el-table
      :data="dataList"
      border
      v-loading="dataListLoading"
      @selection-change="selectionChangeHandle"
      style="width: 100%;">
      <el-table-column
        type="selection"
        header-align="center"
        align="center"
        width="50">
      </el-table-column>
       <el-table-column
        prop="title"
        header-align="center"
        align="center"
        label="标题">
      </el-table-column>
         <el-table-column
        prop="link"
        header-align="center"
        align="center"
        :show-overflow-tooltip="true"
        label="链接">
      </el-table-column>
         <el-table-column
        prop="startTime"
        header-align="center"
        align="center"
        label="开始时间">
      </el-table-column>
         <el-table-column
        prop="endTime"
        header-align="center"
        align="center"
        label="结束时间">
      </el-table-column>
         <el-table-column
        prop="enabled"
        header-align="center"
        align="center"
        label="状态">
        <template slot-scope="scope">
            <el-tag v-if="scope.row.enabled == false" size="small" type="danger">否</el-tag>
            <el-tag v-else size="small">是</el-tag>
        </template>
     </el-table-column>
      <el-table-column
        prop="sequence"
        header-align="center"
        align="center"
        label="排序">
      </el-table-column>
      <el-table-column
        prop="categoryName"
        header-align="center"
        align="center"
        label="所属栏目">
      </el-table-column>
        <el-table-column
        fixed="right"
        header-align="center"
        align="center"
        width="150"
        label="操作">
        <template slot-scope="scope">
          <el-button type="text" v-if="isAuth('cms:banner:update')" size="small" @click="addOrUpdateHandle(scope.row.id)" class="btn-control">修改</el-button>
          <el-button type="text" v-if="isAuth('cms:banner:delete')" size="small" @click="deleteHandle(scope.row.id)" class="btn-control">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      @size-change="sizeChangeHandle"
      @current-change="currentChangeHandle"
      :current-page="pageIndex"
      :page-sizes="[10, 20, 50, 100]"
      :page-size="pageSize"
      :total="totalPage"
      layout="total, sizes, prev, pager, next, jumper">
    </el-pagination>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"></add-or-update>
  </div>
</template>
<script>
  import AddOrUpdate from './add-or-update'
  import listMixin from '@/mixins/list-mixins'
  export default {
    mixins: [listMixin],
    data () {
      return {
        mixinOptions: {
          dataUrl: '/admin/cms/banner/pages',
          deleteUrl: '/admin/cms/banner/removeByIds'
        },
        dataForm: {
          title: null,
          categoryId: [],
          categoryList: null
        },
        categories: []
      }
    },
    components: {
      AddOrUpdate
    },
    activated () {
      this.getDataList()
      this.getCategories()
    },
    methods: {
      queryBeforeHandle () {
        this.dataForm.categoryList = this.dataForm.categoryId.join(',')
      },
      // 获取数据列表
      getCategories () {
        this.$http({
          url: this.$http.adornUrl('/admin/cms/category/cascader'),
          method: 'get',
          params: this.$http.adornParams()
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.categories = this.getTreeData(data.obj)
          } else {
            this.categories = []
          }
        })
      },
      // *处理点位分类最后children数组为空的状态
      getTreeData: function (data) {
        let that = this
        // 循环遍历json数据
        data.forEach(function (e) {
          if (!e.children || e.children.length < 1) {
            e.children = undefined
          } else {
            // children若不为空数组，则继续 递归调用 本方法
            that.getTreeData(e.children)
          }
        })
        return data
      }
    }
  }
</script>
