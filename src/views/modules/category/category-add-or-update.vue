<template>
  <el-dialog
    :title="!dataForm.id ? '新增' : '修改'"
    :close-on-click-modal="false"
    :visible.sync="visible">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" label-width="90px">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="图片：" prop="imgUrl" :error="errors['imgUrl']">
            <el-upload
                id="category-uploader"
                class="avatar-uploader"
                :action="this.$http.adornUrl(`/admin/oss/upload`)"
                :headers="headers"
                :data="{serverCode: uploadOptions.serverCode, media: false}"
                :show-file-list="false"
                :on-success="successHandle"
                :on-change="changHandle"
                :on-exceed="exceedHandle"
                :before-upload="beforeUploadHandle">
              <img v-if="dataForm.imgUrl" :src="$http.adornAttachmentUrl(dataForm.imgUrl)" class="avatar">
              <i v-else class="el-icon-plus avatar-uploader-icon"></i>
            </el-upload>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="上级菜单" prop="parentName">
            <el-popover
                ref="categoryListPopover"
                placement="bottom-start"
                trigger="click">
              <div style="overflow: hidden; width: 320px">
                <div style="overflow-y: auto;max-height:300px; width: 400px">
                  <el-tree
                      :data="categoryList"
                      :props="categoryListTreeProps"
                      node-key="id"
                      ref="categoryListTree"
                      @current-change="categoryListTreeCurrentChangeHandle"
                      :default-expand-all="true"
                      :highlight-current="true"
                      :expand-on-click-node="false">
                  </el-tree>
                </div>
              </div>
            </el-popover>
            <el-input v-model="dataForm.parentName" v-popover:categoryListPopover :readonly="true"
                      placeholder="点击选择上级菜单" class="menu-list__input"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="栏目名称" prop="name" :error="errors['name']">
            <el-input v-model="dataForm.name" placeholder="栏目名称"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="栏目编号" prop="code" :error="errors['code']">
            <el-input v-model="dataForm.code" placeholder="栏目编号"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="排序号" prop="sequence" :error="errors['sequence']">
            <el-input-number v-model="dataForm.sequence" controls-position="right" :min="0"
                             label="排序号"></el-input-number>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="状态" prop="status" :error="errors['status']">
            <el-radio-group v-model="dataForm.status">
              <el-radio :label="true">启用</el-radio>
              <el-radio :label="false">禁用</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>
<!--      <el-row :gutter="20">-->
<!--        <el-col :span="12">-->
<!--          <el-form-item label="SEO标题" prop="seoTitle" :error="errors['seoTitle']">-->
<!--            <el-input v-model="dataForm.seoTitle" placeholder="SEO标题"></el-input>-->
<!--          </el-form-item>-->
<!--        </el-col>-->
<!--        <el-col :span="12">-->
<!--          <el-form-item label="SEO关健字" prop="seoKeyword" :error="errors['seoKeyword']">-->
<!--            <el-input v-model="dataForm.seoKeyword" placeholder="SEO关健字,以“,”逗号区分开"></el-input>-->
<!--          </el-form-item>-->
<!--        </el-col>-->
<!--      </el-row>-->
<!--      <el-row :gutter="20">-->
<!--        <el-col :span="12">-->
<!--          <el-form-item label="SEO描述" prop="seoRemark" :error="errors['seoRemark']">-->
<!--            <el-input v-model="dataForm.seoRemark" placeholder="SEO描述"></el-input>-->
<!--          </el-form-item>-->
<!--        </el-col>-->
<!--      </el-row>-->
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="URL链接" prop="urlLink" :error="errors['urlLink']">
            <el-input v-model="dataForm.urlLink" placeholder="URL链接"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
<!--      <el-row :gutter="20">-->
<!--        <el-col :span="12">-->
<!--          <el-form-item label="标签" prop="labels" :error="errors['labels']">-->
<!--            <el-select v-model="dataForm.labels" multiple placeholder="请选择">-->
<!--              <el-option-->
<!--                v-for="item in labelList"-->
<!--                :key="item.id"-->
<!--                :label="item.name"-->
<!--                :value="item.id">-->
<!--              </el-option>-->
<!--            </el-select>-->
<!--          </el-form-item>-->
<!--        </el-col>-->
<!--      </el-row>-->
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="栏目描述" prop="remark" :error="errors['remark']">
            <el-input type="textarea" :rows="4" v-model="dataForm.remark" :maxlength="260"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import {treeDataTranslate} from '@/utils'
import editMixin from '@/mixins/edit-mixins'
import fileUploadMixin from '@/mixins/file-upload-mixins'
export default {
  mixins: [editMixin, fileUploadMixin],
  data () {
    const validCode = (rule, value, callback) => {
      this.$http({
        url: this.$http.adornUrl(`/admin/cms/category/validateCode`),
        method: 'get',
        params: this.$http.adornParams({
          code: this.dataForm.code,
          id: this.dataForm.id
        })
      }).then(({data}) => {
        if (data && data.code === 0) {
          if (!data.obj) {
            callback(new Error('该编码数据库已存在，请更换！'))
          } else {
            callback()
          }
        } else {
          callback()
        }
      })
    }
    return {
      treeLoadTimes: 0,
      uploadOptions: {
        fieldName: 'imgUrl',
        maxSize: 2
      },
      editOptions: {
        initUrl: '/admin/cms/category'
      },
      labelList: [],
      dataForm: {
        name: null,
        code: null,
        sequence: 0,
        seoTitle: null,
        seoKeyword: null,
        labels: [],
        seoRemark: null,
        imgUrl: null,
        urlLink: null,
        status: true,
        remark: null,
        parentId: null,
        parentName: ''
      },
      dataRule: {
        name: [
          {required: true, message: '名称不能为空', trigger: 'blur'}
        ],
        code: [
          {required: true, message: '编码不能为空', trigger: 'blur'},
          { validator: validCode, trigger: 'blur' }
        ],
        sequence: [
          {required: true, message: '排序不能为空', trigger: 'blur'}
        ],
        status: [
          {required: true, message: '状态不能为空', trigger: 'blur'}
        ]
      },
      categoryList: [],
      categoryListTreeProps: {
        label: 'name',
        children: 'subCategories'
      }
    }
  },
  methods: {
    initBeforeHandle () {
      // 初始化预处理函数
      this.dataForm.parentId = null
      this.getLabelList()
      this.getCategoryTree()
    },
    initCallback (data) {
      // 初始化回调函数
      data.labels = data.labels || []
      this.dataForm = data
      this.categoryListTreeSetCurrentNode()
    },
    getCategoryTree () {
      this.$http({
        url: this.$http.adornUrl('/admin/cms/category/tree'),
        method: 'get',
        params: this.$http.adornParams()
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.categoryList = treeDataTranslate(data.obj)
        } else {
          this.$message.error(data.msg)
        }
      })
    },
    // 获取标签
    getLabelList () {
      this.$http({
        url: this.$http.adornUrl('/admin/cms/label/getLabelList'),
        method: 'get',
        params: this.$http.adornParams()
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.labelList = data.obj
        } else {
          this.$message.error(data.msg)
        }
      })
    },
    // 菜单树选中
    categoryListTreeCurrentChangeHandle (data, node) {
      this.$set(this.dataForm, 'parentId', data.id)
      this.$set(this.dataForm, 'parentName', data.name)
      this.$refs[`categoryListPopover`].doClose()
    },
    // 菜单树设置当前选中节点
    categoryListTreeSetCurrentNode () {
      if (this.dataForm.parentId) {
        this.$refs.categoryListTree.setCurrentKey(this.dataForm.parentId)
        if (!this.$refs.categoryListTree.getCurrentKey() && this.treeLoadTimes < 20) {
          this.treeLoadTimes ++
          setTimeout(() => {
            this.categoryListTreeSetCurrentNode()
          }, 100)
        } else {
          this.treeLoadTimes = 0
          this.$set(this.dataForm, 'parentName', this.$refs.categoryListTree.getCurrentNode().name)
        }
      } else {
        this.$refs.categoryListTree.setCurrentKey([])
        this.$set(this.dataForm, 'parentName', '')
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.avatar {
  width: 250px;
  height: 250px;
  display: block;
}

#category-uploader ::v-deep .el-upload {
  width: 250px;
  height: 250px;
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  img {
    width: 250px;
    height: 250px;
  }
}

#category-uploader ::v-deep .el-upload:hover {
  border-color: #409EFF;
}

#category-uploader ::v-deep .avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 250px;
  height: 250px;
  line-height: 250px;
  text-align: center;
}

.mod-menu {
  .menu-list__input,
  .icon-list__input {
    > .el-input__inner {
      cursor: pointer;
    }
  }

  &__icon-popover {
    width: 458px;
    overflow: hidden;
  }

  &__icon-inner {
    width: 478px;
    max-height: 258px;
    overflow-x: hidden;
    overflow-y: auto;
  }

  &__icon-list {
    width: 458px;
    padding: 0;
    margin: -8px 0 0 -8px;

    > .el-button {
      padding: 8px;
      margin: 8px 0 0 8px;

      > span {
        display: inline-block;
        vertical-align: middle;
        width: 18px;
        height: 18px;
        font-size: 18px;
      }
    }
  }

  .icon-list__tips {
    font-size: 18px;
    text-align: center;
    color: #e6a23c;
    cursor: pointer;
  }
}
</style>
