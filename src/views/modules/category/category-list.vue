<template>
  <div class="mod-dict">
    <el-form :inline="true" :model="dataForm">
      <el-form-item>
        <el-button icon="el-icon-plus" v-if="isAuth('cms:category:save')" type="primary" @click="addOrUpdateHandle()">新增</el-button>
      </el-form-item>
    </el-form>
    <el-table
      :data="dataList"
      border
      style="width: 100%;">
      <table-tree-column
        prop="name"
        header-align="center"
        treeKey="id"
        label="栏目名称">
      </table-tree-column>
      <el-table-column
          prop="code"
          header-align="center"
          align="center"
          label="栏目编码">
      </el-table-column>
      <el-table-column
          prop="urlLink"
          header-align="center"
          align="center"
          label="URL">
      </el-table-column>
      <el-table-column
        prop="sequence"
        header-align="center"
        align="center"
        label="排序">
      </el-table-column>
      <el-table-column
        prop="status"
        header-align="center"
        align="center"
        label="状态">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.status === false" size="small" type="danger">禁用</el-tag>
          <el-tag v-else size="small">启用</el-tag>
        </template>
      </el-table-column>
      <el-table-column
        header-align="center"
        align="center"
        label="操作">
        <template slot-scope="scope">
          <el-button v-if="isAuth('cms:category:update')" type="text" size="small" @click="addOrUpdateHandle(scope.row.id)">修改</el-button>
          <el-button v-if="isAuth('cms:category:delete')" type="text" size="small" @click="deleteHandle(scope.row.id)">删除</el-button>
          <el-button v-if="isAuth('cms:category:enable') && scope.row.status == false" type="text" size="small" @click="setEnableHandle(scope.row.id,true)">启用</el-button>
          <el-button v-if="isAuth('cms:category:enable') && scope.row.status == true" type="text" size="small" @click="setEnableHandle(scope.row.id,false)">禁用</el-button>
          <el-button v-if="isAuth('cms:category:prop')" type="text" size="small" @click="addProp(scope.row.id)">属性</el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"></add-or-update>
    <!-- 弹窗, 新增属性 -->
    <add-prop v-if="addPropVisible" ref="addProp"></add-prop>
  </div>
</template>

<script>
  import TableTreeColumn from '@/components/table-tree-column'
  import AddOrUpdate from './category-add-or-update'
  import AddProp from './prop-list'
  import { treeDataTranslate } from '@/utils'
  import listMixin from '@/mixins/list-mixins'
  export default {
    mixins: [listMixin],
    data () {
      return {
        mixinOptions: {
          method: 'get',
          pageable: false,
          dataUrl: '/admin/cms/category/all',
          deleteUrl: '/admin/cms/category/removeByIds'
        },
        addPropVisible: false
      }
    },
    components: {
      TableTreeColumn,
      AddOrUpdate,
      AddProp
    },
    activated() {
      this.query()
    },
    methods: {
      queryCallback (data) {
        this.dataList = treeDataTranslate(data, 'id')
      },
      // 添加属性
      addProp (id) {
        this.addPropVisible = true
        this.$nextTick(() => {
          this.$refs.addProp.init(id)
        })
      },
      // 启用、禁用
      setEnableHandle (id, enable) {
        this.$confirm(`确定进行[${enable ? '启用' : '禁用'}]操作?`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
          closeOnClickModal: false
        }).then(() => {
          this.$http({
            url: this.$http.adornUrl('/admin/cms/category/setEnable'),
            method: 'post',
            params: this.$http.adornParams({
              'id': id
            })
          }).then(({data}) => {
            if (data && data.code === 0) {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 1500,
                onClose: () => {
                  this.getDataList()
                }
              })
            } else {
              this.$message.error(data.msg)
            }
          })
        }).catch(() => {})
      }
    }
  }
</script>
