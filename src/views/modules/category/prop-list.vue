<template>
  <div>
    <el-dialog
      width="980px"
      title="属性"
      :close-on-click-modal="false"
      :visible.sync="visible">
      <el-form :inline="true" class="search-box" ref="dataForm" :model="dataForm"
               label-width="100px">
        <el-form-item class="pd-15">
          <el-button type="primary" @click="addOrUpdateHandle(dataForm.categoryId)">新增</el-button>
          <el-button type="primary" @click="deleteHandle()" :disabled="dataListSelections.length <= 0">删除</el-button>
        </el-form-item>
      </el-form>
      <el-table
        :data="dataList"
        border
        v-loading="dataListLoading"
        @selection-change="selectionChangeHandle"
        style="width: 100%">
        <el-table-column
          type="selection"
          header-align="center"
          align="center"
          width="50">
        </el-table-column>
        <el-table-column
          prop="propName"
          label="属性名称">
        </el-table-column>
      </el-table>
      <el-pagination
        @size-change="sizeChangeHandle"
        @current-change="currentChangeHandle"
        :current-page="pageIndex"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pageSize"
        :total="totalPage"
        layout="total, sizes, prev, pager, next, jumper">
      </el-pagination>
    </el-dialog>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"></add-or-update>
  </div>
</template>

<script>
  import AddOrUpdate from './prop-add'
  import listMixin from '@/mixins/list-mixins'
  export default {
    mixins: [listMixin],
    data () {
      return {
        mixinOptions: {
          dataUrl: '/admin/cms/categoryProp/page',
          deleteUrl: '/admin/cms/categoryProp/removeByIds'
        },
        visible: false,
        dataForm: {
          categoryId: ''
        }
      }
    },
    components: {
      AddOrUpdate
    },
    methods: {
      init (id) {
        this.visible = true
        this.dataForm.categoryId = id
        this.getDataList()
      }
    }
  }
</script>
