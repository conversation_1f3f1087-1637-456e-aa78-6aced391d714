<template>
  <div class="mod-config">
    <el-form :inline="true" ref="dataForm" :model="dataForm" @keyup.enter.native="queryPage()">
      <el-form-item label="关键字" prop="key" style="padding-right: 40px">
        <el-input v-model="dataForm.key" clearable placeholder="需求名称/联系人/联系电话" style="width: 120%"></el-input>
      </el-form-item>
      <el-form-item :label="'发布组织'" prop="publishOrgCodes">
        <el-cascader placeholder="请选择" v-model="dataForm.publishOrgCodes" :options="orgList"
                     :disabled="this.dataForm.id"
                     :show-all-levels="false" clearable :props="{ checkStrictly: true, value: 'code', label: 'name' }"
                     @change="(value) => { handleChange(value, 'org') }"></el-cascader>
      </el-form-item>
      <el-form-item label="需求类型" prop="type">
        <el-dict :code="'REQUIREMENT_TYPE'" v-model="dataForm.type"></el-dict>
      </el-form-item>
      <el-form-item label="所属领域" prop="fieldId">
        <el-cascader placeholder="请选择" v-model="dataForm.fieldIds" :options="fields"
                     @change="(value) => { handleChange(value, 'field') }" clearable
                     :props="{ checkStrictly: true, label: 'typeName', value: 'typeId' }"></el-cascader>
      </el-form-item>
      <el-form-item label="服务对象" prop="serviceObj">
        <el-dict :code="'SERVICE_OBJECT'" v-model="dataForm.serviceObj"></el-dict>
      </el-form-item>
      <el-form-item label="需求状态" prop="status">
        <el-dict :code="'requirement_status'" v-model="dataForm.status"></el-dict>
      </el-form-item>
      <el-form-item label="需求时间" prop="timeRange">
        <el-date-picker v-model="dataForm.timeRange" clearable type="datetimerange" value-format="yyyy-MM-dd HH:mm:ss"
                        align="right" start-placeholder="开始时间" end-placeholder="结束时间"
                        :default-time="['00:00:00', '23:59:59']">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="上架状态" prop="autoStatus">
        <el-select v-model="dataForm.autoStatus" placeholder="请选择" clearable>
          <el-option v-for="item in [{ label: '上架', value: 'true' }, { label: '下架', value: 'false' }]" :key="item.value"
                     :label="item.label" :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-search" type="warning" @click="queryPage()">查询</el-button>
        <el-button @click="resetForm()" icon="el-icon-refresh-left">重置</el-button>
      </el-form-item>
    </el-form>
    <div class="f-s-c" style="padding: 15px 0;display: flex;justify-content:flex-end;align-items: center ">
      <div>
        <el-button icon="el-icon-plus" type="primary" @click="addOrUpdateHandle()">新增</el-button>
        <!-- <el-button type="danger" @click="deleteHandle()" :disabled="dataListSelections.length <= 0">批量删除</el-button> -->
        <el-button icon="el-icon-download" type="success" @click="exportReqirement()">全部导出</el-button>
      </div>
    </div>
    <el-table :data="dataList" border v-loading="dataListLoading" style="width: 100%;">
      <el-table-column prop="name" header-align="center" align="center" show-overflow-tooltip min-width="250" label="需求名称">
        <template slot-scope="scope">
          <a style="cursor: pointer" @click="requirementDetailHandler(scope.row.id)">{{ scope.row.name }}</a>
        </template>
      </el-table-column>
      <el-table-column prop="typeText" header-align="center" align="center" label="需求类型">
      </el-table-column>
      <el-table-column prop="belongField" header-align="center" align="center" min-width="200" label="所属领域">
        <template slot-scope="scope">
          <span>{{
              scope.row.belongFieldNameTop && scope.row.belongFieldNameEnd ? scope.row.belongFieldNameTop + '-' +
                  scope.row.belongFieldNameEnd : (scope.row.belongFieldNameTop || scope.row.belongFieldNameEnd)
            }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="serviceObjText" header-align="center" align="center" label="服务对象">
      </el-table-column>
      <el-table-column prop="publishOrgName" header-align="center" align="center" min-width="180" show-overflow-tooltip
                       label="发布组织">
      </el-table-column>
      <el-table-column prop="contactPerson" header-align="center" align="center" min-width="80" label="联系人">
      </el-table-column>
      <el-table-column prop="contactPhone" header-align="center" align="center" min-width="120" label="联系电话">
      </el-table-column>
      <el-table-column prop="timeRange" header-align="center" align="center" min-width="160" label="需求时间">
        <template slot-scope="scope">
          <span>{{ (!scope.row.startTime || scope.row.startTime === '' || !scope.row.endTime || scope.row.endTime === '') ? '' : scope.row.startTime + '--' + scope.row.endTime }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="statusText" header-align="center" align="center" min-width="130" label="需求状态">
      </el-table-column>
<!--      <el-table-column prop="dockingOrgName" header-align="center" align="center" min-width="180" show-overflow-tooltip-->
<!--                       label="对接组织">-->
<!--      </el-table-column>-->
<!--      <el-table-column prop="linkActivityName" header-align="center" align="center" min-width="200" show-overflow-tooltip-->
<!--                       label="关联活动">-->
<!--        <template slot-scope="scope">-->
<!--          <a style="cursor: pointer"-->
<!--             @click="activityDetailHandler(scope.row.linkActivityId)">{{ scope.row.linkActivityName }}</a>-->
<!--        </template>-->
<!--      </el-table-column>-->
      <el-table-column prop="autoStatus" header-align="center" align="center" label="是否上架">
        <template slot-scope="scope">
          <div slot="reference" class="name-wrapper">
            <el-tag v-if="scope.row.autoStatus" type="success">是</el-tag>
            <el-tag v-else type="danger">否</el-tag>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="syncText" header-align="center" align="center" label="同步状态">
        <template slot-scope="scope">
          <!--          <div v-if="scope.row.isSyncText === '同步失败'" style="color: #f6070f;cursor:pointer">{{scope.row.isSyncText}}</div>-->
          <!--          <div v-if="scope.row.isSyncText === '待同步'" style="color: #eee98a;cursor:pointer">{{scope.row.isSyncText}}</div>-->
          <!--          <div v-if="scope.row.isSyncText === '已同步'" style="color: #03f303;cursor:pointer">{{scope.row.isSyncText}}</div>-->
          <el-popover trigger="hover" placement="top">
            <p style="text-align: center">
              {{
                '同步时间：' + (scope.row.syncTime ? scope.row.syncTime : '')
              }}<br>{{ scope.row.syncRemark }}</p>
            <div slot="reference" class="name-wrapper">
              <el-tag v-if="scope.row.sync === 'sync_failure'" type="danger">{{ scope.row.syncText }}</el-tag>
              <el-tag v-if="scope.row.sync === 'sync_wait'" type="warning">{{ scope.row.syncText }}</el-tag>
              <el-tag v-if="scope.row.sync === 'sync_success'" type="success">{{ scope.row.syncText }}</el-tag>
            </div>
          </el-popover>
        </template>
      </el-table-column>
      <el-table-column fixed="right" header-align="center" align="center" min-width="180" label="操作">
        <template slot-scope="scope">
          <el-button type="text" size="small" @click="addOrUpdateHandle(scope.row.id, true)">复制</el-button>
          <el-button v-if="scope.row.enable" type="text" size="small"
                     @click="addOrUpdateHandle(scope.row.id, false)">修改
          </el-button>
          <el-button v-if="scope.row.enable" type="text" size="small"
                     @click="render(scope.row.id)">提交
          </el-button>
          <el-button v-if="scope.row.enable" type="text" size="small"
                     @click="deleteHandle(scope.row.id)">删除
          </el-button>
          <el-button v-if="scope.row.autoEnable"
                     type="text" size="small" @click="updateAutoStatus(scope.row.id, scope.row.autoStatus)">
            {{ !scope.row.autoStatus ? '上架' : '下架' }}
          </el-button>
<!--          <el-button v-if="scope.row.activityStatus === 'act_audit_success'" type="text" size="small"-->
<!--                     @click="remarkHandle(scope.row.id,scope.row.linkActivityName)">评价-->
<!--          </el-button>-->
          <el-button type="text" size="small" @click="syncHandle(scope.row.id)"
                     v-if="isAuth('requirement:sync') && scope.row.syncEnable">同步
          </el-button>
          <el-button type="text" size="small" @click="adminUpdateHandle(scope.row.id)" v-if="isAuth('requirement:adminUpdate') && scope.row.status !== 'ras_draft' && scope.row.status !== 'ras_wait_audit' && scope.row.status !== 'ras_reject'">管理员修改</el-button>
          <el-button type="text" size="small" @click="adminSyncHandle(scope.row.id)" v-if="isAuth('requirement:adminSync') && scope.row.status !== 'ras_draft' && scope.row.status !== 'ras_wait_audit' && scope.row.status !== 'ras_reject'">管理员同步</el-button>
          <el-button type="text" size="small" v-if="scope.row.dockingZSQ"  @click="zsqDockingRecords(scope.row.id)">知社区对接记录</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination @size-change="sizeChangeHandle" @current-change="currentChangeHandle" :current-page="pageIndex"
                   :page-sizes="[10, 20, 50, 100]" :page-size="pageSize" :total="totalPage"
                   layout="total, sizes, prev, pager, next, jumper">
    </el-pagination>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"></add-or-update>
    <!-- 评价 -->
<!--    <remark v-if="remarkVisible" ref="remark" @refreshDataList="getDataList"></remark>-->
    <!-- 需求详情 -->
    <requirement-detail v-if="requirementDetailVisible" ref="requirementDetail"></requirement-detail>
    <!-- 修改并同步 -->
    <requirement-admin-update v-if="adminUpdateVisible" ref="adminUpdate" @refreshDataList="getDataList"></requirement-admin-update>
    <!-- 活动详情 -->
<!--    <activity-detail v-if="activityDetailVisible" ref="activityDetail"></activity-detail>-->
    <zsq-docking-records v-if="zsqDockingRecordsVisible" ref="zsqDockingRecords"></zsq-docking-records>
  </div>
</template>

<script>
import AddOrUpdate from './requirement-add-or-update'
// import ActivityDetail from '../activity/activity-detail'
// import Remark from './evaluate-remark'
import RequirementDetail from './requirement-detail'
import RequirementAdminUpdate from './requirement-admin-update.vue'
import ZsqDockingRecords from "./requirement-zsq-docking-records.vue";

export default {
  data() {
    return {
      dataForm: {
        key: '',
        type: '',
        serviceObj: '',
        status: '',
        autoStatus: null,
        timeRange: [],
        publishOrgCodes: [],
        publishOrgCode: null,
        fieldIds: [],
        fieldId: null,
      },
      orgList: [],
      fields: [],
      requirementTime: '',
      dataList: [],
      pageIndex: 1,
      pageSize: 10,
      totalPage: 0,
      dataListLoading: false,
      dataListSelections: [],
      requirementDetailVisible: false,
      addOrUpdateVisible: false,
      remarkVisible: false,
      activityDetailVisible: false,
      adminUpdateVisible: false,
      zsqDockingRecordsVisible: false,
    }
  },
  components: {
    ZsqDockingRecords,
    AddOrUpdate,
    RequirementDetail,
    RequirementAdminUpdate
    // ActivityDetail,
    // Remark
  },
  activated() {
    this.getOrg()
    this.queryPage()
    this.getFields()
  },
  methods: {
    queryPage() {
      this.pageIndex = 1
      this.getDataList()
    },
    // 获取数据列表
    getDataList() {
      this.dataListLoading = true
      this.$http({
        url: this.$http.adornUrl('/admin/zyz/requirement/getPages'),
        method: 'post',
        data: this.$http.adornData({
          'currentPage': this.pageIndex,
          'pageSize': this.pageSize,
          'key': this.dataForm.key,
          'type': this.dataForm.type,
          'serviceObj': this.dataForm.serviceObj,
          'status': this.dataForm.status,
          'autoStatus': this.dataForm.autoStatus,
          'publishOrgCode': this.dataForm.publishOrgCode,
          'startTime': this.dataForm.timeRange != null ? this.dataForm.timeRange[0] : null,
          'endTime': this.dataForm.timeRange != null ? this.dataForm.timeRange[1] : null,
          'fieldId': this.dataForm.fieldId
        })
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.dataList = data.obj.records
          this.totalPage = data.obj.total
          this.dataList.forEach(item => {
            //删除，提交，修改 条件判断
            var enable = item.own && (item.status === 'ras_draft' || item.status === 'ras_reject')
            item.enable = enable
            //判断上下架
            var autoEnable = item.status === 'ras_wait_docking' && !item.actDocked
            item.autoEnable = autoEnable
            //同步判断
            var syncEnable = item.sync !== 'sync_success' && item.status === 'ras_wait_docking'
            item.syncEnable = syncEnable
          })
        } else {
          this.dataList = []
          this.totalPage = 0
        }
        this.dataListLoading = false
      })
    },
    // 每页数
    sizeChangeHandle(val) {
      this.pageSize = val
      this.pageIndex = 1
      this.getDataList()
    },
    // 当前页
    currentChangeHandle(val) {
      this.pageIndex = val
      this.getDataList()
    },
    // 多选
    selectionChangeHandle(val) {
      this.dataListSelections = val
    },
    // 新增 / 修改
    addOrUpdateHandle(id, isCopy) {
      this.addOrUpdateVisible = true
      this.$nextTick(() => {
        this.$refs.addOrUpdate.init(id, isCopy)
      })
    },
    // 修改并同步
    adminUpdateHandle(id) {
      this.adminUpdateVisible = true
      this.$nextTick(() => {
        this.$refs.adminUpdate.init(id)
      })
    },
    adminSyncHandle(id) {
      this.$confirm(`确定进行同步操作?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        closeOnClickModal: false
      }).then(() => {
        this.$http({
          url: this.$http.adornUrl('/admin/zyz/requirement/reSyncForAdmin'),
          method: 'get',
          params: this.$http.adornParams({
            'id': id
          })
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.$message({
              message: '操作成功',
              type: 'success',
              duration: 1500,
              onClose: () => {
                this.getDataList()
              }
            })
          } else {
            this.$message.error(data.msg)
          }
        })
      })
    },
    activityDetailHandler(activityId) {
      this.activityDetailVisible = true
      this.$nextTick(() => {
        this.$refs.activityDetail.init(activityId)
      })
    },
    // 评价
    remarkHandle(id, linkActivityName) {
      this.remarkVisible = true
      this.$nextTick(() => {
        this.$refs.remark.init(id, linkActivityName)
      })
    },
    requirementDetailHandler(requirementId) {
      this.requirementDetailVisible = true
      this.$nextTick(() => {
        this.$refs.requirementDetail.init(requirementId)
      })
    },
    // 重置
    resetForm() {
      this.$refs['dataForm']?.resetFields()
      this.dataForm.fieldId = null
      this.dataForm.fieldIds = []
      this.dataForm.publishOrgCode = null
      this.getDataList()
    },
    // 删除
    deleteHandle(id) {
      var ids = id ? [id] : this.dataListSelections.map(item => {
        return item.id
      })
      this.$confirm(`确定进行[${id ? '删除' : '批量删除'}]操作?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        closeOnClickModal: false
      }).then(() => {
        this.$http({
          url: this.$http.adornUrl('/admin/zyz/requirement/removeByIds'),
          method: 'get',
          params: this.$http.adornParams({
            'ids': ids.join(',')
          })
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.$message({
              message: '操作成功',
              type: 'success',
              duration: 1500,
              onClose: () => {
                this.getDataList()
              }
            })
          } else {
            this.$message.error(data.msg)
          }
        })
      })
    },
    handleChange(value, type) {
      if (type === 'field') {
        this.dataForm.fieldId = value[value.length - 1]
      }
      if (type === 'org') {
        this.dataForm.publishOrgCode = value && value.length > 0 ? value[value.length - 1] : null
      }
    },
    // 提交
    render(id) {
      this.$confirm(`确定进行提交操作? 提交后无法撤回！`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        closeOnClickModal: false
      }).then(() => {
        this.$http({
          url: this.$http.adornUrl('/admin/zyz/requirement/renderById'),
          method: 'get',
          params: this.$http.adornParams({
            'id': id
          })
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.$message({
              message: '操作成功',
              type: 'success',
              duration: 1500,
              onClose: () => {
                this.getDataList()
              }
            })
          } else {
            this.$message.error(data.msg)
          }
        })
      })
    },
    //获取所属区域
    getOrg() {
      this.$http({
        url: this.$http.adornUrl(`/admin/org/getOrgTree`),
        method: 'get',
        params: this.$http.adornParams()
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.orgList = this.getTreeData(data.obj)
        } else {
          this.orgList = []
        }
      })
    },
    getFields() {
      this.$http({
        url: this.$http.adornUrl('/admin/zyz/platform/belongFieldDict/cascade'),
        method: 'get'
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.fields = this.getTreeData(data.obj)
        } else {
          this.fields = []
        }
      })
    },
    // *处理点位分类最后children数组为空的状态
    getTreeData: function (data) {
      var that = this
      // 循环遍历json数据
      data.forEach(function (e) {
        if (!e.children || e.children.length < 1) {
          e.children = undefined
        } else {
          // children若不为空数组，则继续 递归调用 本方法
          that.getTreeData(e.children)
        }
      })
      return data
    },
    // 上架
    updateAutoStatus(id, autoStatus) {
      this.$confirm(`确定进行[${!autoStatus ? '上架' : '下架'}]操作?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        closeOnClickModal: false
      }).then(() => {
        this.$http({
          url: this.$http.adornUrl('/admin/zyz/requirement/updateAutoStatus'),
          method: 'get',
          params: this.$http.adornParams({
            'id': id
          })
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.$message({
              message: '操作成功',
              type: 'success',
              duration: 1500,
              onClose: () => {
                this.getDataList()
              }
            })
          } else {
            this.$message.error(data.msg)
          }
        })
      })
    },
    // 下载文件
    exportReqirement() {
      this.fullscreenLoading = true;
      this.$http({
        url: this.$http.adornUrl('/admin/zyz/requirement/exportRequirement'),
        method: 'post',
        responseType: 'arraybuffer',
        data: this.$http.adornData({
          'key': this.dataForm.key,
          'type': this.dataForm.type,
          'serviceObj': this.dataForm.serviceObj,
          'status': this.dataForm.status,
          'autoStatus': this.dataForm.autoStatus,
          'publishOrgCode': this.dataForm.publishOrgCode,
          'startTime': this.dataForm.timeRange != null ? this.dataForm.timeRange[0] : null,
          'endTime': this.dataForm.timeRange != null ? this.dataForm.timeRange[1] : null,
          'fieldId': this.dataForm.fieldId
        })
      }).then(({data}) => {
        if (data.code && data.code !== 0) {
          this.$message.error('导出失败')
        } else {
          this.fullscreenLoading = false
          let blob = new Blob([data], {type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8'})
          let objectUrl = URL.createObjectURL(blob)
          let a = document.createElement('a')
          // window.location.href = objectUrl
          a.href = objectUrl
          a.download = '需求列表.xlsx'
          a.click()
          URL.revokeObjectURL(objectUrl)
          this.$message({
            type: 'success',
            message: '导出成功'
          })
        }
      })
    },
    // 同步
    syncHandle(id) {
      this.$confirm(`确定要进行同步嘛，请勿重复操作，否则可能会带来不可预知的后果`, '确认同步？', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        closeOnClickModal: false
      }).then(() => {
        this.dataListLoading = true
        this.$http({
          url: this.$http.adornUrl('/admin/zyz/platform-sync/syncRequirement'),
          method: 'post',
          params: this.$http.adornParams({
            'requirementId': id
          })
        }).then(({data}) => {
          this.dataListLoading = false
          if (data && data.code === 0) {
            this.$message({
              message: '请求同步成功，市平台返回：（' + data.obj.message + '）',
              type: data.obj.resultCode === 'SUCCESS' ? 'success' : 'warning',
              duration: 3000,
              onClose: () => {
                this.getDataList()
              }
            })
          } else {
            this.$message.error(data.msg)
          }
        })
      })
    },
    zsqDockingRecords(id) {
        this.zsqDockingRecordsVisible = true
        this.$nextTick(() => {
            this.$refs.zsqDockingRecords.init(id)
        })
    }
  }
}
</script>

