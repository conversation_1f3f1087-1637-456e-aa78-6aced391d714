<template>
  <div>
    <el-dialog
        title="需求详情"
        :close-on-click-modal="false"
        :visible.sync="visible" width="80%">
      <el-form :model="dataForm" ref="dataForm" label-width="120px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="需求名称" prop="name">
              <el-input style="width: 100%" disabled v-model="dataForm.name" placeholder="需求名称"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="需求类型" prop="type">
              <el-dict style="width: 100%" :code="'REQUIREMENT_TYPE'" disabled v-model="dataForm.type"></el-dict>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="需求状态" prop="status" v-if="!dockingCreate || dockingCreate === false">
              <el-dict style="width: 100%" :code="'requirement_status'" disabled v-model="dataForm.status"></el-dict>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="所属领域" prop="fieldIds">
              <el-cascader style="width: 100%" disabled placeholder="请选择" v-model="dataForm.fieldIds" :options="fields"
                           :props="{ label: 'typeName', value: 'typeId' }"></el-cascader>
            </el-form-item>
          </el-col>
          <el-col :span="12" v-if="dockingCreate && dockingCreate === true">
            <el-form-item label="需求时间" prop="timeRange">
              <el-date-picker style="width: 100%" v-model="dataForm.timeRange" clearable type="datetimerange" disabled
                              value-format="yyyy-MM-dd HH:mm:ss"
                              align="right" start-placeholder="开始时间" end-placeholder="结束时间"
                              :default-time="['00:00:00', '23:59:59']">
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20" v-if="!dockingCreate || dockingCreate === false">
          <el-col :span="12">
            <el-form-item label="需求开始时间" prop="startTime">
              <el-date-picker style="width: 100%" v-model="dataForm.startTime" disabled
                              value-format="yyyy-MM-dd HH:mm:ss" type="datetime"
                              placeholder="需求开始时间">
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="需求截止时间" prop="endTime">
              <el-date-picker style="width: 100%" v-model="dataForm.endTime" disabled value-format="yyyy-MM-dd HH:mm:ss"
                              type="datetime"
                              placeholder="需求截止时间">
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="服务对象" prop="serviceObj">
              <el-dict style="width: 100%" :code="'SERVICE_OBJECT'" disabled v-model="dataForm.serviceObj"></el-dict>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="服务地址" prop="serviceAddress">
              <el-input style="width: 100%" v-model="dataForm.serviceAddress" disabled placeholder="服务地址"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="联系人" prop="contactPerson">
              <el-input style="width: 100%" v-model="dataForm.contactPerson" disabled placeholder="联系人"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="联系电话" prop="contactPhone">
              <el-input style="width: 100%" v-model="dataForm.contactPhone" disabled placeholder="联系电话"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="需求详情" prop="detail">
              <el-input style="width: 100%" type="textarea" v-model="dataForm.detail" disabled
                        placeholder="需求详情" rows="5"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
<!--        <el-row :gutter="20" v-if="!dockingCreate || dockingCreate === false">-->
<!--          <el-col :span="12">-->
<!--            <el-form-item label="对接组织" prop="dockingOrgName">-->
<!--              <el-input style="width: 100%" v-model="dataForm.dockingOrgName" disabled placeholder="对接组织"></el-input>-->
<!--            </el-form-item>-->
<!--          </el-col>-->
<!--          <el-col :span="12">-->
<!--            <el-form-item label="对接时间" prop="dockingTime">-->
<!--              <el-input style="width: 100%" v-model="dataForm.dockingTime" disabled placeholder="对接时间"></el-input>-->
<!--            </el-form-item>-->
<!--          </el-col>-->
<!--        </el-row>-->
<!--        <el-row :gutter="20" v-if="!dockingCreate || dockingCreate === false">-->
<!--          <el-col :span="12">-->
<!--            <el-form-item label="对接人" prop="dockingContactPerson">-->
<!--              <el-input v-model="dataForm.dockingContactPerson" disabled placeholder="对接人"></el-input>-->
<!--            </el-form-item>-->
<!--          </el-col>-->
<!--          <el-col :span="12">-->
<!--            <el-form-item label="对接人联系方式" prop="dockingContactPhone">-->
<!--              <el-input v-model="dataForm.dockingContactPhone" disabled placeholder="对接人联系方式"></el-input>-->
<!--            </el-form-item>-->
<!--          </el-col>-->
<!--        </el-row>-->
        <el-row :gutter="20" v-if="!dockingCreate || dockingCreate === false">
          <el-col :span="12">
            <el-form-item label="上下架状态" prop="autoStatus">
              <el-radio-group v-model="dataForm.autoStatus" disabled>
                <el-radio :label=false>下架</el-radio>
                <el-radio :label=true>上架</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="关联活动" prop="detail">
              <el-input style="width: 100%" disabled v-model="dataForm.linkActivityName" placeholder="关联活动"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20" v-if="!dockingCreate || dockingCreate === false">
          <el-col :span="12">
            <el-form-item label="活动创建组织" prop="linkActivityCodeName">
              <el-input style="width: 100%" v-model="dataForm.linkActivityCodeName" disabled
                        placeholder="活动创建组织"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="活动起止时间" prop="linkActivityTimeRange">
              <el-date-picker style="width: 100%" disabled v-model="dataForm.linkActivityTimeRange" clearable type="datetimerange"
                              value-format="yyyy-MM-dd HH:mm:ss"
                              align="right" start-placeholder="开始时间" end-placeholder="结束时间"
                              :default-time="['00:00:00', '23:59:59']">
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20" v-if="!dockingCreate || dockingCreate === false">
          <el-col :span="12">
            <el-form-item label="活动星级" prop="evaluateStar">
              <el-rate
                  style="vertical-align: middle; display: inline-block"
                  v-model="dataForm.evaluateStar"
                  disabled
                  text-color="#ff9900">
              </el-rate>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="评价内容" prop="evaluateRemark">
              <el-input style="width: 100%" disabled type="textarea" v-model="dataForm.evaluateRemark"
                        placeholder="评价内容" rows="3"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="图片" prop="picture">
              <el-image
                  :src="$http.adornAttachmentUrl(dataForm.picture)"
                  :preview-src-list="[$http.adornAttachmentUrl(dataForm.picture)]"
                  class="avatar"
              >
              </el-image>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20" v-if="dockingCreate && dockingCreate === true">
          <el-col :span="12">
            <el-form-item prop="autoEnable">
              <el-checkbox v-model="dataForm.autoEnable" disabled>审核通过后立即上架</el-checkbox>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <el-table v-if="!dockingCreate || dockingCreate === false"
                :data="dataList"
                border
                style="width: 100%;">
        <el-table-column
            type="index"
            align="center"
            label="序号"
            width="50">
        </el-table-column>
        <el-table-column align="center" label="日志记录">
          <el-table-column
              prop="operateTypeText"
              header-align="center"
              width="150"
              :show-overflow-tooltip="true"
              align="center"
              label="操作类型">
          </el-table-column>
          <el-table-column
              prop="operatorName"
              header-align="center"
              align="center"
              width="120"
              label="操作人">
          </el-table-column>
          <el-table-column
              prop="operatorOrgName"
              header-align="center"
              align="center"
              width="150"
              label="所在组织">
          </el-table-column>
          <el-table-column
              prop="operateTime"
              header-align="center"
              align="center"
              width="160"
              label="操作时间">
          </el-table-column>
          <el-table-column
              prop="remark"
              header-align="center"
              align="center"
              label="操作结果">
          </el-table-column>
        </el-table-column>
      </el-table>
      <span slot="footer" class="dialog-footer">
        <el-button @click="visible = false">关闭</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
export default {
  data() {
    return {
      visible: false,
      dataList: [],
      fields: [],
      dockingCreate: false,
      dataForm: {
        id: null,
        version: null,
        name: '',
        publishOrgCode: '',
        publishOrgName: '',
        type: '',
        belongFieldTop: '',
        belongFieldEnd: '',
        startTime: '',
        endTime: '',
        picture: '',
        detail: '',
        contactPerson: '',
        contactPhone: '',
        status: '',
        autoEnable: false,
        remark: '',
        serviceObj: '',
        serviceAddress: '',
        // dockingOrgCode: '',
        // dockingOrgName: '',
        publishActivity: '',
        // dockingContactPerson: '',
        // dockingContactPhone: '',
        evaluateStar: '',
        evaluateTime: '',
        evaluateRemark: '',
        fieldIds: [],
        fieldId: null,
        linkActivityName: '',
        linkActivityCodeName: '',
        linkActivityTimeRange: []
      }
    }
  },
  methods: {
    init(id) {
      this.dataForm.id = id || null
      this.dockingCreate = false
      this.getLogs()
      this.getFields()
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm']?.resetFields()
        if (this.dataForm.id) {
          this.$http({
            url: this.$http.adornUrl(`/admin/zyz/requirement/getRequirementById`),
            method: 'get',
            params: this.$http.adornParams({id: this.dataForm.id})
          }).then(({data}) => {
            if (data && data.code === 0) {
              this.dataForm = data.obj
              this.dataForm.fieldIds = [data.obj.belongFieldTop, data.obj.belongFieldEnd]
              console.log(data.obj.linkActivityStartTime + data.obj.linkActivityEndTime)
              this.$set(this.dataForm, 'linkActivityTimeRange', [data.obj.linkActivityStartTime, data.obj.linkActivityEndTime])
            }
          })
        }
      })
    },
    initFromAct(id) {
      this.dataForm.id = id || null
      this.dockingCreate = true
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm']?.resetFields()
        if (this.dataForm.id) {
          this.$http({
            url: this.$http.adornUrl(`/admin/zyz/requirement_temp`),
            method: 'get',
            params: this.$http.adornParams({id: this.dataForm.id})
          }).then(({data}) => {
            if (data && data.code === 0) {
              this.dataForm = data.obj
              this.dataForm.fieldIds = [data.obj.belongFieldTop, data.obj.belongFieldEnd]
              this.$set(this.dataForm, 'timeRange', [data.obj.startTime, data.obj.endTime])
            }
          })
        }
      })
    },
    //获取所属区域
    getLogs() {
      this.$http({
        url: this.$http.adornUrl(`/admin/zyz/requirement/log/getLogsByRequirementId`),
        method: 'get',
        params: this.$http.adornParams({requirementId: this.dataForm.id})
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.dataList = data.obj
        } else {
          this.dataList = []
        }
      })
    },
    getFields() {
      this.$http({
        url: this.$http.adornUrl('/admin/zyz/platform/belongFieldDict/cascade'),
        method: 'get'
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.fields = this.getTreeData(data.obj)
        } else {
          this.fields = []
        }
      })
    },
    // *处理点位分类最后children数组为空的状态
    getTreeData: function (data) {
      var that = this
      // 循环遍历json数据
      data.forEach(function (e) {
        if (!e.children || e.children.length < 1) {
          e.children = undefined
        } else {
          // children若不为空数组，则继续 递归调用 本方法
          that.getTreeData(e.children)
        }
      })
      return data
    }
  }
}
</script>
<style lang="scss">
</style>
