<template>
  <el-dialog title="修改并同步" :close-on-click-modal="false" append-to-body width="80%" :visible.sync="visible">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" label-width="110px" style="margin-right: 20px">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="需求名称" prop="name">
            <el-input v-model="dataForm.name" placeholder="需求名称"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="需求类型" prop="type">
            <el-dict :code="'REQUIREMENT_TYPE'" v-model="dataForm.type"></el-dict>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="所属领域" prop="fieldIds">
            <el-cascader style="width: 100%" placeholder="请选择" v-model="dataForm.fieldIds" :options="fields"
                         @change="(value) => { handleChange(value) }" clearable
                         :props="{ label: 'typeName', value: 'typeId' }"></el-cascader>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="需求时间" prop="timeRange">
            <el-date-picker style="width: 100%" v-model="dataForm.timeRange" clearable type="datetimerange"
                            value-format="yyyy-MM-dd HH:mm:ss"
                            align="right" start-placeholder="开始时间" end-placeholder="结束时间"
                            :default-time="['00:00:00', '23:59:59']">
            </el-date-picker>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="服务对象" prop="serviceObj">
            <el-dict :code="'SERVICE_OBJECT'" v-model="dataForm.serviceObj"></el-dict>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="服务地址" prop="serviceAddress">
            <el-input v-model="dataForm.serviceAddress" placeholder="服务地址"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="联系人" prop="contactPerson">
            <el-input v-model="dataForm.contactPerson" placeholder="联系人"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="联系电话" prop="contactPhone">
            <el-input v-model="dataForm.contactPhone" placeholder="联系电话"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="需求详情" prop="detail">
            <el-input type="textarea" v-model="dataForm.detail" placeholder="需求详情" rows="5" maxlength="1000" show-word-limit></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="图片" prop="picture">
            <el-upload class="avatar-uploader" :action="this.$http.adornUrl(`/admin/oss/upload`)" :headers="myHeaders"
                       :data="{ serverCode: this.serverCode, media: false }" :show-file-list="false"
                       :on-success="handleAvatarSuccess" :before-upload="beforeAvatarUpload">
              <img v-if="dataForm.picture" :src="$http.adornAttachmentUrl(dataForm.picture)" class="avatar">
              <i v-else class="el-icon-plus avatar-uploader-icon"></i>
              <div slot="tip" class="el-upload__tip" style="color: red">尺寸建议600x1000像素，文件格式jpg、bmp或png，大小不超2M</div>
            </el-upload>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item prop="autoEnable">
            <el-checkbox v-model="dataForm.autoEnable">审核通过后立即上架</el-checkbox>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button :disabled="isDisabled" type="success" @click="dataFormSubmit()">保存</el-button>
    </span>
  </el-dialog>
</template>

<script>
import moment from 'moment'
import Vue from 'vue'
import _ from 'lodash'
import {is8lPhone, isMobile} from "@/utils/validate";

export default {
  data() {
    const validateContactPhone = (rule, value, callback) => {
      if (!isMobile(value) && !is8lPhone(value)) {
        callback(new Error('联系电话须为手机号或区号+8位座机号例如051288888888或0512-88888888'))
      } else {
        callback()
      }
    }
    return {
      visible: false,
      isDisabled: false,
      serverCode: 'LocalServer',
      myHeaders: {Authorization: Vue.cookie.get('Authorization')},
      uploadData: {serverCode: 'LocalServer', media: false},
      fields: [],
      dataForm: {},
      initForm: {
        id: null,
        name: '',
        type: '',
        belongFieldTop: '',
        belongFieldEnd: '',
        startTime: '',
        endTime: '',
        picture: '',
        detail: '',
        contactPerson: this.$store.state.user.nickName,
        contactPhone: this.$store.state.user.phone,
        autoEnable: true,
        serviceObj: '',
        serviceAddress: '',
        fieldIds: [],
        fieldId: null,
        timeRange: []
      },
      dataRule: {
        type: [
          {required: true, message: '需求类型不能为空', trigger: 'blur'}
        ],
        fieldIds: [
          {required: true, message: '所属领域不能为空', trigger: 'blur'}
        ],
        timeRange: [
          {required: true, message: '需求时间不能为空', trigger: 'blur'}
        ],
        detail: [
          {required: true, message: '需求详情不能为空', trigger: 'blur'}
        ],
        picture: [
          {required: true, message: '图片不能为空', trigger: 'blur'}
        ],
        contactPerson: [
          {required: true, message: '联系人不能为空', trigger: 'blur'}
        ],
        contactPhone: [
          {required: true, message: '联系电话不能为空', trigger: 'blur'},
          {validator: validateContactPhone, trigger: 'blur'}
        ],
        serviceObj: [
          {required: true, message: '服务对象不能为空', trigger: 'blur'}
        ],
        serviceAddress: [
          {required: true, message: '服务地址不能为空', trigger: 'blur'}
        ],
        name: [
          {required: true, message: '需求名称不能为空', trigger: 'blur'}
        ]
      }
    }
  },
  components: {
    moment
  },
  methods: {
    init(id) {
      this.dataForm = _.cloneDeep(this.initForm)
      this.dataForm.id = id || null
      this.visible = true
      this.getFields()
      this.$nextTick(() => {
        this.$refs['dataForm']?.resetFields()
        if (this.dataForm.id) {
          this.$http({
            url: this.$http.adornUrl(`/admin/zyz/requirement/getRequirementForUpdateSync`),
            method: 'get',
            params: this.$http.adornParams({id: this.dataForm.id})
          }).then(({data}) => {
            if (data && data.code === 0) {
              this.dataForm = data.obj
              this.dataForm.fieldIds = [data.obj.belongFieldTop, data.obj.belongFieldEnd]
              this.$set(this.dataForm, 'timeRange', [data.obj.startTime, data.obj.endTime])
            }
          })
        }
      })
    },
    getFields() {
      this.$http({
        url: this.$http.adornUrl('/admin/zyz/platform/belongFieldDict/cascade'),
        method: 'get'
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.fields = this.getTreeData(data.obj)
        } else {
          this.fields = []
        }
      })
    },
    // *处理点位分类最后children数组为空的状态
    getTreeData: function (data) {
      var that = this
      // 循环遍历json数据
      data.forEach(function (e) {
        if (!e.children || e.children.length < 1) {
          e.children = undefined
        } else {
          // children若不为空数组，则继续 递归调用 本方法
          that.getTreeData(e.children)
        }
      })
      return data
    },
    // 提交
    dataFormSubmit() {
      this.dataForm.startTime = this.dataForm.timeRange && this.dataForm.timeRange[0]
      this.dataForm.endTime = this.dataForm.timeRange && this.dataForm.timeRange[1]
      this.dataForm.belongFieldTop = this.dataForm.fieldIds[0]
      this.dataForm.belongFieldEnd = this.dataForm.fieldIds[1]
      let method = '/admin/zyz/requirement/updateForReSync'
      this.$confirm(`确定保存本次修改? 提交后无法撤回！`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        closeOnClickModal: false
      }).then(() => {
        this.submitFun(method, 'refreshDataList')
      })
    },
    submitFun(method, callback) {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.isDisabled = true
          this.$http({
            url: this.$http.adornUrl(method),
            method: 'post',
            data: this.$http.adornData(this.dataForm)
          }).then(({data}) => {
            if (data && data.code === 0) {
              this.visible = false
              this.isDisabled = false
              this.$emit(callback)
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 1500
              })
            } else {
              this.$message.error(data.msg)
              this.isDisabled = false
            }
          })
        }
      })
    },
    handleChange(value) {
      this.dataForm.fieldId = value[value.length - 1]
    },
    handleAvatarSuccess(res, file) {
      if (res.success) {
        this.$message({
          message: '操作成功',
          type: 'success',
          duration: 1500
        })
        this.dataForm.picture = res.obj.path
      } else {
        this.$message.error('上传失败')
      }
    },
    beforeAvatarUpload: function (file) {
      let isAccept = ['image/jpg', 'image/jpeg', 'image/png', 'image/bmp'].indexOf(file.type) !== -1
      let isLt2M = file.size / 1024 / 1024 < 2

      if (!isAccept) {
        this.$message.error('上传图片只能是图片!')
      }
      if (!isLt2M) {
        this.$message.error('上传文件大小不能超过 2MB!')
      }
      return isAccept && isLt2M
    }
  }
}
</script>

