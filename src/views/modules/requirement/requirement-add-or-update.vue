<template>
  <el-dialog :title="!dataForm.id ? (initFromAct ? '需求创建' : '新增') : (initFromAct ? '需求编辑' : '修改')"
             :close-on-click-modal="false" append-to-body width="80%" :visible.sync="visible">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm"
             label-width="110px" style="margin-right: 20px">
      <el-row :gutter="20">
        <el-col :span="12">
          <div v-if="initFromAct" style="height:15px; color: red; font-size: xx-small; margin-left: 110px; margin-top: -15px">（请规范需求名称，如xxx需求，不建议使用活动名称）</div>
          <el-form-item label="需求名称" prop="name" :error="nameError">
            <el-input v-model="dataForm.name" placeholder="需求名称"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="需求类型" prop="type" :error="typeError">
            <el-dict :code="'REQUIREMENT_TYPE'" v-model="dataForm.type"></el-dict>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <!-- <el-col :span="14">
          <el-form-item label="所属领域" prop="belongFieldTop" :error="belongFieldTopError">
            <el-input v-model="dataForm.belongFieldTop" placeholder="所属领域一级"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item prop="belongFieldEnd" :error="belongFieldEndError">
            <el-input v-model="dataForm.belongFieldEnd" placeholder="所属领域二级"></el-input>
          </el-form-item>
        </el-col> -->
        <el-col :span="12">
          <el-form-item label="所属领域" prop="fieldIds">
            <el-cascader style="width: 100%" placeholder="请选择" v-model="dataForm.fieldIds" :options="fields"
                         @change="(value) => { handleChange(value, 'field') }" clearable
                         :props="{ label: 'typeName', value: 'typeId' }"></el-cascader>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="需求时间" prop="timeRange">
            <el-date-picker style="width: 100%" v-model="dataForm.timeRange" clearable type="datetimerange"
                            value-format="yyyy-MM-dd HH:mm:ss"
                            align="right" start-placeholder="开始时间" end-placeholder="结束时间"
                            :default-time="['00:00:00', '23:59:59']">
            </el-date-picker>
          </el-form-item>

        </el-col>
      </el-row>
      <el-row :gutter="20">
        <!-- <el-col :span="12">
          <el-form-item label="需求开始时间" prop="startTime">
            <el-date-picker v-model="dataForm.startTime" value-format="yyyy-MM-dd HH:mm:ss" type="datetime"
              placeholder="需求开始时间">
            </el-date-picker>
          </el-form-item>
        </el-col> -->
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="服务对象" prop="serviceObj" :error="serviceObjError">
            <el-dict :code="'SERVICE_OBJECT'" v-model="dataForm.serviceObj"></el-dict>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="服务地址" prop="serviceAddress" :error="serviceAddressError">
            <el-input v-model="dataForm.serviceAddress" placeholder="服务地址"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="联系人" prop="contactPerson" :error="contactPersonError">
            <el-input v-model="dataForm.contactPerson" placeholder="联系人"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="联系电话" prop="contactPhone" :error="contactPhoneError">
            <el-input v-model="dataForm.contactPhone" placeholder="联系电话"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="需求详情" prop="detail" :error="detailError">
            <el-input type="textarea" v-model="dataForm.detail" placeholder="需求详情" rows="5" maxlength="1000" show-word-limit></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="图片" prop="picture" :error="pictureError">
            <el-upload class="avatar-uploader" :action="this.$http.adornUrl(`/admin/oss/upload`)" :headers="myHeaders"
                       :data="{ serverCode: this.serverCode, media: false }" :show-file-list="false"
                       :on-success="handleAvatarSuccess" :before-upload="beforeAvatarUpload">
              <img v-if="dataForm.picture" :src="$http.adornAttachmentUrl(dataForm.picture)" class="avatar">
              <i v-else class="el-icon-plus avatar-uploader-icon"></i>
              <div slot="tip" class="el-upload__tip" style="color: red">尺寸建议600x1000像素，文件格式jpg、bmp或png，大小不超2M</div>
            </el-upload>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item prop="autoEnable" :error="autoEnableError">
            <el-checkbox v-model="dataForm.autoEnable">审核通过后立即上架</el-checkbox>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button v-if="!initFromAct" type="primary" @click="dataFormSubmit(false)">保存草稿</el-button>
      <el-button v-if="!initFromAct" :disabled="isDisabled" type="success" @click="dataFormSubmit(true)">提交审核</el-button>
      <el-button v-if="initFromAct" type="success" @click="dataFormSubmitFromAct()">保存</el-button>
    </span>
  </el-dialog>
</template>

<script>
import moment from 'moment'
import Vue from 'vue'
import _ from 'lodash'
import {is8lPhone, isMobile} from "@/utils/validate";

export default {
  data() {
    const validateContactPhone = (rule, value, callback) => {
      if (!isMobile(value) && !is8lPhone(value)) {
        callback(new Error('联系电话须为手机号或区号+8位座机号例如051288888888或0512-88888888'))
      } else {
        callback()
      }
    }
    return {
      visible: false,
      isDisabled: false,
      serverCode: 'LocalServer',
      myHeaders: {Authorization: Vue.cookie.get('Authorization')},
      uploadData: {serverCode: 'LocalServer', media: false},
      isCopy: '',
      fields: [],
      initFromAct: false,
      dataForm: {},
      initForm: {
        id: null,
        version: null,
        name: '',
        publishOrgCode: '',
        publishOrgName: '',
        type: '',
        belongFieldTop: '',
        belongFieldEnd: '',
        startTime: '',
        endTime: '',
        picture: '',
        detail: '',
        contactPerson: this.$store.state.user.nickName,
        contactPhone: this.$store.state.user.phone,
        status: '',
        autoEnable: true,
        remark: '',
        serviceObj: '',
        serviceAddress: '',
        dockingOrgCode: '',
        dockingOrgName: '',
        publishActivity: '',
        dockingContactPerson: '',
        dockingContactPhone: '',
        evaluateStar: '',
        evaluateTime: '',
        evaluateRemark: '',
        fieldIds: [],
        fieldId: null,
        timeRange: []
      },
      dataRule: {
        publishOrgCode: [
          {required: true, message: '发布组织code不能为空', trigger: 'blur'}
        ],
        publishOrgName: [
          {required: true, message: '发布组织不能为空', trigger: 'blur'}
        ],
        type: [
          {required: true, message: '需求类型不能为空', trigger: 'blur'}
        ],
        fieldIds: [
          {required: true, message: '所属领域不能为空', trigger: 'blur'}
        ],
        timeRange: [
          {required: true, message: '需求时间不能为空', trigger: 'blur'}
        ],
        detail: [
          {required: true, message: '需求详情不能为空', trigger: 'blur'}
        ],
        picture: [
          {required: true, message: '图片不能为空', trigger: 'blur'}
        ],
        contactPerson: [
          {required: true, message: '联系人不能为空', trigger: 'blur'}
        ],
        contactPhone: [
          {required: true, message: '联系电话不能为空', trigger: 'blur'},
          {validator: validateContactPhone, trigger: 'blur'}
        ],
        status: [
          {required: true, message: '审核状态（数据字典）不能为空', trigger: 'blur'}
        ],
        serviceObj: [
          {required: true, message: '服务对象不能为空', trigger: 'blur'}
        ],
        serviceAddress: [
          {required: true, message: '服务地址不能为空', trigger: 'blur'}
        ],
        publishActivity: [
          {required: true, message: '是否发布活动不能为空', trigger: 'blur'}
        ],
        name: [
          {required: true, message: '需求名称不能为空', trigger: 'blur'}
        ]
      },
      nameError: null,
      publishOrgCodeError: null,
      publishOrgNameError: null,
      typeError: null,
      belongFieldTopError: null,
      belongFieldEndError: null,
      startTimeError: null,
      endTimeError: null,
      pictureError: null,
      detailError: null,
      contactPersonError: null,
      contactPhoneError: null,
      statusError: null,
      autoEnableError: null,
      remarkError: null,
      serviceObjError: null,
      serviceAddressError: null,
      dockingOrgCodeError: null,
      dockingOrgNameError: null,
      publishActivityError: null,
      dockingContactPersonError: null,
      dockingContactPhoneError: null,
      evaluateStarError: null,
      evaluateTimeError: null,
      evaluateRemarkError: null
    }
  },
  components: {
    moment
  },
  methods: {
    init(id, isCopy) {
      this.dataForm = _.cloneDeep(this.initForm)
      this.dataForm.id = id || null
      this.isCopy = isCopy
      this.initFromAct = false
      this.visible = true
      this.getFields()
      this.$nextTick(() => {
        this.$refs['dataForm']?.resetFields()
        if (this.dataForm.id) {
          this.$http({
            url: this.$http.adornUrl(`/admin/zyz/requirement/getRequirementById`),
            method: 'get',
            params: this.$http.adornParams({id: this.dataForm.id})
          }).then(({data}) => {
            if (data && data.code === 0) {
              if (!isCopy) {
                this.dataForm = data.obj
              }
              // 复制仅赋部分字段
              else {
                let tmp = {}
                tmp.id = null
                tmp.name = data.obj.name
                tmp.type = data.obj.type
                tmp.belongFieldTop = data.obj.belongFieldTop
                tmp.belongFieldEnd = data.obj.belongFieldEnd
                tmp.startTime = data.obj.startTime
                tmp.endTime = data.obj.endTime
                tmp.picture = data.obj.picture
                tmp.detail = data.obj.detail
                tmp.contactPerson = data.obj.contactPerson
                tmp.contactPhone = data.obj.contactPhone
                tmp.autoEnable = data.obj.autoEnable
                tmp.remark = data.obj.remark
                tmp.serviceObj = data.obj.serviceObj
                tmp.serviceAddress = data.obj.serviceAddress
                tmp.version = 0
                this.dataForm = tmp
              }
              this.dataForm.fieldIds = [data.obj.belongFieldTop, data.obj.belongFieldEnd]
              this.$set(this.dataForm, 'timeRange', [data.obj.startTime, data.obj.endTime])
            }
          })
        }
      })
    },
    fromActInit(id, actForm, fromBigActivity) {
      this.dataForm = _.cloneDeep(this.initForm)
      this.dataForm.id = id || null
      this.isCopy = false
      this.initFromAct = true
      this.visible = true
      this.getFields()
      this.$nextTick(() => {
        this.$refs['dataForm']?.resetFields()
        if (this.dataForm.id) {
          this.$http({
            url: this.$http.adornUrl(`/admin/zyz/requirement_temp`),
            method: 'get',
            params: this.$http.adornParams({id: this.dataForm.id})
          }).then(({data}) => {
            if (data && data.code === 0) {
              this.dataForm = data.obj
              this.dataForm.fieldIds = [data.obj.belongFieldTop, data.obj.belongFieldEnd]
              this.$set(this.dataForm, 'timeRange', [data.obj.startTime, data.obj.endTime])
            }
          })
        } else {
          if (actForm) {
            // let bfn = actForm.belongFieldNameEnd || ''
            // let fwIndex = bfn.lastIndexOf('服务')
            // bfn = (fwIndex === bfn.length - 2 ? bfn.substr(0, fwIndex) : bfn)
            // this.dataForm.name = (actForm.name || '') + bfn  +'需求'
            this.dataForm.name = actForm.name
            this.dataForm.belongFieldTop = actForm.belongFieldTop
            this.dataForm.belongFieldEnd = actForm.belongFieldEnd
            this.dataForm.fieldIds = actForm.fieldIds
            this.dataForm.startTime = actForm.startTime
            this.dataForm.endTime = actForm.endTime
            this.$set(this.dataForm, 'timeRange', actForm.timeRange)
            this.dataForm.serviceObj = 'SO_older'
            this.dataForm.serviceAddress = actForm.address
            this.dataForm.detail = actForm.actSynopsis
            this.dataForm.picture = actForm.picture
            this.dataForm.teamId = actForm.teamId
            if (fromBigActivity && fromBigActivity === true) {
              this.dataForm.type = 'RT_team'
              this.dataForm.serviceObj = 'SO_others'
            }
          }
        }
      })
    },
    getFields() {
      this.$http({
        url: this.$http.adornUrl('/admin/zyz/platform/belongFieldDict/cascade'),
        method: 'get'
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.fields = this.getTreeData(data.obj)
        } else {
          this.fields = []
        }
      })
    },
    // *处理点位分类最后children数组为空的状态
    getTreeData: function (data) {
      var that = this
      // 循环遍历json数据
      data.forEach(function (e) {
        if (!e.children || e.children.length < 1) {
          e.children = undefined
        } else {
          // children若不为空数组，则继续 递归调用 本方法
          that.getTreeData(e.children)
        }
      })
      return data
    },
    // 提交
    dataFormSubmit(isRender) {
      this.dataForm.startTime = this.dataForm.timeRange && this.dataForm.timeRange[0]
      this.dataForm.endTime = this.dataForm.timeRange && this.dataForm.timeRange[1]
      this.dataForm.belongFieldTop = this.dataForm.fieldIds[0]
      this.dataForm.belongFieldEnd = this.dataForm.fieldIds[1]
      let method = ''
      if (isRender) {
        method = '/admin/zyz/requirement/renderByRequirement'
      } else {
        method = `/admin/zyz/requirement/${!this.dataForm.id ? 'saveRequirement' : 'update'}`
      }
      if (isRender) {
        this.$confirm(`确定进行提交操作? 提交后无法撤回！`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
          closeOnClickModal: false
        }).then(() => {
          this.submitFun(method, 'refreshDataList')
        })
      } else {
        this.submitFun(method, 'refreshDataList')
      }
    },
    submitFun(method, callback) {
      this.$refs['dataForm'].validate((valid) => {
        console.log(valid)
        if (valid) {
          this.isDisabled = true
          this.$http({
            url: this.$http.adornUrl(method),
            method: 'post',
            data: this.$http.adornData(this.dataForm)
          }).then(({data}) => {
            if (data && data.code === 0) {
              this.visible = false
              this.isDisabled = false
              if (callback === 'reqCreateCallback') {
                this.$emit(callback, this.dataForm.name, data.obj || this.dataForm.id)
              } else {
                this.$emit(callback)
              }
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 1500
              })
            } else {
              this.$message.error(data.msg)
              this.isDisabled = false
            }
          })
        }
      })
    },
    handleChange(value, type) {
      if (type === 'field') {
        this.dataForm.fieldId = value[value.length - 1]
      }
      if (type === 'org') {
        this.dataForm.publishOrgCode = value[value.length - 1]
      }
    },
    handleAvatarSuccess(res, file) {
      if (res.success) {
        this.$message({
          message: '操作成功',
          type: 'success',
          duration: 1500
        })
        this.dataForm.picture = res.obj.path
      } else {
        this.$message.error('上传失败')
      }
    },
    beforeAvatarUpload: function (file) {
      let isAccept = ['image/jpg', 'image/jpeg', 'image/png', 'image/bmp'].indexOf(file.type) !== -1
      let isLt2M = file.size / 1024 / 1024 < 2

      if (!isAccept) {
        this.$message.error('上传图片只能是图片!')
      }
      if (!isLt2M) {
        this.$message.error('上传文件大小不能超过 2MB!')
      }
      return isAccept && isLt2M
    },
    dataFormSubmitFromAct() {
      this.dataForm.startTime = this.dataForm.timeRange && this.dataForm.timeRange[0]
      this.dataForm.endTime = this.dataForm.timeRange && this.dataForm.timeRange[1]
      this.dataForm.belongFieldTop = this.dataForm.fieldIds[0]
      this.dataForm.belongFieldEnd = this.dataForm.fieldIds[1]
      let method = `/admin/zyz/requirement_temp/${!this.dataForm.id ? 'saveForId' : 'update'}`
      // this.$confirm(`确定进行提交操作?`, '提示', {
      //   confirmButtonText: '确定',
      //   cancelButtonText: '取消',
      //   type: 'warning',
      //   closeOnClickModal: false
      // }).then(() => {
        this.submitFun(method, 'reqCreateCallback')
      // })
    }
  }
}
</script>

