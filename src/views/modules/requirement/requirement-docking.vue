<template>
  <div class="mod-role">
    <el-form :inline="true" ref="dataForm" :model="dataForm" @keyup.enter.native="getDataList()">
      <el-form-item label="关键字" prop="key" style="padding-right: 40px">
        <el-input v-model="dataForm.key" placeholder="需求名称/联系人/联系电话" clearable style="width: 120%"></el-input>
      </el-form-item>
      <el-form-item label="需求方" prop="publishOrgCodes">
        <el-cascader
            placeholder="请选择"
            v-model="dataForm.publishOrgCodes"
            :options="orgList"
            :disabled="this.dataForm.id"
            :show-all-levels="false"
            clearable
            :props="{checkStrictly: true, value: 'code',label: 'name'}"
            @change="(value) => {handleChange(value, 'org')}"
        ></el-cascader>
      </el-form-item>
      <el-form-item label="需求类型" prop="type">
        <el-dict :code="'REQUIREMENT_TYPE'" v-model="dataForm.type"></el-dict>
      </el-form-item>
      <el-form-item label="所属领域" prop="fieldIds">
        <el-cascader
            placeholder="请选择"
            v-model="dataForm.fieldIds"
            :options="fields"
            @change="(value) => {handleChange(value, 'field')}"
            clearable
            :props="{checkStrictly: true, label: 'typeName', value: 'typeId'}"
        ></el-cascader>
      </el-form-item>
      <el-form-item label="服务对象" prop="serviceObj">
        <el-dict :code="'SERVICE_OBJECT'" v-model="dataForm.serviceObj"></el-dict>
      </el-form-item>
      <el-form-item label="对接状态" prop="status">
        <el-select v-model="dataForm.status" placeholder="请选择" clearable>
          <el-option
              v-for="item in [{label: '未对接', value: 'ras_wait_docking'}, {label: '已对接', value: 'ras_docking_success'}]"
              :key="item.value"
              :label="item.label"
              :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="需求时间" prop="timeRange">
        <el-date-picker
            v-model="dataForm.timeRange"
            clearable
            type="datetimerange"
            value-format="yyyy-MM-dd HH:mm:ss"
            align="right"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            :default-time="['00:00:00', '23:59:59']">
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button @click="getDataList()">查询</el-button>
        <el-button class="el-icon-refresh" type="warning" @click="resetForm()">重置</el-button>
      </el-form-item>
    </el-form>
    <div class="f-s-c" style="padding: 15px 0;display: flex; justify-content: flex-end">
      <div>
        <el-button v-if="isAuth('requirement_docking:list:export')" type="success" @click="exportHandle()">导出
        </el-button>
      </div>
    </div>
    <el-table
        :data="dataList"
        border
        v-loading="dataListLoading"
        style="width: 100%;">
      <el-table-column
          prop="name"
          header-align="center"
          align="center"
          min-width="250"
          :show-overflow-tooltip="true"
          label="需求名称">
        <template slot-scope="scope">
          <a style="cursor: pointer" @click="requirementDetailHandler(scope.row.id)">{{ scope.row.name }}</a>
        </template>
      </el-table-column>
      <el-table-column
          prop="typeText"
          header-align="center"
          align="center"
          label="需求类型">
      </el-table-column>
      <el-table-column
          prop="belongField"
          header-align="center"
          align="center"
          show-overflow-tooltip
          min-width="180"
          label="所属领域">
        <template slot-scope="scope">
          <span>{{ scope.row.belongFieldNameTop && scope.row.belongFieldNameEnd ? scope.row.belongFieldNameTop + '-' + scope.row.belongFieldNameEnd : (scope.row.belongFieldNameTop || scope.row.belongFieldNameEnd) }}</span>
        </template>
      </el-table-column>
      <el-table-column
          prop="serviceObjText"
          header-align="center"
          align="center"
          label="服务对象">
      </el-table-column>
      <el-table-column
          prop="publishOrgName"
          header-align="center"
          min-width="150"
          :show-overflow-tooltip="true"
          align="center"
          label="发布组织">
      </el-table-column>
      <el-table-column
          prop="contactPerson"
          header-align="center"
          align="center"
          label="联系人">
      </el-table-column>
      <el-table-column
          prop="contactPhone"
          header-align="center"
          align="center"
          min-width="130"
          label="联系方式">
      </el-table-column>
      <el-table-column
          prop="timeRange"
          header-align="center"
          align="center"
          min-width="160"
          label="需求时间">
        <template slot-scope="scope">
          <span>{{ (!scope.row.startTime || scope.row.startTime === '' || !scope.row.endTime || scope.row.endTime === '') ? '' : scope.row.startTime + '--' + scope.row.endTime }}</span>
        </template>
      </el-table-column>
      <el-table-column
          prop="status"
          header-align="center"
          align="center"
          label="对接状态">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.status === 'ras_docking_success'" type="success">已对接</el-tag>
          <el-tag v-else type="warning">未对接</el-tag>
        </template>
      </el-table-column>
      <el-table-column
          prop="dockingOrgName"
          header-align="center"
          min-width="150"
          :show-overflow-tooltip="true"
          align="center"
          label="对接组织">
      </el-table-column>
      <el-table-column
          prop="dockingTime"
          header-align="center"
          min-width="180"
          align="center"
          label="对接时间">
      </el-table-column>
      <el-table-column
          fixed="right"
          header-align="center"
          align="center"
          label="操作">
        <template slot-scope="scope">
          <el-button v-if="isAuth('requirement_docking:list:docking') && scope.row.status !== 'ras_docking_success'"
                     type="text" size="small" @click="dockingHandle(scope.row)">对接
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
        @size-change="sizeChangeHandle"
        @current-change="currentChangeHandle"
        :current-page="pageIndex"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pageSize"
        :total="totalPage"
        layout="total, sizes, prev, pager, next, jumper">
    </el-pagination>
    <!-- 需求详情 -->
    <requirement-detail v-if="requirementDetailVisible" ref="requirementDetail"></requirement-detail>
    <!-- 活动创建 -->
    <activity-create v-if="activityCreateVisible" ref="activityCreate"></activity-create>
  </div>
</template>

<script>
import listMixin from '@/mixins/list-mixins'
import RequirementDetail from './requirement-detail'
import ActivityCreate from '../activity/activity-add-or-update'
import {is8lPhone, isMobile} from "@/utils/validate";

export default {
  mixins: [listMixin],
  data() {
    const validateContactPhone = (rule, value, callback) => {
      if (!isMobile(value) && !is8lPhone(value)) {
        callback(new Error('联系方式须为手机号或8位座机号'))
      } else {
        callback()
      }
    }
    return {
      mixinOptions: {
        dataUrl: '/admin/zyz/requirement/pagesForDocking',
        exportUrl: '/admin/zyz/requirement/exportForDocking',              // 导出接口，API地址
        exportFileName: '需求对接表'
      },
      dataForm: {
        key: null,
        publishOrgCodes: [],
        publishOrgCode: null,
        fieldIds: [],
        fieldId: null,
        type: null,
        serviceObj: null,
        status: null,
        timeRange: [],
        startTime: '',
        endTime: ''
      },
      currentDockingRow: null,
      currentDockingId: null,
      dockingForm: {
        contactPerson: null,
        contactPhone: null
      },
      dockingRule: {
        contactPerson: [
          {required: true, message: '对接联系人不能为空'}
        ],
        contactPhone: [
          {required: true, message: '联系方式不能为空'},
          {validator: validateContactPhone}
        ],
      },
      fields: [],
      orgList: [],
      requirementDetailVisible: false,
      activityCreateVisible: false
    }
  },
  components: {
    RequirementDetail,
    ActivityCreate
  },
  activated() {
    this.getOrg()
    this.getDataList()
    this.getFields()
  },
  methods: {
    resetForm() {
      this.$refs['dataForm']?.resetFields()
      this.dataForm.fieldIds = []
      this.dataForm.fieldId = ''
      this.dataForm.orgCode = ''
      this.dataForm.startTime = null
      this.dataForm.endTime = null
      this.dataForm.publishOrgCode = null
      this.getDataList()
    },
    queryBeforeHandle() {
      // 查询前操作
      this.dataForm.startTime = this.dataForm.timeRange && this.dataForm.timeRange[0]
      this.dataForm.endTime = this.dataForm.timeRange && this.dataForm.timeRange[1]
    },
    //获取所属区域
    getOrg() {
      this.$http({
        url: this.$http.adornUrl(`/admin/org/getOrgTreeOfAll`),
        method: 'get',
        params: this.$http.adornParams()
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.orgList = this.getTreeData(data.obj)
        } else {
          this.orgList = []
        }
      })
    },
    getFields() {
      this.$http({
        url: this.$http.adornUrl('/admin/zyz/platform/belongFieldDict/cascade'),
        method: 'get'
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.fields = this.getTreeData(data.obj)
        } else {
          this.fields = []
        }
      })
    },
    // *处理点位分类最后children数组为空的状态
    getTreeData: function (data) {
      var that = this
      // 循环遍历json数据
      data.forEach(function (e) {
        if (!e.children || e.children.length < 1) {
          e.children = undefined
        } else {
          // children若不为空数组，则继续 递归调用 本方法
          that.getTreeData(e.children)
        }
      })
      return data
    },
    handleChange(value, type) {
      if (type === 'field') {
        this.dataForm.fieldId = value[value.length - 1]
      }
      if (type === 'org') {
        this.dataForm.publishOrgCode = value.join(',')
      }
    },
    dockingHandle(row) {
      this.currentDockingRow = row
      this.currentDockingId = row.id
      let _this = this
      const h = this.$createElement
      this.$msgbox({
        customClass: 'docking_tips_msg_box',
        title: '对接提示',
        message: h('div', null, [
          h('p', {key: 'split_line', style: 'border-bottom: 2px solid #d7d7d7'}),
          h('div', {style: 'padding: 0px 50px 0px 50px'}, [
            h('h4', null, '确定要与“科普活动场馆”需求对接？'),
            h('h4', null, '是否对接并同时创建活动？'),
          ]),
          h('div', {style: 'padding: 0px 40px 20px 40px'}, [
            h('el-form', {
              ref: 'dockingForm',
              attrs: {
                id: 'dockingForm',
                model: _this.dockingForm,
                rules: _this.dockingRule,
                labelWidth: '120px',
                labelPosition: 'right'
              }
            }, [
              h('el-form-item', {style: 'margin-bottom: 0px', attrs: {label: '对接联系人：', prop: 'contactPerson'}}, [
                h('input', {
                  style: 'border-radius: 4px; border: 1px solid #DCDFE6',
                  type: 'text',
                  modelValue: _this.dockingForm.contactPerson,
                  attrs: {id: 'contactPersonInput', placeholder: '请输入'},
                  value: _this.dockingForm.contactPerson,
                  on: {input: _this.contactPersonChange, blur: _this.validateContactPerson}
                }, null)
              ]),
              h('el-form-item', {style: 'margin-bottom: 0px', attrs: {label: '联系方式：', prop: 'contactPhone'}}, [
                h('input', {
                  style: 'border-radius: 4px; border: 1px solid #DCDFE6',
                  type: 'text',
                  modelValue: _this.dockingForm.contactPhone,
                  attrs: {id: 'contactPhoneInput', placeholder: '请输入'},
                  value: _this.dockingForm.contactPhone,
                  on: {input: _this.contactPhoneChange, blur: _this.validateContactPhone}
                }, null)
              ])
            ])
          ]),
          h('div', {
            attrs: {id: 'dockingButton'},
            style: 'padding: 0px 40px 10px 40px; display: flex; flex-flow: row wrap; justify-content: space-around'
          }, [
            h('el-button', {
              attrs: {id: 'btn_docking', type: 'warning', size: 'small'},
              on: {click: _this.dockingRequirement}
            }, '仅对接'),
            h('el-button', {
              attrs: {id: 'btn_docking&create', size: 'small'},
              style: 'background-color: #d9021a; color: white',
              on: {click: _this.dockingAndCreateActivity}
            }, '对接并创建活动'),
            h('el-button', {attrs: {id: 'btn_cancel', size: 'small'}, on: {click: _this.msgBoxClose}}, '取消')
          ])
        ]),
        closeOnClickModal: false,
        showCancelButton: false,
        showConfirmButton: false,
        beforeClose: (action, instance, done) => {
          _this.currentDockingId = null
          _this.dockingForm.contactPerson = null
          _this.dockingForm.contactPhone = null
          document.getElementById("contactPersonInput").value = null
          document.getElementById("contactPhoneInput").value = null
          _this.$refs['dockingForm'].clearValidate()
          done()
        }
      }).then(action => {
        this.$message({
          type: 'info',
          message: 'action: ' + action
        });
      }).catch(() => {
      })
      setTimeout(() => {
        document.getElementsByClassName('docking_tips_msg_box')[0].childNodes[1].style.padding = '0px'
      }, 50)
    },
    contactPersonChange() {
      this.dockingForm.contactPerson = document.getElementById("contactPersonInput").value
    },
    validateContactPerson() {
      this.$refs['dockingForm'].validateField('contactPerson')
    },
    contactPhoneChange() {
      this.dockingForm.contactPhone = document.getElementById("contactPhoneInput").value
    },
    validateContactPhone() {
      this.$refs['dockingForm'].validateField('contactPhone')
    },
    msgBoxClose() {
      this.$msgbox.close()
      this.currentDockingId = null
      this.dockingForm.contactPerson = null
      this.dockingForm.contactPhone = null
      document.getElementById("contactPersonInput").value = null
      document.getElementById("contactPhoneInput").value = null
      this.$refs.dockingForm.clearValidate()
    },
    dockingRequirement(needCreateActivity) {
      this.$refs['dockingForm'].validate((valid) => {
        if (valid) {
          this.$http({
            url: this.$http.adornUrl('/admin/zyz/requirement/docking'),
            method: 'get',
            params: this.$http.adornParams({
              'id': this.currentDockingId,
              'contactPerson': this.dockingForm.contactPerson,
              'contactPhone': this.dockingForm.contactPhone
            })
          }).then(({data}) => {
            if (data && data.code === 0) {
              this.msgBoxClose()
              this.$message.success('对接成功！暂不支持同步创建活动！')
              this.query()
              // if (needCreateActivity && needCreateActivity === true) {
              //   this.activityCreateHandler(this.currentDockingRow)
              // }
            } else {
              this.$message.error('对接失败!' + data.msg)
            }
          })
        }
      })
    },
    dockingAndCreateActivity() {
      this.dockingRequirement(true)
    },
    requirementDetailHandler(requirementId) {
      this.requirementDetailVisible = true
      this.$nextTick(() => {
        this.$refs.requirementDetail.init(requirementId)
      })
    },
    // activityCreateHandler(row) {
    //   this.activityCreateVisible = true
    //   this.$nextTick(() => {
    //     this.$refs.activityCreate.initFromReqOrRes(row, 'req')
    //   })
    // }
  }
}
</script>

<style lang="scss" scoped>
#dockingForm ::v-deep .el-form-item__error {
  top: 26px;
  font-size: 8px;
}
</style>
