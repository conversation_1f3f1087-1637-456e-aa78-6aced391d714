<template>
  <div>
    <div class="mod-role" v-if="accountTableVisible">
      <el-form :inline="true" :model="dataForm" @keyup.enter.native="getDataList()">
        <el-form-item label="关键字:" prop="roleName">
          <el-input v-model="dataForm.name" placeholder="微信号名称" clearable></el-input>
        </el-form-item>
        <el-form-item>
          <el-button @click="getDataList()">查询</el-button>
          <el-button v-if="isAuth('sys:role:save')" type="primary" @click="addOrUpdateHandle()">新增</el-button>
          <el-button v-if="isAuth('sys:role:batchDelete')" type="danger" @click="deleteHandle()">批量删除</el-button>
          <el-button type="warning" @click="refreshAccount">刷新程序配置信息</el-button>
        </el-form-item>
      </el-form>
      <el-table
        :data="dataList"
        border
        v-loading="dataListLoading"
        @selection-change="selectionChangeHandle"
        style="width: 100%;">
        <el-table-column
          type="selection"
          header-align="center"
          align="center"
          width="50">
        </el-table-column>
        <el-table-column
          prop="name"
          header-align="center"
          align="center"
          label="角色名称" width="350">
        </el-table-column>
        <el-table-column
          prop="code"
          header-align="center"
          align="center"
          width="180"
          label="code">
        </el-table-column>
        <el-table-column
          prop="appId"
          header-align="center"
          align="center"
          width="180"
          label="appId">
        </el-table-column>
        <el-table-column
          prop="createDate"
          header-align="center"
          align="center"
          width="180"
          label="创建时间">
        </el-table-column>
        <el-table-column
          fixed="right"
          header-align="center"
          align="center"
          width="180"
          label="操作">
          <template slot-scope="scope">
            <el-button v-if="isAuth('sys:role:update')" type="text" size="small"
                       @click="showMenuEdit(scope.row.code)">编辑菜单</el-button>
            <el-button v-if="isAuth('sys:role:update')" type="text" size="small" @click="addOrUpdateHandle(scope.row.id)">修改</el-button>
            <el-button v-if="isAuth('role:delete')" type="text" size="small" @click="deleteHandle(scope.row.id,scope.row.name)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        @size-change="sizeChangeHandle"
        @current-change="currentChangeHandle"
        :current-page="pageIndex"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pageSize"
        :total="totalPage"
        layout="total, sizes, prev, pager, next, jumper">
      </el-pagination>
    </div>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"></add-or-update>
    <menu-add-or-update v-if="menuAddOrUpdateVisible" ref="menuAddOrUpdate" @refreshDataList="getDataList"></menu-add-or-update>
  </div>
</template>

<script>
  import AddOrUpdate from './account-add-or-update'
  import MenuAddOrUpdate from './account-menu-add-or-update'
  export default {
    data () {
      return {
        accountTableVisible: true,
        dataForm: {
          name: ''
        },
        dataList: [],
        pageIndex: 1,
        pageSize: 10,
        totalPage: 0,
        dataListLoading: false,
        dataListSelections: '',
        addOrUpdateVisible: false,
        menuAddOrUpdateVisible: false
      }
    },
    components: {
      AddOrUpdate,
      MenuAddOrUpdate
    },
    activated () {
      this.getDataList()
    },
    methods: {
      refreshAccount () {
        this.$http({
          url: this.$http.adornUrl('/admin/wechat/account/refresh'),
          method: 'get',
          data: {}
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.$message.info('操作成功')
          } else {
            this.$message.info(data.msg)
          }
        })
      },
      showMenuEdit (id) {
        this.accountTableVisible = false
        this.menuAddOrUpdateVisible = true
        this.$nextTick(() => {
          this.$refs.menuAddOrUpdate.init(id)
        })
      },
      // 获取数据列表
      getDataList () {
        this.accountTableVisible = true
        this.menuAddOrUpdateVisible = false
        this.dataListLoading = true
        this.$http({
          url: this.$http.adornUrl('/admin/wechat/account/list'),
          method: 'post',
          data: this.$http.adornData({
            'currentPage': this.pageIndex,
            'pageSize': this.pageSize,
            'name': this.dataForm.name
          })
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.dataList = data.obj.records
            this.totalPage = data.obj.total
          } else {
            this.dataList = []
            this.totalPage = 0
          }
          this.dataListLoading = false
        })
      },
      // 每页数
      sizeChangeHandle (val) {
        this.pageSize = val
        this.pageIndex = 1
        this.getDataList()
      },
      // 当前页
      currentChangeHandle (val) {
        this.pageIndex = val
        this.getDataList()
      },
      // 多选
      selectionChangeHandle (val) {
        this.dataListSelections = val
      },
      // 新增 / 修改
      addOrUpdateHandle (id) {
        this.addOrUpdateVisible = true
        this.$nextTick(() => {
          this.$refs.addOrUpdate.init(id)
        })
      },
      // 删除
      deleteHandle (id, name) {
        var ids = id ? [id] : this.dataListSelections.map(item => {
          return item.id
        })
        var names = name ? [name] : this.dataListSelections.map(item => {
          return item.name
        })
        console.info(names)
        this.$confirm(`确定要删除账号[${names.join(',')}]吗？`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
          closeOnClickModal: false
        }).then(() => {
          this.$http({
            url: this.$http.adornUrl('/admin/wechat/account/removeByIds'),
            method: 'get',
            params: this.$http.adornParams({
              'ids': ids.join(',')
            })
          }).then(({data}) => {
            if (data && data.code === 0) {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 1500,
                onClose: () => {
                  this.getDataList()
                }
              })
            } else {
              this.$message.error(data.msg)
            }
          })
        }).catch(() => {})
      }
    }
  }
</script>
