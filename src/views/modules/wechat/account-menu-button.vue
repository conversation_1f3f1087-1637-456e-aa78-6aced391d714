<template>
  <el-dialog
    :title="!dataForm.id ? '新增' : '修改'"
    :close-on-click-modal="false"
    :visible.sync="visible"
    :modal-append-to-body='false'>
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmit()"
             label-width="100px">
      <el-form-item label="名称" prop="name">
        <el-input v-model="dataForm.name" placeholder="名称"></el-input>
      </el-form-item>
      <el-form-item label="类型" prop="type">
        <el-select v-model="dataForm.type" placeholder="类型">
          <el-option v-for="item in types" :key="item.id" :label="item.name" :value="item.code"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="排序" prop="sequence">
        <el-input v-model="dataForm.sequence" placeholder="排序"></el-input>
      </el-form-item>
      <el-form-item label="key值" prop="wxKey" v-if="dataForm.type !== 'media_id' && dataForm.type !=='view_limited'
        && dataForm.type !=='miniprogram' && dataForm.type !=='view' && dataForm.type !== 'father_button'">
        <el-input v-model="dataForm.wxKey" placeholder="值"></el-input>
      </el-form-item>
      <el-form-item label="URL" prop="url" v-if="dataForm.type === 'view'">
        <el-input v-model="dataForm.url" placeholder="请以http://或https://开头"></el-input>
      </el-form-item>
      <el-form-item label="素材编号" prop="mediaId" v-if="dataForm.type === 'media_id' || dataForm.type === 'view_limited'">
        <el-input v-model="dataForm.mediaId" placeholder="请填写存在的素材编号"></el-input>
      </el-form-item>
      <el-form-item label="小程序id" prop="appId" v-if="dataForm.type === 'miniprogram'">
        <el-input v-model="dataForm.appId" placeholder="小程序id"></el-input>
      </el-form-item>
      <el-form-item label="小程序页面" prop="pagePath" v-if="dataForm.type === 'miniprogram'">
        <el-input v-model="dataForm.pagePath" placeholder="小程序页面"></el-input>
      </el-form-item>
      <el-form-item>
        <span>编辑规则请参见
          <a href="https://developers.weixin.qq.com/doc/offiaccount/Custom_Menus/Creating_Custom-Defined_Menu.html" target="_blank" >微信菜单文档</a>
        </span>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
  import _ from 'lodash'

  export default {
    data () {
      return {
        visible: false,
        types: [],
        dataForm: {
          id: undefined,
          wxKey: '',
          name: '',
          type: '',
          url: '',
          pagePath: '',
          appId: '',
          mediaId: '',
          parentId: '',
          mpCode: '',
          sequence: ''
        },
        dataRule: {
          type: [
            {required: true, message: '类型不能为空', trigger: 'blur'}
          ],
          name: [
            {required: true, message: '名称不能为空', trigger: 'blur'}
          ],
          sequence: [
            {required: true, message: '排序不能为空', trigger: 'blur'}
          ]
        }
      }
    },
    methods: {
      init (code, parentId, id) {
        this.$http({
          url: this.$http.adornUrl('/admin/dict/parent'),
          method: 'get',
          params: this.$http.adornParams({
            'code': 'wechat_menu_event'
          })
        }).then(({data}) => {
          if (parentId) {
            let result = _.remove(data.obj, function (item) {
              return item.code !== 'father_button'
            })
            this.types = result
          } else {
            this.types = data.obj
          }
          this.dataForm.parentId = parentId
          this.dataForm.id = id || undefined
          this.visible = true
          this.dataForm.mpCode = code
          this.$nextTick(() => {
            this.$refs['dataForm']?.resetFields()
            if (this.dataForm.id) {
              this.$http({
                url: this.$http.adornUrl(`/admin/wechat/menu/`),
                method: 'get',
                params: this.$http.adornParams({
                  'id': this.dataForm.id
                })
              }).then(({data}) => {
                if (data && data.code === 0) {
                  this.dataForm.id = data.obj.id
                  this.dataForm.name = data.obj.name
                  this.dataForm.type = data.obj.type
                  this.dataForm.wxKey = data.obj.wxKey
                  this.dataForm.url = data.obj.url
                  this.dataForm.mediaId = data.obj.mediaId
                  this.dataForm.pagePath = data.obj.pagePath
                  this.dataForm.appId = data.obj.appId
                  this.dataForm.sequence = data.obj.sequence
                }
              })
            }
          })
        })
      },
      // 表单提交
      dataFormSubmit () {
        this.$refs['dataForm'].validate((valid) => {
          if (valid) {
            this.$http({
              url: this.$http.adornUrl(`/admin/wechat/menu/${!this.dataForm.id ? 'save' : 'update'}`),
              method: 'post',
              data: this.$http.adornData({
                'id': this.dataForm.id || undefined,
                'wxKey': this.dataForm.wxKey,
                'name': this.dataForm.name,
                'type': this.dataForm.type,
                'url': this.dataForm.url,
                'mediaId': this.dataForm.mediaId,
                'pagePath': this.dataForm.pagePath,
                'appId': this.dataForm.appId,
                'parentId': this.dataForm.parentId,
                'mpCode': this.dataForm.mpCode,
                'sequence': this.dataForm.sequence
              })
            }).then(({data}) => {
              if (data && data.code === 0) {
                this.$message({
                  message: '操作成功',
                  type: 'success',
                  duration: 1500,
                  onClose: () => {
                    this.visible = false
                    this.$emit('refreshDataList')
                  }
                })
              } else {
                this.$message.error(data.msg)
              }
            })
          }
        })
      }
    }
  }
</script>
