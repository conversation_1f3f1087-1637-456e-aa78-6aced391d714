<template>
  <el-dialog
    :title="!dataForm.id ? '新增' : '修改'"
    :close-on-click-modal="false"
    :visible.sync="visible">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmit()"
             label-width="100px" v-loading="isLoading">
      <el-form-item label="服务号名称" prop="name">
        <el-input v-model="dataForm.name" placeholder="服务号名称"></el-input>
      </el-form-item>
      <el-form-item label="服务号代码" prop="code">
        <el-input v-model="dataForm.code" placeholder="服务号代码，全局唯一"></el-input>
      </el-form-item>
      <el-form-item label="AppId" prop="appId">
        <el-input v-model="dataForm.appId" placeholder="AppId" :disabled="editDisabled"></el-input>
      </el-form-item>
      <el-form-item label="AppSecret" prop="secret">
        <el-input v-model="dataForm.secret" placeholder="AppSecret" :disabled="editDisabled" type="password"></el-input>
      </el-form-item>
      <el-form-item label="Token" prop="token">
        <el-input v-model="dataForm.token" placeholder="Token" :disabled="editDisabled" type="password"></el-input>
      </el-form-item>
      <el-form-item label="AesKey" prop="aesKey">
        <el-input v-model="dataForm.aesKey" placeholder="AesKey" :disabled="editDisabled" type="password"></el-input>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
      <el-button type="danger" v-if="editDisabled" @click="enableEdit()">启用编辑</el-button>
    </span>
  </el-dialog>
</template>

<script>
  export default {
    data () {
      return {
        isLoading: true,
        editDisabled: true,
        visible: false,
        menuList: [],
        menuListTreeProps: {
          label: 'name',
          children: 'children'
        },
        dataForm: {
          id: undefined,
          code: '',
          name: '',
          appId: '',
          secret: '',
          token: '',
          aesKey: ''
        },
        dataRule: {
          code: [
            {required: true, message: '服务号编码不能为空', trigger: 'blur'}
          ],
          name: [
            {required: true, message: '服务号名称不能为空', trigger: 'blur'}
          ],
          appId: [
            {required: true, message: 'AppId不能为空', trigger: 'blur'}
          ],
          secret: [
            {required: true, message: 'AppSecret不能为空', trigger: 'blur'}
          ]
        }
      }
    },
    methods: {
      init (id) {
        this.isLoading = true
        this.editDisabled = true
        this.dataForm.id = id || undefined
        this.visible = true
        this.$nextTick(() => {
          this.$refs['dataForm']?.resetFields()
          if (this.dataForm.id) {
            this.$http({
              url: this.$http.adornUrl(`/admin/wechat/account/`),
              method: 'get',
              params: this.$http.adornParams({
                'id': this.dataForm.id
              })
            }).then(({data}) => {
              this.isLoading = false
              if (data && data.code === 0) {
                this.dataForm.code = data.obj.code
                this.dataForm.name = data.obj.name
                this.dataForm.code = data.obj.code
                this.dataForm.appId = data.obj.appId
                // notmodify标识未编辑，不将这些隐私信息返回到页面
                this.dataForm.secret = 'notmodify'
                this.dataForm.token = 'notmodify'
                this.dataForm.aesKey = 'notmodify'
              }
            })
          } else {
            this.isLoading = false
            this.editDisabled = false
          }
        })
      },
      enableEdit () {
        this.$confirm(`编辑这些选项可能造成未知后果，请确认是否要继续`, '警告', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
          closeOnClickModal: false
        }).then(() => {
          this.editDisabled = false
        }).catch(() => {})
      },
      // 表单提交
      dataFormSubmit () {
        this.$refs['dataForm'].validate((valid) => {
          if (valid) {
            this.isLoading = true
            this.$http({
              url: this.$http.adornUrl(`/admin/wechat/account/${!this.dataForm.id ? 'save' : 'update'}`),
              method: 'post',
              data: this.$http.adornData({
                'id': this.dataForm.id || undefined,
                'code': this.dataForm.code,
                'name': this.dataForm.name,
                'appId': this.dataForm.appId,
                'secret': this.dataForm.secret,
                'token': this.dataForm.token,
                'aesKey': this.dataForm.aesKey
              })
            }).then(({data}) => {
              this.isLoading = false
              if (data && data.code === 0) {
                this.$message({
                  message: '操作成功',
                  type: 'success',
                  duration: 1500,
                  onClose: () => {
                    this.visible = false
                    this.$emit('refreshDataList')
                  }
                })
              } else {
                this.$message.error(data.msg)
              }
            })
          }
        })
      }
    }
  }
</script>
