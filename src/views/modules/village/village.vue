<template>
	<div class="mod-role">
		<el-form :inline="true" ref="dataForm" :model="dataForm" @keyup.enter.native="getDataList()">
			<el-form-item label="关键字" prop="key" style="padding-right: 40px">
				<el-input v-model="dataForm.key" placeholder="小区名称/社区名称/街道名称模糊查询" clearable style="width: 120%">
				</el-input>
			</el-form-item>
			<el-form-item>
				<el-button @click="getDataList()">查询</el-button>
				<el-button class="el-icon-refresh" type="warning" @click="resetForm()">重置</el-button>
			</el-form-item>
		</el-form>
		<div class="f-s-c" style="padding: 15px 0;display: flex; justify-content: flex-end">
			<div>
				<el-button type="success" @click="importHandle()">批量导入
				</el-button>
			</div>
		</div>
		<el-table :data="dataList" border v-loading="dataListLoading" @selection-change="selectionChangeHandle"
			style="width: 100%;">
			<el-table-column type="index" label="序号" align="center" width="50">
			</el-table-column>
			<el-table-column prop="villageName" header-align="center" align="center" label="小区名称">
			</el-table-column>
			<el-table-column prop="orgCommunityName" header-align="center" align="center" label="社区名称">
			</el-table-column>
			<el-table-column prop="parentOrgName" header-align="center" align="center" label="街道名称">
			</el-table-column>
		</el-table>
		<el-pagination @size-change="sizeChangeHandle" @current-change="currentChangeHandle" :current-page="pageIndex"
			:page-sizes="[10, 20, 50, 100]" :page-size="pageSize" :total="totalPage"
			layout="total, sizes, prev, pager, next, jumper">
		</el-pagination>
		<!-- 弹窗, 导入 -->
		<import v-if="importVisible" ref="import" @refreshDataList="getDataList"></import>
	</div>
</template>

<script>
	import listMixin from '@/mixins/list-mixins'
	import Import from './village-import.vue'
	export default {
		mixins: [listMixin],
		data() {
			return {
				mixinOptions: {
					dataUrl: '/admin/sys/village/getPages',
					exportUrl: '/admin/zyz/activity/apply/exportForServiceLong', // 导出接口，API地址
					exportFileName: '活动服务时长记录列表'
				},
				dataForm: {
					key: null,
					actType: null,
					auditStatus: null,
					teamMember: null,
					timeRange: []
				},
				fields: [],
				orgList: [],
				exportLoading: false,
				importVisible: false,
				resourceDetailVisible: false
			}
		},
		components: {
			Import
		},
		activated() {
			this.getDataList()
		},
		methods: {
			resetForm() {
				this.$refs['dataForm']?.resetFields()
				this.dataForm.startTime = null
				this.dataForm.endTime = null
				this.dataForm.key = null
				this.dataForm.timeRange = []
				this.getDataList()
			},
			importHandle() {
				this.importVisible = true
				this.$nextTick(() => {
					this.$refs.import.init()
				})
			}
			/* 	querySuccessHandle (data) {
				  // 查询成功操作
				  this.dataList = data.records
				  this.totalPage = data.total
				  data.records.forEach((it) => {
				    it.canAudit = it.auditStatus === 'act_apply_wait_audit'
				  })
				}, */
		}
	}
</script>

<style lang="scss" scoped>
</style>
