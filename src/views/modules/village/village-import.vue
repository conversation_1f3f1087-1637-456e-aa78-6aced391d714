<template>
	<el-dialog :title="'批量导入'" :close-on-click-modal="false" @close="closeHandle" :visible.sync="visible">
		<el-form :model="uploadForm" ref="uploadForm" label-width="145px">
			<el-form-item label="上传文件：" prop="excel">
				<el-upload ref="upload" name="excel" :action="this.$http.adornUrl(`/admin/sys/village/uploadExcel`)"
					:data="{}" :headers="myHeaders" :on-success="successHandle" :on-change="changHandle" :limit=1
					:on-exceed="handleExceed" :file-list="fileList" :auto-upload="false">
					<el-button type="primary">选取文件</el-button>
					<div slot="tip" class="el-upload__tip">支持上传{{ fileExts }}文件</div>
				</el-upload>
			</el-form-item>
		</el-form>
		<span slot="footer" class="dialog-footer">
			<el-button @click="visible = false">取消</el-button>
			<el-button type="primary" @click="dataFormSubmit()">确定</el-button>
		</span>
	</el-dialog>
</template>

<script>
	import Vue from 'vue'

	export default {
		props: [],
		data() {
			return {
				visible: false,
				fileList: [],
				uploadForm: {
					excel: null
				},
				fileExts: 'xls/xlsx',
				myHeaders: {
					Authorization: Vue.cookie.get('Authorization')
				}
			}
		},
		components: {

		},
		watch: {},
		methods: {
			init(id) {
				this.fileList = []
				this.visible = true
			},
			// 表单提交
			dataFormSubmit() {
				this.$refs['uploadForm'].validate((valid) => {
					if (valid && this.beforeFileUpload()) {
						this.$refs.upload.submit()
					}
				})
			},
			// 上传成功
			successHandle(response) {
				if (response && response.code === 0) {
					this.$message({
						message: '操作成功',
						type: 'success',
						onClose: () => {}
					})
					this.visible = false
					this.$emit('refreshDataList')
				} else {
					this.$message.error(response.msg)
				}
				this.fileList = []
			},
			changHandle(file, fileList) {
				this.fileList = fileList
			},
			handleExceed() {
				this.$message.error('只能选择一个文件，如需替换请删除已选文件后重试')
			},
			// 资源上传过滤
			beforeFileUpload() {
				let size = this.fileList.length
				if (size === 0) {
					this.$message.error('请选择资源文件')
					return false
				}
				if (size > 1) {
					this.$message.error('只能上传单个资源，请删除资源后再试')
					return false
				}
				let fileName = this.fileList[0].name
				if (fileName.indexOf('.') === -1) {
					this.$message.error('不支持的文件格式')
				}

				let fileExt = fileName.substring(fileName.lastIndexOf('.') + 1)
				let allowTypes = `/${this.fileExts}/`
				if (allowTypes.indexOf(`/${fileExt}/`) === -1) {
					this.$message.error(`上传资源只能是${this.fileExts}格式!`)
					return false
				}
				return true
			},
			// 弹窗关闭时
			closeHandle() {
				this.uploadForm.type = ''
				this.fileList = []
				this.$emit('refreshDataList')
			}
		}
	}
</script>

<style>
	.avatar {
		width: 178px;
		height: 178px;
		display: block;
	}
</style>
