<template>
  <div>
    <el-dialog
      width="980px"
      title="附件"
      :close-on-click-modal="false"
      :visible.sync="visible">
      <el-form :inline="true" class="search-box" ref="dataForm" :model="dataForm" @keyup.enter.native="getDataList()"
               label-width="100px">
        <el-form-item label="名称：" prop="name">
          <el-input v-model="dataForm.name" placeholder="名称"></el-input>
        </el-form-item>
        <el-form-item class="pd-15">
          <el-button type="info" icon="el-icon-search" @click="getDataList()">查询</el-button>
          <el-button type="warning" icon="el-icon-refresh" @click="resetForm()">重置</el-button>
          <el-button type="primary" @click="addOrUpdateHandle(dataForm.contentId)">新增</el-button>
          <el-button type="danger" @click="deleteHandle()" :disabled="dataListSelections.length <= 0">删除</el-button>
        </el-form-item>
      </el-form>
      <el-table
        :data="dataList"
        border
        v-loading="dataListLoading"
        @selection-change="selectionChangeHandle"
        style="width: 100%">
        <el-table-column
          type="selection"
          header-align="center"
          align="center"
          width="50">
        </el-table-column>
        <el-table-column
          prop="name"
          label="名称">
        </el-table-column>
        <el-table-column
          prop="fileName"
          label="文件名称">
        </el-table-column>
        <el-table-column
          prop="createDate"
          label="创建时间">
        </el-table-column>
      </el-table>
      <el-pagination
        @size-change="sizeChangeHandle"
        @current-change="currentChangeHandle"
        :current-page="pageIndex"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pageSize"
        :total="totalPage"
        layout="total, sizes, prev, pager, next, jumper">
      </el-pagination>
    </el-dialog>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"></add-or-update>
  </div>
</template>

<script>
  import AddOrUpdate from './attach-add'
  import listMixin from '@/mixins/list-mixins'
  export default {
    mixins: [listMixin],
    data () {
      return {
        mixinOptions: {
          activatedLoad: false,
          dataUrl: '/admin/cms/contentAttach/pages',
          deleteUrl: '/admin/cms/contentAttach/deleteByIds'
        },
        visible: false,
        dataForm: {
          name: '',
          contentId: ''
        }
      }
    },
    components: {
      AddOrUpdate
    },
    methods: {
      init (contentId) {
        this.dataForm.contentId = contentId
        this.dataForm.name = null
        this.visible = true
        this.getDataList()
      }
    }
  }
</script>
