<template>
  <el-dialog
    :title="'附件上传'"
    :close-on-click-modal="false"
    :visible.sync="visible">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmit()" label-width="120px">
      <el-form-item label="名称" prop="name" :error="errors['name']">
        <el-input v-model="dataForm.name" placeholder="名称"></el-input>
      </el-form-item>
      <el-form-item label="附件：" prop="filePaths" :error="errors['filePaths']">
        <el-upload
          ref="doc"
          class="upload-demo"
          :multiple="true"
          :action="this.$http.adornUrl('/admin/oss/upload')"
          :headers="headers"
          :data="{serverCode: uploadOptions.serverCode, media: false}"
          :on-success="docUploadSuccess"
          :file-list="uploadOptions.fileList"
          :on-remove="removeHandle"
          :before-upload="docBeforeUpload">
          <el-button size="small" type="primary">点击上传</el-button>
          <div slot="tip" class="el-upload__tip">只能上传'txt', 'zip', 'rar', 'pdf', 'doc', 'docx', 'xlsx', 'xls', 'MP4', 'AVI', 'MP3', 'WMA'文件</div>
          <div slot="tip" class="el-upload__tip">且文件和音频不超过2M，视频不超过100M</div>
        </el-upload>
      </el-form-item>
      <el-form-item label="封面图片：" prop="img">
        <el-upload
          class="avatar-uploader"
          :action="this.$http.adornUrl(`/admin/oss/upload`)"
          :headers="headers"
          :data="{serverCode: uploadOptions.serverCode, media: false}"
          :show-file-list="false"
          :on-success="successHandle"
          :before-upload="beforeUploadHandle">
          <img v-if="dataForm.img" :src="$http.adornAttachmentUrl(dataForm.img)" class="avatar">
          <i v-else class="el-icon-plus avatar-uploader-icon"></i>
        </el-upload>
      </el-form-item>
      <el-form-item label="描述" prop="remark" :error="errors['remark']">
        <el-input v-model="dataForm.remark" placeholder="描述"></el-input>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()" :disabled="!uploadOptions.fileList">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
  import Vue from 'vue'
  import _ from 'lodash'
  import editMixin from '@/mixins/edit-mixins'
  import fileUploadMixin from '@/mixins/file-upload-mixins'
  export default {
    mixins: [editMixin, fileUploadMixin],
    data () {
      return {
        uploadOptions: {
          fieldName: 'img'
        },
        editOptions: {
          isUniteUrl: true,
          submitUrl: '/admin/cms/contentAttach/saveAttach'
        },
        canSubmit: true,
        dataForm: {
          contentId: null,
          name: null,
          filePaths: [],
          img: null,
          remark: null
        },
        dataRule: {
          name: [
            { required: true, message: '名称不能为空', trigger: 'blur' }
          ],
          filePaths: [
            { required: true, message: '文件不能为空', trigger: 'blur' }
          ]
        }
      }
    },
    methods: {
      init (contentId) {
        this.dataForm.contentId = contentId
        this.visible = true
        this.$nextTick(() => {
          this.$refs['dataForm']?.resetFields()
        })
      },
      // 附件上传
      docUploadSuccess (res, file, fileList) {
        this.uploadOptions.fileList = fileList
        if (!res.success) {
          return this.$message.error('上传失败,请重试')
        }
        this.$message({
          message: '操作成功',
          type: 'success',
          duration: 1500
        })
        if (this.dataForm.filePaths === null) {
          this.dataForm.filePaths = []
        }
        this.dataForm.filePaths.push({
          id: res.obj.fileName,
          text: res.obj.path
        })
      },
      docBeforeUpload (file) {
        let FileExt = file.name.replace(/.+\./, '')
        if ('txt,zip,rar,pdf,docx,xlsx,mp4,avi,mp3,wma'.indexOf(FileExt.toLowerCase()) === -1) {
          return this.$message.error('上传失败, 不支持的文件格式')
        }
        const isLt2M = file.size / 1024 / 1024 < 2
        if ('txt,zip,rar,pdf,docx,xlsx,mp3,wma'.indexOf(FileExt.toLowerCase()) !== -1 && !isLt2M) {
          return this.$message.error('上传文档和音频文件大小不能超过 2M!')
        }
        const isLt50M = file.size / 1024 / 1024 < 100
        if ('mp4,avi'.indexOf(FileExt.toLowerCase()) !== -1 && !isLt50M) {
          return this.$message.error('上传视频文件大小不能超过 100M!')
        }
        this.canSubmit = false
      },
      submitBeforeHandle () {
        // 提交前预处理函数
        if (this.dataForm.filePaths === null) {
          this.dataForm.filePaths = []
        }
        this.dataForm.filePaths = _.map(this.uploadOptions.fileList, (it) => {
          return {id: it.fileName, text: it.path}
        })
      }
    }
  }
</script>
<style lang="scss">
  .avatar {
    width: 178px;
    height: 178px;
    display: block;
  }
  .avatar-uploader .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
  }

  .avatar-uploader .el-upload:hover {
    border-color: #409EFF;
  }

  .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 178px;
    height: 178px;
    line-height: 178px;
    text-align: center;
  }
  .mod-menu {
    .menu-list__input,
    .icon-list__input {
      > .el-input__inner {
        cursor: pointer;
      }
    }
    &__icon-popover {
      width: 458px;
      overflow: hidden;
    }
    &__icon-inner {
      width: 478px;
      max-height: 258px;
      overflow-x: hidden;
      overflow-y: auto;
    }
    &__icon-list {
      width: 458px;
      padding: 0;
      margin: -8px 0 0 -8px;
      > .el-button {
        padding: 8px;
        margin: 8px 0 0 8px;
        > span {
          display: inline-block;
          vertical-align: middle;
          width: 18px;
          height: 18px;
          font-size: 18px;
        }
      }
    }
    .icon-list__tips {
      font-size: 18px;
      text-align: center;
      color: #e6a23c;
      cursor: pointer;
    }
  }
</style>
