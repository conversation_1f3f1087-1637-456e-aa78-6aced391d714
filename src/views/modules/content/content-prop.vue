<template>
  <el-dialog title="属性"
    :close-on-click-modal="false"
    :visible.sync="visible">
    <el-tabs v-model="activeName" @tab-click="handleClick">
      <el-tab-pane label="公共属性" name="first">
        <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmit()" label-width="100px">
          <el-form-item
            v-for="(field, index) in fieldList"
            :label="field.propName"
            :key="index"
            :prop="field.propId">
            <el-input v-model="dataForm[field.propId]"></el-input>
          </el-form-item>
        </el-form>
        <span class="dialog-footer">
          <el-row type="flex" justify="end" style="margin-bottom: 15px;">
          <el-button @click="visible = false">取消</el-button>
          <el-button v-if="fieldList.length >= 1" type="primary" @click="dataFormSubmit()">确定</el-button>
        </el-row>
        </span>
      </el-tab-pane>
      <el-tab-pane label="私有属性" name="second">
        <el-row type="flex" justify="end" style="margin-bottom: 15px;">
          <el-button type="primary" @click="propAddHandle()">新增</el-button>
        </el-row>
        <div>
          <el-table
            :data="dataList"
            border
            ref="table"
            v-loading="dataListLoading"
            style="width: 100%">
            <el-table-column
              prop="propName"
              header-align="center"
              align="center"
              label="属性名称">
            </el-table-column>
            <el-table-column
              prop="propValue"
              header-align="center"
              align="center"
              label="属性值">
            </el-table-column>
            <el-table-column
              header-align="center"
              align="center"
              label="操作">
              <template slot-scope="scope">
                <el-button type="text" size="small" @click="deleteHandle(scope.row.id)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
          <!-- 弹窗, 新增私有属性-->
          <prop-add v-if="propAddVisible" ref="propAdd" @refreshDataList="getDataList"></prop-add>
        </div>
      </el-tab-pane>
    </el-tabs>
  </el-dialog>
</template>
<script>
  import _ from 'lodash'
  import PropAdd from './prop-add'
  import listMixin from '@/mixins/list-mixins'
  export default {
    mixins: [listMixin],
    data () {
      return {
        mixinOptions: {
          activatedLoad: false,
          deleteUrl: '/admin/cms/categoryContent/recyclingByIds'
        },
        visible: false,
        propAddVisible: false,
        activeName: 'first',
        categoryId: null,
        contentId: null,
        dataForm: {},
        fieldList: [],
        propValueList: [],
        dataRule: {},
        disable: true
      }
    },
    components: {
      PropAdd
    },
    methods: {
      init (categoryId, contentId) {
        this.categoryId = categoryId
        this.contentId = contentId
        this.activeName = 'first'
        this.firstHandle()
        this.getDataList()
      },
      // 新增
      propAddHandle () {
        this.propAddVisible = true
        this.$nextTick(() => {
          this.$refs.propAdd.init(this.contentId)
        })
      },
      firstHandle () {
        this.$http({
          url: this.$http.adornUrl(`/admin/cms/contentProp/getByCategoryId`),
          method: 'get',
          params: this.$http.adornParams({
            'categoryId': this.categoryId,
            'contentId': this.contentId
          })
        }).then(({data}) => {
          if (data && data.code === 0) {
            let that = this
            this.fieldList = data.obj
            _.forEach(data.obj, function (item) {
              that.dataForm[item.propId] = item.propValue
              that.dataRule[`${item.propId}`] = [{
                max: 50,
                message: `${item.propName}不能能超过50个字符`,
                trigger: 'blur'
              }]
            })
          }
        }).then(() => {
          this.visible = true
          this.$nextTick(() => {
            this.$refs['dataForm']?.resetFields()
          })
        })
      },
      getDataList () {
        this.dataListLoading = true
        this.$http({
          url: this.$http.adornUrl('/admin/cms/contentProp/page'),
          method: 'post',
          data: this.$http.adornData({
            'currentPage': this.pageIndex,
            'pageSize': this.pageSize,
            'contentId': this.contentId
          })
        }).then(({data}) => {
          this.queryCallback(data)
          this.dataListLoading = false
        })
      },
      handleClick (tab, event) {
        if (tab.name === 'second') {
          this.$nextTick(() => {
            this.$refs.table.doLayout()
          })
        }
      },
      // 表单提交
      dataFormSubmit () {
        this.$refs['dataForm'].validate((valid) => {
          if (valid) {
            let that = this
            _.forEach(this.dataForm, function (item, index) {
              that.propValueList.push({
                categoryId: that.categoryId,
                contentId: that.contentId,
                propId: index,
                propValue: item,
                categoryRelate: true
              })
            })
            this.$http({
              url: this.$http.adornUrl(`/admin/cms/contentProp/savePubPropValue`),
              method: 'post',
              data: this.$http.adornData({
                categoryId: that.categoryId,
                contentId: that.contentId,
                propValueList: this.propValueList
              })
            }).then(({data}) => {
              if (data && data.code === 0) {
                this.$message({
                  message: '操作成功',
                  type: 'success',
                  duration: 1500,
                  onClose: () => {
                    this.visible = false
                    this.$emit('refreshDataList')
                  }
                })
              } else {
                this.$message.error(data.msg)
              }
            })
          }
        })
      }
    }
  }
</script>
