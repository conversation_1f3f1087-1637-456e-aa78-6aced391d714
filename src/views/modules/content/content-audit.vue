<template>
  <el-dialog
    :title="'审核'"
    :close-on-click-modal="false"
    :visible.sync="visible">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmit()"
             label-width="80px">
      <el-row :gutter="6">
        <el-col :span="12">
          <el-form-item label="审核结果" prop="status">
            <el-radio-group v-model="dataForm.status">
              <el-radio :label="true">通过</el-radio>
              <el-radio :label="false">不通过</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="6">
        <el-col :span="24">
          <el-form-item label="审核意见" prop="checkOpinion">
            <el-input type="textarea" v-model="dataForm.checkOpinion" placeholder="审核意见"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
  import editMixin from '@/mixins/edit-mixins'
  export default {
    mixins: [editMixin],
    data () {
      return {
        editOptions: {
          isUniteUrl: true,
          submitUrl: '/admin/cms/content/audit'
        },
        dataForm: {
          ids: null,
          status: null,
          checkOpinion: null
        },
        dataRule: {
          status: [
            {required: true, message: '审核结果不能为空', trigger: 'blur'}
          ]
        }
      }
    },
    methods: {
      init (ids) {
        this.visible = true
        this.dataForm.ids = ids
        this.$nextTick(() => {
          this.$refs['dataForm']?.resetFields()
        })
      }
    }
  }
</script>
