<template>
  <div>
    <el-dialog
      width="980px"
      title="选择图片"
      :close-on-click-modal="false"
      :visible.sync="visible">
      <el-form :inline="true" class="search-box" ref="dataForm" :model="dataForm" @keyup.enter.native="getDataList()"
               label-width="100px">
        <el-form-item label="名称：" prop="name">
          <el-input v-model="dataForm.name" placeholder="名称"></el-input>
        </el-form-item>
        <el-form-item class="pd-15">
          <el-button type="primary" icon="el-icon-search" @click="getDataList()">查询</el-button>
          <el-button @click="" icon="el-icon-refresh" @click="resetForm()">重置</el-button>
        </el-form-item>
      </el-form>
      <el-table
        :data="dataList"
        border
        v-loading="dataListLoading"
        @row-click = "addHandle"
        style="width: 100%">
        <el-table-column
          prop="name"
          label="名称">
        </el-table-column>
        <el-table-column
          prop="fileName"
          label="原文件名称">
        </el-table-column>
        <el-table-column
          prop="path"
          label="文件访问路径">
        </el-table-column>
      </el-table>
      <el-pagination
        @size-change="sizeChangeHandle"
        @current-change="currentChangeHandle"
        :current-page="pageIndex"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pageSize"
        :total="totalPage"
        layout="total, sizes, prev, pager, next, jumper">
      </el-pagination>
    </el-dialog>
  </div>
</template>

<script>
  import listMixin from '@/mixins/list-mixins'
  export default {
    mixins: [listMixin],
    data () {
      return {
        mixinOptions: {
          activatedLoad: false,
          dataUrl: '/admin/oss/pages'
        },
        visible: false,
        dataForm: {
          type: 'picture',
          name: ''
        }
      }
    },
    methods: {
      init () {
        this.visible = true
        this.resetForm()
        this.getDataList()
      },
      // 新增
      addHandle (row) {
        this.$parent.dataForm.titleImgUrl = row.path
        this.$message({
          message: '操作成功',
          type: 'success',
          duration: 1500,
          onClose: () => {
            this.visible = false
          }
        })
      }
    }
  }
</script>
