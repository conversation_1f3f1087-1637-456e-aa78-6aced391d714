<template>
  <div>
    <el-dialog
      :title="!dataForm.id ? '新增' : '修改'"
      :close-on-click-modal="false"
      :visible.sync="visible" width="80%">
      <el-form :model="dataForm" :rules="dataRule" ref="dataForm" label-width="100px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="所属栏目" prop="categoryId" :error="errors['categoryId']">
              <el-cascader-multi v-model="dataForm.categoryId" :data="categories"></el-cascader-multi>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="标题" prop="title" :error="errors['title']">
              <el-input v-model="dataForm.title" placeholder="标题"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="SEO标题" prop="seoTitle" :error="errors['seoTitle']">
              <el-input v-model="dataForm.seoTitle" placeholder="SEO标题"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="SEO关健字" prop="seoKeyword" :error="errors['seoKeyword']">
              <el-input v-model="dataForm.seoKeyword" placeholder="SEO关健字,以“,”逗号区分开"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="SEO描述" prop="seoRemark" :error="errors['seoRemark']">
              <el-input v-model="dataForm.seoRemark" placeholder="SEO描述"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="简介" prop="briefIntroduction" :error="errors['briefIntroduction']">
              <el-input v-model="dataForm.briefIntroduction" placeholder="简介"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="生效日期" prop="effectiveDate" :error="errors['effectiveDate']">
              <el-date-picker
                v-model="dataForm.effectiveDate"
                value-format="yyyy-MM-dd HH:mm:ss"
                type="datetime"
                placeholder="生效日期"
                :default-time="['00:00:00']"
                :picker-options="pickerOptionsStart">
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="过期日期" prop="expirationDate" :error="errors['expirationDate']">
              <el-date-picker
                v-model="dataForm.expirationDate"
                value-format="yyyy-MM-dd HH:mm:ss"
                :picker-options="pickerOptionsEnd"
                type="datetime"
                :default-time="['00:00:00']"
                placeholder="过期日期">
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="重要度" prop="importantLevel" :error="errors['importantLevel']">
              <el-select v-model="dataForm.importantLevel" placeholder="请选择">
                <el-option v-for="item in importantLevelList" :key="item" :label="item" :value="item"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="排序" prop="sequence">
              <el-input-number v-model="dataForm.sequence" controls-position="right" :min="0" label="排序"></el-input-number>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="自定义链接" prop="customLinks" :error="errors['customLinks']">
              <el-input v-model="dataForm.customLinks" placeholder="自定义链接"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="虚拟阅读量" prop="virtualReading" >
              <el-input v-model="dataForm.virtualReading" placeholder="可自行输入"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="实际阅读量" prop="actualReading" >
              <el-input v-model="dataForm.actualReading" disabled></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="属性配置">
              <el-checkbox v-model="dataForm.stick">置顶</el-checkbox>
              <el-checkbox v-model="dataForm.hot">热门</el-checkbox>
              <el-checkbox v-model="dataForm.audit">是否需要审核</el-checkbox>
              <el-checkbox v-model="dataForm.comment">是否可以评论</el-checkbox>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="10">
          <el-col :span="8">
            <el-form-item label="图片：" prop="titleImgUrl">
              <el-upload
                class="avatar-uploader"
                :action="this.$http.adornUrl(`/admin/oss/upload`)"
                :headers="headers"
                :data="{serverCode: uploadOptions.serverCode, media: false}"
                :show-file-list="false"
                :on-success="successHandle"
                :on-change="changHandle"
                :on-exceed="exceedHandle"
                :before-upload="beforeUploadHandle">
                <img v-if="dataForm.titleImgUrl" :src="$http.adornAttachmentUrl(dataForm.titleImgUrl)" class="avatar">
                <i v-else class="el-icon-plus avatar-uploader-icon"></i>
              </el-upload>
            </el-form-item>
          </el-col>
          <el-col :span="5">
            <el-button type="primary" icon="el-icon-plus" size="mini" plain @click="addFileHandle()">从媒体库选择
            </el-button>
            <el-button v-if="dataForm.titleImgUrl" style="margin-top: 10px;" type="danger" icon="el-icon-delete" size="mini" plain @click="handleImgRemove()">删除
            </el-button>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-form-item label="详细描述：" prop="description">
            <el-upload
              class="avatar-uploader editor-upload-img"
              :action="this.$http.adornUrl(`/admin/oss/upload`)"
              :headers="headers"
              :data="{serverCode: uploadOptions.serverCode, media: false}"
              name="file"
              :show-file-list="false"
              :on-success="uploadSuccess"
              :on-error="uploadError"
              :before-upload="beforeUpload">
            </el-upload>
            <quill-editor v-model="dataForm.description"
              ref="myQuillEditor"
              :options="editorOption">
            </quill-editor>
          </el-form-item>
        </el-row>
        <el-form-item label="word文档：" prop="docAttachments">
          <el-upload
            class="upload-demo"
            ref="upload"
            :headers="headers"
            :action="this.$http.adornUrl('/admin/editor/wordToHtml')"
            :file-list="fileList"
            :auto-upload="false"
            :on-change="wordChangHandle"
            :on-error="wordErrorHandle"
            :on-success="wordUploadSuccess">
            <el-button slot="trigger" size="small" type="primary">选取文件</el-button>
            <el-button style="margin-left: 10px;" size="small" type="primary" @click="submitUpload">文件展示</el-button>
            <div slot="tip" class="el-upload__tip">只能上传doc,docx文件</div>
          </el-upload>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="visible = false">取消</el-button>
        <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
      </span>
    </el-dialog>
    <!-- 弹窗 添加用户 -->
    <add-file v-if="addFileVisible" ref="addFile" ></add-file>
  </div>
</template>

<script>
  import Vue from 'vue'
  import AddFile from './file-list'
  import { quillEditor } from 'vue-quill-editor' // 调用编辑器
  import 'quill/dist/quill.core.css'
  import 'quill/dist/quill.snow.css'
  import 'quill/dist/quill.bubble.css'
  import moment from 'moment'
  const toolbarOptions = [
    ['bold', 'italic', 'underline', 'strike'], //  加粗 斜体 下划线 删除线
    ['blockquote', 'code-block'], // 引用  代码块
    [{ header: 1 }, { header: 2 }], // 1、2 级标题
    [{ list: 'ordered' }, { list: 'bullet' }], // 有序、无序列表
    [{ script: 'sub' }, { script: 'super' }], // 上标/下标
    [{ indent: '-1' }, { indent: '+1' }], // 缩进
    // [{'direction': 'rtl'}],                         // 文本方向
    [{ size: ['small', false, 'large', 'huge'] }], // 字体大小
    [{ header: [1, 2, 3, 4, 5, 6, false] }], // 标题
    [{ color: [] }, { background: [] }], // 字体颜色、字体背景颜色
    [{ font: [] }], // 字体种类
    [{ align: [] }], // 对齐方式
    ['clean'], // 清除文本格式
    ['link', 'image', 'video'] // 链接、图片、视频
  ]
  import editMixin from '@/mixins/edit-mixins'
  import fileUploadMixin from '@/mixins/file-upload-mixins'
  export default {
    mixins: [editMixin, fileUploadMixin],
    data () {
      return {
        uploadOptions: {
          fieldName: 'titleImgUrl'
        },
        editOptions: {
          initUrl: '/admin/cms/categoryContent/getCategoryContentById',
          saveSuffix: 'saveContent',
          updateSuffix: 'updateContent',
          submitUrl: '/admin/cms/categoryContent'
        },
        fileList: [],
        importantLevelList: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10],
        addFileVisible: false,
        quillUpdateImg: false,
        // 开始结束时间限制
        pickerOptionsStart: this.beginDate(),
        pickerOptionsEnd: this.processDate(),
        editorOption: {
          theme: 'snow', // or 'bubble'
          placeholder: '您想说点什么？',
          modules: {
            toolbar: {
              container: toolbarOptions,
              // container: "#toolbar",
              handlers: {
                image: function (value) {
                  if (value) {
                    // 触发input框选择图片文件
                    document.querySelector('.avatar-uploader.editor-upload-img input').click()
                  } else {
                    this.quill.format('image', false)
                  }
                }
              }
            }
          }
        },
        dataForm: {
          categoryId: [],
          title: null,
          contentId: null,
          seoTitle: null,
          seoKeyword: null,
          seoRemark: null,
          titleImgUrl: null,
          briefIntroduction: null,
          description: null,
          customLinks: null,
          virtualReading: null,
          actualReading: 0,
          effectiveDate: null,
          expirationDate: null,
          sequence: 0,
          importantLevel: null,
          stick: false,
          hot: false,
          audit: false,
          comment: false
        },
        dataRule: {
          title: [
            { required: true, message: '标题不能为空', trigger: 'blur' }
          ],
          categoryId: [
            {required: true, message: '所属栏目不能为空', trigger: 'blur'}
          ],
          sequence: [
            {required: true, message: '排序不能为空', trigger: 'blur'}
          ],
          effectiveDate: [
            {required: true, message: '生效日期不能为空', trigger: 'blur'}
          ]
        },
        categories: []
      }
    },
    components: {
      quillEditor,
      moment,
      AddFile
    },
    methods: {
      init (id) {
        this.dataForm.id = id || undefined
        this.dataForm.effectiveDate = moment(new Date()).format('YYYY-MM-DD') + ' 00:00:00'
        this.fileList = []
        this.$http({
          url: this.$http.adornUrl('/admin/cms/category/cascader'),
          method: 'get',
          params: this.$http.adornParams()
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.categories = data.obj
          } else {
            this.categories = []
          }
        }).then(() => {
          this.visible = true
          this.$nextTick(() => {
            this.$refs['dataForm']?.resetFields()
          })
        }).then(() => {
          if (this.dataForm.id) {
            this.getData()
          }
        })
      },
      // 媒体库添加图片
      addFileHandle () {
        this.addFileVisible = true
        this.$nextTick(() => {
          this.$refs.addFile.init()
        })
      },
      handleImgRemove () {
        this.dataForm.titleImgUrl = null
      },
      beginDate () {
        const self = this
        return {
          disabledDate (time) {
            if (self.dataForm.expirationDate) {  // 如果结束时间不为空，则小于结束时间
              return new Date(self.dataForm.expirationDate).getTime() < time.getTime()
            } else {
              // return time.getTime() > Date.now()//开始时间不选时，结束时间最大值小于等于当天
            }
          }
        }
      },
      processDate () {
        const self = this
        return {
          disabledDate (time) {
            if (self.dataForm.effectiveDate) {  // 如果开始时间不为空，则结束时间大于开始时间
              return new Date(self.dataForm.effectiveDate).getTime() > time.getTime()
            } else {
              // return time.getTime() > Date.now()//开始时间不选时，结束时间最大值小于等于当天
            }
          }
        }
      },
      uploadSuccess (res, file) {
        let quill = this.$refs.myQuillEditor.quill
        // 如果上传成功
        if (res.success) {
          // 获取光标所在位置
          let length = quill.getSelection().index
          // 插入图片  res.url为服务器返回的图片地址
          quill.insertEmbed(length, 'image', this.$http.adornUrl(res.obj.path))
          // 调整光标到最后
          quill.setSelection(length + 1)
        } else {
          this.$message.error('图片插入失败')
        }
        // loading动画消失
        this.quillUpdateImg = false
      },
      // 富文本图片上传前
      beforeUpload () {
        this.quillUpdateImg = true
      },
      // 富文本图片上传失败
      uploadError () {
        this.quillUpdateImg = false
        this.$message.error('图片插入失败')
      },
      wordBeforeUpload (file) {
        let FileExt = file.name.replace(/.+\./, '')
        console.info(FileExt)
        if ('docx'.indexOf(FileExt.toLowerCase()) === -1) {
          return this.$message.error('上传失败,请上传后缀名为doc、docx的附件！')
        }
      },
      wordChangHandle (file, fileList) {
        let FileExt = file.name.replace(/.+\./, '')
        if ('docx'.indexOf(FileExt.toLowerCase()) === -1) {
          fileList.length = fileList.length - 1
          return this.$message.error('上传失败,请上传后缀名为doc、docx的附件！')
        }
        if (fileList.length > 1) {
          this.fileList = fileList.slice(1)
        }
      },
      wordErrorHandle (err, file, fileList) {
        this.fileList = []
        this.$message({
          title: '上传失败',
          message: err,
          duration: 1000
        })
      },
      wordUploadSuccess (res) {
        var that = this
        if (res.success) {
          let newContent = res.obj.replace(/src=[\'\"]?([^\'\"]*)[\'\"]?/gi, function (match, capture) {
            return `src='${that.$http.adornUrl(capture)}'`
          })
          this.dataForm.description = newContent
          this.fileList = []
          this.$message({
            message: '操作成功',
            type: 'success',
            duration: 1500
          })
        }
      },
      submitUpload () {
        this.$refs.upload.submit()
      }
    }
  }
</script>

<style lang="scss">
  .avatar {
    width: 178px;
    height: 178px;
    display: block;
  }
  .avatar-uploader .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
  }

  .avatar-uploader .el-upload:hover {
    border-color: #409EFF;
  }

  .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 178px;
    height: 178px;
    line-height: 178px;
    text-align: center;
  }
  .mod-menu {
    .menu-list__input,
    .icon-list__input {
      > .el-input__inner {
        cursor: pointer;
      }
    }
    &__icon-popover {
      width: 458px;
      overflow: hidden;
    }
    &__icon-inner {
      width: 478px;
      max-height: 258px;
      overflow-x: hidden;
      overflow-y: auto;
    }
    &__icon-list {
      width: 458px;
      padding: 0;
      margin: -8px 0 0 -8px;
      > .el-button {
        padding: 8px;
        margin: 8px 0 0 8px;
        > span {
          display: inline-block;
          vertical-align: middle;
          width: 18px;
          height: 18px;
          font-size: 18px;
        }
      }
    }
    .icon-list__tips {
      font-size: 18px;
      text-align: center;
      color: #e6a23c;
      cursor: pointer;
    }
  }
</style>
