<template>
  <div class="mod-role">
    <el-form :inline="true" ref="dataForm" :model="dataForm" @keyup.enter.native="getDataList()">
      <el-form-item label="标题:" prop="title">
        <el-input v-model="dataForm.title" placeholder="标题" clearable></el-input>
      </el-form-item>
      <el-form-item label="标签:" prop="labelName">
        <el-input v-model="dataForm.labelName" placeholder="标签" clearable></el-input>
      </el-form-item>
      <el-form-item label="所属栏目" prop="categoryId">
        <el-cascader
          placeholder="所属栏目"
          v-model="dataForm.categoryId"
          :options="categories"
          :props="{checkStrictly: true}"
        ></el-cascader>
      </el-form-item>
      <el-form-item label="审核状态：" prop="auditStatus">
        <el-select v-model="dataForm.auditStatus" clearable placeholder="请选择">
          <el-option
            v-for="item in auditStatusList"
            :key="item.code"
            :label="item.name"
            :value="item.code">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="过期时间" prop="dateTime">
        <el-date-picker
          v-model="dataForm.dateTime"
          clearable
          type="datetimerange"
          value-format="yyyy-MM-dd HH:mm:ss"
          format="yyyy/MM/dd"
          align="right"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="['00:00:00', '23:59:59']">
        </el-date-picker>
      </el-form-item>
      <div style="float:right;margin-bottom: 15px;">
      <!-- <el-row type="flex" justify="end" style="margin-bottom: 15px;"> -->
        <el-button icon="el-icon-search" @click="getDataList()">查询</el-button>
        <el-button icon="el-icon-refresh" @click="resetForm()">重置</el-button>
        <el-button type="primary" @click="addOrUpdateHandle()">新增</el-button>
        <el-button type="primary" @click="deleteHandle()" :disabled="dataListSelections.length <= 0">批量删除</el-button>
        <el-button type="primary" @click="attachHandle()" :disabled="dataListSelections.length != 1">附件</el-button>
        <!--<el-button v-if="isAuth('cms:content:audit')" type="danger" @click="auditHandle()" :disabled="dataListSelections.length <= 0">审核</el-button>-->
      <!-- </el-row> -->
      </div>
    </el-form>
    <el-table
      :data="dataList"
      border
      v-loading="dataListLoading"
      @selection-change="selectionChangeHandle"
      style="width: 100%;">
      <el-table-column
        type="selection"
        header-align="center"
        align="center"
        width="50">
      </el-table-column>
      <el-table-column
        prop="title"
        header-align="center"
        align="center"
        label="标题">
      </el-table-column>
      <el-table-column
        prop="categoryName"
        header-align="center"
        align="center"
        label="所属栏目">
      </el-table-column>
      <el-table-column
        prop="parentName"
        header-align="center"
        align="center"
        label="所属上级栏目">
      </el-table-column>
      <el-table-column
        prop="labelName"
        header-align="center"
        align="center"
        label="标签">
      </el-table-column>
      <el-table-column
        prop="effectiveDate"
        header-align="center"
        align="center"
        label="生效日期">
      </el-table-column>
      <el-table-column
        prop="expirationDate"
        header-align="center"
        align="center"
        label="过期日期">
      </el-table-column>
      <el-table-column
        prop="sequence"
        header-align="center"
        align="center"
        label="排序">
      </el-table-column>
      <el-table-column
        prop="importantLevel"
        header-align="center"
        align="center"
        label="重要度">
      </el-table-column>
      <el-table-column
        prop="stick"
        header-align="center"
        align="center"
        label="置顶">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.stick == false" size="small" type="danger">否</el-tag>
          <el-tag v-else size="small">是</el-tag>
        </template>
      </el-table-column>
      <el-table-column
        prop="hot"
        header-align="center"
        align="center"
        label="热门">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.hot == false" size="small" type="danger">否</el-tag>
          <el-tag v-else size="small">是</el-tag>
        </template>
      </el-table-column>
      <el-table-column
        prop="auditStatusName"
        header-align="center"
        align="center"
        label="审核状态">
      </el-table-column>
      <el-table-column
        prop="comment"
        header-align="center"
        align="center"
        label="可以评论">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.comment == false" size="small" type="danger">否</el-tag>
          <el-tag v-else size="small">是</el-tag>
        </template>
      </el-table-column>
      <el-table-column
        prop="virtualReading"
        header-align="center"
        align="center"
        label="虚拟阅读量">
      </el-table-column>
      <el-table-column
        prop="actualReading"
        header-align="center"
        align="center"
        label="实际阅读量">
      </el-table-column>
      <el-table-column
        fixed="right"
        header-align="center"
        align="center"
        width="260"
        label="操作">
        <template slot-scope="scope">
          <el-button v-if="isAuth('cms:content:update')" type="text" size="small" @click="addOrUpdateHandle(scope.row.id)" class="btn-control">修改</el-button>
          <el-button v-if="isAuth('cms:content:delete')" type="text" size="small" @click="deleteHandle(scope.row.id)" class="btn-control">删除</el-button>
          <el-button v-if="isAuth('cms:content:stick')" type="text" size="small" @click="stickHandle(scope.row.id, scope.row.stick)">置顶</el-button>
          <el-button v-if="isAuth('cms:content:hot')" type="text" size="small" @click="hotHandle(scope.row.id, scope.row.hot)">热门</el-button>
          <el-button v-if="isAuth('cms:content:prop')" type="text" size="small" @click="propHandle(scope.row.categoryId, scope.row.contentId)">属性</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      @size-change="sizeChangeHandle"
      @current-change="currentChangeHandle"
      :current-page="pageIndex"
      :page-sizes="[10, 20, 50, 100]"
      :page-size="pageSize"
      :total="totalPage"
      layout="total, sizes, prev, pager, next, jumper">
    </el-pagination>
    <!-- 弹窗, 审核 -->
    <content-audit v-if="auditVisible" ref="contentAudit" @refreshDataList="getDataList"></content-audit>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"></add-or-update>
    <!-- 弹窗, 新增附件 -->
    <attach v-if="attachVisible" ref="attach"></attach>
    <!-- 弹窗, 属性 -->
    <content-prop v-if="contentPropVisible" ref="contentProp"></content-prop>
  </div>
</template>

<script>
  import contentAudit from './content-audit'
  import AddOrUpdate from './content-add-or-update'
  import ContentProp from './content-prop'
  import Attach from './attach-list'
  import listMixin from '@/mixins/list-mixins'
  export default {
    mixins: [listMixin],
    data () {
      return {
        mixinOptions: {
          dataUrl: '/admin/cms/categoryContent/page',
          deleteUrl: '/admin/cms/categoryContent/recyclingByIds'
        },
        dataForm: {
          title: '',
          labelName: '',
          auditStatus: '',
          dateTime: '',
          categoryId: []
        },
        auditStatusList: [],
        categories: [],
        auditVisible: false,
        attachVisible: false,
        contentPropVisible: false,
        importVisible: false
      }
    },
    components: {
      AddOrUpdate,
      contentAudit,
      ContentProp,
      Attach
    },
    activated () {
      this.getDataList()
      this.getAuditStatusList()
      this.getCategories()
    },
    methods: {
      queryBeforeHandle () {
        // 查询前操作
        if (this.dataForm.dateTime) {
          this.dataForm.startDate = this.dataForm.dateTime[0]
          this.dataForm.endDate = this.dataForm.dateTime[1]
        }
        this.dataForm.categoryList = this.dataForm.categoryId.join(',')
      },
      // 属性
      propHandle (categoryId, contentId) {
        this.contentPropVisible = true
        this.$nextTick(() => {
          this.$refs.contentProp.init(categoryId, contentId)
        })
      },
      // 审核
      auditHandle (id) {
        this.auditVisible = true
        this.$nextTick(() => {
          let ids = id ? [id] : this.dataListSelections.map(item => {
            return item.id
          })
          this.$refs.contentAudit.init(ids.join(','))
        })
      },
      attachHandle () {
        this.attachVisible = true
        this.$nextTick(() => {
          var contentId = this.dataListSelections.map(item => {
            return item.contentId
          })
          this.$refs.attach.init(contentId[0])
        })
      },
      // 删除
      hotHandle (id, hot) {
        this.$confirm(`确定进行[${hot ? '取消热门' : '热门'}]操作?`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
          closeOnClickModal: false
        }).then(() => {
          this.$http({
            url: this.$http.adornUrl('/admin/cms/categoryContent/setHot'),
            method: 'get',
            params: this.$http.adornParams({
              'id': id
            })
          }).then(({data}) => {
            if (data && data.code === 0) {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 1500,
                onClose: () => {
                  this.getDataList()
                }
              })
            } else {
              this.$message.error(data.msg)
            }
          })
        }).catch(() => {})
      },
      stickHandle (id, stick) {
        this.$confirm(`确定进行[${stick ? '取消置顶' : '置顶'}]操作?`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
          closeOnClickModal: false
        }).then(() => {
          this.$http({
            url: this.$http.adornUrl('/admin/cms/categoryContent/setStick'),
            method: 'get',
            params: this.$http.adornParams({
              'id': id
            })
          }).then(({data}) => {
            if (data && data.code === 0) {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 1500,
                onClose: () => {
                  this.getDataList()
                }
              })
            } else {
              this.$message.error(data.msg)
            }
          })
        }).catch(() => {})
      },
      getCategories () {
        this.$http({
          url: this.$http.adornUrl('/admin/cms/category/cascader'),
          method: 'get',
          params: this.$http.adornParams()
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.categories = this.getTreeData(data.obj)
          } else {
            this.categories = []
          }
        })
      },
      // *处理点位分类最后children数组为空的状态
      getTreeData: function (data) {
        var that = this
        // 循环遍历json数据
        data.forEach(function (e) {
          if (!e.children || e.children.length < 1) {
            e.children = undefined
          } else {
            // children若不为空数组，则继续 递归调用 本方法
            that.getTreeData(e.children)
          }
        })
        return data
      },
      // 审核状态下拉列表
      getAuditStatusList () {
        this.$http({
          url: this.$http.adornUrl('/admin/dict/parent'),
          method: 'get',
          params: this.$http.adornParams({
            'code': 'validStatus'
          })
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.auditStatusList = data.obj
          } else {
            this.auditStatusList = []
          }
        })
      }
    }
  }
</script>
