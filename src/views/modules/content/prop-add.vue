<template>
  <el-dialog
    :title="'新增属性'"
    :close-on-click-modal="false"
    :visible.sync="visible"
    append-to-body>
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmit()" label-width="80px">
      <el-form-item label="属性" prop="propId" :error="errors['propId']">
        <el-select v-model="dataForm.propId" placeholder="请选择">
          <el-option
            v-for="item in propList"
            :key="item.id"
            :label="item.name"
            :value="item.id">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="属性值" prop="propValue" :error="errors['propValue']">
        <el-input v-model="dataForm.propValue" placeholder="属性值"></el-input>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
  import editMixin from '@/mixins/edit-mixins'
  export default {
    mixins: [editMixin],
    data () {
      return {
        editOptions: {
          isUniteUrl: true,
          submitUrl: '/admin/cms/contentProp/save'
        },
        propList: [],
        dataForm: {
          contentId: null,
          propId: null,
          propValue: null,
          categoryRelate: false
        },
        dataRule: {
          propId: [
            { required: true, message: '属性不能为空', trigger: 'blur' }
          ],
          propValue: [
            { required: true, message: '属性值不能为空', trigger: 'blur' }
          ]
        }
      }
    },
    methods: {
      init (contentId) {
        this.dataForm.contentId = contentId
        this.getExtendedPropList()
        this.visible = true
        this.$nextTick(() => {
          this.$refs['dataForm']?.resetFields()
        })
      },
      // 获取扩展属性列表
      getExtendedPropList () {
        this.$http({
          url: this.$http.adornUrl('/admin/cms/extended/getExtendedPropList'),
          method: 'get',
          params: this.$http.adornParams()
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.propList = data.obj
          } else {
            this.$message.error(data.msg)
          }
        })
      }
    }
  }
</script>
