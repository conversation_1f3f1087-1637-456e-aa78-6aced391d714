<template>
  <div class="mod-config">
    <el-form :inline="true" :model="dataForm" @keyup.enter.native="queryPage()">
      <el-form-item label="关键字">
        <el-input v-model="dataForm.key" placeholder="地域名/code" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-button @click="queryPage()">查询</el-button>
      </el-form-item>
    </el-form>
    <div class="f-s-c"
         style="padding: 15px 0;display: flex;justify-content:space-between;align-items: center;float:right ">
      <el-button @click="forceSync()" type="danger">强制同步</el-button>
      <el-button type="success"   @click="zsqDockingRecords()">知社区对接记录</el-button>
    </div>
    <el-table
        :data="dataList"
        border
        v-loading="dataListLoading"
        style="width: 100%;">
      <el-table-column
          prop="regionCode"
          header-align="center"
          align="center"
          label="地域code">
      </el-table-column>
      <el-table-column
          prop="regionName"
          header-align="center"
          align="center"
          label="地域名">
      </el-table-column>
      <el-table-column
          prop="parentCode"
          header-align="center"
          align="center"
          label="父类code">
      </el-table-column>
      <el-table-column
          prop="createDate"
          header-align="center"
          align="center"
          label="同步时间">
      </el-table-column>
    </el-table>
    <el-pagination
        @size-change="sizeChangeHandle"
        @current-change="currentChangeHandle"
        :current-page="pageIndex"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pageSize"
        :total="totalPage"
        layout="total, sizes, prev, pager, next, jumper">
    </el-pagination>
      <zsq-docking-records v-if="zsqDockingRecordsVisible" ref="zsqDockingRecords"></zsq-docking-records>
  </div>
</template>

<script>

import ZsqDockingRecords from "./region-all-zsq-docking-records.vue";

export default {
  data() {
    return {
      dataForm: {
        regionName: ''
      },
      dataList: [],
      pageIndex: 1,
      pageSize: 10,
      totalPage: 0,
      dataListLoading: false,
      dataListSelections: [],
      addOrUpdateVisible: false,
      zsqDockingRecordsVisible: false
    }
  },
  components: {ZsqDockingRecords},
  activated() {
    this.queryPage()
  },
  methods: {
    queryPage() {
      this.pageIndex = 1
      this.getDataList()
    },
    // 获取数据列表
    getDataList() {
      this.dataListLoading = true
      this.$http({
        url: this.$http.adornUrl('/admin/zyz/sync/region/dict/all/pages'),
        method: 'post',
        data: this.$http.adornData({
          'currentPage': this.pageIndex,
          'pageSize': this.pageSize,
          'key': this.dataForm.key,
          'orders': [{column: 'regionCode', sort: 'asc'}]
        })
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.dataList = data.obj.records
          this.totalPage = data.obj.total
        } else {
          this.dataList = []
          this.totalPage = 0
        }
        this.dataListLoading = false
      })
    },
    // 每页数
    sizeChangeHandle(val) {
      this.pageSize = val
      this.pageIndex = 1
      this.getDataList()
    },
    // 当前页
    currentChangeHandle(val) {
      this.pageIndex = val
      this.getDataList()
    },
    // 强制同步
    forceSync() {
      this.$confirm(`确定要立刻开始同步码?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        closeOnClickModal: false
      }).then(() => {
        this.dataListLoading = true
        this.$http({
          url: this.$http.adornUrl('/admin/zyz/platform-sync/syncRegionAll'),
          method: 'post',
          params: {}
        }).then(({data}) => {
          this.dataListLoading = false
          if (data.code !== 0) {
            return this.$message.error(data.msg)
          }
          this.$message({
            message: '操作成功',
            type: 'success',
            duration: 1000,
            onClose: () => {
              this.queryPage()
            }
          })
        })
      })
    },
    zsqDockingRecords() {
        this.zsqDockingRecordsVisible = true
        this.$nextTick(() => {
            this.$refs.zsqDockingRecords.init()
        })
    }
  }
}
</script>
