<template>
  <el-dialog
      width="80%"
      :title="'查看详情'"
      :close-on-click-modal="false"
      :visible.sync="visible">
    <el-form :model="dataForm" ref="dataForm" label-width="150px">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="同步请求单ID" prop="syncBillId">
            <el-input v-model="dataForm.syncBillId" placeholder="mq消息id（幂等）"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="同步业务类型" prop="bizType">
            <el-dict code="sync_biz_type" v-model="dataForm.bizType"></el-dict>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="同步对象" prop="syncObjectName">
            <el-input v-model="dataForm.syncObjectName" placeholder="同步对象"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="业务表id" prop="bizId">
            <el-input v-model="dataForm.bizId" placeholder="业务表id"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="响应结果" prop="resultCode">
            <el-tag v-if="dataForm.resultCode === 'SUCCESS'" type="success">成功</el-tag>
            <el-tag v-if="dataForm.resultCode === 'FAIL'" type="warning">失败</el-tag>
            <el-tag v-if="dataForm.resultCode === 'ERROR'" type="danger">错误</el-tag>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="市平台返回的消息" prop="message">
            <el-input v-model="dataForm.message" placeholder="市平台返回的消息"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="同步时间" prop="syncTime">
            <el-date-picker
                v-model="dataForm.syncTime"
                value-format="yyyy-MM-dd HH:mm:ss"
                type="datetime"
                placeholder="同步时间">
            </el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="响应编码" prop="spCode">
            <el-input v-model="dataForm.spCode" placeholder="响应编码"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="请求体" prop="requestParam">
            <el-input v-model="dataForm.requestParam" placeholder="请求体" type="textarea" :rows="10"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="响应" prop="response">
            <el-input v-model="dataForm.response" placeholder="响应" type="textarea" :rows="10"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">关闭</el-button>
    </span>
  </el-dialog>
</template>

<script>
import moment from 'moment'

export default {
  data() {
    return {
      visible: false,
      dataForm: {
        id: null,
        version: null,
        syncBillId: '',
        bizType: '',
        bizId: '',
        remark: '',
        resultCode: '',
        message: '',
        syncTime: '',
        spCode: '',
        requestParam: '',
        response: '',
        syncObjectName: ''
      }
    }
  },
  components: {
    moment
  },
  methods: {
    init(id) {
      this.dataForm.id = id || null
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm']?.resetFields()
        if (this.dataForm.id) {
          this.$http({
            url: this.$http.adornUrl(`/admin/zyz/sync-log`),
            method: 'get',
            params: this.$http.adornParams({id: this.dataForm.id})
          }).then(({data}) => {
            if (data && data.code === 0) {
              this.dataForm = data.obj
              this.dataForm.response = JSON.stringify(JSON.parse(this.dataForm.response), null, '\t')
              this.dataForm.requestParam = JSON.stringify(JSON.parse(this.dataForm.requestParam), null, '\t')
            }
          })
        }
      })
    }
  }
}
</script>
