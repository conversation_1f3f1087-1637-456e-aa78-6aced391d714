<template>
    <div>
  <el-dialog
      :title="!dataForm.id ? '新增' : '修改'"
      :close-on-click-modal="false"
      :visible.sync="visible">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmit()" width="80%"
             label-width="150px">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="职能部门ID" prop="depId" :error="depIdError">
            <el-input v-model="dataForm.depId" placeholder="职能部门ID" :disabled="dataForm.id"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="市平台ID" prop="syncId" :error="syncIdError" v-show="dataForm.id">
            <el-input v-model="dataForm.syncId" placeholder="同步ID" :disabled="dataForm.id"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="职能部门名称" prop="depName" :error="depNameError">
            <el-input v-model="dataForm.depName" placeholder="职能部门名称" :disabled="dataForm.id"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="职能部门类型" prop="depTypeId" :error="depTypeIdError">
            <el-select v-model="dataForm.depTypeId" placeholder="请选择" :disabled="dataForm.id">
              <el-option
                  v-for="item in typeList"
                  :key="item.depTypeId"
                  :label="item.depTypeName"
                  :value="item.depTypeId">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="负责人姓名" prop="contact" :error="contactError">
            <el-input v-model="dataForm.contact" placeholder="负责人姓名" :disabled="dataForm.id"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="负责人电话" prop="contactNo" :error="contactNoError">
            <el-input v-model="dataForm.contactNo" placeholder="负责人电话" :disabled="dataForm.id"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col>
          <el-form-item :label="'行政区划'" prop="regionCode">
            <el-cascader placeholder="所属组织机构" v-model="dataForm.regionCode" :options="regionList"
                         :disabled="dataForm.id"
                         :show-all-levels="false" clearable
                         :props="{checkStrictly: true,value: 'regionCode',label: 'regionName'}"></el-cascader>
          </el-form-item>
        </el-col>
      </el-row>
          <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="详细地址" prop="mapAddress">
            <el-input style="width: 80%" v-model="dataForm.mapAddress" :placeholder="'详细地址'" type="textarea" :rows="2"
                      :disabled="dataForm.id"></el-input>
              <el-button style="float: right" type="primary" @click="showMap">地图定位</el-button>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col>
          <el-form-item label="经度" prop="txMapLan" :error="txMapLanError">
            <el-input v-model="dataForm.txMapLan" :placeholder="'经度'" disabled></el-input>
          </el-form-item>
        </el-col>
        <el-col>
          <el-form-item label="纬度" prop="txMapLat" :error="txMapLatError">
            <el-input v-model="dataForm.txMapLat" :placeholder="'纬度'" disabled></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="描述" prop="intro" :error="introError">
            <el-input v-model="dataForm.intro" :placeholder="'描述'" type="textarea" :rows="5" maxlength="500" show-word-limit
                      :disabled="dataForm.id"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="图片" prop="picLogoUrl">
            <el-upload
                class="avatar-uploader"
                :action="this.$http.adornUrl(`/admin/oss/upload`)"
                :headers="myHeaders"
                :data="{serverCode: this.serverCode,media:true}"
                :show-file-list="false"
                :on-success="function (res,file){return handleAvatarSuccess(res,file, 'picLogoUrl')}"
                :before-upload="beforeAvatarUpload">
              <img v-if="dataForm.picLogoUrl" :src="$http.adornAttachmentUrl(dataForm.picLogoUrl)" class="avatar">
              <i v-else class="el-icon-plus avatar-uploader-icon"></i>
            </el-upload>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="排序" prop="sequence" :error="sequenceError">
            <el-input-number v-model="dataForm.sequence" controls-position="right" :min="0"></el-input-number>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
  <el-dialog class="map-dialog" title="地图" :close-on-click-modal="false" width="80%" :visible.sync="mapVisible">
      <AMapInfo
              v-if="mapVisible"
              :mapVisible.sync="mapVisible"
              :address.sync="dataForm.mapAddress"
              :latitude.sync="dataForm.txMapLat"
              :longitude.sync="dataForm.txMapLan"></AMapInfo>
  </el-dialog>
    </div>
</template>

<script>
import {getFathersById, treeDataTranslate} from "@/utils";
import Vue from 'vue'
import AMapInfo from '@/components/map/a-map-info.vue'
import moment from "moment/moment";
import {is8lPhone, isMobile} from "@/utils/validate";
export default {
  data() {
    const validateAddress = (rule, value, callback) => {
        if (!this.dataForm.txMapLan || this.dataForm.txMapLan === '' || !this.dataForm.txMapLat || this.dataForm.txMapLat === '') {
            callback(new Error('需要地址经纬度，请通过地图定位选择地址！'))}
        else {callback()}
    }
    const validateContactPhone = (rule, value, callback) => {
        if (!isMobile(value) && !is8lPhone(value)) {
            callback(new Error('联系电话须为手机号或区号+8位座机号例如051288888888或0512-88888888'))
        } else {
            callback()
        }
    }
    return {
      regionList: [],
      myHeaders: {Authorization: Vue.cookie.get('Authorization')},
      serverCode: 'LocalServer',
      visible: false,
      typeList: [],
      dataForm: {
        id: null,
        version: null,
        depId: '',
        syncId: '',
        depName: '',
        depTypeId: '',
        sequence: '',
        contact: '',
        contactNo: '',
        intro: '',
        mapAddress: '',
        txMapLan: null,
        txMapLat: null,
        picLogoUrl: '',
        regionCode: ''
      },
      dataRule: {
        depName: [
          {required: true, message: '职能部门名称不能为空', trigger: 'blur'}
        ],
        depTypeId: [
          {required: true, message: '职能部门类型id不能为空', trigger: 'blur'}
        ],
        intro: [
          {required: true, message: '简介不能为空', trigger: 'blur'}
        ],
        txMapLat: [
          {required: true, message: '纬度不能为空', trigger: 'blur'}
        ],
        txMapLan: [
          {required: true, message: '经度不能为空', trigger: 'blur'}
        ],
        contact: [
          {required: true, message: '负责人不能为空', trigger: 'blur'}
        ],
        contactNo: [
          {required: true, message: '负责人联系电话不能为空', trigger: 'blur'},
          {validator: validateContactPhone, trigger: 'change'}
        ],
        sequence: [
          {required: true, message: '排序不能为空', trigger: 'blur'}
        ],
        mapAddress: [
          {required: true, message: '详细地址不能为空', trigger: 'blur'},
          {validator: validateAddress, trigger: 'change'}
        ],
        regionCode: [
          {required: true, message: '行政区域不能为空', trigger: 'blur'}
        ],
          picLogoUrl: [
              {required: true, message: '图片不能为空', trigger: 'blur'}
          ],
      },
      depIdError: null,
      syncIdError: null,
      depNameError: null,
      depTypeIdError: null,
      sequenceError: null,
      contactError: null,
      contactNoError: null,
      txMapLatError: null,
      txMapLanError: null,
      introError: null,
      mapVisible: false
    }
  },
  components: {
      AMapInfo,
      moment
  },
  methods: {
    async init(id) {
      this.dataForm.id = id || null
      this.visible = true
      await this.initRegionTree()
      this.$nextTick(() => {
        this.initDepType()
        this.$refs['dataForm']?.resetFields()
        if (this.dataForm.id) {
          this.$http({
            url: this.$http.adornUrl(`/admin/zyz/sync-dep`),
            method: 'get',
            params: this.$http.adornParams({id: this.dataForm.id})
          }).then(({data}) => {
            if (data && data.code === 0) {
              data.obj.regionCode = getFathersById(data.obj.regionCode, this.regionList, 'regionCode')
              this.dataForm = data.obj
            }
          })
        }
      })
    },
    // 初始化职能部门类型
    initDepType() {
      this.$http({
        url: this.$http.adornUrl(`/admin/zyz/sync-dep-type-dict/all`),
        method: 'get',
        params: {}
      }).then(({data}) => {
        if (data) {
          this.typeList = data
        }
      })
    },
    // 表单提交
    dataFormSubmit() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          let tmpRegionCode = this.dataForm.regionCode
          this.dataForm.regionCode = tmpRegionCode && Array.isArray(tmpRegionCode) ? this.dataForm.regionCode[this.dataForm.regionCode.length - 1] : tmpRegionCode
          this.$http({
            url: this.$http.adornUrl(`/admin/zyz/sync-dep/${!this.dataForm.id ? 'saveDep' : 'update'}`),
            method: 'post',
            data: this.$http.adornData(this.dataForm)
          }).then(({data}) => {
            if (data && data.code === 0) {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 1500,
                onClose: () => {
                  this.visible = false
                  this.$emit('refreshDataList')
                }
              })
            } else if (data && data.code === 303) {
              for (let it of data.obj) {
                this[`${it.field}Error`] = it.message
              }
            } else {
              this.$message.error(data.msg)
            }
          })
        }
      })
    },
    // 初始化行政区划树
    async initRegionTree() {
      await this.$http({
        url: this.$http.adornUrl(`/admin/zyz/sync/region-dict/all`),
        method: 'get',
        params: this.$http.adornParams()
      }).then(({data}) => {
        this.regionList = treeDataTranslate(data, 'regionCode', 'parentCode')
      })
    },// 上传图片成功
    handleAvatarSuccess(res, file, field) {
      if (res.success) {
        this.$message({
          message: '操作成功',
          type: 'success',
          duration: 1500
        })
        console.log(res)
        this.dataForm[`${field}`] = res.obj.path
      } else {
        this.$message.error('上传失败')
      }
    },
    beforeAvatarUpload: function (file) {
      var isJPG = file.type === 'image/jpeg'
      var isPNG = file.type === 'image/png'
      var isBMP = file.type === 'image/bmp'
      var isLt2M = file.size / 1024 / 1024 < 2

      if (!isJPG && !isPNG && !isBMP) {
        this.$message.error('上传图片只能是图片!')
      }
      if (!isLt2M) {
        this.$message.error('上传文件大小不能超过 2MB!')
      }
      return (isJPG || isPNG || isBMP) && isLt2M
    },
    showMap() {
        this.mapVisible = true
    }
  }
}
</script>
