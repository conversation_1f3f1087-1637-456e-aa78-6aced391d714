<template>
  <div class="mod-config">
    <div class="f-s-c"
         style="padding: 15px 0;display: flex;justify-content:space-between;align-items: center;float:right ">
      <el-button @click="forceSync()" type="danger">强制同步</el-button>
      <el-button type="success"   @click="zsqDockingRecords()">知社区对接记录</el-button>
    </div>
    <el-table
        v-loading="dataListLoading"
        :data="dataList"
        style="width: 100%"
        row-key="typeId"
        border
        :tree-props="{children: 'children', hasChildren: 'hasChildren'}">
      <el-table-column
          prop="typeId"
          label="类型ID"
          width="100">
      </el-table-column>
      <el-table-column
          prop="typeName"
          label="服务领域名称"
          width="250">
      </el-table-column>
      <el-table-column
          prop="nodePath"
          label="层级">
      </el-table-column>
      <el-table-column
          prop="createDate"
          label="同步时间">
      </el-table-column>
      <el-table-column
          prop="minTimeLimit"
          label="最小限制时长">
      </el-table-column>
      <el-table-column
          prop="maxTimeLimit"
          label="最大限制时长">
      </el-table-column>
      <el-table-column fixed="right" header-align="center" align="center" width="170" label="操作">
        <template slot-scope="scope">
          <el-button type="text" size="small" @click="limitTime(scope.row.id,scope.row.minTimeLimit,scope.row.maxTimeLimit,scope.row.typeName)">时长限制</el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 弹窗, 新增 / 修改 -->
    <limit-time v-if="limitTimeVisible" ref="limitTime" @refreshDataList="getDataList"></limit-time>
    <zsq-docking-records v-if="zsqDockingRecordsVisible" ref="zsqDockingRecords"></zsq-docking-records>
  </div>
</template>

<script>

import LimitTime from "../platform/files_time_limit";
import ZsqDockingRecords from "./belong-field-zsq-docking-records.vue";

export default {
  data() {
    return {
      dataForm: {
        key: '',
      },
      dataList: [],
      pageIndex: 1,
      pageSize: 10,
      totalPage: 0,
      dataListLoading: false,
      dataListSelections: [],
      addOrUpdateVisible: false,
      limitTimeVisible: false,
      zsqDockingRecordsVisible: false,
    }
  },
  components: {
    ZsqDockingRecords,
    LimitTime

  },
  activated() {
    this.queryPage()
  },
  methods: {
    queryPage() {
      this.pageIndex = 1
      this.getDataList()
    },
    //时长限制
    limitTime(id,minTimeLimit,maxTimeLimit,typeName) {
      this.limitTimeVisible = true
      this.$nextTick(() => {
        this.$refs.limitTime.init(id,minTimeLimit,maxTimeLimit,typeName)
      })
    },
    // 获取数据列表
    getDataList() {
      this.dataListLoading = true
      this.$http({
        url: this.$http.adornUrl('/admin/zyz/platform/belongFieldDict/cascade'),
        method: 'get',
        data: this.$http.adornData({})
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.dataList = data.obj
        } else {
          this.dataList = []
        }
        this.dataListLoading = false
      })
    },
    // 每页数
    sizeChangeHandle(val) {
      this.pageSize = val
      this.pageIndex = 1
      this.getDataList()
    },
    // 当前页
    currentChangeHandle(val) {
      this.pageIndex = val
      this.getDataList()
    },
    // 强制同步
    forceSync() {
      this.$confirm(`确定要立刻开始同步码?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        closeOnClickModal: false
      }).then(() => {
        this.dataListLoading = true
        this.$http({
          url: this.$http.adornUrl('/admin/zyz/platform-sync/syncBelongField'),
          method: 'post',
          params: {}
        }).then(({data}) => {
          this.dataListLoading = false
          if (data.code !== 0) {
            return this.$message.error(data.msg)
          }
          this.$message({
            message: '操作成功',
            type: 'success',
            duration: 1000,
            onClose: () => {
              this.queryPage()
            }
          })
        })
      })
    },
    zsqDockingRecords() {
        this.zsqDockingRecordsVisible = true
        this.$nextTick(() => {
            this.$refs.zsqDockingRecords.init()
        })
    }
  }
}
</script>
