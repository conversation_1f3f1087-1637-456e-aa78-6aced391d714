<template>
  <el-dialog
      :title="'时长限制'"
      :close-on-click-modal="false"
      width="30%"
      :visible.sync="visible">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" label-width="160px">
            <el-form-item label="服务领域名称" prop="activityName" >
              <div>{{typeName}}</div>
            </el-form-item>
      <el-form-item label="最小限制时间（小时）" prop="minTimeLimit" >
        <el-input-number v-model="dataForm.minTimeLimit" placeholder="最小限制时间"></el-input-number>
      </el-form-item>
      <el-form-item label="最大限制时间（小时）" prop="maxTimeLimit">
        <el-input-number v-model="dataForm.maxTimeLimit" placeholder="最大限制时间"></el-input-number>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="updateLimitTime()">确认</el-button>
    </span>
  </el-dialog>
</template>

<script>
import moment from 'moment'

export default {
  data() {
    return {
      visible: false,
      typeName: '',
      dataForm: {
        id: null,
        minTimeLimit: '',
        maxTimeLimit: ''
      },
      dataRule: {
      },
      livelyError: null
    }
  },
  components: {
    moment
  },
  methods: {
    init(id, minTimeLimit, maxTimeLimit,typeName) {
      this.dataForm.id = id || null
      this.dataForm.minTimeLimit = minTimeLimit || null
      this.dataForm.maxTimeLimit = maxTimeLimit || null
      this.typeName = typeName || null
      this.visible = true
    },
    updateLimitTime() {
      this.$http({
        url: this.$http.adornUrl(`/admin/zyz/platform/belongFieldDict/updateLimitTime`),
        method: 'get',
        params: this.$http.adornParams({
          'id': this.dataForm.id,
          'minTimeLimit': this.dataForm.minTimeLimit,
          'maxTimeLimit': this.dataForm.maxTimeLimit
        })
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.$message({
            message: '操作成功',
            type: 'success',
            duration: 1500,
            onClose: () => {
              this.visible = false
              this.$emit('refreshDataList')
            }
          })
        } else {
          this.$message.error(data.msg)
        }
      })
    }
  }
}
</script>
