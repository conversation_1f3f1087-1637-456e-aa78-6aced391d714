<template>
  <div class="practice-sheet">
    <el-form :inline="true" ref="dataForm" :model="dataForm" @keyup.enter.native="getDataList()">
      <el-form-item label="关键词" prop="key">
        <el-input style="width: 300px" v-model="dataForm.key" placeholder="活动/关联资源/关联需求" clearable></el-input>
      </el-form-item>
<!--      <el-form-item label="类型" prop="type">-->
<!--        <el-select v-model="dataForm.type" placeholder="请选择" clearable>-->
<!--          <el-option-->
<!--              v-for="item in [{label: '需求', value: 'requirement'}, {label: '资源', value: 'resource'}, {label: '活动', value: 'activity'}]"-->
<!--              :key="item.value"-->
<!--              :label="item.label"-->
<!--              :value="item.value">-->
<!--          </el-option>-->
<!--        </el-select>-->
<!--      </el-form-item>-->
      <el-form-item :label="'同步状态'" prop="sync">
        <el-dict :code="'sync_status'" v-model="dataForm.sync"></el-dict>
      </el-form-item>
      <el-form-item>
        <el-button type="warning" icon="el-icon-search" @click="getDataList()">查询</el-button>
        <el-button icon="el-icon-refresh-left" @click="resetForm()">重置</el-button>
      </el-form-item>
    </el-form>
    <el-table
        :data="dataList"
        border
        v-loading="dataListLoading"
        style="width: 100%;">
<!--      <el-table-column-->
<!--          prop="type"-->
<!--          header-align="center"-->
<!--          align="center"-->
<!--          width="100"-->
<!--          label="类型">-->
<!--        <template slot-scope="scope">-->
<!--          <el-tag v-if="scope.row.type === 'requirement'" type="primary">需求</el-tag>-->
<!--          <el-tag v-if="scope.row.type === 'resource'" type="success">资源</el-tag>-->
<!--          <el-tag v-if="scope.row.type === 'activity'" type="info">活动</el-tag>-->
<!--        </template>-->
<!--      </el-table-column>-->
      <el-table-column
          prop="content"
          header-align="center"
          align="center"
          label="活动名称">
      </el-table-column>
      <el-table-column
          prop="actLinkResName"
          header-align="center"
          align="center"
          label="关联资源">
      </el-table-column>
      <el-table-column
          prop="actLinkReqName"
          header-align="center"
          align="center"
          label="关联需求">
      </el-table-column>
<!--      <el-table-column-->
<!--          prop="pbName"-->
<!--          header-align="center"-->
<!--          align="center"-->
<!--          width="150"-->
<!--          label="实践阵地">-->
<!--      </el-table-column>-->
<!--      <el-table-column-->
<!--          prop="dockType"-->
<!--          header-align="center"-->
<!--          align="center"-->
<!--          width="120"-->
<!--          label="对接方类型">-->
<!--        <template slot-scope="scope">-->
<!--          <el-tag v-if="scope.row.dockType === '1'" type="primary">协会</el-tag>-->
<!--          <el-tag v-if="scope.row.dockType === '2'" type="success">分协会</el-tag>-->
<!--          <el-tag v-if="scope.row.dockType === '3'" type="info">社区</el-tag>-->
<!--          <el-tag v-if="scope.row.dockType === '4'" type="info">团队</el-tag>-->
<!--        </template>-->
<!--      </el-table-column>-->
<!--      <el-table-column-->
<!--          prop="dockTime"-->
<!--          header-align="center"-->
<!--          align="center"-->
<!--          width="160"-->
<!--          label="对接时间">-->
<!--      </el-table-column>-->
<!--      <el-table-column-->
<!--          prop="finishTime"-->
<!--          header-align="center"-->
<!--          align="center"-->
<!--          width="160"-->
<!--          label="完成时间">-->
<!--      </el-table-column>-->
      <el-table-column
          prop="syncTime"
          header-align="center"
          align="center"
          width="180"
          label="同步时间">
      </el-table-column>
<!--      <el-table-column-->
<!--          prop="finishRemark"-->
<!--          header-align="center"-->
<!--          align="center"-->
<!--          width="180"-->
<!--          :show-overflow-tooltip="true"-->
<!--          label="完成备注">-->
<!--      </el-table-column>-->
      <el-table-column
          prop="syncText"
          header-align="center"
          align="center"
          width="150"
          label="同步状态">
        <template slot-scope="scope">
          <!--          <div v-if="scope.row.isSyncText === '同步失败'" style="color: #f6070f;cursor:pointer">{{scope.row.isSyncText}}</div>-->
          <!--          <div v-if="scope.row.isSyncText === '待同步'" style="color: #eee98a;cursor:pointer">{{scope.row.isSyncText}}</div>-->
          <!--          <div v-if="scope.row.isSyncText === '已同步'" style="color: #03f303;cursor:pointer">{{scope.row.isSyncText}}</div>-->
          <el-popover trigger="hover" placement="top">
            <p style="text-align: center">
<!--              {{-->
<!--                '同步时间：' + (scope.row.syncTime ? scope.row.syncTime : '')-->
<!--              }}<br>-->
              {{ scope.row.syncRemark }}</p>
            <div slot="reference" class="name-wrapper">
              <el-tag v-if="scope.row.sync === 'sync_failure'" type="danger">{{ scope.row.syncText }}</el-tag>
              <el-tag v-if="scope.row.sync === 'sync_wait'" type="warning">{{ scope.row.syncText }}</el-tag>
              <el-tag v-if="scope.row.sync === 'sync_success'" type="success">{{ scope.row.syncText }}</el-tag>
            </div>
          </el-popover>
        </template>
      </el-table-column>
      <el-table-column
          fixed="right"
          header-align="center"
          align="center"
          width="100"
          label="操作">
        <template slot-scope="scope">
          <el-button v-if="isAuth('practice_sheet:list:sync') && (scope.row.sync === 'sync_wait' || scope.row.sync === 'sync_failure')" type="text" size="small" @click="syncHandle(scope.row.linkId, scope.row.type)" class="btn-control">同步</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
        @size-change="sizeChangeHandle"
        @current-change="currentChangeHandle"
        :current-page="pageIndex"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pageSize"
        :total="totalPage"
        layout="total, sizes, prev, pager, next, jumper">
    </el-pagination>
  </div>
</template>

<script>
import listMixin from '@/mixins/list-mixins'
export default {
  mixins: [listMixin],
  data () {
    return {
      mixinOptions: {
        dataUrl: '/admin/zyz/platform/practice_sheet/pages'
      },
      dataForm: {
        key: '',
        // type: '',
        sync: '',
        orders: [{column: 'syncTime', sort: 'desc'}]
      }
    }
  },
  activated () {
    this.getDataList()
  },
  methods: {
    resetForm () {
      this.$refs['dataForm']?.resetFields()
      this.getDataList()
    },
    syncHandle (id, type) {
      this.$confirm(`确定进行同步?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        closeOnClickModal: false
      }).then(() => {
        this.$http({
          url: this.$http.adornUrl('/admin/zyz/platform/practice_sheet/syncOne'),
          method: 'get',
          params: this.$http.adornParams({
            'id':  id,
            'type': type
          })
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.$message.success('同步操作成功')
            this.getDataList()
          } else {
            this.$message.error(data.msg)
          }
        })
      })
    }
  }
}
</script>
