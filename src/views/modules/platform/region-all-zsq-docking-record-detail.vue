<template>
  <el-dialog
    class="common-dialog"
    :close-on-click-modal="false"
    :modal-append-to-body="false"
    title="详情"
    width="70%"
    :visible.sync="visible">
    <el-form :model="dataForm" ref="dataForm" label-width="120px">
      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="对接类型：" prop="dockingType">
            <el-tag v-if="dataForm.dockingType === 'push'" size="small" type="info">推送</el-tag>
            <el-tag v-else-if="dataForm.dockingType === 'receive'" size="small" type="info">接收</el-tag>
            <el-tag v-else size="small" type="warning">未知</el-tag>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="操作类型：" prop="dockingOperation">
            <el-tag v-if="dataForm.dockingOperation === 'CREATE'" size="small" type="info">所有社区创建</el-tag>
            <el-tag v-if="dataForm.dockingOperation === 'UPDATE'" size="small" type="info">所有社区编辑</el-tag>
            <el-tag v-if="dataForm.dockingOperation === 'DELETE'" size="small" type="info">所有社区删除</el-tag>
            <el-tag v-if="dataForm.dockingOperation === 'ON_OFF'" size="small" type="info">所有社区上/下架</el-tag>
            <el-tag v-if="dataForm.dockingOperation === 'AUDIT'" size="small" type="info">所有社区审核</el-tag>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="对接时间：" prop="dockingTime">
            <span>{{dataForm.dockingTime}}</span>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="对接状态：" prop="dockingStatus">
            <el-tag v-if="dataForm.dockingStatus === 0" size="small" type="warning">进行中</el-tag>
            <el-tag v-else-if="dataForm.dockingStatus === 1" size="small" type="success">成功</el-tag>
            <el-tag v-else-if="dataForm.dockingStatus === 2" size="small" type="danger">失败</el-tag>
            <el-tag v-else size="small" type="info">未知</el-tag>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="重试记录：" prop="retry">
            <span v-if="dataForm.retry" style="color: red">是</span>
            <span v-else>否</span>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="对接情况：" prop="dockingMsg">
            <span>{{dataForm.dockingMsg}}</span>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="对接请求数据：" prop="dockingDataObj">
            <json-viewer :value="dataForm.dockingDataObj" copyable style="box-shadow: 0 0 4px rgba(0, 0, 0, 0.15);"></json-viewer>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20" v-if="dataForm.dockingLog">
        <el-col :span="24">
          <el-form-item label="对接日志：" prop="dockingLog">
            <json-viewer :value="dataForm.dockingLog" copyable style="box-shadow: 0 0 4px rgba(0, 0, 0, 0.15);"></json-viewer>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20" v-if="dataForm.dockingMqFailLog">
        <el-col :span="24">
          <el-form-item label="MQ消费日志：" prop="dockingMqFailLog">
            <json-viewer :value="dataForm.dockingMqFailLog" copyable style="box-shadow: 0 0 4px rgba(0, 0, 0, 0.15);"></json-viewer>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button type="primary" @click="visible = false">关闭</el-button>
    </span>
  </el-dialog>
</template>

<script>
import JsonViewer from 'vue-json-viewer'
export default {
  data () {
    return {
      visible: false,
      dataForm: {
      }
    }
  },
  components: {
    JsonViewer
  },
  methods: {
    init (id) {
      this.visible = true
      this.$nextTick(() => {
        this.$http({
          url: this.$http.adornUrl('/admin/zsq_docking_region_all_record/getDockingRecordDetail'),
          method: 'get',
          params: this.$http.adornParams({
            "recordId": id
          })
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.dataForm = data.obj
          } else {
            this.$message.error(data.msg)
          }
        })
      })
    }
  }
}
</script>
<style lang="scss" scoped>
</style>
