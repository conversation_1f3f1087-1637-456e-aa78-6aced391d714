<template>
  <div>
  <el-dialog
      :title="!dataForm.id ? '新增' : '修改'"
      :close-on-click-modal="false"
      :visible.sync="visible">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmit()" width="80%"
             label-width="100px">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="实践点名称" prop="name" :error="nameError">
            <el-input v-model="dataForm.name" placeholder="实践点名称" :disabled="dataForm.isSync === 'sync_success'"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="负责人姓名" prop="contact" :error="contactError">
            <el-input v-model="dataForm.contact" placeholder="负责人姓名"  ></el-input>
          </el-form-item>
        </el-col>
<!--        <el-col :span="12">-->
<!--          <el-form-item :label="'行政区划'" prop="regionCode">-->
<!--            <el-cascader style="width: 100%" placeholder="所属组织机构" v-model="dataForm.regionCode" :options="regionList"-->
<!--                         :disabled="dataForm.isSync === 'sync_success'"-->
<!--                         :show-all-levels="false" clearable-->
<!--                         :props="{checkStrictly: true,value: 'regionCode',label: 'regionName'}"></el-cascader>-->
<!--          </el-form-item>-->
<!--        </el-col>-->
      </el-row>
<!--      <el-row :gutter="20">-->
<!--        <el-col :span="12">-->
<!--          <el-form-item label="负责人姓名" prop="contact" :error="contactError">-->
<!--            <el-input v-model="dataForm.contact" placeholder="负责人姓名"  ></el-input>-->
<!--          </el-form-item>-->
<!--        </el-col>-->
<!--      </el-row>-->
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="负责人电话" prop="contactNo" :error="contactNoError">
            <el-input v-model="dataForm.contactNo" placeholder="负责人电话"  ></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="详细地址" prop="mapAddress">
            <div style="display: flex;justify-content: space-around">
              <el-input style="flex: 1" v-model="dataForm.mapAddress" placeholder="详细地址" clearable></el-input>
              <el-button style="margin-left: 10px" type="primary" @click="showMap">地图定位</el-button>
            </div>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20"  v-if="">
        <el-col :span="12">
          <el-form-item label="经度" prop="txMapLan" :error="txMapLanError">
            <el-input v-model="dataForm.txMapLan" :placeholder="'经度'" disabled></el-input>
<!--            <span> {{dataForm.txMapLan}}</span>-->
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="纬度" prop="txMapLat" :error="txMapLatError">
            <el-input v-model="dataForm.txMapLat" :placeholder="'纬度'" disabled></el-input>
<!--            <span> {{dataForm.txMapLat}}</span>-->
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="描述" prop="intro" :error="introError">
            <el-input v-model="dataForm.intro" :placeholder="'描述'" type="textarea" :rows="5" maxlength="500" show-word-limit
                     ></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="图片" prop="picLogoUrl">
            <el-upload
                class="avatar-uploader"
                :action="this.$http.adornUrl(`/admin/oss/upload`)"
                :headers="myHeaders"
                :data="{serverCode: this.serverCode,media:true}"
                :show-file-list="false"
                :on-success="function (res,file){return handleAvatarSuccess(res,file, 'picLogoUrl')}"
                :before-upload="beforeAvatarUpload">
              <img v-if="dataForm.picLogoUrl" :src="$http.adornAttachmentUrl(dataForm.picLogoUrl)" class="avatar">
              <i v-else class="el-icon-plus avatar-uploader-icon"></i>
            </el-upload>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="上/下架" prop="onShelve" :error="onShelveError">
            <el-radio-group v-model="dataForm.onShelve">
              <el-radio :label="true">上架</el-radio>
              <el-radio :label="false">下架</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">关闭</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
  <el-dialog class="map-dialog" title="地图" :close-on-click-modal="false" width="80%" :visible.sync="mapVisible">
    <AMapInfo
        v-if="mapVisible"
        :mapVisible.sync="mapVisible"
        :address.sync="dataForm.mapAddress"
        :latitude.sync="dataForm.txMapLat"
        :longitude.sync="dataForm.txMapLan"></AMapInfo>
  </el-dialog>
  </div>
</template>

<script>
import moment from 'moment'
import _ from 'lodash'
import Vue from 'vue'
import {getFathersById, treeDataTranslate} from "@/utils";
import AMapInfo from '@/components/map/a-map-info.vue'
import {is8lPhone, isMobile} from "@/utils/validate";

export default {
  data() {
    const validateAddress = (rule, value, callback) => {
        if (!this.dataForm.txMapLat || this.dataForm.txMapLat === '' || !this.dataForm.txMapLan || this.dataForm.txMapLan === '') {
            callback(new Error('需要地址经纬度，请通过地图定位选择地址！'))}
        else {callback()}
    }
    const validateContactPhone = (rule, value, callback) => {
        if (!isMobile(value) && !is8lPhone(value)) {
            callback(new Error('11位手机号码或带区号固话（例：0512-83626001或051283626001）！'))
        } else {
            callback()
        }
    }
    return {
      myHeaders: {Authorization: Vue.cookie.get('Authorization')},
      serverCode: 'LocalServer',
      regionList: [],
      visible: false,
      initForm: {
        id: null,
        version: null,
        name: '',
        // regionCode: '',
        contact: '',
        contactNo: '',
        intro: '',
        mapAddress: '',
        txMapLat: null,
        txMapLan: null,
        picLogoUrl: '',
        sequence: '',
        isSync: '',
        syncId: '',
        syncTime: '',
        syncRemark: '',
        onShelve: false
      },
      dataForm: {},
      dataRule: {
        name: [
          {required: true, message: '实践点名称不能为空', trigger: 'blur'}
        ],
        contact: [
          {required: true, message: '负责人不能为空', trigger: 'blur'}
        ],
        contactNo: [
          {required: true, message: '负责人电话不能为空', trigger: 'blur'},
          {validator: validateContactPhone, trigger: 'change'}
        ],
        intro: [
          {required: true, message: '简介不能为空', trigger: 'blur'}
        ],
        mapAddress: [
          {required: true, message: '详细地址不能为空', trigger: 'blur'},
          {validator: validateAddress, trigger: 'change'}
        ],
        picLogoUrl: [
          {required: true, message: '图片不能为空', trigger: 'blur'}
        ],
        onShelve: [
          {required: true, message: '请选择上/下架状态', trigger: 'change'}
        ]
      },
      nameError: null,
      regionCodeError: null,
      contactError: null,
      contactNoError: null,
      introError: null,
      mapAddressError: null,
      txMapLatError: null,
      txMapLanError: null,
      picLogoUrlError: null,
      sequenceError: null,
      isSyncError: null,
      syncIdError: null,
      syncTimeError: null,
      syncRemarkError: null,
      onShelveError: null,
      mapVisible: false
    }
  },
  components: {
    AMapInfo,
    moment
  },
  methods: {
    async init(id) {
      this.dataForm = _.cloneDeep(this.initForm)
      this.dataForm.id = id || null
      this.visible = true
      await this.initRegionTree()
      this.$nextTick(() => {
        this.$refs['dataForm']?.resetFields()
        if (this.dataForm.id) {
          this.$http({
            url: this.$http.adornUrl(`/admin/platform/sync-spot`),
            method: 'get',
            params: this.$http.adornParams({id: this.dataForm.id})
          }).then(({data}) => {
            if (data && data.code === 0) {
              data.obj.regionCode = getFathersById(data.obj.regionCode, this.regionList, 'regionCode')
              this.dataForm = data.obj
            }
          })
        }
      })
    },
    // 表单提交
    dataFormSubmit() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          let tmpRegionCode = this.dataForm.regionCode
          this.dataForm.regionCode = tmpRegionCode && Array.isArray(tmpRegionCode) ? this.dataForm.regionCode[this.dataForm.regionCode.length - 1] : tmpRegionCode
          this.$http({
            url: this.$http.adornUrl(`/admin/platform/sync-spot/${!this.dataForm.id ? 'saveSpot' : 'updateSpot'}`),
            method: 'post',
            data: this.$http.adornData(this.dataForm)
          }).then(({data}) => {
            if (data && data.code === 0) {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 1500,
                onClose: () => {
                  this.visible = false
                  this.$emit('refreshDataList')
                }
              })
            } else if (data && data.code === 303) {
              for (let it of data.obj) {
                this[`${it.field}Error`] = it.message
              }
            } else {
              this.$message.error(data.msg)
            }
          })
        }
      })
    },
    // 初始化行政区划树
    async initRegionTree() {
      await this.$http({
        url: this.$http.adornUrl(`/admin/zyz/sync/region-dict/all`),
        method: 'get',
        params: this.$http.adornParams()
      }).then(({data}) => {
        this.regionList = treeDataTranslate(data, 'regionCode', 'parentCode')
      })
    },// 上传图片成功
    handleAvatarSuccess(res, file, field) {
      if (res.success) {
        this.$message({
          message: '操作成功',
          type: 'success',
          duration: 1500
        })
        console.log(res)
        this.dataForm[`${field}`] = res.obj.path
      } else {
        this.$message.error('上传失败')
      }
    },
    beforeAvatarUpload: function (file) {
      var isJPG = file.type === 'image/jpeg'
      var isPNG = file.type === 'image/png'
      var isBMP = file.type === 'image/bmp'
      var isLt2M = file.size / 1024 / 1024 < 2

      if (!isJPG && !isPNG && !isBMP) {
        this.$message.error('上传图片只能是图片!')
      }
      if (!isLt2M) {
        this.$message.error('上传文件大小不能超过 2MB!')
      }
      return (isJPG || isPNG || isBMP) && isLt2M
    },
    showMap() {
      this.mapVisible = true
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep(.map-dialog) {
  .el-dialog__body {
    padding: 15px 20px 30px;
  }
}
</style>

<style lang="scss" scoped>
::v-deep(.map-dialog) {
  .el-dialog__body {
    padding: 15px 20px 30px;
  }
}
</style>
