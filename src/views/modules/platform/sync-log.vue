<template>
  <div class="mod-config">
    <el-form :inline="true" :model="dataForm" @keyup.enter.native="queryPage()">
      <el-form-item label="同步对象">
        <el-input v-model="dataForm.syncObjectName" placeholder="同步对象" clearable></el-input>
      </el-form-item>
      <el-form-item label="同步业务">
        <el-dict v-model="dataForm.bizType" placeholder="同步业务" clearable :code="'sync_biz_type'"></el-dict>
      </el-form-item>
      <el-form-item label="同步时间" prop="dateTime">
        <el-date-picker
            :clearable="false"
            v-model="dataForm.startEndTime"
            type="datetimerange"
            value-format="yyyy-MM-dd HH:mm:ss"
            range-separator="-"
            start-placeholder="开始日期"
            end-placeholder="结束日期">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="响应结果" prop="resultCode">
        <el-select v-model="dataForm.resultCode" placeholder="请选择" clearable>
          <el-option
              :key="'SUCCESS'"
              :label="'成功'"
              :value="'SUCCESS'">
          </el-option>
          <el-option
              :key="'FAIL'"
              :label="'失败'"
              :value="'FAIL'">
          </el-option>
          <el-option
              :key="'ERROR'"
              :label="'错误'"
              :value="'ERROR'">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button @click="queryPage()">查询</el-button>
      </el-form-item>
    </el-form>
    <el-table
        :data="dataList"
        border
        v-loading="dataListLoading"
        style="width: 100%;">
      <el-table-column
          width="200"
          prop="syncBillId"
          header-align="center"
          align="center"
          label="同步请求单号">
      </el-table-column>
      <el-table-column
          width="200"
          prop="bizType"
          header-align="center"
          align="center"
          label="同步业务类型">
      </el-table-column>
      <el-table-column
          width="250"
          prop="syncObjectName"
          header-align="center"
          align="center"
          label="同步对象名称">
      </el-table-column>
      <el-table-column
          prop="resultCode"
          width="100"
          header-align="center"
          align="center"
          show-overflow-tooltip
          label="响应结果">
        <template v-slot="scope">
          <el-tag v-if="scope.row.resultCode === 'SUCCESS'" size="small" type="success">成功</el-tag>
          <el-tag v-if="scope.row.resultCode === 'FAIL'" size="small" type="warning">失败</el-tag>
          <el-tag v-if="scope.row.resultCode === 'ERROR'" size="small" type="danger">错误</el-tag>
        </template>
      </el-table-column>
      <el-table-column
          prop="syncTime"
          width="180"
          header-align="center"
          align="center"
          label="同步时间">
      </el-table-column>
      <el-table-column
          prop="spCode"
          width="100"
          header-align="center"
          align="center"
          label="业务编码">
      </el-table-column>
      <el-table-column
          prop="message"
          header-align="center"
          width="250"
          align="center"
          label="返回消息">
      </el-table-column>
      <el-table-column
          fixed="right"
          header-align="center"
          align="center"
          label="操作">
        <template v-slot="scope">
          <el-button type="text" size="small" @click="addOrUpdateHandle(scope.row.id)">查看详情</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
        @size-change="sizeChangeHandle"
        @current-change="currentChangeHandle"
        :current-page="pageIndex"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pageSize"
        :total="totalPage"
        layout="total, sizes, prev, pager, next, jumper">
    </el-pagination>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"></add-or-update>
  </div>
</template>

<script>
import AddOrUpdate from './sync-log-detail'
import moment from 'moment'

export default {
  data() {
    return {
      dataForm: {
        syncObjectName: '',
        bizType: '',
        startEndTime: [],
        startTime: '',
        endTime: '',
        resultCode: ''
      },
      dataList: [],
      pageIndex: 1,
      pageSize: 10,
      totalPage: 0,
      dataListLoading: false,
      dataListSelections: [],
      addOrUpdateVisible: false
    }
  },
  components: {
    AddOrUpdate
  },
  activated() {
    this.queryPage()
  },
  methods: {
    queryPage() {
      this.pageIndex = 1
      this.getDataList()
    },
    // 获取数据列表
    getDataList() {
      this.dataListLoading = true
      this.dataForm.startTime = this.dataForm.startEndTime ? this.dataForm.startEndTime[0] : null
      this.dataForm.endTime = this.dataForm.startEndTime ? this.dataForm.startEndTime[1] : null
      this.$http({
        url: this.$http.adornUrl('/admin/zyz/sync-log/pages'),
        method: 'post',
        data: this.$http.adornData({
          'currentPage': this.pageIndex,
          'pageSize': this.pageSize,
          'syncObjectName': this.dataForm.syncObjectName,
          'bizType': this.dataForm.bizType,
          'startTime': this.dataForm.startTime,
          'endTime': this.dataForm.endTime,
          'resultCode': this.dataForm.resultCode
        })
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.dataList = data.obj.records
          this.totalPage = data.obj.total
        } else {
          this.dataList = []
          this.totalPage = 0
        }
        this.dataListLoading = false
      })
    },
    // 每页数
    sizeChangeHandle(val) {
      this.pageSize = val
      this.pageIndex = 1
      this.getDataList()
    },
    // 当前页
    currentChangeHandle(val) {
      this.pageIndex = val
      this.getDataList()
    },
    // 新增 / 修改
    addOrUpdateHandle(id) {
      this.addOrUpdateVisible = true
      this.$nextTick(() => {
        this.$refs.addOrUpdate.init(id)
      })
    }
  }
}
</script>
