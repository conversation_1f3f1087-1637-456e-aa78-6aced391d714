<template>
  <div>
    <el-dialog
        :title="onlyRead === true ? '详情' : (!dataForm.id ? '新增' : '修改')"
        :close-on-click-modal="false"
        :visible.sync="visible" width="80%">
      <el-form :model="dataForm" :rules="dataRule" ref="dataForm" label-width="120px">
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="名称：" prop="name" :error="errors['name']">
              <el-input v-model="dataForm.name" placeholder="请输入" clearable :disabled="onlyRead"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item  v-if="!onlyRead" label="图片：" prop="pic">
              <el-upload
                  :disabled="onlyRead"
                  class="avatar-uploader"
                  :action="this.$http.adornUrl(`/admin/oss/upload`)"
                  :headers="headers"
                  :data="{serverCode: uploadOptions.serverCode, media: false}"
                  :show-file-list="false"
                  :on-success="successHandle"
                  :on-change="changHandle"
                  :on-exceed="exceedHandle"
                  :before-upload="beforeUploadHandle">
                <img v-if="dataForm.pic" :src="$http.adornAttachmentUrl(dataForm.pic)" class="avatar">
                <i v-else class="el-icon-plus avatar-uploader-icon"></i>
                <div slot="tip" class="el-upload__tip" style="color: red">尺寸建议600x1000像素，文件格式jpg、bmp或png，大小不超2M</div>
              </el-upload>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item  v-if="onlyRead" label="图片：" prop="pic">
              <el-image
                  :src="$http.adornAttachmentUrl(dataForm.pic)"
                  :preview-src-list="[$http.adornAttachmentUrl(dataForm.pic)]"
                  class="avatar"
              >
              </el-image>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="生效时间：" prop="effectiveTime" :error="errors['effectiveTime']">
              <el-date-picker
                  :disabled="onlyRead"
                  v-model="dataForm.effectiveTime"
                  type="datetime"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  placeholder="请选择">
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="排序：" prop="sequence" :error="errors['sequence']">
              <el-input-number v-model="dataForm.sequence" placeholder="请输入" :min="0" :disabled="onlyRead"></el-input-number>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="状态：" prop="status" :error="errors['status']">
              <el-radio-group v-model="dataForm.status" :disabled="onlyRead">
                <el-radio :label="true">启用</el-radio>
                <el-radio :label="false">禁用</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="外部链接：" prop="link" :error="errors['link']" >
              <el-input v-model="dataForm.link" :disabled="onlyRead" ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="介绍：" prop="introduction" :error="errors['introduction']">
              <el-input type="textarea" rows="3" maxlength="500" show-word-limit v-model="dataForm.introduction" placeholder="请输入" :disabled="onlyRead"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button v-if="!onlyRead || onlyRead === false" @click="visible = false">取消</el-button>
        <el-button v-if="onlyRead && onlyRead === true" @click="visible = false">关闭</el-button>
        <el-button v-if="!onlyRead || onlyRead === false" type="primary" @click="dataFormSubmit()" :disabled="dataFormLoading">确定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import Teditor from '@/components/tinymce'
import moment from 'moment'
import editMixin from '@/mixins/edit-mixins'
import fileUploadMixin from '@/mixins/file-upload-mixins'
export default {
  mixins: [editMixin, fileUploadMixin],
  data () {
    return {
      uploadOptions: {
        fieldName: 'pic',
        maxSize: 2
      },
      editOptions: {
        initUrl: '/admin/civilization_enterprise',
        saveSuffix: 'save',
        updateSuffix: 'update',
        submitUrl: '/admin/civilization_enterprise'
      },
      addFileVisible: false,
      dataForm: {
        name: null,
        pic: null,
        link: null,
        introduction: null,
        status: true,
        effectiveTime: '',
        sequence: 0
      },
      dataRule: {
        name: [
          { required: true, message: '单位名称不能为空', trigger: 'blur' }
        ],
        status: [
          {required: true, message: '状态不能为空', trigger: 'change'}
        ],
        effectiveTime: [
          {required: true, message: '生效时间不能为空', trigger: 'change'}
        ],
        sequence: [
          { required: true, message: '排序不能为空', trigger: 'blur' }
        ]
      },
      onlyRead: false
    }
  },
  components: {
    Teditor,
    moment
  },
  methods: {
    init (id, onlyRead) {
      this.dataForm = {
        id: id || undefined,
        name: null,
        pic: null,
        link: null,
        introduction: null,
        status: true,
        effectiveTime: '',
        sequence: 0
      }
      this.onlyRead = onlyRead
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm']?.resetFields()
        this.dataForm.effectiveTime = moment(new Date()).format('YYYY-MM-DD HH:mm:ss')
        if (this.dataForm.id) {
          this.getData()
        }
      })
    },
    handleImgRemove () {
      this.dataForm.pic = null
    }
  }
}
</script>

<style lang="scss">
.avatar {
  width: 178px;
  height: 178px;
  display: block;
}
.avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.avatar-uploader .el-upload:hover {
  border-color: #409EFF;
}

.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  line-height: 178px;
  text-align: center;
}
.mod-menu {
  .menu-list__input,
  .icon-list__input {
    > .el-input__inner {
      cursor: pointer;
    }
  }
  &__icon-popover {
    width: 458px;
    overflow: hidden;
  }
  &__icon-inner {
    width: 478px;
    max-height: 258px;
    overflow-x: hidden;
    overflow-y: auto;
  }
  &__icon-list {
    width: 458px;
    padding: 0;
    margin: -8px 0 0 -8px;
    > .el-button {
      padding: 8px;
      margin: 8px 0 0 8px;
      > span {
        display: inline-block;
        vertical-align: middle;
        width: 18px;
        height: 18px;
        font-size: 18px;
      }
    }
  }
  .icon-list__tips {
    font-size: 18px;
    text-align: center;
    color: #e6a23c;
    cursor: pointer;
  }
}
</style>
