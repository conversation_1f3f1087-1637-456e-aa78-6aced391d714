<template>
  <div class="mod-role">
    <el-form :inline="true" ref="dataForm" :model="dataForm" @keyup.enter.native="getDataList()">
      <el-form-item label="大屏数据维度" prop="dimension">
        <el-dict code="BIG_SCREEN_DATA_DIMENSION" v-model="dataForm.dimension"></el-dict>
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="dataForm.status" placeholder="请选择" clearable filterable>
          <el-option
              v-for="item in [{code: true, name: '启用'}, {code: false, name: '禁用'}]"
              :key="item.code"
              :label="item.name"
              :value="item.code">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="warning" icon="el-icon-search" @click="getDataList()">查询</el-button>
        <el-button icon="el-icon-refresh-left" @click="resetForm()">重置</el-button>
      </el-form-item>
      <div class="f-s-c" style="padding: 15px 0;display: flex; justify-content: flex-end">
        <div>
          <el-button icon="el-icon-plus" type="primary" @click="addOrUpdateHandle()">新增</el-button>
          <el-button icon="el-icon-delete" type="danger" @click="deleteHandle()" :disabled="dataListSelections.length <= 0">批量删除</el-button>
        </div>
      </div>
    </el-form>
    <el-table
        :data="dataList"
        border
        v-loading="dataListLoading"
        @selection-change="selectionChangeHandle"
        style="width: 100%;">
      <el-table-column
          type="selection"
          header-align="center"
          align="center"
          width="50">
      </el-table-column>
      <el-table-column
          prop="dimensionName"
          header-align="center"
          align="center"
          label="大屏数据维度">
      </el-table-column>
      <el-table-column
          prop="orgName"
          header-align="center"
          align="center"
          label="统计组织">
      </el-table-column>
      <el-table-column
          prop="type"
          header-align="center"
          align="center"
          label="统计类型">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.type === 'total'" size="small" type="success">累计</el-tag>
          <el-tag v-if="scope.row.type === 'this_month'" size="small" type="primary">本月</el-tag>
          <el-tag v-if="scope.row.type === 'this_year'" size="small" type="warning">本年</el-tag>
          <span v-else></span>
        </template>
      </el-table-column>
      <el-table-column
          prop="status"
          header-align="center"
          align="center"
          label="状态">
        <template slot-scope="scope">
          <el-switch
              size="mini"
              active-color="#13ce66"
              inactive-color="#ff4949"
              v-model="scope.row.status"
              @change="statusHandle(scope.row.id, scope.row)">
          </el-switch>
        </template>
      </el-table-column>
      <el-table-column
          fixed="right"
          header-align="center"
          align="center"
          width="150"
          label="操作">
        <template slot-scope="scope">
          <el-button type="text" size="small" @click="addOrUpdateHandle(scope.row.id, scope.row.dimension)" class="btn-control">修改</el-button>
          <el-button type="text" size="small" @click="deleteHandle(scope.row.id)" class="btn-control">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
        @size-change="sizeChangeHandle"
        @current-change="currentChangeHandle"
        :current-page="pageIndex"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pageSize"
        :total="totalPage"
        layout="total, sizes, prev, pager, next, jumper">
    </el-pagination>
    <data-creation-add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"/>
  </div>
</template>

<script>
import ListMixins from "@/mixins/list-mixins";
import DataCreationAddOrUpdate from "./data-creation-add-or-update";

export default {
  name: "data_creation",
  components: {DataCreationAddOrUpdate},
  mixins: [ListMixins],
  data () {
    return {
      mixinOptions: {
        createdLoad: true,
        dataUrl: '/admin/big_screen_data/pages',
        deleteUrl: '/admin/big_screen_data/removeByIds'
      },
      dataForm: {
        dimension: null,
        status: null
      }
    }
  },
  methods: {
    resetForm () {
      this.$refs['dataForm']?.resetFields()
      this.$nextTick(() => {
        this.getDataList()
      })
    },
    statusHandle(id, row) {
      this.$http({
        url: this.$http.adornUrl('/admin/big_screen_data/statusChange'),
        method: 'get',
        params: this.$http.adornParams({
          'id': id
        })
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.$message.success("操作成功")
          this.query()
        } else {
          row.status = !row.status
          this.$message.error(data.msg)
        }
      }).catch(() => {
        row.status = !row.status
      })
    },
    addOrUpdateHandle (id, dimension) {
      this.addOrUpdateVisible = true
      this.$nextTick(() => {
        this.$refs.addOrUpdate.init(id, dimension)
      })
    }
  }
}
</script>

<style scoped>

</style>
