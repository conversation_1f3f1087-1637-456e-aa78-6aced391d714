<template>
  <div class="mod-role">
    <el-form :inline="true" ref="dataForm" :model="dataForm" @keyup.enter.native="getDataList()">
      <el-form-item label="文明单位名称" prop="name">
        <el-input v-model="dataForm.name" placeholder="请输入" clearable></el-input>
      </el-form-item>
<!--      <el-form-item label="类型" prop="categoryIds">-->
<!--        <el-cascader-->
<!--            placeholder="请选择"-->
<!--            v-model="dataForm.categoryIds"-->
<!--            :options="categories"-->
<!--            @change="(value) => {handleChange(value, 'category')}"-->
<!--            clearable-->
<!--            :props="{checkStrictly: true}"-->
<!--        ></el-cascader>-->
<!--      </el-form-item>-->
      <el-form-item label="状态" prop="status">
        <el-select v-model="dataForm.status" placeholder="请选择" clearable>
          <el-option
              v-for="item in [{label: '启用', value: true}, {label: '禁用', value: false}]"
              :key="item.value"
              :label="item.label"
              :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="warning" icon="el-icon-search" @click="getDataList()">查询</el-button>
        <el-button icon="el-icon-refresh-left" @click="resetForm()">重置</el-button>
      </el-form-item>
      <div class="f-s-c" style="padding: 15px 0;display: flex; justify-content: flex-end">
        <div>
          <el-button icon="el-icon-plus" type="primary" @click="addOrUpdateHandle()">新增</el-button>
          <el-button icon="el-icon-delete" type="danger" @click="deleteHandle()" :disabled="dataListSelections.length <= 0">批量删除</el-button>
        </div>
      </div>
    </el-form>
    <el-table
        :data="dataList"
        border
        v-loading="dataListLoading"
        @selection-change="selectionChangeHandle"
        style="width: 100%;">
      <el-table-column
          type="selection"
          header-align="center"
          align="center"
          width="50">
      </el-table-column>
      <el-table-column
          prop="name"
          header-align="center"
          align="center"
          width="300"
          :show-overflow-tooltip="true"
          label="名称">
        <template slot-scope="scope">
          <a style="cursor: pointer" @click="detail(scope.row.id)">{{ scope.row.name }}</a>
        </template>
      </el-table-column>
      <el-table-column
          prop="sequence"
          header-align="center"
          align="center"
          label="排序">
      </el-table-column>
      <el-table-column
          prop="effectiveTime"
          header-align="center"
          align="center"
          label="生效时间">
      </el-table-column>
      <el-table-column
          prop="status"
          header-align="center"
          align="center"
          label="状态">
        <template slot-scope="scope">
          <el-switch
              size="mini"
              active-color="#13ce66"
              inactive-color="#ff4949"
              v-model="scope.row.status"
              @change="statusHandle(scope.row.id, scope.row)">
          </el-switch>
<!--          <el-tag v-if="scope.row.grounding === true" type="success">已上架</el-tag>-->
<!--          <el-tag v-else type="warning">未上架</el-tag>-->
        </template>
      </el-table-column>
      <el-table-column
          fixed="right"
          header-align="center"
          align="center"
          width="140"
          label="操作">
        <template slot-scope="scope">
          <el-button type="text" size="small" @click="addOrUpdateHandle(scope.row.id)" class="btn-control">修改</el-button>
          <el-button type="text" size="small" @click="deleteHandle(scope.row.id)" class="btn-control">删除</el-button>
<!--          <el-button v-if="isAuth('mien:list:ground')" type="text" size="small" @click="groundHandle(scope.row.id)" class="btn-control">{{scope.row.grounding ? '下架' : '上架'}}</el-button>-->
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
        @size-change="sizeChangeHandle"
        @current-change="currentChangeHandle"
        :current-page="pageIndex"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pageSize"
        :total="totalPage"
        layout="total, sizes, prev, pager, next, jumper">
    </el-pagination>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"></add-or-update>
  </div>
</template>

<script>
import AddOrUpdate from './civilization-enterprise-add-or-update'
import listMixin from '@/mixins/list-mixins'
export default {
  mixins: [listMixin],
  data () {
    return {
      mixinOptions: {
        dataUrl: '/admin/civilization_enterprise/pages',
        deleteUrl: '/admin/civilization_enterprise/removeByIds'
      },
      dataForm: {
        name: '',
        status: null,
        orders: [{column: 'sequence', sort: 'asc'}]
      }
    }
  },
  components: {
    AddOrUpdate
  },
  activated () {
    this.getDataList()
  },
  methods: {
    resetForm () {
      this.$refs['dataForm']?.resetFields()
      this.getDataList()
    },
    statusHandle(id, row) {
      this.$http({
        url: this.$http.adornUrl('/admin/civilization_enterprise/statusChange'),
        method: 'get',
        params: this.$http.adornParams({
          'id': id
        })
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.$message.success("操作成功")
          this.query()
        } else {
          row.status = !row.status
          this.$message.error(data.msg)
        }
      }).catch(() => {
        row.status = !row.status
      })
    },
    detail (id) {
      this.addOrUpdateVisible = true
      this.$nextTick(() => {
        this.$refs.addOrUpdate.init(id, true)
      })
    }
    // handleChange(value, type) {
    //   if (type === 'category') {
    //     this.dataForm.categoryId = value[value.length - 1]
    //   }
    // }
  }
}
</script>
