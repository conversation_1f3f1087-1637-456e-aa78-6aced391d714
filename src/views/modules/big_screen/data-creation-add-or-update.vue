<template>
  <div>
    <el-dialog
        :title="!dataForm.id ? '新增' : '修改'"
        :close-on-click-modal="false"
        :visible.sync="visible" width="80%">
      <el-tabs :key="tabKey" v-model="dataForm.dimension" :before-leave="tabChange" stretch style="margin-bottom: 20px">
        <el-tab-pane :disabled="dataForm.id && dataForm.id !== ''"
                     v-for="(item, index) in dimensionTree"
                     :key="index"
                     :label="item.name"
                     :name="item.code"
                     :lazy="true">
          <h3 style="width: 90px; text-align: center">维度数据：</h3>
          <el-form :model="dimensionForm" ref="dimensionForm" label-width="140px">
            <el-row>
              <el-col :span="8" v-for="(it, idx) in item.children" :key="idx">
                <el-form-item :label="it.name + '：'" :prop="it.code.substr(it.code.lastIndexOf('_') + 1)">
                  <el-input-number
                      v-model="dimensionForm[`${it.code.substr(it.code.lastIndexOf('_') + 1)}`]"
                      placeholder="请输入"
                      :min="0"
                      controls-position="right"
                      :precision="it.code.substr(it.code.lastIndexOf('_') + 1) === 'serviceDuration' || it.code.substr(it.code.lastIndexOf('_') + 1) === 'projectMaterialTotalAmount' ? 2 : 0"
                      :step="it.code.substr(it.code.lastIndexOf('_') + 1) === 'serviceDuration' || it.code.substr(it.code.lastIndexOf('_') + 1) === 'projectMaterialTotalAmount' ? 0.25 : 1"/>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
          <h3 style="width: 90px; text-align: center">数据属性：</h3>
          <el-form :model="dataForm" ref="dataForm" :rules="dataRule" label-width="140px">
            <el-row>
              <el-col :span="8">
                <el-form-item v-if="dataForm.dimension === 'BSDD_OVERVIEW'" label="组织：" prop="org">
                  <el-select style="width: 250px" v-model="dataForm.org" placeholder="请选择" clearable>
                    <el-option
                        v-for="item in orgList"
                        :key="item.code"
                        :label="item.name"
                        :value="item.code">
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item v-if="dataForm.dimension !== 'BSDD_ORG_SUM'" label="统计类型：" prop="type">
                  <el-select style="width: 250px" v-model="dataForm.type" placeholder="请选择" clearable>
                    <el-option
                        v-for="item in [{code: 'total', name: '累计'}, {code: 'this_month', name: '本月'}, {code: 'this_year', name: '本年'}]"
                        :key="item.code"
                        :label="item.name"
                        :value="item.code">
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="状态：" prop="status">
                  <el-radio-group v-model="dataForm.status">
                    <el-radio :label="true">启用</el-radio>
                    <el-radio :label="false">禁用</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </el-tab-pane>
      </el-tabs>
      <span slot="footer" class="dialog-footer">
        <el-button @click="visible = false">取消</el-button>
        <el-button type="primary" @click="dataFormSubmit()" :disabled="dataFormLoading">确定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import {uuid} from "@/utils";

export default {
  data () {
    return {
      visible: false,
      dimensionTree: [],
      orgList: [],
      tabKey: uuid(),
      dataForm: {
        id: null,
        dimension: 'BSDD_OVERVIEW',
        org: null,
        type: null,
        status: true,
        data: null
      },
      dimensionForm: {},
      dataRule: {
        org: [
          {required: true, message: '组织不能为空', trigger: 'blur'}
        ],
        type: [
          {required: true, message: '统计类型不能为空', trigger: 'blur'}
        ],
        status: [
          {required: true, message: '状态不能为空', trigger: 'change'}
        ]
      },
      dataFormLoading: false
    }
  },
  methods: {
    async init (id, dimension) {
      this.dataForm.id = id || null
      this.dimensionForm = {}
      this.tabKey = uuid()
      this.dataForm.dimension = dimension || 'BSDD_OVERVIEW'
      await this.getOrgList()
      await this.getDimensionDictTree()
      this.visible = true
      this.$nextTick(() => {
        this.dataForm.org = null
        this.dataForm.type = null
        this.dataForm.status = true
        if (this.dataForm.id) {
          this.$http({
            url: this.$http.adornUrl('/admin/big_screen_data'),
            method: 'get',
            params: this.$http.adornParams({
              'id': this.dataForm.id
            })
          }).then(({data}) => {
            if (data && data.code === 0) {
              this.dataForm = data.obj
              this.dimensionForm = data.obj.data
            }
          })
        } else {
          this.dataForm.createDate = null
          this.dataForm.creator = null
          this.dataForm.version = 0
        }
      })
    },
    async getDimensionDictTree() {
      await this.$http({
        url: this.$http.adornUrl('/admin/dict/tree'),
        method: 'get',
        params: this.$http.adornParams({
          'parentCode': 'BIG_SCREEN_DATA_DIMENSION',
          'includeRoot': false
        })
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.dimensionTree = data.obj
        } else {
          this.dimensionTree = []
        }
      })
    },
    async tabChange (active, oldActive) {
      await this.$confirm(`切换维度将清空当前维护数据，确定切换?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        closeOnClickModal: false
      }).then(({ value }) => {
        this.dimensionForm = {}
        return true
      }).catch(() => {
        return reject()
      })
    },
    async getOrgList() {
      await this.$http({
        url: this.$http.adornUrl('/admin/org/getFiveAreaSubAssociationOrg'),
        method: 'get',
        params: this.$http.adornParams({
          'withTop': true,
          'all': false
        })
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.orgList = data.obj
        } else {
          this.orgList = []
        }
      })
    },
    dataFormSubmit() {
      this.$refs['dataForm'][0].validate((valid) => {
        if (valid) {
          this.$confirm(`启用状态下大屏数据将优先展示您维护的数据，确定保存?`, '提示',
              {confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning', closeOnClickModal: false}
          ).then(() => {
            this.dataFormLoading = true
            this.$set(this.dataForm, 'data', this.dimensionForm)
            this.$http({
              url: this.$http.adornUrl(`/admin/big_screen_data/${!this.dataForm.id ? 'save' : 'update'}`),
              method: 'post',
              data: this.$http.adornData(this.dataForm)
            }).then(({data}) => {
              if (data && data.code === 0) {
                this.$message({
                  message: '操作成功',
                  type: 'success',
                  duration: 1500,
                  onClose: () => {
                    this.dataFormLoading = false
                    this.visible = false
                    this.$emit('refreshDataList')
                  }
                })
              } else {
                this.$message.error(data.msg)
              }
              this.dataFormLoading = false
            })
          })
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
