<template>
  <div class="mod-list">
    <el-form :inline="true" :model="dataForm" ref="dataForm" @keyup.enter.native="getDataList()">
      <el-form-item prop="regionCode" label="区划编号">
        <el-input v-model="dataForm.regionCode" placeholder="请输入区划编号" clearable></el-input>
      </el-form-item>
      <el-form-item prop="regionName" label="区划名称">
        <el-input v-model="dataForm.regionName" placeholder="请输入区划名称" clearable></el-input>
      </el-form-item>
      <el-form-item prop="parentCode" label="父级区划编号">
        <el-input v-model="dataForm.parentCode" placeholder="请输入父级区划编号" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-search" type="warning" @click="getDataList()">查询</el-button>
        <el-button @click="resetForm()" icon="el-icon-refresh-left">重置</el-button>
      </el-form-item>
    </el-form>
    <div class="f-s-c" style="padding: 15px 0; display: flex; justify-content: space-between; align-items: center">
      <div>
        <el-button type="success" @click="showTreeDialog()" icon="el-icon-s-data">树形结构</el-button>
      </div>
      <div>
        <el-button type="primary" @click="syncConfirm()" icon="el-icon-refresh" :disabled="syncLoading" :loading="syncLoading">同步</el-button>
      </div>
    </div>
    <el-table :data="dataList" border v-loading="dataListLoading" @selection-change="selectionChangeHandle" style="width: 100%;">
<!--      <el-table-column type="selection" header-align="center" align="center" width="50"/>-->
      <el-table-column prop="regionCode" header-align="center" align="center" label="区划编号"/>
      <el-table-column prop="parentCode" header-align="center" align="center" label="父级区划编号"/>
      <el-table-column prop="regionName" header-align="center" align="center" label="区划名称"/>
      <el-table-column prop="updateDate" header-align="center" align="center" label="更新时间"/>
      <!-- 操作列已隐藏 -->
    </el-table>
    <el-pagination @size-change="sizeChangeHandle" @current-change="currentChangeHandle" :current-page="pageIndex"
                   :page-sizes="[10, 20, 50, 100]" :page-size="pageSize" :total="totalPage" layout="total, sizes, prev, pager, next, jumper"/>

    <!-- 树形结构弹窗 -->
    <el-dialog title="区划树形结构" :visible.sync="treeDialogVisible" width="60%" :close-on-click-modal="false">
      <el-tree
        ref="tree"
        :data="treeData"
        :props="treeProps"
        node-key="id"
        default-expand-all
        highlight-current
        v-loading="treeLoading">
        <span class="custom-tree-node" slot-scope="{ node, data }">
          <span class="node-label">{{ data.regionName }}</span>
          <span class="node-code">({{ data.regionCode }})</span>
        </span>
      </el-tree>
      <div slot="footer" class="dialog-footer">
        <el-button @click="treeDialogVisible = false">关闭</el-button>
      </div>
    </el-dialog>

    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"/>
  </div>
</template>

<script>
  import AddOrUpdate from './region-add-or-update'
  import listMixin from '@/mixins/list-mixins'

  export default {
    mixins: [listMixin],
    data () {
      return {
        mixinOptions: {
          dataUrl: '/admin/sgb/region/pages',
          deleteUrl: '/admin/sgb/region/removeByIds'
        },
        dataForm: {
          regionCode: null,
          regionName: null,
          parentCode: null,
          orders: [
            {column: 'parentCode', sort: 'asc'},
            {column: 'regionCode', sort: 'asc'}
          ]
        },
        syncLoading: false,
        treeDialogVisible: false,
        treeData: [],
        treeLoading: false,
        treeProps: {
          children: 'children',
          label: 'regionName'
        }
      }
    },
    components: {
      AddOrUpdate
    },
    methods: {
      // 显示树形结构弹窗
      showTreeDialog() {
        this.treeDialogVisible = true
        this.getTreeData()
      },

      // 获取树形数据
      getTreeData() {
        this.treeLoading = true
        this.$http({
          url: this.$http.adornUrl('/admin/sgb/region/tree'),
          method: 'get',
          params: this.$http.adornParams()
        }).then(({data}) => {
          this.treeLoading = false
          if (data && data.code === 0) {
            this.treeData = data.obj || []
          } else {
            this.$message.error(data.msg || '获取树形数据失败')
          }
        }).catch(() => {
          this.treeLoading = false
          this.$message.error('获取树形数据失败')
        })
      },

      // 重置表单
      resetForm() {
        this.$refs['dataForm']?.resetFields()
        this.getDataList()
      },

      // 同步确认
      syncConfirm() {
        this.$confirm('确定同步区划数据吗?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.syncHandle()
        }).catch(() => {
          this.$message.info('已取消同步')
        })
      },

      // 同步处理
      syncHandle() {
        this.syncLoading = true
        this.dataListLoading = true
        this.$http({
          url: this.$http.adornUrl('/admin/sgb/region/sync'),
          method: 'post',
          data: this.$http.adornData({})
        }).then(({data}) => {
          this.syncLoading = false
          this.dataListLoading = false
          if (data && data.code === 0) {
            this.$message.success('同步成功')
            this.getDataList()
          } else {
            this.$message.error(data.msg || '同步失败')
          }
        }).catch(() => {
          this.syncLoading = false
          this.dataListLoading = false
          this.$message.error('同步失败')
        })
      }
    }
  }
</script>

<style lang="scss" scoped>
.custom-tree-node {
  display: inline-flex;
  align-items: center;
  width: 100%;
}

.node-label {
  font-size: 14px;
  color: #303133;
  font-weight: 500;
  margin-right: 8px;
}

.node-code {
  font-size: 12px;
  color: #909399;
  font-weight: normal;
}

.el-tree {
  background: #fff;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
}

.el-tree-node__content {
  height: 36px;
  line-height: 36px;
  padding-left: 10px;
  border-bottom: 1px solid #f5f7fa;
}

.el-tree-node__content:hover {
  background-color: #f5f7fa;
}

.el-tree-node__expand-icon {
  color: #c0c4cc;
  font-size: 12px;
  margin-right: 6px;
}

.el-tree-node__expand-icon.expanded {
  color: #409eff;
}

// 不同层级的缩进和样式
.el-tree-node[data-level="1"] > .el-tree-node__content {
  background-color: #fafafa;
  font-weight: 600;
}

.el-tree-node[data-level="2"] > .el-tree-node__content {
  background-color: #fcfcfc;
}

// 叶子节点样式
.el-tree-node.is-leaf > .el-tree-node__content {
  .node-label {
    color: #606266;
    font-weight: normal;
  }
}

// 根节点样式
.el-tree > .el-tree-node:first-child > .el-tree-node__content {
  border-top: none;
}

// 最后一个节点去掉下边框
.el-tree-node:last-child > .el-tree-node__content {
  border-bottom: none;
}
</style>
