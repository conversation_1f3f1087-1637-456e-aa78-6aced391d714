<template>
  <el-dialog class="common-dialog" :title="!dataForm.id ? '新增' : '修改'" :visible.sync="visible"
             :close-on-click-modal="false" :modal-append-to-body="false" width="60%">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmit()" label-width="90px">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="类型编号" prop="typeId">
            <el-input v-model="dataForm.typeId" placeholder="请输入" clearable/>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="父级类型编号" prop="fatherId">
            <el-input v-model="dataForm.fatherId" placeholder="请输入" clearable/>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="类型名称" prop="typeName">
            <el-input v-model="dataForm.typeName" placeholder="请输入" clearable/>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="节点路径" prop="nodePath">
            <el-input v-model="dataForm.nodePath" placeholder="请输入" clearable/>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()" :disabled="dataFormLoading">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
  import _ from 'lodash'
  import editMixin from '@/mixins/edit-mixins'
  export default {
    mixins: [editMixin],
    data () {
      return {
        editOptions: {
          initUrl: '/admin/sgb/service-type'
        },
        dataForm: {},
        initForm: {
          id: null,
          version: null,
          typeId: null,
          fatherId: null,
          typeName: null,
          nodePath: null
        },
        dataRule: {
          typeId: [
            { required: true, message: '类型编号不能为空', trigger: 'change' }
          ],
          fatherId: [
            { required: true, message: '父级类型编号不能为空', trigger: 'change' }
          ],
          typeName: [
            { required: true, message: '类型名称不能为空', trigger: 'change' }
          ],
          nodePath: [
            { required: true, message: '节点路径不能为空', trigger: 'change' }
          ]
        }
      }
    },
    methods: {
      init (id) {
        this.dataForm = _.cloneDeep(this.initForm)
        this.dataForm.id = id || null
        this.visible = true
        this.$nextTick(() => {
          this.$refs['dataForm']?.resetFields()
          if (this.dataForm.id) {
            this.getData()
          }
        })
      }
    }
  }
</script>

<style lang="scss" scoped>
</style>
