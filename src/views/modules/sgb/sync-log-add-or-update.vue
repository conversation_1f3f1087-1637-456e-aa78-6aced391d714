<template>
  <el-dialog class="common-dialog" title="同步日志详情" :visible.sync="visible"
             :close-on-click-modal="false" :modal-append-to-body="false" width="80%">
    <el-form :model="dataForm" ref="dataForm" label-width="150px">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="mq消息id（幂等）">
            <el-input v-model="dataForm.syncBillId" disabled/>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="同步业务类型">
            <el-input v-model="dataForm.bizType" disabled/>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="业务表id">
            <el-input v-model="dataForm.bizId" disabled/>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="同步对象">
            <el-input v-model="dataForm.syncObjectName" disabled/>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="是否成功">
            <el-input v-model="dataForm.resultCode" disabled/>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="市平台返回的消息">
            <el-input v-model="dataForm.message" disabled/>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="同步时间">
            <el-input v-model="dataForm.syncTime" disabled/>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="业务编码">
            <el-input v-model="dataForm.spCode" disabled/>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="请求体">
            <div class="json-container">
              <json-viewer
                :value="requestParamJson"
                :expand-depth="3"
                copyable
                boxed
                sort/>
            </div>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="响应">
            <div class="json-container">
              <json-viewer
                :value="responseJson"
                :expand-depth="3"
                copyable
                boxed
                sort/>
            </div>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">关闭</el-button>
    </span>
  </el-dialog>
</template>

<script>
  import moment from 'moment'
  import _ from 'lodash'
  import JsonViewer from 'vue-json-viewer'

  export default {
    data () {
      return {
        visible: false,
        dataForm: {},
        initForm: {
          id: null,
          version: null,
          syncBillId: null,
          bizType: null,
          bizId: null,
          syncObjectName: null,
          resultCode: null,
          message: null,
          syncTime: null,
          spCode: null,
          requestParam: null,
          response: null
        }
      }
    },
    computed: {
      requestParamJson() {
        if (!this.dataForm.requestParam) return null
        try {
          return JSON.parse(this.dataForm.requestParam)
        } catch (e) {
          return this.dataForm.requestParam
        }
      },
      responseJson() {
        if (!this.dataForm.response) return null
        try {
          return JSON.parse(this.dataForm.response)
        } catch (e) {
          return this.dataForm.response
        }
      }
    },
    components: {
      moment,
      JsonViewer
    },
    methods: {
      init (id) {
        this.dataForm = _.cloneDeep(this.initForm)
        this.dataForm.id = id || null
        this.visible = true
        this.$nextTick(() => {
          if (this.dataForm.id) {
            this.getData()
          }
        })
      },
      // 获取数据详情
      getData() {
        this.$http({
          url: this.$http.adornUrl(`/admin/sgb/sync-log/${this.dataForm.id}`),
          method: 'get'
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.dataForm = data.obj
          } else {
            this.$message.error(data.msg || '获取数据失败')
          }
        }).catch(() => {
          this.$message.error('获取数据失败')
        })
      }
    }
  }
</script>

<style lang="scss" scoped>
.json-container {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  max-height: 400px;
  overflow: auto;
}

::v-deep .jv-container {
  font-size: 13px;
}

::v-deep .jv-code {
  padding: 10px;
}
</style>
