<template>
  <el-dialog class="common-dialog" :title="!dataForm.id ? '新增' : '修改'" :visible.sync="visible"
             :close-on-click-modal="false" :modal-append-to-body="false" width="60%">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmit()" label-width="90px">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="mq消息id（幂等）" prop="syncBillId">
            <el-input v-model="dataForm.syncBillId" placeholder="请输入" clearable/>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="同步业务类型（志愿者/团队/加入团队/活动...）" prop="bizType">
            <el-input v-model="dataForm.bizType" placeholder="请输入" clearable/>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="业务表id" prop="bizId">
            <el-input v-model="dataForm.bizId" placeholder="请输入" clearable/>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="同步对象" prop="syncObjectName">
            <el-input v-model="dataForm.syncObjectName" placeholder="请输入" clearable/>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="是否成功" prop="resultCode">
            <el-input v-model="dataForm.resultCode" placeholder="请输入" clearable/>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="市平台返回的消息" prop="message">
            <el-input v-model="dataForm.message" placeholder="请输入" clearable/>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="同步时间" prop="syncTime">
            <el-date-picker
              v-model="dataForm.syncTime"
              value-format="yyyy-MM-dd HH:mm:ss"
              type="datetime"
              placeholder="请选择"
              clearable>
            </el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="业务编码" prop="spCode">
            <el-input v-model="dataForm.spCode" placeholder="请输入" clearable/>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="请求体" prop="requestParam">
            <el-input v-model="dataForm.requestParam" placeholder="请输入" clearable/>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="响应" prop="response">
            <el-input v-model="dataForm.response" placeholder="请输入" clearable/>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()" :disabled="dataFormLoading">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
  import moment from 'moment'
  import _ from 'lodash'
  import editMixin from '@/mixins/edit-mixins'
  export default {
    mixins: [editMixin],
    data () {
      return {
        editOptions: {
          initUrl: '/admin/sgb/sync-log'
        },
        dataForm: {},
        initForm: {
          id: null,
          version: null,
          syncBillId: null,
          bizType: null,
          bizId: null,
          syncObjectName: null,
          resultCode: null,
          message: null,
          syncTime: null,
          spCode: null,
          requestParam: null,
          response: null
        },
        dataRule: {
          syncBillId: [
            { required: true, message: 'mq消息id（幂等）不能为空', trigger: 'change' }
          ],
          bizType: [
            { required: true, message: '同步业务类型（志愿者/团队/加入团队/活动...）不能为空', trigger: 'change' }
          ],
          bizId: [
            { required: true, message: '业务表id不能为空', trigger: 'change' }
          ],
          syncObjectName: [
            { required: true, message: '同步对象不能为空', trigger: 'change' }
          ],
          resultCode: [
            { required: true, message: '是否成功不能为空', trigger: 'change' }
          ],
          message: [
            { required: true, message: '市平台返回的消息不能为空', trigger: 'change' }
          ],
          syncTime: [
            { required: true, message: '同步时间不能为空', trigger: 'change' }
          ],
          spCode: [
            { required: true, message: '业务编码不能为空', trigger: 'change' }
          ],
          requestParam: [
            { required: true, message: '请求体不能为空', trigger: 'change' }
          ],
          response: [
            { required: true, message: '响应不能为空', trigger: 'change' }
          ]
        }
      }
    },
    components: {
        moment
    },
    methods: {
      init (id) {
        this.dataForm = _.cloneDeep(this.initForm)
        this.dataForm.id = id || null
        this.visible = true
        this.$nextTick(() => {
          this.$refs['dataForm']?.resetFields()
          if (this.dataForm.id) {
            this.getData()
          }
        })
      }
    }
  }
</script>

<style lang="scss" scoped>
</style>
