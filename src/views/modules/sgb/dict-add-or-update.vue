<template>
  <el-dialog class="common-dialog" :title="!dataForm.id ? '新增' : '修改'" :visible.sync="visible"
             :close-on-click-modal="false" :modal-append-to-body="false" width="60%">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmit()" label-width="90px">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="字典值" prop="value">
            <el-input v-model="dataForm.value" placeholder="请输入" clearable/>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="字典标签" prop="label">
            <el-input v-model="dataForm.label" placeholder="请输入" clearable/>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="字典类型" prop="type">
            <el-input v-model="dataForm.type" placeholder="请输入" clearable/>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="字典描述" prop="describe">
            <el-input v-model="dataForm.describe" placeholder="请输入" clearable/>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()" :disabled="dataFormLoading">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
  import _ from 'lodash'
  import editMixin from '@/mixins/edit-mixins'
  export default {
    mixins: [editMixin],
    data () {
      return {
        editOptions: {
          initUrl: '/admin/sgb/dict'
        },
        dataForm: {},
        initForm: {
          id: null,
          version: null,
          value: null,
          label: null,
          type: null,
          describe: null
        },
        dataRule: {
          value: [
            { required: true, message: '字典值不能为空', trigger: 'change' }
          ],
          label: [
            { required: true, message: '字典标签不能为空', trigger: 'change' }
          ],
          type: [
            { required: true, message: '字典类型不能为空', trigger: 'change' }
          ],
          describe: [
            { required: true, message: '字典描述不能为空', trigger: 'change' }
          ]
        }
      }
    },
    methods: {
      init (id) {
        this.dataForm = _.cloneDeep(this.initForm)
        this.dataForm.id = id || null
        this.visible = true
        this.$nextTick(() => {
          this.$refs['dataForm']?.resetFields()
          if (this.dataForm.id) {
            this.getData()
          }
        })
      }
    }
  }
</script>

<style lang="scss" scoped>
</style>
