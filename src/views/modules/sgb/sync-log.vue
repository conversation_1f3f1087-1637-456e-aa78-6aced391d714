<template>
  <div class="mod-list">
    <el-form :inline="true" :model="dataForm" ref="dataForm" @keyup.enter.native="getDataList()">
      <el-form-item prop="bizType" label="业务类型">
        <el-dict v-model="dataForm.bizType" :code="'sgb_sync_biz_type'" placeholder="请选择业务类型" clearable></el-dict>
      </el-form-item>
      <el-form-item prop="bizId" label="业务表ID">
        <el-input v-model="dataForm.bizId" placeholder="请输入业务表ID" clearable></el-input>
      </el-form-item>
      <el-form-item prop="message" label="返回消息">
        <el-input v-model="dataForm.message" placeholder="请输入返回消息" clearable></el-input>
      </el-form-item>
      <el-form-item prop="resultCode" label="返回CODE">
        <el-input v-model="dataForm.resultCode" placeholder="请输入返回CODE" clearable></el-input>
      </el-form-item>
      <el-form-item prop="syncTime" label="同步时间">
        <el-date-picker
          v-model="dataForm.syncTimeRange"
          type="datetimerange"
          value-format="yyyy-MM-dd HH:mm:ss"
          range-separator="-"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
          :default-time="['00:00:00', '23:59:59']"
          clearable>
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-search" type="warning" @click="getDataList()">查询</el-button>
        <el-button @click="resetForm()" icon="el-icon-refresh-left">重置</el-button>
      </el-form-item>
    </el-form>
    <el-table
        :data="dataList"
        border
        v-loading="dataListLoading"
        @selection-change="selectionChangeHandle"
        style="width: 100%;"
    >
      <!--      <el-table-column type="selection" header-align="center" align="center" width="50"/>-->
      <el-table-column prop="syncBillId" header-align="center" align="center" label="mq消息id"/>
      <el-table-column prop="bizTypeText" header-align="center" align="center" label="同步业务类型"/>
      <el-table-column prop="bizId" header-align="center" align="center" label="业务表id"/>
      <el-table-column prop="syncObjectName" min-width="50px" header-align="center" align="center" label="同步对象"/>
      <el-table-column prop="resultCode" min-width="55px" header-align="center" align="center" label="返回CODE"/>
      <el-table-column prop="message" header-align="center" align="center" label="市平台返回的消息"/>
      <el-table-column prop="syncTime" header-align="center" align="center" label="同步时间">
        <template slot-scope="scope">
          {{ scope.row.syncTime && scope.row.syncTime.length > 16 ? scope.row.syncTime.substr(0, 16) : scope.row.syncTime }}
        </template>
      </el-table-column>
      <el-table-column prop="requestParam" min-width="200px" header-align="center" align="left" label="请求体">
        <template slot-scope="scope">
          <div
              class="json-preview"
              @click="showJsonDialog(scope.row.requestParam, '请求体')"
              :title="scope.row.requestParam">
            {{ formatJsonPreview(scope.row.requestParam) }}
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="response" min-width="200px" header-align="center" align="left" label="响应">
        <template slot-scope="scope">
          <div
              class="json-preview"
              @click="showJsonDialog(scope.row.response, '响应')"
              :title="scope.row.response">
            {{ formatJsonPreview(scope.row.response) }}
          </div>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination @size-change="sizeChangeHandle" @current-change="currentChangeHandle" :current-page="pageIndex"
                   :page-sizes="[10, 20, 50, 100]" :page-size="pageSize" :total="totalPage" layout="total, sizes, prev, pager, next, jumper"/>
    <!-- JSON展示对话框 -->
    <el-dialog
      :title="jsonDialogTitle"
      :visible.sync="jsonDialogVisible"
      width="60%">
      <json-viewer
        :value="jsonDialogData"
        :expand-depth="3"
        copyable
        boxed
        sort/>
    </el-dialog>
  </div>
</template>

<script>
  import listMixin from '@/mixins/list-mixins'
  import JsonViewer from 'vue-json-viewer'

  export default {
    mixins: [listMixin],
    data () {
      return {
        mixinOptions: {
          dataUrl: '/admin/sgb/sync-log/pages',
          deleteUrl: '/admin/sgb/sync-log/removeByIds'
        },
        dataForm: {
          bizType: null,
          bizId: null,
          message: null,
          resultCode: null,
          syncTimeRange: null,
          syncTimeStart: null,
          syncTimeEnd: null
        },
        jsonDialogVisible: false,
        jsonDialogTitle: '',
        jsonDialogData: null
      }
    },
    components: {
      JsonViewer
    },
    methods: {
      // 查询前处理时间范围参数
      queryBeforeHandle() {
        // 处理时间范围参数
        if (this.dataForm.syncTimeRange && this.dataForm.syncTimeRange.length === 2) {
          this.dataForm.syncTimeStart = this.dataForm.syncTimeRange[0]
          this.dataForm.syncTimeEnd = this.dataForm.syncTimeRange[1]
        } else {
          this.dataForm.syncTimeStart = null
          this.dataForm.syncTimeEnd = null
        }
      },
      resetForm() {
        this.$refs['dataForm']?.resetFields()
        this.dataForm = {
          bizType: null,
          bizId: null,
          message: null,
          resultCode: null,
          syncTimeRange: null,
          syncTimeStart: null,
          syncTimeEnd: null
        }
        this.getDataList()
      },
      // 格式化JSON预览，限制为5行
      formatJsonPreview(jsonStr) {
        if (!jsonStr || typeof jsonStr !== 'string') return ''

        const MAX_LINES = 5
        const MAX_LENGTH = 200 // 单行最大长度

        let content = jsonStr.trim()

        // 尝试格式化JSON
        try {
          const parsed = JSON.parse(content)
          content = JSON.stringify(parsed, null, 2)
        } catch (e) {
          // 如果不是有效JSON，保持原样
        }

        // 按行分割并限制行数
        const lines = content.split('\n')
        let result = lines.slice(0, MAX_LINES)

        // 限制每行长度
        result = result.map(line => {
          if (line.length > MAX_LENGTH) {
            return line.substring(0, MAX_LENGTH) + '...'
          }
          return line
        })

        // 如果原内容超过限制，添加省略号
        if (lines.length > MAX_LINES || content.length > MAX_LINES * MAX_LENGTH) {
          result.push('...')
        }

        return result.join('\n')
      },
      // 显示JSON对话框
      showJsonDialog(jsonStr, title) {
        this.jsonDialogTitle = title
        try {
          this.jsonDialogData = JSON.parse(jsonStr)
        } catch (e) {
          // 如果不是有效的JSON，显示原始字符串
          this.jsonDialogData = jsonStr
        }
        this.jsonDialogVisible = true
      }
    }
  }
</script>

<style lang="scss" scoped>
.json-preview {
  cursor: pointer;
  white-space: pre-wrap;
  word-break: break-word;
  max-height: 120px;
  min-height: 40px;
  overflow: hidden;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', 'Consolas', 'source-code-pro', monospace;
  font-size: 12px;
  line-height: 1.5;
  padding: 8px;
  margin: 2px 0;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  background-color: #fafafa;
  color: #606266;
  transition: all 0.2s ease;
  position: relative;

  &:hover {
    background-color: #f0f9ff;
    border-color: #409eff;
    color: #409eff;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);
  }

  &:active {
    transform: translateY(0);
  }

  // 添加点击提示
  &::after {
    content: '点击查看完整内容';
    position: absolute;
    top: 2px;
    right: 6px;
    font-size: 10px;
    color: #909399;
    opacity: 0;
    transition: opacity 0.2s ease;
  }

  &:hover::after {
    opacity: 1;
  }
}
</style>
