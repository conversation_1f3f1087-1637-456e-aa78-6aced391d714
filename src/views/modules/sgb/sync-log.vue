<template>
  <div class="mod-list">
    <el-form :inline="true" :model="dataForm" ref="dataForm" @keyup.enter.native="getDataList()">
      <el-form-item prop="keyword" label="关键字">
        <el-input v-model="dataForm.keyword" placeholder="请输入关键字模糊查询" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-search" type="warning" @click="getDataList()">查询</el-button>
        <el-button @click="resetForm()"icon="el-icon-refresh-left">重置</el-button>
      </el-form-item>
    </el-form>
    <div class="f-s-c" style="padding: 15px 0; display: flex; justify-content: flex-end; align-items: center">
      <div>
        <el-button type="primary" @click="addOrUpdateHandle()" icon="el-icon-plus">新增</el-button>
      </div>
    </div>
    <el-table :data="dataList" border v-loading="dataListLoading" @selection-change="selectionChangeHandle" style="width: 100%;">
<!--      <el-table-column type="selection" header-align="center" align="center" width="50"/>-->
      <el-table-column prop="syncBillId" header-align="center" align="center" label="mq消息id（幂等）"/>
      <el-table-column prop="bizType" header-align="center" align="center" label="同步业务类型（志愿者/团队/加入团队/活动...）"/>
      <el-table-column prop="bizId" header-align="center" align="center" label="业务表id"/>
      <el-table-column prop="syncObjectName" header-align="center" align="center" label="同步对象"/>
      <el-table-column prop="resultCode" header-align="center" align="center" label="是否成功"/>
      <el-table-column prop="message" header-align="center" align="center" label="市平台返回的消息"/>
      <el-table-column prop="syncTime" header-align="center" align="center" label="同步时间">
        <template slot-scope="scope">
          {{scope.row.syncTime && scope.row.syncTime.length > 16 ? scope.row.syncTime.substr(0, 16) : scope.row.syncTime}}
        </template>
      </el-table-column>
      <el-table-column prop="spCode" header-align="center" align="center" label="业务编码"/>
      <el-table-column prop="requestParam" header-align="center" align="center" label="请求体"/>
      <el-table-column prop="response" header-align="center" align="center" label="响应"/>
      <el-table-column fixed="right" header-align="center" align="center" width="150" label="操作">
        <template slot-scope="scope">
          <el-button type="text" @click="addOrUpdateHandle(scope.row.id)">编辑</el-button>
          <el-button class="delete_btn" type="text" @click="deleteHandle(scope.row.id,scope.row.name)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination @size-change="sizeChangeHandle" @current-change="currentChangeHandle" :current-page="pageIndex"
                   :page-sizes="[10, 20, 50, 100]" :page-size="pageSize" :total="totalPage" layout="total, sizes, prev, pager, next, jumper"/>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"/>
  </div>
</template>

<script>
  import AddOrUpdate from './sync-log-add-or-update'
  import listMixin from '@/mixins/list-mixins'
  export default {
    mixins: [listMixin],
    data () {
      return {
        mixinOptions: {
          dataUrl: '/admin/sgb/sync-log/pages',
          deleteUrl: '/admin/sgb/sync-log/removeByIds'
        },
        dataForm: {
          keyword: null
        }
      }
    },
    components: {
      AddOrUpdate
    },
    methods: {
      resetForm() {
        this.$refs['dataForm']?.resetFields()
        this.getDataList()
      }
    }
  }
</script>

<style lang="scss" scoped>
</style>
