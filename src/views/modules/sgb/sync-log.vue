<template>
  <div class="mod-list">
    <el-form :inline="true" :model="dataForm" ref="dataForm" @keyup.enter.native="getDataList()">
      <el-form-item prop="keyword" label="关键字">
        <el-input v-model="dataForm.keyword" placeholder="请输入关键字模糊查询" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-search" type="warning" @click="getDataList()">查询</el-button>
        <el-button @click="resetForm()"icon="el-icon-refresh-left">重置</el-button>
      </el-form-item>
    </el-form>
    <div class="f-s-c" style="padding: 15px 0; display: flex; justify-content: flex-end; align-items: center">
      <div>
        <el-button type="primary" @click="addOrUpdateHandle()" icon="el-icon-plus">新增</el-button>
      </div>
    </div>
    <el-table :data="dataList" border v-loading="dataListLoading" @selection-change="selectionChangeHandle" style="width: 100%;">
<!--      <el-table-column type="selection" header-align="center" align="center" width="50"/>-->
      <el-table-column prop="syncBillId" header-align="center" align="center" label="mq消息id（幂等）"/>
      <el-table-column prop="bizTypeText" header-align="center" align="center" label="同步业务类型（志愿者/团队/加入团队/活动...）"/>
      <el-table-column prop="bizId" header-align="center" align="center" label="业务表id"/>
      <el-table-column prop="syncObjectName" header-align="center" align="center" label="同步对象"/>
      <el-table-column prop="resultCode" header-align="center" align="center" label="是否成功"/>
      <el-table-column prop="message" header-align="center" align="center" label="市平台返回的消息"/>
      <el-table-column prop="syncTime" header-align="center" align="center" label="同步时间">
        <template slot-scope="scope">
          {{scope.row.syncTime && scope.row.syncTime.length > 16 ? scope.row.syncTime.substr(0, 16) : scope.row.syncTime}}
        </template>
      </el-table-column>
      <el-table-column prop="spCode" header-align="center" align="center" label="业务编码"/>
      <el-table-column prop="requestParam" header-align="center" align="left" label="请求体" width="200">
        <template slot-scope="scope">
          <div
            class="json-preview"
            @click="showJsonDialog(scope.row.requestParam, '请求体')"
            :title="scope.row.requestParam">
            {{ formatJsonPreview(scope.row.requestParam) }}
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="response" header-align="center" align="left" label="响应" width="200">
        <template slot-scope="scope">
          <div
            class="json-preview"
            @click="showJsonDialog(scope.row.response, '响应')"
            :title="scope.row.response">
            {{ formatJsonPreview(scope.row.response) }}
          </div>
        </template>
      </el-table-column>
      <el-table-column fixed="right" header-align="center" align="center" width="100" label="操作">
        <template slot-scope="scope">
          <el-button type="text" @click="addOrUpdateHandle(scope.row.id)">详情</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination @size-change="sizeChangeHandle" @current-change="currentChangeHandle" :current-page="pageIndex"
                   :page-sizes="[10, 20, 50, 100]" :page-size="pageSize" :total="totalPage" layout="total, sizes, prev, pager, next, jumper"/>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"/>

    <!-- JSON展示对话框 -->
    <el-dialog
      :title="jsonDialogTitle"
      :visible.sync="jsonDialogVisible"
      width="60%"
      :close-on-click-modal="false">
      <json-viewer
        :value="jsonDialogData"
        :expand-depth="3"
        copyable
        boxed
        sort/>
    </el-dialog>
  </div>
</template>

<script>
  import AddOrUpdate from './sync-log-add-or-update'
  import listMixin from '@/mixins/list-mixins'
  import JsonViewer from 'vue-json-viewer'

  export default {
    mixins: [listMixin],
    data () {
      return {
        mixinOptions: {
          dataUrl: '/admin/sgb/sync-log/pages',
          deleteUrl: '/admin/sgb/sync-log/removeByIds'
        },
        dataForm: {
          keyword: null
        },
        jsonDialogVisible: false,
        jsonDialogTitle: '',
        jsonDialogData: null
      }
    },
    components: {
      AddOrUpdate,
      JsonViewer
    },
    methods: {
      resetForm() {
        this.$refs['dataForm']?.resetFields()
        this.getDataList()
      },
      // 格式化JSON预览，限制为5行
      formatJsonPreview(jsonStr) {
        if (!jsonStr) return ''

        try {
          // 尝试解析JSON并格式化
          const parsed = JSON.parse(jsonStr)
          const formatted = JSON.stringify(parsed, null, 2)
          const lines = formatted.split('\n')

          if (lines.length <= 5) {
            return formatted
          } else {
            return lines.slice(0, 5).join('\n') + '\n...'
          }
        } catch (e) {
          // 如果不是有效的JSON，直接按行处理
          const lines = jsonStr.split('\n')
          if (lines.length <= 5) {
            return jsonStr
          } else {
            return lines.slice(0, 5).join('\n') + '\n...'
          }
        }
      },
      // 显示JSON对话框
      showJsonDialog(jsonStr, title) {
        this.jsonDialogTitle = title
        try {
          this.jsonDialogData = JSON.parse(jsonStr)
        } catch (e) {
          // 如果不是有效的JSON，显示原始字符串
          this.jsonDialogData = jsonStr
        }
        this.jsonDialogVisible = true
      }
    }
  }
</script>

<style lang="scss" scoped>
.json-preview {
  cursor: pointer;
  white-space: pre-wrap;
  word-break: break-all;
  max-height: 120px;
  overflow: hidden;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  line-height: 1.4;
  padding: 4px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  background-color: #f5f7fa;

  &:hover {
    background-color: #ecf5ff;
    border-color: #409eff;
  }
}
</style>
