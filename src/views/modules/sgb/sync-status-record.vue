<template>
  <div class="mod-list">
    <el-form :inline="true" :model="dataForm" ref="dataForm" @keyup.enter.native="getDataList()">
      <el-form-item prop="bizType" label="业务类型">
        <el-select v-model="dataForm.bizType" placeholder="请选择业务类型">
          <el-option label="志愿者同步" value="sync_biz_volunteer"></el-option>
          <el-option label="团队同步" value="sync_biz_team"></el-option>
          <el-option label="加入团队同步" value="sync_biz_join_team"></el-option>
          <el-option label="活动同步" value="sync_biz_activity"></el-option>
          <el-option label="活动招募同步" value="sync_biz_activity_recruit"></el-option>
          <el-option label="活动成员同步" value="sync_biz_activity_member"></el-option>
          <el-option label="服务时长同步" value="sync_biz_service_time"></el-option>
          <el-option label="阵地同步" value="sync_biz_base_sync"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item prop="keyword" label="关键字">
        <el-input v-model="dataForm.keyword" placeholder="请输入关键字模糊查询" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-search" type="warning" @click="getDataList()">查询</el-button>
        <el-button @click="resetForm()"icon="el-icon-refresh-left">重置</el-button>
      </el-form-item>
    </el-form>
    <el-table :data="dataList" border v-loading="dataListLoading" style="width: 100%;">
      <el-table-column prop="businessType" header-align="center" align="center" label="业务类型"/>
      <el-table-column prop="businessId" header-align="center" align="center" label="业务ID"/>
      <el-table-column v-if="dataForm.bizType" prop="bizName" header-align="center" align="center" label="业务数据名称"/>
      <el-table-column prop="syncStatus" header-align="center" align="center" label="同步状态">
        <template slot-scope="scope">
          <el-tag v-if="!scope.row.syncStatus" size="small" type="danger">同步失败</el-tag>
          <el-tag v-else size="small" type="success">同步成功</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="syncRemark" header-align="center" align="center" label="同步备注"/>
      <el-table-column prop="sgbId" header-align="center" align="center" label="社工部ID"/>
      <el-table-column prop="syncTime" header-align="center" align="center" label="同步时间">
        <template slot-scope="scope">
          {{scope.row.syncTime && scope.row.syncTime.length > 16 ? scope.row.syncTime.substr(0, 16) : scope.row.syncTime}}
        </template>
      </el-table-column>
    </el-table>
    <el-pagination @size-change="sizeChangeHandle" @current-change="currentChangeHandle" :current-page="pageIndex"
                   :page-sizes="[10, 20, 50, 100]" :page-size="pageSize" :total="totalPage" layout="total, sizes, prev, pager, next, jumper"/>
  </div>
</template>

<script>
  import listMixin from '@/mixins/list-mixins'
  export default {
    mixins: [listMixin],
    data () {
      return {
        mixinOptions: {
          dataUrl: '/admin/sgb/sync-status-record/getPages'
        },
        dataForm: {
          bizType: 'sync_biz_volunteer',
          keyword: null
        }
      }
    },
    methods: {
      resetForm() {
        this.$refs['dataForm']?.resetFields()
        this.dataForm.bizType = 'sync_biz_volunteer'
        this.getDataList()
      },
      // 重写查询方法以适配新的接口参数格式
      query () {
        this.queryBeforeHandle()
        let params = {
          current: this.pageIndex,
          size: this.pageSize,
          ...this.dataForm
        }

        // 过滤空值参数
        Object.keys(params).forEach(key => {
          if (params[key] === null || params[key] === undefined || params[key] === '') {
            delete params[key]
          }
        })

        let request = {
          url: this.$http.adornUrl(this.mixinOptions.dataUrl),
          method: 'post',
          data: this.$http.adornData(params)
        }

        this.dataListLoading = true
        this.$http(request).then(({data}) => {
          this.dataListLoading = false
          this.queryCallback(data)
        }).catch(() => {
          this.dataListLoading = false
        })
      }
    }
  }
</script>

<style lang="scss" scoped>
</style>
