<template>
  <div class="mod-list">
    <el-form :inline="true" :model="dataForm" ref="dataForm" @keyup.enter.native="getDataList()">
      <el-form-item prop="bizType" label="业务类型">
        <el-dict v-model="dataForm.bizType" :code="'sgb_sync_biz_type'" placeholder="请选择业务类型" clearable :default-first="true" @filterDictData="onDictLoaded"></el-dict>
      </el-form-item>
      <el-form-item prop="businessId" label="业务ID">
        <el-input v-model="dataForm.businessId" placeholder="请输入业务ID" clearable></el-input>
      </el-form-item>
      <el-form-item prop="syncStatus" label="同步状态">
        <el-select v-model="dataForm.syncStatus" placeholder="请选择同步状态" clearable>
          <el-option label="同步成功" :value="true"></el-option>
          <el-option label="同步失败" :value="false"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item prop="syncRemark" label="同步备注">
        <el-input v-model="dataForm.syncRemark" placeholder="请输入同步备注" clearable></el-input>
      </el-form-item>
      <el-form-item prop="sgbId" label="社工部ID">
        <el-input v-model="dataForm.sgbId" placeholder="请输入社工部ID" clearable></el-input>
      </el-form-item>
      <el-form-item prop="syncTime" label="同步时间">
        <el-date-picker
          v-model="dataForm.syncTimeRange"
          type="datetimerange"
          value-format="yyyy-MM-dd HH:mm:ss"
          range-separator="-"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
          :default-time="['00:00:00', '23:59:59']"
          clearable>
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-search" type="warning" @click="getDataList()">查询</el-button>
        <el-button @click="resetForm()" icon="el-icon-refresh-left">重置</el-button>
      </el-form-item>
    </el-form>
    <el-table :data="dataList" border v-loading="dataListLoading" style="width: 100%;">
      <el-table-column prop="businessTypeText" header-align="center" align="center" label="业务类型"/>
      <el-table-column prop="businessId" header-align="center" align="center" label="业务ID"/>
      <el-table-column v-if="dataForm.bizType" prop="bizName" header-align="center" align="center" label="业务数据名称"/>
      <el-table-column prop="syncStatus" header-align="center" align="center" label="同步状态">
        <template slot-scope="scope">
          <el-tag v-if="!scope.row.syncStatus" size="small" type="danger">同步失败</el-tag>
          <el-tag v-else size="small" type="success">同步成功</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="syncRemark" header-align="center" align="center" label="同步备注"/>
      <el-table-column min-width="110px" prop="sgbId" header-align="center" align="center" label="社工部ID"/>
      <el-table-column prop="syncTime" header-align="center" align="center" label="同步时间">
        <template slot-scope="scope">
          {{scope.row.syncTime && scope.row.syncTime.length > 16 ? scope.row.syncTime.substr(0, 16) : scope.row.syncTime}}
        </template>
      </el-table-column>
    </el-table>
    <el-pagination @size-change="sizeChangeHandle" @current-change="currentChangeHandle" :current-page="pageIndex"
                   :page-sizes="[10, 20, 50, 100]" :page-size="pageSize" :total="totalPage" layout="total, sizes, prev, pager, next, jumper"/>
  </div>
</template>

<script>
  import listMixin from '@/mixins/list-mixins'
  export default {
    mixins: [listMixin],
    data () {
      return {
        mixinOptions: {
          dataUrl: '/admin/sgb/sync-status-record/getPages',
          activatedLoad: false
        },
        dataForm: {
          bizType: null,
          businessId: null,
          syncStatus: null,
          syncRemark: null,
          sgbId: null,
          syncTimeRange: null,
          syncTimeStart: null,
          syncTimeEnd: null
        }
      }
    },
    methods: {
      // 查询前处理时间范围参数
      queryBeforeHandle() {
        // 处理时间范围参数
        if (this.dataForm.syncTimeRange && this.dataForm.syncTimeRange.length === 2) {
          this.dataForm.syncTimeStart = this.dataForm.syncTimeRange[0]
          this.dataForm.syncTimeEnd = this.dataForm.syncTimeRange[1]
        } else {
          this.dataForm.syncTimeStart = null
          this.dataForm.syncTimeEnd = null
        }
      },
      // 字典数据加载完成的回调
      onDictLoaded() {
        // 字典加载完成后加载列表数据
        this.query()
      },
      resetForm() {
        this.$refs['dataForm']?.resetFields()
        this.dataForm = {
          bizType: null,
          businessId: null,
          syncStatus: null,
          syncRemark: null,
          sgbId: null,
          syncTimeRange: null,
          syncTimeStart: null,
          syncTimeEnd: null
        }
        this.getDataList()
      }
    }
  }
</script>

<style lang="scss" scoped>
</style>
