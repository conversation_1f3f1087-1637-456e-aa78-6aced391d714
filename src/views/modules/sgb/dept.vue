<template>
  <div class="mod-list">
    <el-form :inline="true" :model="dataForm" ref="dataForm" @keyup.enter.native="getDataList()">
      <el-form-item prop="deptId" label="部门单位编号">
        <el-input v-model="dataForm.deptId" placeholder="请输入部门单位编号" clearable></el-input>
      </el-form-item>
      <el-form-item prop="deptName" label="部门单位名称">
        <el-input v-model="dataForm.deptName" placeholder="请输入部门单位名称" clearable></el-input>
      </el-form-item>
      <el-form-item prop="regionCode" label="所属区划编号">
        <el-input v-model="dataForm.regionCode" placeholder="请输入所属区划编号" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-search" type="warning" @click="getDataList()">查询</el-button>
        <el-button @click="resetForm()"icon="el-icon-refresh-left">重置</el-button>
      </el-form-item>
    </el-form>
    <div class="f-s-c" style="padding: 15px 0; display: flex; justify-content: flex-end; align-items: center">
      <div>
        <el-button type="primary" @click="syncConfirm()" icon="el-icon-refresh" :disabled="syncLoading" :loading="syncLoading">同步</el-button>
      </div>
    </div>
    <el-table :data="dataList" border v-loading="dataListLoading" @selection-change="selectionChangeHandle" style="width: 100%;">
<!--      <el-table-column type="selection" header-align="center" align="center" width="50"/>-->
      <el-table-column prop="deptId" header-align="center" align="center" label="部门单位编号"/>
      <el-table-column prop="deptName" header-align="center" align="center" label="部门单位名称"/>
      <el-table-column prop="regionCode" header-align="center" align="center" label="所属区划编号"/>
      <el-table-column prop="updateDate" header-align="center" align="center" label="更新时间"/>
      <!-- 操作列已隐藏 -->
    </el-table>
    <el-pagination @size-change="sizeChangeHandle" @current-change="currentChangeHandle" :current-page="pageIndex"
                   :page-sizes="[10, 20, 50, 100]" :page-size="pageSize" :total="totalPage" layout="total, sizes, prev, pager, next, jumper"/>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"/>
  </div>
</template>

<script>
  import AddOrUpdate from './dept-add-or-update'
  import listMixin from '@/mixins/list-mixins'
  export default {
    mixins: [listMixin],
    data () {
      return {
        mixinOptions: {
          dataUrl: '/admin/sgb/dept/pages',
          deleteUrl: '/admin/sgb/dept/removeByIds'
        },
        dataForm: {
          deptId: null,
          deptName: null,
          regionCode: null,
          orders: [
            {column: 'regionCode', sort: 'asc'},
            {column: 'deptId', sort: 'asc'}
          ]
        },
        syncLoading: false
      }
    },
    components: {
      AddOrUpdate
    },
    methods: {
      resetForm() {
        this.$refs['dataForm']?.resetFields()
        this.getDataList()
      },
      syncConfirm() {
        this.$confirm('确定同步部门单位数据吗?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.syncHandle()
        }).catch(() => {
          this.$message.info('已取消同步')
        })
      },
      syncHandle() {
        this.syncLoading = true
        this.dataListLoading = true
        this.$http({
          url: this.$http.adornUrl('/admin/sgb/dept/sync'),
          method: 'post',
          data: this.$http.adornData({})
        }).then(({data}) => {
          this.syncLoading = false
          this.dataListLoading = false
          if (data && data.code === 0) {
            this.$message.success('同步成功')
            this.getDataList()
          } else {
            this.$message.error(data.msg || '同步失败')
          }
        }).catch(() => {
          this.syncLoading = false
          this.dataListLoading = false
          this.$message.error('同步失败')
        })
      }
    }
  }
</script>

<style lang="scss" scoped>
</style>
