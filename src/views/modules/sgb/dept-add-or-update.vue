<template>
  <el-dialog class="common-dialog" :title="!dataForm.id ? '新增' : '修改'" :visible.sync="visible"
             :close-on-click-modal="false" :modal-append-to-body="false" width="60%">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmit()" label-width="90px">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="部门单位编号" prop="deptId">
            <el-input v-model="dataForm.deptId" placeholder="请输入" clearable/>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="部门单位名称" prop="deptName">
            <el-input v-model="dataForm.deptName" placeholder="请输入" clearable/>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="所属区划编号" prop="regionCode">
            <el-input v-model="dataForm.regionCode" placeholder="请输入" clearable/>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()" :disabled="dataFormLoading">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
  import _ from 'lodash'
  import editMixin from '@/mixins/edit-mixins'
  export default {
    mixins: [editMixin],
    data () {
      return {
        editOptions: {
          initUrl: '/admin/sgb/dept'
        },
        dataForm: {},
        initForm: {
          id: null,
          version: null,
          deptId: null,
          deptName: null,
          regionCode: null
        },
        dataRule: {
          deptId: [
            { required: true, message: '部门单位编号不能为空', trigger: 'change' }
          ],
          deptName: [
            { required: true, message: '部门单位名称不能为空', trigger: 'change' }
          ],
          regionCode: [
            { required: true, message: '所属区划编号不能为空', trigger: 'change' }
          ]
        }
      }
    },
    methods: {
      init (id) {
        this.dataForm = _.cloneDeep(this.initForm)
        this.dataForm.id = id || null
        this.visible = true
        this.$nextTick(() => {
          this.$refs['dataForm']?.resetFields()
          if (this.dataForm.id) {
            this.getData()
          }
        })
      }
    }
  }
</script>

<style lang="scss" scoped>
</style>
