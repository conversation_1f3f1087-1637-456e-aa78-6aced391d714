<template>
  <el-dialog class="common-dialog" :title="!dataForm.id ? '新增' : '修改'" :visible.sync="visible"
             :close-on-click-modal="false" :modal-append-to-body="false" width="60%">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmit()" label-width="120px">
      <el-form-item label="本系统字典类型" prop="sysType">
        <el-select
          v-model="dataForm.sysType"
          filterable
          allow-create
          default-first-option
          placeholder="请选择或输入本系统字典类型"
          :loading="sysTypeLoading"
          clearable
          style="width: 100%;">
          <el-option
            v-for="item in sysTypeOptions"
            :key="item.id"
            :label="`${item.text} - ${item.id}`"
            :value="item.id">
            <span style="float: left">{{ item.text }}</span>
            <span style="float: right; color: #8492a6; font-size: 13px; margin-left: 30px;">{{ item.id }}</span>
          </el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="本系统字典值" prop="sysValue">
        <el-select
          v-model="dataForm.sysValue"
          filterable
          allow-create
          default-first-option
          placeholder="请选择或输入本系统字典值"
          :loading="sysValueLoading"
          :disabled="!dataForm.sysType"
          clearable
          style="width: 100%;">
          <el-option
            v-for="item in sysValueOptions"
            :key="item.code"
            :label="`${item.name} - ${item.code}`"
            :value="item.code">
            <span style="float: left">{{ item.name }}</span>
            <span style="float: right; color: #8492a6; font-size: 13px; margin-left: 30px;">{{ item.code }}</span>
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="第三方字典类型" prop="thirdType">
        <el-select
          v-model="dataForm.thirdType"
          filterable
          allow-create
          default-first-option
          placeholder="请选择或输入第三方字典类型"
          :loading="thirdTypeLoading"
          clearable
          style="width: 100%;">
          <el-option
            v-for="item in thirdTypeOptions"
            :key="item.id"
            :label="`${item.text} - ${item.id}`"
            :value="item.id">
            <span style="float: left">{{ item.text }}</span>
            <span style="float: right; color: #8492a6; font-size: 13px; margin-left: 30px;">{{ item.id }}</span>
          </el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="第三方字典值" prop="thirdValue">
        <el-select
          v-model="dataForm.thirdValue"
          filterable
          allow-create
          default-first-option
          placeholder="请选择或输入第三方字典值"
          :loading="thirdValueLoading"
          :disabled="!dataForm.thirdType"
          clearable
          style="width: 100%;">
          <el-option
            v-for="item in thirdValueOptions"
            :key="item.id"
            :label="`${item.text} - ${item.id}`"
            :value="item.id">
            <span style="float: left">{{ item.text }}</span>
            <span style="float: right; color: #8492a6; font-size: 13px; margin-left: 30px;">{{ item.id }}</span>
          </el-option>
        </el-select>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()" :disabled="dataFormLoading">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
  import _ from 'lodash'
  import editMixin from '@/mixins/edit-mixins'
  export default {
    mixins: [editMixin],
    data () {
      return {
        editOptions: {
          initUrl: '/admin/sgb/dict-mapping'
        },
        dataForm: {},
        initForm: {
          id: null,
          version: null,
          sysType: null,
          sysValue: null,
          thirdType: null,
          thirdValue: null
        },
        dataRule: {
          sysType: [
            { required: true, message: '本系统字典类型不能为空', trigger: 'change' }
          ],
          sysValue: [
            { required: true, message: '本系统字典值不能为空', trigger: 'change' }
          ],
          thirdType: [
            { required: true, message: '第三方字典类型不能为空', trigger: 'change' }
          ],
          thirdValue: [
            { required: true, message: '第三方字典值不能为空', trigger: 'change' }
          ]
        },
        // 选项数据
        sysTypeOptions: [],
        sysValueOptions: [],
        thirdTypeOptions: [],
        thirdValueOptions: [],
        // 加载状态
        sysTypeLoading: false,
        sysValueLoading: false,
        thirdTypeLoading: false,
        thirdValueLoading: false,
        // 数据加载标志，用于区分是数据加载还是用户操作
        isDataLoading: false
      }
    },
    watch: {
      'dataForm.sysType'(newVal, oldVal) {
        if (newVal !== oldVal) {
          // 清空字典值选择
          this.dataForm.sysValue = null
          this.sysValueOptions = []
          // 如果选择了字典类型，则获取对应的字典值
          if (newVal) {
            this.loadSysValueOptions()
          }
        }
      },
      'dataForm.thirdType'(newVal, oldVal) {
        if (newVal !== oldVal) {
          // 清空字典值选择
          this.dataForm.thirdValue = null
          this.thirdValueOptions = []
          // 如果选择了字典类型，则获取对应的字典值
          if (newVal) {
            this.loadThirdValueOptions()
          }
        }
      }
    },
    methods: {
      init (id) {
        this.dataForm = _.cloneDeep(this.initForm)
        this.dataForm.id = id || null
        this.visible = true

        // 初始化选项数据
        this.loadAllSysTypeOptions()
        this.loadAllThirdTypeOptions()

        this.$nextTick(() => {
          this.$refs['dataForm']?.resetFields()
          if (this.dataForm.id) {
            this.getData()
          }
        })
      },

      // 加载所有系统字典类型选项
      loadAllSysTypeOptions() {
        this.sysTypeLoading = true
        this.$http({
          url: this.$http.adornUrl('/admin/dict/findTopDictCode'),
          method: 'get'
        }).then(({data}) => {
          this.sysTypeLoading = false
          if (data && data.code === 0) {
            this.sysTypeOptions = data.obj || []
          } else {
            this.sysTypeOptions = []
          }
        }).catch(() => {
          this.sysTypeLoading = false
          this.sysTypeOptions = []
        })
      },

      // 加载系统字典值选项
      loadSysValueOptions() {
        if (!this.dataForm.sysType) {
          this.sysValueOptions = []
          return
        }

        this.sysValueLoading = true
        this.$http({
          url: this.$http.adornUrl('/admin/dict/parent'),
          method: 'get',
          params: this.$http.adornParams({ code: this.dataForm.sysType })
        }).then(({data}) => {
          this.sysValueLoading = false
          if (data && data.code === 0) {
            this.sysValueOptions = data.obj || []
          } else {
            this.sysValueOptions = []
          }
        }).catch(() => {
          this.sysValueLoading = false
          this.sysValueOptions = []
        })
      },

      // 加载所有第三方字典类型选项
      loadAllThirdTypeOptions() {
        this.thirdTypeLoading = true
        this.$http({
          url: this.$http.adornUrl('/admin/sgb/dict/types'),
          method: 'get'
        }).then(({data}) => {
          this.thirdTypeLoading = false
          if (data && data.code === 0) {
            this.thirdTypeOptions = data.obj || []
          } else {
            this.thirdTypeOptions = []
          }
        }).catch(() => {
          this.thirdTypeLoading = false
          this.thirdTypeOptions = []
        })
      },

      // 加载第三方字典值选项
      loadThirdValueOptions() {
        if (!this.dataForm.thirdType) {
          this.thirdValueOptions = []
          return
        }

        this.thirdValueLoading = true
        this.$http({
          url: this.$http.adornUrl('/admin/sgb/dict/type'),
          method: 'get',
          params: this.$http.adornParams({ type: this.dataForm.thirdType })
        }).then(({data}) => {
          this.thirdValueLoading = false
          if (data && data.code === 0) {
            this.thirdValueOptions = data.obj || []
          } else {
            this.thirdValueOptions = []
          }
        }).catch(() => {
          this.thirdValueLoading = false
          this.thirdValueOptions = []
        })
      }
    }
  }
</script>

<style lang="scss" scoped>
</style>
