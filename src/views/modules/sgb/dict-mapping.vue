<template>
  <div class="mod-list">
    <el-form :inline="true" :model="dataForm" ref="dataForm" @keyup.enter.native="getDataList()">
      <el-form-item prop="sysType" label="本系统字典类型">
        <el-select
          v-model="dataForm.sysType"
          filterable
          remote
          allow-create
          default-first-option
          reserve-keyword
          placeholder="请选择或输入本系统字典类型"
          :remote-method="getSysTypeOptions"
          :loading="sysTypeLoading"
          clearable
          style="width: 280px;">
          <el-option
            v-for="item in sysTypeOptions"
            :key="item.id"
            :label="`${item.text} - ${item.id}`"
            :value="item.id">
            <span style="float: left">{{ item.text }}</span>
            <span style="float: right; color: #8492a6; font-size: 13px; margin-left: 30px;">{{ item.id }}</span>
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item prop="sysValue" label="本系统字典值">
        <el-select
          v-model="dataForm.sysValue"
          filterable
          remote
          allow-create
          default-first-option
          reserve-keyword
          placeholder="请选择或输入本系统字典值"
          :remote-method="getSysValueOptions"
          :loading="sysValueLoading"
          :disabled="!dataForm.sysType"
          clearable
          style="width: 280px;">
          <el-option
            v-for="item in sysValueOptions"
            :key="item.code"
            :label="`${item.name} - ${item.code}`"
            :value="item.code">
            <span style="float: left">{{ item.name }}</span>
            <span style="float: right; color: #8492a6; font-size: 13px; margin-left: 30px;">{{ item.code }}</span>
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item prop="thirdType" label="第三方字典类型">
        <el-select
          v-model="dataForm.thirdType"
          filterable
          remote
          allow-create
          default-first-option
          reserve-keyword
          placeholder="请选择或输入第三方字典类型"
          :remote-method="getThirdTypeOptions"
          :loading="thirdTypeLoading"
          clearable
          style="width: 280px;">
          <el-option
            v-for="item in thirdTypeOptions"
            :key="item.id"
            :label="`${item.text} - ${item.id}`"
            :value="item.id">
            <span style="float: left">{{ item.text }}</span>
            <span style="float: right; color: #8492a6; font-size: 13px; margin-left: 30px;">{{ item.id }}</span>
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item prop="thirdValue" label="第三方字典值">
        <el-select
          v-model="dataForm.thirdValue"
          filterable
          remote
          allow-create
          default-first-option
          reserve-keyword
          placeholder="请选择或输入第三方字典值"
          :remote-method="getThirdValueOptions"
          :loading="thirdValueLoading"
          :disabled="!dataForm.thirdType"
          clearable
          style="width: 280px;">
          <el-option
            v-for="item in thirdValueOptions"
            :key="item.id"
            :label="`${item.text} - ${item.id}`"
            :value="item.id">
            <span style="float: left">{{ item.text }}</span>
            <span style="float: right; color: #8492a6; font-size: 13px; margin-left: 30px;">{{ item.id }}</span>
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-search" type="warning" @click="getDataList()">查询</el-button>
        <el-button @click="resetForm()"icon="el-icon-refresh-left">重置</el-button>
      </el-form-item>
    </el-form>
    <div class="f-s-c" style="padding: 15px 0; display: flex; justify-content: flex-end; align-items: center">
      <div>
        <el-button type="success" @click="batchEditHandle()" icon="el-icon-edit">批量编辑映射</el-button>
        <el-button type="primary" @click="addOrUpdateHandle()" icon="el-icon-plus">新增</el-button>
      </div>
    </div>
    <el-table :data="dataList" border v-loading="dataListLoading" @selection-change="selectionChangeHandle" style="width: 100%;">
<!--      <el-table-column type="selection" header-align="center" align="center" width="50"/>-->
      <el-table-column prop="sysType" header-align="center" align="center" label="本系统字典类型"/>
      <el-table-column prop="sysTypeText" header-align="center" align="center" label="本系统字典类型名称"/>
      <el-table-column prop="sysValue" header-align="center" align="center" label="本系统字典值"/>
      <el-table-column prop="sysText" header-align="center" align="center" label="本系统字典名称"/>
      <el-table-column prop="thirdType" header-align="center" align="center" label="第三方字典类型"/>
      <el-table-column prop="thirdValue" header-align="center" align="center" label="第三方字典值"/>
      <el-table-column fixed="right" header-align="center" align="center" width="150" label="操作">
        <template slot-scope="scope">
          <el-button type="text" @click="addOrUpdateHandle(scope.row.id)">编辑</el-button>
          <el-button class="delete_btn" type="text" @click="deleteHandle(scope.row.id,scope.row.name)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination @size-change="sizeChangeHandle" @current-change="currentChangeHandle" :current-page="pageIndex"
                   :page-sizes="[10, 20, 50, 100]" :page-size="pageSize" :total="totalPage" layout="total, sizes, prev, pager, next, jumper"/>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"/>

    <!-- 批量编辑弹窗 -->
    <batch-edit v-if="batchEditVisible" ref="batchEdit" @refreshDataList="getDataList"/>
  </div>
</template>

<script>
  import AddOrUpdate from './dict-mapping-add-or-update'
  import BatchEdit from './dict-mapping-batch-edit'
  import listMixin from '@/mixins/list-mixins'
  export default {
    mixins: [listMixin],
    data () {
      return {
        mixinOptions: {
          dataUrl: '/admin/sgb/dict-mapping/pages',
          deleteUrl: '/admin/sgb/dict-mapping/removeByIds'
        },
        dataForm: {
          sysType: null,
          sysValue: null,
          thirdType: null,
          thirdValue: null,
          orders: [
            {column: 'sysType', sort: 'asc'},
            {column: 'sysValue', sort: 'asc'}
          ]
        },
        sysTypeOptions: [],
        sysTypeLoading: false,
        sysValueOptions: [],
        sysValueLoading: false,
        thirdTypeOptions: [],
        thirdTypeLoading: false,
        thirdValueOptions: [],
        thirdValueLoading: false,
        batchEditVisible: false
      }
    },
    components: {
      AddOrUpdate,
      BatchEdit
    },
    created() {
      this.getSysTypeOptions('')
      this.getThirdTypeOptions('')
    },
    watch: {
      'dataForm.sysType'(newVal, oldVal) {
        if (newVal !== oldVal) {
          // 清空字典值选择
          this.dataForm.sysValue = null
          this.sysValueOptions = []
          // 如果选择了字典类型，则获取对应的字典值
          if (newVal) {
            this.getSysValueOptions('')
          }
        }
      },
      'dataForm.thirdType'(newVal, oldVal) {
        if (newVal !== oldVal) {
          // 清空字典值选择
          this.dataForm.thirdValue = null
          this.thirdValueOptions = []
          // 如果选择了字典类型，则获取对应的字典值
          if (newVal) {
            this.getThirdValueOptions('')
          }
        }
      }
    },
    methods: {
      resetForm() {
        this.$refs['dataForm']?.resetFields()
        this.getDataList()
      },

      // 批量编辑
      batchEditHandle() {
        this.batchEditVisible = true
        this.$nextTick(() => {
          this.$refs.batchEdit.init()
        })
      },
      getSysTypeOptions(query) {
        if (query !== '') {
          this.sysTypeLoading = true
          this.$http({
            url: this.$http.adornUrl('/admin/dict/findTopDictCode'),
            method: 'get',
            params: this.$http.adornParams({ query: query })
          }).then(({data}) => {
            this.sysTypeLoading = false
            if (data && data.code === 0) {
              this.sysTypeOptions = data.obj || []
            }
          }).catch(() => {
            this.sysTypeLoading = false
          })
        } else {
          this.$http({
            url: this.$http.adornUrl('/admin/dict/findTopDictCode'),
            method: 'get'
          }).then(({data}) => {
            if (data && data.code === 0) {
              this.sysTypeOptions = data.obj || []
            }
          })
        }
      },
      getSysValueOptions(query) {
        if (!this.dataForm.sysType) {
          return
        }

        if (query !== '') {
          this.sysValueLoading = true
          this.$http({
            url: this.$http.adornUrl('/admin/dict/parent'),
            method: 'get',
            params: this.$http.adornParams({
              code: this.dataForm.sysType,
              query: query
            })
          }).then(({data}) => {
            this.sysValueLoading = false
            if (data && data.code === 0) {
              this.sysValueOptions = data.obj || []
            }
          }).catch(() => {
            this.sysValueLoading = false
          })
        } else {
          this.$http({
            url: this.$http.adornUrl('/admin/dict/parent'),
            method: 'get',
            params: this.$http.adornParams({ code: this.dataForm.sysType })
          }).then(({data}) => {
            if (data && data.code === 0) {
              this.sysValueOptions = data.obj || []
            }
          })
        }
      },

      // 获取第三方字典类型选项
      getThirdTypeOptions(query) {
        if (query !== '') {
          this.thirdTypeLoading = true
          this.$http({
            url: this.$http.adornUrl('/admin/sgb/dict/types'),
            method: 'get',
            params: this.$http.adornParams({ query: query })
          }).then(({data}) => {
            this.thirdTypeLoading = false
            if (data && data.code === 0) {
              this.thirdTypeOptions = data.obj || []
            }
          }).catch(() => {
            this.thirdTypeLoading = false
          })
        } else {
          this.$http({
            url: this.$http.adornUrl('/admin/sgb/dict/types'),
            method: 'get'
          }).then(({data}) => {
            if (data && data.code === 0) {
              this.thirdTypeOptions = data.obj || []
            }
          })
        }
      },

      // 获取第三方字典值选项
      getThirdValueOptions(query) {
        if (!this.dataForm.thirdType) {
          return
        }

        if (query !== '') {
          this.thirdValueLoading = true
          this.$http({
            url: this.$http.adornUrl('/admin/sgb/dict/type'),
            method: 'get',
            params: this.$http.adornParams({
              type: this.dataForm.thirdType,
              query: query
            })
          }).then(({data}) => {
            this.thirdValueLoading = false
            if (data && data.code === 0) {
              this.thirdValueOptions = data.obj || []
            }
          }).catch(() => {
            this.thirdValueLoading = false
          })
        } else {
          this.$http({
            url: this.$http.adornUrl('/admin/sgb/dict/type'),
            method: 'get',
            params: this.$http.adornParams({ type: this.dataForm.thirdType })
          }).then(({data}) => {
            if (data && data.code === 0) {
              this.thirdValueOptions = data.obj || []
            }
          })
        }
      }
    }
  }
</script>

<style lang="scss" scoped>
</style>
