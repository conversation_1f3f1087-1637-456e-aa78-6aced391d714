<template>
  <el-dialog 
    class="common-dialog" 
    title="批量编辑映射关系" 
    :visible.sync="visible"
    :close-on-click-modal="false" 
    :modal-append-to-body="false" 
    width="80%"
    :before-close="handleClose">
    
    <!-- 顶部选择系统字典类型 -->
    <div style="margin-bottom: 20px;">
      <el-form :inline="true">
        <el-form-item label="本系统字典类型：">
          <el-select
            v-model="selectedSysType"
            filterable
            placeholder="请选择本系统字典类型"
            :loading="sysTypeLoading"
            clearable
            style="width: 300px;"
            @change="onSysTypeChange">
            <el-option
              v-for="item in sysTypeOptions"
              :key="item.id"
              :label="`${item.text} - ${item.id}`"
              :value="item.id">
              <span style="float: left">{{ item.text }}</span>
              <span style="float: right; color: #8492a6; font-size: 13px; margin-left: 30px;">{{ item.id }}</span>
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="loadMappingData" :disabled="!selectedSysType" :loading="dataLoading">
            加载映射数据
          </el-button>
          <el-button v-if="mappingList.length > 0" type="warning" @click="clearAllMappings" plain>
            清空所有映射
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 统计信息 -->
    <div v-if="mappingList.length > 0" style="margin-bottom: 16px; padding: 12px; background-color: #f0f9ff; border: 1px solid #b3d8ff; border-radius: 4px;">
      <div style="display: flex; justify-content: space-between; align-items: center;">
        <div>
          <span style="color: #409eff; font-weight: 500;">数据统计：</span>
          <span style="margin-left: 8px;">总计 {{ mappingList.length }} 条</span>
          <span style="margin-left: 16px;">已映射 {{ mappedCount }} 条</span>
          <span style="margin-left: 16px;">未映射 {{ unmappedCount }} 条</span>
        </div>
        <div v-if="hasChanges" style="color: #e6a23c;">
          <i class="el-icon-warning"></i>
          <span style="margin-left: 4px;">有 {{ changedCount }} 条数据待保存</span>
        </div>
      </div>
    </div>

    <!-- 映射关系编辑区域 -->
    <div v-if="mappingList.length > 0" style="border: 1px solid #dcdfe6; border-radius: 4px;">
      <!-- 表头 -->
      <div style="display: flex; background-color: #f5f7fa; padding: 12px; border-bottom: 1px solid #dcdfe6;">
        <div style="flex: 1; font-weight: bold;">本系统字典值</div>
        <div style="flex: 1; font-weight: bold;">第三方字典类型</div>
        <div style="flex: 1; font-weight: bold;">第三方字典值</div>
        <div style="width: 100px; text-align: center; font-weight: bold;">操作</div>
      </div>
      
      <!-- 数据行 -->
      <div 
        v-for="(item, index) in mappingList" 
        :key="item.sysValue"
        style="display: flex; padding: 12px; border-bottom: 1px solid #ebeef5; align-items: center;"
        :style="{ backgroundColor: index % 2 === 0 ? '#ffffff' : '#fafafa' }">
        
        <!-- 本系统字典值（只读） -->
        <div style="flex: 1; padding-right: 12px; position: relative;">
          <div style="font-weight: 500;">{{ item.sysValueText }}</div>
          <div style="font-size: 12px; color: #909399;">{{ item.sysValue }}</div>
          <!-- 状态标识 -->
          <div style="position: absolute; top: 0; right: 12px;">
            <el-tag v-if="item.id" type="info" size="mini">已映射</el-tag>
            <el-tag v-else-if="item.thirdType || (Array.isArray(item.thirdValue) && item.thirdValue.length > 0)" type="warning" size="mini">新增</el-tag>
            <el-tag v-else type="" size="mini">未映射</el-tag>
          </div>
        </div>
        
        <!-- 第三方字典类型 -->
        <div style="flex: 1; padding-right: 12px;">
          <el-select
            v-model="item.thirdType"
            filterable
            placeholder="请选择第三方字典类型"
            :loading="item.thirdTypeLoading"
            clearable
            size="small"
            style="width: 100%;"
            @focus="() => ensureThirdTypeOptions(index)"
            @change="(value) => onThirdTypeChange(value, index)">
            <el-option
              v-for="option in item.thirdTypeOptions"
              :key="option.id"
              :label="`${option.text} - ${option.id}`"
              :value="option.id">
              <span style="float: left">{{ option.text }}</span>
              <span style="float: right; color: #8492a6; font-size: 13px; margin-left: 30px;">{{ option.id }}</span>
            </el-option>
          </el-select>
        </div>
        
        <!-- 第三方字典值 -->
        <div style="flex: 1; padding-right: 12px;">
          <el-select
            v-model="item.thirdValue"
            filterable
            multiple
            placeholder="请选择第三方字典值（可多选）"
            :loading="item.thirdValueLoading"
            :disabled="!item.thirdType"
            clearable
            size="small"
            style="width: 100%;">
            <el-option
              v-for="option in item.thirdValueOptions"
              :key="option.id"
              :label="`${option.text} - ${option.id}`"
              :value="option.id">
              <span style="float: left">{{ option.text }}</span>
              <span style="float: right; color: #8492a6; font-size: 13px; margin-left: 30px;">{{ option.id }}</span>
            </el-option>
          </el-select>
        </div>
        
        <!-- 操作 -->
        <div style="width: 100px; text-align: center;">
          <el-button 
            type="text" 
            size="small" 
            @click="clearMapping(index)"
            :disabled="!item.thirdType && !item.thirdValue">
            清空
          </el-button>
        </div>
      </div>
    </div>

    <!-- 空状态 -->
    <div v-else-if="selectedSysType && !dataLoading" style="text-align: center; padding: 40px; color: #909399;">
      <i class="el-icon-info" style="font-size: 48px; margin-bottom: 16px;"></i>
      <div>该字典类型下暂无字典值数据</div>
    </div>

    <!-- 使用说明 -->
    <div v-else-if="!selectedSysType" style="padding: 20px; background-color: #fafafa; border-radius: 4px; color: #606266;">
      <h4 style="margin-top: 0; color: #303133;">使用说明：</h4>
      <ol style="margin: 0; padding-left: 20px; line-height: 1.6;">
        <li>首先选择要编辑的本系统字典类型</li>
        <li>点击"加载映射数据"获取该类型下的所有字典值</li>
        <li>为每个系统字典值选择对应的第三方字典类型和值</li>
        <li>支持搜索和创建新的字典类型/值</li>
        <li>点击"批量保存"提交所有变更</li>
      </ol>
      <div style="margin-top: 12px; padding: 8px; background-color: #e1f3d8; border-left: 4px solid #67c23a; color: #529b2e;">
        <strong>提示：</strong>有ID的记录会更新，无ID的记录会新增保存
      </div>
    </div>

    <!-- 底部按钮 -->
    <span slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取消</el-button>
      <el-button 
        type="primary" 
        @click="batchSave" 
        :disabled="!hasChanges || saveLoading"
        :loading="saveLoading">
        批量保存 ({{ changedCount }})
      </el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  name: 'DictMappingBatchEdit',
  data() {
    return {
      visible: false,
      selectedSysType: null,
      sysTypeOptions: [],
      sysTypeLoading: false,
      mappingList: [],
      dataLoading: false,
      saveLoading: false,
      originalData: [] // 用于对比变化
    }
  },
  computed: {
    // 计算有变化的映射数量
    changedCount() {
      return this.mappingList.filter(item => this.isItemChanged(item)).length
    },
    // 是否有变化
    hasChanges() {
      return this.changedCount > 0
    },
    // 已映射数量
    mappedCount() {
      return this.mappingList.filter(item => {
        const hasThirdValue = Array.isArray(item.thirdValue) ? item.thirdValue.length > 0 : !!item.thirdValue
        return item.id || (item.thirdType && hasThirdValue)
      }).length
    },
    // 未映射数量
    unmappedCount() {
      return this.mappingList.length - this.mappedCount
    }
  },
  methods: {
    // 初始化对话框
    init() {
      this.visible = true
      this.selectedSysType = null
      this.mappingList = []
      this.originalData = []
      this.loadAllSysTypeOptions()
    },

    // 加载所有系统字典类型选项
    loadAllSysTypeOptions() {
      this.sysTypeLoading = true
      this.$http({
        url: this.$http.adornUrl('/admin/dict/findTopDictCode'),
        method: 'get'
      }).then(({data}) => {
        this.sysTypeLoading = false
        if (data && data.code === 0) {
          this.sysTypeOptions = data.obj || []
        } else {
          this.sysTypeOptions = []
        }
      }).catch(() => {
        this.sysTypeLoading = false
        this.sysTypeOptions = []
      })
    },

    // 系统字典类型变化
    onSysTypeChange(value) {
      this.mappingList = []
      this.originalData = []
    },

    // 加载映射数据
    async loadMappingData() {
      if (!this.selectedSysType) {
        this.$message.warning('请先选择系统字典类型')
        return
      }

      this.dataLoading = true
      try {
        // 获取系统字典值列表
        const sysValuesRes = await this.$http({
          url: this.$http.adornUrl('/admin/dict/parent'),
          method: 'get',
          params: this.$http.adornParams({ code: this.selectedSysType })
        })

        if (sysValuesRes.data && sysValuesRes.data.code === 0) {
          const sysValues = sysValuesRes.data.obj || []
          
          // 获取现有的映射关系
          const mappingsRes = await this.$http({
            url: this.$http.adornUrl('/admin/sgb/dict-mapping/pages'),
            method: 'post',
            data: this.$http.adornData({
              currentPage: 1,
              pageSize: 1000,
              sysType: this.selectedSysType
            })
          })

          const existingMappings = mappingsRes.data && mappingsRes.data.code === 0 
            ? (mappingsRes.data.obj.records || []) 
            : []

          // 构建映射列表
          this.mappingList = sysValues.map((sysValue, index) => {
            const existingMapping = existingMappings.find(m => m.sysValue === sysValue.code)

            const item = {
              id: existingMapping ? existingMapping.id : null,
              version: existingMapping ? existingMapping.version : null,
              sysType: this.selectedSysType,
              sysValue: sysValue.code,
              sysValueText: sysValue.name,
              thirdType: existingMapping ? existingMapping.thirdType : null,
              thirdValue: existingMapping && existingMapping.thirdValue
                ? existingMapping.thirdValue.split(',').filter(v => v.trim())
                : [],
              thirdTypeOptions: [],
              thirdValueOptions: [],
              thirdTypeLoading: false,
              thirdValueLoading: false
            }

            return item
          })

          // 为所有项目初始化第三方字典类型选项
          this.mappingList.forEach((item, index) => {
            // 初始化第三方字典类型选项
            this.loadThirdTypeOptions(index)

            // 如果已有第三方字典类型，则加载对应的字典值选项
            if (item.thirdType) {
              this.loadThirdValueOptions(index)
            }
          })

          // 保存原始数据用于对比
          this.originalData = JSON.parse(JSON.stringify(this.mappingList))
          
          this.$message.success(`加载完成，共 ${this.mappingList.length} 条数据`)
        }
      } catch (error) {
        this.$message.error('加载数据失败')
        console.error(error)
      } finally {
        this.dataLoading = false
      }
    },

    // 加载第三方字典类型选项
    loadThirdTypeOptions(index) {
      const item = this.mappingList[index]
      if (!item) return

      item.thirdTypeLoading = true
      this.$http({
        url: this.$http.adornUrl('/admin/sgb/dict/types'),
        method: 'get'
      }).then(({data}) => {
        item.thirdTypeLoading = false
        if (data && data.code === 0) {
          item.thirdTypeOptions = data.obj || []
        } else {
          item.thirdTypeOptions = []
        }
      }).catch(() => {
        item.thirdTypeLoading = false
        item.thirdTypeOptions = []
      })
    },

    // 确保第三方字典类型选项已加载
    ensureThirdTypeOptions(index) {
      const item = this.mappingList[index]
      if (!item) return

      // 如果选项为空，则加载
      if (item.thirdTypeOptions.length === 0) {
        this.loadThirdTypeOptions(index)
      }
    },

    // 第三方字典类型变化
    onThirdTypeChange(value, index) {
      const item = this.mappingList[index]
      if (!item) return

      // 清空第三方字典值
      item.thirdValue = []
      item.thirdValueOptions = []

      // 如果选择了第三方字典类型，则获取对应的字典值
      if (value) {
        this.loadThirdValueOptions(index)
      }
    },

    // 加载第三方字典值选项
    loadThirdValueOptions(index) {
      const item = this.mappingList[index]
      if (!item || !item.thirdType) {
        if (item) {
          item.thirdValueOptions = []
        }
        return
      }

      item.thirdValueLoading = true
      this.$http({
        url: this.$http.adornUrl('/admin/sgb/dict/type'),
        method: 'get',
        params: this.$http.adornParams({ type: item.thirdType })
      }).then(({data}) => {
        item.thirdValueLoading = false
        if (data && data.code === 0) {
          item.thirdValueOptions = data.obj || []
        } else {
          item.thirdValueOptions = []
        }
      }).catch(() => {
        item.thirdValueLoading = false
        item.thirdValueOptions = []
      })
    },

    // 清空映射
    clearMapping(index) {
      const item = this.mappingList[index]
      if (!item) return

      item.thirdType = null
      item.thirdValue = []
      item.thirdTypeOptions = []
      item.thirdValueOptions = []
    },

    // 清空所有映射
    clearAllMappings() {
      this.mappingList.forEach(item => {
        item.thirdType = null
        item.thirdValue = []
        item.thirdTypeOptions = []
        item.thirdValueOptions = []
      })
      this.$message.success('已清空所有映射关系')
    },

    // 判断项目是否有变化
    isItemChanged(item) {
      const original = this.originalData.find(o => o.sysValue === item.sysValue)
      if (!original) return false

      // 比较第三方字典类型
      if (original.thirdType !== item.thirdType) return true

      // 比较第三方字典值（数组）
      const originalValues = Array.isArray(original.thirdValue) ? original.thirdValue : []
      const currentValues = Array.isArray(item.thirdValue) ? item.thirdValue : []

      if (originalValues.length !== currentValues.length) return true

      return !originalValues.every(val => currentValues.includes(val))
    },

    // 批量保存
    async batchSave() {
      if (!this.hasChanges) {
        this.$message.warning('没有需要保存的变化')
        return
      }

      // 验证所有系统字典值都必须有对应的映射
      const unmappedItems = this.mappingList.filter(item => {
        const hasThirdValue = Array.isArray(item.thirdValue) ? item.thirdValue.length > 0 : !!item.thirdValue
        return !item.thirdType || !hasThirdValue
      })

      if (unmappedItems.length > 0) {
        const unmappedNames = unmappedItems.map(item => item.sysValueText).join('、')
        this.$message.error(`以下系统字典值必须配置完整的第三方映射：${unmappedNames}`)
        return
      }

      // 获取有变化的数据
      const changedItems = this.mappingList.filter(item => this.isItemChanged(item))

      // 构建保存数据
      const saveData = changedItems.map(item => ({
        id: item.id,
        version: item.version,
        sysType: item.sysType,
        sysValue: item.sysValue,
        thirdType: item.thirdType,
        thirdValue: Array.isArray(item.thirdValue) ? item.thirdValue.join(',') : item.thirdValue
      }))

      this.saveLoading = true
      try {
        const response = await this.$http({
          url: this.$http.adornUrl('/admin/sgb/dict-mapping/batch-save'),
          method: 'post',
          data: saveData
        })

        if (response.data && response.data.code === 0) {
          this.$message.success(`批量保存成功，共处理 ${changedItems.length} 条数据`)

          // 更新原始数据
          this.originalData = JSON.parse(JSON.stringify(this.mappingList))

          // 触发父组件刷新
          this.$emit('refreshDataList')

          // 关闭对话框
          this.visible = false
        } else {
          this.$message.error(response.data.msg || '批量保存失败')
        }
      } catch (error) {
        this.$message.error('批量保存失败')
        console.error(error)
      } finally {
        this.saveLoading = false
      }
    },

    // 关闭对话框
    handleClose() {
      if (this.hasChanges) {
        this.$confirm('有未保存的变化，确定要关闭吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.visible = false
        }).catch(() => {
          // 用户取消关闭
        })
      } else {
        this.visible = false
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.dialog-footer {
  text-align: right;
}

// 表格样式优化
.el-select {
  .el-input__inner {
    border: 1px solid #dcdfe6;
    
    &:hover {
      border-color: #c0c4cc;
    }
    
    &:focus {
      border-color: #409eff;
    }
  }
}
</style>
