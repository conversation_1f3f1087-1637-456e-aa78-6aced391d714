<template>
  <div class="mod-list">
    <el-form :inline="true" :model="dataForm" ref="dataForm" @keyup.enter.native="getDataList()">
      <el-form-item prop="value" label="字典值">
        <el-input v-model="dataForm.value" placeholder="请输入字典值" clearable></el-input>
      </el-form-item>
      <el-form-item prop="label" label="字典标签">
        <el-input v-model="dataForm.label" placeholder="请输入字典标签" clearable></el-input>
      </el-form-item>
      <el-form-item prop="type" label="字典类型">
        <el-select
          v-model="dataForm.type"
          filterable
          remote
          allow-create
          default-first-option
          reserve-keyword
          placeholder="请选择或输入字典类型"
          :remote-method="getTypeOptions"
          :loading="typeLoading"
          clearable
          style="width: 280px;">
          <el-option
            v-for="item in typeOptions"
            :key="item.id"
            :label="`${item.text} - ${item.id}`"
            :value="item.id">
            <span style="float: left">{{ item.text }}</span>
            <span style="float: right; color: #8492a6; font-size: 13px; margin-left: 30px;">{{ item.id }}</span>
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item prop="describe" label="字典描述">
        <el-input v-model="dataForm.describe" placeholder="请输入字典描述" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-search" type="warning" @click="getDataList()">查询</el-button>
        <el-button @click="resetForm()"icon="el-icon-refresh-left">重置</el-button>
      </el-form-item>
    </el-form>
    <div class="f-s-c" style="padding: 15px 0; display: flex; justify-content: flex-end; align-items: center">
      <div>
        <el-button type="primary" @click="syncConfirm()" icon="el-icon-refresh" :disabled="syncLoading" :loading="syncLoading">同步</el-button>
      </div>
    </div>
    <el-table :data="dataList" border v-loading="dataListLoading" @selection-change="selectionChangeHandle" style="width: 100%;">
<!--      <el-table-column type="selection" header-align="center" align="center" width="50"/>-->
      <el-table-column prop="value" header-align="center" align="center" label="字典值"/>
      <el-table-column prop="label" header-align="center" align="center" label="字典标签"/>
      <el-table-column prop="type" header-align="center" align="center" label="字典类型"/>
      <el-table-column prop="describe" header-align="center" align="center" label="字典描述"/>
      <el-table-column prop="updateDate" header-align="center" align="center" label="更新时间"/>
      <!-- 操作列已隐藏 -->
    </el-table>
    <el-pagination @size-change="sizeChangeHandle" @current-change="currentChangeHandle" :current-page="pageIndex"
                   :page-sizes="[10, 20, 50, 100]" :page-size="pageSize" :total="totalPage" layout="total, sizes, prev, pager, next, jumper"/>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"/>
  </div>
</template>

<script>
  import AddOrUpdate from './dict-add-or-update'
  import listMixin from '@/mixins/list-mixins'
  export default {
    mixins: [listMixin],
    data () {
      return {
        mixinOptions: {
          dataUrl: '/admin/sgb/dict/pages',
          deleteUrl: '/admin/sgb/dict/removeByIds'
        },
        dataForm: {
          value: null,
          label: null,
          type: null,
          describe: null,
          orders: [
            {column: 'type', sort: 'asc'},
            {column: 'value', sort: 'asc'}
          ]
        },
        typeOptions: [],
        typeLoading: false,
        syncLoading: false
      }
    },
    components: {
      AddOrUpdate
    },
    created() {
      this.getTypeOptions('')
    },
    methods: {
      resetForm() {
        this.$refs['dataForm']?.resetFields()
        this.getDataList()
      },
      syncConfirm() {
        this.$confirm('确定同步字典数据吗?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.syncHandle()
        }).catch(() => {
          this.$message.info('已取消同步')
        })
      },
      syncHandle() {
        this.syncLoading = true
        this.dataListLoading = true
        this.$http({
          url: this.$http.adornUrl('/admin/sgb/dict/sync'),
          method: 'post',
          data: this.$http.adornData({})
        }).then(({data}) => {
          this.syncLoading = false
          this.dataListLoading = false
          if (data && data.code === 0) {
            this.$message.success('同步成功')
            this.getDataList()
          } else {
            this.$message.error(data.msg || '同步失败')
          }
        }).catch(() => {
          this.syncLoading = false
          this.dataListLoading = false
          this.$message.error('同步失败')
        })
      },
      getTypeOptions(query) {
        if (query !== '') {
          this.typeLoading = true
          this.$http({
            url: this.$http.adornUrl('/admin/sgb/dict/types'),
            method: 'get',
            params: this.$http.adornParams({ query: query })
          }).then(({data}) => {
            this.typeLoading = false
            if (data && data.code === 0) {
              this.typeOptions = data.obj || []
            }
          }).catch(() => {
            this.typeLoading = false
          })
        } else {
          this.$http({
            url: this.$http.adornUrl('/admin/sgb/dict/types'),
            method: 'get'
          }).then(({data}) => {
            if (data && data.code === 0) {
              this.typeOptions = data.obj || []
            }
          })
        }
      }
    }
  }
</script>

<style lang="scss" scoped>
</style>
