<template>
  <el-dialog class="common-dialog" :title="!dataForm.id ? '新增' : '修改'" :visible.sync="visible"
             :close-on-click-modal="false" :modal-append-to-body="false" width="60%">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmit()" label-width="90px">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="业务类型" prop="businessType">
            <el-input v-model="dataForm.businessType" placeholder="请输入" clearable/>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="业务ID" prop="businessId">
            <el-input v-model="dataForm.businessId" placeholder="请输入" clearable/>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="同步状态：0-未同步，1-已同步" prop="syncStatus">
            <el-radio-group v-model="dataForm.syncStatus">
              <el-radio :label="true">启用</el-radio>
              <el-radio :label="false">禁用</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="同步备注" prop="syncRemark">
            <el-input v-model="dataForm.syncRemark" placeholder="请输入" clearable/>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="社工部业务ID" prop="sgbId">
            <el-input v-model="dataForm.sgbId" placeholder="请输入" clearable/>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="同步时间" prop="syncTime">
            <el-date-picker
              v-model="dataForm.syncTime"
              value-format="yyyy-MM-dd HH:mm:ss"
              type="datetime"
              placeholder="请选择"
              clearable>
            </el-date-picker>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()" :disabled="dataFormLoading">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
  import moment from 'moment'
  import _ from 'lodash'
  import editMixin from '@/mixins/edit-mixins'
  export default {
    mixins: [editMixin],
    data () {
      return {
        editOptions: {
          initUrl: '/admin/sgb/sync-status-record'
        },
        dataForm: {},
        initForm: {
          id: null,
          version: null,
          businessType: null,
          businessId: null,
          syncStatus: null,
          syncRemark: null,
          sgbId: null,
          syncTime: null
        },
        dataRule: {
          businessType: [
            { required: true, message: '业务类型不能为空', trigger: 'change' }
          ],
          businessId: [
            { required: true, message: '业务ID不能为空', trigger: 'change' }
          ],
          syncStatus: [
            { required: true, message: '同步状态：0-未同步，1-已同步不能为空', trigger: 'change' }
          ],
          syncRemark: [
            { required: true, message: '同步备注不能为空', trigger: 'change' }
          ],
          sgbId: [
            { required: true, message: '社工部业务ID不能为空', trigger: 'change' }
          ],
          syncTime: [
            { required: true, message: '同步时间不能为空', trigger: 'change' }
          ]
        }
      }
    },
    components: {
        moment
    },
    methods: {
      init (id) {
        this.dataForm = _.cloneDeep(this.initForm)
        this.dataForm.id = id || null
        this.visible = true
        this.$nextTick(() => {
          this.$refs['dataForm']?.resetFields()
          if (this.dataForm.id) {
            this.getData()
          }
        })
      }
    }
  }
</script>

<style lang="scss" scoped>
</style>
