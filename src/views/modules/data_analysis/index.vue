<template>
  <div>
    <div style="display: flex;">
      <div class="top-wrap">
        <div class="top-row1">累计志愿者数</div>
        <div class="top-row2">246079</div>
      </div>
      <div class="top-wrap">
        <div class="top-row1">志愿者占常驻人口百分比</div>
        <div class="top-row2">26.53%</div>
      </div>
    </div>
    <div class="table-box">
      <div class="table-box-item">
        <el-card class="table-sum-card" shadow="hover">
          <div class="dashboard-title">分协会累计数据</div>
          <el-table
            :data="subAssociationDataSumResult"
            style="width: 100%">
            <el-table-column
              label="排名"
              width="50"
              type="index">
              <template slot-scope="scoped">
                <div class="sort">{{scoped.$index + 1}}</div>
              </template>
            </el-table-column>
            <el-table-column
              align="center"
              prop="subAssociationName"
              label="街道"
              width="220">
            </el-table-column>
            <el-table-column
              align="center"
              prop="volunteerNum"
              label="志愿者数">
            </el-table-column>
            <el-table-column
              align="center"
              width="100"
              prop="activeVolunteerNum"
              label="本月活跃数">
            </el-table-column>
            <el-table-column
              align="center"
              prop="teamNum"
              label="团队数">
            </el-table-column>
            <el-table-column
              align="center"
              prop="actNum"
              label="活动数">
            </el-table-column>
            <el-table-column
              align="center"
              width="100"
              prop="serviceTimeTotal"
              label="总服务时长">
            </el-table-column>
          </el-table>
        </el-card>
      </div>
      <div class="table-box-item">
        <el-card class="table-sum-card" shadow="hover">
          <div class="echarts-wrap" ref="echartsMain"></div>
        </el-card>
      </div>
    </div>
  </div>
</template>
<script>
import * as sr from '../dashboard/sum_request.js'
import * as echarts from 'echarts'
export default {
  name: "index",
  components: {
  },
  mounted() {
    this.dataInit()
  },
  data () {
    return {
      sr: sr,
      option: '',
      myChart: null,
      subAssociationDataSumResult: null
    }
  },
  methods: {
    dataInit() {
      this.option = {
        title: {
          text: ''
        },
        tooltip: {
          trigger: 'item'
        },
        legend: {
          data: ['金鸡湖街道', '娄葑街道', '唯亭街道', '胜浦街道', '斜塘街道']
        },
        grid: {
          left: '3%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: ['2018', '2019', '2020', '2021', '2022'],
          axisLabel: {
            rotate: -45
          },
        },
        yAxis: {
          type: 'value'
        },
        series: [
          {
            name: '金鸡湖街道',
            type: 'line',
            symbol: 'circle',
            symbolSize: 8,
            data: [5264, 4056, 3641, 3614, 11205]
          },
          {
            name: '娄葑街道',
            type: 'line',
            symbol: 'circle',
            symbolSize: 8,
            data: [2580, 2164, 1348, 1241, 8099]
          },
          {
            name: '唯亭街道',
            type: 'line',
            symbol: 'circle',
            symbolSize: 8,
            data: [4471, 1165, 3307, 1144, 4911]
          },
          {
            name: '胜浦街道',
            type: 'line',
            symbol: 'circle',
            symbolSize: 8,
            data: [2814, 1476, 1677, 800, 2778]
          },
          {
            name: '斜塘街道',
            type: 'line',
            symbol: 'circle',
            symbolSize: 8,
            data: [1809, 2146, 2021, 1681, 3343]
          }
        ]
      },
      this.getChart()
      this.getSubAssociationDataSumResult()
    },
    async getSubAssociationDataSumResult () {
      this.subAssociationDataSumResult = await sr.subAssociationDataSum()
    },
    getChart() {
      this.$nextTick(() => {
        this.myChart = echarts.init(this.$refs.echartsMain)
        this.myChart.setOption(this.option)
      })
    }
  }
}
</script>
<style scoped lang="scss">
.top-wrap{
  width: 200px;
  border: 1px solid #eae9e9;
  border-radius: 3px;
  padding: 15px;
  color: #fc8452;
  margin-right: 20px;
  .top-row1{
    margin-bottom: 10px;
  }
  .top-row2{
    font-weight: bold;
    font-size: 18px;
  }
}
.table-box{
  display: flex;
  gap: 10px;
  padding: 24px 0 24px 0;
  .table-box-item{
    width: 50%;
    border: 1px solid #eae9e9;
    box-shadow: 3px 3px 5px 0px rgba(0,0,0,0.2);
    min-height: 400px;
    .table-box-item-main{
      display: flex;
    }
    ::v-deep .el-card__body {
      height: 100%;
    }
    .echarts-wrap{
      width: 100%;
      height: 400px;
    }
  }
  .sort{
    background-color: #F59A23;
    color: #fff;
    border-radius: 50%;
    width: 30px;
    height: 30px;
    display: flex;
    justify-content: center;
    align-items: center;
  }
}
</style>
