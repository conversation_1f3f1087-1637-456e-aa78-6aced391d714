<template>
  <el-dialog
    :title="!dataForm.sortNum ? '参数添加' : '参数修改'"
    :close-on-click-modal="false"
    width="50%"
    :modal="false"
    center
    :visible.sync="visible">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmit()" label-width="110px">
      <el-row :gutter="20" >
        <el-col :span="12"><div class="grid-content bg-purple">
          <el-form-item label="参数序号" prop="sortNum">
            <el-input v-model="dataForm.sortNum" size="mini" :disabled="this.originSortNum != null ? true : false" placeholder="请输入"></el-input>
          </el-form-item>
        </div></el-col>
        <el-col :span="12"><div class="grid-content bg-purple">
          <el-form-item label="参数类型" prop="type">
            <el-select v-model="dataForm.type" size="mini" clearable placeholder="请选择">
              <el-option
                v-for="item in option"
                :label="item.name"
                :key="item.name"
                :value="item.name">
              </el-option>
            </el-select>
          </el-form-item>
        </div></el-col>
      </el-row>
      <el-row :gutter="20" >
        <el-col :span="12"><div class="grid-content bg-purple">
          <el-form-item label="参数名" prop="name">
            <el-input v-model="dataForm.name" size="mini" placeholder="请输入"></el-input>
          </el-form-item>
        </div></el-col>
        <el-col :span="12"><div class="grid-content bg-purple">
          <el-form-item label="是否必填" prop="need">
            <el-radio-group  v-model="dataForm.need">
              <el-radio :label="false">否</el-radio>
              <el-radio :label="true">是</el-radio>
            </el-radio-group>
          </el-form-item>
        </div></el-col>
      </el-row>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
  import {isNum} from '../../../utils/validate'
  export default {
    data () {
      var validateNum = (rule, value, callback) => {
        if (!isNum(value)) {
          callback(new Error('参数序号应为数字'))
        } else {
          callback()
        }
      }
      var validateSortNum = (rule, value, callback) => {
        if (this.originSortNum == null) {
          var sortNum = []
          for (var i = 0; i < this.dataList.length; i++) {
            sortNum.push(this.dataList[i].sortNum)
          }
          if (sortNum.indexOf(value) > -1 || sortNum.indexOf(Number(value)) > -1 || sortNum.indexOf(value.toString()) > -1) {
            callback(new Error('参数序号不应重复'))
          } else {
            callback()
          }
        } else {
          callback()
        }
      }
      return {
        visible: false,
        dataListLoading: false,
        dataList: [],
        option: [
          {
            name: 'Number'
          },
          {
            name: 'String'
          }
        ],
        originSortNum: null,
        dataForm: {
          id: null,
          name: '',
          type: '',
          sortNum: '',
          need: null,
          urlId: '',
          urlCode: '',
          createDate: '',
          createor: '',
          version: null
        },
        dataRule: {
          sortNum: [
            { required: true, message: '参数序号不能为空', trigger: 'blur' },
            { validator: validateNum, trigger: 'blur' },
            { validator: validateSortNum, trigger: 'blur' }
          ],
          name: [
            { required: true, message: '参数名不能为空', trigger: 'blur' }
          ],
          type: [
            { required: true, message: '参数类型不能为空', trigger: 'blur' }
          ],
          need: [
            { required: true, message: '是否必填不能为空', trigger: 'blur' }
          ]
        }
      }
    },
    methods: {
      init (val1, val2) {
        this.visible = true
        this.dataListLoading = true
        this.dataForm.sortNum = val2
        this.originSortNum = val2
        this.dataList = val1
        if (val2 == null) {
          var max
          if (val1[0] != null) {
            max = val1[0].sortNum
            for (var x = 0; x < val1.length; x++) {
              if (val1[x].sortNum > max) {
                max = val1[x].sortNum
              }
            }
          } else {
            max = -1
          }
          this.dataForm.name = ''
          this.dataForm.type = ''
          this.dataForm.sortNum = Number(max) + 1
          this.dataForm.need = null
        }
        for (var i = 0; i < val1.length; i++) {
          if (val1[i].sortNum === val2) {
            this.dataForm.type = val1[i].type
            this.dataForm.name = val1[i].name
            this.dataForm.need = val1[i].need
            break
          }
        }
        this.dataListLoading = false
      },
      // 表单提交
      dataFormSubmit () {
        this.$refs['dataForm'].validate((valid) => {
          if (valid) {
            this.visible = false
            const input = JSON.parse(JSON.stringify(this.dataForm))
            this.$emit('refreshDataList', input)
          }
        })
      }
    }
  }
</script>
