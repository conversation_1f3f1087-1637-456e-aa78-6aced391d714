<template>
  <el-dialog
      :title="privilegeChange ? '授权变更' : '授权'"
      :close-on-click-modal="false"
      width="60%"
      :visible.sync="visible">
    <el-form :model="dataForm" ref="dataForm" label-width="80px">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item v-if="!privilegeChange" label="用户搜索" prop="searchKeyword">
            <el-select style="width: 100%" v-model="dataForm.searchKeyword" filterable remote placeholder="请输入用户名/手机号/姓名"
                       value-key="id" :remote-method="searchUser" :multiple-limit="1" :loading="searchLoading" @change="(value) => userSelected(value)" clearable no-data-text="该账号不存在，请先前往小程序注册！">
              <el-option
                  v-for="item in userList"
                  :key="item.id"
                  :label="item.name"
                  :value="item">
                <span style="float: left">{{ item.name }}</span>
                <span style="float: right; color: #8492a6; font-size: 13px">{{ item.mobile + '(手机号)--' + item.username + '(用户名)' }}</span>
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="用户名" prop="username">
            <el-input placeholder="用户名" v-model="dataForm.username" disabled></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="手机号" prop="mobile">
            <el-input placeholder="手机号" v-model="dataForm.mobile" disabled></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="姓名" prop="name">
            <el-input placeholder="姓名" v-model="dataForm.name" disabled></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="权限树" prop="privilegeTree">
            <el-tree
                id="privilege_tree"
                ref="privilegeTree"
                :data="orgTree"
                :props="{label: 'name', children: 'children'}"
                node-key="code"
                :check-strictly="true"
                show-checkbox
                :default-expand-all="true"
                :render-content="renderContent"
                :node-expand="handleExpand"
                @check-change="handleCheckChange">
            </el-tree>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()" :disabled="submitLoading">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  name: "account-privilege-option",
  data () {
    return {
      visible: false,
      searchLoading: false,
      privilegeChange: false,
      orgTree: [],
      userList: [],
      dataForm: {
        id: null,
        searchKeyword: null,
        username: null,
        mobile: null,
        name: null
      },
      submitLoading: false
    }
  },
  methods: {
    async init(id, username, mobile, name) {
      this.dataForm.id = id || null
      this.dataForm.username = username || null
      this.dataForm.mobile = mobile || null
      this.dataForm.name = name || null
      this.privilegeChange = id && id !== ''
      this.dataForm.searchKeyword = null
      this.userList = []
      this.visible = true
      await this.getPrivilegeTree()
      this.$nextTick(() => {
        if (this.privilegeChange) {
          this.getUserPrivileges(id)
        } else {
          this.$refs.privilegeTree.setCheckedKeys([])
        }
      })
    },
    searchUser(value) {
      if (!value || value === '') {
        this.userList = []
        return
      }
      this.$http({
        url: this.$http.adornUrl('/admin/user/searchUser'),
        method: 'get',
        params: this.$http.adornParams({'keyword': value})
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.userList = data.obj|| []
        } else {
          this.userList = []
        }
      })
    },
    userSelected(value) {
      if (!value || value === '') {
        this.dataForm.id = null
        this.dataForm.username = null
        this.dataForm.mobile = null
        this.dataForm.name = null
        this.$refs.privilegeTree.setCheckedKeys([])
        return
      }
      this.dataForm.id = value.id
      this.dataForm.username = value.username
      this.dataForm.mobile = value.mobile
      this.dataForm.name = value.name
      this.$nextTick(() => {
        this.getUserPrivileges(value.id)
      })
    },
    async getPrivilegeTree() {
      this.$http({
        url: this.$http.adornUrl('/admin/org/getOrgTreeForSubAssociation'),
        method: 'get'
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.orgTree = data.obj
          this.orgTree[0].disabled = true
          this.$nextTick(() => {
            this.changeCss()
          })
        } else {
          this.orgTree = []
        }
      })
    },
    handleCheckChange(data, checked) {
      // console.log(data, checked)
    },
    handleExpand() {
      this.$nextTick(() => {
        this.changeCss()
      })
    },
    renderContent(h, { node }) {
      let classname = ''
      if (node.childNodes.length === 0) {
        classname = 'leaf'
      }
      return h('p', {class: classname}, node.label)
    },
    changeCss() {
      let leaves = document.getElementsByClassName('leaf')
      for (let i = 0; i < leaves.length; i++) {
        leaves[i].parentNode.style.width = '31%'
        leaves[i].parentNode.style.cssFloat = 'left'
        leaves[i].parentNode.style.styleFloat = 'left'
        leaves[i].parentNode.onmouseover = function() {
          this.style.backgroundColor = '#fff'
        }
      }
    },
    getUserPrivileges(id) {
      this.$refs.privilegeTree.setCheckedKeys([])
      this.$nextTick(() => {
        this.$http({
          url: this.$http.adornUrl('/admin/user/getUserSACPrivileges'),
          method: 'get',
          params: this.$http.adornParams({'id': id})
        }).then(({data}) => {
          if (data && data.code === 0 && data.obj && data.obj.length > 0) {
            this.$refs.privilegeTree.setCheckedKeys(data.obj)
          } else {
            this.$refs.privilegeTree.setCheckedKeys([])
          }
        })
      })
    },
    dataFormSubmit() {
      this.$confirm(`确定对该用户的授权进行变更吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.submitLoading = true
        this.$http({
          url: this.$http.adornUrl('/admin/user/privilegesChange'),
          method: 'post',
          data: this.$http.adornData({
            'id': this.dataForm.id,
            'privileges': this.$refs.privilegeTree.getCheckedKeys()
          })
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.$message({
              message: '授权变更成功',
              type: 'success',
              duration: 1500,
              onClose: () => {
                this.submitLoading = false
                this.visible = false
                this.$emit('refreshDataList')
              }
            })
          } else {
            this.submitLoading = false
            this.$message.error(data.msg)
          }
        })
      })
    }
  }
}
</script>

<style scoped lang="scss">
#privilege_tree {
  padding-top: 10px;
  ::v-deep .el-tree-node {
    position: relative;
    padding-left: 16px; // 缩进量
  }

  ::v-deep .el-tree-node__children {
    padding-left: 16px; // 缩进量
  }

  // 竖线
  ::v-deep .el-tree-node::before {
    content: "";
    height: 100%;
    width: 1px;
    position: absolute;
    left: -3px;
    top: -26px;
    border-width: 1px;
    border-left: 1px dashed #ccc;
  }

  // 当前层最后⼀个节点的竖线⾼度固定
  ::v-deep .el-tree-node:last-child::before {
    height: 38px; // 可以⾃⼰调节到合适数值
  }

  // 横线
  ::v-deep .el-tree-node::after {
    content: "";
    width: 40px;
    height: 20px;
    position: absolute;
    left: -3px;
    top: 12px;
    border-width: 1px;
    border-top: 1px dashed #ccc;
  }

  // 去掉最顶层的虚线，放最下⾯样式才不会被上⾯的覆盖了
  & > ::v-deep .el-tree-node::after {
    border-top: none;
  }

  & > ::v-deep .el-tree-node::before {
    border-left: none;
  }

  // 展开关闭的icon
  ::v-deep .el-tree-node__expand-icon {
    font-size: 16px;
    // 叶⼦节点（⽆⼦节点）
    ::v-deep &.is-leaf {
      color: transparent;
      // display: none; // 也可以去掉
    }
  }
}
</style>
