<template>
  <div style="margin: 150px">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" label-width="100px" label-position="right">
      <el-row :gutter="80">
        <el-col :span="12">
          <el-form-item label="类目名称" prop="name" :error="nameError">
            <el-input v-model="dataForm.name"  clearable placeholder="栏目名称"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="上级菜单" prop="parentName">
            <el-popover
              ref="categoryListPopover"
              placement="bottom-start"
              trigger="click">
              <el-tree
                :data="categoryList"
                :props="categoryListTreeProps"
                node-key="id"
                ref="categoryListTree"
                @current-change="categoryListTreeCurrentChangeHandle"
                :default-expand-all="false"
                :highlight-current="true"
                :accordion="true"
                :expand-on-click-node="false">
              </el-tree>
            </el-popover>
            <el-input v-model="dataForm.parentName" v-popover:categoryListPopover :readonly="true" placeholder="点击选择上级菜单" class="menu-list__input"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="80">
        <el-col :span="12">
          <el-form-item label="排序号" prop="sequence" :error="sequenceError">
            <el-input-number v-model="dataForm.sequence" controls-position="right" :min="0" label="排序号"></el-input-number>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="状态" prop="status" :error="statusError">
            <el-radio-group v-model="dataForm.status">
              <el-radio :label="true">启用</el-radio>
              <el-radio :label="false">禁用</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="80">
        <el-col :span="24">
          <el-form-item label="栏目描述" prop="remark" :error="remarkError">
            <el-input type="textarea" :rows="4" clearable v-model="dataForm.remark" :maxlength="150"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <span slot="footer" class="dialog-footer" style="float: right">
      <el-button @click="returnPage()">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </div>
</template>

<script>
  import { treeDataTranslate } from '@/utils'
  export default {
    data () {
      return {
        dataForm: {
          id: null,
          version: null,
          name: null,
          sequence: 0,
          status: true,
          remark: null,
          parentId: null,
          parentName: ''
        },
        dataRule: {
          name: [
            { required: true, message: '名称不能为空', trigger: 'blur' }
          ],
          sequence: [
            {required: true, message: '排序不能为空', trigger: 'blur'}
          ],
          status: [
            {required: true, message: '状态不能为空', trigger: 'blur'}
          ]
        },
        categoryList: [],
        categoryListTreeProps: {
          label: 'name',
          children: 'subCategories'
        },
        nameError: null,
        sequenceError: null,
        statusError: null,
        remarkError: null,
        parentIdError: null
      }
    },
    methods: {
      // 初始化接口类目编辑/新增表单页
      init (id) {
        this.dataForm.id = id || undefined
        this.getCategoryList()
        this.$nextTick(() => {
          this.$refs['dataForm']?.resetFields()
          if (!this.dataForm.id) {
            // 新增
            this.categoryListTreeSetCurrentNode()
          } else {
            // 修改
            this.$http({
              url: this.$http.adornUrl(`/admin/sys_api_category`),
              method: 'get',
              params: this.$http.adornParams({ id: this.dataForm.id })
            }).then(({data}) => {
              if (data && data.code === 0) {
                this.dataForm.id = data.obj.id
                this.dataForm.version = data.obj.version
                this.dataForm.name = data.obj.name
                this.dataForm.sequence = data.obj.sequence
                this.dataForm.status = data.obj.status
                this.dataForm.remark = data.obj.remark
                this.dataForm.parentId = data.obj.parentId
                this.categoryListTreeSetCurrentNode()
              } else {
                this.$message.error(data.msg)
              }
            })
          }
        })
      },
      // 取消编辑或新增时的回调
      returnPage () {
        this.$emit('refreshDataList', false, false)
      },
      // 获取上级菜单
      getCategoryList () {
        this.$http({
          url: this.$http.adornUrl('/admin/sys_api_category/tree'),
          method: 'get',
          params: this.$http.adornParams()
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.categoryList = treeDataTranslate(data.obj)
          } else {
            this.$message.error(data.msg)
          }
        })
      },
      // 菜单树选中
      categoryListTreeCurrentChangeHandle (data, node) {
        this.dataForm.parentId = data.id
        this.dataForm.parentName = data.name
        this.$refs[`categoryListPopover`].doClose()
      },
      // 菜单树设置当前选中节点
      categoryListTreeSetCurrentNode () {
        if (this.dataForm.parentId) {
          this.$refs.categoryListTree.setCurrentKey(this.dataForm.parentId)
          this.dataForm.parentName = (this.$refs.categoryListTree.getCurrentNode() || {})['name']
        } else {
          this.$refs.categoryListTree.setCurrentKey([])
          this.dataForm.parentName = ''
        }
      },
      // 表单提交
      dataFormSubmit () {
        this.$refs['dataForm'].validate((valid) => {
          if (valid) {
            this.clearErrors()
            this.$http({
              url: this.$http.adornUrl(`/admin/sys_api_category/${!this.dataForm.id ? 'save' : 'update'}`),
              method: 'post',
              data: this.$http.adornData(this.dataForm)
            }).then(({data}) => {
              if (data && data.code === 0) {
                this.$message({
                  message: '操作成功',
                  type: 'success',
                  duration: 1500,
                  onClose: () => {
                    this.visible = false
                    this.$emit('refreshDataList', false, true)
                  }
                })
              } else if (data && data.code === 303) {
                for (let it of data.obj) {
                  this[`${it.field}Error`] = it.message
                }
              } else {
                this.$message.error(data.msg)
              }
            })
          }
        })
      },
      clearErrors () {
        this.nameError = null
        this.sequenceError = null
        this.statusError = null
        this.remarkError = null
        this.parentIdError = null
      }
    }
  }
</script>

<style lang="scss">
  .mod-menu {
    .menu-list__input,
    .icon-list__input {
      > .el-input__inner {
        cursor: pointer;
      }
    }
    &__icon-popover {
      width: 458px;
      overflow: hidden;
    }
    &__icon-inner {
      width: 478px;
      max-height: 258px;
      overflow-x: hidden;
      overflow-y: auto;
    }
    &__icon-list {
      width: 458px;
      padding: 0;
      margin: -8px 0 0 -8px;
      > .el-button {
        padding: 8px;
        margin: 8px 0 0 8px;
        > span {
          display: inline-block;
          vertical-align: middle;
          width: 18px;
          height: 18px;
          font-size: 18px;
        }
      }
    }
    .icon-list__tips {
      font-size: 18px;
      text-align: center;
      color: #e6a23c;
      cursor: pointer;
    }
  }
</style>
