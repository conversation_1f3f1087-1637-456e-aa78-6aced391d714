<template>
  <div>
    <template v-for="menu in this.menuData">
      <el-submenu :key="menu.id" :index="menu.id" v-if="menu.subCategories" :disabled="menuDisabled">
        <template slot="title">
          <div slot="title" @click="getApiListOfCategory(menu)">{{menu.name}}</div>
        </template>
        <menu-tree :menuData="menu.subCategories" :menuDisabled="menuDisabled"></menu-tree>
      </el-submenu>
      <el-menu-item :disabled="menuDisabled" :key="menu.index" :index="menu.id" v-else @click.native="getApiListOfCategory(menu)">
        <span slot="title">{{menu.name}}</span>
      </el-menu-item>
    </template>
  </div>
</template>

<script>
export default {
  props: ['menuData', 'menuDisabled'],
  name: 'MenuTree',
  data () {
    return {
      currentCategory: [{id: '1', name: '全部', level: 0}]
    }
  },
  methods: {
    getApiListOfCategory (menu) {
      if (!this.menuDisabled) {
        var id = menu.id
        var level = menu.level
        var name = menu.name
        var parentId = menu.parentId
        var originCategory = JSON.parse(sessionStorage.getItem('currentCategory'))
        if (originCategory === null || originCategory.id !== id) {
          this.$addStorageEvent(2, 'currentCategory', JSON.stringify({'id': id, 'name': name, 'level': level, 'parentId': parentId}))
        }
      }
    }
  }
}
</script>

<style lang="scss" scoped>

</style>
