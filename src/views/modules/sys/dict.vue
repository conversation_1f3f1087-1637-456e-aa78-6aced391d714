<template>
  <div class="mod-dict">
    <el-row>
      <el-col :span="6">
        <el-card shadow="hover" class="box-card">
          <el-input
            placeholder="输入关键字进行过滤"
            v-model="filterText">
          </el-input>
          <el-tree
            class="filter-tree"
            :data="treeData"
            node-key="id"
            :props="props"
            :highlight-current="true"
            :expand-on-click-node="false"
            :default-expanded-keys="['rootKey']"
            :filter-node-method="filterNode"
            @current-change="currentChange"
            ref="tree">
          </el-tree>
        </el-card>
      </el-col>
      <el-col :span="18">
        <el-card shadow="hover" style="margin-left: 20px">
          <el-form :model="data" ref="dataForm" label-width="80px">
            <el-row :gutter="20">
              <el-form-item>
                <el-button v-if="isAuth('sys:dict:save')" type="primary" @click="addHandle()">新增</el-button>
                <el-button v-if="isAuth('sys:dict:save')" type="primary" @click="batchAddVisible = true">批量新增
                </el-button>
                <el-button v-if="isAuth('sys:dict:update')" type="warning" @click="updateHandle()">编辑</el-button>
                <el-button v-if="isAuth('sys:dict:delete')" type="danger" @click="deleteHandle()">删除</el-button>
              </el-form-item>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="10" :offset="2">
                <el-form-item label="字典名称:" prop="name">
                  <span align="left" v-model="data.name" :title="data.name">{{ data.name }}</span>
                </el-form-item>
              </el-col>
              <el-col :span="10">
                <el-form-item label="字典编码:" prop="code">
                  <span align="left" v-model="data.code" :title="data.code">{{ data.code }}</span>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="10" :offset="2">
                <el-form-item label="上级字典:" prop="parentName">
                  <span align="left" v-model="data.parentName" :title="data.parentName">{{ data.parentName }}</span>
                </el-form-item>
              </el-col>
              <el-col :span="10">
                <el-form-item label="排序:" prop="sequence">
                  <span align="left" v-model="data.sequence" :title="data.sequence">{{ data.sequence }}</span>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="10" :offset="2">
                <el-form-item label="启用状态:" prop="status">
                  <span align="left" v-model="data.status">{{ data.status ? '启用' : '禁用' }}</span>
                </el-form-item>
              </el-col>
              <el-col :span="10">
                <el-form-item label="数据值:" prop="value">
                  <span align="left" v-model="data.value" :title="data.value">{{ data.value }}</span>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </el-card>
      </el-col>
    </el-row>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"></add-or-update>
    <el-dialog
      :title="'批量新增'"
      :close-on-click-modal="false"
      :visible.sync="batchAddVisible">
      <el-form :model="batchAddForm" label-width="120px">
        <el-form-item label="父类数据字典" prop="parentId">
          <el-input v-model="data.name" placeholder="父类数据字典" disabled></el-input>
        </el-form-item>
        <el-form-item label="字典字符串" prop="batchAddText">
          <el-input v-model="batchAddText" placeholder="字符串" type="textarea" :rows="5"></el-input>
          <pre style="color:red;line-height: 15px">
            *格式：
              数据字典A dataCodeA
              数据字典B dataCodeB
              数据字典C dataCodeC
              ...
              先name后code中间空格，换行
          </pre>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
      <el-button @click="batchAddVisible = false">取消</el-button>
      <el-button type="primary" @click="batchAddHandle()">确定</el-button>
    </span>
    </el-dialog>
  </div>
</template>

<script>
import AddOrUpdate from './dict-add-or-update'

export default {
  watch: {
    filterText (val) {
      this.$refs.tree.filter(val)
    }
  },
  data () {
    return {
      batchAddForm: {},
      filterText: '',
      treeData: [],
      props: {
        key: 'id',
        label: 'name',
        children: 'children'
      },
      data: {
        id: null,
        code: '',
        name: '',
        parentName: '',
        sequence: '',
        status: '',
        value: ''
      },
      batchAddText: '',
      addOrUpdateVisible: false,
      batchAddVisible: false
    }
  },
  components: {
    AddOrUpdate
  },
  activated () {
    this.getDataList()
  },
  methods: {
    filterNode (value, data) {
      if (!value) return true
      return data.name.indexOf(value) !== -1
    },
    // 获取数据列表
    getDataList () {
      this.$http({
        url: this.$http.adornUrl('/admin/dict/tree'),
        method: 'get',
        params: this.$http.adornParams()
      }).then(({data}) => {
        this.treeData = [{
          id: 'rootKey',
          name: '所有数据字典',
          children: data.obj
        }]
      })
    },
    currentChange (selectNode) {
      if (selectNode.id === 'rootKey') {
        return
      }
      if (selectNode) {
        this.data.id = selectNode.id
      }
      this.$http({
        url: this.$http.adornUrl('/admin/dict'),
        method: 'get',
        params: this.$http.adornParams({
          id: this.data.id
        })
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.data = data.obj
        } else {
          this.data = {}
        }
      })
    },
    // 新增
    addHandle () {
      this.addOrUpdateVisible = true
      this.$nextTick(() => {
        this.$refs.addOrUpdate.init(null, this.data.id)
      })
    },
    // 批量新增
    batchAddHandle () {
      this.$http({
        url: this.$http.adornUrl(`/admin/dict/batchAdd`),
        method: 'post',
        params: this.$http.adornParams({
          parentId: this.data.id,
          text: this.batchAddText
        })
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.$message({
            message: '操作成功',
            type: 'success',
            duration: 1500,
            onClose: () => {
              this.batchAddVisible = false
              this.getDataList()
            }
          })
        } else {
          this.$message.error(data.msg)
        }
      })
    },
    // 修改
    updateHandle () {
      this.addOrUpdateVisible = true
      this.$nextTick(() => {
        this.$refs.addOrUpdate.init(this.data.id)
      })
    },
    // 删除
    deleteHandle (id) {
      if (!this.data.id) {
        this.$message.error('请选择字典')
        return
      }
      this.$confirm(`确定进行删除操作?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        closeOnClickModal: false
      }).then(() => {
        this.$http({
          url: this.$http.adornUrl(`/admin/dict/removeByIds`),
          method: 'get',
          params: this.$http.adornParams({'ids': this.data.id})
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.$message({
              message: '操作成功',
              type: 'success',
              duration: 1500,
              onClose: () => {
                this.getDataList()
                this.data = {}
              }
            })
          } else {
            this.$message.error(data.msg)
          }
        })
      })
    }
  }
}
</script>
