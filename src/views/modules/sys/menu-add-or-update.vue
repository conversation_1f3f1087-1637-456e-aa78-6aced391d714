<template>
  <div v-loading="isLoading">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmit()"
             label-width="80px">
      <!--      <el-form-item label="类型" prop="type">-->
      <!--        <el-radio-group v-model="dataForm.type">-->
      <!--          <el-radio v-for="(type, index) in dataForm.typeList" :label="index+1" :key="index">{{ type }}</el-radio>-->
      <!--        </el-radio-group>-->
      <!--      </el-form-item>-->
      <el-form-item :label="'菜单名'" prop="name">
        <el-input v-model="dataForm.name" :placeholder="'菜单名'"></el-input>
      </el-form-item>
      <el-form-item label="上级菜单" prop="parentName">
        <el-popover
            ref="menuListPopover"
            placement="bottom-start"
            trigger="click">
          <el-tree
              :data="menuList"
              :props="menuListTreeProps"
              node-key="id"
              :default-expanded-keys="[getRootPID(),dataForm.id]"
              ref="menuListTree"
              @current-change="menuListTreeCurrentChangeHandle"
              :highlight-current="true"
              :expand-on-click-node="false">
          </el-tree>
        </el-popover>
        <el-input v-model="dataForm.parentName" v-popover:menuListPopover :readonly="true" placeholder="点击选择上级菜单"
                  class="menu-list__input"></el-input>
      </el-form-item>
      <el-form-item label="菜单路由" prop="url">
        <el-input v-model="dataForm.url" placeholder="菜单路由">
          <template slot="append">
            <el-select v-model="dataForm.openTarget" style="width:120px">
              <el-option
                  :key="'_self'"
                  :label="'窗口内打开'"
                  :value="'_self'">
              </el-option>
              <el-option
                  :key="'_blank'"
                  :label="'新窗口'"
                  :value="'_blank'">
              </el-option>
            </el-select>
          </template>
        </el-input>
      </el-form-item>
      <!--      <el-form-item label="菜单标识" prop="code">-->
      <!--        <el-input v-model="dataForm.code" placeholder="请输入菜单标识"></el-input>-->
      <!--      </el-form-item>-->
      <el-form-item label="状态" prop="status">
        <el-radio-group v-model="dataForm.status">
          <el-radio :label="true">启用</el-radio>
          <el-radio :label="false">禁用</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="排序号" prop="sequence">
        <el-input-number v-model="dataForm.sequence" controls-position="right" :min="0" label="排序号"></el-input-number>
      </el-form-item>
      <el-form-item label="菜单图标" prop="icon">
        <el-row>
          <el-col :span="22">
            <el-popover
                ref="iconListPopover"
                placement="bottom-start"
                trigger="click"
                popper-class="mod-menu__icon-popover">
              <div class="mod-menu__icon-inner">
                <div class="mod-menu__icon-list">
                  <el-button
                      v-for="(item, index) in iconList"
                      :key="index"
                      @click="iconActiveHandle(item)"
                      :class="{ 'is-active': item === dataForm.icon }">
                    <icon-svg :name="item"></icon-svg>
                  </el-button>
                </div>
              </div>
            </el-popover>
            <el-input v-model="dataForm.icon" v-popover:iconListPopover :readonly="true" placeholder="菜单图标名称"
                      class="icon-list__input"></el-input>
          </el-col>
        </el-row>
      </el-form-item>
      <el-form-item label="图片图标" prop="iconPicture">
        <el-upload
            class="avatar-uploader"
            :action="this.$http.adornUrl(`/admin/oss/upload`)"
            :headers="headers"
            :data="{serverCode: uploadOptions.serverCode, media: false}"
            :show-file-list="false"
            :on-success="successHandle"
            :on-change="changHandle"
            :on-exceed="exceedHandle"
            :before-upload="beforeUploadHandle">
          <img v-if="dataForm.iconPicture" :src="$http.adornAttachmentUrl(dataForm.iconPicture)" class="avatar">
          <i v-else class="el-icon-plus avatar-uploader-icon"></i>
        </el-upload>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="resetForm(dataForm.id)">重置</el-button>
      <el-button type="primary" @click="dataFormSubmit()">保存菜单</el-button>
    </span>
  </div>
</template>

<script>
import {treeDataTranslate} from '@/utils'
import Icon from '@/icons'
import _ from 'lodash'
import fileUploadMixin from '@/mixins/file-upload-mixins'

const rootPID = 9999999999999

export default {
  mixins: [fileUploadMixin],
  data() {
    return {
      visible: false,
      isLoading: false,
      bakDataForm: {},
      uploadOptions: {
        fieldName: 'iconPicture',
        maxSize: 1
      },
      dataForm: {
        id: null,
        type: 1,
        name: null,
        parentId: null,
        parentName: '',
        url: null,
        code: null,
        sequence: 0,
        icon: null,
        path: null,
        version: 0,
        status: true,
        openTarget: '_self',
        iconPicture: '',
        iconList: []
      },
      dataRule: {
        name: [
          {required: true, message: '菜单名称不能为空', trigger: 'blur'}
        ],
        parentName: [
          {required: true, message: '请选择上级菜单', trigger: 'blur'}
        ]
      },
      menuList: [],
      menuListTreeProps: {
        label: 'name',
        children: 'children'
      }
    }
  },
  created() {
    this.iconList = Icon.getNameList()
  },
  methods: {
    init(id, parentId, type) {
      this.isLoading = true
      this.dataForm.id = id || undefined
      this.menuList = []
      this.$http({
        url: this.$http.adornUrl('/admin/user/userMenusAndActionAuthorities'),
        method: 'get',
        params: this.$http.adornParams({type: type})
      }).then(({data}) => {
        let tmp = [{name: '根菜单', 'id': rootPID}]
        _.forEach(data.obj.menuList, (it) => {
          if (it.parentId == null) {
            it.parentId = rootPID
          }
        })
        tmp.push(...data.obj.menuList)
        this.menuList = treeDataTranslate(tmp)
        this.dataForm.parentId = parentId
      }).then(() => {
        this.visible = true
        this.$nextTick(() => {
          this.$refs['dataForm']?.resetFields()
          this.bakDataForm = _.cloneDeep(this.dataForm)
        })
      }).then(() => {
        if (!this.dataForm.id) {
          // 新增
          this.menuListTreeSetCurrentNode()
          this.dataForm.type = type
        } else {
          // 修改
          this.$http({
            url: this.$http.adornUrl(`/admin/resource`),
            method: 'get',
            params: this.$http.adornParams({id: this.dataForm.id})
          }).then(({data}) => {
            this.dataForm.id = data.obj.id
            this.dataForm.type = data.obj.type
            this.dataForm.status = data.obj.status
            this.dataForm.name = data.obj.name
            this.dataForm.parentId = data.obj.parentId || rootPID
            this.dataForm.url = data.obj.url
            this.dataForm.code = data.obj.code
            this.dataForm.sequence = data.obj.sequence
            this.dataForm.icon = data.obj.icon
            this.dataForm.iconPicture = data.obj.iconPicture
            this.dataForm.path = data.obj.path
            this.dataForm.openTarget = data.obj.openTarget
            this.dataForm.version = data.obj.version
            this.menuListTreeSetCurrentNode()
            this.bakDataForm = _.cloneDeep(this.dataForm)
          })
        }
        this.isLoading = false
      })
    },
    getRootPID() {
      return rootPID
    },
    resetForm() {
      this.dataForm = _.cloneDeep(this.bakDataForm)
    },
    // 菜单树选中
    menuListTreeCurrentChangeHandle(data, node) {
      this.dataForm.parentId = data.id
      this.dataForm.parentName = data.name
      this.$refs[`menuListPopover`].doClose()
    },
    // 菜单树设置当前选中节点
    menuListTreeSetCurrentNode() {
      if (this.dataForm.parentId) {
        this.$refs.menuListTree.setCurrentKey(this.dataForm.parentId)
        this.dataForm.parentName = (this.$refs.menuListTree.getCurrentNode() || {})['name']
      } else {
        this.$refs.menuListTree.setCurrentKey([])
        this.dataForm.parentName = ''
      }
    },
    // 图标选中
    iconActiveHandle(iconName) {
      this.dataForm.icon = iconName
      this.$refs[`iconListPopover`].doClose()
    },
    // 表单提交
    dataFormSubmit() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.$http({
            url: this.$http.adornUrl(`/admin/resource/${!this.dataForm.id ? 'save' : 'update'}`),
            method: 'post',
            data: this.$http.adornData({
              'id': this.dataForm.id || undefined,
              'type': this.dataForm.type,
              'name': this.dataForm.name,
              'parentId': this.dataForm.parentId === rootPID ? null : this.dataForm.parentId,
              'url': this.dataForm.url,
              'code': this.dataForm.code,
              'sequence': this.dataForm.sequence,
              'openTarget': this.dataForm.openTarget,
              'icon': this.dataForm.icon,
              'status': this.dataForm.status,
              'path': this.dataForm.path,
              'iconPicture': this.dataForm.iconPicture,
              'version': this.dataForm.version,
            })
          }).then(({data}) => {
            if (data && data.code === 0) {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 1500,
                onClose: () => {
                  this.visible = false
                  this.$emit('refreshDataList', data.obj.id)
                }
              })
            } else {
              this.$message.error(data.msg)
            }
          })
        }
      })
    }
  }
}
</script>

<style lang="scss">
.mod-menu {
  .menu-list__input,
  .icon-list__input {
    > .el-input__inner {
      cursor: pointer;
    }
  }

  &__icon-popover {
    width: 458px;
    overflow: hidden;
  }

  &__icon-inner {
    width: 478px;
    max-height: 258px;
    overflow-x: hidden;
    overflow-y: auto;
  }

  &__icon-list {
    width: 458px;
    padding: 0;
    margin: -8px 0 0 -8px;

    > .el-button {
      padding: 8px;
      margin: 8px 0 0 8px;

      > span {
        display: inline-block;
        vertical-align: middle;
        width: 18px;
        height: 18px;
        font-size: 18px;
      }
    }
  }

  .icon-list__tips {
    font-size: 18px;
    text-align: center;
    color: #e6a23c;
    cursor: pointer;
  }
}
</style>
