<template>
  <div class="mod-config">
    <el-form :inline="true" :model="dataForm" @keyup.enter.native="queryPage()">
      <el-form-item :label="'关键字'">
        <el-input v-model="dataForm.keyword" placeholder="请输入名称或CODE" clearable></el-input>
      </el-form-item>
      <el-form-item :label="'所属栏位'">
        <el-dict v-model="dataForm.categoryCode" :code="'app_config_category'" clearable></el-dict>
      </el-form-item>
      <el-form-item>
        <el-button @click="queryPage()">查询</el-button>
        <el-button type="primary" @click="addOrUpdateHandle()">新增</el-button>
      </el-form-item>
    </el-form>
    <el-table
        :data="dataList"
        border
        v-loading="dataListLoading"
        style="width: 100%;">
      <el-table-column
          prop="name"
          header-align="center"
          align="center"
          label="菜单名称">
      </el-table-column>
      <el-table-column
          prop="categoryName"
          header-align="center"
          align="center"
          label="所属栏位">
      </el-table-column>
      <el-table-column
          prop="photo"
          header-align="center"
          align="center"
          label="图标">
        <template slot-scope="scope">
          <img
              slot="reference"
              :src="$http.adornAttachmentUrl(scope.row.picture)"
              style="max-height: 130px;max-width: 130px"
          />
        </template>
      </el-table-column>
      <el-table-column
          prop="code"
          header-align="center"
          align="center"
          label="菜单code">
      </el-table-column>
      <el-table-column
          prop="createDate"
          header-align="center"
          align="center"
          width="200"
          label="创建时间">
      </el-table-column>
      <el-table-column
          prop="sequence"
          header-align="center"
          align="center"
          width="60"
          label="排序">
      </el-table-column>
      <el-table-column
          prop="description"
          header-align="center"
          align="center"
          show-overflow-tooltip
          label="菜单描述">
      </el-table-column>
      <el-table-column
          prop="status"
          header-align="center"
          align="center"
          label="是否启用">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.status == false" size="small" type="danger">否</el-tag>
          <el-tag v-else size="small">是</el-tag>
        </template>
      </el-table-column>
      <el-table-column
          prop="outLink"
          header-align="center"
          align="center"
          label="是否外链">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.outLink == false" size="small" type="danger">否</el-tag>
          <el-tag v-else size="small">是</el-tag>
        </template>
      </el-table-column>
      <el-table-column
          prop="needLogin"
          header-align="center"
          align="center"
          label="需要登陆">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.needLogin == false" size="small" type="danger">否</el-tag>
          <el-tag v-else size="small">是</el-tag>
        </template>
      </el-table-column>
      <el-table-column
          prop="jumpUrl"
          header-align="center"
          align="center"
          width="230"
          show-overflow-tooltip
          label="跳转目录">
      </el-table-column>
      <el-table-column
          fixed="right"
          header-align="center"
          align="center"
          width="180"
          label="操作">
        <template slot-scope="scope">
          <el-button type="text" size="small" @click="addOrUpdateHandle(scope.row.id)">修改</el-button>
          <el-button type="text" size="small" @click="deleteHandle(scope.row.id)">删除</el-button>
          <el-button size="small" type="text" v-if="scope.row.code === 'ANNUAL_ACCOUNT'" @click="showQrCode()" v-loading.fullscreen.lock="qrCodeLoading">二维码下载</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
        @size-change="sizeChangeHandle"
        @current-change="currentChangeHandle"
        :current-page="pageIndex"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pageSize"
        :total="totalPage"
        layout="total, sizes, prev, pager, next, jumper">
    </el-pagination>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"></add-or-update>
  </div>
</template>

<script>
import AddOrUpdate from './app-menu-config-add-or-update.vue'
import moment from "moment";
import html2canvas from "html2canvas";

export default {
  data() {
    return {
      dataForm: {
        keyword: '',
        categoryCode: ''
      },
      dataList: [],
      pageIndex: 1,
      pageSize: 10,
      totalPage: 0,
      dataListLoading: false,
      dataListSelections: [],
      addOrUpdateVisible: false,
      qrCodeVisible: false,
      qrCodeLoading: false,
      qrCodeUrl: null,
      qrCodeText: null
    }
  },
  components: {
    AddOrUpdate
  },
  activated() {
    this.queryPage()
  },
  methods: {
    queryPage() {
      this.pageIndex = 1
      this.getDataList()
    },
    // 获取数据列表
    getDataList() {
      this.dataListLoading = true
      this.$http({
        url: this.$http.adornUrl('/admin/sys/app-menu-config/pages'),
        method: 'post',
        data: this.$http.adornData({
          'currentPage': this.pageIndex,
          'pageSize': this.pageSize,
          'keyword': this.dataForm.keyword,
          'categoryCode': this.dataForm.categoryCode,
          'orders': [{column: 'sequence', sort: 'asc'}]
        })
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.dataList = data.obj.records
          this.totalPage = data.obj.total
        } else {
          this.dataList = []
          this.totalPage = 0
        }
        this.dataListLoading = false
      })
    },
    // 每页数
    sizeChangeHandle(val) {
      this.pageSize = val
      this.pageIndex = 1
      this.getDataList()
    },
    // 当前页
    currentChangeHandle(val) {
      this.pageIndex = val
      this.getDataList()
    },
    // 多选
    // 新增 / 修改
    addOrUpdateHandle(id) {
      this.addOrUpdateVisible = true
      this.$nextTick(() => {
        this.$refs.addOrUpdate.init(id)
      })
    },
    // 删除
    deleteHandle(id) {
      var ids = id ? [id] : this.dataListSelections.map(item => {
        return item.id
      })
      this.$confirm(`确定进行[${id ? '删除' : '批量删除'}]操作?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        closeOnClickModal: false
      }).then(() => {
        this.$http({
          url: this.$http.adornUrl('/admin/sys/app-menu-config/removeByIds'),
          method: 'get',
          params: this.$http.adornParams({
            'ids': ids.join(',')
          })
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.$message({
              message: '操作成功',
              type: 'success',
              duration: 1500,
              onClose: () => {
                this.getDataList()
              }
            })
          } else {
            this.$message.error(data.msg)
          }
        })
      })
    },
    showQrCode() {
      this.qrCodeLoading = true
      console.log(process.env.VUE_APP_NODE_ENV)
      this.$http({
        url: this.$http.adornUrl('/admin/annual_account/createQrCode'),
        method: 'get',
        responseType: 'blob',
        params: this.$http.adornParams({
          'appId': window.SITE_CONFIG['wechatAppID'],
          'prod': process.env.VUE_APP_NODE_ENV === 'prod'
        })
      }).then(({data}) => {
        console.log(data)
        if (data && data.type === 'application/json') {
          const reader = new FileReader()
          reader.readAsText(data, 'UTF-8')
          let that = this
          reader.onload = function() {
            const result = JSON.parse(reader.result)
            that.$message.error(result.msg)
            that.qrCodeLoading = false
          }
        } else {
          this.qrCodeLoading = false
          let blob = new Blob([data], {type: 'application/octet-stream'})
          let objectUrl = URL.createObjectURL(blob)
          let a = document.createElement('a')
          a.href = objectUrl
          a.download = '年度账单二维码.png'
          a.click()
          URL.revokeObjectURL(objectUrl)
          this.$message({
            type: 'success',
            message: '二维码下载成功'
          })
        }
      }).catch(() => {
        this.qrCodeLoading = false
      })
    }
  }
}
</script>
