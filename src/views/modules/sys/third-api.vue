<template>
  <div>
    <el-form :inline="true" :model="dataForm" ref="selectForm" @keyup.enter.native="getDataListAndBackFirstPage()">
      <el-row :gutter="20" >
        <el-col :span="8"><div class="grid-content bg-purple">
          <el-form-item label="请求类型"  prop="method">
            <el-select v-model="dataForm.method" size="mini" clearable placeholder="请选择">
              <el-option
                v-for="item in option1"
                :label="item.name"
                :key="item.code"
                :value="item.code">
              </el-option>
            </el-select>
          </el-form-item>
        </div></el-col>
        <el-col :span="8"><div class="grid-content bg-purple">
          <el-form-item label="接口状态" prop="status">
            <el-select v-model="dataForm.status" size="mini" clearable placeholder="请选择">
              <el-option
                v-for="item in option2"
                :label="item.label"
                :key="item.value"
                :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
        </div></el-col>
        <el-col :span="8"><div class="grid-content bg-purple">
          <el-form-item label="关键字" prop="keyword">
            <el-input v-model="dataForm.keyword" size="mini" placeholder="请输入" clearable></el-input>
          </el-form-item>
        </div></el-col>
      </el-row>
      <el-row :gutter="20" >
        <el-col :span="13" :offset="11"><div class="grid-content bg-purple">
          <el-form-item>
            <el-button v-if="isAuth('sys:api:reset')" type="primary" @click="resetButton('selectForm')">重置</el-button>
            <el-button v-if="isAuth('sys:api:list')" type="primary" @click="getDataListAndBackFirstPage()">查询</el-button>
            <el-button v-if="isAuth('sys:api:save')" type="primary" @click="addOrUpdateHandle()">新增</el-button>
            <el-button v-if="isAuth('sys:api:deleteAll')" type="danger" @click="deleteHandle()" :disabled="dataListSelections.length <= 0">批量删除</el-button>
            <el-button v-if="isAuth('sys:api:status')" type="danger" @click="statusHandle()" :disabled="dataListSelections.length <= 0">批量更改状态</el-button>
          </el-form-item>
        </div></el-col>
      </el-row>
    </el-form>
    <el-table
      :data="dataList"
      border
      v-loading="dataListLoading"
      @selection-change="selectionChangeHandle"
      :row-style="rowClass"
      style="width: 100%;">
      <el-table-column
        type="selection"
        header-align="center"
        align="center"
        width="50">
      </el-table-column>
      <el-table-column
        class="el-tooltip__popper"
        prop="mark"
        header-align="center"
        align="center"
        label="接口描述"
        show-overflow-tooltip>
      </el-table-column>
      <el-table-column
        class="el-tooltip__popper"
        prop="url"
        header-align="center"
        align="center"
        label="接口地址"
        show-overflow-tooltip>
      </el-table-column>
      <el-table-column
        prop="method"
        header-align="center"
        align="center"
        label="请求方式">
      </el-table-column>
      <el-table-column
        prop="paramTypeText"
        header-align="center"
        align="center"
        label="参数类型">
      </el-table-column>
      <el-table-column
        prop="responseTypeText"
        header-align="center"
        align="center"
        label="返回类型">
      </el-table-column>
      <el-table-column
        prop="status"
        header-align="center"
        align="center"
        label="接口状态">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.status === false" size="small" type="danger">禁用</el-tag>
          <el-tag v-if="scope.row.status === true" size="small">正常</el-tag>
        </template>
      </el-table-column>
      <el-table-column
        prop="paramNumber"
        header-align="center"
        align="center"
        label="参数数量">
      </el-table-column>
      <el-table-column
        fixed="right"
        header-align="center"
        align="center"
        width="150"
        label="操作">
        <template slot-scope="scope">
          <el-button v-if="isAuth('sys:api:update')" type="text" size="small" @click="addOrUpdateHandle(scope.row.id)">修改</el-button>
          <el-button v-if="isAuth('sys:api:delete')" type="text" size="small" @click="deleteHandle(scope.row.id)">删除</el-button>
          <el-button v-if="isAuth('sys:api:debug')" type="text" size="small" @click="debugHandle(scope.row.id,scope.row.code,scope.row.responseTypeText)">调试</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      @size-change="sizeChangeHandle"
      @current-change="currentChangeHandle"
      :current-page="pageIndex"
      :page-sizes="[10, 20, 50, 100]"
      :page-size="pageSize"
      :total="totalPage"
      layout="total, sizes, prev, pager, next, jumper">
    </el-pagination>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"></add-or-update>
    <debug v-if="debugVisible" ref="debug" @refreshDataList="getDataList"></debug>
  </div>
</template>

<script>
  import AddOrUpdate from './third-api-add-or-update'
  import Debug from './third-api-debug'
  export default {
    data () {
      return {
        option1: [],
        option2: [{
          value: true,
          label: '正常'
        }, {
          value: false,
          label: '禁用'
        }],
        dataForm: {
          method: '',
          status: '',
          keyword: ''
        },
        dataList: [],
        pageIndex: 1,
        pageSize: 10,
        totalPage: 0,
        dataListLoading: false,
        dataListSelections: [],
        selectRow: [],
        addOrUpdateVisible: false,
        debugVisible: false
      }
    },
    components: {
      AddOrUpdate,
      Debug
    },
    activated () {
      this.initSelect()
    },
    methods: {
      initSelect () {
        this.dataListLoading = true
        this.$http({
          url: this.$http.adornUrl('/admin/dict/parent'),
          method: 'get',
          params: this.$http.adornParams({'code': 'apiType'})
        }).then(({data}) => {
          this.option1 = data.obj || []
          this.getDataList()
        })
      },
      // 获取数据列表
      getDataList () {
        this.dataListLoading = true
        this.$http({
          url: this.$http.adornUrl('/admin/api/pageForApi'),
          method: 'post',
          data: this.$http.adornData({
            'currentPage': this.pageIndex,
            'pageSize': this.pageSize,
            'method': this.dataForm.method,
            'status': this.dataForm.status,
            'keyword': this.dataForm.keyword
          })
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.dataList = data.obj.records
            this.totalPage = data.total
          } else {
            this.dataList = []
            this.totalPage = 0
          }
          this.dataListLoading = false
        })
      },
      resetButton (val) {
        this.$refs[val]?.resetFields()
        this.$nextTick(() => {
          this.getDataList()
        })
      },
      // 每页数
      sizeChangeHandle (val) {
        this.pageSize = val
        this.pageIndex = 1
        this.getDataList()
      },
      // 当前页
      currentChangeHandle (val) {
        this.pageIndex = val
        this.getDataList()
      },
      // 多选
      selectionChangeHandle (val) {
        this.dataListSelections = val
      },
      rowClass ({row, rowIndex}) {
        if (this.selectRow.includes(rowIndex)) {
          return {'background-color': 'rgba(185, 211 ,249, 0.75'}
        }
      },
      getDataListAndBackFirstPage () {
        this.pageIndex = 1
        this.getDataList()
      },
      deleteHandle (id) {
        var ids = id ? [id] : this.dataListSelections.map(item => {
          return item.id
        })
        this.$confirm(`确定进行${id ? '删除' : '批量删除'}操作?`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
          closeOnClickModal: false
        }).then(() => {
          this.$http({
            url: this.$http.adornUrl('/admin/api/removeByIds'),
            method: 'get',
            params: this.$http.adornParams({'ids': ids.join(',')})
          }).then(({data}) => {
            if (data && data.code === 0) {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 1500,
                onClose: () => {
                  this.getDataList()
                }
              })
            } else {
              this.$message.error(data.msg)
            }
          })
        }).catch(() => {})
      },
      statusHandle () {
        var ids = this.dataListSelections.map(item => {
          return item.id
        })
        this.$confirm(`确定进行批量更改状态操作?`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
          closeOnClickModal: false
        }).then(() => {
          this.$http({
            url: this.$http.adornUrl('/admin/api/statusChangeByIds'),
            method: 'get',
            params: this.$http.adornParams({'ids': ids.join(',')}, false)
          }).then(({data}) => {
            if (data && data.code === 0) {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 1500,
                onClose: () => {
                  this.getDataList()
                }
              })
            } else {
              this.$message.error(data.msg)
            }
          })
        }).catch(() => {})
      },
      // 新增 / 修改
      addOrUpdateHandle (id) {
        this.addOrUpdateVisible = true
        this.$nextTick(() => {
          this.$refs.addOrUpdate.init(id)
        })
      },
      debugHandle (id, code, responseType) {
        this.debugVisible = true
        this.$nextTick(() => {
          this.$refs.debug.init(id, code, responseType)
        })
      }
    },
    watch: {
      dataListSelections (data) {
        this.selectRow = []
        if (data.length > 0) {
          data.forEach((item, index) => {
            this.selectRow.push(this.dataList.indexOf(item))
          })
        }
      }
    }
  }
</script>

<style lang="css">
  .el-tooltip__popper{font-size: 15px; max-width:30% } /*设置显示隐藏部分内容，按50%显示*/
</style>
