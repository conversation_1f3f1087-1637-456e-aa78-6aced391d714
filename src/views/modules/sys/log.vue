<template>
  <div class="mod-log">
    <el-form :inline="true" :model="dataForm" @keyup.enter.native="getDataList()">
      <el-form-item label="用户名">
        <el-input v-model="dataForm.key" placeholder="用户名" clearable></el-input>
      </el-form-item>
      <el-form-item label="创建时间" prop="startEndTime">
        <el-date-picker
            v-model="dataForm.startEndTime"
            type="datetimerange"
            value-format="yyyy-MM-dd HH:mm:ss"
            range-separator="-"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :default-time="['00:00:00', '23:59:59']">
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button v-if="isAuth('sysLog-list')" @click="getDataList()">查询</el-button>
      </el-form-item>
    </el-form>
    <el-table
        :data="dataList"
        border
        v-loading="dataListLoading"
        style="width: 100%">
      <el-table-column
          prop="username"
          header-align="center"
          align="center"
          label="用户名">
      </el-table-column>
      <el-table-column
          prop="operation"
          header-align="center"
          align="center"
          label="用户操作">
      </el-table-column>
      <el-table-column
          prop="method"
          header-align="center"
          align="center"
          width="150"
          :show-overflow-tooltip="true"
          label="请求方法">
      </el-table-column>
      <el-table-column
          prop="params"
          header-align="center"
          align="center"
          width="150"
          :show-overflow-tooltip="true"
          label="请求参数">
      </el-table-column>
      <el-table-column
          prop="result"
          header-align="center"
          align="center"
          width="150"
          :show-overflow-tooltip="true"
          label="响应结果">
      </el-table-column>
      <el-table-column
          prop="time"
          header-align="center"
          align="center"
          label="执行时长(毫秒)">
      </el-table-column>
      <el-table-column
          prop="ip"
          header-align="center"
          align="center"
          width="150"
          label="IP地址">
      </el-table-column>
      <el-table-column
          prop="createDate"
          header-align="center"
          align="center"
          width="180"
          label="创建时间">
      </el-table-column>
    </el-table>
    <el-pagination
        @size-change="sizeChangeHandle"
        @current-change="currentChangeHandle"
        :current-page="currentPage"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pageSize"
        :total="totalPage"
        layout="total, sizes, prev, pager, next, jumper">
    </el-pagination>
  </div>
</template>

<script>
export default {
  data() {
    return {
      dataForm: {
        key: null,
        startDate: '',
        endDate: '',
        startEndTime: null
      },
      dataList: [],
      currentPage: 1,
      pageSize: 10,
      totalPage: 0,
      dataListLoading: false,
      selectionDataList: []
    }
  },
  created() {
    this.getDataList()
  },
  methods: {
    // 获取数据列表
    getDataList() {
      this.dataListLoading = true
      if (this.dataForm.startEndTime && this.dataForm.startEndTime.length === 2) {
        this.dataForm.startDate = this.dataForm.startEndTime[0] ? this.dataForm.startEndTime[0] : null
        this.dataForm.endDate = this.dataForm.startEndTime[1] ? this.dataForm.startEndTime[1] : null
      } else {
        this.dataForm.startDate = null
        this.dataForm.endDate = null
      }
      this.$http({
        url: this.$http.adornUrl('/admin/sysLog/pages'),
        method: 'post',
        data: this.$http.adornData({
          'currentPage': this.currentPage,
          'pageSize': this.pageSize,
          'key': this.dataForm.key,
          'startDate': this.dataForm.startDate,
          'endDate': this.dataForm.endDate
        })
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.dataList = data.obj.records
          this.totalPage = data.obj.total
        } else {
          this.dataList = []
          this.totalPage = 0
        }
        this.dataListLoading = false
      })
    },
    // 每页数
    sizeChangeHandle(val) {
      this.pageSize = val
      this.currentPage = 1
      this.getDataList()
    },
    // 当前页
    currentChangeHandle(val) {
      this.currentPage = val
      this.getDataList()
    }
  }
}
</script>
