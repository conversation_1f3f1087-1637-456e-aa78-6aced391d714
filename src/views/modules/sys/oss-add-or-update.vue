<template>
  <el-dialog
    :title="!uploadForm.id ? '上传文件' : '查看'"
    :close-on-click-modal="false"
    @close="closeHandle"
    :visible.sync="visible">
    <el-form :model="uploadForm" :rules="uploadRule" ref="uploadForm" label-width="145px">
      <el-form-item label="名称" prop="name">
        <el-input v-model="uploadForm.name" placeholder="名称" :disabled="!isDisOption"></el-input>
      </el-form-item>
      <el-form-item label="标签" prop="labels">
        <el-select v-model="uploadForm.labels" multiple placeholder="" :disabled="!isDisOption">
          <el-option
            v-for="item in labelList"
            :key="item.id"
            :label="item.name"
            :value="item.id">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="存储方式：" prop="serverCode">
        <el-select v-model="uploadForm.serverCode" placeholder="" :disabled="!isDisOption">
          <el-option v-for="item in serverList" :key="item.id" :label="item.text" :value="item.id"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item v-if="isDisOption" label="上传文件：">
        <el-upload
          ref="upload"
          :action="this.$http.adornUrl(`/admin/oss/upload`)"
          :headers="myHeaders"
          :data="{serverCode: this.uploadForm.serverCode,labels: this.uploadForm.labels,name: this.uploadForm.name,media:true}"
          :on-success="successHandle"
          :on-change="changHandle"
          :limit=1
          :on-exceed="handleExceed"
          :file-list="fileList"
          :auto-upload="false">
          <el-button type="primary">选取文件</el-button>
          <div slot="tip" class="el-upload__tip">支持上传{{fileExts}}文件</div>
        </el-upload>
      </el-form-item>
      <el-form-item v-if="uploadForm.type === 'audio' || uploadForm.type === 'video'" label="资源展示：" prop="videoPlayer">
        <video-player  class="video-player-box"
                       ref="videoPlayer"
                       :options="playerOptions"
                       :playsinline="true">
        </video-player>
      </el-form-item>
      <el-form-item v-if="uploadForm.type === 'picture'" label="资源展示：" prop="imageViewer">
        <img :src="filePath" class="avatar">
      </el-form-item>
      <el-form-item v-if="!isDisOption" label="文件名称：" prop="document">
        <el-input v-model="uploadForm.fileName" disabled>
          <el-button v-if="isAuth('oss-download')" slot="append" icon="el-icon-download" @click="downloadFile"></el-button>
        </el-input>
      </el-form-item>
      <el-form-item v-if="!isDisOption" label="文件类型：" prop="document">
        <el-input v-model="uploadForm.typeText" placeholder="文件类型" disabled></el-input>
      </el-form-item>
      <el-form-item v-if="!isDisOption" label="文件大小：" prop="document">
        <el-input v-model="uploadForm.fileSize" placeholder="文件大小" disabled></el-input>
      </el-form-item>
      <el-form-item v-if="!isDisOption" label="文件路径：" prop="document">
        <el-input v-model="filePath" placeholder="文件路径" disabled></el-input>
      </el-form-item>
      <el-form-item v-if="!isDisOption" label="上传时间：" prop="document">
        <el-input v-model="uploadForm.createDate" placeholder="上传时间" disabled></el-input>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" v-if="!uploadForm.id" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
  import Vue from 'vue'
  import { videoPlayer } from 'vue-video-player'
  import 'video.js/dist/video-js.css'
  export default {
    props: [],
    data () {
      return {
        visible: false,
        labelList: [],
        uploadForm: {
          id: '',
          serverCode: '',
          fileName: '',
          labels: [],
          name: '',
          path: '',
          typeText: '',
          fileSize: '',
          createDate: ''
        },
        filePath: '',
        fileExts: 'mp4/mov/avi/flv/mp3/wav/flac/wma/jpg/bmp/png/jpeg/pdf/doc/docx/xls/xlsx/txt',
        isDisOption: false, // 是否禁止操作
        serverList: [], // 存储方式列表
        fileList: [],
        hostUrl: '',  //  当前域名
        uploadRule: {
          serverCode: [
            {required: true, message: '存储方式不能为空', trigger: 'blur'}
          ],
          name: [
            {required: true, message: '名称不能为空', trigger: 'blur'}
          ]
        },
        myHeaders: {Authorization: Vue.cookie.get('Authorization')},
        playerOptions: {
          muted: false,
          language: 'zh-CN',
          width: 320,
          sources: [{
            type: '',
            src: ''
          }],
          poster: ''
        }
      }
    },
    components: {
      videoPlayer
    },
    watch: {
    },
    methods: {
      init (id) {
        this.isDisOption = !id
        this.fileList = []
        this.labelList = []
        this.uploadForm.id = id
        this.visible = true
        // 存储方式
        this.getServerList()
        this.getLabelList()
        this.$nextTick(() => {
          this.$refs['uploadForm']?.resetFields()
          this.playerOptions.sources = [{
            type: '',
            src: ''
          }]
          this.filePath = ''
          if (id) {
            this.$http({
              url: this.$http.adornUrl('/admin/oss'),
              method: 'get',
              params: this.$http.adornParams({
                id: id
              })
            }).then(({data}) => {
              if (data && data.code === 0) {
                this.uploadForm.id = data.obj.id
                this.uploadForm.serverCode = data.obj.serverCode
                this.uploadForm.fileName = data.obj.fileName
                if (data.obj.labels != null) {
                  this.uploadForm.labels = data.obj.labels
                }
                this.uploadForm.name = data.obj.name
                this.uploadForm.path = data.obj.path
                this.uploadForm.typeText = data.obj.typeText
                this.uploadForm.fileSize = data.obj.fileSize
                this.uploadForm.createDate = data.obj.createDate
                if (this.uploadForm.serverCode === 'LocalServer') {
                  this.filePath = this.$http.adornAttachmentUrl(this.uploadForm.path)
                } else {
                  this.filePath = this.uploadForm.path
                }
                if (this.uploadForm.type === 'video') {
                  this.playerOptions.sources = [{
                    type: `video/${this.uploadForm.fileExt}`,
                    src: this.filePath
                  }]
                } else if (this.uploadForm.type === 'audio') {
                  this.playerOptions.sources = [{
                    type: 'audio/mp3',
                    src: this.filePath
                  }]
                }
              }
            })
          }
        })
      },
      // 获取标签
      getLabelList () {
        this.$http({
          url: this.$http.adornUrl('/admin/cms/label/getLabelList'),
          method: 'get',
          params: this.$http.adornParams()
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.labelList = data.obj
          } else {
            this.$message.error(data.msg)
          }
        })
      },
      // 表单提交
      dataFormSubmit () {
        this.$refs['uploadForm'].validate((valid) => {
          if (valid && this.beforeFileUpload()) {
            this.$refs.upload.submit()
          }
        })
      },
      // 上传成功
      successHandle (response) {
        if (response && response.code === 0) {
          this.$message({
            message: '操作成功',
            type: 'success',
            onClose: () => { }
          })
          this.visible = false
          this.$emit('refreshDataList')
        } else {
          this.$message.error(response.msg)
        }
        this.fileList = []
      },
      changHandle (file, fileList) {
        this.fileList = fileList
      },
      handleExceed () {
        this.$message.error('只能选择一个文件，如需替换请删除已选文件后重试')
      },
      // 获取文件存储方式下拉列表
      getServerList () {
        this.$http({
          url: this.$http.adornUrl('/admin/oss/serverList'),
          method: 'get',
          params: this.$http.adornParams({})
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.serverList = data.obj
          } else {
            this.serverList = []
            this.$message.error(data.msg)
          }
        })
      },
      // 资源上传过滤
      beforeFileUpload () {
        let size = this.fileList.length
        if (size === 0) {
          this.$message.error('请选择资源文件')
          return false
        }
        if (size > 1) {
          this.$message.error('只能上传单个资源，请删除资源后再试')
          return false
        }
        let fileName = this.fileList[0].name
        if (fileName.indexOf('.') === -1) {
          this.$message.error('不支持的文件格式')
        }

        let fileExt = fileName.substring(fileName.lastIndexOf('.') + 1)
        let allowTypes = `/${this.fileExts}/`
        if (allowTypes.indexOf(`/${fileExt}/`) === -1) {
          this.$message.error(`上传资源只能是${this.fileExts}格式!`)
          return false
        }
        return true
      },
      // 弹窗关闭时
      closeHandle () {
        this.uploadForm.type = ''
        this.fileList = []
        this.$emit('refreshDataList')
      },
      // 下载文件
      downloadFile () {
        let link = document.createElement('a')
        link.setAttribute('download', this.uploadForm.fileName)
        link.setAttribute('target', '_blank')
        link.href = this.filePath
        link.click()
      }
    }
  }
</script>

<style >
  .avatar {
    width: 178px;
    height: 178px;
    display: block;
  }
</style>
