<template>
  <el-dialog
      :title="!dataForm.id ? '新增' : '修改'"
      :close-on-click-modal="false"
      width="80%"
      :visible.sync="visible">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmit()"
             label-width="150px" v-loading="isLoading">
      <el-form-item label="用户名" prop="userName">
        <el-input v-model="dataForm.userName" placeholder="登录帐号"></el-input>
      </el-form-item>
      <el-form-item label="密码" v-if="dataForm.id === null" prop="password" :class="{ 'is-required': !dataForm.id }">
        <el-input v-model="dataForm.password" type="password" placeholder="密码"></el-input>
      </el-form-item>
      <el-form-item label="确认密码" v-if="dataForm.id === null" prop="comfirmPassword"
                    :class="{ 'is-required': !dataForm.id }">
        <el-input v-model="dataForm.comfirmPassword" type="password" placeholder="确认密码"></el-input>
      </el-form-item>
      <el-form-item label="姓名" prop="name">
        <el-input v-model="dataForm.name" placeholder="姓名"></el-input>
      </el-form-item>
      <el-form-item label="手机号" prop="mobile">
        <el-input v-model="dataForm.mobile" placeholder="手机号"></el-input>
      </el-form-item>
      <el-form-item label="身份" prop="embedRoleIds" placeholder="请选择">
        <el-checkbox-group v-model="dataForm.roleCodes">
          <el-checkbox v-for="item in sysEmbedRoles"
                       :key="item.code"
                       :label="item.code"
                       :disabled="item.disabled"
                       :value="item.code">{{ item.name }}
          </el-checkbox>
        </el-checkbox-group>
      </el-form-item>
      <el-form-item label="所在分协会" prop="subAssociationList" placeholder="请选择"
                    v-if="dataForm.roleCodes.includes('SUB_ASSOCIATION_ADMIN')">
        <el-select v-model="dataForm.subAssociationList" :multiple="true" placeholder="请选择" clearable
                   style="width: 100%">
          <el-option
              v-for="item in subAssociationList"
              :key="item.code"
              :label="item.name"
              :value="item.code">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="所在社区" prop="communityList" placeholder="请选择"
                    v-if="dataForm.roleCodes.includes('COMMUNITY_ADMIN')">
        <el-select v-model="dataForm.communityList" filterable :multiple="true" placeholder="请选择" clearable
                   style="width: 100%">
          <el-option
              v-for="item in communityList"
              :key="item.code"
              :label="item.name"
              :value="item.code">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="所在团队" prop="teamList" placeholder="请选择"
                    v-if="dataForm.roleCodes.includes('TEAM_ADMIN')">
        <el-select v-model="dataForm.teamList" filterable :multiple="true" placeholder="请选择" clearable
                   style="width: 100%">
          <el-option
              v-for="item in teamList"
              :key="item.id"
              :label="item.text"
              :value="item.id">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="权限角色" prop="roleIds" placeholder="请选择">
        <el-checkbox-group v-model="dataForm.roleCodes">
          <el-checkbox v-for="item in roleList"
                       :key="item.code"
                       :label="item.code"
                       :value="item.code">{{ item.name }}
          </el-checkbox>
        </el-checkbox-group>
      </el-form-item>
      <el-form-item label="邮箱" prop="email">
        <el-input v-model="dataForm.email" placeholder="邮箱"></el-input>
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-radio-group v-model="dataForm.status">
          <el-radio :label="false">禁用</el-radio>
          <el-radio :label="true">启用</el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import {isEmail, isMobile, isName} from '@/utils/validate'
import _ from 'lodash'

export default {
  data() {
    var validateName = (rule, value, callback) => {
      if (value === undefined || value === null || value === '') {
        callback(new Error('姓名不能为空'))
      } else if (value.length > 30) {
        callback(new Error('姓名长度不能超过30个字'))
      } else {
        callback()
      }
    }
    var validatePassword = (rule, value, callback) => {
      if (!this.dataForm.id && !/\S/.test(value)) {
        callback(new Error('密码不能为空'))
      } else {
        callback()
      }
    }
    var validateConfirmPassword = (rule, value, callback) => {
      if (!this.dataForm.id && (value === undefined || value === null || value === '')) {
        callback(new Error('确认密码不能为空'))
      } else if (this.dataForm.password !== value) {
        callback(new Error('确认密码与密码输入不一致'))
      } else {
        callback()
      }
    }
    var validateEmail = (rule, value, callback) => {
      if (!value) {
        callback()
      }
      if (!isEmail(value)) {
        callback(new Error('邮箱格式错误'))
      } else {
        callback()
      }
    }
    var validateMobile = (rule, value, callback) => {
      if (!isMobile(value)) {
        callback(new Error('手机号格式错误'))
      } else {
        callback()
      }
    }
    var validateNewPassword = (rule, value, callback) => {
      let reg = /(?=.*[A-Z])(?=.*[a-z])(?=.*[0-9])(?=.*[\W_]).{8,20}/
      if (value === undefined || value === null || value === '') {
        callback(new Error('密码不能为空'))
      } else {
        if (!reg.test(value)) {
          callback(new Error('密码须包含大小写、数字、特殊字符，且长度8~20位'))
        } else {
          callback()
        }
      }
    }
    var validateUsername = (rule, value, callback) => {
      if (value === undefined || value === null || value === '') {
        callback(new Error('用户名不能为空'))
      } else if (value.length < 5) {
        callback(new Error('用户名应至少5个字符'))
      } else if (value.length > 80) {
        callback(new Error('用户名应至多80个字符'))
      } else if (!/^\w+$/.test(value)) {
        callback(new Error('用户名只能由字母和数字组成'))
      } else {
        callback()
      }
    }
    return {
      isLoading: false,
      visible: false,
      sysEmbedRoles: [{code: 'ASSOCIATION_ADMIN', name: '协会管理员', disabled: false},
        {code: 'SUB_ASSOCIATION_ADMIN', name: '分协会管理员', disabled: false},
        {code: 'COMMUNITY_ADMIN', name: '社区管理员', disabled: false},
        {code: 'TEAM_ADMIN', name: '团队管理员', disabled: false},
        {code: 'VOLUNTEER', name: '志愿者', disabled: true}],
      subAssociationList: [],
      communityList: [],
      teamList: [],
      roleList: [],
      orgDataList: [],
      dataForm: {
        id: null,
        userName: null,
        password: null,
        orgCode: null,
        comfirmPassword: null,
        email: null,
        mobile: null,
        roleCodes: [],
        name: null,
        status: true,
        type: 0,
        idNum: null,
        version: null,
        communityList: [],
        subAssociationList: [],
        teamList: [],
        expandList: []
      },
      dataRule: {
        userName: [
          {validator: validateUsername, trigger: 'blur'},
          {required: true, message: '用户名不能为空', trigger: 'blur'}
        ],
        password: [
          {validator: validateNewPassword, trigger: 'blur'}
        ],
        comfirmPassword: [
          {validator: validateConfirmPassword, trigger: 'blur'}
        ],
        email: [
          {validator: validateEmail, trigger: 'blur'}
        ],
        subAssociationList: [
          {required: true, message: '请选择所在分协会', trigger: 'blur'}
        ],
        communityList: [
          {required: true, message: '请选择所在社区', trigger: 'blur'}
        ],
        teamList: [
          {required: true, message: '请选择所在团队', trigger: 'blur'}
        ],
        mobile: [
          {required: true, message: '手机号不能为空', trigger: 'blur'},
          {validator: validateMobile, trigger: 'blur'}
        ],
        roleCodes: [
          {required: true, message: '用户角色不能为空', trigger: 'blur'}
        ],
        name: [
          {required: true, message: '用户姓名不能为空', trigger: 'blur'},
          {validator: validateName, trigger: 'blur'}
        ]
      }
    }
  },
  methods: {
    async init(id) {
      Object.assign(this.$data.dataForm, this.$options.data().dataForm)
      this.dataForm.id = id || null
      this.dataForm.roleCodes = []
      await this.initOrg()
      await this.initTeam()
      await this.getAllRole()
      this.visible = true
      console.log(this.dataForm)
      this.$nextTick(() => {
        // console.log(this.$refs['dataForm'])
        // this.$refs['dataForm']?.resetFields()
        this.dataForm.subAssociationList = []
        this.dataForm.communityList = []
        this.dataForm.teamList = []
        if (this.dataForm.id) {
          this.$http({
            url: this.$http.adornUrl('/admin/user'),
            method: 'get',
            params: this.$http.adornParams({'id': this.dataForm.id})
          }).then(({data}) => {
            if (data && data.code === 0) {
              this.dataForm.version = data.obj.version
              this.dataForm.userName = data.obj.username
              this.dataForm.email = data.obj.email
              this.dataForm.mobile = data.obj.mobile
              this.dataForm.roleId = data.obj.roleId
              this.dataForm.name = data.obj.name
              this.dataForm.status = data.obj.status
              this.dataForm.orgCode = data.obj.orgCode
              this.dataForm.type = data.obj.type
              this.dataForm.roleCodes = data.obj.roleCodes
              let expandList = data.obj.expandList || []
              //处理分协会
              this.dataForm.subAssociationList = _.map(_.filter(expandList, (it) => it.roleCode === 'SUB_ASSOCIATION_ADMIN'), (it) => it.orgCode)
              //社区
              this.dataForm.communityList = _.map(_.filter(expandList, (it) => it.roleCode === 'COMMUNITY_ADMIN'), (it) => it.orgCode)
              //团队
              this.dataForm.teamList = _.map(_.filter(expandList, (it) => it.roleCode === 'TEAM_ADMIN'), (it) => it.teamId)
              this.dataForm.expandList = []
            }
          })
        }
      })
    },
    async getAllRole () {
      this.$http({
        url: this.$http.adornUrl('/admin/role/all'),
        method: 'get',
        params: this.$http.adornParams()
      }).then(({data}) => {
        let temp = data || []
        let embedCodes = _.map(this.sysEmbedRoles, 'code')
        this.roleList = _.filter(temp, (it) => {
          return !embedCodes.includes(it.code)
        });
      })
    },
    // 初始化组织架构
    async initOrg() {
      this.$http({
        url: this.$http.adornUrl('/admin/org/all'),
        method: 'get',
        params: this.$http.adornParams()
      }).then(({data}) => {
        //所在分协会
        this.subAssociationList = _.filter(data, (org) => {
          return org.level === 2
        })
        this.communityList = _.filter(data, (org) => {
          return org.level === 3
        })
      })
    },
    // 初始化团队列表
    async initTeam() {
      this.$http({
        url: this.$http.adornUrl('/admin/zyz/team/teamIdTextList'),
        method: 'get',
        params: this.$http.adornParams()
      }).then(({data}) => {
        this.teamList = data.obj
      })
    },
    // 表单提交
    dataFormSubmit() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          if (this.dataForm.roleCodes.includes('ASSOCIATION_ADMIN')) {
            this.dataForm.expandList.push({
              roleCode: 'ASSOCIATION_ADMIN',
              orgCode: 'top_dept'
            })
          }
          // 处理勾选的分协会
          if (this.dataForm.roleCodes.includes('SUB_ASSOCIATION_ADMIN')) {
            console.info(this.dataForm.subAssociationList)
            this.dataForm.subAssociationList = _.uniq(this.dataForm.subAssociationList, true)
            console.info(this.dataForm.subAssociationList)
            _.forEach(this.dataForm.subAssociationList, (it) => {
              this.dataForm.expandList.push({
                roleCode: 'SUB_ASSOCIATION_ADMIN',
                orgCode: it
              })
            })
          }
          this.isLoading = true
          // 处理勾选的社区
          if (this.dataForm.roleCodes.includes('COMMUNITY_ADMIN')) {
            this.dataForm.communityList = _.uniq(this.dataForm.communityList, 'toString')
            _.forEach(this.dataForm.communityList, (it) => {
              this.dataForm.expandList.push({
                roleCode: 'COMMUNITY_ADMIN',
                orgCode: it
              })
            })
          }
          // 处理勾选的团队
          if (this.dataForm.roleCodes.includes('TEAM_ADMIN')) {
            this.dataForm.teamList = _.uniq(this.dataForm.teamList, 'toString')
            _.forEach(this.dataForm.teamList, (it) => {
              this.dataForm.expandList.push({
                roleCode: 'TEAM_ADMIN',
                teamId: it
              })
            })
          }
          this.$http({
            url: this.$http.adornUrl(`/admin/user/${!this.dataForm.id ? 'save' : 'update'}`),
            method: 'post',
            data: this.$http.adornData({
              'id': this.dataForm.id || null,
              'version': this.dataForm.version,
              'username': this.dataForm.userName,
              'password': this.dataForm.password,
              'email': this.dataForm.email,
              'mobile': this.dataForm.mobile,
              'expandList': this.dataForm.expandList,
              'status': this.dataForm.status,
              'name': this.dataForm.name,
              'roleCodes': this.dataForm.roleCodes,
            })
          }).then(({data}) => {
            console.log(data.roleId)
            if (data && data.code === 0) {
              this.$message({
                message: '操作成功，如果权限未生效，请重新登录',
                type: 'success',
                duration: 1500,
                onClose: () => {
                  this.visible = false
                  this.$emit('refreshDataList')
                }
              })
            } else {
              this.$message.error(data.msg)
            }
            this.isLoading = false
          })
        }
      })
    }
  }
}
</script>
<style type="text/css">
.el-transfer-panel {
  width: 400px;
}
</style>
