<template>
  <div class="mod-role">
    <el-form :inline="true" ref="dataForm" :model="dataForm" @keyup.enter.native="getDataList()">
      <el-form-item label="用户名/姓名/手机号" prop="keyword">
        <el-input v-model="dataForm.keyword" placeholder="请输入" clearable></el-input>
      </el-form-item>
      <el-form-item label="账号类型" prop="adminType">
        <el-select v-model="dataForm.adminType" placeholder="请选择" clearable @change="typeChange">
          <el-option
              v-for="item in [{label: '分协会管理员', value: 'SUB_ASSOCIATION_ADMIN'}, {label: '社区管理员', value: 'COMMUNITY_ADMIN'}]"
              :key="item.value"
              :label="item.label"
              :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item v-if="dataForm.adminType && dataForm.adminType === 'COMMUNITY_ADMIN'" label="管理社区" prop="community">
        <el-select style="width: 250px" v-model="dataForm.community" placeholder="请选择" clearable filterable>
          <el-option
              v-for="item in communityList"
              :key="item.code"
              :label="item.name"
              :value="item.code">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="warning" icon="el-icon-search" @click="getDataList()">查询</el-button>
        <el-button icon="el-icon-refresh-left" @click="resetForm()">重置</el-button>
      </el-form-item>
      <div class="f-s-c" style="padding: 15px 0;display: flex; justify-content: flex-end">
        <div>
          <el-button type="primary" icon="el-icon-s-custom" @click="grantPrivileges()">授权</el-button>
          <el-button type="success" icon="el-icon-download" v-loading.fullscreen.lock="exportLoading" @click="exportHandle()">导出</el-button>
        </div>
      </div>
    </el-form>
    <el-table
        :data="dataList"
        border
        v-loading="dataListLoading"
        style="width: 100%;">
      <el-table-column
          prop="username"
          header-align="center"
          align="center"
          label="用户名">
      </el-table-column>
      <el-table-column
          prop="name"
          header-align="center"
          align="center"
          label="姓名">
      </el-table-column>
      <el-table-column
          prop="mobile"
          header-align="center"
          align="center"
          label="手机">
      </el-table-column>
      <el-table-column
          prop="roleNames"
          header-align="center"
          align="center"
          label="用户角色">
        <template slot-scope="scope" v-if="scope.row.roleNames && scope.row.roleNames !== ''">
          <el-tag type="info" style="margin: 5px" v-for="(item, index) in scope.row.roleNames.split(',')" :key="index">{{ item }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column
          prop="saNames"
          header-align="center"
          align="center"
          width="250"
          label="管理实践所">
        <template slot-scope="scope" v-if="scope.row.saNames && scope.row.saNames !== ''">
          <el-tag type="info" style="margin: 5px" v-for="(item, index) in scope.row.saNames.split(',')" :key="index">{{ item }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column
          prop="communityNames"
          header-align="center"
          align="center"
          width="250"
          label="管理实践站">
        <template slot-scope="scope" v-if="scope.row.communityNames && scope.row.communityNames !== ''">
          <el-tag type="info" style="margin: 5px" v-for="(item, index) in scope.row.communityNames.split(',')" :key="index">{{ item }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column
          prop="status"
          header-align="center"
          align="center"
          width="100"
          label="状态">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.status === false" size="small" type="danger">禁用</el-tag>
          <el-tag v-else size="small" type="success">正常</el-tag>
        </template>
      </el-table-column>
      <el-table-column
          prop="createDate"
          header-align="center"
          align="center"
          width="180"
          label="创建时间">
      </el-table-column>
      <el-table-column
          fixed="right"
          header-align="center"
          align="center"
          width="150"
          label="操作">
        <template slot-scope="scope">
          <el-button type="text" size="small" @click="resetPasswordHandle(scope.row.id)">重置密码</el-button>
          <el-button type="text" size="small" @click="changePrivileges(scope.row)">授权变更</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
        @size-change="sizeChangeHandle"
        @current-change="currentChangeHandle"
        :current-page="pageIndex"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pageSize"
        :total="totalPage"
        layout="total, sizes, prev, pager, next, jumper">
    </el-pagination>
    <account-privilege-option v-if="accountPrivilegeOptionVisible" ref="accountPrivilegeOption" @refreshDataList="getDataList"/>
  </div>
</template>

<script>
import ListMixins from "@/mixins/list-mixins";
import AccountPrivilegeOption from "@/views/modules/sys/account-privilege-option";

export default {
  name: "account-privilege",
  components: {AccountPrivilegeOption},
  mixins: [ListMixins],
  data () {
    return {
      mixinOptions: {
        createdLoad: true,
        dataUrl: '/admin/user/pageForSubAssociationAdmin',
        exportUrl: '/admin/user/exportForSubAssociationAdmin',              // 导出接口，API地址
        exportFileName: '账号权限表'
      },
      communityList: [],
      dataForm: {
        keyword: null,
        adminType: null,
        community: null
      },
      exportLoading: false,
      accountPrivilegeOptionVisible: false
    }
  },
  methods: {
    resetForm () {
      this.$refs['dataForm']?.resetFields()
      this.$nextTick(() => {
        this.getDataList()
      })
    },
    typeChange(value) {
      if (value === 'COMMUNITY_ADMIN') {
        this.getCommunityList()
        return
      }
      this.communityList = []
      this.dataForm.community = null
    },
    getCommunityList() {
      this.$http({
        url: this.$http.adornUrl('/admin/org/getCommunityBySACode'),
        method: 'get'
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.communityList = data.obj
        } else {
          this.communityList = []
        }
      })
    },
    resetPasswordHandle(id) {
      this.$confirm(`确定重置密码?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        closeOnClickModal: false
      }).then(() => {
        this.categoryEditLoading = true
        this.$http({
          url: this.$http.adornUrl('/admin/user/resetPassWordOfSelect'),
          method: 'post',
          params: this.$http.adornParams({
            'userId': id
          })
        }).then(({data}) => {
          if (data && data.code === 0) {
            let newPwd = data.obj
            let msg = '重置密码成功，新密码为:' + newPwd
            this.$alert(msg, '重置密码', {
              confirmButtonText: '确定',
              callback: action => {
                this.getDataList()
              }
            })
          } else {
            this.$message.error(data.msg)
          }
        })
      }).catch(() => {
      })
    },
    changePrivileges(row) {
      this.accountPrivilegeOptionVisible = true
      this.$nextTick(() => {
        this.$refs.accountPrivilegeOption.init(row.id, row.username, row.mobile, row.name)
      })
    },
    grantPrivileges() {
      this.accountPrivilegeOptionVisible = true
      this.$nextTick(() => {
        this.$refs.accountPrivilegeOption.init()
      })
    }
  }
}
</script>

<style lang="scss" scoped>

</style>
