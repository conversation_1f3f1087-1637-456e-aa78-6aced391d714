<template>
  <el-dialog title="知社区用户同步" :close-on-click-modal="false" width="40%" :visible.sync="visible" @closed="refreshDataList">
    <el-form :model="dataForm" ref="dataForm" @keyup.enter.native="dataFormSubmit()" label-width="100px" v-loading="isLoading">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="开始日期" prop="startDate">
            <el-date-picker
                v-model="dataForm.startDate"
                type="date"
                clearable
                value-format="yyyy-MM-dd"
                :picker-options="startDatePickerOptions"
                placeholder="请选择">
            </el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="结束日期" prop="endDate">
            <el-date-picker
                v-model="dataForm.endDate"
                type="date"
                clearable
                value-format="yyyy-MM-dd"
                :picker-options="endDatePickerOptions"
                placeholder="请选择">
            </el-date-picker>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" :disabled="isLoading" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import moment from "moment";
export default {
  data() {
    return {
      isLoading: false,
      visible: false,
      dataForm: {
        startDate: null,
        endDate: null
      },
      syncFileName: '知社区用户同步记录.xlsx',
      startDatePickerOptions: {
        disabledDate: (time) => {
          if (this.dataForm.endDate) {
            const timeStr = moment(time).format('YYYY-MM-DD');
            const endDateStr = this.dataForm.endDate; // 已经是 YYYY-MM-DD
            return timeStr > endDateStr; // 字符串比较，避免时区问题
          }
          return false;
        }
      },
      endDatePickerOptions: {
        disabledDate: (time) => {
          if (this.dataForm.startDate) {
            const timeStr = moment(time).format('YYYY-MM-DD');
            const startDateStr = this.dataForm.startDate;
            return timeStr < startDateStr;
          }
          return false;
        }
      }
    }
  },
  methods: {
    async init() {
      this.visible = true
      this.$refs['dataForm']?.resetFields()
    },
    // 表单提交
    dataFormSubmit() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.$confirm(`确定开始同步（此过程将持续一段时间，请耐心等待）?`, '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
            closeOnClickModal: false
          }).then(() => {
            this.isLoading = true
            this.$http({
              timeout: 30 * 60 * 1000,
              url: this.$http.adornUrl('/admin/sso/user/import/syncZsqUser'),
              method: 'get',
              params: this.$http.adornParams({
                'startDate': this.dataForm.endDate,
                'endDate': this.dataForm.startDate
              }),
              responseType: 'arraybuffer'
            }).then(({data}) => {
              var enc = new TextDecoder('utf-8')
              var str = enc.decode(new Uint8Array(data))
              if (!str) {
                this.$alert('所有数据同步成功', '成功', {
                  type: 'success',
                  confirmButtonText: '确定'
                })
              }
              else if (str.substr(0, 1) === '{') {
                var jsonData = JSON.parse(str)
                this.$alert(jsonData.msg, '出错了', {
                  type: 'warning',
                  confirmButtonText: '确定'
                })
              } else {
                let blob = new Blob([data], {type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8'})
                let objectUrl = URL.createObjectURL(blob)
                let a = document.createElement('a')
                a.href = objectUrl
                a.download = this.syncFileName
                a.click()
                URL.revokeObjectURL(objectUrl)
                this.$alert('操作成功，请查看浏览器自动下载的EXCEL，EXCEL会记录所有同步数据的处理情况。', '同步完成提醒', {
                  type: 'warning',
                  confirmButtonText: '确定'
                })
              }
              this.isLoading = false
            })
          }).catch(() => {
          })
        }
      })
    },
    refreshDataList() {
      this.$emit('refreshDataList')
    }
  }
}
</script>
<style lang="scss" scoped>

</style>
