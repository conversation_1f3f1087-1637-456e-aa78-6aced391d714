<template>
  <div class="mod-config">
    <el-form :inline="true" :model="dataForm">
      <el-form-item label="应用名称" prop="name">
        <el-input v-model="dataForm.name" placeholder="应用名称" clearable></el-input>
      </el-form-item>
      <el-form-item label="应用类型" prop="type">
        <el-dict :code="'app-type'" v-model="dataForm.type"></el-dict>
      </el-form-item>
      <el-form-item>
        <el-button @click="getDataList()">查询</el-button>
        <el-button type="primary" @click="addOrUpdateHandle()">新增</el-button>
        <el-button type="danger" @click="deleteHandle()" :disabled="dataListSelections.length <= 0">批量删除</el-button>
      </el-form-item>
    </el-form>
    <el-table
      :data="dataList"
      border
      v-loading="dataListLoading"
      @selection-change="selectionChangeHandle"
      style="width: 100%;">
      <el-table-column
        type="selection"
        header-align="center"
        align="center"
        width="50">
      </el-table-column>
      <el-table-column
        prop="name"
        header-align="center"
        align="center"
        label="应用名称">
      </el-table-column>
      <el-table-column
        prop="code"
        header-align="center"
        align="center"
        label="英文名称">
      </el-table-column>
      <el-table-column
        prop="appId"
        header-align="center"
        align="center"
        label="AppId">
      </el-table-column>
      <el-table-column
        prop="typeText"
        header-align="center"
        align="center"
        label="应用类型">
      </el-table-column>
      <el-table-column
        prop="status"
        header-align="center"
        align="center"
        label="是否启用">
        <template slot-scope="scope">
            <el-tag v-if="scope.row.status == false" size="small" type="danger">否</el-tag>
            <el-tag v-else size="small">是</el-tag>
        </template>
      </el-table-column>
      <el-table-column
        prop="author"
        header-align="center"
        align="center"
        label="开发者">
      </el-table-column>
      <el-table-column
        prop="website"
        header-align="center"
        align="center"
        label="官网地址">
      </el-table-column>
      <el-table-column
        fixed="right"
        header-align="center"
        align="center"
        width="150"
        label="操作">
        <template slot-scope="scope">
          <el-button type="text" size="small" @click="addOrUpdateHandle(scope.row.id)">修改</el-button>
          <el-button type="text" size="small" @click="deleteHandle(scope.row.id)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      @size-change="sizeChangeHandle"
      @current-change="currentChangeHandle"
      :current-page="pageIndex"
      :page-sizes="[10, 20, 50, 100]"
      :page-size="pageSize"
      :total="totalPage"
      layout="total, sizes, prev, pager, next, jumper">
    </el-pagination>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"></add-or-update>
  </div>
</template>

<script>
  import AddOrUpdate from './add-or-update'
  import listMixin from '@/mixins/list-mixins'
  export default {
    mixins: [listMixin],
    data () {
      return {
        mixinOptions: {
          dataUrl: '/admin/app/pages'
        },
        dataForm: {
          name: null,
          type: null
        }
      }
    },
    components: {
      AddOrUpdate
    }
  }
</script>
