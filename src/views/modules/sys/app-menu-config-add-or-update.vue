<template>
  <el-dialog
      width="75%"
      :title="!dataForm.id ? '新增' : '修改'"
      :close-on-click-modal="false"
      :visible.sync="visible">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmit()"
             label-width="150px">
      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="菜单名称" prop="name" :error="nameError">
            <el-input v-model="dataForm.name" placeholder="菜单名称"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="菜单code" prop="code" :error="codeError">
            <el-input v-model="dataForm.code" placeholder="菜单code"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="所属栏位" prop="categoryCode" :error="categoryCodeError">
            <el-dict v-model="dataForm.categoryCode" :code="'app_config_category'" clearable></el-dict>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="是否外链" prop="outLink" :error="outLinkError">
            <el-radio-group v-model="dataForm.outLink">
              <el-radio :label="true">是</el-radio>
              <el-radio :label="false">否</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="跳转目录" prop="jumpUrl" :error="jumpUrlError">
            <el-input v-model="dataForm.jumpUrl" placeholder="跳转目录"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="菜单描述" prop="description" :error="descriptionError">
            <el-input v-model="dataForm.description" placeholder="菜单描述" type="textarea" :rows="5"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="图片地址" prop="picture" :error="pictureError">
            <el-upload
                class="avatar-uploader"
                :action="this.$http.adornUrl(`/admin/oss/upload`)"
                :headers="headers"
                :data="{serverCode: uploadOptions.serverCode, media: false}"
                :show-file-list="false"
                :on-success="successHandle"
                :on-change="changHandle"
                :on-exceed="exceedHandle"
                :before-upload="beforeUploadHandle">
              <img v-if="dataForm.picture" :src="$http.adornAttachmentUrl(dataForm.picture)" class="avatar">
              <i v-else class="el-icon-plus avatar-uploader-icon"></i>
            </el-upload>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="排序" prop="sequence" :error="sequenceError">
            <el-input-number v-model="dataForm.sequence" controls-position="right" :min="0"></el-input-number>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="限制身份" prop="limitCapacity">
            <el-checkbox-group v-model="dataForm.limitCapacityList">
              <el-checkbox v-for="item in sysEmbedRoles"
                           :key="item.code"
                           :label="item.code"
                           :value="item.code">{{ item.name }}
              </el-checkbox>
            </el-checkbox-group>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="是否启用" prop="status" :error="statusError">
            <el-radio-group v-model="dataForm.status">
              <el-radio :label="true">启用</el-radio>
              <el-radio :label="false">禁用</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="是否需要登录" prop="needLogin" :error="needLoginError">
            <el-radio-group v-model="dataForm.needLogin">
              <el-radio :label="true">是</el-radio>
              <el-radio :label="false">否</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import fileUploadMixin from "@/mixins/file-upload-mixins";

export default {
  mixins: [fileUploadMixin],
  data() {
    return {
      visible: false,
      sysEmbedRoles: [{code: 'ASSOCIATION_ADMIN', name: '协会管理员', disabled: false},
        {code: 'SUB_ASSOCIATION_ADMIN', name: '分协会管理员', disabled: false},
        {code: 'COMMUNITY_ADMIN', name: '社区管理员', disabled: false},
        {code: 'TEAM_ADMIN', name: '团队管理员', disabled: false}],
      uploadOptions: {
        maxSize: 1,
        fieldName: 'picture'
      },
      initForm: {
        id: null,
        version: null,
        name: '',
        code: '',
        description: '',
        picture: '',
        status: true,
        outLink: false,
        jumpUrl: '',
        sequence: '',
        needLogin: true,
        limitCapacity: '',
        limitCapacityList: []
      },
      dataForm: {},
      dataRule: {
        name: [
          {required: true, message: '请填写名称', trigger: 'blur'}
        ],
        code: [
          {required: true, message: '菜单code不能为空', trigger: 'blur'}
        ],
        categoryCode: [
          {required: true, message: '所属栏位不能为空', trigger: 'blur'}
        ],
        description: [
          {required: true, message: '菜单描述不能为空', trigger: 'blur'}
        ],
        picture: [
          {required: true, message: '图片地址不能为空', trigger: 'blur'}
        ],
        status: [
          {required: true, message: '是否启用不能为空', trigger: 'blur'}
        ],
        outLink: [
          {required: true, message: '是否外链不能为空', trigger: 'blur'}
        ],
        jumpUrl: [
          {required: true, message: '跳转目录不能为空', trigger: 'blur'}
        ],
        sequence: [
          {required: true, message: '排序不能为空', trigger: 'blur'}
        ],
        needLogin: [
          {required: true, message: '请选择是否需要登录', trigger: 'blur'}
        ]
      },
      nameError: null,
      codeError: null,
      descriptionError: null,
      pictureError: null,
      statusError: null,
      outLinkError: null,
      jumpUrlError: null,
      sequenceError: null,
      needLoginError: null,
      categoryCodeError: null
    }
  },
  methods: {
    init(id) {
      this.dataForm = _.cloneDeep(this.initForm)
      this.dataForm.id = id || null
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm']?.resetFields()
        if (this.dataForm.id) {
          this.$http({
            url: this.$http.adornUrl(`/admin/sys/app-menu-config`),
            method: 'get',
            params: this.$http.adornParams({id: this.dataForm.id})
          }).then(({data}) => {
            if (data && data.code === 0) {
              data.obj.limitCapacityList = data.obj.limitCapacity ? data.obj.limitCapacity.split(',') : []
              this.dataForm = data.obj
            }
          })
        }
      })
    },
    // 表单提交
    dataFormSubmit() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.dataForm.limitCapacity = this.dataForm.limitCapacityList.join(',')
          this.$http({
            url: this.$http.adornUrl(`/admin/sys/app-menu-config/${!this.dataForm.id ? 'save' : 'update'}`),
            method: 'post',
            data: this.$http.adornData(this.dataForm)
          }).then(({data}) => {
            if (data && data.code === 0) {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 1500,
                onClose: () => {
                  this.visible = false
                  this.$emit('refreshDataList')
                }
              })
            } else if (data && data.code === 303) {
              for (let it of data.obj) {
                this[`${it.field}Error`] = it.message
              }
            } else {
              this.$message.error(data.msg)
            }
          })
        }
      })
    }
  }
}
</script>
