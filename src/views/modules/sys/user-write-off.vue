<template>
  <div class="mod-list">
    <el-form :inline="true" :model="dataForm" ref="dataForm" @keyup.enter.native="getDataList()">
      <el-form-item prop="keyword" label="关键字">
        <el-input style="width: 300px" v-model="dataForm.keyword" placeholder="用户名、姓名、手机号模糊查询" clearable></el-input>
      </el-form-item>
      <el-form-item prop="userType" label="账号类型">
        <el-select v-model="dataForm.userType" placeholder="请选择" clearable>
          <el-option
              v-for="item in [{code: 0, name: '志愿者账号'}, {code:  1, name: '团队账号'}, {code:  2, name: '其他账号'}]"
              :label="item.name"
              :key="item.code"
              :value="item.code">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item prop="canceled" label="取消状态">
        <el-select v-model="dataForm.canceled" placeholder="请选择" clearable>
          <el-option
              v-for="item in [{code: true, name: '已取消'}, {code: false, name: '未取消'}]"
              :label="item.name"
              :key="item.code"
              :value="item.code">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item prop="applyDateRange" label="申请日期">
        <el-date-picker v-model="dataForm.applyDateRange" clearable type="daterange" value-format="yyyy-MM-dd" clearable
                        align="right" start-placeholder="开始" end-placeholder="结束">
        </el-date-picker>
      </el-form-item>
      <el-form-item prop="daysDuration" label="距离销户天数">
        <el-input-number v-model="dataForm.daysDuration" :min="0"></el-input-number>
      </el-form-item>
      <el-form-item prop="removed" label="销户状态">
        <el-select v-model="dataForm.removed" placeholder="请选择" clearable>
          <el-option
              v-for="item in [{code: true, name: '已销户'}, {code: false, name: '未销户'}]"
              :label="item.name"
              :key="item.code"
              :value="item.code">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item prop="removeFailed">
        <el-checkbox v-model="dataForm.removeFailed">销户失败</el-checkbox>
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-search" type="warning" @click="getDataList()">查询</el-button>
        <el-button @click="resetForm()"icon="el-icon-refresh-left">重置</el-button>
      </el-form-item>
    </el-form>
    <div class="f-s-c" style="padding: 15px 0; display: flex; justify-content: flex-end; align-items: center">
<!--      <div>-->
<!--        <el-button type="primary" @click="addOrUpdateHandle()" icon="el-icon-plus">新增</el-button>-->
<!--      </div>-->
    </div>
    <el-table :data="dataList" border v-loading="dataListLoading" @selection-change="selectionChangeHandle" style="width: 100%;">
      <el-table-column prop="username" header-align="center" align="center" label="用户名" width="130"/>
      <el-table-column prop="name" header-align="center" align="center" label="用户姓名" width="130"/>
      <el-table-column prop="mobile" header-align="center" align="center" label="用户手机号" width="120"/>
      <el-table-column prop="volunteerName" header-align="center" align="center" label="志愿者名" width="130"/>
      <el-table-column prop="userRole" header-align="center" align="center" label="用户角色身份" width="160">
        <template slot-scope="scope">
          <div style="display: flex; flex-direction: column; gap: 4px;">
            <el-tooltip v-for="(item, index) in scope.row.userRole && scope.row.userRole !== '' ? scope.row.userRole.split('\n') : []" :key="index" effect="dark" :content="item.indexOf('(') >= 0 ? item.substring(item.indexOf('(') + 1, item.indexOf(')')) : item" placement="right">
              <el-button style="line-height: 0px" type="text">{{item.indexOf('(') >= 0 ? item.substring(0, item.indexOf('(')) : item}}</el-button>
            </el-tooltip>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="applyDate" header-align="center" align="center" label="申请日期"/>
      <el-table-column prop="cancelApply" header-align="center" align="center" label="是否取消">
        <template slot-scope="scope">
          <el-tag v-if="!scope.row.cancelApply" size="small" type="danger">否</el-tag>
          <el-tag v-else size="small">是</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="userStatus" header-align="center" align="center" label="用户状态">
        <template slot-scope="scope">
          <el-tag v-if="!scope.row.userStatus" size="small" type="danger">禁用</el-tag>
          <el-tag v-else size="small" type="success">启用</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="planOrRealAccountLinkInfoRemoveDate" header-align="center" align="center" label="销户日期"/>
      <el-table-column prop="removed" header-align="center" align="center" label="销户状态">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.removed" size="small" type="success">已销户</el-tag>
          <el-tag v-else size="small">未销户</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="removeFailReason" header-align="center" align="center" label="销户失败原因" show-overflow-tooltip/>
      <el-table-column fixed="right" header-align="center" align="center" width="150" label="操作">
        <template slot-scope="scope">
          <el-button v-if="!scope.row.cancelApply && !scope.row.removed" type="text" @click="writeOffCancel(scope.row.id)">取消</el-button>
          <el-button v-if="!scope.row.cancelApply && !scope.row.removed" type="text" @click="writeOffFinalDeal(scope.row.id)">销户</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination @size-change="sizeChangeHandle" @current-change="currentChangeHandle" :current-page="pageIndex"
                   :page-sizes="[10, 20, 50, 100]" :page-size="pageSize" :total="totalPage" layout="total, sizes, prev, pager, next, jumper"/>
  </div>
</template>

<script>
  import listMixin from '@/mixins/list-mixins'
  export default {
    mixins: [listMixin],
    data () {
      return {
        mixinOptions: {
          dataUrl: '/admin/user/write_off/pageQuery'
        },
        dataForm: {
          keyword: null,
          userType: null,
          applyDateRange: null,
          applyDateStart: null,
          applyDateEnd: null,
          daysDuration: undefined,
          canceled: null,
          removed: null,
          removeFailed: null
        }
      }
    },
    methods: {
      resetForm() {
        this.$refs['dataForm']?.resetFields()
        this.dataForm.daysDuration = undefined
        this.getDataList()
      },
      queryBeforeHandle () {
        this.dataForm.applyDateStart = this.dataForm.applyDateRange && this.dataForm.applyDateRange.length === 2 ? this.dataForm.applyDateRange[0] : null
        this.dataForm.applyDateEnd = this.dataForm.applyDateRange && this.dataForm.applyDateRange.length === 2 ? this.dataForm.applyDateRange[1] : null
      },
      writeOffCancel(id) {
        this.$confirm(`确定取消注销?`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
          closeOnClickModal: false
        }).then(() => {
          this.$http({
            url: this.$http.adornUrl('/admin/user/write_off/cancel'),
            method: 'get',
            params: this.$http.adornParams({
              id: id
            })
          }).then(({data}) => {
            if (data && data.code === 0) {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 1000,
                onClose: () => {
                  this.getDataList()
                }
              })
            } else {
              this.$message.error(data.msg)
            }
          })
        }).catch(() => {
        })
      },
      writeOffFinalDeal(id) {
        this.$confirm(`确定进行销户，将删除个人注册数据（部分参与活动记录数据无法删除）?`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
          closeOnClickModal: false
        }).then(() => {
          this.$http({
            url: this.$http.adornUrl('/admin/user/write_off/finalWriteOffDeal'),
            method: 'get',
            params: this.$http.adornParams({
              id: id
            })
          }).then(({data}) => {
            if (data && data.code === 0) {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 1000,
                onClose: () => {
                  this.getDataList()
                }
              })
            } else {
              this.$message.error(data.msg)
            }
          })
        }).catch(() => {
        })
      }
    }
  }
</script>

<style lang="scss" scoped>
</style>
