<template>
  <el-dialog
      width="65%"
      v-loading="isLoading"
      :title="!dataForm.id ? '新增' : '修改'"
      :close-on-click-modal="false"
      :visible.sync="visible">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmit()"
             label-width="150px">
      <el-form-item label="角色编码" prop="code">
        <el-input v-model="dataForm.code" placeholder="角色编码"></el-input>
      </el-form-item>
      <el-form-item label="角色名称" prop="name">
        <el-input v-model="dataForm.name" placeholder="角色名称"></el-input>
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-radio-group v-model="dataForm.status">
          <el-radio :label=false>禁用</el-radio>
          <el-radio :label=true>正常</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="排序" prop="sequence">
        <el-input v-model="dataForm.sequence" placeholder="排序"></el-input>
      </el-form-item>
      <el-form-item size="mini" label="分配权限">
        <tree-table
            ref="tree"
            style="max-height:480px;overflow: auto"
            expand-key="name"
            :expand-type="false"
            :is-fold="false"
            :tree-type="true"
            :selectable="true"
            :columns=" [
          {
            title: '平台',
            type: 'template',
            template: 'type',
            minWidth: '15px',
          },
          {
            title: '菜单',
            key: 'name',
            minWidth: '250px',
          },
          {
            title: '操作',
            type: 'template',
            template: 'operation',
            minWidth: '200px'
          }
        ]"
            :data="menuList">
          <template slot="type" slot-scope="scope">
            <div>
              <el-tag size="large" type="danger" v-if="scope.row.type === 'app'">APP</el-tag>
              <el-tag size="large" v-if="scope.row.type === 'admin'">管理平台</el-tag>
            </div>
          </template>
          <template slot="operation" slot-scope="scope">
            <div>
              <div class="selectBtnGroup" v-if="scope.row.actionList.length>0">
                <el-button type="text" @click="selectAllAction(scope.row.actionList)">全选</el-button>
                <el-button type="text" @click="cancelSelectAllAction(scope.row.actionList)">取消全选</el-button>
              </div>
              <div style="margin: 2px 0;"></div>
              <el-checkbox-group v-model="dataForm.actionIdList">
                <el-checkbox v-for="item in scope.row.actionList" :label="item.id"
                             :key="item.id">{{ item.actionName }}
                </el-checkbox>
              </el-checkbox-group>
            </div>
          </template>
        </tree-table>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import _ from 'lodash'
import {treeDataTranslate} from '@/utils'

export default {
  data() {
    return {
      visible: false,
      isLoading: false,
      menuList: [],
      menuListTreeProps: {
        label: 'name',
        children: 'children'
      },
      dataForm: {
        id: undefined,
        actionIdList: [],
        code: '',
        name: '',
        status: true,
        sequence: 99
      },
      dataRule: {
        code: [
          {required: true, message: '角色编码不能为空', trigger: 'blur'}
        ],
        name: [
          {required: true, message: '角色名称不能为空', trigger: 'blur'}
        ],
        status: [
          {required: true, message: '角色状态不能为空', trigger: 'blur'}
        ],
        sequence: [
          {required: true, message: '角色排序不能为空', trigger: 'blur'}
        ]
      }
    }
  },
  methods: {
    init(id) {
      let menuList = []
      this.dataForm.actionIdList = []
      this.dataForm.id = id || undefined
      this.$http({
        url: this.$http.adornUrl('/admin/resource/menuListForRolePage'),
        method: 'get',
        params: this.$http.adornParams()
      }).then(({data}) => {
        menuList = data.obj
      }).then(() => {
        this.visible = true
        this.$nextTick(() => {
          this.$refs['dataForm']?.resetFields()
        })
      }).then(() => {
        if (this.dataForm.id) {
          this.$http({
            url: this.$http.adornUrl(`/admin/role/findOneById`),
            method: 'get',
            params: this.$http.adornParams({
              'id': this.dataForm.id
            })
          }).then(({data}) => {
            if (data && data.code === 0) {
              this.dataForm.code = data.obj.code
              this.dataForm.name = data.obj.name
              this.dataForm.status = data.obj.status
              this.dataForm.sequence = data.obj.sequence
              this.dataForm.actionIdList = data.obj.actionIdList
              _.forEach(menuList, (item) => {
                item._isChecked = data.obj.menuIdList.includes(item.id)
              })
              this.menuList = treeDataTranslate(menuList, 'id')
            }
          })
        } else {
          _.forEach(menuList, (item) => {
            item._isChecked = false
          })
          this.menuList = treeDataTranslate(menuList, 'id')
        }
      })
    },
    // 处理按钮全选
    selectAllAction(menuActionList) {
      _.forEach(menuActionList, (item) => {
        if (!this.dataForm.actionIdList.includes(item.id)) {
          this.dataForm.actionIdList.push(item.id)
        }
      })
    },
    // 处理取消全选
    cancelSelectAllAction(menuActionList) {
      _.forEach(menuActionList, (item) => {
        let indexOfMenu = this.dataForm.actionIdList.indexOf(item.id)
        if (indexOfMenu >= 0) {
          this.dataForm.actionIdList.splice(indexOfMenu, 1)
        }
      })
    },
    // 表单提交
    dataFormSubmit() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.isLoading = true
          this.$http({
            url: this.$http.adornUrl(`/admin/role/saveRole`),
            method: 'post',
            data: this.$http.adornData({
              'id': this.dataForm.id || undefined,
              'code': this.dataForm.code,
              'name': this.dataForm.name,
              'status': this.dataForm.status,
              'sequence': this.dataForm.sequence,
              'menuIdList': this.$refs['tree'].getCheckedProp('id'),
              'actionIdList': this.dataForm.actionIdList
            })
          }).then(({data}) => {
            this.isLoading = false
            if (data && data.code === 0) {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 1500,
                onClose: () => {
                  this.visible = false
                  this.$emit('refreshDataList')
                }
              })
            } else {
              this.$message.error(data.msg)
            }
          })
        }
      })
    }
  }
}
</script>
