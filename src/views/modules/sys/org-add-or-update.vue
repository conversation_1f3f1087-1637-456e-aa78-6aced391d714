<template>
  <div v-loading="isLoading">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmit()" label-width="120px">
        <el-form-item :label="'部门名'" prop="name">
          <el-input v-model="dataForm.name" :placeholder="'部门名'"></el-input>
        </el-form-item>
        <el-form-item :label="'部门代码'" prop="code">
          <el-input v-model="dataForm.code" :placeholder="'部门代码'"></el-input>
        </el-form-item>
        <el-form-item label="上级部门" prop="parentName" v-show="dataForm.parentId !== getRootPID()">
          <el-popover
              ref="orgListPopover"
              placement="bottom-start"
              trigger="click">
            <el-tree
                :data="orgList"
                :props="orgListTreeProps"
                node-key="id"
                :default-expanded-keys="[getRootPID(),dataForm.id]"
                ref="orgListTree"
                @current-change="orgListTreeCurrentChangeHandle"
                :highlight-current="true"
                :expand-on-click-node="false">
            </el-tree>
          </el-popover>
          <el-input v-model="dataForm.parentName" v-popover:orgListPopover :readonly="true" placeholder="点击选择上级部门"
                    class="menu-list__input"></el-input>
        </el-form-item>
        <el-form-item label="层级" prop="level">
          <el-radio-group label="层级" prop="sequence" v-model="dataForm.level">
            <el-radio :label="1">协会</el-radio>
            <el-radio :label="2">分协会</el-radio>
            <el-radio :label="3">社区</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="地址" prop="address">
          <el-input style="width: 70%" v-model="dataForm.address" :placeholder="'地址'" type="textarea" :rows="2"
                    clearable></el-input>
          <el-button style="float: right" type="primary" @click="showMap">地图定位</el-button>
        </el-form-item>
        <el-row>
          <el-col :span="24">
            <el-form-item label="经度" prop="longitude" >
              <el-input v-model="dataForm.longitude" :placeholder="'经度'" disabled></el-input>
              <!--        <span> {{dataForm.longitude}}</span>-->
            </el-form-item>
          </el-col>
<!--          <el-col :span="12" v-if="dataForm.id">-->
<!--            <el-form-item label="gps84经度" prop="wsg84Longitude">-->
<!--              <el-input v-model="dataForm.wsg84Longitude" :placeholder="'gps84经度'" disabled></el-input>-->
<!--              &lt;!&ndash;        <span> {{dataForm.longitude}}</span>&ndash;&gt;-->
<!--            </el-form-item>-->
<!--          </el-col>-->
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="纬度" prop="latitude">
              <el-input v-model="dataForm.latitude" :placeholder="'纬度'" disabled></el-input>
              <!--        <span> {{dataForm.latitude}}</span>-->
            </el-form-item>
          </el-col>
<!--          <el-col :span="12" v-if="dataForm.id">-->
<!--            <el-form-item label="gps84纬度" prop="wsg84Latitude">-->
<!--              <el-input v-model="dataForm.wsg84Latitude" :placeholder="'gps84纬度'" disabled></el-input>-->
<!--              &lt;!&ndash;        <span> {{dataForm.longitude}}</span>&ndash;&gt;-->
<!--            </el-form-item>-->
<!--          </el-col>-->
        </el-row>
        <!--      <el-form-item label="经度" prop="longitude">-->
        <!--        <el-input v-model="dataForm.longitude" :placeholder="'经度'"></el-input>-->
        <!--      </el-form-item>-->
        <!--      <el-form-item label="纬度" prop="latitude">-->
        <!--        <el-input v-model="dataForm.latitude" :placeholder="'纬度'"></el-input>-->
        <!--      </el-form-item>-->
        <el-form-item label="人口数量" prop="population">
          <el-input v-model="dataForm.population" :placeholder="'人口数量'"></el-input>
        </el-form-item>
        <el-form-item label="联系人" prop="linkMan">
          <el-input v-model="dataForm.linkMan" :placeholder="'联系人'"></el-input>
        </el-form-item>
        <el-form-item label="联系电话" prop="linkMobile">
          <el-input v-model="dataForm.linkMobile" :placeholder="'联系电话'"></el-input>
        </el-form-item>
        <el-form-item label="联系人身份证" prop="linkIdCard">
          <el-input v-model="dataForm.linkIdCard" :placeholder="'联系人身份证'"></el-input>
        </el-form-item>
        <el-form-item label="职能部门" prop="depId">
          <el-select style="width: 100%" v-model="dataForm.depId" placeholder="职能部门" clearable filterable>
            <el-option
                v-for="item in depList"
                :key="item.depId"
                :label="item.depName"
                :value="item.depId">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item :label="'行政区划'" prop="regionCode">
          <el-cascader style="width: 100%" placeholder="所属组织机构" v-model="dataForm.regionCode" :options="regionList"
                       :show-all-levels="false" clearable
                       :props="{checkStrictly: true,value: 'regionCode',label: 'regionName'}"></el-cascader>
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input v-model="dataForm.description" :placeholder="'描述'" type="textarea" :rows="5" maxlength="500" show-word-limit></el-input>
        </el-form-item>
        <el-form-item label="logo" prop="logoUrl">
          <el-upload
              class="avatar-uploader"
              :action="this.$http.adornUrl(`/admin/oss/upload`)"
              :headers="myHeaders"
              :data="{serverCode: this.serverCode,media:true}"
              :show-file-list="false"
              :on-success="function (res,file){return handleAvatarSuccess(res,file, 'logoUrl')}"
              :before-upload="beforeAvatarUpload">
            <img v-if="dataForm.logoUrl" :src="$http.adornAttachmentUrl(dataForm.logoUrl)" class="avatar">
            <i v-else class="el-icon-plus avatar-uploader-icon"></i>
          </el-upload>
        </el-form-item>
        <el-form-item label="可以被注册" prop="canBeRegister">
          <el-radio-group v-model="dataForm.canBeRegister">
            <el-radio :label="true" border>是</el-radio>
            <el-radio :label="false" border>否</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="排序号" prop="sequence">
          <el-input-number v-model="dataForm.sequence" controls-position="right" :min="0" label="排序号"></el-input-number>
        </el-form-item>
      <!--      <el-form-item label="类型" prop="type">-->
      <!--        <el-radio-group v-model="dataForm.type">-->
      <!--          <el-radio v-for="(type, index) in dataForm.typeList" :label="index+1" :key="index">{{ type }}</el-radio>-->
      <!--        </el-radio-group>-->
      <!--      </el-form-item>-->
    </el-form>
    <span slot="footer" class="dialog-footer" v-show="this.dataForm.code !== 'top_dept'" style="float: right; padding: 20px">
      <el-button @click="resetForm(dataForm.id)">重置</el-button>
      <el-button type="primary" @click="dataFormSubmit()">保存部门</el-button>
    </span>
    <el-dialog class="map-dialog" title="地图" :close-on-click-modal="false" width="80%" :visible.sync="mapVisible">
      <AMapInfo
          v-if="mapVisible"
          :mapVisible.sync="mapVisible"
          :address.sync="dataForm.address"
          :latitude.sync="dataForm.latitude"
          :longitude.sync="dataForm.longitude"></AMapInfo>
    </el-dialog>
  </div>
</template>

<script>
import {getFathersById, treeDataTranslate} from '@/utils'
import _ from 'lodash'
import Vue from 'vue'
import AMapInfo from '@/components/map/a-map-info.vue'
import {is8lPhone, isMobile} from "@/utils/validate";

const rootPID = 9999999999999

export default {
  data() {
    const validateAddress = (rule, value, callback) => {
        if (!this.dataForm.longitude || this.dataForm.longitude === '' || !this.dataForm.latitude || this.dataForm.latitude === '') {
            callback(new Error('需要地址经纬度，请通过地图定位选择地址！'))}
        else {callback()}
    }
    const validateContactPhone = (rule, value, callback) => {
        if (!isMobile(value) && !is8lPhone(value)) {
            callback(new Error('联系电话须为手机号或区号+8位座机号例如051288888888或0512-88888888'))
        } else {
            callback()
        }
    }
      var checkCertificateId = (rule, value, callback) => {
          var reg = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/;
          if (!value) {
              return callback(new Error('证件号码不能为空'));
          }

          var sum = 0,
              weights = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2],
              codes = "10X98765432";
          for (var i = 0; i < value.length - 1; i++) {
              sum += value[i] * weights[i];
          }
          var last = codes[sum % 11]; //计算出来的最后一位身份证号码
          if (value[value.length - 1] !== last) {
              callback(new Error("你输入的身份证号非法"));
          } else {
              callback();
          }
          if (reg.test(value)) {
              callback();
          } else {
              callback(new Error('证件号码格式不对，请检查!'));
          }

      };
    return {
      myHeaders: {Authorization: Vue.cookie.get('Authorization')},
      serverCode: 'LocalServer',
      depList: [],
      regionList: [],
      visible: false,
      isLoading: false,
      bakDataForm: {},
      dataForm: {
        id: null,
        type: 1,
        name: null,
        parentId: null,
        parentName: '',
        url: null,
        code: null,
        sequence: 0,
        icon: null,
        path: null,
        version: 0,
        status: true,
        level: '',
        address: null,
        longitude: null,
        latitude: null,
        wsg84Longitude: null,
        wsg84Latitude: null,
        population: null,
        linkMan: '',
        linkMobile: '',
        linkIdCard: '',
        description: '',
        canBeRegister: true,
        logoUrl: '',
        depId: '',
        regionCode: ''
      },
      dataRule: {
        name: [
          {required: true, message: '部门名称不能为空', trigger: 'blur'}
        ],
        parentName: [
          {required: true, message: '请选择上级部门', trigger: 'blur'}
        ],
        code: [
          {required: true, message: '请输入部门代码', trigger: 'blur'}
        ],
        regionCode: [
          {required: true, message: '请选择行政区划', trigger: 'blur'}
        ],
        depId: [
          {required: true, message: '请选择职能部门', trigger: 'blur'}
        ],
        level: [
          {required: true, message: '请选择层级', trigger: 'blur'}
        ],
        linkMan: [
          {required: true, message: '请输入联系人', trigger: 'blur'}
        ],
        linkMobile: [
          {required: true, message: '请输入联系人电话', trigger: 'blur'},
          {validator: validateContactPhone, trigger: 'change'}
        ],
        address: [
          {required: true, message: '请获取地址', trigger: 'blur'},
          {validator: validateAddress, trigger: 'change'}
        ],
        // longitude: [
        //   {required: true, message: '请输入经纬度', trigger: 'blur'}
        // ],
        // latitude: [
        //   {required: true, message: '请输入经纬度', trigger: 'blur'}
        // ],
        description: [
          {required: true, message: '请输入描述,最多500字', trigger: 'blur', max:500}
        ],
        linkIdCard: [
            {required: true, validator: checkCertificateId, trigger: 'change'}
        ],
        logoUrl: [
            {required: true, message: '请选择logo', trigger: 'blur'}
        ]
      },
      orgList: [],
      orgListTreeProps: {
        label: 'name',
        children: 'children'
      },
      mapVisible: false
    }
  },
  components: {
    AMapInfo
  },
  created() {
  },
  methods: {
    async init(id, parentId) {
      this.isLoading = true
      this.dataForm.id = id || undefined
      this.orgList = []
      await this.initDepList()
      await this.initRegionTree()
      await this.initOrgTree()
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm']?.resetFields()
        this.dataForm.parentId = parentId
        this.bakDataForm = _.cloneDeep(this.dataForm)
        if (!this.dataForm.id) {
          // 新增
          this.orgListTreeSetCurrentNode()
        } else {
          // 修改
          this.$http({
            url: this.$http.adornUrl(`/admin/org/getOrg`),
            method: 'get',
            params: this.$http.adornParams({orgId: this.dataForm.id})
          }).then(({data}) => {
            this.dataForm.id = data.obj.id
            this.dataForm.name = data.obj.name
            this.dataForm.parentId = data.obj.parentId || rootPID
            this.dataForm.code = data.obj.code
            this.dataForm.sequence = data.obj.sequence
            this.dataForm.codePrefix = data.obj.codePrefix
            this.dataForm.level = data.obj.level
            this.dataForm.version = data.obj.version
            this.dataForm.address = data.obj.address
            this.dataForm.longitude = data.obj.longitude
            this.dataForm.latitude = data.obj.latitude
            this.dataForm.wsg84Longitude = data.obj.wsg84Longitude
            this.dataForm.wsg84Latitude = data.obj.wsg84Latitude
            this.dataForm.population = data.obj.population
            this.dataForm.linkMan = data.obj.linkMan
            this.dataForm.linkMobile = data.obj.linkMobile
            this.dataForm.linkIdCard = data.obj.linkIdCard
            this.dataForm.description = data.obj.description
            this.dataForm.canBeRegister = data.obj.canBeRegister
            this.dataForm.logoUrl = data.obj.logoUrl
            this.dataForm.depId = data.obj.depId
            this.dataForm.regionCode = getFathersById(data.obj.regionCode, this.regionList, 'regionCode')
            this.orgListTreeSetCurrentNode()
            this.bakDataForm = _.cloneDeep(this.dataForm)
          })
        }
        this.isLoading = false
      })
    },
    getRootPID() {
      return rootPID
    },
    resetForm() {
      this.dataForm = _.cloneDeep(this.bakDataForm)
    },
    async initOrgTree() {
      await this.$http({
        url: this.$http.adornUrl('/admin/org/all'),
        method: 'get',
        params: this.$http.adornParams()
      }).then(({data}) => {
        this.orgList = treeDataTranslate(data)
      })
    },
    // 初始化职能部门列表
    async initDepList() {
      await this.$http({
        url: this.$http.adornUrl(`/admin/zyz/sync-dep/all`),
        method: 'get',
        params: this.$http.adornParams({id: this.dataForm.id})
      }).then(({data}) => {
        this.depList = data
      })
    },
    // 初始化行政区划树
    async initRegionTree() {
      await this.$http({
        url: this.$http.adornUrl(`/admin/zyz/sync/region-dict/all`),
        method: 'get',
        params: this.$http.adornParams()
      }).then(({data}) => {
        this.regionList = treeDataTranslate(data, 'regionCode', 'parentCode')
      })
    },
    // 部门树选中
    orgListTreeCurrentChangeHandle(data, node) {
      this.dataForm.parentId = data.id
      this.dataForm.parentName = data.name
      this.$refs[`orgListPopover`].doClose()
    },
    // 部门树设置当前选中节点
    orgListTreeSetCurrentNode() {
      if (this.dataForm.parentId !== this.getRootPID()) {
        this.$refs.orgListTree.setCurrentKey(this.dataForm.parentId)
        this.dataForm.parentName = (this.$refs.orgListTree.getCurrentNode() || {})['name']
      } else {
        this.$refs.orgListTree.setCurrentKey([])
        this.dataForm.parentName = ''
      }
    },
    // 表单提交
    dataFormSubmit() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          let tmpRegionCode = this.dataForm.regionCode
          this.dataForm.regionCode = tmpRegionCode && Array.isArray(tmpRegionCode) ? this.dataForm.regionCode[this.dataForm.regionCode.length - 1] : tmpRegionCode
          this.$http({
            url: this.$http.adornUrl(`/admin/org/${!this.dataForm.id ? 'save' : 'update'}`),
            method: 'post',
            data: this.$http.adornData({
              'id': this.dataForm.id || undefined,
              'name': this.dataForm.name,
              'parentId': this.dataForm.parentId === rootPID ? null : this.dataForm.parentId,
              'code': this.dataForm.code,
              'codePrefix': this.dataForm.codePrefix,
              'sequence': this.dataForm.sequence,
              'level': this.dataForm.level,
              'version': this.dataForm.version,
              'address': this.dataForm.address,
              'longitude': this.dataForm.longitude,
              'latitude': this.dataForm.latitude,
              'wsg84Longitude': this.dataForm.wsg84Longitude,
              'wsg84Latitude': this.dataForm.wsg84Latitude,
              'population': this.dataForm.population,
              'linkMan': this.dataForm.linkMan,
              'linkMobile': this.dataForm.linkMobile,
              'linkIdCard': this.dataForm.linkIdCard,
              'description': this.dataForm.description,
              'canBeRegister': this.dataForm.canBeRegister,
              'logoUrl': this.dataForm.logoUrl,
              'depId': this.dataForm.depId,
              'regionCode': this.dataForm.regionCode
            })
          }).then(({data}) => {
            if (data && data.code === 0) {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 1500,
                onClose: () => {
                  this.visible = false
                  this.$emit('refreshDataList', data.obj.id)
                }
              })
            } else {
              this.$message.error(data.msg)
            }
          })
        }
      })
    },// 上传图片成功
    handleAvatarSuccess(res, file, field) {
      if (res.success) {
        this.$message({
          message: '操作成功',
          type: 'success',
          duration: 1500
        })
        console.log(res)
        this.dataForm[`${field}`] = res.obj.path
      } else {
        this.$message.error('上传失败')
      }
    },
    beforeAvatarUpload: function (file) {
      var isJPG = file.type === 'image/jpeg'
      var isPNG = file.type === 'image/png'
      var isBMP = file.type === 'image/bmp'
      var isLt2M = file.size / 1024 / 1024 < 2

      if (!isJPG && !isPNG && !isBMP) {
        this.$message.error('上传图片只能是图片!')
      }
      if (!isLt2M) {
        this.$message.error('上传文件大小不能超过 2MB!')
      }
      return (isJPG || isPNG || isBMP) && isLt2M
    },
    showMap() {
      this.mapVisible = true
    }
  }
}
</script>
