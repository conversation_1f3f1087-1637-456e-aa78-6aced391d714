<template>
  <el-dialog
    :title="'接口调试'"
    :close-on-click-modal="false"
    width="40%"
    center
    :visible.sync="visible">
    <el-form  :model="dataForm" ref="dataForm" @keyup.enter.native="dataFormSubmit()" label-width="220px">
      <div class="mui-input-row" v-for="(items,index) in domList">
        <template v-if="items.type!=='Boolean'&& items.type!=='boolean'">
          <el-form-item :label="labelList[index]" :prop="domList[index].name">
            <el-input class="inputBox" v-model="items.name" size="mini" placeholder="请输入" clearable></el-input>
          </el-form-item>
        </template>
        <template v-else-if="items.type==='Boolean'||items.type==='boolean'">
          <el-form-item :label="labelList[index]">
            <el-radio-group  v-model="items.name">
              <el-radio :label="false">否</el-radio>
              <el-radio :label="true">是</el-radio>
            </el-radio-group>
          </el-form-item>
        </template>
      </div>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">开始调试</el-button>
    </span>
  </el-dialog>
</template>

<script>
  export default {
    data () {
      return {
        visible: false,
        domList: [],
        labelList: [],
        responseType: '',
        url: '',
        dataForm: {
          code: ''
        },
        id: null
      }
    },
    methods: {
      init (id, code, responseType) {
        this.domList = []
        this.labelList = []
        this.id = id || null
        this.dataForm.code = code || null
        this.responseType = responseType || null
        this.visible = true
        this.$http({
          url: this.$http.adornUrl('/admin/apiParam/getParamListByUrlId'),
          method: 'get',
          params: this.$http.adornParams({'urlId': this.id})
        }).then(({data}) => {
          this.domList = data.obj || []
          for (var i = 0; i < this.domList.length; i++) {
            this.labelList.push(this.domList[i].name + '(' + this.domList[i].type + '):')
            this.domList[i]['label'] = this.domList[i].name
            this.dataForm[this.domList[i].name] = ''
            this.domList[i].name = ''
          }
        })
      },
      // 表单提交
      dataFormSubmit () {
        var self = this
        this.domList.forEach(function (e) {
          self.dataForm[e.label] = e.name
        })
        console.log('表单数据', self.dataForm)
        var subOfUrl = ''
        for (let key of Object.keys(this.dataForm)) {
          var value = this.dataForm[key]
          if (value.indexOf(',') > -1) {
            this.dataForm[key] = value.split(',')
            for (var i = 0; i < this.dataForm[key].length; i++) {
              subOfUrl += key + '=' + this.dataForm[key][i] + '&'
            }
          } else {
            subOfUrl += key + '=' + this.dataForm[key] + '&'
          }
        }
        if (subOfUrl.charAt(subOfUrl.length - 1) === '&') {
          subOfUrl = subOfUrl.substring(0, subOfUrl.length - 1)
        }
        subOfUrl = subOfUrl.replace(/\+/g, '%2B')
        if (this.responseType === 'text/plain') {
          this.$http({
            url: this.$http.adornUrl(`/admin/api/getApiOfCTIsTextPlain?` + subOfUrl),
            method: 'get'
          }).then(({data}) => {
            this.resultHandle(data)
          })
        } else if (this.responseType === 'application/json') {
          this.$http({
            url: this.$http.adornUrl(`/admin/api/getApiOfCTIsApplicationJson?` + subOfUrl),
            method: 'get'
          }).then(({data}) => {
            this.resultHandle(data)
          })
        }
      },
      resultHandle (data) {
        console.log(data)
        if (data && data.res.status === 1) {
          this.$message({
            message: '操作成功',
            type: 'success',
            duration: 1500,
            onClose: () => {
              this.visible = false
              this.open(data)
            }
          })
        } else {
          this.$message.error('ErrorCode:' + data.res.errorCode + ',' + 'ErrorMessage:' + data.res.errorMessage)
        }
      },
      open (data) {
        console.log(data)
        this.$alert(data, '第三方接口调用结果', {
          confirmButtonText: '确定',
          callback: action => {
            this.$emit('refreshDataList')
          }
        })
      }
    }
  }
</script>

<style lang="scss">
  .inputBox .el-input__inner {
    width: 88%;
  }
</style>
