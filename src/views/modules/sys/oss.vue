<template>
  <div class="mod-oss">
    <el-form :inline="true" :model="dataForm" ref="dataForm" @keyup.enter.native="getDataList()">
      <el-form-item label="名称:" prop="name">
        <el-input v-model="dataForm.name" placeholder="名称" clearable></el-input>
      </el-form-item>
      <el-form-item label="原文件名:" prop="fileName">
        <el-input v-model="dataForm.fileName" placeholder="原文件名" clearable></el-input>
      </el-form-item>
      <el-form-item label="标签:" prop="labelName">
        <el-input v-model="dataForm.labelName" placeholder="标签" clearable></el-input>
      </el-form-item>
      <el-form-item label="文件类型:" prop="type">
        <el-select v-model="dataForm.type" clearable placeholder="请选择">
          <el-option v-for="item in typeList" :key="item.id" :label="item.text" :value="item.id"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="存储方式:" prop="serverCode">
        <el-select v-model="dataForm.serverCode" clearable placeholder="请选择">
          <el-option v-for="item in serverList" :key="item.id" :label="item.text" :value="item.id"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button v-if="isAuth('oss-list')" @click="getDataList()">查询</el-button>
        <el-button v-if="isAuth('oss-list')" @click="resetForm()">重置</el-button>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="configHandle()" v-if="isAuth('sys:oss:config')">OSS配置</el-button>
        <el-button type="primary" @click="uploadHandle()" v-if="isAuth('oss-upload')">上传文件</el-button>
        <el-button type="danger" @click="deleteHandle()" v-if="isAuth('oss-batch-remove')" :disabled="dataListSelections.length <= 0">批量删除</el-button>
      </el-form-item>
    </el-form>
    <el-table
      :data="dataList"
      border
      v-loading="dataListLoading"
      @selection-change="selectionChangeHandle"
      style="width: 100%;">
      <el-table-column
        type="selection"
        header-align="center"
        align="center"
        width="50">
      </el-table-column>
      <el-table-column
        prop="name"
        header-align="center"
        align="center"
        label="名称">
      </el-table-column>
      <el-table-column
        prop="fileName"
        header-align="center"
        align="center"
        label="原文件名">
      </el-table-column>
      <el-table-column
        prop="typeText"
        header-align="center"
        align="center"
        label="文件类型">
      </el-table-column>
      <el-table-column
        prop="serverText"
        header-align="center"
        align="center"
        label="存储方式">
      </el-table-column>
      <el-table-column
        prop="labelName"
        header-align="center"
        align="center"
        label="标签">
      </el-table-column>
      <el-table-column
        prop="path"
        header-align="center"
        align="center"
        label="URL地址">
      </el-table-column>
      <el-table-column
        prop="createDate"
        header-align="center"
        align="center"
        label="创建时间">
      </el-table-column>
      <el-table-column
        v-if="isAuth('oss-view') || isAuth('oss-download') || isAuth('oss-delete')"
        fixed="right"
        header-align="center"
        align="center"
        label="操作">
        <template slot-scope="scope">
          <el-button type="text" size="small" v-if="isAuth('oss-view')" @click="view(scope.row.id)">查看</el-button>
          <el-button type="text" size="small" v-if="isAuth('oss-download')" @click="download(scope.row)">下载</el-button>
          <el-button type="text" v-if="checkPdf(scope.row.path)" size="small" @click="exportQrCodeHandle(scope.row.name)">二维码</el-button>
          <el-button type="text" size="small" v-if="isAuth('oss-delete')" @click="deleteHandle(scope.row.id, scope.row.fileName)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      @size-change="sizeChangeHandle"
      @current-change="currentChangeHandle"
      :current-page="pageIndex"
      :page-sizes="[10, 20, 50, 100]"
      :page-size="pageSize"
      :total="totalPage"
      layout="total, sizes, prev, pager, next, jumper">
    </el-pagination>
    <!-- 弹窗, 云存储配置 -->
    <config v-if="configVisible" ref="config"></config>
    <!-- 弹窗, 上传文件 -->
    <upload v-if="uploadVisible" ref="upload" @refreshDataList="getDataList"></upload>
    <!-- 弹窗, 二维码展示 -->
    <qr-list v-if="qrListVisible" ref="qrlist"></qr-list>
  </div>
</template>

<script>
  import Config from './oss-config'
  // import Upload from './oss-upload'
  import Upload from './oss-add-or-update'
  import QrList from '@/components/qr-list/index'
  export default {
    data () {
      return {
        dataForm: {
          fileName: null,
          labelName: null,
          name: null,
          serverCode: null,
          type: null
        },
        dataList: [],
        typeList: [],
        serverList: [],
        pageIndex: 1,
        pageSize: 10,
        totalPage: 0,
        dataListLoading: false,
        dataListSelections: '',
        qrListVisible: false,
        configVisible: false,
        uploadVisible: false
      }
    },
    components: {
      Config,
      Upload,
      QrList
    },
    activated () {
      this.init()
    },
    methods: {
      init () {
        this.getDataList()
        this.getTypeList()
        this.getServerList()
      },
      // 获取文件分类下拉列表
      getTypeList () {
        this.$http({
          url: this.$http.adornUrl('/admin/oss/typeList'),
          method: 'get',
          params: this.$http.adornParams({})
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.typeList = data.obj
          } else {
            this.typeList = []
            this.$message.error(data.msg)
          }
        })
      },
      // 获取文件存储方式下拉列表
      getServerList () {
        this.$http({
          url: this.$http.adornUrl('/admin/oss/serverList'),
          method: 'get',
          params: this.$http.adornParams({})
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.serverList = data.obj
          } else {
            this.serverList = []
            this.$message.error(data.msg)
          }
        })
      },
      // 获取数据列表
      getDataList () {
        this.dataListLoading = true
        this.$http({
          url: this.$http.adornUrl('/admin/oss/page'),
          method: 'post',
          data: this.$http.adornData({
            'fileName': this.dataForm.fileName,
            'name': this.dataForm.name,
            'labelName': this.dataForm.labelName,
            'type': this.dataForm.type,
            'serverCode': this.dataForm.serverCode,
            'currentPage': this.pageIndex,
            'pageSize': this.pageSize
          })
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.dataList = data.obj.records
            this.totalPage = data.obj.total
          } else {
            this.dataList = []
            this.totalPage = 0
          }
          this.dataListLoading = false
        })
      },
      // 重置
      resetForm () {
        this.$nextTick(() => {
          this.$refs['dataForm']?.resetFields()
          this.getDataList()
        })
      },
      checkPdf (path) {
        let FileExt = path.replace(/.+\./, '')
        return FileExt.toLowerCase() === 'pdf'
      },
      exportQrCodeHandle (name) {
        this.qrListVisible = true
        this.$nextTick(() => {
          this.$refs.qrlist.initWithPdfUrl(name)
        })
      },
      // 每页数
      sizeChangeHandle (val) {
        this.pageSize = val
        this.pageIndex = 1
        this.getDataList()
      },
      // 当前页
      currentChangeHandle (val) {
        this.pageIndex = val
        this.getDataList()
      },
      // 多选
      selectionChangeHandle (val) {
        this.dataListSelections = val
      },
      // 云存储配置
      configHandle () {
        this.configVisible = true
        this.$nextTick(() => {
          this.$refs.config.init()
        })
      },
      // 上传文件
      uploadHandle () {
        this.uploadVisible = true
        this.$nextTick(() => {
          this.$refs.upload.init()
        })
      },
      // 上传文件
      view (id) {
        this.uploadVisible = true
        this.$nextTick(() => {
          this.$refs.upload.init(id)
        })
      },
      // 下载文件
      download (row) {
        let filepath = row.path
        if (row.serverCode === 'LocalServer') {
          filepath = this.$http.adornUrl(row.path)
        }
        let link = document.createElement('a')
        link.setAttribute('download', row.fileName)
        link.setAttribute('target', '_blank')
        link.href = filepath
        link.click()
      },
      // 删除
      deleteHandle (id, name) {
        let ids = id ? [id] : this.dataListSelections.map(item => {
          return item.id
        })
        var names = name ? [name] : this.dataListSelections.map(item => {
          return item.fileName
        })
        this.$confirm(`确定对[${names.join(',')}]进行[${id ? '删除' : '批量删除'}]操作?`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
          closeOnClickModal: false
        }).then(() => {
          this.$http({
            url: this.$http.adornUrl('/admin/oss/removeByIds'),
            method: 'get',
            params: this.$http.adornParams({ids: ids.join(',')})
          }).then(({data}) => {
            if (data && data.code === 0) {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 1500,
                onClose: () => {
                  this.getDataList()
                }
              })
            } else {
              this.$message.error(data.msg)
            }
          })
        }).catch(() => {})
      }
    }
  }
</script>
