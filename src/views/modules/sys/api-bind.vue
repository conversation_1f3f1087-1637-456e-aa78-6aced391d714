<template>
  <el-dialog
    title="绑定Api"
    :close-on-click-modal="false"
    :visible.sync="visible"
    width="85%"
    custom-class="api-bind">
    <div style="height: 100%">
      <el-transfer
        ref="apiBindTransfer"
        class="api-bind-transfer"
        filterable
        :filter-method="filterMethod"
        filter-placeholder="请输入接口名称"
        v-model="value"
        :titles="['选择接口', '已选择接口']"
        :data="data">
      </el-transfer>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="apiSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  data () {
    return {
      visible: false,
      bindId: null,
      bindApiPath: null,
      data: [],
      value: []
    }
  },
  methods: {
    init (id, bindApiPath) {
      this.visible = true
      this.bindId = id || null
      this.bindApiPath = bindApiPath
      this.getApiList()
      this.$nextTick(() => {
        this.$refs.apiBindTransfer.clearQuery('left')
        this.$refs.apiBindTransfer.clearQuery('right')
        this.$http({
          url: this.$http.adornUrl(`/admin/${bindApiPath}/bindApi`),
          method: 'get',
          params: this.$http.adornParams({
            'bindId': id
          })
        }).then(({data}) => {
          this.value = data.obj
        })
      })
    },
    getApiList () {
      this.$http({
        url: this.$http.adornUrl('/admin/sys_api/bind_list'),
        method: 'get',
        params: this.$http.adornParams()
      }).then(({data}) => {
        this.data = data.obj
      })
    },
    filterMethod (query, item) {
      return item.label.indexOf(query) > -1
    },
    apiSubmit () {
      this.$http({
        url: this.$http.adornUrl(`/admin/${this.bindApiPath}/savePath`),
        method: 'post',
        data: this.$http.adornData({
          'bindId': this.bindId,
          'apiIdList': this.value
        })
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.$message({
            message: '操作成功',
            type: 'success',
            duration: 1500,
            onClose: () => {
              this.visible = false
              this.$emit('refreshDataList')
            }
          })
        } else {
          this.$message.error(data.msg)
        }
      })
    }
  }
}
</script>

<style lang="scss">
.api-bind-transfer {
  height: 100%;
  .el-transfer-panel {
    width: 40%;
    height: 95%;
    .el-transfer-panel__body {
      height: 90%;
      .el-checkbox-group {
        height: 88%;
      }
    }
  }
}

.api-bind {
  height: 70%;
  .el-dialog__body {
    height: 80%;
    padding-bottom: 15px;
  }
}
</style>
