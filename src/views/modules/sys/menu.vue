<template>
  <div class="mod-menu">
    <el-row :gutter="20">
      <el-col :span="8">
        <el-card class="box-card">
          <el-form :inline="true" :model="dataForm">
            <el-form-item :label="'所属平台'" prop="type">
              <el-select v-model="dataForm.type" placeholder="请选择" @change="changeType()">
                <el-option
                    :label="'管理端'"
                    :value="'admin'">
                </el-option>
                <el-option
                    :label="'APP'"
                    :value="'app'">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" v-if="isAuth('menu-save')" @click="menuAddOrUpdateHandle()">创建菜单</el-button>
            </el-form-item>
          </el-form>
          <el-table
              :data="dataList"
              border
              style="width: 100%;height: 700px;overflow-y: scroll"
              highlight-current-row
              @current-change="handleCurrentChange">
            <table-tree-column
                prop="name"
                header-align="center"
                treeKey="id"
                label="名称">
            </table-tree-column>
            <el-table-column
                header-align="center"
                align="center"
                width="80"
                label="状态">
              <template slot-scope="scope">
                <el-badge is-dot v-if="scope.row.status" type="success" class="status-badge">
                </el-badge>
                <el-badge is-dot v-else type="danger" class="status-badge">
                </el-badge>
                <icon-svg v-if="scope.row.icon" :name="scope.row.icon || ''"></icon-svg>
                <img v-else :src="$http.adornAttachmentUrl(scope.row.iconPicture)" style="width: 30px"/>
              </template>
            </el-table-column>
            <el-table-column
                prop="sequence"
                header-align="center"
                align="center"
                width="80"
                label="排序">
            </el-table-column>
            <el-table-column
                v-if="isAuth('menu-update') || isAuth('menu-delete')"
                header-align="center"
                width="130"
                align="center"
                label="操作">
              <template slot-scope="scope">
                <el-button type="text" v-if="isAuth('menu-update')" size="small"
                           @click.stop="menuAddOrUpdateHandle(null,scope.row.id)">
                  新增子菜单
                </el-button>
                <el-button type="text" size="small" v-if="isAuth('menu-delete')"
                           @click.stop="deleteHandle(scope.row.id)">删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-col>
      <el-col :span="8">
        <el-card>
          <!-- 弹窗, 新增 / 修改 -->
          <menu-add-or-update v-if="menuAddOrUpdateVisible" ref="menuAddOrUpdate"
                              @refreshDataList="saveOrUpdateCallback"></menu-add-or-update>
          <el-empty v-else description="请选择一个菜单"></el-empty>
        </el-card>
      </el-col>
      <el-col :span="8">
        <el-card>
          <!-- 弹窗, 新增 / 修改 -->
          <action-list v-if="actionAddOrUpdateVisible" ref="actionList"
                       @refreshDataList="getDataList"></action-list>
          <el-empty v-else description="请选择一个菜单"></el-empty>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import TableTreeColumn from '@/components/table-tree-column'
import MenuAddOrUpdate from './menu-add-or-update'
import ActionList from './action'
import {treeDataTranslate} from '@/utils'

export default {
  data() {
    return {
      dataForm: {
        type: 'admin'
      },
      dataList: [],
      dataListLoading: false,
      menuAddOrUpdateVisible: false,
      actionAddOrUpdateVisible: false
    }
  },
  components: {
    TableTreeColumn,
    MenuAddOrUpdate,
    ActionList
  },
  activated() {
    this.getDataList()
  },
  methods: {
    // 获取数据列表
    getDataList() {
      this.dataListLoading = true
      this.$http({
        url: this.$http.adornUrl('/admin/resource/resourceListByType'),
        method: 'get',
        params: this.$http.adornParams({type: this.dataForm.type})
      }).then(({data}) => {
        this.dataList = treeDataTranslate(data.obj, 'id')
        this.dataListLoading = false
      })
    },
    changeType() {
      this.menuAddOrUpdateVisible = false
      this.actionAddOrUpdateVisible = false
      this.getDataList()
    },
    // 保存或者编辑完成后回调
    saveOrUpdateCallback(menuId) {
      this.getDataList()
      // 保存完了后刷一下“action”的控件
      this.actionAddOrUpdateHandle(menuId)
      this.menuAddOrUpdateHandle(menuId)
    },
    // 处理选中行
    handleCurrentChange(val) {
      this.menuAddOrUpdateHandle(val.id)
      this.actionAddOrUpdateHandle(val.id)
    },
    // 菜单控件
    menuAddOrUpdateHandle(menuId, parentId) {
      this.menuAddOrUpdateVisible = true
      this.$nextTick(() => {
        this.$refs.menuAddOrUpdate.init(menuId, parentId, this.dataForm.type)
      })
    },
    // 操作控件
    actionAddOrUpdateHandle(menuId) {
      // 新增菜单时候因为没有菜单的id，所以没办法编辑“action”
      if (!menuId) {
        return
      }
      this.actionAddOrUpdateVisible = true
      this.$nextTick(() => {
        this.$refs.actionList.init(menuId)
      })
    },
    // 删除
    deleteHandle(id) {
      this.$confirm(`确定要删除此菜单吗?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        closeOnClickModal: false
      }).then(() => {
        this.$http({
          url: this.$http.adornUrl(`/admin/resource/removeByIds`),
          method: 'get',
          params: this.$http.adornParams({'ids': id})
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.$message({
              message: '操作成功',
              type: 'success',
              duration: 1500,
              onClose: () => {
                this.getDataList()
                this.menuAddOrUpdateVisible = false
                this.actionAddOrUpdateVisible = false
              }
            })
          } else {
            this.$message.error(data.msg)
          }
        })
      }).catch(() => {
      })
    }
  }
}
</script>
<style>
.status-badge {
  margin-right: 5px;
}
</style>
