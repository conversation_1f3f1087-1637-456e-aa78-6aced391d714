<template>
  <el-dialog
    :title="!dataForm.id ? '新增' : '修改'"
    :close-on-click-modal="false"
    width="60%"
    center
    :visible.sync="visible">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmit()" label-width="110px">
      <el-row :gutter="20" >
        <el-col :span="12"><div class="grid-content bg-purple">
          <el-form-item label="接口地址" prop="url">
            <el-input v-model="dataForm.url" size="mini" placeholder="请输入"></el-input>
          </el-form-item>
        </div></el-col>
        <el-col :span="12"><div class="grid-content bg-purple">
          <el-form-item label="接口code" prop="code">
            <el-input v-model="dataForm.code" :disabled="this.dataForm.id ? true : false" size="mini" placeholder="请输入"></el-input>
          </el-form-item>
        </div></el-col>
      </el-row>
      <el-row :gutter="20" >
        <el-col :span="12"><div class="grid-content bg-purple">
          <el-form-item label="接口类型" prop="method">
            <el-select v-model="dataForm.method" size="mini" clearable placeholder="请选择">
              <el-option
                v-for="item in option1"
                :key="item.code"
                :label="item.name"
                :value="item.code">
              </el-option>
            </el-select>
          </el-form-item>
        </div></el-col>
        <el-col :span="12"><div class="grid-content bg-purple">
          <el-form-item label="接口状态" prop="status">
            <el-radio-group  v-model="dataForm.status">
              <el-radio :label="false">禁用</el-radio>
              <el-radio :label="true">正常</el-radio>
            </el-radio-group>
          </el-form-item>
        </div></el-col>
      </el-row>
      <el-row :gutter="20" >
        <el-col :span="12"><div class="grid-content bg-purple">
          <el-form-item label="接口返回类型" prop="responseType">
            <el-select v-model="dataForm.responseType" size="mini" clearable placeholder="请选择">
              <el-option
                v-for="item in option2"
                :key="item.code"
                :label="item.name"
                :value="item.code">
              </el-option>
            </el-select>
          </el-form-item>
        </div></el-col>
        <el-col :span="12"><div class="grid-content bg-purple">
          <el-form-item label="接口入参类型" prop="paramType">
            <el-select v-model="dataForm.paramType" size="mini" clearable  placeholder="请选择" @change="change(dataForm.paramType)">
              <el-option
                v-for="item in option3"
                :key="item.code"
                :label="item.name"
                :value="item.code">
              </el-option>
            </el-select>
          </el-form-item>
        </div></el-col>
      </el-row>
      <el-form-item label="接口描述" prop="mark">
        <el-input type="textarea" v-model="dataForm.mark"size="mini" placeholder="请输入" :rows="3"></el-input>
      </el-form-item>
    </el-form>
    <el-card class="box-card" v-if="tableVisible">
      <div slot="header" class="clearfix">
        <span v-if="commonParamManageVisible">接口普通参数管理</span>
        <span v-if="objectParamManageVisible">接口对象参数管理</span>
        <el-button style="float: right; padding: 3px 0" type="text" @click="paramAddOrUpdate(dataList)">添加参数</el-button>
      </div>
      <el-table
        :data="dataList"
        border
        v-loading="dataListLoading"
        style="width: 100%;">
        <el-table-column
          prop="sortNum"
          header-align="center"
          align="center"
          label="序号">
        </el-table-column>
        <el-table-column
          prop="type"
          header-align="center"
          align="center"
          label="类型">
        </el-table-column>
        <el-table-column
          prop="name"
          header-align="center"
          align="center"
          label="名称">
        </el-table-column>
        <el-table-column
          prop="need"
          header-align="center"
          align="center"
          label="是否必填">
          <template slot-scope="scope">
            <el-tag v-if="scope.row.need === false" size="small" type="danger">否</el-tag>
            <el-tag v-if="scope.row.need === true" size="small">是</el-tag>
          </template>
        </el-table-column>
        <el-table-column
          fixed="right"
          header-align="center"
          align="center"
          width="150"
          label="操作">
          <template slot-scope="scope">
            <el-button v-if="isAuth('sys:api:update')" type="text" size="small" @click="paramAddOrUpdate(dataList,scope.row.sortNum)">修改</el-button>
            <el-button v-if="isAuth('sys:api:delete')" type="text" size="small" @click="deleteHandle(scope.row.sortNum)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
    <param-add-or-update v-if="paramAddOrUpdateVisible" ref="paramAddOrUpdate" @refreshDataList="getParamList"></param-add-or-update>
  </el-dialog>
</template>

<script>
  import {isURL} from '../../../utils/validate'
  import ParamAddOrUpdate from './third-api-param-add-or-update'
  export default {
    data () {
      var validateUrl = (rule, value, callback) => {
        if (!isURL(value)) {
          callback(new Error('接口地址格式不正确'))
        } else {
          callback()
        }
      }
      var validateCode = (rule, value, callback) => {
        if (!this.dataForm.id) {
          this.$http({
            url: this.$http.adornUrl('/admin/api/validateCode'),
            method: 'get',
            params: this.$http.adornParams({'code': this.dataForm.code})
          }).then(({data}) => {
            if (!data) {
              callback()
            } else {
              callback(new Error('接口code已存在！'))
            }
          })
        } else {
          callback()
        }
      }
      return {
        visible: false,
        tableVisible: false,
        commonParamManageVisible: false,
        objectParamManageVisible: false,
        dataListLoading: false,
        paramAddOrUpdateVisible: false,
        option1: [],
        option2: [],
        option3: [],
        dataList: [],
        dataForm: {
          id: null,
          url: '',
          code: '',
          method: '',
          status: '',
          responseType: '',
          paramType: '',
          mark: '',
          version: null
        },
        paramType: '',
        paramList: [],
        dataRule: {
          url: [
            { required: true, message: '接口地址不能为空', trigger: 'blur' },
            { validator: validateUrl, trigger: 'blur' }
          ],
          code: [
            { required: true, message: '接口code不能为空', trigger: 'blur' },
            { validator: validateCode, trigger: 'blur' }
          ],
          method: [
            { required: true, message: '接口类型不能为空', trigger: 'blur' }
          ],
          status: [
            { required: true, message: '请选择接口状态', trigger: 'blur' }
          ],
          responseType: [
            { required: true, message: '接口返回类型不能为空', trigger: 'blur' }
          ]
        }
      }
    },
    components: {
      ParamAddOrUpdate
    },
    methods: {
      init (id) {
        this.dataForm.id = id || null
        this.$http({
          url: this.$http.adornUrl('/admin/dict/parent'),
          method: 'get',
          params: this.$http.adornParams({'code': 'apiType'})
        }).then(({data}) => {
          this.option1 = data.obj || []
          return this.$http({
            url: this.$http.adornUrl('/admin/dict/parent'),
            method: 'get',
            params: this.$http.adornParams({'code': 'responseType'})
          }).then(({data}) => {
            this.option2 = data.obj || []
            return this.$http({
              url: this.$http.adornUrl('/admin/dict/parent'),
              method: 'get',
              params: this.$http.adornParams({'code': 'paramType'})
            }).then(({data}) => {
              this.option3 = data.obj || []
            })
          })
        }).then(() => {
          this.visible = true
          this.$nextTick(() => {
            this.commonParamManageVisible = false
            this.objectParamManageVisible = false
            this.tableVisible = false
            this.$refs['dataForm']?.resetFields()
          })
        }).then(() => {
          if (this.dataForm.id) {
            this.tableVisible = true
            this.dataListLoading = true
            this.$http({
              url: this.$http.adornUrl('/admin/api'),
              method: 'get',
              params: this.$http.adornParams({ 'id': this.dataForm.id })
            }).then(({data}) => {
              if (data && data.code === 0) {
                this.dataForm.version = data.obj.version
                this.dataForm.url = data.obj.url
                this.dataForm.code = data.obj.code
                this.dataForm.method = data.obj.method
                this.dataForm.paramType = data.obj.paramType
                this.paramType = this.dataForm.paramType
                this.dataForm.responseType = data.obj.responseType
                this.dataForm.mark = data.obj.mark
                this.dataForm.status = data.obj.status
                this.dataList = data.obj.apiParamManageList
              }
              if (this.dataForm.paramType === 'paramTypeThree') {
                this.commonParamManageVisible = true
              } else {
                this.objectParamManageVisible = true
              }
              this.dataListLoading = false
            })
          }
        })
      },
      change (val) {
        this.tableVisible = true
        if (!this.dataForm.id || (this.dataForm.id && this.dataForm.paramType !== this.paramType)) {
          this.dataList = []
        } else {
          this.init(this.dataForm.id)
        }
        this.dataListLoading = true
        if (val === 'paramTypeThree') {
          this.commonParamManageVisible = true
          this.objectParamManageVisible = false
          this.dataListLoading = false
        } else {
          this.objectParamManageVisible = true
          this.commonParamManageVisible = false
          this.dataListLoading = false
        }
      },
      deleteHandle (val) {
        var index
        var id
        for (var i = 0; i < this.dataList.length; i++) {
          if (this.dataList[i].sortNum === val) {
            index = i
            id = this.dataList[i].id ? this.dataList[i].id : null
          }
        }
        this.dataList.splice(index, 1)
        if (id !== null) {
          this.$http({
            url: this.$http.adornUrl('/admin/apiParam/delete'),
            method: 'get',
            params: this.$http.adornParams({'id': id})
          }).then(({data}) => {
            if (data && data.code === 0) {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 1500
              })
            } else {
              this.$message.error(data.msg)
            }
          })
        }
      },
      // 新增 / 修改
      paramAddOrUpdate (val1, val2) {
        this.paramAddOrUpdateVisible = true
        this.$nextTick(() => {
          this.$refs.paramAddOrUpdate.init(val1, val2)
        })
      },
      getParamList (data) {
        var dataList = data
        var sortNum = []
        for (var i = 0; i < this.dataList.length; i++) {
          sortNum.push(this.dataList[i].sortNum)
        }
        if (sortNum.indexOf(data.sortNum) > -1) {
          var j = sortNum.indexOf(data.sortNum)
          this.dataList[j].type = data.type
          this.dataList[j].name = data.name
          this.dataList[j].need = data.need
        } else {
          this.dataList.push(dataList)
        }
      },
      // 表单提交
      dataFormSubmit () {
        this.$refs['dataForm'].validate((valid) => {
          if (valid) {
            this.$http({
              url: this.$http.adornUrl(`/admin/api/saveApiInfoAndParams`),
              method: 'post',
              data: this.$http.adornData({
                'id': this.dataForm.id || null,
                'version': this.dataForm.version,
                'code': this.dataForm.code,
                'url': this.dataForm.url,
                'method': this.dataForm.method,
                'paramType': this.dataForm.paramType,
                'responseType': this.dataForm.responseType,
                'mark': this.dataForm.mark,
                'status': this.dataForm.status,
                'apiParamManageList': this.dataList
              })
            }).then(({data}) => {
              if (data && data.code === 0) {
                this.$message({
                  message: '操作成功',
                  type: 'success',
                  duration: 1500,
                  onClose: () => {
                    this.visible = false
                    this.$emit('refreshDataList')
                  }
                })
              } else {
                this.$message.error(data.msg)
              }
            })
          }
        })
      }
    }
  }
</script>

<style>
  .text {
    font-size: 14px;
  }

  .item {
    margin-bottom: 18px;
  }

  .clearfix:before,
  .clearfix:after {
    display: table;
    content: "";
  }
  .clearfix:after {
    clear: both
  }

  .box-card {
    width: 100%;
  }
</style>
