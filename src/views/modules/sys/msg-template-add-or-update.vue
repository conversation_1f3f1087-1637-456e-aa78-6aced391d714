<template>
  <div>
    <el-dialog
        :title="!dataForm.id ? '新增' : '修改'"
        :close-on-click-modal="false"
        :visible.sync="visible" width="80%">
      <el-form :model="dataForm" :rules="dataRule" ref="dataForm" label-width="100px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="模块：" prop="module" :error="errors['module']">
              <el-dict :code="'MSG_TEP_MODULE'" v-model="dataForm.module"></el-dict>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="code：" prop="code" :error="errors['code']">
              <el-input v-model="dataForm.code" placeholder="请输入" clearable></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="标题：" prop="title" :error="errors['title']">
              <el-input v-model="dataForm.title" placeholder="请输入" clearable></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="状态：" prop="status" :error="errors['status']">
              <el-radio-group v-model="dataForm.status">
                <el-radio :label="true">启用</el-radio>
                <el-radio :label="false">禁用</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="推送方式：" prop="pushWay" :error="errors['pushWay']">
              <el-radio-group v-model="dataForm.pushWay" @change="() => {if (dataForm.pushWay === 'sms') dataForm.msgPrompt = null}">
                <el-radio label="sms">短信推送</el-radio>
                <el-radio label="wechat">微信推送</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="模版说明：" prop="description" :error="errors['description']">
              <el-input type="textarea" rows="1" v-model="dataForm.description" placeholder="请输入"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20" style="margin-bottom: 10px">
          <el-col :span="24">
            <span style="color: red; font-size: smaller; margin-left: 10px;">*传参占位符请输入"{}"</span>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="模版内容：" prop="template" :error="errors['template']">
              <el-input type="textarea" rows="3" v-model="dataForm.template" placeholder="请输入"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20" style="margin-bottom: 10px" v-if="dataForm.pushWay === 'wechat'">
          <el-col :span="24">
            <span style="color: red; font-size: smaller; margin-left: 10px;">*传参占位符请输入"{}"</span>
          </el-col>
        </el-row>
        <el-row :gutter="20" v-if="dataForm.pushWay === 'wechat'">
          <el-col :span="24">
            <el-form-item label="消息提示：" prop="msgPrompt" :error="errors['msgPrompt']">
              <el-input type="textarea" rows="1" v-model="dataForm.msgPrompt" placeholder="请输入"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20" style="margin-bottom: 10px">
          <el-col :span="24">
            <span style="color: red; font-size: smaller; margin-left: 10px;">*传参占位符请输入"{}"</span>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="跳转链接：" prop="jumpLink" :error="errors['jumpLink']">
              <el-input type="textarea" rows="1" v-model="dataForm.jumpLink" placeholder="请输入"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="visible = false">取消</el-button>
        <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import editMixin from '@/mixins/edit-mixins'
export default {
  mixins: [editMixin],
  data () {
    const validateCode = (rule, value, callback) => {
      this.$http({
        url: this.$http.adornUrl(`/admin/msg_template/validateCode`),
        method: 'get',
        params: this.$http.adornParams({
          code: this.dataForm.code,
          id: this.dataForm.id
        })
      }).then(({data}) => {
        if (data && data.code === 0) {
          if (!data.obj) {
            callback(new Error('该code数据库已存在，请更换！'))
          } else {
            callback()
          }
        } else {
          callback()
        }
      })
    }
    return {
      editOptions: {
        initUrl: '/admin/msg_template',
        saveSuffix: 'save',
        updateSuffix: 'update',
        submitUrl: '/admin/msg_template'
      },
      dataForm: {
        module: null,
        code: null,
        title: null,
        template: null,
        msgPrompt: null,
        description: null,
        pushWay: null,
        status: true,
        jumpLink: null
      },
      dataRule: {
        module: [
          { required: true, message: '模块不能为空', trigger: 'change' }
        ],
        code: [
          { required: true, message: '模版code不能为空', trigger: 'blur' },
          { validator: validateCode, trigger: 'blur' }
        ],
        title: [
          { required: true, message: '模版标题不能为空', trigger: 'blur' }
        ],
        template: [
          {required: true, message: '模版内容不能为空', trigger: 'blur'}
        ],
        msgPrompt: [
          {required: true, message: '消息提示不能为空', trigger: 'blur'}
        ],
        pushWay: [
          {required: true, message: '请选择推送方式', trigger: 'change' }
        ]
      }
    }
  },
  methods: {
    init (id) {
      this.dataForm.id = id || undefined
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm']?.resetFields()
        this.dataForm.msgPrompt = null
        this.dataForm.createDate = null
        this.dataForm.updateDate = null
        this.dataForm.creator = null
        this.dataForm.updater = null
        if (this.dataForm.id) {
          this.getData()
        }
      })
    }
  }
}
</script>
