<template>
  <div style="margin: 150px">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" label-width="100px" label-position="right">
      <el-row :gutter="80">
<!--        <el-col :span="12">-->
<!--           <el-form-item label="接口编码" prop="apiCode" :error="apiCodeError">-->
<!--                <el-input v-model="dataForm.apiCode" placeholder="接口编码" style="width: 60%"></el-input>-->
<!--            </el-form-item>-->
<!--        </el-col>-->
            <el-col :span="24">
           <el-form-item label="接口名称" prop="apiName" :error="apiNameError">
                <el-input v-model="dataForm.apiName" placeholder="接口名称" clearable style="width: 60%"></el-input>
            </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="80">
        <el-col :span="12">
          <el-form-item label="接口分类" prop="apiCategories" :error="apiCategoriesError">
            <el-cascader
              style="width: 60%"
              filterable
              v-model="dataForm.apiCategories"
              :options="categories"
              :props="{ checkStrictly: true }"
              clearable></el-cascader>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="80">
        <el-col :span="12">
          <el-form-item label="状态" prop="status" :error="statusError">
            <el-radio-group v-model="dataForm.status">
              <el-radio :label="true">启用</el-radio>
              <el-radio :label="false">禁用</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="12">
            <el-form-item label="是否保留" prop="persist" :error="persistError">
               <el-radio-group v-model="dataForm.persist">
                   <el-radio :label="true">是</el-radio>
                   <el-radio :label="false">否</el-radio>
               </el-radio-group>
            </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="80">
        <el-col :span="12">
          <el-form-item label="是否需要认证" prop="auth" :error="authError">
            <el-radio-group v-model="dataForm.auth">
              <el-radio :label="true">是</el-radio>
              <el-radio :label="false">否</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="是否公开" prop="open" :error="openError">
            <el-radio-group v-model="dataForm.open">
              <el-radio :label="true">是</el-radio>
              <el-radio :label="false">否</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="80">
        <el-col :span="24">
          <el-form-item label="请求路径" prop="apiPath" :error="apiPathError">
            <el-input type="textarea" v-model="dataForm.apiPath" clearable placeholder="请求路径"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="80">
        <el-col :span="24">
          <el-form-item label="接口描述" prop="apiDesc" :error="apiDescError">
            <el-input v-model="dataForm.apiDesc" type="textarea" clearable rows="3" placeholder="接口描述"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
        </el-form>
    <span slot="footer" class="dialog-footer" style="float: right">
      <el-button @click="returnPage()">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </div>
</template>

<script>
  import _ from 'lodash'
  export default {
    data () {
      return {
        categories: [],
        dataForm: {
          id: null,
          version: null,
          apiCode: '',
          apiName: '',
          apiCategories: [],
          apiDesc: '',
          apiPath: '',
          status: true,
          persist: false,
          auth: '',
          open: ''
        },
        type: null,
        dataRule: {
          // apiCode: [
          //   {required: true, message: '接口编码不能为空', trigger: 'blur'}
          // ],
          apiName: [
            {required: true, message: '接口名称不能为空', trigger: 'blur'}
          ],
          // apiCategories: [
          //   {required: true, message: '接口分类', trigger: 'change'}
          // ],
          apiPath: [
            {required: true, message: '请求路径不能为空', trigger: 'blur'}
          ],
          status: [
            {required: true, message: '状态不能为空', trigger: 'change'}
          ],
          persist: [
            {required: true, message: '是否保留不能为空', trigger: 'change'}
          ]
        },
        apiCodeError: null,
        apiNameError: null,
        apiCategoriesError: null,
        apiDescError: null,
        apiPathError: null,
        statusError: null,
        persistError: null,
        authError: null,
        openError: null
      }
    },
    methods: {
      init (id) {
        this.dataForm.id = id || null
        this.type = id ? 'update' : 'save'
        this.$http({
          url: this.$http.adornUrl('/admin/sys_api_category/cascader'),
          method: 'get',
          params: this.$http.adornParams()
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.categories = data.obj
          } else {
            this.categories = []
          }
        }).then(() => {
          this.visible = true
          this.$nextTick(() => {
            this.$refs['dataForm']?.resetFields()
          })
        }).then(() => {
          if (this.dataForm.id) {
            // 修改
            this.$http({
              url: this.$http.adornUrl(`/admin/sys_api`),
              method: 'get',
              params: this.$http.adornParams({ id: this.dataForm.id })
            }).then(({data}) => {
              this.dataForm = data.obj
            })
          }
        })
      },
      returnPage () {
        this.$emit('returnPrimaryPage', null, null)
      },
      // 表单提交
      dataFormSubmit () {
        this.$refs['dataForm'].validate((valid) => {
          if (valid) {
            this.$http({
              url: this.$http.adornUrl(`/admin/sys_api/${!this.dataForm.id ? 'save' : 'update'}`),
              method: 'post',
              data: this.$http.adornData(this.dataForm)
            }).then(({data}) => {
              if (data && data.code === 0) {
                this.$message({
                  message: '操作成功',
                  type: 'success',
                  duration: 1500,
                  onClose: () => {
                    this.visible = false
                    this.$emit('returnPrimaryPage', _.last(this.dataForm.apiCategories), this.type)
                  }
                })
              } else if (data && data.code === 303) {
                for (let it of data.obj) {
                  this[`${it.field}Error`] = it.message
                }
              } else {
                this.$message.error(data.msg)
              }
            })
          }
        })
      }
    }
  }
</script>
