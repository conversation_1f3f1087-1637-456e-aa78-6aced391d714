<template>
  <div>
    <el-container style="height: 60px">
        <el-aside width="200px" style="background-color: lightblue">
          <div>
            <span style="font-size:20px; float: left; padding: 15px 0px 0px 30px">接口类目</span>
            <el-button
              :disabled="categorySaveOrUpdate || apiSaveOrUpdate"
              title="类目整理"
              class="el-icon-edit-outline"
              circle
              style="background-color:lightblue; font-size: 30px; border: 0px; float: right; padding: 15px 15px 0px 0px"
              @click="editCategory()"/>
          </div>
        </el-aside>
        <el-main style="height:60px; text-align: left; font-size: 20px; padding:15px; background-color: #B3C0D1;">
          <el-row :gutter="20">
            <el-col :span="6" style="padding-top: 5px"><span style="font-size: 15px">{{categoryIndex}}</span></el-col>
            <el-col :span="6" v-show="!apiSaveOrUpdate && !categorySaveOrUpdate">
              <el-input clearable v-model="dataForm.keyword" placeholder="api名称/路径模糊查询"></el-input>
            </el-col>
            <el-col :span="4" v-show="!apiSaveOrUpdate && !categorySaveOrUpdate">
              <el-select clearable v-model="dataForm.status" placeholder="接口状态">
                <el-option
                  v-for="item in [{label: '启用', value: true}, {label: '禁用', value: false}]"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value">
                </el-option>
              </el-select>
            </el-col>
            <el-col :span="4" v-show="!apiSaveOrUpdate && !categorySaveOrUpdate">
              <el-select clearable v-model="dataForm.persist" placeholder="是否保留">
                <el-option
                  v-for="item in [{label: '是', value: true}, {label: '否', value: false}]"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value">
                </el-option>
              </el-select>
            </el-col>
            <el-col :span="4" v-show="!apiSaveOrUpdate && !categorySaveOrUpdate">
              <el-button
                class="el-icon-search"
                title="查询"
                circle
                style="background-color:#B3C0D1; font-size: 30px; border: 0px; padding: 5px"
                @click="currentChangeHandle(1)"/>
              <el-button
                class="el-icon-circle-plus-outline"
                title="接口新增"
                circle
                style="background-color:#B3C0D1; font-size: 30px; border: 0px; padding: 5px"
                @click="saveOrUpdateApi()"/>
            </el-col>
          </el-row>
        </el-main>
    </el-container>
    <el-container style="height: 750px">
      <el-aside width="200px" style="background-color: rgb(238, 241, 246)">
        <el-menu v-if="menuList.length > 0" style="overflow: hidden; border: solid 1px #e6e6e6" class="category_menu" active-text-color="black" :unique-opened="true">
          <sub-menu :menuData="menuList" :menuDisabled="categorySaveOrUpdate || apiSaveOrUpdate" ref="subMenu"></sub-menu>
        </el-menu>
      </el-aside>
      <el-main v-if="!categorySaveOrUpdate && !apiSaveOrUpdate">
          <el-table
            :data="dataList"
            border
            v-loading="dataListLoading"
            style="width: 100%">
<!--            <el-table-column-->
<!--              prop="apiCode"-->
<!--              header-align="center"-->
<!--              align="center"-->
<!--              label="接口编码">-->
<!--            </el-table-column>-->
            <el-table-column
              prop="apiName"
              header-align="center"
              align="center"
              label="接口名称">
            </el-table-column>
            <el-table-column
              prop="apiPath"
              header-align="center"
              align="center"
              label="请求路径">
            </el-table-column>
            <el-table-column
              prop="apiCategoriesText"
              header-align="center"
              align="center"
              label="接口类目">
            </el-table-column>
            <el-table-column
              prop="apiDesc"
              header-align="center"
              align="center"
              show-overflow-tooltip
              label="接口描述">
            </el-table-column>
            <el-table-column
              prop="status"
              header-align="center"
              align="center"
              label="状态">
              <template slot-scope="scope">
                <el-tag v-if="scope.row.status == false" size="small" type="danger">禁用</el-tag>
                <el-tag v-else size="small">启用</el-tag>
              </template>
            </el-table-column>
            <el-table-column
              prop="persist"
              header-align="center"
              align="center"
              label="保留数据">
              <template slot-scope="scope">
                <el-tag v-if="scope.row.persist == false" size="small" type="danger">否</el-tag>
                <el-tag v-else size="small">是</el-tag>
              </template>
            </el-table-column>
            <el-table-column
              fixed="right"
              header-align="center"
              align="center"
              width="150"
              label="操作">
              <template slot-scope="scope">
                <el-button title="编辑" class="el-icon-edit" circle style="font-size: 20px; border: 0px; padding: 5px" @click="saveOrUpdateApi(scope.row.id)"></el-button>
                <el-button title="删除" v-if="!scope.row.persist" class="el-icon-delete" circle style="font-size: 20px; border: 0px; padding: 5px" @click="deleteApi(scope.row.id)"></el-button>
              </template>
            </el-table-column>
          </el-table>
          <el-pagination
            @size-change="sizeChangeHandle"
            @current-change="currentChangeHandle"
            :current-page="pageIndex"
            :page-sizes="[10, 20, 50, 100]"
            :page-size="pageSize"
            :total="totalPage"
            layout="total, sizes, prev, pager, next, jumper">
          </el-pagination>
        </el-main>
      <el-main v-if="categorySaveOrUpdate">
        <api-category-list ref="apiCategoryList" @returnPrimaryPage="returnPrimaryPageFromCategoryEdit"></api-category-list>
      </el-main>
      <el-main v-if="apiSaveOrUpdate">
        <api-add-or-update ref="apiAddOrUpdate" @returnPrimaryPage="returnPrimaryPageFromApiEdit"></api-add-or-update>
      </el-main>
    </el-container>
  </div>
</template>

<script>
import _ from 'lodash'
import SubMenu from './api-category-sub-menu'
import ApiAddOrUpdate from './api-add-or-update'
import ApiCategoryList from './api-category-list'
export default {
  components: {
    SubMenu,
    ApiAddOrUpdate,
    ApiCategoryList
  },
  data () {
    return {
      categoryIndex: '全部',
      originIndex: '全部',
      originDataFormCategoryId: null,
      categoryChanged: false,
      categorySaveOrUpdate: false,
      apiSaveOrUpdate: false,
      dataForm: {
        keyword: null,
        status: null,
        persist: null,
        categoryId: null
      },
      dataList: [],
      pageIndex: 1,
      pageSize: 10,
      totalPage: 0,
      dataListLoading: false,
      menuList: [],
      allCategoryList: []
    }
  },
  created () {
    this.getCategoryList()
    this.getAllCategory()
  },
  activated () {
    this.getDataList()
  },
  mounted () {
    // 监听menu点击触发接口列表查询更新
    window.addEventListener('setItem', () => {
      var allCategoryList = this.allCategoryList
      var data = JSON.parse(sessionStorage.getItem('currentCategory'))
      if (data.id === '1') {
        this.categoryIndex = '全部'
        this.dataForm.categoryId = data.id
        this.currentChangeHandle(1)
      } else {
        var currentCategoryList = []
        while (data.level >= 1) {
          currentCategoryList.push(data)
          if (data.level !== 1) {
            data = _.find(allCategoryList, ['id', data.parentId])
          } else {
            break
          }
        }
        currentCategoryList = _.reverse(currentCategoryList)
        this.categoryIndex = '全部 > ' + _.join(_.map(currentCategoryList, 'name'), ' > ')
        this.dataForm.categoryId = _.last(currentCategoryList).id
        this.currentChangeHandle(1)
      }
    })
  },
  methods: {
    // 获取接口列表
    getDataList () {
      this.$http({
        url: this.$http.adornUrl('/admin/sys_api/pages'),
        method: 'post',
        data: this.$http.adornData({
          currentPage: this.pageIndex,
          pageSize: this.pageSize,
          keyword: this.dataForm.keyword,
          status: this.dataForm.status,
          persist: this.dataForm.persist,
          categoryId: this.dataForm.categoryId
        })
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.dataList = data.obj.records
          this.totalPage = data.obj.total
        } else {
          this.dataList = []
          this.totalPage = 0
        }
        this.dataListLoading = false
      })
    },
    // 获取包含"全部"的层级类目树
    getCategoryList () {
      this.$http({
        url: this.$http.adornUrl('/admin/sys_api_category/treeOfAll'),
        method: 'get',
        params: this.$http.adornParams()
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.menuList = [data.obj]
        } else {
          this.$message.error(data.msg)
        }
      })
    },
    // 获取所有启用状态下的类目（不含层级的list)
    getAllCategory () {
      this.$http({
        url: this.$http.adornUrl('/admin/sys_api_category/allOfEnable'),
        method: 'get',
        params: this.$http.adornParams()
      }).then(({data}) => {
        this.allCategoryList = data
      })
    },
    // 每页数
    sizeChangeHandle (val) {
      this.pageSize = val
      this.pageIndex = 1
      this.getDataList()
    },
    // 当前页
    currentChangeHandle (val) {
      this.pageIndex = val
      this.getDataList()
    },
    // 新增或修改接口
    saveOrUpdateApi (id) {
      this.apiSaveOrUpdate = true
      this.categorySaveOrUpdate = false
      this.originIndex = this.categoryIndex
      this.categoryIndex = id ? '接口更新' : '接口新增'
      this.$nextTick(() => {
        this.$refs.apiAddOrUpdate.init(id)
      })
    },
    // 接口类目列表编辑
    editCategory () {
      this.categorySaveOrUpdate = true
      this.categoryChanged = false
      this.apiSaveOrUpdate = false
      this.originIndex = this.categoryIndex
      this.originDataFormCategoryId = this.dataForm.categoryId
      this.categoryIndex = '接口类目编辑列表'
      this.$nextTick(() => {
        this.$refs.apiCategoryList.init()
      })
    },
    // 删除
    deleteApi (id) {
      this.$confirm(`确定删除该接口?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        closeOnClickModal: false
      }).then(() => {
        this.$http({
          url: this.$http.adornUrl(`/admin/sys_api/removeByIds`),
          method: 'get',
          params: this.$http.adornParams({'ids': id})
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.$message({
              message: '操作成功',
              type: 'success',
              duration: 1500,
              onClose: () => {
                this.currentChangeHandle(1)
              }
            })
          } else {
            this.$message.error(data.msg)
          }
        })
      }).catch(() => {})
    },
    // 从类目编辑页返回接口管理主页（isReturn判断是真的返回页面还是仅更新menu树, needUpdate判断是否需要更新索引和接口列表参数）
    returnPrimaryPageFromCategoryEdit (isReturn, needUpdate) {
      this.categorySaveOrUpdate = !isReturn
      if (needUpdate) {
        this.getCategoryList()
        this.$http({
          url: this.$http.adornUrl('/admin/sys_api_category/allOfEnable'),
          method: 'get',
          params: this.$http.adornParams()
        }).then(({data}) => {
          this.allCategoryList = data
          if (_.findIndex(this.allCategoryList, ['id', this.originDataFormCategoryId]) < 0) {
            this.categoryChanged = true
            this.originDataFormCategoryId = '1'
            this.originIndex = '全部'
            sessionStorage.setItem('currentCategory', JSON.stringify({id: '1', name: '全部', level: 0, parentId: null}))
          }
        })
      }
      if (isReturn) {
        this.categoryIndex = this.originIndex
        this.dataForm.categoryId = this.originDataFormCategoryId
        if (this.categoryChanged) {
          this.currentChangeHandle(1)
        } else {
          this.getDataList()
        }
      }
    },
    // 从接口新增/编辑返回接口管理主页（categoryId为新增或修改接口的最终类目id, type为类型新增还是修改）
    returnPrimaryPageFromApiEdit (categoryId, type) {
      this.apiSaveOrUpdate = false
      this.categoryIndex = this.originIndex
      if (type === 'update') {
        if (categoryId !== this.dataForm.categoryId) {
          this.currentChangeHandle(1)
        } else {
          this.getDataList()
        }
      } else if (type === 'save') {
        if (this.dataForm.categoryId === categoryId) {
          this.currentChangeHandle(1)
        }
      }
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
