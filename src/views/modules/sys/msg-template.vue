<template>
  <div class="mod-role">
    <el-form :inline="true" ref="dataForm" :model="dataForm" @keyup.enter.native="getDataList()">
      <el-form-item label="关键字：" prop="keyword">
        <el-input v-model="dataForm.keyword" placeholder="模版code/标题模糊搜索" clearable></el-input>
      </el-form-item>
      <el-form-item label="模块：" prop="module">
        <el-dict :code="'MSG_TEP_MODULE'" v-model="dataForm.module"></el-dict>
      </el-form-item>
      <el-form-item label="状态：" prop="status">
        <el-select v-model="dataForm.status" placeholder="请选择" clearable>
          <el-option
              v-for="item in [{label: '启用', value: true}, {label: '禁用', value: false}]"
              :key="item.value"
              :label="item.label"
              :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="推送方式：" prop="pushWay">
        <el-radio-group v-model="dataForm.pushWay">
          <el-radio :label="null">全部</el-radio>
          <el-radio :label="'sms'">短信推送</el-radio>
          <el-radio :label="'wechat'">微信推送</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item>
        <el-button type="warning" icon="el-icon-search" @click="getDataList()">查询</el-button>
        <el-button icon="el-icon-refresh-left" @click="resetForm()">重置</el-button>
      </el-form-item>
      <div class="f-s-c" style="padding: 15px 0;display: flex; justify-content: flex-end">
        <div>
          <el-button icon="el-icon-plus" type="primary" @click="addOrUpdateHandle()">新增</el-button>
        </div>
      </div>
    </el-form>
    <el-table
        :data="dataList"
        border
        v-loading="dataListLoading"
        style="width: 100%;">
      <el-table-column
          prop="module"
          header-align="center"
          align="center"
          width="160"
          label="所属模块">
      </el-table-column>
      <el-table-column
          prop="code"
          header-align="center"
          align="center"
          width="220"
          label="模版code">
      </el-table-column>
      <el-table-column
          prop="title"
          header-align="center"
          align="center"
          :show-overflow-tooltip="true"
          label="模版标题">
      </el-table-column>
      <el-table-column
          prop="pushWay"
          header-align="center"
          align="center"
          width="120"
          label="推送方式">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.pushWay === 'sms'" size="small" type="success">短信推送</el-tag>
          <el-tag v-if="scope.row.pushWay === 'wechat'" size="small" type="primary">微信推送</el-tag>
        </template>
      </el-table-column>
      <el-table-column
          label="参数数量"
          header-align="center"
          align="center">
        <el-table-column
            prop="templateParamsNum"
            label="模版内容"
            header-align="center"
            align="center"
            width="80">
        </el-table-column>
        <el-table-column
            prop="promptParamsNum"
            label="消息提示"
            header-align="center"
            align="center"
            width="80">
        </el-table-column>
        <el-table-column
            prop="linkParamsNum"
            label="跳转链接"
            header-align="center"
            align="center"
            width="80">
        </el-table-column>
      </el-table-column>
      <el-table-column
          prop="status"
          header-align="center"
          align="center"
          width="80"
          label="状态">
        <template slot-scope="scope">
          <el-switch
              size="mini"
              active-color="#13ce66"
              inactive-color="#ff4949"
              v-model="scope.row.status"
              @change="statusHandle(scope.row.id, scope.row)">
          </el-switch>
        </template>
      </el-table-column>
      <el-table-column
          prop="jumpLink"
          header-align="center"
          align="center"
          :show-overflow-tooltip="true"
          label="跳转链接">
      </el-table-column>
      <el-table-column
          fixed="right"
          header-align="center"
          align="center"
          width="100"
          label="操作">
        <template slot-scope="scope">
          <el-button type="text" size="small" @click="addOrUpdateHandle(scope.row.id)" class="btn-control">修改</el-button>
          <el-button type="text" size="small" @click="deleteHandle(scope.row.id)" class="btn-control">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
        @size-change="sizeChangeHandle"
        @current-change="currentChangeHandle"
        :current-page="pageIndex"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pageSize"
        :total="totalPage"
        layout="total, sizes, prev, pager, next, jumper">
    </el-pagination>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="query"></add-or-update>
  </div>
</template>

<script>
import AddOrUpdate from './msg-template-add-or-update'
import listMixin from '@/mixins/list-mixins'
export default {
  mixins: [listMixin],
  data () {
    return {
      mixinOptions: {
        dataUrl: '/admin/msg_template/pages',
        deleteUrl: '/admin/msg_template/removeByIds'
      },
      dataForm: {
        keyword: '',
        module: '',
        status: '',
        pushWay: null
      }
    }
  },
  components: {
    AddOrUpdate
  },
  activated () {
    this.getDataList()
  },
  methods: {
    resetForm () {
      this.$refs['dataForm']?.resetFields()
      this.dataForm.pushWay = []
      this.dataForm.smsPush = null
      this.dataForm.wechatPush = null
      this.getDataList()
    },
    statusHandle(id, row) {
      this.$http({
        url: this.$http.adornUrl('/admin/msg_template/ground'),
        method: 'get',
        params: this.$http.adornParams({
          'id': id
        })
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.$message.success("操作成功")
          this.query()
        } else {
          row.status = !row.status
          this.$message.error(data.msg)
        }
      }).catch(() => {
        row.status = !row.status
      })
    }
  }
}
</script>
