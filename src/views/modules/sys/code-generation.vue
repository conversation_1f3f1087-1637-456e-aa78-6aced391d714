<template>
  <div class="mod-config">
    <el-form :inline="true" :model="dataForm" :rules="dataRule" @keyup.enter.native="getDataList()">
      <el-form-item label="数据库类型"  prop="databaseType">
        <el-select v-model="dataForm.databaseType" placeholder="请选择" @change="selectDatabaseType($event)" clearable>
          <el-option
            v-for="item in options"
            :key="item"
            :label="item"
            :value="item">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="数据库名称"  prop="databaseName">
        <el-select v-model="dataForm.databaseName" placeholder="请选择" @change="selectDatabaseName($event)" clearable>
          <el-option
            v-for="item in options1"
            :key="item.databaseName"
            :label="item.databaseName"
            :value="item.databaseName">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button @click="getDataList()">查询</el-button>
        <el-button type="danger" @click="generatorHandle()" :disabled="dataListSelections.length <= 0">批量生成代码</el-button>
      </el-form-item>
    </el-form>
    <el-table
      :data="dataList"
      border
      v-loading="dataListLoading"
      @selection-change="selectionChangeHandle"
      style="width: 100%;">
      <el-table-column
        type="selection"
        header-align="center"
        align="center"
        width="50">
      </el-table-column>
      <el-table-column
        prop="tableName"
        header-align="center"
        align="center"
        label="表名">
      </el-table-column>
      <el-table-column
        prop="engine"
        header-align="center"
        align="center"
        label="引擎"
        show-overflow-tooltip>
      </el-table-column>
      <el-table-column
        prop="tableComment"
        header-align="center"
        align="center"
        label="表备注"
        show-overflow-tooltip>
      </el-table-column>
      <el-table-column
        prop="createTime"
        header-align="center"
        align="center"
        label="创建时间"
        show-overflow-tooltip>
      </el-table-column>
      <el-table-column
        fixed="right"
        header-align="center"
        align="center"
        width="150"
        label="操作">
        <template slot-scope="scope">
          <el-button type="text" size="small" @click="generatorHandle(scope.row.tableName)">生成代码</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      @size-change="sizeChangeHandle"
      @current-change="currentChangeHandle"
      :current-page="pageIndex"
      :page-sizes="[10, 20, 50, 100]"
      :page-size="pageSize"
      :total="totalPage"
      layout="total, sizes, prev, pager, next, jumper">
    </el-pagination>
  </div>
</template>

<script>
  export default {
    data () {
      return {
        dataForm: {
          databaseType: '',
          databaseName: '',
          tableName: ''

        },
        dataList: [],
        pageIndex: 1,
        pageSize: 10,
        totalPage: 0,
        dataListLoading: false,
        dataListSelections: [],
        databaseType: '',
        databaseName: '',
        addOrUpdateVisible: false,
        options: ['MySQL', 'Oracle', 'SQLServer', 'PostgreSQL'],
        options1: []
      }
    },
    // activated () {
    //   this.getDataList()
    // },
    methods: {
      // 获取数据列表
      getDataList () {
        this.databaseType = ''
        this.databaseName = ''
        this.dataListLoading = true
        this.$http({
          url: this.$http.adornUrl('/admin/generator/page'),
          method: 'post',
          data: this.$http.adornData({
            'currentPage': this.pageIndex,
            'pageSize': this.pageSize,
            'databaseType': this.dataForm.databaseType,
            'tableName': this.dataForm.tableName,
            'databaseName': this.dataForm.databaseName
          })
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.databaseType = this.dataForm.databaseType
            this.databaseName = this.dataForm.databaseName
            this.dataList = data.obj.records
            this.totalPage = data.obj.total
          } else {
            this.dataList = []
            this.totalPage = 0
            this.$message({
              message: data.msg,
              type: 'warning'
            })
          }
          this.dataListLoading = false
        }).catch(() => {})
      },
      // 每页数
      sizeChangeHandle (val) {
        this.pageSize = val
        this.pageIndex = 1
        this.getDataList()
      },
      // 当前页
      currentChangeHandle (val) {
        this.pageIndex = val
        this.getDataList()
      },
      // 多选
      selectionChangeHandle (val) {
        this.dataListSelections = val
      },
      selectDatabaseType (val) {
        this.dataForm.databaseType = ''
        this.options1 = []
        this.dataForm.databaseType = val
        this.$http({
          url: this.$http.adornUrl('/admin/sysDatabase/getDatabaseList'),
          method: 'post',
          params: this.$http.adornParams({
            'databaseType': val
          })
        }).then(({data}) => {
          if (data && data.code === 0) {
            let list = data.obj || []
            for (let i in list) {
              this.options1.push({
                databaseName: list[i].databaseName
              })
            }
          }
        }).catch(() => {})
      },
      selectDatabaseName (val) {
        this.dataForm.databaseName = ''
        this.dataForm.databaseName = val
      },
      generatorHandle (tableName) {
        var tables = tableName ? [tableName] : this.dataListSelections.map(item => {
          return item.tableName
        })
        this.$http({
          url: this.$http.adornUrl('/admin/generator/code'),
          method: 'post',
          params: this.$http.adornParams({
            'tableNames': tables.join(','),
            'databaseType': this.databaseType,
            'databaseName': this.databaseName
          }),
          responseType: 'blob'
        }).then(({data}) => {
          let blob = new Blob([data], {type: 'application/zip'})
          let url = window.URL.createObjectURL(blob)
          window.location.href = url
        }).catch(() => {})
      }
    }
  }
</script>
