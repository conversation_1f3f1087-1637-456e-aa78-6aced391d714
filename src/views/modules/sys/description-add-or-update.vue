<template>
  <el-dialog
      :title="!dataForm.id ? '新增' : '修改'"
      :close-on-click-modal="false"
      :visible.sync="visible" width="80%">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmit()"
             label-width="150px">
      <el-row :gutter="20">
        <el-form-item label="名称" prop="name" :error="nameError">
          <el-input v-model="dataForm.name" placeholder="名称"></el-input>
        </el-form-item>
      </el-row>
      <el-row :gutter="20">
        <el-form-item label="CODE" prop="code" :error="codeError">
          <el-input v-model="dataForm.code" placeholder="CODE"></el-input>
        </el-form-item>
      </el-row>
      <el-row :gutter="20">
        <el-form-item label="类型" prop="type" :error="typeError">
          <el-radio-group v-model="dataForm.type">
            <el-radio label="1">普通文本</el-radio>
            <el-radio label="2">富文本</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-row>
      <el-row :gutter="20">
        <el-form-item label="内容" prop="content" :error="contentError" v-if="dataForm.type === '1'">
          <el-input v-model="dataForm.normalContent" placeholder="内容" type="textarea"></el-input>
        </el-form-item>
      </el-row>
      <el-row :gutter="20">
        <el-form-item label="内容" prop="content" :error="contentError" v-if="dataForm.type === '2'">
          <teditor
              style="width: 100%;"
              :value="dataForm.richContent"
              :disabled="false"
              ref="teditor"
              @changeEditorValue="changeAnswer"
          ></teditor>
        </el-form-item>
      </el-row>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import Teditor from '@/components/tinymce'

export default {
  data() {
    return {
      visible: false,
      initForm: {
        id: null,
        version: null,
        name: '',
        code: '',
        content: '',
        normalContent: '',
        richContent: '',
        type: '1'
      },
      dataForm: {},
      dataRule: {
        name: [
          {required: true, message: '名称不能为空', trigger: 'blur'}
        ],
        code: [
          {required: true, message: 'code不能为空', trigger: 'blur'}
        ],
        content: [
          {required: true, message: '内容不能为空', trigger: 'blur'}
        ],
        type: [
          {required: true, message: '类型不能为空', trigger: 'blur'}
        ]
      },
      nameError: null,
      codeError: null,
      contentError: null,
      typeError: null
    }
  },
  components: {
    Teditor,
  },
  methods: {
    init(id) {
      this.dataForm = _.cloneDeep(this.initForm)
      this.dataForm.id = id || null
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm']?.resetFields()
        if (this.dataForm.id) {
          this.$http({
            url: this.$http.adornUrl(`/admin/sys/description`),
            method: 'get',
            params: this.$http.adornParams({id: this.dataForm.id})
          }).then(({data}) => {
            if (data && data.code === 0) {
              data.obj.normalContent = data.obj.type === '1' ? data.obj.content : ''
              data.obj.richContent = data.obj.type === '2' ? data.obj.content : ''
              this.dataForm = data.obj
            }
          })
        }
      })
    },
    // 表单提交
    dataFormSubmit() {
      this.dataForm.content = this.dataForm.type === '1' ? this.dataForm.normalContent : this.dataForm.richContent
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.$http({
            url: this.$http.adornUrl(`/admin/sys/description/saveOrUpdateDescription`),
            method: 'post',
            data: this.$http.adornData(this.dataForm)
          }).then(({data}) => {
            if (data && data.code === 0) {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 1500,
                onClose: () => {
                  this.visible = false
                  this.$emit('refreshDataList')
                }
              })
            } else if (data && data.code === 303) {
              for (let it of data.obj) {
                this[`${it.field}Error`] = it.message
              }
            } else {
              this.$message.error(data.msg)
            }
          })
        }
      })
    },
    // 富文本赋值
    changeAnswer(html) {
      console.log(html)
      this.dataForm.richContent = html
    }
  }
}
</script>
