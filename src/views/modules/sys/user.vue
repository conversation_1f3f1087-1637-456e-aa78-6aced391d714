<template>
  <div class="mod-user">
    <el-form :inline="true" :model="dataForm" @keyup.enter.native="getDataList()">
      <el-form-item label="关键字">
        <el-input v-model="dataForm.keyword" placeholder="用户名、手机号、邮箱" clearable></el-input>
      </el-form-item>
<!--      <el-form-item label="展示用户">-->
<!--        <el-select v-model="dataForm.managerAccount" placeholder="请选择" clearable>-->
<!--          <el-option :value="true" label="管理权限账户"></el-option>-->
<!--          <el-option :value="false" label="非管理权限账户"></el-option>-->
<!--        </el-select>-->
<!--      </el-form-item>-->
<!--      <el-form-item v-if="dataForm.managerAccount === true" label="管理员类型">-->
<!--        <el-select v-model="dataForm.adminType" placeholder="请选择" clearable>-->
<!--          <el-option value="SYS_ADMIN" label="系统管理员"></el-option>-->
<!--          <el-option value="ASSOCIATION_ADMIN" label="协会管理员"></el-option>-->
<!--          <el-option value="SUB_ASSOCIATION_ADMIN" label="分协会管理员"></el-option>-->
<!--          <el-option value="COMMUNITY_ADMIN" label="社区管理员"></el-option>-->
<!--          <el-option value="TEAM_ADMIN" label="团队管理员"></el-option>-->
<!--        </el-select>-->
<!--      </el-form-item>-->
      <el-form-item label="用户角色">
        <el-cascader style="width: 280px" v-model="dataForm.roleCodes" :options="roleTree" :props="{ checkStrictly: true,value: 'codes', label: 'name' }" clearable></el-cascader>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="currentChangeHandle(1)">查询</el-button>
        <el-button v-if="isAuth('sys:user:save')" type="primary" @click="addOrUpdateHandle()">新增</el-button>
        <el-button v-if="isAuth('sys:user:delete')" type="danger" @click="deleteHandle()" :disabled="dataListSelections.length <= 0">批量删除</el-button>
        <el-button v-if="isAuth('sys:user:syncZSQ')" type="warning" @click="zsqUserSync()">同步知社区用户</el-button>
      </el-form-item>
    </el-form>
    <div class="f-s-c" style="padding: 15px 0;display: flex;justify-content:flex-end;align-items: center ">
      <div>
        <el-button v-if="isAuth('sys:user:import')" icon="el-icon-upload2" type="success" @click="caImport()">社区管理员导入</el-button>
        <el-button v-if="isAuth('sys:user:import')" icon="el-icon-upload2" type="success" @click="taImport()">团队管理员导入</el-button>
      </div>
    </div>
    <el-table
        :data="dataList"
        border
        v-loading="dataListLoading"
        @selection-change="selectionChangeHandle"
        style="width: 100%;">
      <el-table-column
          type="selection"
          header-align="center"
          align="center"
          width="50">
      </el-table-column>
      <el-table-column
          prop="name"
          header-align="center"
          align="center"
          label="用户姓名">
      </el-table-column>
      <el-table-column
          prop="username"
          header-align="center"
          align="center"
          label="用户名">
      </el-table-column>
<!--      <el-table-column-->
<!--          prop="email"-->
<!--          header-align="center"-->
<!--          align="center"-->
<!--          label="邮箱">-->
<!--      </el-table-column>-->
      <el-table-column
          prop="mobile"
          header-align="center"
          align="center"
          label="手机">
      </el-table-column>
      <el-table-column
          prop="roleNames"
          header-align="center"
          align="center"
          label="用户角色">
      </el-table-column>
      <el-table-column
          prop="status"
          header-align="center"
          align="center"
          label="状态">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.status === false" size="small" type="danger">禁用</el-tag>
          <el-tag v-else size="small">正常</el-tag>
        </template>
      </el-table-column>
      <el-table-column
          prop="createDate"
          header-align="center"
          align="center"
          width="180"
          label="创建时间">
      </el-table-column>
      <el-table-column
          fixed="right"
          header-align="center"
          align="center"
          width="200"
          label="操作">
        <template slot-scope="scope">
          <el-button v-if="isAuth('sys:user:update')" type="text" size="small" @click="addOrUpdateHandle(scope.row.id)">
            修改
          </el-button>
          <el-button v-if="isAuth('sys:user:delete')" type="text" size="small" @click="deleteHandle(scope.row.id)">删除
          </el-button>
          <el-button type="text" size="small" @click="updatePasswordHandle(scope.row.username)">修改密码
          </el-button>
          <el-button type="text" size="small" @click="resetPasswordHandle(scope.row.id)">重置密码
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
        @size-change="sizeChangeHandle"
        @current-change="currentChangeHandle"
        :current-page="pageIndex"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pageSize"
        :total="totalPage"
        layout="total, sizes, prev, pager, next, jumper">
    </el-pagination>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"></add-or-update>
    <!-- 弹窗, 修改密码 -->
    <update-password v-if="updatePasswordVisible" ref="updatePassword"></update-password>
    <admin-account-import v-if="adminAccountImportVisible" ref="adminAccountImport"></admin-account-import>
    <zsq-sync v-if="zsqSyncVisible" ref="zsqSync" @refreshDataList="getDataList"></zsq-sync>
  </div>
</template>

<script>
import AddOrUpdate from './user-add-or-update'
import UpdatePassword from '../../main-navbar-update-password'
import AdminAccountImport from "./admin-account-import";
import ZsqSync from "./user-zsq-sync";

export default {
  data() {
    return {
      dataForm: {
        keyword: null,
        managerAccount: null,
        adminType: null,
        roleCodes: null
      },
      roleTree: [],
      dataList: [],
      pageIndex: 1,
      pageSize: 10,
      totalPage: 0,
      dataListLoading: false,
      dataListSelections: [],
      addOrUpdateVisible: false,
      updatePasswordVisible: false,
      adminAccountImportVisible: false,
      zsqSyncVisible: false
    }
  },
  components: {
    AddOrUpdate,
    UpdatePassword,
    AdminAccountImport,
    ZsqSync
  },
  activated() {
    this.getDataList()
    this.getRoleTree()
  },
  methods: {
    // 获取数据列表
    getDataList() {
      this.dataListLoading = true
      this.$http({
        url: this.$http.adornUrl('/admin/user/pages'),
        method: 'post',
        data: this.$http.adornData({
          'currentPage': this.pageIndex,
          'pageSize': this.pageSize,
          'keyword': this.dataForm.keyword,
          'managerAccount': this.dataForm.managerAccount,
          'adminType': this.dataForm.adminType,
          'roleCodes': this.dataForm.roleCodes && this.dataForm.roleCodes.length > 0 ? this.dataForm.roleCodes[this.dataForm.roleCodes.length - 1] : null
        })
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.dataList = data.obj.records
          this.totalPage = data.obj.total
        } else {
          this.dataList = []
          this.totalPage = 0
        }
        this.dataListLoading = false
      })
    },
    getRoleTree() {
      this.$http({
        url: this.$http.adornUrl('/admin/role/roleTree'),
        method: 'get',
        params: this.$http.adornParams()
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.roleTree = data.obj
        } else {
          this.$message.error(data.msg)
          this.roleTree = []
        }
      })
    },
    // 每页数
    sizeChangeHandle(val) {
      this.pageSize = val
      this.pageIndex = 1
      this.getDataList()
    },
    // 当前页
    currentChangeHandle(val) {
      this.pageIndex = val
      this.getDataList()
    },
    // 多选
    selectionChangeHandle(val) {
      this.dataListSelections = val
    },
    // 新增 / 修改
    addOrUpdateHandle(id) {
      this.addOrUpdateVisible = true
      this.$nextTick(() => {
        this.$refs.addOrUpdate.init(id)
      })
    },
    // 修改密码
    updatePasswordHandle(userName) {
      this.updatePasswordVisible = true
      this.$nextTick(() => {
        this.$refs.updatePassword.init(userName)
      })
    },
    // 删除
    deleteHandle(id) {
      var ids = id ? [id] : this.dataListSelections.map(item => {
        return item.id
      })
      this.$confirm(`确定进行${id ? '删除' : '批量删除'}操作?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        closeOnClickModal: false
      }).then(() => {
        this.$http({
          url: this.$http.adornUrl('/admin/user/removeByIds'),
          method: 'get',
          params: this.$http.adornParams({'ids': ids.join(',')}, false)
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.$message({
              message: '操作成功',
              type: 'success',
              duration: 1500,
              onClose: () => {
                this.getDataList()
              }
            })
          } else {
            this.$message.error(data.msg)
          }
        })
      }).catch(() => {
      })
    },
    resetPasswordHandle(id) {
      this.$confirm(`确定重置密码?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        closeOnClickModal: false
      }).then(() => {
        this.categoryEditLoading = true
        this.$http({
          url: this.$http.adornUrl('/admin/user/resetPassWordOfSelect'),
          method: 'post',
          params: this.$http.adornParams({
            'userId': id
          })
        }).then(({data}) => {
          if (data && data.code === 0) {
            let newPwd = data.obj
            let msg = '重置密码成功，新密码为:' + newPwd
            this.$alert(msg, '重置密码', {
              confirmButtonText: '确定',
              callback: action => {
                this.getDataList()
              }
            })
          } else {
            this.$message.error(data.msg)
          }
        })
      }).catch(() => {
      })
    },
    caImport() {
      this.adminAccountImportVisible = true
      this.$nextTick(() => {
        this.$refs.adminAccountImport.init('COMMUNITY_ADMIN')
      })
    },
    taImport() {
      this.adminAccountImportVisible = true
      this.$nextTick(() => {
        this.$refs.adminAccountImport.init('TEAM_ADMIN')
      })
    },
    zsqUserSync() {
      this.zsqSyncVisible = true
      this.$nextTick(() => {
        this.$refs.zsqSync.init()
      })
    }
  }
}
</script>
