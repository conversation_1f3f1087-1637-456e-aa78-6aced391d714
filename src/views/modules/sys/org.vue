<template>
  <div class="mod-menu" v-loading="dataListLoading">
    <el-row :gutter="20">
      <el-col :span="12">
        <el-card class="box-card" style="height: 850px">
          <el-form :inline="true" :model="dataForm">
            <el-form-item>
              <el-button icon="el-icon-plus" type="primary" @click="orgAddOrUpdateHandle()">创建部门</el-button>
            </el-form-item>
          </el-form>
          <el-table
              :data="dataList"
              border
              height="750"
              style="width: 100%"
              highlight-current-row
              @current-change="handleCurrentChange">
            <table-tree-column
                width="260"
                prop="name"
                header-align="center"
                fixed="left"
                treeKey="id"
                label="部门名称">
            </table-tree-column>
            <el-table-column
                prop="code"
                header-align="center"
                align="center"
                label="部门编码">
            </el-table-column>
            <el-table-column
                header-align="center"
                width="100"
                align="center"
                label="层级">
              <template v-slot="scope">
                <el-tag v-if="scope.row.level === 1" size="small" type="success">协会</el-tag>
                <el-tag v-else-if="scope.row.level === 2" size="small" type="primary">分协会</el-tag>
                <el-tag v-else-if="scope.row.level === 3" size="small" type="warning">社区</el-tag>
                <el-tag v-else-if="scope.row.level === 4" size="small" type="info">小区</el-tag>
                <el-tag v-else size="small" type="danger">未知</el-tag>
              </template>
            </el-table-column>
            <el-table-column
                prop="sequence"
                header-align="center"
                align="center"
                width="80"
                label="排序">
            </el-table-column>
<!--            <el-table-column-->
<!--                prop="isSyncText"-->
<!--                header-align="center"-->
<!--                align="center"-->
<!--                width="100px"-->
<!--                label="同步状态">-->
<!--              <template slot-scope="scope">-->
<!--                <el-popover trigger="hover" placement="top">-->
<!--                  <p style="text-align: center">-->
<!--                    {{-->
<!--                      '同步时间：' + (scope.row.syncTime ? scope.row.syncTime : '')-->
<!--                    }}<br>{{ scope.row.syncRemark }}</p>-->
<!--                  <div slot="reference" class="name-wrapper">-->
<!--                    <el-tag v-if="scope.row.isSync === 'sync_failure'" type="danger">{{ scope.row.syncText }}</el-tag>-->
<!--                    <el-tag v-if="scope.row.isSync === 'sync_wait'" type="warning">{{ scope.row.syncText }}</el-tag>-->
<!--                    <el-tag v-if="scope.row.isSync === 'sync_success'" type="success">{{-->
<!--                        scope.row.syncText-->
<!--                      }}-->
<!--                    </el-tag>-->
<!--                  </div>-->
<!--                </el-popover>-->
<!--              </template>-->
<!--            </el-table-column>-->
            <el-table-column
                header-align="center"
                width="150"
                align="center"
                label="操作">
              <template v-slot="scope">
                <el-button type="text" size="small"
                           @click.stop="orgAddOrUpdateHandle(null,scope.row.id)">
                  新增子部门
                </el-button>
                <el-button type="text" size="small"
                           @click.stop="deleteHandle(scope.row.id)">删除
                </el-button>
                <el-button type="text" size="small" :disabled="scope.row.isSync === 'sync_success'"
                           @click="syncHandle(scope.row.id)">同步
                </el-button>
                  <el-button type="text" size="small"   @click="zsqDockingRecords(scope.row.id)">知社区对接记录</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card class="box-card" style="height: 850px; overflow: auto">
          <!-- 弹窗, 新增 / 修改 -->
          <org-add-or-update v-if="orgAddOrUpdateVisible" ref="OrgAddOrUpdate" @refreshDataList="saveOrUpdateCallback"/>
          <el-empty v-else description="请选择一个部门"></el-empty>
        </el-card>
      </el-col>
    </el-row>
      <zsq-docking-records v-if="zsqDockingRecordsVisible" ref="zsqDockingRecords"></zsq-docking-records>
  </div>
</template>

<script>
import TableTreeColumn from '@/components/table-tree-column'
import OrgAddOrUpdate from './org-add-or-update'
import ActionList from './action'
import {treeDataTranslate} from '@/utils'
import ZsqDockingRecords from "./org-zsq-docking-records.vue";

export default {
  data() {
    return {
      dataForm: {},
      dataList: [],
      dataListLoading: false,
      orgAddOrUpdateVisible: false,
      actionAddOrUpdateVisible: false,
      zsqDockingRecordsVisible: false
    }
  },
  components: {
    ZsqDockingRecords,
    TableTreeColumn,
    OrgAddOrUpdate,
    ActionList
  },
  activated() {
    this.getDataList()
  },
  methods: {
    // 获取数据列表
    getDataList() {
      this.dataListLoading = true
      this.$http({
        url: this.$http.adornUrl('/admin/org/getOrgPages'),
        method: 'get',
        params: this.$http.adornParams()
      }).then(({data}) => {
        this.dataList = treeDataTranslate(data.obj, 'id')
        this.dataListLoading = false
      })
    },
    // 处理同步
    syncHandle(id) {
      this.$confirm(`确定要进行同步嘛，请勿重复操作，否则可能会带来不可预知的后果`, '确认同步？', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        closeOnClickModal: false
      }).then(() => {
        this.dataListLoading = true
        this.$http({
          url: this.$http.adornUrl('/admin/zyz/platform-sync/syncBase'),
          method: 'post',
          params: this.$http.adornParams({
            'baseId': id
          })
        }).then(({data}) => {
          this.dataListLoading = false
          if (data && data.code === 0) {
            this.$message({
              message: '请求同步成功，市平台返回：（' + data.obj.message + '）',
              type: data.obj.resultCode === 'SUCCESS' ? 'success' : 'warning',
              duration: 3000,
              onClose: () => {
                this.getDataList()
              }
            })
          } else {
            this.$message.error(data.msg)
          }
        })
      })
    },
    // 保存或者编辑完成后回调
    saveOrUpdateCallback(menuId) {
      this.getDataList()
      // 保存完了后刷一下“action”的控件
      this.orgAddOrUpdateHandle(menuId)
    },
    // 处理选中行
    handleCurrentChange(val) {
      this.orgAddOrUpdateHandle(val.id)
    },
    // 菜单控件
    orgAddOrUpdateHandle(menuId, parentId) {
      this.orgAddOrUpdateVisible = true
      this.$nextTick(() => {
        this.$refs.OrgAddOrUpdate.init(menuId, parentId)
      })
    },
    // 删除
    deleteHandle(id) {
      this.$confirm(`确定要删除此菜单吗?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        closeOnClickModal: false
      }).then(() => {
        this.$http({
          url: this.$http.adornUrl(`/admin/org/removeByIds`),
          method: 'get',
          params: this.$http.adornParams({'ids': id})
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.$message({
              message: '操作成功',
              type: 'success',
              duration: 1500,
              onClose: () => {
                this.getDataList()
                this.orgAddOrUpdateVisible = false
                this.actionAddOrUpdateVisible = false
              }
            })
          } else {
            this.$message.error(data.msg)
          }
        })
      }).catch(() => {
      })
    },
    zsqDockingRecords(id) {
        this.zsqDockingRecordsVisible = true
        this.$nextTick(() => {
            this.$refs.zsqDockingRecords.init(id)
        })
    }
  }
}
</script>
<style>
.status-badge {
  margin-right: 5px;
}
</style>
