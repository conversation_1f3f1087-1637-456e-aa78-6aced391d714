<template>
  <div class="mod-dict">
    <el-form :inline="true" :model="dataForm" ref="dataForm">
      <el-form-item label="模糊搜索" prop="keyword">
        <el-input style="width: 300px" v-model="dataForm.keyword" placeholder="手机号/任务序列id" clearable></el-input>
      </el-form-item>
      <el-form-item label="发送状态" prop="status">
        <el-select v-model="dataForm.status" placeholder="请选择" clearable>
          <el-option
            v-for="item in [{label: ' 发送成功', value: '10'}, {label: '发送失败', value: '20'}]"
            :key="item.value"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="接收时间" prop="timeRange">
        <el-date-picker v-model="dataForm.timeRange" clearable type="datetimerange"
                        value-format="yyyy-MM-dd HH:mm:ss" align="right" start-placeholder="开始时间" end-placeholder="结束时间"
                        :default-time="['00:00:00', '23:59:59']">
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="warning" icon="el-icon-search" @click="currentChangeHandle(1)">查询</el-button>
        <el-button icon="el-icon-refresh-left" @click="resetForm()">重置</el-button>
      </el-form-item>
    </el-form>
    <div class="f-s-c" style="padding: 15px 0;display: flex; justify-content: flex-end">
      <div>
        <el-button icon="el-icon-s-operation" type="primary" @click="syncSendStatus">同步发送状态</el-button>
      </div>
    </div>
    <el-table
      :data="dataList"
      border
      v-loading="dataListLoading"
      style="width: 100%;">
      <el-table-column
        prop="taskID"
        header-align="center"
        align="center"
        label="任务序列id">
      </el-table-column>
      <el-table-column
        prop="mobile"
        header-align="center"
        align="center"
        label="手机号">
      </el-table-column>
      <el-table-column
        prop="status"
        header-align="center"
        align="center"
        width="150"
        label="信息发送状态">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.status === '20'" size="small" type="danger">发送失败</el-tag>
          <el-tag v-else-if="scope.row.status === '10'" size="small" type="success">发送成功</el-tag>
          <el-tag v-else size="small" type="warning">{{scope.row.status}}</el-tag>
        </template>
      </el-table-column>
      <el-table-column
          prop="receiveTime"
          header-align="center"
          align="center"
          label="接收时间">
      </el-table-column>
      <el-table-column
          prop="errorCode"
          header-align="center"
          align="center"
          label="返回code">
      </el-table-column>
      <el-table-column
          prop="extNo"
          fixed="right"
          header-align="center"
          align="center"
          label="扩展子号">
      </el-table-column>
    </el-table>
    <el-pagination
      @size-change="sizeChangeHandle"
      @current-change="currentChangeHandle"
      :current-page="pageIndex"
      :page-sizes="[10, 20, 50, 100]"
      :page-size="pageSize"
      :total="totalPage"
      layout="total, sizes, prev, pager, next, jumper">
    </el-pagination>
  </div>
</template>

<script>
  export default {
    data () {
      return {
        dataForm: {
          keyword: null,
          status: null,
          timeRange: []
        },
        dataList: [],
        pageIndex: 1,
        pageSize: 10,
        totalPage: 0,
        dataListLoading: false
      }
    },
    components: {
    },
    activated () {
      this.init()
    },
    methods: {
      init () {
        this.$refs['dataForm']?.resetFields()
        var taskId = this.$route.params.taskId
        if (taskId && taskId !== '') {
          this.dataForm.keyword = taskId
        }
        this.pageIndex = 1
        this.getDataList()
      },
      // 重置
      resetForm () {
        this.$refs['dataForm']?.resetFields()
        this.getDataList()
      },
      // 获取数据列表
      getDataList () {
        this.dataListLoading = true
        this.$http({
          url: this.$http.adornUrl('/admin/msg_record_real_send_status/pages'),
          method: 'post',
          data: this.$http.adornData({
            'currentPage': this.pageIndex,
            'pageSize': this.pageSize,
            'orders': [{column: 'receiveTime', sort: 'desc'}],
            'keyword': this.dataForm.keyword,
            'status': this.dataForm.status,
            'start': this.dataForm.timeRange && this.dataForm.timeRange.length >= 1 ? this.dataForm.timeRange[0] : null,
            'end': this.dataForm.timeRange && this.dataForm.timeRange.length >= 2 ? this.dataForm.timeRange[1] : null
          })
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.dataList = data.obj.records
            this.totalPage = data.obj.total
          } else {
            this.dataList = []
            this.totalPage = 0
          }
          this.dataListLoading = false
        })
      },
      // 每页数
      sizeChangeHandle (val) {
        this.pageSize = val
        this.pageIndex = 1
        this.getDataList()
      },
      // 当前页
      currentChangeHandle (val) {
        this.pageIndex = val
        this.getDataList()
      },
      // 删除
      syncSendStatus () {
        this.$confirm(`确定开始同步消息发送真实状态?`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.$http({
            url: this.$http.adornUrl('/admin/msg_record_real_send_status/syncRealSendStatus'),
            method: 'get'
          }).then(({data}) => {
            if (data && data.code === 0) {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 1500,
                onClose: () => {
                  this.getDataList()
                }
              })
            } else {
              this.$message.error(data.msg)
            }
          })
        }).catch(() => {})
      }
    }
  }
</script>
