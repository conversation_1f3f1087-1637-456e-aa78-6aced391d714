<template>
  <el-dialog
    :title="dataForm.status === 1 ? '推送进度' : '推送结果'"
    :close-on-click-modal="false"
    width="70%"
    @close="() => {
        visible = false
        this.$emit('refreshDataList')}"
    :visible.sync="visible">
    <el-form :model="dataForm" ref="dataForm" label-width="120px">
      <el-row :gutter="6" v-if="dataForm.status === 1">
        <el-col :span="24">
          <el-form-item label="推送进度:" prop="percentage">
            <el-progress style="margin-top: 8px" :percentage="dataForm.percentage" :color="progressColor"></el-progress>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="6">
        <el-col :span="6">
          <el-form-item label="推送总数:" prop="total">
            <span>{{dataForm.total}}</span>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="已推送数:" prop="done">
            <span>{{dataForm.done}}</span>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="推送成功数:" prop="success">
            <span>{{dataForm.success}}</span>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="推送失败数:" prop="fail">
            <span>{{dataForm.fail}}</span>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="6">
        <el-col :span="24">
          <el-form-item label="推送成功明细:" prop="msgSuccessList">
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="6">
        <el-col :span="24">
          <el-table
            :data="dataForm.msgSuccessList"
            border
            style="width: 100%;">
            <el-table-column
              v-if="siteUse === true"
              prop="sendType"
              header-align="center"
              align="center"
              label="推送方式">
            </el-table-column>
            <el-table-column
              v-if="tmpUse === true"
              prop="selfDefine"
              header-align="center"
              align="center"
              label="消息类型">
              <template slot-scope="scope">
                <el-tag v-if="scope.row.selfDefine === true" size="small">自定义消息</el-tag>
                <el-tag v-if="scope.row.selfDefine === false" size="small" type="success">模版消息</el-tag>
              </template>
            </el-table-column>
            <el-table-column
              prop="senderName"
              header-align="center"
              align="center"
              label="推送者">
            </el-table-column>
            <el-table-column
              prop="sendTime"
              header-align="center"
              align="center"
              label="推送时间">
            </el-table-column>
            <el-table-column
              prop="receiverName"
              header-align="center"
              align="center"
              label="接收者">
            </el-table-column>
            <el-table-column
                prop="receiverMobile"
                header-align="center"
                align="center"
                label="接收者手机号">
            </el-table-column>
            <el-table-column
              fixed="right"
              prop="content"
              header-align="center"
              align="center"
              :show-overflow-tooltip="true"
              label="消息内容">
            </el-table-column>
          </el-table>
          <el-pagination
            @size-change="successSizeChangeHandle"
            @current-change="successCurrentChangeHandle"
            :current-page="successPageIndex"
            :page-sizes="[5, 10, 20, 50, 100]"
            :page-size="successPageSize"
            :total="successTotalPage"
            layout="total, sizes, prev, pager, next, jumper">
          </el-pagination>
        </el-col>
      </el-row>
      <el-row :gutter="6" v-if="dataForm.fail !== 0">
        <el-col :span="24">
          <el-form-item label="推送失败明细:" prop="msgFailList">
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="6" v-if="dataForm.fail !== 0">
        <el-col :span="24">
          <el-table
            :data="dataForm.msgFailList"
            border
            style="width: 100%;">
            <el-table-column
              v-if="siteUse === true"
              prop="sendType"
              header-align="center"
              align="center"
              label="推送方式">
            </el-table-column>
            <el-table-column
              v-if="tmpUse === true"
              prop="selfDefine"
              header-align="center"
              align="center"
              label="消息类型">
              <template slot-scope="scope">
                <el-tag v-if="scope.row.selfDefine === true" size="small">自定义消息</el-tag>
                <el-tag v-if="scope.row.selfDefine === false" size="small" type="success">模版消息</el-tag>
              </template>
            </el-table-column>
            <el-table-column
                prop="senderName"
                header-align="center"
                align="center"
                label="推送者">
            </el-table-column>
            <el-table-column
                prop="sendTime"
                header-align="center"
                align="center"
                label="推送时间">
            </el-table-column>
            <el-table-column
                prop="receiverName"
                header-align="center"
                align="center"
                label="接收者">
            </el-table-column>
            <el-table-column
                prop="receiverMobile"
                header-align="center"
                align="center"
                label="接收者手机号">
            </el-table-column>
            <el-table-column
                prop="content"
                header-align="center"
                align="center"
                :show-overflow-tooltip="true"
                label="消息内容">
            </el-table-column>
            <el-table-column
              fixed="right"
              prop="sendFailReason"
              header-align="center"
              align="center"
              :show-overflow-tooltip="true"
              label="失败原因">
              <template slot-scope="scope">
                <span style="color: red">{{scope.row.sendFailReason}}</span>
              </template>
            </el-table-column>
          </el-table>
          <el-pagination
            @size-change="failSizeChangeHandle"
            @current-change="failCurrentChangeHandle"
            :current-page="failPageIndex"
            :page-sizes="[5, 10, 20, 50, 100]"
            :page-size="failPageSize"
            :total="failTotalPage"
            layout="total, sizes, prev, pager, next, jumper">
          </el-pagination>
        </el-col>
      </el-row>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="() => {
        visible = false
        this.$emit('refreshDataList')}">关闭</el-button>
    </span>
  </el-dialog>
</template>

<script>
  export default {
    data () {
      return {
        tmpUse: false,
        siteUse: false,
        visible: false,
        progressColor: '#5cb87a',
        msgSuccessListLoading: false,
        msgFailListLoading: false,
        successPageIndex: 1,
        successPageSize: 5,
        successTotalPage: 0,
        failPageIndex: 1,
        failPageSize: 5,
        failTotalPage: 0,
        dataForm: {
          id: null,
          status: null,
          percentage: 0,
          total: 0,
          done: 0,
          success: 0,
          fail: 0,
          msgSuccessList: [],
          msgFailList: []
        },
        timed: null
      }
    },
    destroyed () {
      window.clearInterval(this.timed)
    },
    watch: {
      dataForm: {
        handler (newVal, oldVal) {
          if (newVal.percentage === 100) {
            window.clearInterval(this.timed)
          }
        },
        immediate: true,
        deep: true
      }
    },
    methods: {
      init (id, status, tmpUse, siteUse) {
        this.tmpUse = tmpUse
        this.siteUse = siteUse
        this.dataForm.id = id
        this.dataForm.status = status
        this.visible = true
        this.$nextTick(() => {
          this.$refs['dataForm']?.resetFields()
          this.getSendProgress()
          this.getMsgSuccessList()
          this.getMsgFailList()
          if (status === 1) {
            this.setTiming()
          }
        })
      },
      setTiming () {
        this.timed = window.setInterval(() => {
          setTimeout(() => {
            this.getMsgSuccessList()
            this.getMsgFailList()
            this.getSendProgress()
          }, 0)
        }, 2000)
      },
      getMsgSuccessList () {
        this.msgSuccessListLoading = true
        this.$http({
          url: this.$http.adornUrl('/admin/msg_record/pages'),
          method: 'post',
          data: this.$http.adornData({
            'currentPage': this.successPageIndex,
            'pageSize': this.successPageSize,
            'sendId': this.dataForm.id,
            'sendStatus': 1
          })
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.dataForm.msgSuccessList = data.obj.records
            this.successTotalPage = data.obj.total
          } else {
            this.dataForm.msgSuccessList = []
            this.successTotalPage = 0
          }
          this.msgSuccessListLoading = false
        })
      },
      getMsgFailList () {
        this.msgFailListLoading = true
        this.$http({
          url: this.$http.adornUrl('/admin/msg_record/pages'),
          method: 'post',
          data: this.$http.adornData({
            'currentPage': this.failPageIndex,
            'pageSize': this.failPageSize,
            'sendId': this.dataForm.id,
            'sendStatus': 2
          })
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.dataForm.msgFailList = data.obj.records
            this.failTotalPage = data.obj.total
          } else {
            this.dataForm.msgFailList = []
            this.failTotalPage = 0
          }
          this.msgFailListLoading = false
        })
      },
      getSendProgress () {
        this.$http({
          url: this.$http.adornUrl('/admin/msg_record/sendProgress'),
          method: 'get',
          params: this.$http.adornParams({
            'sendId': this.dataForm.id
          })
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.dataForm.total = data.obj.total
            this.dataForm.done = data.obj.done
            this.dataForm.success = data.obj.success
            this.dataForm.fail = data.obj.fail
            this.dataForm.percentage = (data.obj.done / data.obj.total * 100).toFixed(0)
          }
        })
      },
      // 每页数
      successSizeChangeHandle (val) {
        this.successPageSize = val
        this.successPageIndex = 1
        this.getMsgSuccessList()
      },
      // 当前页
      successCurrentChangeHandle (val) {
        this.successPageIndex = val
        this.getMsgSuccessList()
      },
      // 每页数
      failSizeChangeHandle (val) {
        this.failPageSize = val
        this.failPageIndex = 1
        this.getMsgFailList()
      },
      // 当前页
      failCurrentChangeHandle (val) {
        this.failPageIndex = val
        this.getMsgFailList()
      }
    }
  }
</script>

<style lang="scss" scoped>
.form-table ::v-deep .el-form-item__error {
  position: static;
  text-align: left;
}
</style>
