<template>
  <el-dialog
    :title="!dataForm.id ? '新增' : '修改'"
    :close-on-click-modal="false"
    width="70%"
    @closed="() => {visible = false; sendPersonForm.sendPersonList = []}"
    :visible.sync="visible">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmit()" label-width="120px" v-loading="dataFormLoading">
      <el-row :gutter="6">
        <el-col :span="12">
          <el-form-item label="消息类型" prop="selfDefine" v-if="tmpUse === true">
            <el-radio-group v-model="dataForm.selfDefine" :disabled="dataForm.status !== 0" @change="() => {
              if (dataForm.selfDefine === false) {
                dataForm.selfDefineMsg = null
              } else {
                dataForm.tmpId = null
              }
            }">
              <el-radio :label="false">模版消息</el-radio>
              <el-radio :label="true">自定义消息</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="推送方式" prop="sendTypeList" v-if="siteUse === true">
            <el-checkbox-group  :disabled="dataForm.status !== 0" v-model="dataForm.sendTypeList" @change="tableKey = Math.random()">
              <el-checkbox v-for="item in sendTypeList" :label="item.code" :key="item.code">{{item.name}}</el-checkbox>
            </el-checkbox-group>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="6" v-if="dataForm.selfDefine === false">
        <el-col :span="12">
           <el-form-item label="消息模版" prop="tmpId">
             <el-row>
               <el-select :disabled="dataForm.status !== 0" v-model="dataForm.tmpId" filterable placeholder="消息模版" clearable style="width: 100%" @change="(value) => {dataForm.title = lodash.find(tmpList, ['id', value])['title']}">
                 <el-option
                   v-for="item in tmpList"
                   :key="item.id"
                   :label="item.title"
                   :value="item.id">
                 </el-option>
               </el-select>
             </el-row>
             <el-input style="margin-top: 5px" disabled v-if="dataForm.tmpId && dataForm.tmpId !== ''" type="textarea" v-model="lodash.find(tmpList, ['id', dataForm.tmpId])['tmp']" placeholder="模板" :rows="3"></el-input>
            </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="6">
        <el-col :span="24">
          <el-form-item label="消息标题" prop="title">
            <el-input v-model="dataForm.title" placeholder="标题" maxlength="100" show-word-limit clearable></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="6" v-if="dataForm.selfDefine === true">
        <el-col :span="24">
          <el-form-item :label="tmpUse === true ? '自定义消息' : '消息体'" prop="selfDefineMsg">
            <el-input id="contentInput" type="textarea" v-model="dataForm.selfDefineMsg" placeholder="模板" maxlength="300" show-word-limit :rows="3"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row v-if="dataForm.selfDefine === true">
        <el-form-item style="float: right">
          <el-button size="mini" type="primary" @click="addMark('%rn')">添加接收者占位符</el-button>
        </el-form-item>
      </el-row>
      <el-row :gutter="6" v-if="dataForm.id && dataForm.receiverOverLimit === true">
        <el-col :span="24">
          <el-form-item label="接收对象">
           <span style="color: red">*接收者数据量较大，请使用接收者维护功能单独对接收者进行维护！</span>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="6" v-if="!dataForm.id || dataForm.receiverOverLimit === false">
        <el-col :span="3">
          <el-form-item label="接收对象">
            <el-button type="primary" @click="templateDownload()" size="mini">Excel模版下载</el-button>
          </el-form-item>
        </el-col>
        <el-col :span="3">
          <el-form-item>
            <el-upload
              ref="upload"
              name="excel"
              :action="this.$http.adornUrl(`/admin/msg_send/receiverImport`)"
              :headers="myHeaders"
              :data="this.dataForm.id ?  { siteUse: this.siteUse, sendId: this.dataForm.id } : { siteUse: this.siteUse }"
              :on-success="successHandle"
              :on-change="changHandle"
              accept="xlsx"
              :limit="1"
              :show-file-list="false"
              :on-exceed="handleExceed"
              :before-upload="function (file){return docBeforeUpload(file)}"
              :file-list="fileList">
              <el-button type="primary" size="mini">Excel上传</el-button>
            </el-upload>
          </el-form-item>
        </el-col>
        <el-col :span="14">
          <el-form-item style="color: red">
          *只能上传xls或xlsx文件，大小10M以内，请对接收对象的手机号去重
          </el-form-item>
        </el-col>
        <el-col :span="4">
          <el-form-item label-width="0px">
            <a :href="$http.adornAttachmentUrl(dataForm.receiverExcelFile.path)" v-if="dataForm.receiverExcelFile" style="cursor: pointer; margin-right: 5px">{{dataForm.receiverExcelFile.fileName}}</a>
            <i v-if="dataForm.receiverExcelFile" id="excel_remove" class="el-icon-delete" style="cursor: pointer" @click="() => {fileList = []; dataForm.receiverExcelFile = null; dataForm.receiverOverLimit = false; dataForm.receivers = null; sendPersonForm.sendPersonList = []}"></i>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="6" v-if="!dataForm.id || dataForm.receiverOverLimit === false">
        <el-col :span="24">
          <el-form :model="sendPersonForm" ref="sendPersonForm" :rules="sendPersonForm.sendPersonRules">
            <table id="send_person_table_header" :key="tableKey">
              <thead>
                <tr>
                  <th style="width: 20%"><i class="el-icon-circle-plus-outline" style="font-size: 20px;" @click="addOneRecord()"/></th>
                  <th style="width: 20%">接收者手机号</th>
                  <th style="width: 20%">接收者姓名</th>
                  <th style="width: 20%" v-if="dataForm.sendTypeList.indexOf('mpt_site') >= 0 && siteUse === true">接收者志愿者信息</th>
                  <th style="width: 20%">操作</th>
                </tr>
              </thead>
            </table>
            <div id="send_person_table_body_container" v-loading="sendPersonListLoading">
              <div v-if="sendPersonForm.sendPersonList.length <= 0" style="margin-top: 50px">{{dataForm.receiverOverLimit === true ? '数据量太大，不予展示…………' : '暂无数据…………'}}</div>
              <table id="send_person_table_body" :key="tableKey">
                <tbody id="tbody">
                <tr v-for="(item, index) in sendPersonForm.sendPersonList" :key="index">
                  <td style="width: 20%">{{index + 1}}</td>
                  <td style="width: 20%">
                    <el-form-item :prop="'sendPersonList.' + index + '.receiverMobile'" :rules='sendPersonForm.sendPersonRules.receiverMobile' style="margin: 0px">
                      <input v-model="item.receiverMobile" placeholder="请输入接收者手机号" @change="changeReceiverMobile(index)" />
                      <i class="el-icon-search" v-if="dataForm.sendTypeList.indexOf('mpt_site') >= 0 && siteUse === true" @click="searchVolunteer(index)" style="font-size: 10px; padding-left: 5px"></i>
                    </el-form-item>
                  </td>
                  <td style="width: 20%">
                    <el-form-item :prop="'sendPersonList.' + index + '.receiverName'" style="margin: 0px">
                      <input v-model="item.receiverName" placeholder="请输入接收者姓名"/>
                    </el-form-item>
                  </td>
                  <td style="width: 20%" v-if="dataForm.sendTypeList.indexOf('mpt_site') >= 0 && siteUse === true">
                    <el-form-item style="margin-bottom: 0"
                                  :prop="'sendPersonList.' + index + '.receiverVolunteerName'">
                      <span :style="item.receiver ? {color: 'green'} : {color: 'red'}">{{item.receiverVolunteerName}}</span>
                    </el-form-item>
                  </td>
                  <td style="width: 20%"><i class="el-icon-delete" style="font-size: 18px;" @click="deleteRecord(index, item.id)"/></td>
                </tr>
                </tbody>
              </table>
            </div>
<!--            <el-table-->
<!--              :key="tableKey"-->
<!--              class="form-table"-->
<!--              :data="sendPersonForm.sendPersonList"-->
<!--              border-->
<!--              v-loading="sendPersonListLoading"-->
<!--              height="500px"-->
<!--              style="width: 100%;">-->
<!--              <el-table-column-->
<!--                type="index"-->
<!--                align="center"-->
<!--                width="100px"-->
<!--                header-align="center">-->
<!--                <template-->
<!--                  slot="header"-->
<!--                  slot-scope="{ column, $index }">-->
<!--                  <el-button class="el-icon-circle-plus-outline" circle style="font-size: 30px; border: 0px; padding: 0px"-->
<!--                             @click="addOneRecord()"/>-->
<!--                </template>-->
<!--              </el-table-column>-->
<!--              <el-table-column-->
<!--                prop="receiverMobile"-->
<!--                header-align="center"-->
<!--                align="center"-->
<!--                label="接收者手机号">-->
<!--                <template slot-scope="scope">-->
<!--                  <el-form-item style="margin-bottom: 0"-->
<!--                                :prop="'sendPersonList.' + scope.$index + '.receiverMobile'"-->
<!--                                :rules='sendPersonForm.sendPersonRules.receiverMobile'>-->
<!--                    <el-input size="small" v-model="scope.row.receiverMobile" placeholder="请输入接收者手机号" clearable @change="changeReceiverMobile(scope.$index)">-->
<!--&lt;!&ndash;                      <el-button size="mini" v-if="dataForm.sendTypeList.indexOf('mpt_site') >= 0 && siteUse === true" slot="append" icon="el-icon-search" @click="searchVolunteer(scope.$index)"></el-button>&ndash;&gt;-->
<!--                    </el-input>-->
<!--                  </el-form-item>-->
<!--                </template>-->
<!--              </el-table-column>-->
<!--              <el-table-column-->
<!--                prop="receiverName"-->
<!--                header-align="center"-->
<!--                align="center"-->
<!--                label="接收者姓名">-->
<!--                <template slot-scope="scope">-->
<!--                  <el-form-item style="margin-bottom: 0"-->
<!--                                :prop="'sendPersonList.' + scope.$index + '.receiverName'">-->
<!--                    <el-input size="mini"  v-model="scope.row.receiverName" placeholder="请输入接收者姓名" clearable></el-input>-->
<!--                  </el-form-item>-->
<!--                </template>-->
<!--              </el-table-column>-->
<!--              <el-table-column-->
<!--                v-if="dataForm.sendTypeList.indexOf('mpt_site') >= 0 && siteUse === true"-->
<!--                prop="receiverVolunteerName"-->
<!--                header-align="center"-->
<!--                align="center"-->
<!--                label="接收者志愿者信息">-->
<!--                <template slot-scope="scope">-->
<!--                  <el-form-item style="margin-bottom: 0"-->
<!--                                :prop="'sendPersonList.' + scope.$index + '.receiverVolunteerName'">-->
<!--                    <span :style="scope.row.receiver ? {color: 'green'} : {color: 'red'}">{{scope.row.receiverVolunteerName}}</span>-->
<!--                  </el-form-item>-->
<!--                </template>-->
<!--              </el-table-column>-->
<!--              <el-table-column-->
<!--                fixed="right"-->
<!--                header-align="center"-->
<!--                align="center"-->
<!--                label="操作">-->
<!--                <template slot-scope="scope">-->
<!--                  <i class="el-icon-delete" style="font-size: 18px;" @click="deleteRecord(scope.$index, scope.row.id)"></i>-->
<!--                </template>-->
<!--              </el-table-column>-->
<!--            </el-table>-->
          </el-form>
        </el-col>
      </el-row>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()" :disabled="dataFormLoading || sendPersonListLoading">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
  import _ from 'lodash'
  import Vue from 'vue'
  export default {
    data () {
      var validateRepeat = (rule, value, callback) => {
        let mobiles = _.map(this.sendPersonForm.sendPersonList, 'receiverMobile')
        if (_.filter(mobiles, function(o) {return o === value}).length > 1) {
          callback(new Error('该手机号已存在！'))
        } else {
          callback()
        }
      }
      return {
        tmpUse: false,
        siteUse: false,
        tableKey: null,
        visible: false,
        tmpList: [],
        sendTypeList: [],
        lodash: null,
        myHeaders: {Authorization: Vue.cookie.get('Authorization')},
        fileList: [],
        dataFormLoading: false,
        dataForm: {
          id: null,
          version: null,
          createDate: null,
          updateDate: null,
          creator: null,
          updater: null,
          selfDefine: null,
          sendCode: null,
          tmpId: null,
          selfDefineMsg: null,
          title: null,
          status: 0,
          sendType: null,
          sendTypeList: [],
          receivers: null,
          receiverOverLimit: null,
          receiverExcelFile: null,
          siteUse: false
        },
        sendPersonListLoading: false,
        sendPersonForm: {
          sendPersonList: [],
          sendPersonRules: {
            receiverMobile: [
              { required: true, message: '接收者手机号不能为空', trigger: 'change' },
              {validator: validateRepeat, trigger: 'change'}
            ]
          }
        },
        dataRule: {
          title: [
            { required: true, message: '标题不能为空', trigger: 'change' }
          ],
          selfDefine: [
            { required: true, message: '消息类型不能为空', trigger: 'change' }
          ],
          tmpId: [
            { required: true, message: '消息模版不能为空', trigger: 'change' }
          ],
          selfDefineMsg: [
            { required: true, message: '自定义消息不能为空', trigger: 'blur' }
          ],
          sendTypeList: [
            { type: 'array', required: true, message: '推送方式不能为空', trigger: 'change' }
          ]
        }
      }
    },
    methods: {
      init (id, tmpUse, siteUse) {
        this.dataForm.id = id || null
        this.tmpUse = tmpUse || false
        this.siteUse = siteUse || false
        this.visible = true
        this.tableKey = Math.random()
        this.getTmpList()
        this.getSendTypeList()
        this.lodash = _
        this.$nextTick(() => {
          this.$refs['dataForm']?.resetFields()
          this.dataForm.status = 0
          this.dataForm.sendCode = null
          this.dataForm.createDate = null
          this.dataForm.updateDate = null
          this.dataForm.creator = null
          this.dataForm.updater = null
          this.dataForm.tmpId = null
          this.dataForm.selfDefineMsg = null
          this.dataForm.selfDefine = (this.tmpUse === true ? null : true)
          this.dataForm.sendType = (this.siteUse === true ? null : 'mpt_sms')
          this.dataForm.sendTypeList = (this.siteUse === true ? [] : ['mpt_sms'])
          this.dataForm.receivers = null
          this.dataForm.receiverOverLimit = null
          this.dataForm.receiverExcelFile = null
          this.dataForm.siteUse = siteUse
          this.sendPersonForm.sendPersonList = []
          this.fileList = []
          if (this.dataForm.id) {
            // this.dataFormLoading = true
            this.sendPersonListLoading = true
            this.$http({
              url: this.$http.adornUrl(`/admin/msg_send`),
              method: 'get',
              params: this.$http.adornParams({ id: this.dataForm.id })
            }).then(({data}) => {
              if (data && data.code === 0) {
                // this.dataFormLoading = false
                this.tableKey = Math.random()
                this.dataForm = data.obj
                this.dataForm.siteUse = siteUse
                this.dataForm.sendTypeList = data.obj.sendType.split(',')
                this.sendPersonForm.sendPersonList = data.obj.receivers
                this.sendPersonListLoading = false
              }
            })
          }
        })
      },
      changeReceiverMobile(index) {
        this.$nextTick(() => {
          this.$refs.sendPersonForm.validateField('sendPersonList.' + index + '.receiverMobile')
        });
        this.$set(this.sendPersonForm.sendPersonList[index], 'receiver',  null)
        this.$set(this.sendPersonForm.sendPersonList[index], 'receiver',  null)
        this.$set(this.sendPersonForm.sendPersonList[index], 'receiverVolunteerName', null)
      },
      searchVolunteer (index) {
        const mobile = this.sendPersonForm.sendPersonList[index].receiverMobile
        if (mobile && mobile !== '') {
          this.$http({
            url: this.$http.adornUrl(`/admin/zyz/volunteer/getVolunteerByPhone`),
            method: 'get',
            params: this.$http.adornParams({ phone: mobile })
          }).then(({data}) => {
            if (data && data.code === 0) {
              this.$set(this.sendPersonForm.sendPersonList[index], 'receiver',  data.obj.id)
              this.$set(this.sendPersonForm.sendPersonList[index], 'receiverVolunteerName', data.obj.name)
              if (!this.sendPersonForm.sendPersonList[index].receiverName || this.sendPersonForm.sendPersonList[index].receiverName === '') {
                this.$set(this.sendPersonForm.sendPersonList[index], 'receiverName', data.obj.name)
              }
            } else {
              this.$set(this.sendPersonForm.sendPersonList[index], 'receiver',  null)
              this.$set(this.sendPersonForm.sendPersonList[index], 'receiverVolunteerName', data.msg)
            }
          })
        } else {
          this.$message.warning('请先输入手机号！')
        }
      },
      addOneRecord () {
        let data = {
          receiverMobile: null,
          receiverName: null,
          receiver: null,
          receiverVolunteerName: null
        }
        this.sendPersonForm.sendPersonList.unshift(data)
      },
      deleteRecord (index, id) {
        this.sendPersonForm.sendPersonList.splice(index, 1)
      },
      getSendTypeList () {
        this.$http({
          url: this.$http.adornUrl('/admin/dict/parent'),
          method: 'get',
          params: this.$http.adornParams({
            'code': 'MSG_PUSH_TYPE'
          })
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.sendTypeList = data.obj
          } else {
            this.sendTypeList = []
          }
        })
      },
      addMark (mark) {
        var elInput = document.getElementById('contentInput') // 根据id选择器选中对象
        var startPos = elInput.selectionStart// input 第0个字符到选中的字符
        var endPos = elInput.selectionEnd// 选中的字符到最后的字符
        if (startPos === undefined || endPos === undefined) return
        var txt = elInput.value
        // 将表情添加到选中的光标位置
        var result = txt.substring(0, startPos) + mark + txt.substring(endPos)
        elInput.value = result// 赋值给input的value
        // 重新定义光标位置
        elInput.focus()
        elInput.selectionStart = startPos + mark.length
        elInput.selectionEnd = startPos + mark.length
        this.dataForm.selfDefineMsg = result
      },
      getTmpList () {
        this.$http({
          url: this.$http.adornUrl(`/admin/msg_tmp/listOfEnable`),
          method: 'get'
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.tmpList = data.obj
          } else {
            this.tmpList = []
          }
        })
      },
      // 下载模板文件
      templateDownload () {
        this.$http({
          url: this.$http.adornUrl('/admin/msg_send/receiverExcelTemplate'),
          method: 'get',
          responseType: 'arraybuffer',
          params: this.$http.adornParams()
        }).then(({data}) => {
          if (data.code && data.code !== 0) {
            this.$message.error('下载失败')
          } else {
            let blob = new Blob([data], {type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8'})
            let objectUrl = URL.createObjectURL(blob)
            let a = document.createElement('a')
            // window.location.href = objectUrl
            a.href = objectUrl
            a.download = '消息接收者导入模版.xlsx'
            a.click()
            URL.revokeObjectURL(objectUrl)
            this.$message({
              type: 'success',
              message: '下载模板成功'
            })
          }
        })
      },
      // 上传成功
      successHandle (response) {
        if (response && response.code === 0) {
          this.dataForm.receiverOverLimit = response.obj.receiverOverLimit
          this.dataForm.receiverExcelFile = response.obj.ossObj
          if (response.obj.receiverOverLimit === true) {
            this.$message.warning('由于您上传的数据量超过2000，考虑到网页性能，将不为您展示上传数据！如需修改，您可以下载上传的文件进行数据修改后重新上传！')
            this.sendPersonListLoading = false
            return
          }
          _.forEach(response.obj.receiverList, it => {
            let data = {
              receiverMobile: it.receiverMobile,
              receiverName: it.receiverName,
              receiver: it.receiver,
              receiverVolunteerName: it.receiverVolunteerName
            }
            this.sendPersonForm.sendPersonList.unshift(data)
          })
        } else {
          this.$message.error(response.msg)
        }
        this.sendPersonListLoading = false
        // this.fileList = []
      },
      changHandle (file, fileList) {
        let FileExt = file.name.replace(/.+\./, '')
        const isLt10M = file.size / 1024 / 1024 < 10
        if (['xlsx', 'xls'].indexOf(FileExt.toLowerCase()) === -1) {
          this.$message({
            type: '上传失败',
            message: '请上传后缀名为xlsx或xls的文件！'
          })
          this.fileList = []
          return
        }
        if (!isLt10M) {
          this.$message.error('上传文件大小不能超过 10M!')
          this.fileList = []
          return
        }
        this.fileList = fileList
      },
      handleExceed () {
        this.$message.error('只能选择一个文件，如需替换请删除已选文件后重试')
      },
      docBeforeUpload (file) {
        let FileExt = file.name.replace(/.+\./, '')
        if (['xlsx', 'xls'].indexOf(FileExt.toLowerCase()) === -1) {
          this.$message({
            type: '上传失败',
            message: '请上传后缀名为xlsx, xls的附件！'
          })
          return false
        }
        const isLt10M = file.size / 1024 / 1024 < 10
        if (['xlsx', 'xls'].indexOf(FileExt.toLowerCase()) !== -1 && !isLt10M) {
          this.$message.error('附件大小不能超过 10M!')
          return false
        }
        this.sendPersonListLoading = true
      },
      // 表单提交
      dataFormSubmit () {
        let validSendPersonForm = true
        this.$refs['sendPersonForm'].validate((valid) => {
          validSendPersonForm = valid
        })
        if (validSendPersonForm) {
          this.$refs['dataForm'].validate((valid) => {
            this.dataForm.sendType = this.dataForm.sendTypeList && this.dataForm.sendTypeList.length > 0 ? this.dataForm.sendTypeList.join(',') : ''
            this.dataForm.receivers = this.sendPersonForm.sendPersonList
            if (valid) {
              this.dataFormLoading = true
              if (this.dataForm.receiverOverLimit === true && !this.dataForm.id) {
                this.$set(this.dataForm, 'receiverExcelFilePath', this.$http.adornAttachmentUrl(this.dataForm.receiverExcelFile.path))
              }
              this.$http({
                url: this.$http.adornUrl(`/admin/msg_send/${!this.dataForm.id ? 'save' : 'update'}`),
                method: 'post',
                data: this.$http.adornData(this.dataForm)
              }).then(({data}) => {
                if (data && data.code === 0) {
                  this.$message({
                    message: '操作成功, 先前如有发送失败数据，现已更新为待发送！',
                    type: 'success',
                    duration: 1500,
                    onClose: () => {
                      this.dataFormLoading = false
                      this.visible = false
                      this.sendPersonForm.sendPersonList = []
                      this.$emit('refreshDataList')
                    }
                  })
                } else if (data && data.code === 303) {
                  for (let it of data.obj) {
                    this[`${it.field}Error`] = it.message
                  }
                  this.dataFormLoading = false
                } else {
                  this.dataFormLoading = false
                  this.$message.error(data.msg)
                }
              })
            }
          })
        }
      }
    }
  }
</script>

<style lang="scss" scoped>
#send_person_table_body ::v-deep .el-form-item__error {
  position: static;
  text-align: left;
  padding-left: 10%;
}
table {
  border-collapse: collapse;
  width: 100%;
}
th {
  border: 1px solid #EBEEF5;
  color: #909399;
  font-size: 15px;
  font-weight: bold;
  padding: 15px 0;
  text-align: center;
}
td {
  border: 1px solid #EBEEF5;
  font-size: 12px;
  padding: 6px 0px 6px 0px;
  color: black;
  text-align: center;
}
#send_person_table_body ::v-deep input {
  padding: 0px 30px 0px 15px;
  width: 80%;
  height: 30px;
  border: 1px solid #DCDFE6;
  border-radius: 4px;
}
#send_person_table_body ::v-deep input::placeholder {
  color: #c1c5cd !important;
}
#send_person_table_body_container {
  height: 350px;
  overflow-y: auto;
  border-bottom: 1px solid #EBEEF5;
  border-left: 1px solid #EBEEF5;
  border-right: 1px solid #EBEEF5;
  text-align: center;
}
#excel_remove:hover{
  color: #54a8c6;
}
</style>
