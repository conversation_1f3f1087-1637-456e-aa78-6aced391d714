<template>
  <div class="mod-dict">
    <el-form :inline="true" :model="dataForm" ref="dataForm">
      <el-form-item label="模糊搜索" prop="keyword">
        <el-input style="width: 300px" v-model="dataForm.keyword" :placeholder="tmpUse === true ? '模版code/标题/发送批次/内容' : '发送批次/内容'" clearable></el-input>
      </el-form-item>
      <el-form-item label="推送方式" prop="sendType" v-if="siteUse === true">
        <el-dict :code="'MSG_PUSH_TYPE'" v-model="dataForm.sendType"></el-dict>
      </el-form-item>
      <el-form-item label="消息状态" prop="status" v-if="dataForm.sendType === 'mpt_site'">
        <el-select v-model="dataForm.status" placeholder="消息类型" clearable>
          <el-option
              v-for="item in [{code: false, name: '未读'}, {code: true, name: '已读'}]"
              :key="item.code"
              :label="item.name"
              :value="item.code">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="推送状态" prop="sendStatus">
        <el-select v-model="dataForm.sendStatus" placeholder="请选择" clearable>
          <el-option
              v-for="item in [ {label: '待推送', value: 0}, {label: '推送成功', value: 1}, {label: '推送失败', value: 2}]"
              :key="item.value"
              :label="item.label"
              :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="warning" icon="el-icon-search" @click="query()">查询</el-button>
        <el-button icon="el-icon-refresh-left" @click="resetForm()">重置</el-button>
      </el-form-item>
    </el-form>
    <div class="f-s-c" style="padding: 15px 0;display: flex; justify-content: flex-end">
      <div>
        <el-button icon="el-icon-download" type="success" @click="exportHandle()" v-loading.fullscreen.lock="fullscreenLoading">导出</el-button>
        <el-button icon="el-icon-view" v-if="siteUse === true" type="info" @click="readBatch()" :disabled="dataListSelections.length <= 0">批量阅读</el-button>
        <el-button icon="el-icon-delete" type="danger" @click="deleteHandle()" :disabled="dataListSelections.length <= 0">批量删除</el-button>
      </div>
    </div>
    <el-table
      :data="dataList"
      border
      v-loading="dataListLoading"
      @selection-change="selectionChangeHandle"
      style="width: 100%;">
      <el-table-column
        type="selection"
        header-align="center"
        align="center"
        width="50">
      </el-table-column>
      <el-table-column
          prop="sendCode"
          header-align="center"
          align="center"
          width="220"
          label="推送批次">
      </el-table-column>
      <el-table-column
          v-if="tmpUse === true"
          prop="selfDefine"
          header-align="center"
          align="center"
          width="120"
          label="消息类型">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.selfDefine === true" size="small">自定义消息</el-tag>
          <el-tag v-if="scope.row.selfDefine === false" size="small" type="success">模版消息</el-tag>
        </template>
      </el-table-column>
      <el-table-column
          v-if="siteUse === true"
          prop="sendType"
          header-align="center"
          align="center"
          width="120"
          label="推送方式">
      </el-table-column>
      <el-table-column
          prop="title"
          header-align="center"
          align="center"
          :show-overflow-tooltip="true"
          width="150"
          label="消息标题">
      </el-table-column>
      <el-table-column
          prop="sendTime"
          header-align="center"
          align="center"
          width="180"
          label="推送时间">
      </el-table-column>
      <el-table-column
          prop="sendStatus"
          header-align="center"
          align="center"
          width="120"
          label="推送状态">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.sendStatus === 2" size="small" type="danger">推送失败</el-tag>
          <el-tag v-else-if="scope.row.sendStatus === 1" size="small" type="success">推送成功</el-tag>
          <el-tag v-else size="small" type="warning">待推送</el-tag>
        </template>
      </el-table-column>
      <el-table-column
          prop="sendFailReason"
          header-align="center"
          align="center"
          width="150"
          :show-overflow-tooltip="true"
          label="推送失败原因">
        <template slot-scope="scope">
          <span style="color: red">{{scope.row.sendFailReason}}</span>
        </template>
      </el-table-column>
      <el-table-column
          prop="smsSendStatus"
          header-align="center"
          align="center"
          width="120"
          label="真实发送状态">
        <template slot-scope="scope" v-if="scope.row.taskId && scope.row.taskId !== ''">
          <el-tag v-if="scope.row.smsSendStatus === '20'" size="small" type="danger">发送失败</el-tag>
          <el-tag v-else-if="scope.row.smsSendStatus === '10'" size="small" type="success">发送成功</el-tag>
          <el-tag v-else size="small" type="warning">{{scope.row.smsSendStatus}}</el-tag>
        </template>
      </el-table-column>
      <el-table-column
          prop="smsReceiveTime"
          header-align="center"
          align="center"
          width="180"
          label="真实接收时间">
      </el-table-column>
      <el-table-column
          prop="taskId"
          header-align="center"
          align="center"
          width="120"
          label="任务序列 id">
        <template slot-scope="scope">
          <a @click="$router.push({ name:'msg-msg-real-send-status-list',params: { taskId: scope.row.taskId } })" target="_blank" class="buttonText" style="cursor: pointer">{{scope.row.taskId}}</a>
        </template>
      </el-table-column>
      <el-table-column
          prop="senderName"
          header-align="center"
          align="center"
          width="120"
          label="推送者">
      </el-table-column>
      <el-table-column
          prop="receiverName"
          header-align="center"
          align="center"
          width="120"
          label="接收者">
      </el-table-column>
      <el-table-column
          prop="receiverMobile"
          header-align="center"
          align="center"
          width="140"
          label="接收者手机号">
      </el-table-column>
      <el-table-column
        v-if="siteUse === true"
        prop="receiverVolunteer"
        header-align="center"
        align="center"
        width="100"
        label="志愿者">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.receiverVolunteer === false" size="small" type="danger">否</el-tag>
          <el-tag v-if="scope.row.receiverVolunteer === true" size="small" type="success">是</el-tag>
        </template>
      </el-table-column>
      <el-table-column
          prop="content"
          header-align="center"
          align="center"
          :show-overflow-tooltip="true"
          width="150"
          label="消息内容">
      </el-table-column>
      <el-table-column
          v-if="dataForm.sendType === 'mpt_site'"
          prop="status"
          header-align="center"
          align="center"
          width="100"
          label="消息状态">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.status === false" size="small" type="danger">未读</el-tag>
          <el-tag v-if="scope.row.status === true" size="small" type="success">已读</el-tag>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
    @size-change="sizeChangeHandle"
    @current-change="currentChangeHandle"
    :current-page="pageIndex"
    :page-sizes="[10, 20, 50, 100]"
    :page-size="pageSize"
    :total="totalPage"
    layout="total, sizes, prev, pager, next, jumper">
    </el-pagination>
  </div>
</template>
<script>
  export default {
    data () {
      return {
        tmpUse: false,
        siteUse: false,
        dataForm: {
          sendId: null,
          keyword: null,
          sendType: null,
          selfDefine: null,
          sendStatus: null,
          status: null
        },
        dataList: [],
        pageIndex: 1,
        pageSize: 10,
        totalPage: 0,
        dataListLoading: false,
        dataListSelections: '',
        fullscreenLoading: false
      }
    },
    activated () {
      this.init()
    },
    methods: {
      init () {
        this.$refs['dataForm']?.resetFields()
        this.dataForm.status = null
        this.dataForm.sendId = null
        var sendCode = this.$route.params.sendCode
        var sendId = this.$route.params.sendId
        this.dataForm.sendType = (this.siteUse === true ? null : 'mpt_sms')
        this.dataForm.selfDefine = (this.tmpUse === true ? null : true)
        if (sendCode && sendCode !== '') {
          this.dataForm.keyword = sendCode
        }
        if (sendId && sendId !== '') {
          this.dataForm.sendId = sendId
        }
        this.pageIndex = 1
        this.getDataList()
      },
      // 重置
      resetForm () {
        this.$refs['dataForm']?.resetFields()
        this.dataForm.status = null
        this.dataForm.sendId = null
        this.getDataList()
      },
      // 获取数据列表
      getDataList () {
        this.dataListLoading = true
        this.$http({
          url: this.$http.adornUrl('/admin/msg_record/pages'),
          method: 'post',
          data: this.$http.adornData({
            'currentPage': this.pageIndex,
            'pageSize': this.pageSize,
            'sendId': this.dataForm.sendId,
            'keyword': this.dataForm.keyword,
            'sendType': this.dataForm.sendType,
            'status': this.dataForm.status,
            'sendStatus': this.dataForm.sendStatus,
            'selfDefine': this.dataForm.selfDefine
          })
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.dataList = data.obj.records
            this.totalPage = data.obj.total
          } else {
            this.dataList = []
            this.totalPage = 0
          }
          this.dataListLoading = false
        })
      },
      // 每页数
      sizeChangeHandle (val) {
        this.pageSize = val
        this.pageIndex = 1
        this.getDataList()
      },
      // 当前页
      currentChangeHandle (val) {
        this.pageIndex = val
        this.getDataList()
      },
      query () {
        this.pageIndex = 1
        this.dataForm.sendId = null
        this.getDataList()
      },
      // 多选
      selectionChangeHandle (val) {
        this.dataListSelections = val
      },
      // 删除
      deleteHandle (id) {
        var ids = id ? [id] : this.dataListSelections.map(item => {
          return item.id
        })
        this.$confirm(`确定进行${id ? '删除' : '批量删除'}操作?`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.$http({
            url: this.$http.adornUrl('/admin/msg_record/removeByIds'),
            method: 'get',
            params: this.$http.adornParams({
              'ids': ids.join(',')
            })
          }).then(({data}) => {
            if (data && data.code === 0) {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 1500,
                onClose: () => {
                  this.getDataList()
                }
              })
            } else {
              this.$message.error(data.msg)
            }
          })
        }).catch(() => {})
      },
      // 批量已读
      readBatch () {
        var ids = this.dataListSelections.map(item => {
          return item.id
        })
        this.$confirm(`确定进行批量阅读操作?`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.$http({
            url: this.$http.adornUrl('/admin/msg_record/readBatch'),
            method: 'get',
            params: this.$http.adornParams({
              'ids': ids.join(',')
            })
          }).then(({data}) => {
            if (data && data.code === 0) {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 1500,
                onClose: () => {
                  this.getDataList()
                }
              })
            } else {
              this.$message.error(data.msg)
            }
          })
        }).catch(() => {})
      },
      // 导出
      exportHandle () {
        this.fullscreenLoading = true
        this.$http({
          url: this.$http.adornUrl(this.tmpUse === true && this.siteUse === true ? '/admin/msg_record/export_all' : '/admin/msg_record/export'),
          method: 'post',
          responseType: 'arraybuffer',
          data: this.$http.adornData({
            'keyword': this.dataForm.keyword,
            'sendType': this.dataForm.sendType,
            'status': this.dataForm.status,
            'sendStatus': this.dataForm.sendStatus,
            'selfDefine': this.dataForm.selfDefine
          })
        }).then(({data}) => {
          if (data.code && data.code !== 0) {
            this.$message.error('导出失败')
            this.fullscreenLoading = false
          } else {
            this.fullscreenLoading = false
            let blob = new Blob([data], {type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8'})
            let objectUrl = URL.createObjectURL(blob)
            let a = document.createElement('a')
            // window.location.href = objectUrl
            a.href = objectUrl
            a.download = '消息记录.xlsx'
            a.click()
            URL.revokeObjectURL(objectUrl)
            this.$message({
              type: 'success',
              message: '导出数据成功'
            })
          }
        })
      }
    }
  }
</script>
