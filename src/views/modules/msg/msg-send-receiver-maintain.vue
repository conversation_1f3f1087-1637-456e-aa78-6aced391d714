<template>
  <el-dialog
    title="接收者维护"
    :close-on-click-modal="false"
    width="70%"
    :visible.sync="visible"
    @closed="closeDialog">
    <el-form :model="dataForm" ref="dataForm" label-width="120px">
      <el-row :gutter="6">
        <el-col :span="3">
          <el-form-item label="接收对象">
            <el-button type="primary" @click="templateDownload()" size="mini">Excel模版下载</el-button>
          </el-form-item>
        </el-col>
        <el-col :span="3">
          <el-form-item>
            <el-upload
                ref="upload"
                name="excel"
                :action="this.$http.adornUrl(`/admin/msg_send/receiverImport`)"
                :headers="myHeaders"
                :data="{ siteUse: this.siteUse, sendId: this.dataForm.sendId }"
                :on-success="successHandle"
                :on-change="changHandle"
                accept="xlsx"
                :limit="1"
                :show-file-list="false"
                :on-exceed="handleExceed"
                :before-upload="function (file){return docBeforeUpload(file)}"
                :file-list="fileList">
              <el-button type="primary" size="mini">Excel上传</el-button>
            </el-upload>
          </el-form-item>
        </el-col>
        <el-col :span="14">
          <el-form-item style="color: red">
            *只能上传xls或xlsx文件，大小10M以内，请对接收对象的手机号去重
          </el-form-item>
        </el-col>
        <el-col :span="4">
          <el-form-item label-width="0px">
            <a :href="$http.adornAttachmentUrl(dataForm.receiverExtensionFile.path)" v-if="dataForm.receiverExtensionFile" style="cursor: pointer; margin-right: 5px">{{dataForm.receiverExtensionFile.fileName}}</a>
            <i v-if="dataForm.receiverExcelFile" id="excel_remove" class="el-icon-delete" style="cursor: pointer" @click="() => {fileList = []; dataForm.receiverExtensionFile = null; dataForm.receivers = null; sendPersonForm.sendPersonList = []}"></i>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="6">
        <el-col :offset="2" :span="20">
          <el-form :model="sendPersonForm" ref="sendPersonForm" :rules="sendPersonForm.sendPersonRules">
            <el-table
                id="send_person_table"
                :key="tableKey"
                class="form-table"
                :data="sendPersonForm.sendPersonList"
                border
                v-loading="sendPersonListLoading"
                style="width: 100%;">
              <el-table-column
                  type="index"
                  align="center"
                  width="100px"
                  header-align="center">
                <template
                    slot="header"
                    slot-scope="{ column, $index }">
                  <el-button class="el-icon-circle-plus-outline" circle style="font-size: 30px; border: 0px; padding: 0px"
                             @click="addOneRecord()"/>
                </template>
              </el-table-column>
              <el-table-column
                  prop="receiverMobile"
                  header-align="center"
                  align="center"
                  label="接收者手机号">
                <template slot-scope="scope">
                  <el-form-item style="margin-bottom: 0"
                                :prop="'sendPersonList.' + scope.$index + '.receiverMobile'"
                                :rules='sendPersonForm.sendPersonRules.receiverMobile' v-if="scope.row.editing && scope.row.editing === true">
                    <el-input size="small" v-model="scope.row.receiverMobile" placeholder="请输入接收者手机号" clearable @change="changeReceiverMobile(scope.$index)">
                      <el-button size="mini" v-if="sendTypeList.indexOf('mpt_site') >= 0 && siteUse === true" slot="append" icon="el-icon-search" @click="searchVolunteer(scope.$index)"></el-button>
                    </el-input>
                  </el-form-item>
                  <span v-else>{{scope.row.receiverMobile}}</span>
                </template>
              </el-table-column>
              <el-table-column
                  prop="receiverName"
                  header-align="center"
                  align="center"
                  label="接收者姓名">
                <template slot-scope="scope">
                  <el-form-item style="margin-bottom: 0"
                                :prop="'sendPersonList.' + scope.$index + '.receiverName'" v-if="scope.row.editing && scope.row.editing === true">
                    <el-input size="mini"  v-model="scope.row.receiverName" placeholder="请输入接收者姓名" clearable></el-input>
                  </el-form-item>
                  <span v-else>{{scope.row.receiverName}}</span>
                </template>
              </el-table-column>
              <el-table-column
                  v-if="sendTypeList.indexOf('mpt_site') >= 0 && siteUse === true"
                  prop="receiverVolunteerName"
                  header-align="center"
                  align="center"
                  label="接收者志愿者信息">
                <template slot-scope="scope">
                  <el-form-item style="margin-bottom: 0"
                                :prop="'sendPersonList.' + scope.$index + '.receiverVolunteerName'" v-if="scope.row.editing && scope.row.editing === true">
                    <span :style="scope.row.receiver ? {color: 'green'} : {color: 'red'}">{{scope.row.receiverVolunteerName}}</span>
                  </el-form-item>
                  <span v-else>{{scope.row.receiverVolunteerName}}</span>
                </template>
              </el-table-column>
              <el-table-column
                  fixed="right"
                  header-align="center"
                  align="center"
                  label="操作">
                <template slot-scope="scope">
                  <el-button v-if="!scope.row.editing || scope.row.editing === false" type="text" class="el-icon-edit" style="font-size: 18px; cursor: pointer" @click="editRecord(scope.$index)"></el-button>
                  <el-button v-if="scope.row.editing && scope.row.editing === true" type="text" class="el-icon-finished" style="font-size: 18px; cursor: pointer" @click="editRecord(scope.$index, scope.row)"></el-button>
                  <el-button type="text" class="el-icon-delete" style="font-size: 18px; cursor: pointer" @click="deleteRecord(scope.$index, scope.row)"></el-button>
                </template>
              </el-table-column>
            </el-table>
            <el-pagination
                @size-change="sizeChangeHandle"
                @current-change="currentChangeHandle"
                :current-page="pageIndex"
                :page-sizes="[10, 20, 50, 100]"
                :page-size="pageSize"
                :total="totalPage"
                layout="total, sizes, prev, pager, next, jumper">
            </el-pagination>
          </el-form>
        </el-col>
      </el-row>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()" :disabled="sendPersonListLoading">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
  import _ from "lodash";
  import Vue from 'vue'
  export default {
    data () {
      return {
        visible: false,
        siteUse: false,
        sendTypeList: [],
        tableKey: null,
        myHeaders: {Authorization: Vue.cookie.get('Authorization')},
        fileList: [],
        dataForm: {
          sendId: null,
          receiverExtensionFile: null
        },
        pageIndex: 1,
        pageSize: 10,
        totalPage: 0,
        sendPersonListLoading: false,
        sendPersonForm: {
          sendPersonList: [],
          sendPersonRules: {
            receiverMobile: [
              { required: true, message: '接收者手机号不能为空', trigger: 'change' }
            ]
          }
        },
        editingNum: 0
      }
    },
    methods: {
      init (sendId, sendTypeList, siteUse) {
        this.dataForm.sendId = sendId || null
        this.sendTypeList = sendTypeList
        this.siteUse = siteUse || false
        this.visible = true
        this.tableKey = Math.random()
        this.lodash = _
        this.$nextTick(() => {
          this.dataForm.receivers = null
          this.dataForm.receiverExtensionFile = null
          this.sendPersonForm.sendPersonList = []
          this.fileList = []
          this.pageIndex = 1
          this.pageSize = 10
          this.totalPage = 0
          this.getRecordList()
        })
      },
      getRecordList () {
        this.sendPersonListLoading = true
        this.$http({
          url: this.$http.adornUrl(`/admin/msg_record/pageForMaintain`),
          method: 'post',
          data: this.$http.adornData({
            'sendId': this.dataForm.sendId,
            'currentPage': this.pageIndex,
            'pageSize': this.pageSize,
          })
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.tableKey = Math.random()
            this.sendPersonForm.sendPersonList = data.obj.records
            this.sendPersonListLoading = false
            this.totalPage = data.obj.total
          }
        })
      },
      // 每页数
      sizeChangeHandle (val) {
        if (this.editingNum > 0) {
          this.$message.warning('当前页存在正在编辑的数据，请先完成对正在编辑的数据的处理！')
          return
        }
        this.pageSize = val
        this.pageIndex = 1
        this.getRecordList()
      },
      // 当前页
      currentChangeHandle (val) {
        if (this.editingNum > 0) {
          this.$message.warning('当前页存在正在编辑的数据，请先完成对正在编辑的数据的处理！')
          return
        }
        this.pageIndex = val
        this.getRecordList()
      },
      addOneRecord () {
        if (this.editingNum > 0) {
          this.$message.warning('当前页存在正在编辑的数据，请先完成对正在编辑的数据的处理！')
          return
        }
        let data = {
          receiverMobile: null,
          receiverName: null,
          receiver: null,
          receiverVolunteerName: null,
          sendId: this.dataForm.sendId,
          editing: true
        }
        this.sendPersonForm.sendPersonList.unshift(data)
        this.editingNum ++
      },
      editRecord (index, row) {
        if (this.sendPersonForm.sendPersonList[index].editing && this.sendPersonForm.sendPersonList[index].editing === true) {
          this.$refs.sendPersonForm.validate((valid) => {
            if (valid) {
              this.$http({
                url: this.$http.adornUrl(`/admin/msg_record/updateRecord`),
                method: 'post',
                data: this.$http.adornData(row)
              }).then(({data}) => {
                if (data && data.code === 0) {
                  this.$message.success(row.id ? '修改成功！' : '添加成功！')
                  this.editingNum --
                  this.$set(this.sendPersonForm.sendPersonList[index], 'editing', false)
                  this.getRecordList()
                } else {
                  this.$message.error(data.msg)
                }
              })
            }
          })
        } else {
          this.editingNum ++
          this.$set(this.sendPersonForm.sendPersonList[index], 'editing', true)
        }
      },
      deleteRecord (index, row) {
        if (!row.id) {
          this.editingNum --
          this.sendPersonForm.sendPersonList.splice(index, 1)
          return
        }
        let editingFlag = row.editing && row.editing === true
        this.$confirm(`确定删除该接收者?`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.$http({
            url: this.$http.adornUrl(`/admin/msg_record/removeRecord`),
            method: 'get',
            params: this.$http.adornParams({
              id: row.id
            })
          }).then(({data}) => {
            if (data && data.code === 0) {
              this.$message.success('删除成功！')
              if (editingFlag) {
                this.editingNum --
              }
              this.getRecordList()
            } else {
              this.$message.error(data.msg)
            }
          })
        })

      },
      // 下载模板文件
      templateDownload () {
        this.$http({
          url: this.$http.adornUrl('/admin/msg_send/receiverExcelTemplate'),
          method: 'get',
          responseType: 'arraybuffer',
          params: this.$http.adornParams()
        }).then(({data}) => {
          if (data.code && data.code !== 0) {
            this.$message.error('下载失败')
          } else {
            let blob = new Blob([data], {type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8'})
            let objectUrl = URL.createObjectURL(blob)
            let a = document.createElement('a')
            // window.location.href = objectUrl
            a.href = objectUrl
            a.download = '消息接收者导入模版.xlsx'
            a.click()
            URL.revokeObjectURL(objectUrl)
            this.$message({
              type: 'success',
              message: '下载模板成功'
            })
          }
        })
      },
      // 上传成功
      successHandle (response) {
        if (response && response.code === 0) {
          this.dataForm.receiverExtensionFile = response.obj.ossObj
        } else {
          this.$message.error(response.msg)
        }
        this.sendPersonListLoading = false
        // this.fileList = []
      },
      changHandle (file, fileList) {
        let FileExt = file.name.replace(/.+\./, '')
        const isLt10M = file.size / 1024 / 1024 < 10
        if (['xlsx', 'xls'].indexOf(FileExt.toLowerCase()) === -1) {
          this.$message({
            type: '上传失败',
            message: '请上传后缀名为xlsx或xls的文件！'
          })
          this.fileList = []
          return
        }
        if (!isLt10M) {
          this.$message.error('上传文件大小不能超过 10M!')
          this.fileList = []
          return
        }
        this.fileList = fileList
      },
      handleExceed () {
        this.$message.error('只能选择一个文件，如需替换请删除已选文件后重试')
      },
      docBeforeUpload (file) {
        let FileExt = file.name.replace(/.+\./, '')
        if (['xlsx', 'xls'].indexOf(FileExt.toLowerCase()) === -1) {
          this.$message({
            type: '上传失败',
            message: '请上传后缀名为xlsx, xls的附件！'
          })
          return false
        }
        const isLt10M = file.size / 1024 / 1024 < 10
        if (['xlsx', 'xls'].indexOf(FileExt.toLowerCase()) !== -1 && !isLt10M) {
          this.$message.error('附件大小不能超过 10M!')
          return false
        }
        this.sendPersonListLoading = true
      },

      changeReceiverMobile(index) {
        this.$nextTick(() => {
          this.$refs.sendPersonForm.validateField('sendPersonList.' + index + '.receiverMobile')
        });
        this.$set(this.sendPersonForm.sendPersonList[index], 'receiver',  null)
        this.$set(this.sendPersonForm.sendPersonList[index], 'receiver',  null)
        this.$set(this.sendPersonForm.sendPersonList[index], 'receiverVolunteerName', null)
      },
      searchVolunteer (index) {
        const mobile = this.sendPersonForm.sendPersonList[index].receiverMobile
        if (mobile && mobile !== '') {
          this.$http({
            url: this.$http.adornUrl(`/admin/zyz/volunteer/getVolunteerByPhone`),
            method: 'get',
            params: this.$http.adornParams({ phone: mobile })
          }).then(({data}) => {
            if (data && data.code === 0) {
              this.$set(this.sendPersonForm.sendPersonList[index], 'receiver',  data.obj.id)
              this.$set(this.sendPersonForm.sendPersonList[index], 'receiverVolunteerName', data.obj.name)
              if (!this.sendPersonForm.sendPersonList[index].receiverName || this.sendPersonForm.sendPersonList[index].receiverName === '') {
                this.$set(this.sendPersonForm.sendPersonList[index], 'receiverName', data.obj.name)
              }
            } else {
              this.$set(this.sendPersonForm.sendPersonList[index], 'receiver',  null)
              this.$set(this.sendPersonForm.sendPersonList[index], 'receiverVolunteerName', data.msg)
            }
          })
        } else {
          this.$message.warning('请先输入手机号！')
        }
      },
      dataFormSubmit() {
        if (this.editingNum > 0) {
          this.$message.warning('存在正在编辑的数据，请先完成对正在编辑的数据的处理！')
          return
        }
        this.$http({
          url: this.$http.adornUrl(`/admin/msg_send/receiverMaintainSubmit`),
          method: 'get',
          params: this.$http.adornParams({
            filePath: this.dataForm.receiverExtensionFile ? this.$http.adornAttachmentUrl(this.dataForm.receiverExtensionFile.path) : null,
            sendId: this.dataForm.sendId
          })
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.$message({
              message: '维护成功, 先前如有发送失败数据，现已更新为待发送！',
              type: 'success',
              duration: 1500,
              onClose: () => {
                this.visible = false
                this.$emit('refreshDataList')
              }
            })
          } else {
            this.$message.error(data.msg)
          }
        })
      },
      closeDialog () {
        if (this.editingNum > 0) {
          this.$message.warning('存在正在编辑的数据，请先完成对正在编辑的数据的处理！')
          return
        }
        this.visible = false
        this.$emit('refreshDataList')
      }
    }
  }
</script>

<style lang="scss" scoped>
#send_person_table ::v-deep .el-form-item__error {
  position: static;
  text-align: left;
}
</style>
