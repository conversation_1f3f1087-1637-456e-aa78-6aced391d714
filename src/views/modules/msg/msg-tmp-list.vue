<template>
  <div class="mod-dict">
    <el-form :inline="true" :model="dataForm" ref="dataForm">
      <el-form-item label="模糊搜索" prop="keyword">
        <el-input style="width: 300px" v-model="dataForm.keyword" placeholder="编码/标题/内容" clearable></el-input>
      </el-form-item>
      <el-form-item label="模版状态" prop="status">
        <el-select v-model="dataForm.status" placeholder="请选择" clearable>
          <el-option
            v-for="item in [{label: '启用', value: true}, {label: '禁用', value: false}]"
            :key="item.value"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="warning" icon="el-icon-search" @click="currentChangeHandle(1)">查询</el-button>
        <el-button icon="el-icon-refresh-left" @click="resetForm()">重置</el-button>
      </el-form-item>
    </el-form>
    <div class="f-s-c" style="padding: 15px 0;display: flex; justify-content: flex-end">
      <div>
        <el-button icon="el-icon-plus" type="primary" @click="addOrUpdateHandle()">新增</el-button>
        <el-button icon="el-icon-delete" type="danger" @click="deleteHandle()" :disabled="dataListSelections.length <= 0">批量删除</el-button>
      </div>
    </div>
    <el-table
      :data="dataList"
      border
      v-loading="dataListLoading"
      @selection-change="selectionChangeHandle"
      style="width: 100%;">
      <el-table-column
        type="selection"
        header-align="center"
        align="center"
        width="50">
      </el-table-column>
      <el-table-column
        prop="code"
        header-align="center"
        align="center"
        width="250"
        label="编码">
      </el-table-column>
      <el-table-column
        prop="title"
        header-align="center"
        align="center"
        width="400"
        label="标题">
        <template slot-scope="scope">
          <a @click="$router.push({ name:'msg-msg-send-list', params: { tmpId: scope.row.id } })" target="_blank" class="buttonText" style="cursor: pointer">{{ scope.row.title }}</a>
        </template>
      </el-table-column>
      <el-table-column
        prop="tmp"
        header-align="center"
        align="center"
        :show-overflow-tooltip="true"
        label="模板">
      </el-table-column>
      <el-table-column
        prop="status"
        header-align="center"
        align="center"
        width="150"
        label="状态">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.status === false" size="small" type="danger">禁用</el-tag>
          <el-tag v-else size="small">启用</el-tag>
        </template>
      </el-table-column>
      <el-table-column
        fixed="right"
        header-align="center"
        align="center"
        width="150"
        label="操作">
        <template slot-scope="scope">
          <el-button type="text" size="small" @click="deleteHandle(scope.row.id)">删除</el-button>
          <el-button type="text" size="small" @click="addOrUpdateHandle(scope.row.id)">修改</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      @size-change="sizeChangeHandle"
      @current-change="currentChangeHandle"
      :current-page="pageIndex"
      :page-sizes="[10, 20, 50, 100]"
      :page-size="pageSize"
      :total="totalPage"
      layout="total, sizes, prev, pager, next, jumper">
    </el-pagination>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"></add-or-update>
  </div>
</template>

<script>
  import AddOrUpdate from './msg-tmp-add-or-update'
  export default {
    data () {
      return {
        dataForm: {
          keyword: null,
          status: null
        },
        dataList: [],
        pageIndex: 1,
        pageSize: 10,
        totalPage: 0,
        dataListLoading: false,
        dataListSelections: '',
        addOrUpdateVisible: false
      }
    },
    components: {
      AddOrUpdate
    },
    activated () {
      this.getDataList()
    },
    methods: {
      // 重置
      resetForm () {
        this.$refs['dataForm']?.resetFields()
        this.getDataList()
      },
      // 获取数据列表
      getDataList () {
        this.dataListLoading = true
        this.$http({
          url: this.$http.adornUrl('/admin/msg_tmp/pages'),
          method: 'post',
          data: this.$http.adornData({
            'currentPage': this.pageIndex,
            'pageSize': this.pageSize,
            'keyword': this.dataForm.keyword,
            'status': this.dataForm.status
          })
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.dataList = data.obj.records
            this.totalPage = data.obj.total
          } else {
            this.dataList = []
            this.totalPage = 0
          }
          this.dataListLoading = false
        })
      },
      // 每页数
      sizeChangeHandle (val) {
        this.pageSize = val
        this.pageIndex = 1
        this.getDataList()
      },
      // 当前页
      currentChangeHandle (val) {
        this.pageIndex = val
        this.getDataList()
      },
      // 多选
      selectionChangeHandle (val) {
        this.dataListSelections = val
      },
      // 新增 / 修改
      addOrUpdateHandle (id, detail) {
        this.addOrUpdateVisible = true
        this.$nextTick(() => {
          this.$refs.addOrUpdate.init(id, detail)
        })
      },
      // 删除
      deleteHandle (id) {
        var ids = id ? [id] : this.dataListSelections.map(item => {
          return item.id
        })
        this.$confirm(`确定进行${id ? '删除' : '批量删除'}操作?`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.$http({
            url: this.$http.adornUrl('/admin/msg_tmp/removeByIds'),
            method: 'get',
            params: this.$http.adornParams({
              'ids': ids.join(',')
            })
          }).then(({data}) => {
            if (data && data.code === 0) {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 1500,
                onClose: () => {
                  this.getDataList()
                }
              })
            } else {
              this.$message.error(data.msg)
            }
          })
        }).catch(() => {})
      }
    }
  }
</script>
