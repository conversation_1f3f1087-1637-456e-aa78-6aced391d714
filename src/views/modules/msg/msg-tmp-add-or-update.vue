<template>
  <el-dialog
    :title="!dataForm.id ? '新增' : '修改'"
    :close-on-click-modal="false"
    :visible.sync="visible">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" label-width="80px">
      <el-row :gutter="6">
        <el-col :span="24">
          <el-form-item label="编码" prop="code">
            <el-input v-model="dataForm.code" placeholder="编码" maxlength="50" show-word-limit></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="6">
        <el-col :span="24">
          <el-form-item label="标题" prop="title">
            <el-input v-model="dataForm.title" placeholder="标题" maxlength="100" show-word-limit></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="跳转路径" prop="pageUrl">
            <el-input v-model="dataForm.pageUrl" placeholder="跳转路径"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="6">
        <el-col :span="24">
          <el-form-item label="模板" prop="tmp">
            <el-input id="contentInput" type="textarea" v-model="dataForm.tmp" placeholder="模板" maxlength="300" show-word-limit :rows="3"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row style="padding-bottom: 20px">
        <el-form-item>
          <el-button type="primary" @click="addMark('%rn')">添加接收者占位符</el-button>
          <el-button type="primary" @click="addMark('%pu')">添加跳转链接占位符</el-button>
          <span>(在消息模版光标处添加)</span>
        </el-form-item>
      </el-row>
      <el-row :gutter="6">
        <el-col :span="24">
          <el-form-item label="是否启用" prop="status">
            <el-radio-group v-model="dataForm.status">
              <el-radio :label="true">是</el-radio>
              <el-radio :label="false">否</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()" :disabled="dataFormLoading">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
  export default {
    data () {
      var validateCode = (rule, value, callback) => {
        this.$http({
          url: this.$http.adornUrl('/admin/msg_tmp/checkCode'),
          method: 'get',
          params: this.$http.adornParams({
            'id': this.dataForm.id,
            'code': this.dataForm.code
          })
        }).then(({data}) => {
          if (!data || data.code !== 0) {
            callback(new Error('模版code校验失败！'))
          } else {
            if (data.obj === false) {
              callback(new Error('模版code已存在！'))
            } else {
              callback()
            }
          }
        })
      }
      return {
        visible: false,
        dataFormLoading: false,
        dataForm: {
          id: null,
          version: null,
          code: null,
          title: null,
          pageUrl: null,
          tmp: null,
          status: true
        },
        types: [],
        dataRule: {
          code: [
            {required: true, message: '编码不能为空', trigger: 'change'},
            {validator: validateCode, trigger: 'change'}
          ],
          title: [
            {required: true, message: '标题不能为空', trigger: 'change'}
          ],
          tmp: [
            {required: true, message: '模板不能为空', trigger: 'change'}
          ],
          status: [
            {required: true, message: '状态不能为空', trigger: 'change'}
          ]
        }
      }
    },
    methods: {
      init (id) {
        this.dataForm.id = id || null
        this.visible = true
        this.$nextTick(() => {
          this.$refs['dataForm']?.resetFields()
          if (this.dataForm.id) {
            this.$http({
              url: this.$http.adornUrl(`/admin/msg_tmp`),
              method: 'get',
              params: this.$http.adornParams({'id': this.dataForm.id})
            }).then(({data}) => {
              if (data && data.code === 0) {
                this.dataForm = data.obj
              }
            })
          }
        })
      },
      addMark (mark) {
        var elInput = document.getElementById('contentInput') // 根据id选择器选中对象
        var startPos = elInput.selectionStart// input 第0个字符到选中的字符
        var endPos = elInput.selectionEnd// 选中的字符到最后的字符
        if (startPos === undefined || endPos === undefined) return
        var txt = elInput.value
        // 将表情添加到选中的光标位置
        var result = txt.substring(0, startPos) + mark + txt.substring(endPos)
        elInput.value = result// 赋值给input的value
        // 重新定义光标位置
        elInput.focus()
        elInput.selectionStart = startPos + mark.length
        elInput.selectionEnd = startPos + mark.length
        this.dataForm.tmp = result
      },
      // 表单提交
      dataFormSubmit () {
        this.$refs['dataForm'].validate((valid) => {
          if (valid) {
            this.dataFormLoading = true
            this.$http({
              url: this.$http.adornUrl(`/admin/msg_tmp/${!this.dataForm.id ? 'save' : 'update'}`),
              method: 'post',
              data: this.$http.adornData({
                'id': this.dataForm.id,
                'version': this.dataForm.version,
                'code': this.dataForm.code,
                'title': this.dataForm.title,
                'tmp': this.dataForm.tmp,
                'pageUrl': this.dataForm.pageUrl,
                'status': this.dataForm.status
              })
            }).then(({data}) => {
              if (data && data.code === 0) {
                this.$message({
                  message: '操作成功',
                  type: 'success',
                  duration: 1500,
                  onClose: () => {
                    this.dataFormLoading = false
                    this.visible = false
                    this.$emit('refreshDataList')
                  }
                })
              } else {
                this.dataFormLoading = false
                this.$message.error(data.msg)
              }
            })
          }
        })
      }
    }
  }
</script>
