<template>
  <div class="mod-config">
    <el-form :inline="true" :model="dataForm" @keyup.enter.native="getDataList()" ref="dataForm">
      <el-form-item label="模糊搜索" prop="keyword">
        <el-input style="width: 300px" v-model="dataForm.keyword" :placeholder="tmpUse === true ? '模版编码/标题/发送内容' : '发送内容'" clearable></el-input>
      </el-form-item>
      <el-form-item label="推送状态" prop="status">
        <el-select v-model="dataForm.status" placeholder="请选择" clearable>
          <el-option
            v-for="item in [ {label: '未推送', value: 0}, {label: '推送中', value: 1}, {label: '推送完成', value: 2}]"
            :key="item.value"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="推送方式" prop="sendType" v-if="siteUse === true">
        <el-dict :code="'MSG_PUSH_TYPE'" v-model="dataForm.sendType"></el-dict>
      </el-form-item>
      <el-form-item label="消息类型" prop="selfDefine" v-if="tmpUse === true">
        <el-select v-model="dataForm.selfDefine" placeholder="消息类型" clearable>
          <el-option
            v-for="item in [{code: false, name: '模版消息'}, {code: true, name: '自定义消息'}]"
            :key="item.code"
            :label="item.name"
            :value="item.code">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="模版消息:" prop="tmpId" v-if="dataForm.selfDefine === false">
        <el-select v-model="dataForm.tmpId" filterable placeholder="模版消息" clearable>
          <el-option
            v-for="item in tmpList"
            :key="item.id"
            :label="item.title"
            :value="item.id">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="warning" icon="el-icon-search" @click="currentChangeHandle(1)">查询</el-button>
        <el-button icon="el-icon-refresh-left" @click="resetForm()">重置</el-button>
      </el-form-item>
    </el-form>
    <div class="f-s-c" style="padding: 15px 0;display: flex; justify-content: space-between">
      <div style="background-color: #efefef; border-radius: 5px">
        <div style="font-size: 18px; font-weight:bolder; padding: 10px">
          <el-button title="刷新" style="cursor: pointer; font-size: 18px; font-weight:bolder; border: 0px; padding: 0px 10px 0px 0px; background-color: #efefef" class="el-icon-refresh" @click="getSmsPlatformInfoData"/>
          短信充值信息：
          <span v-if="overageDataLoading === true" style="font-size: 14px; padding-left: 20px">数据获取中，请稍候……</span>
          <span v-if="overageDataLoading === false" style="font-size: 14px; padding-left: 20px">
            <span v-if="overageData">
              <span style="padding-right: 30px">
                {{'支付方式：' + overageData.payinfo}}
              </span>
              <span style="padding-right: 30px">
                {{'总点数：' + overageData.sendTotal}}
              </span>
              <span style="padding-right: 30px">
                {{'余额：' + overageData.overage}}
              </span>
            </span>
            <span v-else>
              {{overageDataGetFailMsg || ''}}
            </span>
          </span>
        </div>
      </div>
      <div>
        <el-button icon="el-icon-plus" type="primary" @click="addOrUpdateHandle()">新增</el-button>
        <el-button icon="el-icon-delete" type="danger" @click="deleteHandle()" :disabled="dataListSelections.length <= 0">批量删除</el-button>
      </div>
    </div>
    <el-table
      :data="dataList"
      border
      v-loading="dataListLoading"
      @selection-change="selectionChangeHandle"
      style="width: 100%;">
      <el-table-column
        type="selection"
        header-align="center"
        align="center"
        width="50">
      </el-table-column>
      <el-table-column
        prop="sendCode"
        header-align="center"
        align="center"
        width="280"
        label="发送批次">
        <template slot-scope="scope">
          <a @click="$router.push({ name:'msg-msg-record-list',params: { sendCode: scope.row.sendCode, sendId: scope.row.id } })" target="_blank" class="buttonText" style="cursor: pointer">{{scope.row.sendCode}}</a>
        </template>
      </el-table-column>
      <el-table-column
        v-if="tmpUse === true"
        prop="selfDefine"
        header-align="center"
        align="center"
        label="消息类型">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.selfDefine === true" size="small">自定义消息</el-tag>
          <el-tag v-if="scope.row.selfDefine === false" size="small" type="success">模版消息</el-tag>
        </template>
      </el-table-column>
      <el-table-column
        prop="title"
        header-align="center"
        align="center"
        :show-overflow-tooltip="true"
        label="标题">
      </el-table-column>
      <el-table-column
        prop="msgContent"
        header-align="center"
        align="center"
        :show-overflow-tooltip="true"
        label="推送内容">
      </el-table-column>
      <el-table-column
        prop="sendTypeText"
        header-align="center"
        align="center"
        width="200"
        v-if="siteUse === true"
        label="推送类型">
      </el-table-column>
      <el-table-column
        prop="total"
        header-align="center"
        align="center"
        width="100"
        label="推送总数">
      </el-table-column>
      <el-table-column
        prop="success"
        header-align="center"
        align="center"
        width="180"
        label="推送情况(成功/失败)">
        <template slot-scope="scope">
          <el-button v-if="scope.row.status === 1" size="small" type="text" @click="sendDetail(scope.row.id, scope.row.status)">查看推送进度</el-button>
          <div v-else><span style="color: green"> {{scope.row.success}} </span><span>/</span><span style="color: red"> {{scope.row.fail}} </span></div>
<!--          <el-tag v-else size="small" type="success">{{scope.row.success + '/' + scope.row.fail}}</el-tag>-->
        </template>
      </el-table-column>
      <el-table-column
        prop="status"
        header-align="center"
        align="center"
        width="150"
        label="推送状态">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.status === 0" size="small" type="danger">未推送</el-tag>
          <el-tag v-else-if="scope.row.status === 1" size="small">推送中……</el-tag>
          <el-tag v-else-if="scope.row.status === 2" size="small">推送完成</el-tag>
        </template>
      </el-table-column>
      <el-table-column
          prop="sendTime"
          header-align="center"
          align="center"
          width="200"
          label="推送时间">
      </el-table-column>
        <el-table-column
        fixed="right"
        header-align="center"
        align="center"
        width="250"
        label="操作">
        <template slot-scope="scope">
          <el-button v-if="scope.row.status !== 1" type="text" size="small" @click="addOrUpdateHandle(scope.row.id)">修改</el-button>
          <el-button v-if="scope.row.status !== 1" type="text" size="small" @click="receiverMaintainHandle(scope.row.id, scope.row.sendType)">接收者维护</el-button>
          <el-button v-if="scope.row.status === 0" type="text" size="small" @click="deleteHandle(scope.row.id)">删除</el-button>
          <el-button v-if="scope.row.status === 0 && scope.row.success === 0" type="text" size="small" @click="sendMessage(scope.$index, scope.row.id)">推送</el-button>
          <el-button v-if="scope.row.status === 2 && scope.row.fail === 0 && scope.row.success !== scope.row.total" type="text" size="small" @click="sendMessage(scope.$index, scope.row.id)">重新推送</el-button>
          <el-button v-if="scope.row.status === 2 && scope.row.fail !== 0" type="text" size="small" @click="sendMessage(scope.$index, scope.row.id)">失败重推</el-button>
          <el-button v-if="scope.row.status === 2" type="text" size="small" @click="sendDetail(scope.row.id, scope.row.status)">推送结果</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      @size-change="sizeChangeHandle"
      @current-change="currentChangeHandle"
      :current-page="pageIndex"
      :page-sizes="[10, 20, 50, 100]"
      :page-size="pageSize"
      :total="totalPage"
      layout="total, sizes, prev, pager, next, jumper">
    </el-pagination>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"></add-or-update>
    <!-- 弹窗, 接收者维护 -->
    <receiver-maintain v-if="receiverMaintainVisible" ref="receiverMaintain" @refreshDataList="getDataList"></receiver-maintain>
    <!-- 弹窗, 发送情况-->
    <detail v-if="detailVisible" ref="detail" @refreshDataList="() => {
      getDataList()
      detailVisible = false}"></detail>
  </div>
</template>

<script>
  import AddOrUpdate from './msg-send-add-or-update'
  import ReceiverMaintain from './msg-send-receiver-maintain'
  import Detail from './msg-send-detail'
  import router from "@/router";
  export default {
    data () {
      return {
        tmpUse: false,
        siteUse: false,
        dataForm: {
          keyword: null,
          tmpId: null,
          sendType: null,
          status: null,
          selfDefine: null
        },
        dataList: [],
        tmpList: [],
        pageIndex: 1,
        pageSize: 10,
        totalPage: 0,
        dataListLoading: false,
        dataListSelections: [],
        addOrUpdateVisible: false,
        receiverMaintainVisible: false,
        detailVisible: false,
        overageData: null,
        overageDataGetFailMsg: null,
        overageDataLoading: false
      }
    },
    components: {
      AddOrUpdate,
      ReceiverMaintain,
      Detail
    },
    activated () {
      this.init()
    },
    methods: {
      init () {
        this.$refs['dataForm']?.resetFields()
        this.dataForm.tmpId = null
        var tmpId = this.$route.params.tmpId
        this.getTmpList()
        this.getSmsPlatformInfoData()
        this.dataForm.sendType = (this.siteUse === true ? null : 'mpt_sms')
        // this.dataForm.selfDefine = (this.tmpUse === true ? null : true)
        if (tmpId && tmpId !== '') {
          this.dataForm.tmpId = tmpId
          this.dataForm.selfDefine = false
        }
        this.pageIndex = 1
        this.getDataList()
      },
      // 重置
      resetForm () {
        this.$refs['dataForm']?.resetFields()
        this.dataForm.tmpId = null
        this.getDataList()
      },
      // 获取数据列表
      getDataList () {
        this.dataListLoading = true
        this.$http({
          url: this.$http.adornUrl('/admin/msg_send/pageForMsgSend'),
          method: 'post',
          data: this.$http.adornData({
            'currentPage': this.pageIndex,
            'pageSize': this.pageSize,
            'keyword': this.dataForm.keyword,
            'sendType': this.dataForm.sendType,
            'status': this.dataForm.status,
            'tmpId': this.dataForm.tmpId,
            'selfDefine': this.dataForm.selfDefine
          })
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.dataList = data.obj.records
            this.totalPage = data.obj.total
          } else {
            this.dataList = []
            this.totalPage = 0
          }
          this.dataListLoading = false
        })
      },
      getTmpList () {
        this.$http({
          url: this.$http.adornUrl(`/admin/msg_tmp/all`),
          method: 'get'
        }).then(({data}) => {
          if (data) {
            this.tmpList = data
          } else {
            this.tmpList = []
          }
        })
      },
      // 每页数
      sizeChangeHandle (val) {
        this.pageSize = val
        this.pageIndex = 1
        this.getDataList()
      },
      // 当前页
      currentChangeHandle (val) {
        this.pageIndex = val
        this.getDataList()
      },
      // 多选
      selectionChangeHandle (val) {
        this.dataListSelections = val
      },
      // 新增 / 修改
      addOrUpdateHandle (id) {
        this.addOrUpdateVisible = true
        this.$nextTick(() => {
          this.$refs.addOrUpdate.init(id, this.tmpUse, this.siteUse)
        })
      },
      // 接收者维护
      receiverMaintainHandle (id, sendType) {
        this.receiverMaintainVisible = true
        this.$nextTick(() => {
          console.log(id, sendType)
          this.$refs.receiverMaintain.init(id, sendType.split(','), this.siteUse)
        })
      },
      sendDetail (id, status) {
        this.detailVisible = true
        this.$nextTick(() => {
          this.$refs.detail.init(id, status, this.tmpUse, this.siteUse)
        })
      },
      // 删除
      deleteHandle (id) {
        var ids = id ? [id] : this.dataListSelections.map(item => {
          return item.id
        })
        this.$confirm(`确定进行${id ? '删除' : '批量删除'}操作?`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.$http({
            url: this.$http.adornUrl('/admin/msg_send/removeByIds'),
            method: 'get',
            params: this.$http.adornParams({
              'ids': ids.join(',')
            })
          }).then(({data}) => {
            if (data && data.code === 0) {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 1500,
                onClose: () => {
                  this.getDataList()
                }
              })
            } else {
              this.$message.error(data.msg)
            }
          })
        })
      },
      sendMessage (index, id) {
        this.$confirm(`消息发送操作确定后将无法撤回，确定发送消息?`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.dataListLoading = true
          this.$http({
            url: this.$http.adornUrl('/admin/msg_send/sendMessage'),
            method: 'post',
            params: this.$http.adornParams({
              'sendId': id
            })
          }).then(({data}) => {
            if (data && data.code === 0) {
              this.$message({
                message: '发送成功',
                type: 'success',
                duration: 1000,
                onClose: () => {
                  this.getDataList()
                }
              })
            } else {
              this.$message.error(data.msg)
              this.dataListLoading = false
            }
          })
        })
      },
      getSmsPlatformInfoData () {
        this.overageDataLoading = true
        this.$http({
          url: this.$http.adornUrl(`/admin/msg_send/getSmsPlatformInfoData`),
          method: 'get'
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.overageData = data.obj
            this.overageDataLoading = false
          } else {
            this.overageData = null
            this.overageDataGetFailMsg = data.msg
            this.overageDataLoading = false
          }
        }).catch((e) => {
          this.overageData = null
          this.overageDataGetFailMsg = e
          this.overageDataLoading = false
        })
      },
      exportSendDetail (id) {
        this.$http({
          url: this.$http.adornUrl('/admin/msg_send/exportSendDetail'),
          method: 'post',
          params: this.$http.adornParams({
            'sendId': id
          }),
          responseType: 'arraybuffer'
        }).then(({data}) => {
          if (data.code && data.code !== 0) {
            this.$message.error('导出失败')
          } else {
            let blob = new Blob([data], {type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8'})
            let objectUrl = URL.createObjectURL(blob)
            let a = document.createElement('a')
            a.href = objectUrl
            a.download = '消息发送情况.xlsx'
            a.click()
            URL.revokeObjectURL(objectUrl)
            this.$message({type: 'success', message: '下载成功'})
          }
        })
      }
    }
  }
</script>
