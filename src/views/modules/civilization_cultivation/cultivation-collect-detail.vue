<template>
  <el-dialog class="common-dialog" title="详情" :visible.sync="visible"
             :close-on-click-modal="false" :modal-append-to-body="false" width="60%">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" label-width="90px">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="类型" prop="type">
            <el-dict disabled :code="'CIVILIZATION_CULTIVATION_TYPE'" v-model="dataForm.type" :clearable="false"/>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="姓名" prop="name">
            <el-input disabled v-model="dataForm.name"/>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="联系电话" prop="phone">
            <el-input disabled v-model="dataForm.phone"/>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="年份" prop="year">
            <el-date-picker
                disabled
                style="width: 100%"
                v-model="dataForm.year"
                type="year"
                value-format="yyyy">
            </el-date-picker>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12" v-if="dataForm.type === 'CCT_XJDX'">
          <el-form-item label="推荐组织" prop="recommendOrg">
            <el-input disabled v-model="dataForm.recommendOrg"/>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="所属社区" prop="belongCommunityCode">
            <el-cascader
                style="width: 100%"
                disabled
                :placeholder="''"
                v-model="dataForm.belongCommunityCode"
                :options="orgList"
                :show-all-levels="false"
                :props="{
                  value: 'code',
                  label: 'name',
                  emitPath: false
                }"/>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20" v-if="dataForm.type !== 'CCT_YQHR'">
        <el-col :span="24">
          <el-form-item class="address-item" label="地址" prop="address">
            <el-input disabled v-model="dataForm.address"/>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20" v-if="dataForm.type !== 'CCT_YQHR'">
        <el-col :span="12">
          <el-form-item label="经度" prop="longitude" >
            <el-input v-model="dataForm.longitude" disabled></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="纬度" prop="latitude" >
            <el-input v-model="dataForm.latitude" disabled></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20" v-if="dataForm.type && dataForm.type !== ''">
        <el-col :span="24">
          <el-form-item :label="dataForm.type === 'CCT_DDMF' ? '影响事迹' : (dataForm.type === 'CCT_SDXR' ? '事迹' : (dataForm.type === 'CCT_XJDX' ? '成就' : '事迹简要'))" prop="achievements">
            <el-input disabled type="textarea" rows="3" v-model="dataForm.achievements"/>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20" v-if="dataForm.type !== 'CCT_XJDX'">
        <el-col :span="24">
          <el-form-item label="推荐理由" prop="recommendReason">
            <el-input disabled type="textarea" rows="3" v-model="dataForm.recommendReason"/>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="图片" prop="picture">
            <el-upload
                v-if="dataForm.pictureList && dataForm.pictureList.length > 0"
                disabled
                class="avatar-uploader"
                list-type="picture-card"
                :action="this.$http.adornUrl(`/admin/oss/upload`)"
                :headers="headers"
                :data="{serverCode: uploadOptions.serverCode, media: false}"
                :on-success="(res, file, fileList) => successHandle(res, file, fileList, successCallback)"
                :on-change="changHandle"
                :on-exceed="exceedHandle"
                :before-upload="beforeUploadHandle"
                :on-remove="(file, fileList) => removeHandle(file, fileList, removeCallback)"
                :file-list="uploadOptions.fileList">
<!--              <i class="el-icon-plus"></i>-->
<!--              <div slot="tip" class="el-upload__tip" style="color: red">文件格式jpg、bmp或png，大小不超10M</div>-->
            </el-upload>
            <span v-else>暂未上传图片</span>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="上下架" prop="onShelve">
            <el-radio-group v-model="dataForm.onShelve" disabled>
              <el-radio :label="true">上架</el-radio>
              <el-radio :label="false">下架</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="排序" prop="sequence">
            <el-input-number disabled v-model="dataForm.sequence" controls-position="right" :min="0"/>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">关闭</el-button>
    </span>
  </el-dialog>
</template>

<script>
  import _ from 'lodash'
  import editMixin from '@/mixins/edit-mixins'
  import fileUploadMixin from "@/mixins/file-upload-mixins";
  export default {
    mixins: [editMixin, fileUploadMixin],
    data () {
      return {
        editOptions: {
          initUrl: '/admin/civilization_cultivation_collect'
        },
        uploadOptions: {
          fieldName: 'picture',
          maxSize: 10,
          limit: 10,
          fileList: [] // 完全重置上传选项
        },
        orgList: [],
        dataForm: {},
        initForm: {
          id: null,
          version: null,
          type: null,
          name: null,
          phone: null,
          year: null,
          recommendOrg: null,
          belongStreetCode: null,
          belongStreetName: null,
          belongCommunityCode: null,
          belongCommunityName: null,
          address: null,
          longitude: null,
          latitude: null,
          achievements: null,
          recommendReason: null,
          picture: null,
          pictureList: [],
          status: null,
          onShelve: false,
          sequence: null,
          checkAttachments: null
        },
        dataRule: {
          type: [
            { required: true, message: '文明培育类型不能为空', trigger: 'blur' }
          ],
          name: [
            { required: true, message: '姓名不能为空', trigger: 'change' }
          ],
          phone: [
            { required: true, message: '联系电话不能为空', trigger: 'change' }
          ],
          year: [
            { required: true, message: '年份不能为空', trigger: 'change' }
          ],
          belongCommunityCode: [
            { required: true, message: '所属社区不能为空', trigger: 'change' }
          ],
          onShelve: [
            { required: true, message: '是否上下架不能为空', trigger: 'change' }
          ],
          sequence: [
            { required: true, message: '排序不能为空', trigger: 'change' }
          ]
        }
      }
    },
    methods: {
      init (id) {
        this.dataForm = _.cloneDeep(this.initForm)
        this.dataForm.id = id || null
        this.dataForm.pictureList = []
        this.uploadOptions.fileList = [] // 清空上传组件的文件列表
        this.visible = true
        this.getOrg()
        this.$nextTick(() => {
          this.$refs['dataForm']?.resetFields()
          if (this.dataForm.id) {
            this.getData()
          }
        })
      },
      initCallback (data) {
        // 初始化回调函数
        this.dataForm = data
        this.dataForm.year = String(data.year)
        this.dataForm.pictureList = this.dataForm.picture && this.dataForm.picture !== '' ? this.dataForm.picture.split(',') : []
        if (this.dataForm.pictureList && this.dataForm.pictureList.length > 0) {
          this.uploadOptions.fileList = this.dataForm.pictureList.map(item => ({
            url: this.$http.adornUrl(item), // 确保图片URL完整
            name: item.split('/').pop(),     // 获取文件名
            status: 'success',               // 标记为已上传成功
            response: {
              obj:{
                path: item
              }
            }
          }))
        }
      },
      //获取所属区域
      getOrg() {
        this.$http({
          url: this.$http.adornUrl(`/admin/org/getOrgTree`),
          method: 'get',
          params: this.$http.adornParams()
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.orgList = data.obj[0].children
          } else {
            this.orgList = []
          }
        })
      }
    }
  }
</script>

<style lang="scss" scoped>
::v-deep .el-upload--picture-card {
  visibility: hidden;
}
</style>
