<template>
  <el-dialog class="common-dialog" title="详情" :visible.sync="visible"
             :close-on-click-modal="false" :modal-append-to-body="false" width="60%">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" label-width="90px">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="类型" prop="type">
            <el-dict disabled :code="'CIVILIZATION_CULTIVATION_TYPE'" v-model="dataForm.type" :clearable="false"/>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="姓名" prop="name">
            <el-input disabled v-model="dataForm.name" placeholder="请输入" clearable/>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="联系电话" prop="phone">
            <el-input disabled v-model="dataForm.phone" placeholder="请输入" clearable/>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="年份" prop="year">
            <el-date-picker
                disabled
                style="width: 100%"
                v-model="dataForm.year"
                type="year"
                clearable
                value-format="yyyy"
                placeholder="请选择">
            </el-date-picker>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="上下架" prop="onShelve">
            <el-radio-group v-model="dataForm.onShelve" disabled>
              <el-radio :label="true">上架</el-radio>
              <el-radio :label="false">下架</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="排序" prop="sequence">
            <el-input-number disabled v-model="dataForm.sequence" controls-position="right" :min="0"/>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="所属社区" prop="belongCommunityCode">
            <el-cascader
                disabled
                style="width: 100%"
                placeholder="请选择上级组织"
                v-model="dataForm.belongCommunityCode"
                :options="orgList"
                :show-all-levels="false"
                clearable
                :props="{
                  value: 'code',
                  label: 'name',
                  emitPath: false
                }"/>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="事迹" prop="achievements">
            <el-input disabled type="textarea" rows="3" v-model="dataForm.achievements" placeholder="请输入" clearable/>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item class="address-item" label="地址" prop="address">
            <el-input disabled v-model="dataForm.address" placeholder="地址" clearable/>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="经度" prop="longitude" >
            <el-input v-model="dataForm.longitude" :placeholder="'经度'" disabled></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="纬度" prop="latitude" >
            <el-input v-model="dataForm.latitude" :placeholder="'纬度'" disabled></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="是否外链" prop="outLink">
            <el-radio-group v-model="dataForm.outLink" disabled>
              <el-radio :label="true">是</el-radio>
              <el-radio :label="false">否</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20" v-if="dataForm.outLink">
        <el-col :span="24">
          <el-form-item label="外链地址" prop="outLinkAddress">
            <el-input disabled v-model="dataForm.outLinkAddress" placeholder="请输入" clearable/>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="介绍" prop="description">
            <teditor
                :disabled="true"
                style="width: 100%;"
                :value="dataForm.description"
                ref="teditor"
                @changeEditorValue="changeAnswer"
            ></teditor>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="封面图" prop="picture">
            <el-upload
                disabled
                class="avatar-uploader"
                :action="this.$http.adornUrl(`/admin/oss/upload`)"
                :headers="headers"
                :data="{serverCode: uploadOptions.serverCode, media: false}"
                :show-file-list="false"
                :on-success="successHandle"
                :on-change="changHandle"
                :on-exceed="exceedHandle"
                :before-upload="beforeUploadHandle">
              <img v-if="dataForm.picture" :src="$http.adornAttachmentUrl(dataForm.picture)" class="avatar">
              <i v-else class="el-icon-plus avatar-uploader-icon"></i>
              <div slot="tip" class="el-upload__tip" style="color: red">尺寸建议600x1000像素，文件格式jpg、bmp或png，大小不超2M</div>
            </el-upload>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">关闭</el-button>
    </span>
  </el-dialog>
</template>

<script>
  import _ from 'lodash'
  import editMixin from '@/mixins/edit-mixins'
  import fileUploadMixin from "@/mixins/file-upload-mixins";
  import Teditor from "@/components/tinymce/index.vue";
  export default {
    components: {Teditor},
    mixins: [editMixin, fileUploadMixin],
    data () {
      return {
        editOptions: {
          initUrl: '/admin/civilization_cultivation_show'
        },
        uploadOptions: {
          fieldName: 'picture',
          maxSize: 2
        },
        orgList: [],
        dataForm: {},
        initForm: {
          id: null,
          version: null,
          type: null,
          name: null,
          phone: null,
          year: null,
          belongStreetCode: null,
          belongStreetName: null,
          belongCommunityCode: null,
          belongCommunityName: null,
          address: null,
          longitude: null,
          latitude: null,
          achievements: null,
          picture: null,
          outLink: false,
          outLinkAddress: null,
          description: null,
          onShelve: false,
          sequence: null
        },
        dataRule: {
          type: [
            { required: true, message: '文明培育类型不能为空', trigger: 'blur' }
          ],
          name: [
            { required: true, message: '姓名不能为空', trigger: 'change' }
          ],
          year: [
            { required: true, message: '年份不能为空', trigger: 'change' }
          ],
          picture: [
            { required: true, message: '封面图不能为空', trigger: 'change' }
          ]
        }
      }
    },
    methods: {
      init (id) {
        this.dataForm = _.cloneDeep(this.initForm)
        this.dataForm.id = id || null
        this.dataForm.picture = null
        this.changeAnswer('')
        this.visible = true
        this.getOrg()
        this.$nextTick(() => {
          this.$refs['dataForm']?.resetFields()
          if (this.dataForm.id) {
            this.getData()
          }
        })
      },
      initCallback (data) {
        // 初始化回调函数
        this.dataForm = data
        this.dataForm.year = String(data.year)
      },
      //获取所属区域
      getOrg() {
        this.$http({
          url: this.$http.adornUrl(`/admin/org/getOrgTree`),
          method: 'get',
          params: this.$http.adornParams()
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.orgList = data.obj[0].children
          } else {
            this.orgList = []
          }
        })
      },
      changeAnswer(html) {
        this.dataForm.description = html
      }
    }
  }
</script>

<style lang="scss" scoped>
</style>
