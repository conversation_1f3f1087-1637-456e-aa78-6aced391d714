<template>
  <el-dialog class="common-dialog" :title="onlyView ? '核实信息' : '核实信息上传'" :visible.sync="visible"
             :close-on-click-modal="false" :modal-append-to-body="false" width="60%" @closed="$emit('refreshDataList')">
    <el-form :model="dataForm" ref="dataForm" label-width="120px" label-position="left">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="核实信息列表" prop="type">
            <span slot="label" style="font-size: 15px; font-weight: bold">核实信息列表</span>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-upload
              ref="upload"
              action=""
              :auto-upload="false"
              :multiple="true"
              :file-list="fileList"
              :before-upload="beforeUpload"
              :on-change="handleFileChange"
              :show-file-list="false"
              style="float: right">
            <el-button type="primary">{{onlyView ? '上传核实文件' : '上传并确认核实'}}</el-button>
          </el-upload>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="24">
          <el-table :data="dataForm.checkAttachments" border style="width: 100%" v-loading="checkAttachmentsLoading" max-height="500">
            <el-table-column prop="name" label="附件名称" align="center" header-align="center"/>
            <el-table-column prop="uploadTime" label="上传时间" align="center" header-align="center">
              <template slot-scope="scope">
                {{scope.row.uploadTime.indexOf('T') > -1 ? scope.row.uploadTime.replace('T', ' ') : scope.row.uploadTime}}
              </template>
            </el-table-column>
            <el-table-column fixed="right" label="操作" align="center" header-align="center">
              <template slot-scope="scope">
                <el-button type="text" size="small" @click="downloadCheckAttachment(scope.row)">下载</el-button>
                <el-button v-if="scope.row.id && scope.row.id !== ''" type="text" size="small" @click="removeCheckAttachment(scope.row.id)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-col>
      </el-row>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">关闭</el-button>
    </span>
  </el-dialog>
</template>

<script>
  import {fileDownload} from "@/utils";
  export default {
    data () {
      return {
        visible: false,
        onlyView: false,
        checkAttachmentsLoading: false,
        fileList: [],
        dataForm: {
          collectInfoId: null,
          checkAttachments: []
        }
      }
    },
    methods: {
      init (id, onlyView) {
        this.visible = true
        this.onlyView = onlyView
        this.dataForm.collectInfoId = id
        this.getCheckAttachments()
      },
      getCheckAttachments() {
        this.checkAttachmentsLoading = true
        this.$http({
          url: this.$http.adornUrl(`/admin/civilization_cultivation_collect/getCheckInfo`),
          method: 'get',
          params: this.$http.adornParams({
            id: this.dataForm.collectInfoId
          })
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.dataForm.checkAttachments = data.obj
          } else {
            this.dataForm.checkAttachments = []
          }
          this.checkAttachmentsLoading = false
        })
      },
      downloadCheckAttachment(row) {
        fileDownload(row.path, row.name)
      },
      removeCheckAttachment(id) {
        this.$confirm('确定删除吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.$http({
            url: this.$http.adornUrl(`/admin/civilization_cultivation_collect/checkInfoRemove`),
            method: 'get',
            params: this.$http.adornParams({
              collectId: this.dataForm.collectInfoId,
              checkInfoId: id
            })
          }).then(({data}) => {
            if (data && data.code === 0) {
              this.$message({
                type: 'success',
                message: '删除成功'
              })
            } else {
              this.$message({
                type: 'error',
                message: data.msg || '删除失败'
              })
            }
            this.getCheckAttachments()
          })
        })
      },
      // 文件选择前的校验
      beforeUpload(file) {
        const isImage = file.type.includes('image/')
        const isDocument = file.type.includes('application/pdf') ||
            file.type.includes('application/msword') ||
            file.type.includes('application/vnd.openxmlformats-officedocument.wordprocessingml.document') ||
            file.type.includes('application/vnd.ms-excel') ||
            file.type.includes('application/vnd.openxmlformats-officedocument.spreadsheetml.sheet') ||
            file.type.includes('application/vnd.ms-powerpoint') ||
            file.type.includes('application/vnd.openxmlformats-officedocument.presentationml.presentation')

        const isLt10M = file.size / 1024 / 1024 < 10

        if (!isImage && !isDocument) {
          this.$message.error('只能上传图片或文档文件!')
          return false
        }
        if (!isLt10M) {
          this.$message.error('文件大小不能超过10MB!')
          return false
        }
        return true
      },
      // 文件选择变化
      handleFileChange(file, fileList) {
        this.fileList = fileList
        this.uploadFiles()
      },
      // 上传文件
      uploadFiles() {
        if (this.fileList.length === 0) {
          this.$message.warning('请选择要上传的文件')
          return
        }
        this.$confirm('确认上传选中的文件吗?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          const formData = new FormData()
          this.fileList.forEach(file => {
            formData.append('files', file.raw)
          })
          formData.append('id', this.dataForm.collectInfoId)

          this.$http({
            url: this.$http.adornUrl('/admin/civilization_cultivation_collect/checkInfoUpload'),
            method: 'post',
            data: formData,
            headers: {
              'Content-Type': 'multipart/form-data'
            },
            transformRequest: [function(data) {
              return data  // 禁用默认的JSON转换
            }]
          }).then(({data}) => {
            if (data && data.code === 0) {
              this.$message.success('上传成功')
              this.fileList = []
              this.getCheckAttachments()
            } else {
              this.$message.error(data.msg || '上传失败')
            }
          }).catch(() => {
            this.$message.error('上传失败')
          })
        }).catch(() => {
          this.fileList = []
        })
      }
    }
  }
</script>

<style lang="scss" scoped>
</style>
