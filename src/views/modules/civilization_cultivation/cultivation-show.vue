<template>
  <div class="mod-list" v-loading="exportLoading">
    <el-form :inline="true" :model="dataForm" ref="dataForm" @keyup.enter.native="getDataList()">
      <el-form-item prop="type" label="类型">
        <el-dict :code="'CIVILIZATION_CULTIVATION_TYPE'" v-model="dataForm.type"/>
      </el-form-item>
      <el-form-item prop="phone" label="联系电话">
        <el-input v-model="dataForm.phone" placeholder="请输入" clearable/>
      </el-form-item>
      <el-form-item label="所属街道社区" prop="orgCodeList">
        <el-cascader
            placeholder="请选择上级组织"
            v-model="dataForm.orgCode"
            :options="orgList"
            :show-all-levels="false"
            clearable
            :props="{
              value: 'code',
              label: 'name',
              emitPath: false,
            }"/>
      </el-form-item>
      <el-form-item label="年份" prop="year">
        <el-date-picker
            v-model="dataForm.year"
            type="year"
            clearable
            value-format="yyyy"
            placeholder="请选择">
        </el-date-picker>
      </el-form-item>
      <el-form-item prop="onShelve" label="状态">
        <el-select placeholder="请选择" v-model="dataForm.onShelve" clearable>
          <el-option v-for="item in [{code: true, name: '上架'}, {code: false, name: '下架'}]"
                     :key="item.code" :label="item.name" :value="item.code"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-search" type="warning" @click="getDataList()">查询</el-button>
        <el-button @click="resetForm()"icon="el-icon-refresh-left">重置</el-button>
      </el-form-item>
    </el-form>
    <div class="f-s-c" style="padding: 15px 0; display: flex; justify-content: flex-end; align-items: center">
      <div>
        <el-button type="primary" @click="addOrUpdateHandle()" icon="el-icon-plus">新增</el-button>
        <el-button icon="el-icon-upload2" type="success" @click="importHandle()">批量导入</el-button>
        <el-button icon="el-icon-download" type="success" @click="exportHandle()">导出</el-button>
      </div>
    </div>
    <el-table :data="dataList" border v-loading="dataListLoading" @selection-change="selectionChangeHandle" style="width: 100%;">
      <el-table-column type="index" header-align="center" align="center" width="50" label="序号"/>
      <el-table-column prop="picture" header-align="center" align="center" label="图片">
        <template  slot-scope="scope">
          <el-tag v-if="!scope.row.picture || scope.row.picture === ''" type="danger" size="small">未上传</el-tag>
          <img v-if="scope.row.picture && scope.row.picture !== ''" class="small" :onerror="pictureErr"
               @click="bigImg($http.adornAttachmentUrl(scope.row.picture))" :src="$http.adornAttachmentUrl(scope.row.picture)" width="50px" height="50px" style="cursor: pointer">
        </template>
      </el-table-column>
      <el-table-column prop="typeText" header-align="center" align="center" label="类型"/>
      <el-table-column prop="name" header-align="center" align="center" label="姓名">
        <template slot-scope="scope">
          <a style="cursor: pointer" @click="getDetail(scope.row.id)">{{ scope.row.name }}</a>
        </template>
      </el-table-column>
      <el-table-column prop="phone" header-align="center" align="center" label="联系电话"/>
      <el-table-column prop="belongStreetCode" header-align="center" align="center" label="所属街道社区" width="240">
        <template slot-scope="scope">
          <span v-if="scope.row.belongStreetName && scope.row.belongStreetName !== '' && scope.row.belongCommunityName && scope.row.belongCommunityName !== ''">{{scope.row.belongStreetName}} <br/> {{scope.row.belongCommunityName}}</span>
          <span v-else>{{scope.row.belongStreetName && scope.row.belongStreetName !== '' ? scope.row.belongStreetName : (scope.row.belongCommunityName && scope.row.belongCommunityName !== '' ? scope.row.belongCommunityName : '')}}</span>
        </template>
      </el-table-column>
      <el-table-column prop="year" header-align="center" align="center" label="年份">
        <template slot-scope="scope">
          <span>{{scope.row.year + '年'}}</span>
        </template>
      </el-table-column>
      <el-table-column prop="sequence" header-align="center" align="center" label="排序"/>
      <el-table-column prop="updateDate" header-align="center" align="center" label="更新时间" width="160"/>
      <el-table-column prop="onShelve" header-align="center" align="center" label="状态" width="100">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.onShelve" type="success" size="small">上架</el-tag>
          <el-tag v-else type="danger" size="small">下架</el-tag>
        </template>
      </el-table-column>
      <el-table-column fixed="right" header-align="center" align="center" width="200" label="操作">
        <template slot-scope="scope">
          <el-button type="text" @click="addOrUpdateHandle(scope.row.id)">编辑</el-button>
          <el-button type="text" @click="deleteHandle(scope.row.id,scope.row.name)">删除</el-button>
          <el-button type="text" @click="onShelveChange(scope.row.id, scope.row.onShelve)">{{ scope.row.onShelve ? '下架' : '上架' }}</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination @size-change="sizeChangeHandle" @current-change="currentChangeHandle" :current-page="pageIndex"
                   :page-sizes="[10, 20, 50, 100]" :page-size="pageSize" :total="totalPage" layout="total, sizes, prev, pager, next, jumper"/>
    <el-dialog
        title="大图展示"
        :visible.sync="dialogVisible"
        width="50%">
      <div style="display: flex; justify-content: center; align-items: center">
        <img class="big" :src="imgUrl" alt="" style="width: 80%;">
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="dialogVisible = false">确 定</el-button>
      </span>
    </el-dialog>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="query"/>
    <!-- 弹窗, 详情 -->
    <detail v-if="detailVisible" ref="detail"/>
    <!-- 弹窗, 导入 -->
    <import v-if="importVisible" ref="import" @refreshDataList="getDataList"></import>
  </div>
</template>

<script>
import AddOrUpdate from './cultivation-show-add-or-update'
import Detail from  './cultivation-show-detail'
import listMixin from '@/mixins/list-mixins'
import pictureErr from '@/assets/img/pic_error.png'
import Import from './cultivation-show-import'
export default {
  mixins: [listMixin],
  data () {
    return {
      pictureErr: 'this.src="' + pictureErr + '"',
      imgUrl: null,
      dialogVisible: false,
      mixinOptions: {
        dataUrl: '/admin/civilization_cultivation_show/pages',
        deleteUrl: '/admin/civilization_cultivation_show/removeByIds',
        exportUrl: '/admin/civilization_cultivation_show/export',              // 导出接口，API地址
        exportFileName: '文明培育展示数据' // 导出文件名称
      },
      orgList: [],
      dataForm: {
        type: null,
        phone: null,
        orgCode: null,
        year: null,
        onShelve: null,
        orders: [{ column: 'sequence', sort: 'asc'}]
      },
      detailVisible: false,
      importVisible: false,
      exportLoading: false
    }
  },
  components: {
    AddOrUpdate,
    Detail,
    Import
  },
  activated() {
    this.getOrg()
  },
  methods: {
    resetForm() {
      this.$refs['dataForm']?.resetFields()
      this.dataForm.orgCode = null
      this.getDataList()
    },
    //获取所属区域
    getOrg() {
      this.$http({
        url: this.$http.adornUrl(`/admin/org/getOrgTree`),
        method: 'get',
        params: this.$http.adornParams()
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.orgList = data.obj[0].children
        } else {
          this.orgList = []
        }
      })
    },
    bigImg (imgUrl) {
      this.imgUrl = imgUrl
      this.dialogVisible = true
    },
    getDetail(id) {
      this.detailVisible = true
      this.$nextTick(() => {
        this.$refs.detail.init(id)
      })
    },
    onShelveChange(id, onShelve) {
      this.$confirm('确定要' + (onShelve ? '下架' : '上架') + '吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$http({
          url: this.$http.adornUrl(`/admin/civilization_cultivation_show/onShelveChange`),
          method: 'get',
          params: this.$http.adornParams({
            id: id
          })
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.$message({
              message: '操作成功',
              type: 'success',
              duration: 1500
            })
          } else {
            this.$message.error(data.msg)
          }
          this.query()
        })
      })
    },
    importHandle() {
      this.importVisible = true
      this.$nextTick(() => {
        this.$refs.import.init()
      })
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
