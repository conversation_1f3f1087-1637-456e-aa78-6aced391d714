<template>
    <el-dialog
            :title="'修改上级'"
            :close-on-click-modal="false"
            width="40%"
            :visible.sync="visible">
        <el-form :model="dataForm" :rules="dataRule" ref="dataForm" label-width="120px">
            <el-form-item label="上级组织" prop="orgCodeList">
                <el-cascader
                    v-model="dataForm.orgCodeList"
                    :options="orgList"
                    :show-all-levels="false"
                    :props="{checkStrictly: true,label: 'name',value: 'code'}"
                    @change="handleChange"/>
            </el-form-item>
        </el-form>
        <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="updateOrgCode()">确定</el-button>
    </span>
    </el-dialog>
</template>

<script>
import moment from 'moment'
import {getFathersById} from "@/utils";

export default {
    data() {

        return {
            visible: false,
            orgList: [],
            dataForm: {
                id: null,
                version: null,
                orgCode: '',
                orgCodeList: [],
            },
            dataRule: {

            },

        }
    },
    components: {
        moment
    },
    methods: {
        async init(id) {
            this.dataForm.id = id || null
            this.dataForm.orgCode = ''
            this.visible = true
            await this.getOrg()
            await this.getInitDate()
        },
        async getInitDate() {
            this.visible = true
            this.$nextTick(() => {
                this.$refs['dataForm']?.resetFields()
                if (this.dataForm.id) {
                    this.$http({
                        url: this.$http.adornUrl(`/admin/zyz/team/getTeamById`),
                        method: 'get',
                        params: this.$http.adornParams({
                            id: this.dataForm.id
                        })
                    }).then(({data}) => {
                        if (data && data.code === 0) {
                            data.obj.orgCodeList = getFathersById(data.obj.orgCode, this.orgList)
                            this.dataForm = data.obj
                        }
                    })
                }
            })
        },
        updateOrgCode(){
            this.$refs['dataForm'].validate((valid) => {
                if (valid) {
                    this.$confirm(`确定进行修改上级组织?`, '提示', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning',
                    }).then(() => {

                        this.$http({
                            url: this.$http.adornUrl(`/admin/zyz/team/updateOrgCode`),
                            method: 'get',
                            params: this.$http.adornParams({
                                'id': this.dataForm.id,
                                'orgCode': this.dataForm.orgCode
                            })
                        }).then(({data}) => {
                            if (data && data.code === 0) {
                                this.$message({
                                    message: '操作成功',
                                    type: 'success',
                                    duration: 1500,
                                    onClose: () => {
                                        this.visible = false
                                        this.$emit('refreshDataList')
                                    }
                                })
                            } else {
                                this.$message.error(data.msg)
                            }
                        })

                    })
                }
            })
        },
        //获取所属区域
        //获取所属区域
        async getOrg() {
            await this.$http({
                url: this.$http.adornUrl(`/admin/org/getOrgTree`),
                method: 'get',
                params: this.$http.adornParams()
            }).then(({data}) => {
                if (data && data.code === 0) {
                    this.orgList = this.getTreeData(data.obj)
                } else {
                    this.orgList = []
                }
            })
        },
        // *处理点位分类最后children数组为空的状态
        getTreeData: function (data) {
            let that = this
            // 循环遍历json数据
            data.forEach(function (e) {
                if (!e.children || e.children.length < 1) {
                    e.children = undefined
                } else {
                    // children若不为空数组，则继续 递归调用 本方法
                    that.getTreeData(e.children)
                }
            })
            return data
        },
        handleChange(value) {
            this.dataForm.orgCode = value && value.length > 0 ? value[value.length - 1] : null
            // console.log(this.dataForm.orgCode)
        },
    }
}
</script>
