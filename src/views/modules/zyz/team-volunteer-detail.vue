<template>
  <el-dialog
      :title="'志愿者详情'"
      :close-on-click-modal="false"
      width="80%"
      append-to-body
      :visible.sync="visible">
    <el-form :inline="true" :model="dataForm" ref="dataForm">
      <el-form-item label="姓名" prop="name">
        <el-input v-model="dataForm.name" placeholder="姓名" clearable></el-input>
      </el-form-item>
      <el-form-item label="证件号码" prop="certificateId">
        <el-input v-model="dataForm.certificateId" placeholder="请输入证件号码"></el-input>
      </el-form-item>
      <el-form-item label="电话" prop="phone">
        <el-input v-model="dataForm.phone" placeholder="请输入电话"></el-input>
      </el-form-item>
      <el-form-item label="加入时间" prop="joinTime">
        <el-date-picker
            v-model="dataForm.joinTime"
            clearable
            type="daterange"
            value-format="yyyy-MM-dd"
            format="yyyy-MM-dd"
            align="right"
            start-placeholder="开始时间"
            end-placeholder="结束时间">
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-search" type="warning" @click="queryPage()">查询</el-button>
        <el-button @click="resetForm()" icon="el-icon-refresh-left">重置</el-button>
      </el-form-item>
    </el-form>
    <div class="f-s-c" style="padding: 15px 0;display: flex;justify-content:flex-end;align-items: center ">
      <div>
          <el-button icon="el-icon-download" type="success" v-loading.fullscreen.lock="fullscreenLoading" @click="exportMemberTeamApply()">团队成员活动记录
          </el-button>
        <el-button icon="el-icon-download" type="success" v-loading.fullscreen.lock="fullscreenLoading" @click="exportVolunteer()">全部导出
        </el-button>
      </div>
    </div>
    <el-table
        :data="dataList"
        border
        v-loading="dataListLoading"
        style="width: 100%;">
      <!--      <el-table-column-->
      <!--          prop="volunteerNo"-->
      <!--          header-align="center"-->
      <!--          align="center"-->
      <!--          label="编号">-->
      <!--      </el-table-column>-->
      <el-table-column
          prop="volunteerName"
          header-align="center"
          align="center"
          width="100"
          label="姓名">
      </el-table-column>
      <el-table-column
          prop="phone"
          header-align="center"
          align="center"
          width="120"
          label="手机号码">
      </el-table-column>
      <el-table-column
          prop="certificateTypeText"
          header-align="center"
          align="center"
          width="130"
          label="证件类型">
      </el-table-column>
      <el-table-column
          prop="certificateId"
          header-align="center"
          align="center"
          width="200"
          label="证件号码">
      </el-table-column>
      <el-table-column
          prop="dutyTypeText"
          header-align="center"
          align="center"
          width="120"
          label="团队角色">
      </el-table-column>
      <el-table-column
          prop="serviceLong"
          header-align="center"
          align="center"
          width="120"
          label="服务时长">
      </el-table-column>
      <el-table-column
          prop="serviceLongThisYear"
          header-align="center"
          align="center"
          width="120"
          label="当年服务时长">
      </el-table-column>
      <el-table-column
          prop="joinTime"
          header-align="center"
          align="center"
          width="180"
          label="加入时间">
      </el-table-column>
      <el-table-column
          prop="volunteerStatus"
          header-align="center"
          align="center"
          width="100"
          label="志愿者状态">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.volunteerStatus" type="success">正常</el-tag>
          <el-tag v-if="!scope.row.volunteerStatus" type="warning">冻结</el-tag>
        </template>
      </el-table-column>
      <el-table-column
          prop="isSyncText"
          header-align="center"
          align="center"
          width="100"
          label="同步状态">
        <template slot-scope="scope">
          <el-popover trigger="hover" placement="top">
            <p style="text-align: center">
              {{
                '同步时间：' + (scope.row.syncTime ? scope.row.syncTime : '')
              }}<br>{{ scope.row.syncRemark }}</p>
            <div slot="reference" class="name-wrapper">
              <el-tag v-if="scope.row.isSync === 'sync_failure'" type="danger">{{ scope.row.isSyncText }}</el-tag>
              <el-tag v-if="scope.row.isSync === 'sync_wait'" type="warning">{{ scope.row.isSyncText }}</el-tag>
              <el-tag v-if="scope.row.isSync === 'sync_success'" type="success">{{ scope.row.isSyncText }}</el-tag>
            </div>
          </el-popover>
        </template>
      </el-table-column>
      <el-table-column
          fixed="right"
          header-align="center"
          align="center"
          width="200"
          label="操作">
        <template slot-scope="scope">
          <el-button type="text" size="small" @click="deleteHandle(scope.row.teamId,scope.row.volunteerId)">移除
          </el-button>
<!--          <el-button type="text" size="small"-->
<!--                     @click="changeRole(scope.row.teamId,scope.row.volunteerId,scope.row.dutyType)">变更角色-->
<!--          </el-button>-->
          <el-button type="text" size="small" :disabled="scope.row.isSync === 'sync_success'"
                     @click="syncHandle(scope.row.teamId,scope.row.volunteerId)">同步成员
          </el-button>
            <el-button type="text" size="small"
                       @click="exportTeamApply(scope.row.teamId,scope.row.volunteerId)">活动导出
            </el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
        @size-change="sizeChangeHandle"
        @current-change="currentChangeHandle"
        :current-page="pageIndex"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pageSize"
        :total="totalPage"
        layout="total, sizes, prev, pager, next, jumper">
    </el-pagination>
  </el-dialog>
</template>

<script>
import moment from 'moment'

export default {
  data() {
    return {
      visible: false,
      changeRoleVisible: false,
      dataForm: {
        id: null,
        version: null,
        name: '',
        volunteerId: '',
        joinTime: '',
        phone: '',
        certificateId: ''
      },
      fullscreenLoading: false,
      dataList: [],
      pageIndex: 1,
      pageSize: 10,
      totalPage: 0,
      dataListLoading: false,
      dataListSelections: [],
      nameError: null,
    }
  },
  components: {
    moment
  },
  methods: {
    queryPage() {
      this.pageIndex = 1
      this.getDataList()
    },
    init(id) {
      this.dataForm.teamId = id || null
      this.visible = true
      this.getDataList()
    },
    // 获取数据列表
    getDataList() {
      this.dataListLoading = true
      this.$http({
        url: this.$http.adornUrl('/admin/zyz/volunteer/team/getMyVolunteerPages'),
        method: 'post',
        data: this.$http.adornData({
          'currentPage': this.pageIndex,
          'pageSize': this.pageSize,
          'name': this.dataForm.name,
          'teamId': this.dataForm.teamId,
          'certificateId': this.dataForm.certificateId,
          'phone': this.dataForm.phone,
          'joinStartDate': this.dataForm.joinTime != null ? this.dataForm.joinTime[0] : null,
          'joinEndDate': this.dataForm.joinTime != null ? this.dataForm.joinTime[1] : null
        })
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.dataList = data.obj.records
          this.totalPage = data.obj.total
        } else {
          this.dataList = []
          this.totalPage = 0
        }
        this.dataListLoading = false
      })
    }, // 同步
    syncHandle(teamId, volunteerId) {
      this.$confirm(`确定要进行同步嘛，请勿重复操作，否则可能会带来不可预知的后果`, '确认同步？', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        closeOnClickModal: false
      }).then(() => {
        this.dataListLoading = true
        this.$http({
          url: this.$http.adornUrl('/admin/zyz/platform-sync/syncTeamMember'),
          method: 'post',
          params: this.$http.adornParams({
            'teamId': teamId,
            'volunteerId': volunteerId,
          })
        }).then(({data}) => {
          this.dataListLoading = false
          if (data && data.code === 0) {
            this.$message({
              message: '请求同步成功，市平台返回：（' + data.obj.message + '）',
              type: data.obj.resultCode === 'SUCCESS' ? 'success' : 'warning',
              duration: 3000,
              onClose: () => {
                this.getDataList()
              }
            })
          } else {
            this.$message.error(data.msg)
          }
        })
      })
    },
    // 重置
    resetForm() {
      this.$refs['dataForm']?.resetFields()
      this.getDataList()
    },
    // 每页数
    sizeChangeHandle(val) {
      this.pageSize = val
      this.pageIndex = 1
      this.getDataList()
    },
    // 当前页
    currentChangeHandle(val) {
      this.pageIndex = val
      this.getDataList()
    },
    // 下载模板文件
    exportVolunteer() {
      this.fullscreenLoading = true;
      this.$http({
        url: this.$http.adornUrl('/admin/zyz/volunteer/team/exportVolunteer'),
        method: 'post',
        responseType: 'arraybuffer',
        data: this.$http.adornData({
          'name': this.dataForm.name,
          'teamId': this.dataForm.teamId,
          'certificateId': this.dataForm.certificateId,
          'phone': this.dataForm.phone,
          'joinStartDate': this.dataForm.joinTime != null ? this.dataForm.joinTime[0] : null,
          'joinEndDate': this.dataForm.joinTime != null ? this.dataForm.joinTime[1] : null
        })
      }).then(({data}) => {
        if (data.code && data.code !== 0) {
          this.$message.error('导出失败')
        } else {
          this.fullscreenLoading = false;
          let blob = new Blob([data], {type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8'})
          let objectUrl = URL.createObjectURL(blob)
          let a = document.createElement('a')
          // window.location.href = objectUrl
          a.href = objectUrl
          a.download = '团队志愿者明细.xlsx'
          a.click()
          URL.revokeObjectURL(objectUrl)
          this.$message({
            type: 'success',
            message: '导出成功'
          })
        }
      })
    },
    // 单个成员团队活动报名导出
    exportTeamApply(teamId, volunteerId) {
        this.fullscreenLoading = true;
        this.$http({
            url: this.$http.adornUrl('/admin/zyz/volunteer/team/exportTeamApply'),
            method: 'post',
            responseType: 'arraybuffer',
            data: this.$http.adornData({
                'teamId':teamId,
                'volunteerId':volunteerId
            })
        }).then(({data}) => {
            if (data.code && data.code !== 0) {
                this.$message.error('导出失败')
            } else {
                this.fullscreenLoading = false;
                let blob = new Blob([data], {type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8'})
                let objectUrl = URL.createObjectURL(blob)
                let a = document.createElement('a')
                // window.location.href = objectUrl
                a.href = objectUrl
                a.download = '团队活动报名明细.xlsx'
                a.click()
                URL.revokeObjectURL(objectUrl)
                this.$message({
                    type: 'success',
                    message: '导出成功'
                })
            }
        })
    },
    // 所有成员团队活动报名导出
    exportMemberTeamApply() {
        this.fullscreenLoading = true;
        this.$http({
            url: this.$http.adornUrl('/admin/zyz/volunteer/team/exportTeamApply'),
            method: 'post',
            responseType: 'arraybuffer',
            data: this.$http.adornData({
                'teamId':this.dataForm.teamId
            })
        }).then(({data}) => {
            if (data.code && data.code !== 0) {
                this.$message.error('导出失败')
            } else {
                this.fullscreenLoading = false;
                let blob = new Blob([data], {type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8'})
                let objectUrl = URL.createObjectURL(blob)
                let a = document.createElement('a')
                // window.location.href = objectUrl
                a.href = objectUrl
                a.download = '团队活动报名明细.xlsx'
                a.click()
                URL.revokeObjectURL(objectUrl)
                this.$message({
                    type: 'success',
                    message: '导出成功'
                })
            }
        })
    },
    // // 修改角色
    // changeRole(teamId, volunteerId, dutyType) {
    //   this.changeRoleVisible = true
    //   this.$nextTick(() => {
    //     this.$refs.changeRole.init(teamId, volunteerId, dutyType)
    //   })
    // },
    // 移除
    deleteHandle(teamId, volunteerId) {
      this.$confirm(`确定进行移除操作?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        closeOnClickModal: false
      }).then(() => {
        this.$http({
          url: this.$http.adornUrl('/admin/zyz/volunteer/team/removeVolunteer'),
          method: 'get',
          params: this.$http.adornParams({
            'teamId': teamId,
            'volunteerId': volunteerId
          })
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.$message({
              message: '操作成功',
              type: 'success',
              duration: 1500,
              onClose: () => {
                this.getDataList()
              }
            })
          } else {
            this.$message.error(data.msg)
          }
        })
      })
    }
  }
}
</script>
