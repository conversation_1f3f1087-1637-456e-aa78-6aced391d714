<template>
  <div class="mod-config">
    <el-form :inline="true" :model="dataForm" ref="dataForm" @keyup.enter.native="queryPage()">
<!--      <el-form-item label="编号" prop="teamNo">-->
<!--        <el-input v-model="dataForm.teamNo" placeholder="编号" clearable></el-input>-->
<!--      </el-form-item>-->
      <el-form-item label="姓名" prop="name">
        <el-input v-model="dataForm.name" placeholder="姓名" clearable></el-input>
      </el-form-item>
      <el-form-item label="手机号码" prop="phone">
        <el-input v-model="dataForm.phone" placeholder="手机号码" clearable></el-input>
      </el-form-item>
      <el-form-item label="审核状态" prop="status">
        <el-dict :code="'vol_team_status'" v-model="dataForm.status"></el-dict>
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-search" type="warning" @click="queryPage()">查询</el-button>
        <el-button @click="resetForm()" icon="el-icon-refresh-left">重置</el-button>
      </el-form-item>
    </el-form>
    <div class="f-s-c" style="padding: 15px 0;display: flex;justify-content:flex-end;align-items: center ">
      <div>
<!--        <el-button  type="primary" @click="addOrUpdateHandle()">新增</el-button>-->
        <el-button icon="el-icon-s-check" type="primary"  @click="volunteerAudit()" :disabled="dataListSelections.length <= 0">批量审核</el-button>
<!--        <el-button  type="danger" @click="deleteHandle()" :disabled="dataListSelections.length <= 0">批量删除</el-button>-->
      </div>
    </div>
    <el-table
      :data="dataList"
      border
      v-loading="dataListLoading"
      @selection-change="selectionChangeHandle"
      style="width: 100%;">
      <el-table-column
        type="selection"
        header-align="center"
        show-overflow-tooltip
        align="center"
        :selectable="checkSelectable"
        width="50">
      </el-table-column>
      <el-table-column
          prop="teamName"
          header-align="center"
          align="center"
          min-width="300"
          show-overflow-tooltip
          label="团队名称">
      </el-table-column>
      <el-table-column
          prop="orgName"
          header-align="center"
          align="center"
          min-width="250px"
          label="上级组织">
      </el-table-column>
      <el-table-column
          prop="name"
          header-align="center"
          align="center"
          min-width="100px"
          label="志愿者姓名">
      </el-table-column>
      <el-table-column
          prop="phone"
          header-align="center"
          align="center"
          min-width="130px"
          label="手机号">
      </el-table-column>
      <el-table-column
          prop="certificateTypeText"
          header-align="center"
          align="center"
          min-width="150px"
          label="证件类型">
      </el-table-column>
      <el-table-column
          prop="certificateId"
          header-align="center"
          align="center"
          min-width="200px"
          label="证件号码">
      </el-table-column>
      <el-table-column
          prop="sexText"
          header-align="center"
          align="center"
          label="性别">
      </el-table-column>
      <el-table-column
          prop="applicationTime"
          header-align="center"
          align="center"
          min-width="200px"
          label="申请时间">
      </el-table-column>
      <el-table-column
          prop="statusText"
          header-align="center"
          align="center"
          label="审核状态">
      </el-table-column>
      <el-table-column
          fixed="right"
          header-align="center"
          align="center"
          min-width="70"
          label="操作">
        <template slot-scope="scope">
          <el-button type="text" v-if="scope.row.status === 'vts_not_audit'" size="small" @click="volunteerAudit(scope.row.id)">审核</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      @size-change="sizeChangeHandle"
      @current-change="currentChangeHandle"
      :current-page="pageIndex"
      :page-sizes="[10, 20, 50, 100]"
      :page-size="pageSize"
      :total="totalPage"
      layout="total, sizes, prev, pager, next, jumper">
    </el-pagination>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"></add-or-update>
    <volunteer-audit v-if="volunteerAuditVisible" ref="volunteerAudit" @refreshDataList="getDataList"></volunteer-audit>
  </div>
</template>

<script>
  import AddOrUpdate from './volunteer-team-audit-add-or-update'
  import VolunteerAudit from "./change-volunteer-audit";

  export default {
    data () {
      return {
        dataForm: {
          key: '',
          name: '',
          phone: '',
          status: 'vts_not_audit'
        },
        volunteerAuditVisible: '',
        dataList: [],
        pageIndex: 1,
        pageSize: 10,
        totalPage: 0,
        dataListLoading: false,
        dataListSelections: [],
        addOrUpdateVisible: false
      }
    },
    components: {
      AddOrUpdate,
      VolunteerAudit
    },
    activated () {
      this.queryPage()
    },
    methods: {
      queryPage () {
        this.pageIndex = 1
        this.getDataList()
      },
      checkSelectable(row,index){
        if (row.status === 'vts_not_audit'){
          return true
        } else {
          return false;
        }
      },
      // 重置
      resetForm() {
        this.$refs['dataForm']?.resetFields()
        this.getDataList()
      },
      // 获取数据列表
      getDataList () {
        this.dataListLoading = true
        this.$http({
          url: this.$http.adornUrl('/admin/zyz/volunteer/team/audit/getPages'),
          method: 'post',
          data: this.$http.adornData({
            'currentPage': this.pageIndex,
            'pageSize': this.pageSize,
            'status': this.dataForm.status,
            'name': this.dataForm.name,
            'phone': this.dataForm.phone
          })
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.dataList = data.obj.records
            this.totalPage = data.obj.total
          } else {
            this.dataList = []
            this.totalPage = 0
          }
          this.dataListLoading = false
        })
      },
      volunteerAudit (id){
        var ids = id ? [id] : this.dataListSelections.map(item => {
          return item.id
        })
        this.volunteerAuditVisible = true
        this.$nextTick(() => {
          this.$refs.volunteerAudit.init(ids.join(','))
        })
      },
      // 每页数
      sizeChangeHandle (val) {
        this.pageSize = val
        this.pageIndex = 1
        this.getDataList()
      },
      // 当前页
      currentChangeHandle (val) {
        this.pageIndex = val
        this.getDataList()
      },
      // 多选
      selectionChangeHandle (val) {
        this.dataListSelections = val
      },
      // 新增 / 修改
      addOrUpdateHandle (id) {
        this.addOrUpdateVisible = true
        this.$nextTick(() => {
          this.$refs.addOrUpdate.init(id)
        })
      },
      // 删除
      deleteHandle (id) {
        var ids = id ? [id] : this.dataListSelections.map(item => {
          return item.id
        })
        this.$confirm(`确定进行[${id ? '删除' : '批量删除'}]操作?`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
          closeOnClickModal: false
        }).then(() => {
          this.$http({
            url: this.$http.adornUrl('/admin/zyz/volunteer/team/audit/removeByIds'),
            method: 'get',
            params: this.$http.adornParams({
              'ids': ids.join(',')
            })
          }).then(({data}) => {
            if (data && data.code === 0) {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 1500,
                onClose: () => {
                  this.getDataList()
                }
              })
            } else {
              this.$message.error(data.msg)
            }
          })
        })
      }
    }
  }
</script>
