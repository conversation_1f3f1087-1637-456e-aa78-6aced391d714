<template>
  <div class="mod-list">
    <div class="select-content">
      <el-form :inline="true" :model="dataForm" ref="dataForm">
        <el-form-item prop="type" label="类型">
          <el-dict :code="'sug_advice_type'" v-model="dataForm.type"></el-dict>
        </el-form-item>
        <el-form-item prop="status" label="处理状态">
          <el-select clearable v-model="dataForm.status" placeholder="请选择">
            <el-option
                v-for="item in [{label: '已处理', value: true}, {label: '待处理', value: false}]"
                :key="item.value"
                :label="item.label"
                :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button icon="el-icon-search" type="primary" @click="getDataList()">查询</el-button>
          <el-button icon="el-icon-refresh-left" @click="resetField()">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
    <el-table :data="dataList" border v-loading="dataListLoading" style="width: 100%;">
      <el-table-column
          prop="typeText"
          header-align="center"
          align="center"
          label="类型">
      </el-table-column>
      <el-table-column
          prop="name"
          header-align="center"
          align="center"
          label="姓名">
      </el-table-column>
      <el-table-column
          prop="mobile"
          header-align="center"
          align="center"
          label="联系电话">
      </el-table-column>
      <el-table-column
          prop="adviceContent"
          header-align="center"
          align="center"
          show-overflow-tooltip
          label="内容">
      </el-table-column>
      <el-table-column prop="createDate" header-align="center" align="center" label="提交时间">
      </el-table-column>
      <el-table-column
          prop="status"
          header-align="center"
          align="center"
          label="处理状态">
        <template slot-scope="scope">
          <el-tag v-if="!scope.row.status" size="small" type="danger">待处理</el-tag>
          <el-tag v-else size="small">已处理</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="replyDate" header-align="center" align="center" label="处理时间">
      </el-table-column>
      <el-table-column fixed="right" header-align="center" align="center" width="150" label="操作">
        <template slot-scope="scope">
          <el-button v-if="scope.row.status" type="text" @click="addOrUpdateHandle(scope.row.id)">查看</el-button>
          <el-button v-if="!scope.row.status"type="text" @click="addOrUpdateHandle(scope.row.id)">处理</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination @size-change="sizeChangeHandle" @current-change="currentChangeHandle" :current-page="pageIndex"
                   :page-sizes="[10, 20, 50, 100]" :page-size="pageSize" :total="totalPage" layout="total, sizes, prev, pager, next, jumper"/>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="query"/>
  </div>
</template>

<script>
import AddOrUpdate from './reply.vue'
import listMixin from '@/mixins/list-mixins'

export default {
    mixins: [listMixin],
    data () {
      return {
        mixinOptions: {
          dataUrl: '/admin/zyz/sugAdvice/pages'
        },
        dataForm: {
          type: null,
          status: null
        }
      }
    },
    components: {
      AddOrUpdate
    },
    created () {
      this.dataForm.status = this.$route.query.status === '0' ? false : ''
      console.log('status', this.dataForm.status)
    },
    methods: {
      resetField() {
        this.$refs['dataForm']?.resetFields()
        this.getDataList()
      }
    }
  }
</script>

<style lang="scss" scoped>
</style>
