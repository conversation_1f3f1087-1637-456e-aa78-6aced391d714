<template>
  <el-dialog class="common-dialog" :title="dataFormLoading ? '查看' : '处理'" :visible.sync="visible"
             :close-on-click-modal="false" :modal-append-to-body="false" width="70%">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" label-width="110px">
      <el-form-item label="类型" prop="type">
        <el-dict :code="'sug_advice_type'" v-model="dataForm.type" :disabled="true"></el-dict>
      </el-form-item>
      <el-form-item label="姓名" prop="name">
        <el-input v-model="dataForm.name"  disabled/>
      </el-form-item>
      <el-form-item label="电话" prop="mobile">
        <el-input v-model="dataForm.mobile" disabled/>
      </el-form-item>
      <el-form-item label="提交时间" prop="createDate">
        <el-input v-model="dataForm.createDate" disabled />
      </el-form-item>
      <el-form-item label="咨询内容" prop="adviceContent" >
        <el-input v-model="dataForm.adviceContent" :rows="3" type="textarea" disabled/>
      </el-form-item>
      <el-form-item label="图片" prop="picList"  v-if="dataForm.imageUrl">
        <el-image  style="width:80px;height:80px;border:none; margin-right: 2px;" v-for="(item, index) in dataForm.picList" :key="index" :src="item"
                   :preview-src-list="dataForm.picList">
        </el-image>
      </el-form-item>
        <el-form-item label="手机端是否展示" prop="frontShow" >
            <el-radio-group v-model="dataForm.frontShow">
                <el-radio :label="true">是</el-radio>
                <el-radio :label="false">否</el-radio>
            </el-radio-group>
        </el-form-item>
      <el-form-item label="处理内容" prop="replyContent" >
        <el-input v-model="dataForm.replyContent" :rows="3" type="textarea" :disabled="dataFormLoading"/>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="reply()" :disabled="dataFormLoading" v-if="!dataFormLoading">提交处理</el-button>
    </span>
  </el-dialog>
</template>

<script>
  import editMixin from '@/mixins/edit-mixins'
  import _ from "lodash";
  export default {
    mixins: [editMixin],
    data () {
      return {
        editOptions: {
          initUrl: '/admin/zyz/sugAdvice/',
          replyUrl: '/admin/zyz/sugAdvice/reply'
        },
        dataForm: {
          id: null,
          version: null,
          type: null,
          name: null,
          mobile: null,
          adviceContent: null,
          createDate: null,
          replyContent: null,
          frontShow: false,
          imageUrl: null
        },
        fileUrls: '',
        dataRule: {
          replyContent: [
            { required: true, message: '处理内容不能为空', trigger: 'change' }
          ]
        }
      }
    },
    methods: {
      initCallback (data) {
        data.picList= []
        if(data.imageUrl){
          data.imageUrl.split(',').forEach(item => {
            data.picList.push(this.$http.adornAttachmentUrl(item))
          })
        }
        this.dataForm = data
        this.dataFormLoading = data.status

      },
      reply () {
        this.dataFormLoading = true
        this.$refs['dataForm'].validate((valid) => {
          if (valid) {
            this.clearErrors()
            this.$http({
              url: this.$http.adornUrl(this.editOptions.replyUrl),
              method: 'post',
              data: this.$http.adornData(this.dataForm)
            }).then(({data}) => {
              if (data && data.code === 0) {
                return this.$message({
                  message: '操作成功',
                  type: 'success',
                  duration: 1000,
                  onClose: () => {
                    this.dataFormLoading = false
                    this.visible = false
                    if(this.editOptions.isEmit) {
                      this.$emit(this.editOptions.emit)
                    }
                  }
                })
              }
              if (data && data.code === 303) {
                let errors = {}
                for (let it of data.obj) {
                  errors[`${it.field}`] = it.message
                }
                this.errors = _.cloneDeep(errors)
              } else {
                this.$message.error(data.msg)
              }
              this.dataFormLoading = false
            }).catch(() => {
              this.dataFormLoading = false
            })
          } else {
            this.dataFormLoading = false
          }
        })
      }
    }
  }
</script>

<style>
</style>
