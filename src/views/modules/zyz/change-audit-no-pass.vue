<template>
  <el-dialog
      :title="'驳回'"
      :close-on-click-modal="false"
      width="40%"
      append-to-body
      :visible.sync="visible">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" label-width="120px">
      <el-form-item v-if="isShow" label="审核" prop="auditStatus">
        <el-radio-group v-model="dataForm.pass">
          <el-radio :label="true">通过</el-radio>
          <el-radio :label="false">驳回</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item v-if="!dataForm.pass" label="驳回理由" prop="remarks">
        <el-input type="textarea" :autosize="{ minRows: 2}" v-model="dataForm.remark"
                  placeholder="驳回理由"></el-input>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="noPassAudit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import moment from 'moment'

export default {
  data() {
    return {
      visible: false,
      isShow: false,
      dataForm: {
        id: null,
        version: null,
        remark: null,
        pass: null
      },
      dataRule: {
        remark: [
          {required: true, message: '驳回理由不能为空', trigger: 'blur'}
        ]
      },
      remarksError: null
    }
  },
  components: {
    moment
  },
  methods: {
    init(id, pass, isShow) {
      this.dataForm.id = id || null
      this.dataForm.pass = pass
      this.dataForm.remark = null
      this.isShow = isShow
      this.visible = true
    },
    noPassAudit() {
      this.$http({
        url: this.$http.adornUrl('/admin/team/audit/log/audit'),
        method: 'get',
        params: this.$http.adornParams({
          'teamIds': this.dataForm.id,
          'remark': this.dataForm.remark,
          'pass': this.dataForm.pass
        })
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.$message({
            message: '操作成功',
            type: 'success',
            duration: 1500,
            onClose: () => {
              this.visible = false
              this.$emit('refreshDataList')
            }
          })
        } else {
          this.$message.error(data.msg)
        }
      })
    }
  }
}
</script>
