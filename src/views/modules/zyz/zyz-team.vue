R
<template>
  <div class="mod-config">
    <el-form :inline="true" :model="dataForm" ref="dataForm" @keyup.enter.native="queryPage()">
      <!--      <el-form-item label="团队编号：" prop="teamNo">-->
      <!--        <el-input v-model="dataForm.teamNo" placeholder="请输入团队编号" clearable></el-input>-->
      <!--      </el-form-item>-->
      <el-form-item label="团队名称" prop="name">
        <el-input v-model="dataForm.name" placeholder="请输入团队名称" clearable></el-input>
      </el-form-item>
      <el-form-item label="管理员" prop="admin">
        <el-input style="width: 250px" v-model="dataForm.admin" placeholder="请输入管理员名称或联系方式" clearable></el-input>
      </el-form-item>
      <el-form-item label="负责人" prop="curator">
        <el-input style="width: 250px" v-model="dataForm.curator" placeholder="请输入负责人名称或联系方式" clearable></el-input>
      </el-form-item>
      <el-form-item label="上级组织" prop="orgCodeList">
        <el-cascader
            placeholder="请选择上级组织"
            v-model="dataForm.orgCodeList"
            :options="orgList"
            :show-all-levels="false"
            clearable
            :props="{checkStrictly: true,value: 'code',label: 'name'}"
        ></el-cascader>
      </el-form-item>
      <el-form-item label="重点团队" prop="isEmphasis">
        <el-dict :code="'emphasis_type'" v-model="dataForm.isEmphasis"></el-dict>
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-radio-group v-model="dataForm.status">
          <el-radio :label="null">全部</el-radio>
          <el-radio :label="true">启用</el-radio>
          <el-radio :label="false">禁用</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-search" type="warning" @click="queryPage()">查询</el-button>
        <el-button @click="resetForm()" icon="el-icon-refresh-left">重置</el-button>
      </el-form-item>
    </el-form>
    <div class="f-s-c" style="padding: 15px 0;display: flex;justify-content:flex-end;align-items: center ">
      <div>
        <el-button v-if="isAuth('team:create')" type="primary" @click="addOrUpdateHandle()">新增</el-button>
        <el-button icon="el-icon-download" type="success" v-loading.fullscreen.lock="fullscreenLoading" @click="exportTeam()">全部导出</el-button>
      </div>
    </div>
    <el-table
        :data="dataList"
        border
        v-loading="dataListLoading"
        @selection-change="selectionChangeHandle"
        style="width: 100%;">
      <!--      <el-table-column-->
      <!--          type="selection"-->
      <!--          header-align="center"-->
      <!--          align="center"-->
      <!--          min-width="50">-->
      <!--      </el-table-column>-->
      <!--      <el-table-column-->
      <!--          prop="teamNo"-->
      <!--          header-align="center"-->
      <!--          align="center"-->
      <!--          min-width="100px"-->
      <!--          label="团队编号">-->
      <!--      </el-table-column>-->
      <el-table-column
          prop="name"
          header-align="center"
          align="center"
          show-overflow-tooltip
          min-width="250"
          label="团队名称">
        <template slot-scope="scope">
          <a style="cursor: pointer" @click="getTeamDetail(scope.row.id,false, isAuth('team:edit_self'))">{{ scope.row.name }}</a>
        </template>
      </el-table-column>
      <el-table-column
          prop="founded"
          header-align="center"
          align="center"
          width="120"
          label="成立时间">
      </el-table-column>
      <el-table-column
          header-align="center"
          align="center"
          label="管理员信息">
        <el-table-column
            prop="adminName"
            header-align="center"
            align="center"
            width="80"
            label="姓名">
        </el-table-column>
        <el-table-column
            prop="adminContact"
            header-align="center"
            align="center"
            width="120"
            label="联系方式">
        </el-table-column>
      </el-table-column>
      <el-table-column
          header-align="center"
          align="center"
          label="负责人信息">
        <el-table-column
            prop="curatorName"
            header-align="center"
            align="center"
            width="80"
            label="姓名">
        </el-table-column>
        <el-table-column
            prop="curatorContact"
            header-align="center"
            align="center"
            width="120"
            label="联系方式">
        </el-table-column>
      </el-table-column>
      <el-table-column
          prop="teamNumber"
          header-align="center"
          align="center"
          width="120"
          label="团队实际人数">
      </el-table-column>
      <el-table-column
          prop="orgName"
          header-align="center"
          width="180"
          align="center"
          label="上级组织">
      </el-table-column>
      <el-table-column
          prop="livelyCountThisYear"
          header-align="center"
          width="120"
          align="center"
          label="当年活跃人数">
      </el-table-column>
      <el-table-column
          prop="serviceLongThisYear"
          header-align="center"
          width="120"
          align="center"
          label="当年服务时长">
      </el-table-column>
      <el-table-column
          prop="activityCountThisYear"
          header-align="center"
          width="120"
          align="center"
          label="当年活动次数">
      </el-table-column>
      <el-table-column
          prop="emphasisText"
          header-align="center"
          width="100"
          align="center"
          label="重点团队">
      </el-table-column>
      <el-table-column
          prop="status"
          header-align="center"
          align="center"
          width="80"
          label="状态">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.status == false" size="small" type="danger">禁用</el-tag>
          <el-tag v-else size="small">启用</el-tag>
        </template>
      </el-table-column>
      <el-table-column
          v-if="managerCapacity && managerCapacity === 'TEAM_ADMIN'"
          prop="infoChangeAuditStatus"
          header-align="center"
          align="center"
          width="140"
          label="信息变更审核状态">
        <template slot-scope="scope">
          <el-tag v-if="!scope.row.auditType || scope.row.auditType === 0 || !scope.row.infoChangeAuditStatus" size="small" type="primary">未变更信息</el-tag>
          <el-tag v-if="scope.row.infoChangeAuditStatus && scope.row.infoChangeAuditStatus === 'team_audit_waiting'" size="small" type="warning">待审核</el-tag>
          <el-tag v-if="scope.row.infoChangeAuditStatus && scope.row.infoChangeAuditStatus === 'team_audit_pass'" size="small" type="success">审核通过</el-tag>
          <el-tag v-if="scope.row.infoChangeAuditStatus && scope.row.infoChangeAuditStatus === 'team_audit_no_pass'" size="small" type="danger">审核驳回</el-tag>
        </template>
      </el-table-column>
      <el-table-column
          prop="isSyncText"
          header-align="center"
          align="center"
          width="120"
          label="同步状态">
        <template slot-scope="scope">
          <el-popover trigger="hover" placement="top">
            <p style="text-align: center">
              {{
                '同步时间：' + (scope.row.syncTime ? scope.row.syncTime : '')
              }}<br>{{ scope.row.syncRemark }}</p>
            <div slot="reference" class="name-wrapper">
              <el-tag v-if="scope.row.isSync === 'sync_failure'" type="danger">{{ scope.row.isSyncText }}</el-tag>
              <el-tag v-if="scope.row.isSync === 'sync_wait'" type="warning">{{ scope.row.isSyncText }}</el-tag>
              <el-tag v-if="scope.row.isSync === 'sync_success'" type="success">{{ scope.row.isSyncText }}</el-tag>
            </div>
          </el-popover>
        </template>
      </el-table-column>
      <el-table-column
          fixed="right"
          header-align="center"
          align="center"
          width="180"
          label="操作">
        <template slot-scope="scope">
          <el-button type="text" size="small" @click="getVolunteerDetail(scope.row.id)">成员详情</el-button>
          <el-button v-if="isAuth('team:edit')" type="text" size="small" @click="addOrUpdateHandle(scope.row.id, false)">编辑</el-button>
          <el-button v-if="isAuth('team:edit_self') && (!scope.row.infoChangeAuditStatus || scope.row.infoChangeAuditStatus !== 'team_audit_waiting')" type="text" size="small" @click="addOrUpdateHandle(scope.row.id, true, scope.row.infoChangeAuditStatus)">信息变更</el-button>
          <el-button v-if="isAuth('team:edit_self') && scope.row.infoChangeAuditStatus && scope.row.infoChangeAuditStatus === 'team_audit_waiting'" type="text" size="small" @click="callbackInfoChange(scope.row.id)">撤回信息变更</el-button>
          <el-dropdown @command="" style="padding-left: 10px">
            <el-button type="text" size="small">更多操作</el-button>
            <el-dropdown-menu slot="dropdown" class="header-new-drop">
              <el-dropdown-item v-if="isAuth('team:important')" type="text"
                                @click.native="updateEmphasis(scope.row.id)">
                <el-button type="text" size="small">设为重点</el-button>
              </el-dropdown-item>
              <el-dropdown-item v-if="isAuth('team:enable-disable')" type="text"
                                @click.native="stopUse(scope.row.id,scope.row.status)">
                <el-button type="text" size="small">{{ scope.row.status ? '停用' : '解除停用' }}</el-button>
              </el-dropdown-item>
              <el-dropdown-item v-if="isAuth('team:sync')" type="text" @click.native="syncHandle(scope.row.id)"
                                :disabled="scope.row.isSync === 'sync_success'">
                <el-button type="text" size="small">同步</el-button>
              </el-dropdown-item>
              <el-dropdown-item v-if="isAuth('team:delete')" @click.native="deleteHandle(scope.row.id)"> <el-button type="text" size="small">删除</el-button>
              </el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
        @size-change="sizeChangeHandle"
        @current-change="currentChangeHandle"
        :current-page="pageIndex"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pageSize"
        :total="totalPage"
        layout="total, sizes, prev, pager, next, jumper">
    </el-pagination>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"></add-or-update>
    <get-volunteer-detail v-if="volunteerDetailVisible" ref="volunteerDetail"
                          @refreshDataList="getDataList"></get-volunteer-detail>
    <get-team-detail v-if="teamDetailVisible" ref="teamDetail" @refreshDataList="getDataList"></get-team-detail>
    <update-emphasis v-if="updateEmphasisVisible" ref="updateEmphasis" @refreshDataList="getDataList"></update-emphasis>
  </div>
</template>

<script>
import AddOrUpdate from './zyz-team-add-or-update'
import GetVolunteerDetail from './team-volunteer-detail'
import GetTeamDetail from './zyz-team-detail'
import UpdateEmphasis from './change-emphasis'


export default {
  data() {
    return {
      dataForm: {
        teamNo: null,
        name: null,
        admin: null,
        curator: null,
        isEmphasis: null,
        status: null,
        orgCodeList: [],
      },
      managerCapacity: this.$store.state.user.managerCapacity,
      fullscreenLoading: false,
      orgList: [],
      dataList: [],
      pageIndex: 1,
      pageSize: 10,
      totalPage: 0,
      dataListLoading: false,
      dataListSelections: [],
      addOrUpdateVisible: false,
      volunteerDetailVisible: false,
      teamDetailVisible: false,
      updateEmphasisVisible: false
    }
  },
  components: {
    AddOrUpdate,
    GetVolunteerDetail,
    GetTeamDetail,
    UpdateEmphasis
  },
  activated() {
    this.queryPage()
    this.getOrg()
  },
  methods: {
    queryPage() {
      this.pageIndex = 1
      this.getDataList()
    },

    // 重置
    resetForm() {
      this.$refs['dataForm']?.resetFields()
      this.getDataList()
    },
    // 获取数据列表
    getDataList() {
      this.dataListLoading = true
      this.$http({
        url: this.$http.adornUrl('/admin/zyz/team/getPassTeamPages'),
        method: 'post',
        data: this.$http.adornData({
          'currentPage': this.pageIndex,
          'pageSize': this.pageSize,
          'isEmphasis': this.dataForm.isEmphasis,
          'orgCode': this.dataForm.orgCodeList && this.dataForm.orgCodeList.length > 0 ? this.dataForm.orgCodeList[this.dataForm.orgCodeList.length - 1] : null,
          'name': this.dataForm.name,
          'admin': this.dataForm.admin,
          'curator': this.dataForm.curator,
          'teamNo': this.dataForm.teamNo,
          'status': this.dataForm.status
        })
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.dataList = data.obj.records
          this.totalPage = data.obj.total
        } else {
          this.dataList = []
          this.totalPage = 0
        }
        this.dataListLoading = false
      })
    },
    // 同步
    syncHandle(teamId) {
      this.$confirm(`确定要进行同步嘛，请勿重复操作，否则可能会带来不可预知的后果`, '确认同步？', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        closeOnClickModal: false
      }).then(() => {
        this.dataListLoading = true
        this.$http({
          url: this.$http.adornUrl('/admin/zyz/platform-sync/syncTeam'),
          method: 'post',
          params: this.$http.adornParams({
            'teamId': teamId
          })
        }).then(({data}) => {
          this.dataListLoading = false
          if (data && data.code === 0) {
            this.$message({
              message: '请求同步成功，市平台返回：（' + data.obj.message + '）',
              type: data.obj.resultCode === 'SUCCESS' ? 'success' : 'warning',
              duration: 3000,
              onClose: () => {
                this.getDataList()
              }
            })
          } else {
            this.$message.error(data.msg)
          }
        })
      })
    },
    // 冻结
    stopUse(id, Status) {
      this.$confirm(`[${Status ? '停用' : '解除停用'}]后该账号${Status ? '无法' : '可'}正常使用本平台，确认是否操作`, `${Status ? '停用' : '解除停用'}`, {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        closeOnClickModal: false
      }).then(() => {
        this.$http({
          url: this.$http.adornUrl('/admin/zyz/team/stopUse'),
          method: 'get',
          params: this.$http.adornParams({
            'id': id
          })
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.$message({
              message: '操作成功',
              type: 'success',
              duration: 1500,
              onClose: () => {
                this.getDataList()
              }
            })
          } else {
            this.$message.error(data.msg)
          }
        })
      })
    },
    // 每页数
    sizeChangeHandle(val) {
      this.pageSize = val
      this.pageIndex = 1
      this.getDataList()
    },
    // 当前页
    currentChangeHandle(val) {
      this.pageIndex = val
      this.getDataList()
    },
    // 多选
    selectionChangeHandle(val) {
      this.dataListSelections = val
    },
    // 新增 / 修改
    addOrUpdateHandle(id, self) {
      this.addOrUpdateVisible = true
      this.$nextTick(() => {
        this.$refs.addOrUpdate.init(id, self)
      })
    },
    callbackInfoChange(id) {
      this.$confirm(`确定撤回信息变更?`, '提示',
          {confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning', closeOnClickModal: false}
      ).then(() => {
        this.$http({
          url: this.$http.adornUrl('/admin/zyz/team/callbackInfoChange'),
          method: 'get',
          params: this.$http.adornParams({
            'id': id
          })
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.$message({
              message: '操作成功',
              type: 'success',
              duration: 1500,
              onClose: () => {
                this.getDataList()
              }
            })
          } else {
            this.$message.error(data.msg)
          }
        })
      })
    },
    updateEmphasis(id) {
      this.updateEmphasisVisible = true
      this.$nextTick(() => {
        this.$refs.updateEmphasis.init(id)
      })
    },
    getTeamDetail(id, isAudit, isLog) {
      this.teamDetailVisible = true
      this.$nextTick(() => {
        this.$refs.teamDetail.init(id, isAudit, isLog)
      })
    },
    getVolunteerDetail(id) {
      this.volunteerDetailVisible = true
      this.$nextTick(() => {
        this.$refs.volunteerDetail.init(id)
      })
    },
    //获取所属区域
    getOrg() {
      this.$http({
        url: this.$http.adornUrl(`/admin/org/getOrgTree`),
        method: 'get',
        params: this.$http.adornParams()
      }).then(({data}) => {
        if (data && data.code === 0) {
          // this.orgList = this.getTreeData(data.obj)
          this.orgList = data.obj
        } else {
          this.orgList = []
        }
      })
    },
    // 删除
    deleteHandle(id) {
      var ids = id ? [id] : this.dataListSelections.map(item => {
        return item.id
      })
      this.$confirm(`确定进行[${id ? '删除' : '批量删除'}]操作?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        closeOnClickModal: false
      }).then(() => {
        this.$http({
          url: this.$http.adornUrl('/admin/zyz/team/removeTeam'),
          method: 'get',
          params: this.$http.adornParams({
            'ids': ids.join(',')
          })
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.$message({
              message: '操作成功',
              type: 'success',
              duration: 1500,
              onClose: () => {
                this.getDataList()
              }
            })
          } else {
            this.$message.error(data.msg)
          }
        })
      })
    },
    // 下载模板文件
    exportTeam() {
      this.fullscreenLoading = true;
      this.$http({
        url: this.$http.adornUrl('/admin/zyz/team/exportTeam'),
        method: 'post',
        responseType: 'arraybuffer',
        data: this.$http.adornData({
          'isEmphasis': this.dataForm.isEmphasis,
          'orgCode': this.dataForm.orgCodeList && this.dataForm.orgCodeList.length > 0 ? this.dataForm.orgCodeList[this.dataForm.orgCodeList.length - 1] : null,
          'name': this.dataForm.name,
          'admin': this.dataForm.admin,
          'curator': this.dataForm.curator,
          'teamNo': this.dataForm.teamNo,
          'status': this.dataForm.status
        })
      }).then(({data}) => {
        if (data.code && data.code !== 0) {
          this.$message.error('导出失败')
        } else {
          this.fullscreenLoading = false;
          let blob = new Blob([data], {type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8'})
          let objectUrl = URL.createObjectURL(blob)
          let a = document.createElement('a')
          // window.location.href = objectUrl
          a.href = objectUrl
          a.download = '团队列表明细.xlsx'
          a.click()
          URL.revokeObjectURL(objectUrl)
          this.$message({
            type: 'success',
            message: '导出成功'
          })
        }
      })
    }
  }
}
</script>
