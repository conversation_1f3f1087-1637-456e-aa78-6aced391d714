<template>
  <el-dialog
    :title="!dataForm.id ? '新增' : '修改'"
    :close-on-click-modal="false"
    :visible.sync="visible">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmit()" label-width="80px">
      <el-row :gutter="20">
        <el-col :span="12">
           <el-form-item label="志愿者id" prop="volunteerId" :error="volunteerIdError">
                <el-input v-model="dataForm.volunteerId" placeholder="志愿者id"></el-input>
            </el-form-item>
        </el-col>
            <el-col :span="12">
           <el-form-item label="团队Id" prop="teamId" :error="teamIdError">
                <el-input v-model="dataForm.teamId" placeholder="团队Id"></el-input>
            </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
           <el-form-item label="团队成员审核状态，待审核，已通过，已驳回" prop="status" :error="statusError">
                <el-input v-model="dataForm.status" placeholder="团队成员审核状态，待审核，已通过，已驳回"></el-input>
            </el-form-item>
        </el-col>
            <el-col :span="12">
           <el-form-item label="原表id" prop="uuId" :error="uuIdError">
                <el-input v-model="dataForm.uuId" placeholder="原表id"></el-input>
            </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
           <el-form-item label="志愿者uuId" prop="volunteerUuId" :error="volunteerUuIdError">
                <el-input v-model="dataForm.volunteerUuId" placeholder="志愿者uuId"></el-input>
            </el-form-item>
        </el-col>
        </el-row>
        </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
  export default {
    data () {
      return {
        visible: false,
        dataForm: {
          id: null,
          version: null,
          volunteerId: '',
          teamId: '',
          status: '',
          uuId: '',
          volunteerUuId: ''
        },
        dataRule: {
          teamId: [
            { required: true, message: '团队Id不能为空', trigger: 'blur' }
          ],
          status: [
            { required: true, message: '团队成员审核状态，待审核，已通过，已驳回不能为空', trigger: 'blur' }
          ],
          uuId: [
            { required: true, message: '原表id不能为空', trigger: 'blur' }
          ],
          volunteerUuId: [
            { required: true, message: '志愿者uuId不能为空', trigger: 'blur' }
          ]
        },
        volunteerIdError: null,
        teamIdError: null,
        statusError: null,
        uuIdError: null,
        volunteerUuIdError: null
      }
    },
    methods: {
      init (id) {
        this.dataForm.id = id || null
        this.visible = true
        this.$nextTick(() => {
          this.$refs['dataForm']?.resetFields()
          if (this.dataForm.id) {
            this.$http({
              url: this.$http.adornUrl(`/admin/zyz/volunteer/team/audit`),
              method: 'get',
              params: this.$http.adornParams({ id: this.dataForm.id })
            }).then(({data}) => {
              if (data && data.code === 0) {
                this.dataForm = data.obj
              }
            })
          }
        })
      },
      // 表单提交
      dataFormSubmit () {
        this.$refs['dataForm'].validate((valid) => {
          if (valid) {
            this.$http({
              url: this.$http.adornUrl(`/admin/zyz/volunteer/team/audit/${!this.dataForm.id ? 'save' : 'update'}`),
              method: 'post',
              data: this.$http.adornData(this.dataForm)
            }).then(({data}) => {
              if (data && data.code === 0) {
                this.$message({
                  message: '操作成功',
                  type: 'success',
                  duration: 1500,
                  onClose: () => {
                    this.visible = false
                    this.$emit('refreshDataList')
                  }
                })
              } else if (data && data.code === 303) {
                for (let it of data.obj) {
                  this[`${it.field}Error`] = it.message
                }
              } else {
                this.$message.error(data.msg)
              }
            })
          }
        })
      }
    }
  }
</script>
