<template>
  <el-dialog
      :title="getDialogTitle()"
      :close-on-click-modal="false"
      :visible.sync="visible"
      width="40%">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmit()"
             label-width="100px">
      <el-form-item label="分类名称" prop="name" :error="errors['name']">
        <el-input v-model="dataForm.name" placeholder="请输入分类名称" :disabled="readonly"></el-input>
      </el-form-item>
      <el-form-item label="分类说明" prop="description" :error="errors['description']">
        <el-input type="textarea" :rows="4" v-model="dataForm.description" placeholder="请输入分类说明" :disabled="readonly"></el-input>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">{{ readonly ? '关闭' : '取消' }}</el-button>
      <el-button v-if="!readonly" type="primary" @click="dataFormSubmit()">保存</el-button>
    </span>
  </el-dialog>
</template>

<script>
import editMixin from '@/mixins/edit-mixins'

export default {
  mixins: [editMixin],
  data() {
    return {
      readonly: false,
      editOptions: {
        initUrl: '/admin/zyz/certificate/category'
      },
      initForm: {
        name: '', 
        description: ''
      },
      dataRule: {
        name: [
          {required: true, message: '分类名称不能为空', trigger: 'blur'}
        ],
        description: [
          {required: true, message: '分类说明不能为空', trigger: 'blur'}
        ]
      }
    }
  },
  methods: {
    getDialogTitle() {
      if (this.readonly) {
        return '查看分类详情'
      }
      return !this.dataForm.id ? '新增分类' : '修改分类'
    },
    async init(id, isReadonly = false) {
      this.readonly = isReadonly
      this.dataForm = this.initForm
      this.dataForm.id = id || null
      if (!id) {
        this.dataForm.createDate = null
        this.dataForm.creator = null
        this.dataForm.version = 0
      }
      await this.initBeforeHandle()
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm']?.resetFields()
        if (this.dataForm.id) {
          this.getData()
        }
      })
    },
  }
}
</script>
