<template>
    <div class="mod-config">
        <el-form :inline="true" :model="dataForm" ref="dataForm" @keyup.enter.native="queryPage()">
            <el-form-item label="志愿者姓名" prop="name">
                <el-input v-model="dataForm.name" placeholder="请输入志愿者姓名" clearable></el-input>
            </el-form-item>
            <el-form-item>
                <el-button icon="el-icon-search" type="warning" @click="queryPage()">查询</el-button>
                <el-button @click="resetForm()" icon="el-icon-refresh-left">重置</el-button>
            </el-form-item>
        </el-form>
        <div class="f-s-c" style="padding: 15px 0;display: flex;justify-content:flex-end;align-items: center ">
            <div>
                <el-button icon="el-icon-download" type="success" v-loading.fullscreen.lock="fullscreenLoading"
                           @click="exportTeamApply()">全部导出
                </el-button>
            </div>
        </div>
        <el-table
                :data="dataList"
                border
                v-loading="dataListLoading"
                @selection-change="selectionChangeHandle"
                style="width: 100%;">
            <el-table-column
                    prop="volunteerName"
                    header-align="center"
                    align="center"
                    label="团队成员">
            </el-table-column>
            <el-table-column
                    prop="phone"
                    header-align="center"
                    align="center"
                    label="成员手机号">
            </el-table-column>
            <el-table-column
                    prop="activityName"
                    header-align="center"
                    align="center"
                    label="活动名称">
            </el-table-column>
            <el-table-column
                    prop="timeRange"
                    header-align="center"
                    width="200"
                    align="center"
                    label="活动开始结束时间">
            </el-table-column>
            <el-table-column
                    prop="address"
                    header-align="center"
                    width="400"
                    align="center"
                    label="活动地址">
            </el-table-column>
            <el-table-column
                    prop="signTime"
                    header-align="center"
                    width="200"
                    align="center"
                    label="签到时间">
            </el-table-column>
            <el-table-column
                prop="serviceLong"
                header-align="center"
                align="center"
                label="服务时长">
            </el-table-column>
        </el-table>
        <el-pagination
                @size-change="sizeChangeHandle"
                @current-change="currentChangeHandle"
                :current-page="pageIndex"
                :page-sizes="[10, 20, 50, 100]"
                :page-size="pageSize"
                :total="totalPage"
                layout="total, sizes, prev, pager, next, jumper">
        </el-pagination>
        <!-- 弹窗, 新增 / 修改 -->
    </div>
</template>

<script>
import moment from 'moment'

export default {
    data() {
        return {
            dataForm: {
                name: null,
            },
            managerCapacity: this.$store.state.user.managerCapacity,
            fullscreenLoading: false,
            dataList: [],
            pageIndex: 1,
            pageSize: 10,
            totalPage: 0,
            dataListLoading: false,
            dataListSelections: []
        }
    },
    components: {},
    activated() {
        this.queryPage()
    },
    methods: {
        queryPage() {
            this.pageIndex = 1
            this.getDataList()
        },

        // 重置
        resetForm() {
            this.$refs['dataForm']?.resetFields()
            this.getDataList()
        },
        // 获取数据列表
        getDataList() {
            this.dataListLoading = true
            this.$http({
                url: this.$http.adornUrl('/admin/zyz/volunteer/team/getTeamVolunteerApplyList'),
                method: 'post',
                data: this.$http.adornData({
                    'currentPage': this.pageIndex,
                    'pageSize': this.pageSize,
                    'name': this.dataForm.name
                })
            }).then(({data}) => {
                if (data && data.code === 0) {
                    this.dataList = data.obj.records
                    this.totalPage = data.obj.total
                    this.dataList.forEach(item => {
                        item.timeRange = moment(item.timePeriodStartTime).format('YYYY-MM-DD HH:mm') + '-' + moment(item.timePeriodEndTime).format('HH:mm')
                    })
                } else {
                    this.dataList = []
                    this.totalPage = 0
                }
                this.dataListLoading = false
            })
        },
        // 每页数
        sizeChangeHandle(val) {
            this.pageSize = val
            this.pageIndex = 1
            this.getDataList()
        },
        // 当前页
        currentChangeHandle(val) {
            this.pageIndex = val
            this.getDataList()
        },
        // 多选
        selectionChangeHandle(val) {
            this.dataListSelections = val
        },
        // 导出
        exportTeamApply() {
            this.fullscreenLoading = true;
            this.$http({
                url: this.$http.adornUrl('/admin/zyz/volunteer/team/exportTeamApply'),
                method: 'post',
                responseType: 'arraybuffer',
                data: this.$http.adornData({
                    'name': this.dataForm.name
                })
            }).then(({data}) => {
                if (data.code && data.code !== 0) {
                    this.$message.error('导出失败')
                } else {
                    this.fullscreenLoading = false;
                    let blob = new Blob([data], {type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8'})
                    let objectUrl = URL.createObjectURL(blob)
                    let a = document.createElement('a')
                    // window.location.href = objectUrl
                    a.href = objectUrl
                    a.download = '团队报名明细.xlsx'
                    a.click()
                    URL.revokeObjectURL(objectUrl)
                    this.$message({
                        type: 'success',
                        message: '导出成功'
                    })
                }
            })
        }
    }
}
</script>
