<template>
    <el-dialog title='详情' :close-on-click-modal="false" :visible.sync="visible"
               width="60%">
        <el-form :model="dataForm" :rules="dataRule" ref="dataForm" label-width="150px">
            <el-row :gutter="20">
                <el-col :span="12">
                    <el-form-item label="志愿者姓名" prop="volunteerName">
                        <el-input v-model="dataForm.volunteerName" placeholder="志愿者姓名"></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="电话" prop="phone">
                        <el-input v-model="dataForm.phone" placeholder="电话"></el-input>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row :gutter="20">
                <el-col :span="12">
                    <el-form-item label="证件类型" prop="certificateType">
                        <el-dict :code="'certificate_type'" v-model="dataForm.certificateType"></el-dict>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="身份证号" prop="certificateId">
                        <el-input v-model="dataForm.certificateId" placeholder="身份证号"></el-input>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row :gutter="20">
                <el-col :span="12">
                    <el-form-item label="证书名称" prop="awardCertificateName">
                        <el-input v-model="dataForm.awardCertificateName" placeholder="证书名称"></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="获得时间" prop="receiveDate">
                        <el-input v-model="dataForm.receiveDate" placeholder="获得时间"></el-input>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row :gutter="20">
                <el-col :span="24">
                    <el-form-item label="证书PDF地址" prop="pdfUrl">
                        <el-input v-model="dataForm.pdfUrl" placeholder="证书PDF地址" ></el-input>
                    </el-form-item>
                </el-col>

            </el-row>
            <el-row :gutter="20">
                <el-col :span="24">
                    <el-form-item label="证书图片地址" prop="imgUrl">
                        <el-input v-model="dataForm.imgUrl" placeholder="证书图片地址" ></el-input>
                    </el-form-item>
                </el-col>

            </el-row>
        </el-form>

    </el-dialog>
</template>

<script>

export default {
    data() {
        return {
            visible: false,
            posed: false,
            dataForm: {
                id: null,
                version: null,
                volunteerId: null,
                volunteerName: null,
                awardCertificateId: null,
                awardCertificateName: null,
                receiveDate: '',
                pdfUrl: '',
                imgUrl: ''
            },
            cerList: [],
            dataRule: {
                volunteerId: [
                    {required: true, message: '志愿者id不能为空', trigger: 'blur'}
                ],
                volunteerName: [
                    {required: true, message: '志愿者姓名不能为空', trigger: 'blur'}
                ],
                awardCertificateId: [
                    {required: true, message: '证书id不能为空', trigger: 'blur'}
                ],
                awardCertificateName: [
                    {required: true, message: '证书名称不能为空', trigger: 'blur'}
                ],
                receiveDate: [
                    {required: true, message: '获得时间不能为空', trigger: 'blur'}
                ]
            }
        }
    },
    components: {},
    methods: {
        init(id) {
            this.dataForm.id = id || null
            this.visible = true
            this.getCertificateList()
            this.$nextTick(() => {
                this.$refs['dataForm']?.resetFields()
                if (this.dataForm.id) {
                    this.$http({
                        url: this.$http.adornUrl(`/admin/zyz/volunteer/certificate`),
                        method: 'get',
                        params: this.$http.adornParams({id: this.dataForm.id})
                    }).then(({data}) => {
                        if (data && data.code === 0) {
                            this.dataForm = data.obj
                            if(this.dataForm.pdfUrl){
                                this.dataForm.pdfUrl = this.$http.adornAttachmentUrl(this.dataForm.pdfUrl)
                            }
                            if(this.dataForm.imgUrl){
                                this.dataForm.imgUrl = this.$http.adornAttachmentUrl(this.dataForm.imgUrl)
                            }
                        }
                    })
                }
            })
        },
        // 表单提交
        dataFormSubmit() {
            this.$refs['dataForm'].validate((valid) => {
                if (valid) {
                    this.$http({
                        url: this.$http.adornUrl(`/admin/zyz/volunteer/certificate/saveOrUpdateCer`),
                        method: 'post',
                        data: this.$http.adornData(this.dataForm)
                    }).then(({data}) => {
                        if (data && data.code === 0) {
                            this.$message({
                                message: '操作成功',
                                type: 'success',
                                duration: 1500,
                                onClose: () => {
                                    this.visible = false
                                    this.$emit('refreshDataList')
                                }
                            })
                        } else if (data && data.code === 303) {
                            for (let it of data.obj) {
                                this[`${it.field}Error`] = it.message
                            }
                        } else {
                            this.$message.error(data.msg)
                        }
                    })
                }
            })
        },
        getCertificateList() {
            this.$http({
                url: this.$http.adornUrl(`/admin/zyz/certificate/all`),
                method: 'get',
                params: this.$http.adornParams()
            }).then(({data}) => {
                if (data) {
                    this.cerList = data
                } else {
                    this.cerList = []
                }
            })
        }
    }
}
</script>
