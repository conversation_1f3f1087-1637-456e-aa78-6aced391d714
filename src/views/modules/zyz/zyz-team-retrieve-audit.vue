<template>
  <div class="mod-config">
    <el-form :inline="true" :model="dataForm" ref="dataForm" @keyup.enter.native="queryPage()">
      <el-form-item label="团队名称" prop="name">
        <el-input v-model="dataForm.name" placeholder="请输入团队名称" clearable></el-input>
      </el-form-item>
      <el-form-item label="管理员" prop="admin">
        <el-input style="width: 250px" v-model="dataForm.admin" placeholder="请输入管理员名称或联系方式" clearable></el-input>
      </el-form-item>
      <el-form-item label="负责人" prop="curator">
        <el-input style="width: 250px" v-model="dataForm.curator" placeholder="请输入负责人名称或联系方式" clearable></el-input>
      </el-form-item>
      <el-form-item label="上级组织" prop="orgCodeList">
        <el-cascader
            placeholder="请选择上级组织"
            v-model="dataForm.orgCodeList"
            :options="orgList"
            :show-all-levels="false"
            clearable
            :props="{checkStrictly: true,value: 'code',label: 'name'}"
        ></el-cascader>
      </el-form-item>
      <el-form-item v-if="!dataForm.needAudit" label="审核状态" prop="teamStatus">
        <el-select class="width185" v-model="dataForm.teamStatus" clearable
                   placeholder="请选择" style="width: 100%">
          <el-option
              v-for="item in teamStatusList"
              :key="item.code"
              :label="item.name"
              :value="item.code">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="是否加急" prop=" hurry">
        <el-select v-model="dataForm.hurry" clearable
                   placeholder="请选择" style="width: 100%">
          <el-option
              v-for="item in [{code: true, name: '加急数据'}, {code: false, name: '非加急数据'}]"
              :key="item.code"
              :label="item.name"
              :value="item.code">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item  prop="needAudit">
        <el-checkbox v-model="dataForm.needAudit">需要我审核的</el-checkbox>
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-search" type="warning" @click="queryPage()">查询</el-button>
        <el-button @click="resetForm()" icon="el-icon-refresh-left">重置</el-button>
        <!--        <el-button  type="danger" @click="deleteHandle()" :disabled="dataListSelections.length <= 0">批量删除</el-button>-->
      </el-form-item>
    </el-form>
    <div class="f-s-c" style="padding: 15px 0;display: flex; justify-content: space-between">
      <div style="display: flex; justify-content: flex-start; color: red">*<div style="background-color: #f5d2a5; width: 40px; height: 16px"></div>标识的团队审核数据为加急数据，请优先处理！</div>
      <div>
        <el-button icon="el-icon-s-check" type="primary" @click="audit()" :disabled="dataListSelections.length <= 0">批量审核</el-button>
        <!--        <el-button type="danger" @click="deleteHandle()" :disabled="dataListSelections.length <= 0">批量删除</el-button>-->
      </div>
    </div>
    <el-table
        :data="dataList"
        border
        v-loading="dataListLoading"
        @selection-change="selectionChangeHandle"
        :row-class-name="tableRowClassName"
        style="width: 100%;">
      <el-table-column
          type="selection"
          header-align="center"
          align="center"
          :selectable="checkSelectable"
          width="50">
      </el-table-column>
      <el-table-column
          prop="name"
          header-align="center"
          align="center"
          min-width="250"
          show-overflow-tooltip
          label="团队名称">
        <template slot-scope="scope">
          <a style="cursor: pointer" @click="getTeamDetail(scope.row.id,false)">{{ scope.row.name }}</a>
        </template>
      </el-table-column>
      <el-table-column
          prop="createDate"
          header-align="center"
          align="center"
          width="160"
          label="申请时间">
      </el-table-column>
      <el-table-column
          header-align="center"
          align="center"
          label="管理员信息">
        <el-table-column
            prop="adminName"
            header-align="center"
            align="center"
            width="80"
            label="姓名">
        </el-table-column>
        <el-table-column
            prop="adminContact"
            header-align="center"
            align="center"
            width="120"
            label="联系方式">
        </el-table-column>
      </el-table-column>
      <el-table-column
          header-align="center"
          align="center"
          label="负责人信息">
        <el-table-column
            prop="curatorName"
            header-align="center"
            align="center"
            width="80"
            label="姓名">
        </el-table-column>
        <el-table-column
            prop="curatorContact"
            header-align="center"
            align="center"
            width="120"
            label="联系方式">
        </el-table-column>
      </el-table-column>
      <el-table-column
          prop="orgName"
          header-align="center"
          width="180"
          align="center"
          label="上级组织">
      </el-table-column>
      <el-table-column
          prop="founded"
          header-align="center"
          align="center"
          width="120"
          label="成立时间">
      </el-table-column>
      <el-table-column
          prop="teamStatusText"
          header-align="center"
          align="center"
          width="100"
          label="审核状态">
      </el-table-column>
      <el-table-column
          prop="auditOrgName"
          header-align="center"
          width="180"
          align="center"
          label="当前审核组织">
      </el-table-column>
      <el-table-column
          fixed="right"
          header-align="center"
          align="center"
          width="150"
          label="操作">
        <template slot-scope="scope">
          <el-button type="text" size="small"
                     @click="getTeamDetail(scope.row.id,false)">详情
          </el-button>
          <el-button v-if="scope.row.canAudit" type="text" size="small"
                     @click="getTeamDetail(scope.row.id,true)">审核
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
        @size-change="sizeChangeHandle"
        @current-change="currentChangeHandle"
        :current-page="pageIndex"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pageSize"
        :total="totalPage"
        layout="total, sizes, prev, pager, next, jumper">
    </el-pagination>
    <!-- 弹窗, 新增 / 修改 -->
    <get-team-detail v-if="teamDetailVisible" ref="teamDetail" @refreshDataList="getDataList"></get-team-detail>
    <audit v-if="auditVisible" ref="audit" @refreshDataList="getDataList"></audit>
  </div>
</template>

<script>
import GetTeamDetail from "./zyz-team-retrieve-detail";
import Audit from "./team-retrieve-audit-no-pass";

export default {
  data() {
    return {
      dataForm: {
        name: null,
        admin: null,
        curator: null,
        needAudit: true,
        orgCodeList: [],
        teamStatus: null,
        hurry: null
      },
      dataList: [],
      auditVisible: false,
      orgList: [],
      teamStatusList: [],
      pageIndex: 1,
      pageSize: 10,
      totalPage: 0,
      dataListLoading: false,
      dataListSelections: [],
      addOrUpdateVisible: false,
      teamDetailVisible: false
    }
  },
  components: {
    GetTeamDetail,
    Audit
  },
  activated() {
    this.queryPage()
    this.getOrg()
    this.getTeamStatus()
  },
  methods: {
    queryPage() {
      this.pageIndex = 1
      this.getDataList()
    },
    // 获取数据列表
    getDataList() {
      this.dataListLoading = true
      this.$http({
        url: this.$http.adornUrl('/admin/zyz/team/retrieve/getPages'),
        method: 'post',
        data: this.$http.adornData({
          'currentPage': this.pageIndex,
          'pageSize': this.pageSize,
          'name': this.dataForm.name,
          'needAudit': this.dataForm.needAudit,
          'admin': this.dataForm.admin,
          'curator': this.dataForm.curator,
          'orgCode': this.dataForm.orgCodeList && this.dataForm.orgCodeList.length > 0 ? this.dataForm.orgCodeList[this.dataForm.orgCodeList.length - 1] : null,
          'teamStatus': this.dataForm.teamStatus,
          'hurry': this.dataForm.hurry
        })
      }).then(({data}) => {
        if (data && data.code === 0) {
          data.obj.records.forEach((it) => {
            it.canAudit = it.teamStatus === 'team_audit_waiting' && it.auditOrgCode === this.$store.state.user.orgCode
          })
          this.dataList = data.obj.records
          this.totalPage = data.obj.total
        } else {
          this.dataList = []
          this.totalPage = 0
        }
        this.dataListLoading = false
      })
    },
    tableRowClassName (row, rowIndex) {
      if (row.row.hurry && row.row.hurry === true) {
        return 'warning-row';
      } else {
        return '';
      }
    },
    checkSelectable(row, index) {
      return row.canAudit
    },
    // 每页数
    sizeChangeHandle(val) {
      this.pageSize = val
      this.pageIndex = 1
      this.getDataList()
    },
    // 当前页
    currentChangeHandle(val) {
      this.pageIndex = val
      this.getDataList()
    },
    // 多选
    selectionChangeHandle(val) {
      this.dataListSelections = val
    },
    getTeamDetail(id, isAudit) {
      this.teamDetailVisible = true
      this.$nextTick(() => {
        this.$refs.teamDetail.init(id, isAudit, true)
      })
    },
    audit(id) {
      let ids = id ? [id] : this.dataListSelections.map(item => {
        return item.id
      })
      this.auditVisible = true
      this.$nextTick(() => {
        this.$refs.audit.init(ids.join(','), null, true)
      })
    },
    // 重置
    resetForm() {
      this.$refs['dataForm']?.resetFields()
      this.getDataList()
    },
    getTeamStatus() {
      this.$http({
        url: this.$http.adornUrl(`/admin/dict/parent`),
        method: 'get',
        params: this.$http.adornParams({ code: 'team_audit_type' })
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.teamStatusList = data.obj || []
          this.teamStatusList.push({code: 'team_audit_wait_check', name: '待核实'})
        } else {
          this.teamStatusList = []
        }
      })
    },
    //获取所属区域
    getOrg() {
      this.$http({
        url: this.$http.adornUrl(`/admin/org/getOrgTree`),
        method: 'get',
        params: this.$http.adornParams()
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.orgList = data.obj
        } else {
          this.orgList = []
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.el-table::v-deep .warning-row {
  background: #f5d2a5 !important;
}
</style>
