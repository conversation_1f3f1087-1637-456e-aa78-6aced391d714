<template>
  <div class="mod-config">
    <el-form :inline="true" :model="dataForm" ref="dataForm" @keyup.enter.native="queryPage()">
      <el-form-item label="类型名称" prop="name">
        <el-input v-model="dataForm.name" placeholder="类型名称" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-search" type="warning" @click="queryPage()">查询</el-button>
        <el-button @click="resetForm()" icon="el-icon-refresh-left">重置</el-button>
      </el-form-item>
    </el-form>
    <div class="f-s-c" style="padding: 15px 0;display: flex;justify-content:flex-end;align-items: center ">
      <div>
        <!--        <el-button v-if="isAuth('team:create')" type="primary" @click="addOrUpdateHandle()">新增</el-button>-->
        <el-button icon="el-icon-plus"  type="primary" @click="addOrUpdateHandle()">新增</el-button>
        <el-button icon="el-icon-delete"  type="danger" @click="deleteHandle()" :disabled="dataListSelections.length <= 0">批量删除</el-button>
      </div>
    </div>
    <el-table
      :data="dataList"
      border
      v-loading="dataListLoading"
      @selection-change="selectionChangeHandle"
      style="width: 100%;">
      <el-table-column
        type="selection"
        header-align="center"
        align="center"
        width="50">
      </el-table-column>
       <el-table-column
        prop="name"
        header-align="center"
        align="center"
        label="类型名称">
      </el-table-column>
      <el-table-column
          prop="photo"
          header-align="center"
          align="center"
          label="图标">
        <template slot-scope="scope">
<!--          <img width="50%" :src="$http.adornUrl(scope.row.photo)" class="avatar">-->
<!--          <el-popover placement="right" title trigger="hover">-->
<!--            <img :src="$http.adornUrl(scope.row.photo)" />-->
            <img
                slot="reference"
                :src="$http.adornAttachmentUrl(scope.row.photo)"
                style="max-height: 130px;max-width: 130px"
            />
<!--          </el-popover>-->
        </template>
      </el-table-column>
         <el-table-column
        prop="status"
        header-align="center"
        align="center"
        label="状态">
        <template slot-scope="scope">
            <el-tag v-if="scope.row.status == false" size="small" type="danger">禁用</el-tag>
            <el-tag v-else size="small">启用</el-tag>
        </template>
     </el-table-column>
      <el-table-column
          prop="createDate"
          header-align="center"
          align="center"
          label="发布时间">
      </el-table-column>
      <el-table-column
          prop="teamCount"
          header-align="center"
          align="center"
          label="团队数量">
      </el-table-column>
        <el-table-column
        fixed="right"
        header-align="center"
        align="center"
        width="150"
        label="操作">
        <template slot-scope="scope">
          <el-button type="text" size="small" @click="getDetail(scope.row.id)">团队列表</el-button>
          <el-button type="text" size="small" @click="addOrUpdateHandle(scope.row.id)">编辑</el-button>
          <el-button type="text" size="small" @click="deleteHandle(scope.row.id)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      @size-change="sizeChangeHandle"
      @current-change="currentChangeHandle"
      :current-page="pageIndex"
      :page-sizes="[10, 20, 50, 100]"
      :page-size="pageSize"
      :total="totalPage"
      layout="total, sizes, prev, pager, next, jumper">
    </el-pagination>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"></add-or-update>
    <get-detail v-if="getDetailVisible" ref="getDetail" @refreshDataList="getDataList"></get-detail>
  </div>
</template>

<script>
  import AddOrUpdate from './zyz-team-type-add-or-update'
  import GetDetail from './zyz-team-type-detail'


  export default {
    data () {
      return {
        dataForm: {
          name: ''
        },
        dataList: [],
        pageIndex: 1,
        pageSize: 10,
        totalPage: 0,
        dataListLoading: false,
        dataListSelections: [],
        addOrUpdateVisible: false,
        getDetailVisible: false
      }
    },
    components: {
      AddOrUpdate,
      GetDetail
    },
    activated () {
      this.queryPage()
    },
    methods: {
      // 重置
      resetForm() {
        this.$refs['dataForm']?.resetFields()
        this.getDataList()
      },
      queryPage () {
        this.pageIndex = 1
        this.getDataList()
      },
      // 获取数据列表
      getDataList () {
        this.dataListLoading = true
        this.$http({
          url: this.$http.adornUrl('/admin/zyz/team/type/getPages'),
          method: 'post',
          data: this.$http.adornData({
            'currentPage': this.pageIndex,
            'pageSize': this.pageSize,
            'name': this.dataForm.name
          })
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.dataList = data.obj.records
            this.totalPage = data.obj.total
          } else {
            this.dataList = []
            this.totalPage = 0
          }
          this.dataListLoading = false
        })
      },
      // 每页数
      sizeChangeHandle (val) {
        this.pageSize = val
        this.pageIndex = 1
        this.getDataList()
      },
      // 当前页
      currentChangeHandle (val) {
        this.pageIndex = val
        this.getDataList()
      },
      // 多选
      selectionChangeHandle (val) {
        this.dataListSelections = val
      },
      // 新增 / 修改
      addOrUpdateHandle (id) {
        this.addOrUpdateVisible = true
        this.$nextTick(() => {
          this.$refs.addOrUpdate.init(id)
        })
      },
      getDetail (id) {
        this.getDetailVisible = true
        this.$nextTick(() => {
          this.$refs.getDetail.init(id)
        })
      },
      // 删除
      deleteHandle (id) {
        var ids = id ? [id] : this.dataListSelections.map(item => {
          return item.id
        })
        this.$confirm(`确定进行[${id ? '删除' : '批量删除'}]操作?`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
          closeOnClickModal: false
        }).then(() => {
          this.$http({
            url: this.$http.adornUrl('/admin/zyz/team/type/removeByIds'),
            method: 'get',
            params: this.$http.adornParams({
              'ids': ids.join(',')
            })
          }).then(({data}) => {
            if (data && data.code === 0) {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 1500,
                onClose: () => {
                  this.getDataList()
                }
              })
            } else {
              this.$message.error(data.msg)
            }
          })
        })
      }
    }
  }
</script>
