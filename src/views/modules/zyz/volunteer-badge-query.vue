<template>
  <el-dialog
    :title="'志愿勋章查询'"
    :visible.sync="visible"
    width="60%"
    :fullscreen="false"
    :close-on-click-modal="false"
    append-to-body
    destroy-on-close
  >
    <div class="certificate-container" v-loading="dataListLoading">
      <div class="certificate-list">
        <div class="certificate-item" 
          v-for="(item, index) in dataList" 
          :key="item.id"
          :class="{ active: currentIndex === index }"
          @click="currentIndex = index"
        >
          <div class="certificate-name">{{ item.badgeName }}</div>
          <div class="certificate-date">获得时间: {{ item.updateDate || '未知' }}</div>
        </div>
        
        <div v-if="dataList.length === 0 && !dataListLoading" class="empty-state">
          <i class="el-icon-document"></i>
          <p>暂无证书数据</p>
        </div>
      </div>
      
      <div class="certificate-preview" v-if="currentBadge">
        <div class="preview-container">
          <!-- 3D模型预览 -->
          <div v-if="currentBadge.badgeFile && !modelLoadError" class="model-preview">
            <auto-rotate-model-viewer 
              :model-url="currentModelUrl"
              :auto-rotate="true"
              :auto-rotate-speed="1.0"
              @load-error="handleModelError"
              @load-success="handleModelSuccess"
            ></auto-rotate-model-viewer>
          </div>
          
          <!-- 图片预览（降级方案） -->
          <div v-else-if="currentBadge.previewImage" class="image-preview">
            <img 
              :src="currentImageUrl" 
              alt="证书预览" 
              @load="handleImageLoaded" 
              @error="handleImageError"
            />
            <div v-if="imageLoading" class="loading-overlay">
              <i class="el-icon-loading"></i>
              <p>图片加载中...</p>
            </div>
            <div v-if="imageLoadError" class="error-overlay">
              <i class="el-icon-warning"></i>
              <p>图片加载失败</p>
            </div>
          </div>
          
          <!-- 无预览内容 -->
          <div v-else class="no-preview">
            <i class="el-icon-document"></i>
            <p>暂无预览内容</p>
          </div>
        </div>
        
        <div class="certificate-details">
          <h3>证书详情</h3>
          <div class="detail-item">
            <span class="label">证书名称:</span>
            <span class="value">{{ currentBadge.badgeName }}</span>
          </div>
          <div class="detail-item">
            <span class="label">证书简介:</span>
            <span class="value">{{ currentBadge.briefIntro || '暂无简介' }}</span>
          </div>
          <div class="detail-item">
            <span class="label">获得时间:</span>
            <span class="value">{{ currentBadge.updateDate || '未知' }}</span>
          </div>
          <!-- <div class="detail-item">
            <span class="label">证书状态:</span>
            <span class="value">{{ currentBadge.recycle ? '已回收' : '有效' }}</span>
          </div> -->
        </div>
      </div>
      
      <div class="com-pagination">
        <el-pagination
          @size-change="sizeChangeHandle"
          @current-change="currentChangeHandle"
          :current-page="pageIndex"
          :page-sizes="[5, 10, 20]"
          :page-size="pageSize"
          :total="totalPage"
          layout="total, sizes, prev, pager, next, jumper"
        />
      </div>
    </div>
  </el-dialog>
</template>

<script>
import ModelViewer from '@/components/model-viewer'
import AutoRotateModelViewer from '@/components/model-viewer/auto-rotate'

export default {
  components: {
    ModelViewer,
    AutoRotateModelViewer
  },
  data() {
    return {
      visible: false,
      volunteerId: null,
      pageIndex: 1,
      pageSize: 5,
      totalPage: 0,
      dataList: [],
      currentIndex: 0,
      dataListLoading: false,
      imageLoading: false,
      imageLoadError: false,
      currentImageUrl: '',
      modelVisible: false,
      modelLoadError: false
    }
  },
  computed: {
    currentBadge() {
      return this.dataList.length > 0 ? this.dataList[this.currentIndex] : null;
    },
    currentModelUrl() {
      return this.currentBadge && this.currentBadge.badgeFile ? 
        this.$http.adornAttachmentUrl(this.currentBadge.badgeFile) : '';
    }
  },
  watch: {
    currentBadge(newVal, oldVal) {
      if (newVal) {
        // 重置状态
        this.imageLoading = false;
        this.imageLoadError = false;
        this.modelLoadError = false;
        
        // 如果有预览图片，设置图片URL
        if (newVal.previewImage) {
          this.imageLoading = true;
          this.currentImageUrl = this.$http.adornAttachmentUrl(newVal.previewImage);
        } else {
          this.currentImageUrl = '';
        }
      }
    }
  },
  methods: {
    init(id) {
      this.volunteerId = id
      this.visible = true
      this.dataList = []
      this.currentIndex = 0
      this.$nextTick(() => {
        this.getDataList()
      })
    },
    // 获取数据列表
    getDataList() {
      this.dataListLoading = true
      this.$http({
        url: this.$http.adornUrl('/admin/zyz/badge/instance/pageBadges'),
        method: 'post',
        data: this.$http.adornData({
          'currentPage': this.pageIndex,
          'pageSize': this.pageSize,
          'ownerId': this.volunteerId
        })
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.dataList = data.obj.records
          this.totalPage = data.obj.total
          this.currentIndex = 0 // 重置为第一个
        } else {
          this.dataList = []
          this.totalPage = 0
        }
        this.dataListLoading = false
      })
    },
    // 每页数
    sizeChangeHandle(val) {
      this.pageSize = val
      this.pageIndex = 1
      this.getDataList()
    },
    // 当前页
    currentChangeHandle(val) {
      this.pageIndex = val
      this.getDataList()
    },
    // 图片加载完成事件
    handleImageLoaded() {
      this.imageLoading = false;
      this.imageLoadError = false;
    },
    // 图片加载失败事件
    handleImageError() {
      this.imageLoading = false;
      this.imageLoadError = true;
    },
    // 处理模型加载成功
    handleModelSuccess() {
      this.modelLoadError = false;
    },
    // 处理模型加载失败
    handleModelError() {
      this.modelLoadError = true;
    }
  }
}
</script>

<style scoped>
.certificate-container {
  display: flex;
  flex-direction: column;
  /* height: 700px; */
}

.certificate-list {
  display: flex;
  overflow-x: auto;
  padding: 10px 0;
  border-bottom: 1px solid #ebeef5;
  margin-bottom: 20px;
}

.certificate-item {
  flex: 0 0 190px;
  margin-right: 15px;
  padding: 10px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s;
}

.certificate-item:hover {
  border-color: #c0c4cc;
}

.certificate-item.active {
  border-color: #409EFF;
  background-color: #ecf5ff;
}

.certificate-name {
  font-weight: bold;
  margin-bottom: 5px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.certificate-date {
  font-size: 12px;
  color: #909399;
}

.certificate-preview {
  display: flex;
  flex: 1;
  overflow: hidden;
  margin-bottom: 20px;
}

.preview-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  overflow: hidden;
  margin-right: 20px;
  position: relative;
}

.model-preview {
  width: 100%;
  height: 400px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.image-preview {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.image-preview img {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

.loading-overlay, .error-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: rgba(255, 255, 255, 0.8);
}

.no-preview {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #909399;
}

.no-preview i {
  font-size: 48px;
  margin-bottom: 10px;
}

.certificate-details {
  flex: 0 0 300px;
  padding: 0 15px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
}

.certificate-details h3 {
  margin: 15px 0;
  padding-bottom: 10px;
  border-bottom: 1px solid #ebeef5;
}

.detail-item {
  margin-bottom: 10px;
}

.detail-item .label {
  font-weight: bold;
  margin-right: 5px;
}

.empty-state {
  width: 100%;
  height: 100px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #909399;
}

.empty-state i {
  font-size: 32px;
  margin-bottom: 10px;
}

.com-pagination {
  padding: 10px 0;
  text-align: right;
}
</style> 