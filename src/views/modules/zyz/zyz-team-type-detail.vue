
<template>
  <el-dialog
      :title="'团队列表'"
      :close-on-click-modal="false"
      width="80%"
      :visible.sync="visible">
    <el-table
        :data="dataList"
        border
        v-loading="dataListLoading"
        @selection-change="selectionChangeHandle"
        style="width: 100%;">
      <el-table-column
          type="selection"
          header-align="center"
          align="center"
          width="50">
      </el-table-column>
      <el-table-column
          prop="name"
          header-align="center"
          align="center"
          width="150px"
          label="团队名称">
      </el-table-column>
      <el-table-column
          prop="founded"
          header-align="center"
          align="center"
          width="150px"
          label="成立时间">
      </el-table-column>
      <el-table-column
          prop="contactPerson"
          header-align="center"
          align="center"
          width="100px"
          label="联系人">
      </el-table-column>
      <el-table-column
          prop="contactPhone"
          header-align="center"
          align="center"
          width="150px"
          label="联系人手机号">
      </el-table-column>
      <el-table-column
          prop="expectTeamNum"
          header-align="center"
          align="center"
          width="110px"
          label="团队预计人数">
      </el-table-column>
      <el-table-column
          prop="orgName"
          header-align="center"
          align="center"
          width="150px"
          label="上级组织">
      </el-table-column>
      <el-table-column
          prop="livelyCountThisYear"
          header-align="center"
          align="center"
          label="当年活跃人数">
      </el-table-column>
      <el-table-column
          prop="serviceLongThisYear"
          header-align="center"
          align="center"
          label="当年服务时长">
      </el-table-column>
      <el-table-column
          prop="activityCountThisYear"
          header-align="center"
          align="center"
          label="当年活动次数">
      </el-table-column>
      <el-table-column
          prop="emphasisText"
          header-align="center"
          align="center"
          label="重点团队">
      </el-table-column>
      <el-table-column
          prop="status"
          header-align="center"
          align="center"
          label="状态">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.status == false" size="small" type="danger">禁用</el-tag>
          <el-tag v-else size="small">启用</el-tag>
        </template>
      </el-table-column>
      <el-table-column
          prop="isSyncText"
          header-align="center"
          align="center"
          width="100px"
          label="同步状态">
        <template slot-scope="scope">
          <el-popover trigger="hover" placement="top">
            <p style="text-align: center">
              {{
                '同步时间：' + (scope.row.syncTime ? scope.row.syncTime : '')
              }}<br>{{ scope.row.syncRemark }}</p>
            <div slot="reference" class="name-wrapper">
              <el-tag v-if="scope.row.isSync === 'sync_failure'" type="danger">{{ scope.row.isSyncText }}</el-tag>
              <el-tag v-if="scope.row.isSync === 'sync_wait'" type="warning">{{ scope.row.isSyncText }}</el-tag>
              <el-tag v-if="scope.row.isSync === 'sync_success'" type="success">{{ scope.row.isSyncText }}</el-tag>
            </div>
          </el-popover>
        </template>
      </el-table-column>
      <el-table-column
          fixed="right"
          header-align="center"
          align="center"
          width="150"
          label="操作">
        <template slot-scope="scope">
          <el-button type="text" size="small" @click="getTeamDetail(scope.row.id,false,false)">团队详情</el-button>
          <el-button type="text" size="small" @click="getVolunteerDetail(scope.row.id)">成员详情</el-button>
          <el-button type="text" size="small" @click="addOrUpdateHandle(scope.row.id)">编辑</el-button>
          <el-button type="text" size="small" @click="deleteHandle(scope.row.id)">删除</el-button>
<!--          <el-dropdown @command="handleCommand" style="padding-left: 10px">-->
<!--            <el-button type="text" size="small">更多操作</el-button>-->
<!--            <el-dropdown-menu slot="dropdown" class="header-new-drop">-->
<!--              <el-dropdown-item :command="beforeHandleCommand(scope.row.id,'updateEmphasis')">设为重点</el-dropdown-item>-->
<!--              <el-dropdown-item :command="beforeHandleCommand(scope.row.id,'stopUse')">-->
<!--                {{ scope.row.status ? '停用' : '解除停用' }}-->
<!--              </el-dropdown-item>-->
<!--              <el-dropdown-item :command="beforeHandleCommand(scope.row.id,'syncHandle')"-->
<!--                                :disabled="scope.row.isSync !== 'sync_wait'">同步-->
<!--              </el-dropdown-item>-->
<!--              <el-dropdown-item :command="beforeHandleCommand(scope.row.id,'delete')">删除</el-dropdown-item>-->
<!--            </el-dropdown-menu>-->
<!--          </el-dropdown>-->
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
        @size-change="sizeChangeHandle"
        @current-change="currentChangeHandle"
        :current-page="pageIndex"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pageSize"
        :total="totalPage"
        layout="total, sizes, prev, pager, next, jumper">
    </el-pagination>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"></add-or-update>
    <get-volunteer-detail v-if="volunteerDetailVisible" ref="volunteerDetail"
                          @refreshDataList="getDataList"></get-volunteer-detail>
    <get-team-detail v-if="teamDetailVisible" ref="teamDetail" @refreshDataList="getDataList"></get-team-detail>
    <update-emphasis v-if="updateEmphasisVisible" ref="updateEmphasis" @refreshDataList="getDataList"></update-emphasis>
  </el-dialog>
</template>

<script>
import AddOrUpdate from './zyz-team-add-or-update'
import GetVolunteerDetail from './team-volunteer-detail'
import GetTeamDetail from './zyz-team-detail'
import UpdateEmphasis from './change-emphasis'

export default {
  data() {
    return {
      dataForm: {
        teamNo: '',
        name: '',
        contactPerson: '',
        contactPhone: '',
        isEmphasis: null,
        status: null,
        orgCode: '',
        typeId: '',
        orgCodeList: [],
        typeName: ''
      },
      orgList: [],
      dataList: [],
      pageIndex: 1,
      pageSize: 10,
      totalPage: 0,
      visible: false,
      dataListLoading: false,
      dataListSelections: [],
      addOrUpdateVisible: false,
      volunteerDetailVisible: false,
      teamDetailVisible: false,
      updateEmphasisVisible: false
    }
  },
  components: {
    AddOrUpdate,
    GetVolunteerDetail,
    GetTeamDetail,
    UpdateEmphasis
  },
  activated() {
    this.queryPage()
    this.getOrg()
  },
  methods: {
    queryPage() {
      this.pageIndex = 1
    },

    // 重置
    resetForm() {
      this.$refs['dataForm']?.resetFields()
      this.getDataList()
    },
    init (id) {
      this.dataForm.typeId = id || null
      this.visible = true
       this.getDataList()
    },
    // 获取数据列表
    getDataList() {
      this.dataListLoading = true
      this.$http({
        url: this.$http.adornUrl('/admin/zyz/type/team/relevance/getPagesByType'),
        method: 'post',
        data: this.$http.adornData({
          'currentPage': this.pageIndex,
          'pageSize': this.pageSize,
          'typeId': this.dataForm.typeId
        })
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.dataList = data.obj.records
          this.totalPage = data.obj.total
        } else {
          this.dataList = []
          this.totalPage = 0
        }
        this.dataListLoading = false
      })
    },
    // 每页数
    sizeChangeHandle(val) {
      this.pageSize = val
      this.pageIndex = 1
      this.getDataList()
    },
    // 当前页
    currentChangeHandle(val) {
      this.pageIndex = val
      this.getDataList()
    },
    // 多选
    selectionChangeHandle(val) {
      this.dataListSelections = val
    },
    // 新增 / 修改
    addOrUpdateHandle(id) {
      this.addOrUpdateVisible = true
      this.$nextTick(() => {
        this.$refs.addOrUpdate.init(id)
      })
    },
    updateEmphasis(id) {
      this.updateEmphasisVisible = true
      this.$nextTick(() => {
        this.$refs.updateEmphasis.init(id)
      })
    },
    getTeamDetail(id, isAudit, isLog) {
      this.teamDetailVisible = true
      this.$nextTick(() => {
        this.$refs.teamDetail.init(id, isAudit, isLog)
      })
    },
    getVolunteerDetail(id) {
      this.volunteerDetailVisible = true
      this.$nextTick(() => {
        this.$refs.volunteerDetail.init(id)
      })
    },
    //获取所属区域
    getOrg() {
      this.$http({
        url: this.$http.adornUrl(`/admin/org/getOrgTree`),
        method: 'get',
        params: this.$http.adornParams()
      }).then(({data}) => {
        if (data && data.code === 0) {
          // this.orgList = this.getTreeData(data.obj)
          this.orgList = data.obj
        } else {
          this.orgList = []
        }
      })
    },
    // 删除
    deleteHandle(id) {
      var ids = id ? [id] : this.dataListSelections.map(item => {
        return item.id
      })
      this.$confirm(`确定进行[${id ? '删除' : '批量删除'}]操作?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        closeOnClickModal: false
      }).then(() => {
        this.$http({
          url: this.$http.adornUrl('/admin/zyz/team/removeByIds'),
          method: 'get',
          params: this.$http.adornParams({
            'ids': ids.join(',')
          })
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.$message({
              message: '操作成功',
              type: 'success',
              duration: 1500,
              onClose: () => {
                this.getDataList()
              }
            })
          } else {
            this.$message.error(data.msg)
          }
        })
      })
    }
  }
}
</script>
