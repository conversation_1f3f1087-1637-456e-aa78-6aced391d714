<template>
  <el-dialog
    :title="'所在团队'"
    :close-on-click-modal="false"
    width="80%"
    :visible.sync="visible">
    <el-form :inline="true" :model="dataForm"  ref="dataForm" >
     <el-form-item label="团队名称" prop="name" >
          <el-input v-model="dataForm.name" placeholder="团队名称"></el-input>
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-search" type="warning" @click="queryPage()">查询</el-button>
        <el-button @click="resetForm()" icon="el-icon-refresh-left">重置</el-button>
      </el-form-item>
      <el-form-item style="float: right">
        <div style="font-weight: bold;color: red">如需调整该志愿者团队信息，请联系团队联系人或管理员</div>
      </el-form-item>
        </el-form>
    <div class="f-s-c" style="padding: 15px 0;display: flex;justify-content:flex-end;align-items: center ">
      <div>
        <el-button icon="el-icon-download" type="success" v-loading.fullscreen.lock="fullscreenLoading" @click="exportTeam()">全部导出</el-button>
      </div>
    </div>
    <el-table
        :data="dataList"
        border
        v-loading="dataListLoading"
        style="width: 100%;">
      <el-table-column
          prop="volunteerName"
          header-align="center"
          align="center"
          label="姓名">
      </el-table-column>
      <el-table-column
          prop="name"
          header-align="center"
          align="center"
          width="300px"
          label="团队名称">
      </el-table-column>
      <el-table-column
          prop="contactPerson"
          header-align="center"
          align="center"
          label="联系人">
      </el-table-column>
      <el-table-column
          prop="contactPhone"
          header-align="center"
          align="center"
          width="150px"
          label="联系电话">
      </el-table-column>
      <el-table-column
        prop="teamNumber"
        header-align="center"
        align="center"
        label="团队人数">
    </el-table-column>
      <el-table-column
          prop="orgName"
          header-align="center"
          align="center"
          label="上级组织">
      </el-table-column>
<!--      <el-table-column-->
<!--          prop="volunteerStatusText"-->
<!--          header-align="center"-->
<!--          align="center"-->
<!--          label="人员状态">-->
<!--      </el-table-column>-->
    </el-table>
    <el-pagination
        @size-change="sizeChangeHandle"
        @current-change="currentChangeHandle"
        :current-page="pageIndex"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pageSize"
        :total="totalPage"
        layout="total, sizes, prev, pager, next, jumper">
    </el-pagination>
  </el-dialog>
</template>

<script>
import moment from 'moment'
  export default {
    data () {
      return {
        visible: false,
        dataForm: {
          id: null,
          version: null,
          name: '',
          volunteerId: ''
        },
        fullscreenLoading: false,
        dataList: [],
        pageIndex: 1,
        pageSize: 10,
        totalPage: 0,
        dataListLoading: false,
        dataListSelections: [],
        nameError: null,
      }
    },
    components: {
        moment
    },
    methods: {
      queryPage () {
        this.pageIndex = 1
        this.getDataList()
      },
      init (id) {
        this.dataForm.volunteerId = id || null
        this.visible = true
        this.getDataList()
      },
      resetForm() {
        this.$refs['dataForm']?.resetFields()
        this.getDataList()
      },
      // 获取数据列表
      getDataList () {
        this.dataListLoading = true
        this.$http({
          url: this.$http.adornUrl('/admin/zyz/team/getMyPages'),
          method: 'post',
          data: this.$http.adornData({
            'currentPage': this.pageIndex,
            'pageSize': this.pageSize,
            'name': this.dataForm.name,
            'volunteerId': this.dataForm.volunteerId
          })
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.dataList = data.obj.records
            this.totalPage = data.obj.total
          } else {
            this.dataList = []
            this.totalPage = 0
          }
          this.dataListLoading = false
        })
      },
      // 每页数
      sizeChangeHandle (val) {
        this.pageSize = val
        this.pageIndex = 1
        this.getDataList()
      },
      // 当前页
      currentChangeHandle (val) {
        this.pageIndex = val
        this.getDataList()
      },
      // 下载模板文件
      exportTeam () {
        this.fullscreenLoading = true;
        this.$http({
          url: this.$http.adornUrl('/admin/zyz/team/export'),
          method: 'post',
          responseType: 'arraybuffer',
          data: this.$http.adornData({
            'name': this.dataForm.name,
            'volunteerId': this.dataForm.volunteerId
          })
        }).then(({data}) => {
          if (data.code && data.code !== 0) {
            this.$message.error('导出失败')
          } else {
            this.fullscreenLoading = false
            let blob = new Blob([data], {type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8'})
            let objectUrl = URL.createObjectURL(blob)
            let a = document.createElement('a')
            // window.location.href = objectUrl
            a.href = objectUrl
            a.download = '所在团队明细.xlsx'
            a.click()
            URL.revokeObjectURL(objectUrl)
            this.$message({
              type: 'success',
              message: '导出成功'
            })
          }
        })
      }
    }
  }
</script>
