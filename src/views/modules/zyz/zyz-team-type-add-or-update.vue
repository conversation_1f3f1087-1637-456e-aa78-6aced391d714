<template>
  <el-dialog
    :title="!dataForm.id ? '新增' : '修改'"
    :close-on-click-modal="false"
    width="40%"
    :visible.sync="visible">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmit()" label-width="90px">
      <el-row :gutter="20">
        <el-col :span="24">
           <el-form-item label="类型名称" prop="name" :error="nameError">
                <el-input style="width: 100%" v-model="dataForm.name" clearable placeholder="类型名称"></el-input>
            </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="状态" prop="status" :error="statusError">
            <el-radio-group v-model="dataForm.status">
              <el-radio :label="true">启用</el-radio>
              <el-radio :label="false">禁用</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="图标" prop="photo" :error="photoError">
            <el-upload
                class="avatar-uploader"
                :action="this.$http.adornUrl(`/admin/oss/upload`)"
                :headers="myHeaders"
                :data="{serverCode: this.serverCode, media: false}"
                :show-file-list="false"
                :on-success="handleAvatarSuccess"
                :before-upload="beforeAvatarUpload">
              <img v-if="dataForm.photo" :src="$http.adornAttachmentUrl(dataForm.photo)" class="avatar">
              <i v-else class="el-icon-plus avatar-uploader-icon"></i>
              <div slot="tip" class="el-upload__tip" style="color: red">尺寸建议600x1000像素，文件格式jpg、bmp或png，大小不超2M</div>
            </el-upload>
          </el-form-item>
        </el-col>
      </el-row>
        </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import Vue from 'vue'

  export default {
    data () {
      return {
        visible: false,
        serverCode: 'LocalServer',
        myHeaders: {Authorization: Vue.cookie.get('Authorization')},
        uploadData: {serverCode: 'LocalServer', media: false},
        dataForm: {
          id: null,
          version: null,
          name: '',
          status: '',
          photo: ''
        },
        dataRule: {
          status: [
            { required: true, message: '状态不能为空', trigger: 'blur' }
          ],
          name: [
            { required: true, message: '类型名称不能为空', trigger: 'blur' }
          ],
          photo: [
            { required: true, message: '图标不能为空', trigger: 'blur' }
          ]
        },
        nameError: null,
        statusError: null,
        photoError: null
      }
    },
    methods: {
      init (id) {
        this.dataForm.id = id || null
        this.visible = true
        this.$nextTick(() => {
          this.$refs['dataForm']?.resetFields()
          if (this.dataForm.id) {
            this.$http({
              url: this.$http.adornUrl(`/admin/zyz/team/type`),
              method: 'get',
              params: this.$http.adornParams({ id: this.dataForm.id })
            }).then(({data}) => {
              if (data && data.code === 0) {
                this.dataForm = data.obj
              }
            })
          }
        })
      },
      handleAvatarSuccess(res, file) {
        if (res.success) {
          this.$message({
            message: '操作成功',
            type: 'success',
            duration: 1500
          })
          this.dataForm.photo = res.obj.path
        } else {
          this.$message.error('上传失败')
        }
      },
      beforeAvatarUpload: function (file) {
        let isAccept = ['image/jpg', 'image/jpeg', 'image/png', 'image/bmp'].indexOf(file.type) !== -1
        let isLt2M = file.size / 1024 / 1024 < 2

        if (!isAccept) {
          this.$message.error('上传图片只能是图片!')
        }
        if (!isLt2M) {
          this.$message.error('上传文件大小不能超过 2MB!')
        }
        return isAccept && isLt2M
      },
      // 表单提交
      dataFormSubmit () {
        this.$refs['dataForm'].validate((valid) => {
          if (valid) {
            this.$http({
              url: this.$http.adornUrl(`/admin/zyz/team/type/${!this.dataForm.id ? 'save' : 'update'}`),
              method: 'post',
              data: this.$http.adornData(this.dataForm)
            }).then(({data}) => {
              if (data && data.code === 0) {
                this.$message({
                  message: '操作成功',
                  type: 'success',
                  duration: 1500,
                  onClose: () => {
                    this.visible = false
                    this.$emit('refreshDataList')
                  }
                })
              } else if (data && data.code === 303) {
                for (let it of data.obj) {
                  this[`${it.field}Error`] = it.message
                }
              } else {
                this.$message.error(data.msg)
              }
            })
          }
        })
      }
    }
  }
</script>
