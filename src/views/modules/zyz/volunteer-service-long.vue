<template>
  <el-dialog
      :title="'服务时长'"
      :close-on-click-modal="false"
      width="80%"
      :visible.sync="visible">
    <el-form :inline="true" ref="dataForm" :model="dataForm" @keyup.enter.native="queryPage()">
      <el-form-item label="姓名" prop="name">
        <el-input v-model="dataForm.name" placeholder="姓名" clearable></el-input>
      </el-form-item>
      <el-form-item label="参与时间" prop="lastChatDate">
        <el-date-picker
            v-model="dataForm.timeRange"
            clearable
            type="daterange"
            value-format="yyyy-MM-dd"
            format="yyyy-MM-dd"
            align="right"
            start-placeholder="开始时间"
            end-placeholder="结束时间">
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-search" type="warning" @click="queryPage()">查询</el-button>
        <el-button @click="resetForm()" icon="el-icon-refresh-left">重置</el-button>
      </el-form-item>
    </el-form>
    <div class="f-s-c" style="padding: 15px 0;display: flex;justify-content:flex-end;align-items: center ">
      <div>
        <el-button icon="el-icon-download" type="success" v-loading.fullscreen.lock="fullscreenLoading" @click="exportServiceLong()">全部导出
        </el-button>
      </div>
    </div>
    <el-table
        :data="dataList"
        border
        v-loading="dataListLoading"
        @selection-change="selectionChangeHandle"
        style="width: 100%;">
      <el-table-column
          type="selection"
          header-align="center"
          align="center"
          width="50">
      </el-table-column>
      <el-table-column
          prop="volunteerName"
          header-align="center"
          align="center"
          width="200px"
          label="姓名">
      </el-table-column>
      <el-table-column
          prop="activityName"
          header-align="center"
          align="center"
          width="300px"
          label="活动名称">
      </el-table-column>
      <el-table-column
          prop="applyTime"
          header-align="center"
          align="center"
          width="200px"
          label="报名时间">
      </el-table-column>
      <el-table-column
          prop="signTime"
          header-align="center"
          align="center"
          width="200px"
          label="签到时间">
      </el-table-column>
      <el-table-column
          prop="timeRange"
          header-align="center"
          align="center"
          width="400px"
          label="活动时段">
      </el-table-column>
      <el-table-column
          prop="serviceLong"
          header-align="center"
          align="center"
          label="服务时长">
      </el-table-column>
    </el-table>
    <el-pagination
        @size-change="sizeChangeHandle"
        @current-change="currentChangeHandle"
        :current-page="pageIndex"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pageSize"
        :total="totalPage"
        layout="total, sizes, prev, pager, next, jumper">
    </el-pagination>
  </el-dialog>
</template>
<script>
import moment from 'moment'

export default {
  data() {
    return {
      visible: false,
      dataForm: {
        name: '',
        timeRange: []
      },
      dataList: [],
      orgList: [],
      pageIndex: 1,
      pageSize: 10,
      totalPage: 0,
      dataListLoading: false,
      fullscreenLoading: false,
      dataListSelections: []
    }
  },
  components: {},
  activated() {
  },
  methods: {
    queryPage() {
      this.pageIndex = 1
      this.getDataList()
    },
    init(id) {
      this.dataForm.id = id || null
      this.visible = true
      this.queryPage()
    },
    // 获取数据列表
    getDataList() {
      this.dataListLoading = true
      this.$http({
        url: this.$http.adornUrl('/admin/zyz/activity/apply/pageForVolunteerServiceLong'),
        method: 'post',
        data: this.$http.adornData({
          'currentPage': this.pageIndex,
          'pageSize': this.pageSize,
          'key': this.dataForm.name,
          "volunteerId": this.dataForm.id,
          'startTime': this.dataForm.timeRange && this.dataForm.timeRange.length === 2 ? this.dataForm.timeRange[0] + ' 00:00:00' : null,
          'endTime': this.dataForm.timeRange && this.dataForm.timeRange.length === 2 ? this.dataForm.timeRange[1] + ' 23:59:59' : null,
        })
      }).then(({data}) => {
        if (data && data.code === 0) {
          data.obj.records.forEach((it) => {
            it.timeRange = moment(it.timePeriodStartTime).format('YYYY-MM-DD HH:mm') + '-' + moment(it.timePeriodEndTime).format('HH:mm')
          })
          this.dataList = data.obj.records
          this.totalPage = data.obj.total
        } else {
          this.dataList = []
          this.totalPage = 0
        }
        this.dataListLoading = false
      })
    },
    // 每页数
    sizeChangeHandle(val) {
      this.pageSize = val
      this.pageIndex = 1
      this.getDataList()
    },
    // 当前页
    currentChangeHandle(val) {
      this.pageIndex = val
      this.getDataList()
    },
    // 多选
    selectionChangeHandle(val) {
      this.dataListSelections = val
    },
    // 重置
    resetForm() {
      this.$refs['dataForm']?.resetFields()
      this.dataForm.timeRange = []
      this.getDataList()
    },
    // 导出
    exportServiceLong() {
      this.fullscreenLoading = true;
      this.$http({
        url: this.$http.adornUrl('/admin/zyz/activity/apply/exportServiceLong'),
        method: 'post',
        responseType: 'arraybuffer',
        data: this.$http.adornData({
          'key': this.dataForm.name,
          "volunteerId": this.dataForm.id,
          'startTime': this.dataForm.timeRange != null ? this.dataForm.timeRange[0] : null,
          'endTime': this.dataForm.timeRange != null ? this.dataForm.timeRange[1] : null
        })
      }).then(({data}) => {
        if (data.code && data.code !== 0) {
          this.$message.error('导出失败')
        } else {
          this.fullscreenLoading = false;
          let blob = new Blob([data], {type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8'})
          let objectUrl = URL.createObjectURL(blob)
          let a = document.createElement('a')
          // window.location.href = objectUrl
          a.href = objectUrl
          a.download = '志愿者服务时长列表明细.xlsx'
          a.click()
          URL.revokeObjectURL(objectUrl)
          this.$message({
            type: 'success',
            message: '导出成功'
          })
        }
      })
    }

  }
}
</script>

