<template>
  <el-dialog
      :title="'审核'"
      :close-on-click-modal="false"
      width="40%"
      append-to-body
      :visible.sync="visible">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" label-width="120px">
      <el-form-item label="审核" prop="auditStatus">
        <el-radio-group v-model="dataForm.auditStatus">
          <el-radio :label="'vts_audit'">通过</el-radio>
          <el-radio :label="'vts_reject'">驳回</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item v-if="dataForm.auditStatus === 'vts_reject'" label="驳回理由" prop="remark">
        <el-input type="textarea" :autosize="{ minRows: 2}" v-model="dataForm.remark"
                  placeholder="驳回理由"></el-input>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="audit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import moment from 'moment'

export default {
  data() {
    return {
      visible: false,
      dataForm: {
        id: null,
        version: null,
        auditStatus: '',
        remark: ''
      },
      dataRule: {
        remark: [
          {required: true, message: '驳回理由不能为空', trigger: 'blur'}
        ],
        auditStatus: [
          {required: true, message: '审核状态不能为空', trigger: 'blur'}
        ]
      },
      remarksError: null
    }
  },
  components: {
    moment
  },
  methods: {
    init(id) {
      this.dataForm.id = id || null
      this.dataForm.auditStatus = ''
      this.dataForm.remark = ''
      this.visible = true
    },
    audit(){
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.$http({
            url: this.$http.adornUrl('/admin/zyz/volunteer/team/audit/volunteerAudit'),
            method: 'get',
            params: this.$http.adornParams({
              'ids': this.dataForm.id,
              'remake': this.dataForm.remark,
              'auditStatus': this.dataForm.auditStatus
            })
          }).then(({data}) => {
            if (data && data.code === 0) {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 1500,
                onClose: () => {
                  this.visible = false
                  this.$emit('refreshDataList')
                }
              })
            } else {
              this.$message.error(data.msg)
            }
          })
        }
      })
    }
  }
}
</script>
