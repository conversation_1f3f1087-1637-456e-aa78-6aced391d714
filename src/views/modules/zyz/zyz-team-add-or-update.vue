<template>
  <el-dialog
      :title="!dataForm.id ? '新增' : (selfEdit ? '信息变更' : '修改')"
      :close-on-click-modal="false"
      width="80%"
      append-to-body
      :visible.sync="visible">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmit()" label-width="140px">
      <el-card class="box-card">
        <div slot="header" class="clearfix"><span>团队基本信息</span></div>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="团队名称" prop="name">
              <el-input v-model="dataForm.name" placeholder="团队名称" clearable/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="团队预计成员人数" prop="expectTeamNum">
              <el-input-number style="width: 100%" v-model="dataForm.expectTeamNum" controls-position="right" :min="0"/>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
<!--          <el-col :span="12">-->
<!--            <el-form-item label="团队性质" prop="civilRegistration">-->
<!--              <el-radio-group v-model="dataForm.civilRegistration">-->
<!--                <el-radio :label="true">民政注册团队</el-radio>-->
<!--                <el-radio :label="false">非民政注册团队</el-radio>-->
<!--              </el-radio-group>-->
<!--            </el-form-item>-->
<!--          </el-col>-->
          <el-col :span="12">
            <el-form-item label="团队性质" prop="teamNature">
              <el-dict
                  :code="'team_nature'"
                  v-model="dataForm.teamNature"
                  clearable
                  placeholder="请选择团队性质"></el-dict>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="dataForm.civilRegistration && dataForm.civilRegistration === true ? '注册时间' :'成立时间'" prop="founded">
              <el-date-picker
                  style="width: 100%"
                  v-model="dataForm.founded"
                  value-format="yyyy-MM-dd"
                  type="date"
                  clearable
                  placeholder="成立时间"/>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="团队类型" prop="teamClassifyList">
              <el-dict
                  v-model="dataForm.teamClassifyList"
                  :multiple="true"
                  :multiple-limit="5"
                  custom-url="/admin/sgb/dict/type"
                  :custom-params="{ type: 'team_classify' }"
                  :props="{ label: 'text', value: 'id' }"
                  placeholder="请选择（最多5个）">
              </el-dict>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="业务主管单位" prop="businessSupervisoryUnit">
              <el-input v-model="dataForm.businessSupervisoryUnit" placeholder="业务主管单位" clearable/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="dataForm.civilRegistration && dataForm.civilRegistration === true ? '注册地点' : '所在地(街道/社区)'" prop="registerPlace">
              <el-input v-model="dataForm.registerPlace" :placeholder="dataForm.civilRegistration && dataForm.civilRegistration === true ? '注册地点' : '所在地（街道/社区）'" clearable/>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item prop="attachmentList">
              <span slot="label">{{dataForm.civilRegistration && dataForm.civilRegistration === true ? '附件（营业执照）' : '附件'}}</span>
              <div v-if="dataForm.civilRegistration && dataForm.civilRegistration === true" slot="label" style="font-size: xx-small; display: flex; flex-wrap: wrap; line-height: 15px">
                <div>请上传材料:</div>
                <div style="text-align: left">1、
                  <el-button style="font-size: xx-small; padding: 0px; border: 0px" type="text" @click="downloadTemplate()">
                    <i class="el-icon-download el-icon--right"/>
                    信息登记表
                  </el-button>
                  ，请本单位盖章后上传；
                </div>
                <div style="text-align: left">2、请上传有效期内的登记证书扫描件(正反面)。</div>
              </div>
              <div v-else slot="label" style="font-size: xx-small; line-height: 15px">
                <div>请上传材料:</div>
                <div>
                  <el-button style="font-size: xx-small; padding: 0px; border: 0px" type="text" @click="downloadTemplate()">
                    <i class="el-icon-download el-icon--right"/>
                    信息登记表
                  </el-button>
                </div>
                <div>请在本单位盖章后上传</div>
              </div>
              <el-upload
                  class="upload-demo"
                  :action="this.$http.adornUrl('/admin/oss/upload')"
                  :data="uploadData"
                  :headers="myHeaders"
                  :on-success="function (res,file,fileList){return uploadSuccess(res,file,fileList)}"
                  :file-list="dataForm.attachmentList"
                  :on-preview="download"
                  :on-remove="function (file,fileList){return handleRemove(file,fileList)}"
                  :before-upload="function (file,fileList){return beforeUpload(file,fileList)}">
                <el-button size="small" type="primary" plain>点击上传<i class="el-icon-upload el-icon--left"/></el-button>
                <span style="font-size: xx-small; margin-left: 20px">{{dataForm.civilRegistration && dataForm.civilRegistration === true ? '(请上传有效期内的营业执照扫描件)' : '(主管单位盖章批复文件)'}}</span>
                <div slot="tip" class="el-upload__tip">支持上传word、excel、pdf、图片</div>
              </el-upload>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="上级组织" prop="orgCodeList">
              <el-cascader
                  placeholder="上级组织"
                  :disabled="dataForm.id !== undefined && dataForm.id !== null"
                  v-model="dataForm.orgCodeList"
                  :options="orgList"
                  :show-all-levels="false"
                  :props="{checkStrictly: true,label: 'name',value: 'code'}"
                  @change="handleChange"/>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="是否有注册号" prop="hasRegist">
              <el-radio-group v-model="dataForm.hasRegist">
                <el-radio :label="true">是</el-radio>
                <el-radio :label="false">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label-width="180px" label="是否社会工作服务机构" prop="socialOrgFlag">
              <el-radio-group v-model="dataForm.socialOrgFlag">
                <el-radio :label="true">是</el-radio>
                <el-radio :label="false">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="是否成立党组织" prop="partyOrgFlag">
              <el-radio-group v-model="dataForm.partyOrgFlag">
                <el-radio :label="true">是</el-radio>
                <el-radio :label="false">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item v-if="dataForm.hasRegist" label="团队注册号" prop="registerCard">
              <el-input v-model="dataForm.registerCard" placeholder="注册号" clearable/>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="所属领域" prop="belongFieldList">
              <el-select style="width: 100%" v-model="dataForm.belongFieldList" multiple clearable placeholder="请选择" @change="belongFieldsSelect">
                <el-option
                    v-for="item in belongFieldList"
                    :key="item.typeId"
                    :label="item.typeName"
                    :value="item.typeId">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="24">
              <el-form-item class="address-item" label="办公地点" prop="workAddress">
                  <el-input v-model="dataForm.workAddress" placeholder="办公地点" clearable @clear="addressClear"/><el-button icon="el-icon-location-outline" type="primary" @click="showMap">地图定位</el-button>
              </el-form-item>
          </el-col>
        </el-row>
          <el-row :gutter="20">
          <el-col :span="6">
              <el-form-item label="经度" prop="longitude" >
                  <el-input v-model="dataForm.longitude" :placeholder="'经度'" disabled></el-input>
              </el-form-item>
          </el-col>
          <el-col :span="6">
              <el-form-item label="纬度" prop="latitude" >
                  <el-input v-model="dataForm.latitude" :placeholder="'纬度'" disabled></el-input>
              </el-form-item>
          </el-col>
          </el-row>
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="团队介绍" prop="introduction">
              <el-input type="textarea" v-model="dataForm.introduction" placeholder="团队介绍" rows="5" maxlength="1000" show-word-limit/>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="报名条件" prop="joinRequires">
              <el-input type="textarea" v-model="dataForm.joinRequires" placeholder="报名条件" rows="5" maxlength="1000" show-word-limit/>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="服务对象" prop="serviceTargetList">
              <el-dict
                  v-model="dataForm.serviceTargetList"
                  :multiple="true"
                  :multiple-limit="5"
                  custom-url="/admin/sgb/dict/type"
                  :custom-params="{ type: 'service_target' }"
                  :props="{ label: 'text', value: 'id' }"
                  placeholder="请选择（最多5个）">
              </el-dict>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="服务内容" prop="services">
              <el-input type="textarea" v-model="dataForm.services" placeholder="服务内容" rows="5" maxlength="1000" show-word-limit/>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="团队LOGO" prop="teamPhoto">
              <el-upload
                  class="avatar-uploader"
                  :action="this.$http.adornUrl(`/admin/oss/upload`)"
                  :headers="myHeaders"
                  :data="{serverCode: this.serverCode, media: false}"
                  :show-file-list="false"
                  :on-success="handleAvatarSuccess"
                  :before-upload="beforeAvatarUpload">
                <img v-if="dataForm.teamPhoto" :src="$http.adornAttachmentUrl(dataForm.teamPhoto)" class="avatar">
                <i v-else class="el-icon-plus avatar-uploader-icon"/>
                <div slot="tip" class="el-upload__tip" style="color: red">尺寸建议600x1000像素，文件格式jpg、bmp或png，大小不超2M</div>
              </el-upload>
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>
      <el-card class="box-card" style="margin-top: 20px;">
        <div slot="header" class="clearfix"><span>团队负责人信息</span></div>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="负责人姓名" prop="curatorName">
              <el-input v-model="dataForm.curatorName" placeholder="负责人姓名" clearable/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="证件号码" prop="curatorCard">
              <el-input v-model="dataForm.curatorCard" placeholder="证件号码" clearable/>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="联系电话" prop="curatorContact">
              <el-input v-model="dataForm.curatorContact" placeholder="联系电话" clearable/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="是否党员" prop="curatorPartyMember">
              <el-radio-group v-model="dataForm.curatorPartyMember">
                <el-radio :label="true">是</el-radio>
                <el-radio :label="false">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="所在单位" prop="curatorDepartment">
              <el-input v-model="dataForm.curatorDepartment" placeholder="所在单位" clearable/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="职位" prop="curatorJob">
              <el-input v-model="dataForm.curatorJob" placeholder="职位" clearable/>
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>
      <el-card class="box-card" style="margin-top: 20px;">
        <div slot="header" class="clearfix"><span>团队管理员信息</span></div>
        <el-row :gutter="20">
          <el-col :span="12">
            <div style="height:15px; color: red; font-size: xx-small; margin-left: 140px; margin-top: -15px" v-if="!lodash.find(teamMemberList, ['certificateId', dataForm.adminCard])">*当前管理员不在团队成员内，可以从团队成员中选择更换管理员！</div>
            <el-form-item v-if="!this.dataForm.id" label="管理员姓名" prop="adminName">
              <el-input v-model="dataForm.adminName" placeholder="管理员姓名" clearable/>
            </el-form-item>
            <el-form-item v-else label="管理员姓名" :prop="lodash.find(teamMemberList, ['certificateId', dataForm.adminCard]) ? 'adminCard' : 'adminName'">
              <el-select v-if="lodash.find(teamMemberList, ['certificateId', dataForm.adminCard])" style="width: 100%" v-model="dataForm.adminCard" clearable placeholder="请选择" @change="adminChange">
                <el-option
                    v-for="item in teamMemberList"
                    :key="item.certificateId"
                    :label="item.volunteerName"
                    :value="item.certificateId">
                </el-option>
              </el-select>
              <el-select v-else style="width: 100%" v-model="dataForm.adminName" clearable placeholder="请选择" @change="adminChange">
                <el-option
                    v-for="item in teamMemberList"
                    :key="item.certificateId"
                    :label="item.volunteerName"
                    :value="item.certificateId">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="证件号码" prop="adminCard">
              <el-input :disabled="dataForm.id && dataForm.id !== ''" v-model="dataForm.adminCard" placeholder="证件号码" clearable/>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="联系电话" prop="adminContact">
              <el-input :disabled="dataForm.id && dataForm.id !== ''" v-model="dataForm.adminContact" placeholder="联系电话" clearable/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="是否党员" prop="adminPartyMember">
              <el-radio-group v-model="dataForm.adminPartyMember">
                <el-radio :label="true">是</el-radio>
                <el-radio :label="false">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="所在单位" prop="adminDepartment">
              <el-input v-model="dataForm.adminDepartment" placeholder="所在单位" clearable/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="职位" prop="adminJob">
              <el-input v-model="dataForm.adminJob" placeholder="职位" clearable/>
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button :disabled="submitLoading" type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
      <el-dialog class="map-dialog" title="地图" width="80%" :close-on-click-modal="false" :visible.sync="mapVisible" :modal-append-to-body="false" :append-to-body="true">
          <AMapInfo :mapVisible.sync="mapVisible" :address.sync="dataForm.workAddress" :latitude.sync="dataForm.latitude" :longitude.sync="dataForm.longitude" />
      </el-dialog>
  </el-dialog>
</template>

<script>
import moment from 'moment'
import Vue from 'vue'
import _ from 'lodash'
import {isMobile, is8lPhone} from "@/utils/validate";
import AMapInfo from "@/components/map/a-map-info";
export default {
  data() {
    const validateContactPhone = (rule, value, callback) => {
      if (!isMobile(value) && !is8lPhone(value)) {
        callback(new Error('联系方式须为手机号或区号+8位座机号例如051288888888或0512-88888888'))
      } else {
        callback()
      }
    }
    const validateName = (rule, value, callback) => {
        let reg =/^[\w\u4e00-\u9fa5”“（）+]+$/
        if (value === '') {
            callback(new Error("团队名称不能为空"))
        } else {
            if (!reg.test(value)) {
                callback(new Error('团队名称不能含有特殊字符（团队名字，只支持数字、中文、字母、以及”+（））'))
            } else {
                callback()
            }
        }
    }
      const validateAddress = (rule, value, callback) => {
          if (!this.dataForm.longitude || this.dataForm.longitude === '' || !this.dataForm.latitude || this.dataForm.latitude === '') {callback(new Error('地址经纬度，请通过地图定位选择地址！'))}
          else {callback()}
      }

    const validateRegisterPlace = (rule, value, callback) => {
      if (value && value !== '') {
        callback()
      }
      if (this.dataForm.civilRegistration && this.dataForm.civilRegistration === true) {
        callback(new Error('注册地点不能为空'))
      } else {
        callback(new Error('所在地(街道/社区)不能为空'))
      }

    }
    const validateFounded = (rule, value, callback) => {
      if (value && value !== '') {
        callback()
      }
      if (this.dataForm.civilRegistration && this.dataForm.civilRegistration === true) {
        callback(new Error('注册时间不能为空'))
      } else {
        callback(new Error('成立时间不能为空'))
      }
    }
    return {
      visible: false,
      mapVisible: false,
      serverCode: 'LocalServer',
      myHeaders: {Authorization: Vue.cookie.get('Authorization')},
      uploadData: {serverCode: 'LocalServer', media: false},
      orgList: [],
      fileList: [],
      belongFieldList: [],
      teamMemberList: [],
      dataForm: {},
      selfEdit: false,
      submitLoading: false,
      lodash: _,
      initForm: {
        id: null,
        version: null,
        updateDate: null,
        updater: null,
        createDate: null,
        creator: null,
        name: null,
        expectTeamNum: 0,
        civilRegistration: false,
        founded: null,
        businessSupervisoryUnit: null,
        registerPlace: null,
        attachmentList: [],
        orgCodeList: [],
        orgCode: null,
        hasRegist: false,
        registerCard: null,
        belongFieldList: [],
        belongFields: null,
        workAddress: null,
        introduction: null,
        joinRequires: null,
        services: null,
        teamPhoto: null,
        socialOrgFlag: false,
        serviceTarget: null,
        serviceTargetList: [],
        partyOrgFlag: false,
        teamNature: null,
        teamClassify: null,
        teamClassifyList: [],
        curatorName: null,
        curatorCard: null,
        curatorContact: null,
        curatorPartyMember: false,
        curatorDepartment: null,
        curatorJob: null,
        adminName: null,
        adminCard: null,
        adminContact: null,
        adminPartyMember: false,
        adminDepartment: null,
        adminJob: null,
        status: null,
        auditOrgCode: null,
        teamStatus: null,
        registerTime: null,
        hasFinancial: null,
        isEmphasis: null,
        isSync: null,
        syncRemark: null,
        syncTime: null,
        syncId: null,
        teamNo: null,
        teamNumber: null,
        lively: null,
        longitude: null,
        latitude: null,
      },
      dataRule: {
        name: [{required: true, message: '团队名称不能为空', trigger: 'change'},
            {validator: validateName, trigger: 'change'}],
        expectTeamNum: [{required: true, message: '团队人数不能为空', trigger: 'change'}],
        civilRegistration: [{required: true, message: '团队性质不能为空', trigger: 'change'}],
        founded: [{validator: validateFounded, trigger: 'change'}],
        businessSupervisoryUnit: [{required: true, message: '业务主管单位不能为空', trigger: 'change'}],
        registerPlace: [{validator: validateRegisterPlace, trigger: 'change'}],
        orgCodeList: [{required: true, message: '上级组织不能为空', trigger: 'change'}],
        attachmentList: [{required: true, message: '附件不能为空', trigger: 'change'}],
        hasRegist: [{required: true, message: '是否有注册号不能为空', trigger: 'change'}],
        registerCard: [{required: true, message: '注册号不能为空', trigger: 'change'}],
        belongFieldList: [{required: true, message: '所属领域不能为空', trigger: 'change'}],
        workAddress: [{required: true, message: '办公地点不能为空', trigger: 'change'},
            {validator: validateAddress, trigger: 'change'}],
        introduction: [{required: true, message: '团队介绍不能为空', trigger: 'change'}],
        services: [{required: true, message: '服务内容不能为空', trigger: 'change'}],
        teamPhoto: [{required: true, message: '团队LOGO不能为空', trigger: 'change'}],
        curatorName: [{required: true, message: '负责人姓名不能为空', trigger: 'change'}],
        curatorCard: [{required: true, message: '负责人证件号码不能为空', trigger: 'change'}],
        curatorContact: [
          {required: true, message: '负责人联系方式不能为空', trigger: 'change'},
          {validator: validateContactPhone, trigger: 'change'}
        ],
        curatorPartyMember: [{required: true, message: '负责人是否为党员不能为空', trigger: 'change'}],
        adminName: [{required: true, message: '管理员姓名不能为空', trigger: 'change'}],
        adminCard: [{required: true, message: '管理员证件号码不能为空', trigger: 'change'}],
        adminContact: [
          {required: true, message: '管理员联系方式不能为空', trigger: 'change'},
          {validator: validateContactPhone, trigger: 'change'}
        ],
        adminPartyMember: [{required: true, message: '管理员是否为党员不能为空', trigger: 'change'}],
        socialOrgFlag: [{required: true, message: '是否社会工作服务机构不能为空', trigger: 'change'}],
        serviceTargetList: [{required: true, message: '服务对象不能为空', trigger: 'change'}],
        partyOrgFlag: [{required: true, message: '是否成立党组织不能为空', trigger: 'change'}],
        teamNature: [{required: true, message: '团队性质不能为空', trigger: 'change'}],
        teamClassifyList: [{required: true, message: '团队类型不能为空', trigger: 'change'}]
      },
      infoRegisterTemplateDownloadPath: '/admin/zyz/team/template',
      infoRegisterTemplateFileName: '志愿服务团队信息登记表.docx'
    }
  },
  components: {
    moment,
    AMapInfo
  },
  methods: {
    async init(id, self) {
      this.dataForm = _.cloneDeep(this.initForm)
      this.dataForm.id = id || null
      this.selfEdit = self || false
      this.visible = true
      await this.getOrg()
      await this.getBelongFields()
      if (this.dataForm.id) {
        await this.getTeamMembers()
      }
      await this.getInitData()
    },
    async getInitData() {
      this.$nextTick(() => {
        this.$refs['dataForm']?.resetFields()
        this.dataForm.status = null
        this.dataForm.auditOrgCode = null
        this.dataForm.teamStatus = null
        this.dataForm.registerTime = null
        this.dataForm.hasFinancial = null
        this.dataForm.isEmphasis = null
        this.dataForm.isSync = null
        this.dataForm.syncRemark = null
        this.dataForm.syncTime = null
        this.dataForm.syncId = null
        this.dataForm.teamNo = null
        this.dataForm.teamNumber = null
        this.dataForm.lively = null
        this.dataForm.updateDate = null
        this.dataForm.updater = null
        this.dataForm.createDate = null
        this.dataForm.creator = null
        if (this.dataForm.id) {
          this.$http({
            url: this.$http.adornUrl(`/admin/zyz/team/getTeamById`),
            method: 'get',
            params: this.$http.adornParams({id: this.dataForm.id})
          }).then(({data}) => {
            if (data && data.code === 0) {
              this.dataForm = data.obj
              // 处理多选字段，将逗号分隔的字符串转换为数组
              this.dataForm.serviceTargetList = this.dataForm.serviceTarget ? this.dataForm.serviceTarget.split(',') : []
              this.dataForm.teamClassifyList = this.dataForm.teamClassify ? this.dataForm.teamClassify.split(',') : []
            }
          })
        }
      })
    },
    handleAvatarSuccess(res, file) {
      if (res.success) {
        this.$message({
          message: '操作成功',
          type: 'success',
          duration: 1500
        })
        this.dataForm.teamPhoto = res.obj.path
        this.$nextTick(() => {
          this.$refs.dataForm.validateField('teamPhoto')
        })
      } else {
        this.$message.error('上传失败')
      }
    },
    beforeAvatarUpload: function (file) {
      let isAccept = ['image/jpg', 'image/jpeg', 'image/png', 'image/bmp'].indexOf(file.type) !== -1
      let isLt2M = file.size / 1024 / 1024 < 2

      if (!isAccept) {
        this.$message.error('上传图片只能是图片!')
      }
      if (!isLt2M) {
        this.$message.error('上传文件大小不能超过 2MB!')
      }
      return isAccept && isLt2M
    },
    // 表单提交
    dataFormSubmit() {
      if (this.selfEdit) {
        this.$confirm(`${this.selfEdit ? '确定进行信息变更?' : '确定进行提交操作?'}`, '提示',
            {confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning', closeOnClickModal: false}
        ).then(() => {
          this.submitFun()
        })
      } else {
        this.submitFun()
      }
    },
    submitFun() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          // 验证多选字段数量限制
          if (this.dataForm.serviceTargetList && this.dataForm.serviceTargetList.length > 5) {
            this.$message.error('服务对象最多只能选择5个')
            return
          }
          if (this.dataForm.teamClassifyList && this.dataForm.teamClassifyList.length > 5) {
            this.$message.error('团队类型最多只能选择5个')
            return
          }
          this.dataForm.orgCode = this.dataForm.orgCodeList[this.dataForm.orgCodeList.length - 1]
          // 处理多选字段，将数组转换为逗号分隔的字符串
          this.dataForm.serviceTarget = this.dataForm.serviceTargetList && this.dataForm.serviceTargetList.length > 0 ? this.dataForm.serviceTargetList.join(',') : null
          this.dataForm.teamClassify = this.dataForm.teamClassifyList && this.dataForm.teamClassifyList.length > 0 ? this.dataForm.teamClassifyList.join(',') : null
          this.submitLoading = true
          this.$http({
            url: this.$http.adornUrl(`/admin/zyz/team/${!this.dataForm.id ? 'saveTeam' : (this.selfEdit ? 'updateTeamNeedAudit' : 'updateTeam')}`),
            method: 'post',
            data: this.$http.adornData(this.dataForm)
          }).then(({data}) => {
            if (data && data.code === 0) {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 1500,
                onClose: () => {
                  this.submitLoading = false
                  this.visible = false
                  this.$emit('refreshDataList')
                }
              })
            } else if (data && data.code === 303) {
              this.submitLoading = false
              for (let it of data.obj) {
                this[`${it.field}Error`] = it.message
              }
            } else {
              this.submitLoading = false
              this.$message.error(data.msg)
            }
          })
        }
      })
    },
    //获取所属区域
    async getOrg() {
      await this.$http({
        url: this.$http.adornUrl(`/admin/org/getOrgTreeOfAll`),
        method: 'get',
        params: this.$http.adornParams()
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.orgList = this.getTreeData(data.obj)
        } else {
          this.orgList = []
        }
      })
    },
    // *处理点位分类最后children数组为空的状态
    getTreeData: function (data) {
      let that = this
      // 循环遍历json数据
      data.forEach(function (e) {
        if (!e.children || e.children.length < 1) {
          e.children = undefined
        } else {
          // children若不为空数组，则继续 递归调用 本方法
          that.getTreeData(e.children)
        }
      })
      return data
    },
    //获取所属领域
    async getBelongFields() {
      await this.$http({
        url: this.$http.adornUrl(`/admin/zyz/platform/belongFieldDict/getTopBelongField`),
        method: 'get'
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.belongFieldList = data.obj
        } else {
          this.belongFieldList = []
        }
      })
    },
    async getTeamMembers() {
      await this.$http({
        url: this.$http.adornUrl(`/admin/zyz/volunteer/team/getTeamMembers`),
        method: 'get',
        params: this.$http.adornParams({
          'teamId': this.dataForm.id
        })
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.teamMemberList = data.obj
        } else {
          this.teamMemberList = []
        }
      })
    },
    handleChange(value) {
      this.dataForm.orgCode = value && value.length > 0 ? value[value.length - 1] : null
      // console.log(this.dataForm.orgCode)
    },
    belongFieldsSelect(value) {
      this.dataForm.belongFields = value && value.length > 0 ? value.join(',') : null
      // console.log(this.dataForm.belongFields)
    },
    adminChange(value) {
      if (!value || value === '') {
        this.dataForm.adminName = null
        this.dataForm.adminContact = null
        this.dataForm.adminCard = null
        return;
      }
      let filterData = _.filter(this.teamMemberList, ['certificateId', value])
      if (!filterData || filterData.length === 0) {
        return
      }
      let admin = filterData[0]
      this.dataForm.adminName = admin.volunteerName
      this.dataForm.adminContact = admin.phone
      this.dataForm.adminPartyMember = admin.party
      this.dataForm.adminCard = value
    },
    // 上传成功
    uploadSuccess(res, file, fileList) {
      if (res.success) {
        this.$message({
          message: '操作成功',
          type: 'success',
          duration: 1500
        })
        let file = {
          name: res.obj.fileName,
          path: res.obj.path
        }
        this.dataForm.attachmentList.push(file)
        this.$nextTick(() => {
          this.$refs.dataForm.validateField('attachmentList')
        })
      } else {
        this.$message.error('上传失败')
      }
    },
    handleRemove(file) {
      let index = _.findIndex(this.dataForm.attachmentList, {url: file.url})
      this.dataForm.attachmentList.splice(index, 1)
    },
    beforeUpload(file) {
      const isLt2M = file.size / 1024 / 1024 < 2
      if (!isLt2M) {
        this.$message.error('上传文件大小不能超过 2M!')
        return false
      }
    },
    download(file) {
      window.open(this.$http.adornAttachmentUrl(file.path), '_blank')
      // this.$http({
      //   url: this.$http.adornAttachmentUrl(file.path),
      //   method: 'get',
      //   responseType: 'arraybuffer',
      //   params: this.$http.adornParams()
      // }).then(({data}) => {
      //   console.info('asdasdsad', data)
      //   if (data.code && data.code !== 0) {
      //     this.$message.error('下载失败')
      //   } else {
      //     let blob = new Blob([data], {type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8'})
      //     let objectUrl = URL.createObjectURL(blob)
      //     let a = document.createElement('a')
      //     // window.location.href = objectUrl
      //     a.href = objectUrl
      //     a.download = file.name
      //     a.click()
      //     URL.revokeObjectURL(objectUrl)
      //   }
      // })
    },
    showMap() {
        this.mapVisible = true
    },
    addressClear() {
        this.dataForm.workAddress = null
        this.dataForm.longitude = null
        this.dataForm.latitude = null
    },
    downloadTemplate () {
      this.$http({
        url: this.$http.adornUrl(this.infoRegisterTemplateDownloadPath),
        method: 'get',
        responseType: 'arraybuffer',
        params: this.$http.adornParams()
      }).then(({data}) => {
        if (data.code && data.code !== 0) {
          this.$message.error('下载失败')
        } else {
          let blob = new Blob([data], {type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document;charset=utf-8'})
          let objectUrl = URL.createObjectURL(blob)
          let a = document.createElement('a')
          // window.location.href = objectUrl
          a.href = objectUrl
          a.download = this.infoRegisterTemplateFileName
          a.click()
          URL.revokeObjectURL(objectUrl)
          this.$message({
            type: 'success',
            message: '信息登记表模板下载成功'
          })
        }
      })
    }
  }
}
</script>
