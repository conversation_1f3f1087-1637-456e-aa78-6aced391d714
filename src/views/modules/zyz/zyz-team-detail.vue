<template>
  <el-dialog
      :title="!isAudit? '团队详情' : '审核'"
      :close-on-click-modal="false"
      width="80%"
      append-to-body
      :visible.sync="visible">
    <el-form :model="dataForm" ref="dataForm"
             label-width="140px">
      <div style="color: red; margin-bottom: 20px" v-if="auditType && auditType === 1">*标红属性存在信息变更，请审核这部分信息！</div>
      <el-card class="box-card">
        <div slot="header" class="clearfix"><span>团队基本信息</span></div>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="团队名称" prop="name">
              <el-input disabled v-model="dataForm.name"/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="团队预计成员人数" prop="expectTeamNum">
              <el-input-number style="width: 100%" v-model="dataForm.expectTeamNum" disabled controls-position="right" :min="0"/>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="团队性质" prop="civilRegistration">
              <el-radio-group disabled v-model="dataForm.civilRegistration">
                <el-radio :label="true">民政注册团队</el-radio>
                <el-radio :label="false">非民政注册团队</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="dataForm.civilRegistration && dataForm.civilRegistration === true ? '注册时间' :'成立时间'" prop="founded">
              <el-date-picker
                  disabled
                  style="width: 100%"
                  v-model="dataForm.founded"
                  value-format="yyyy-MM-dd"
                  type="date"
                  clearable/>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="业务主管单位" prop="businessSupervisoryUnit">
              <el-input disabled v-model="dataForm.businessSupervisoryUnit" clearable/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="dataForm.civilRegistration && dataForm.civilRegistration === true ? '注册地点' : '所在地(街道/社区)'" prop="registerPlace">
              <el-input disabled v-model="dataForm.registerPlace" clearable/>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item :label="dataForm.civilRegistration && dataForm.civilRegistration === true ? '附件（营业执照）' : '附件'" prop="attachmentList">
              <el-upload
                  disabled
                  class="upload-demo"
                  :action="this.$http.adornUrl('/admin/oss/upload')"
                  :data="uploadData"
                  :headers="myHeaders"
                  :on-success="function (res,file,fileList){return uploadSuccess(res,file,fileList)}"
                  :file-list="dataForm.attachmentList"
                  :on-preview="download"
                  :on-remove="function (file,fileList){return handleRemove(file,fileList)}"
                  :before-upload="function (file,fileList){return beforeUpload(file,fileList)}">
<!--                <el-button size="small" type="primary" plain>点击上传<i class="el-icon-upload el-icon&#45;&#45;left"/></el-button>-->
                <span style="font-size: xx-small; margin-left: 20px">{{dataForm.civilRegistration && dataForm.civilRegistration === true ? '(请上传有效期内的营业执照扫描件)' : '(主管单位盖章批复文件)'}}</span>
<!--                <div slot="tip" class="el-upload__tip">支持上传word、excel、pdf、图片</div>-->
              </el-upload>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="上级组织" v-if="this.$store.state.user.managerCapacity !== 'TEAM_ADMIN' " prop="orgCodeList">
              <el-cascader
                  disabled
                  v-model="dataForm.orgCodeList"
                  :options="orgList"
                  :show-all-levels="false"
                  :props="{checkStrictly: true,label: 'name',value: 'code'}"
                  @change="handleChange"/>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="是否有注册号" prop="hasRegist">
              <el-radio-group disabled v-model="dataForm.hasRegist">
                <el-radio :label="true">是</el-radio>
                <el-radio :label="false">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item v-if="dataForm.hasRegist" label="团队注册号" prop="registerCard">
              <el-input disabled v-model="dataForm.registerCard" clearable/>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="所属领域" prop="belongFieldList">
              <el-select disabled style="width: 100%" v-model="dataForm.belongFieldList" multiple clearable @change="belongFieldsSelect">
                <el-option
                    v-for="item in belongFieldList"
                    :key="item.typeId"
                    :label="item.typeName"
                    :value="item.typeId">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="办公地点" prop="workAddress">
              <el-input disabled type="textarea" v-model="dataForm.workAddress" rows="5"/>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="团队介绍" prop="introduction">
              <el-input disabled type="textarea" v-model="dataForm.introduction" rows="5"/>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="报名条件" prop="joinRequires">
              <el-input disabled type="textarea" v-model="dataForm.joinRequires" rows="5"/>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="服务内容" prop="services">
              <el-input disabled type="textarea"  v-model="dataForm.services" rows="5"/>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20" v-if="dataForm.teamPhoto">
          <el-col :span="12">
            <el-form-item label="团队头像" prop="teamPhoto">
              <el-image
                  :src="$http.adornAttachmentUrl(dataForm.teamPhoto)"
                  :preview-src-list="[$http.adornAttachmentUrl(dataForm.teamPhoto)]"
                  class="avatar"/>
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>
      <el-card class="box-card" style="margin-top: 20px;">
        <div slot="header" class="clearfix"><span>团队负责人信息</span></div>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="负责人姓名" prop="curatorName">
              <el-input disabled v-model="dataForm.curatorName" clearable/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="证件号码" prop="curatorCard">
              <el-input disabled v-model="dataForm.curatorCard" clearable/>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="联系电话" prop="curatorContact">
              <el-input disabled v-model="dataForm.curatorContact" clearable/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="是否党员" prop="curatorPartyMember">
              <el-radio-group disabled v-model="dataForm.curatorPartyMember">
                <el-radio :label="true">是</el-radio>
                <el-radio :label="false">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="所在单位" prop="curatorDepartment">
              <el-input disabled v-model="dataForm.curatorDepartment" clearable/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="职位" prop="curatorJob">
              <el-input disabled v-model="dataForm.curatorJob" clearable/>
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>
      <el-card class="box-card" style="margin-top: 20px;">
        <div slot="header" class="clearfix"><span>团队管理员信息</span></div>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="管理员姓名" prop="adminName">
              <el-input disabled v-model="dataForm.adminName" clearable/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="证件号码" prop="adminCard">
              <el-input disabled v-model="dataForm.adminCard" clearable/>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="联系电话" prop="adminContact">
              <el-input disabled v-model="dataForm.adminContact" clearable/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="是否党员" prop="adminPartyMember">
              <el-radio-group disabled v-model="dataForm.adminPartyMember">
                <el-radio :label="true">是</el-radio>
                <el-radio :label="false">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="所在单位" prop="adminDepartment">
              <el-input disabled v-model="dataForm.adminDepartment" clearable/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="职位" prop="adminJob">
              <el-input disabled v-model="dataForm.adminJob" clearable/>
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>
      <div style="display: flex;justify-content: center; margin: 30px" v-if="isAudit">
        <el-button style="width: 150px" round type="primary" @click="passAudit()">通过</el-button>
        <div style="margin-left: 100px">
          <el-button style="width: 150px" round type="warning"  @click="noPassAudit()">驳回</el-button></div>
      </div>
      <el-card v-if="isLog" class="box-card" style="margin-top: 20px;">
        <div slot="header" class="clearfix"><span>审核操作记录</span></div>
        <el-table
            :data="dataList"
            border
            style="width: 100%;">
          <el-table-column
              prop="operatorName"
              header-align="center"
              align="center"
              label="操作者">
          </el-table-column>
          <el-table-column
              prop="operateTime"
              header-align="center"
              align="center"
              label="操作时间">
          </el-table-column>
          <el-table-column
              prop="operatorOrgName"
              header-align="center"
              align="center"
              label="操作组织">
          </el-table-column>
<!--          <el-table-column-->
<!--              prop="auditStatusText"-->
<!--              header-align="center"-->
<!--              align="center"-->
<!--              label="流程状态">-->
<!--          </el-table-column>-->
          <el-table-column
              prop="finalRemark"
              header-align="center"
              align="center"
              label="操作详情">
          </el-table-column>
        </el-table>
      </el-card>
    </el-form>
    <n-o-pass-audit v-if="noPassAuditVisible" ref="noPassAudit" @refreshDataList="exit()"></n-o-pass-audit>
  </el-dialog>
</template>

<script>
import moment from 'moment'
import Vue from 'vue'
import _ from 'lodash'
import NOPassAudit from "./change-audit-no-pass";
import {getFathersById} from "@/utils";


export default {
  data() {
    return {
      visible: false,
      noPassAuditVisible: false,
      isAudit: false,
      isLog: true,
      auditType: 0,
      dataList: [],
      orgList: [],
      belongFieldList: [],
      fileList: [],
      serverCode: 'LocalServer',
      myHeaders: {Authorization: Vue.cookie.get('Authorization')},
      uploadData: {serverCode: 'LocalServer', media: false},
      changePropsInfo: null,
      dataForm: {
        id: null,
        version: null,
        name: null,
        expectTeamNum: 0,
        civilRegistration: false,
        founded: null,
        businessSupervisoryUnit: null,
        registerPlace: null,
        attachmentList: [],
        orgCodeList: [],
        orgCode: null,
        hasRegist: false,
        registerCard: null,
        belongFieldList: [],
        belongFields: null,
        workAddress: null,
        introduction: null,
        joinRequires: null,
        services: null,
        teamPhoto: null,
        curatorName: null,
        curatorCard: null,
        curatorContact: null,
        curatorPartyMember: false,
        curatorDepartment: null,
        curatorJob: null,
        adminName: null,
        adminCard: null,
        adminContact: null,
        adminPartyMember: false,
        adminDepartment: null,
        adminJob: null
      }
    }
  },
  components: {
    moment,
    NOPassAudit
  },
  methods: {
    async init(id, isAudit, isLog, auditType) {
      this.dataForm.id = id || null
      this.isAudit = isAudit
      this.isLog = isLog
      this.auditType = auditType || 0
      this.changePropsInfo = []
      this.getLogs()
      await this.getOrg()
      await this.getBelongFields()
      await this.getInitDate()
    },
    async getInitDate() {
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm']?.resetFields()
        if (this.dataForm.id) {
          this.$http({
            url: this.$http.adornUrl(`/admin/zyz/team/getTeamById`),
            method: 'get',
            params: this.$http.adornParams({
              id: this.dataForm.id,
              infoChangeAudit: this.auditType !== undefined && this.auditType !== '' && this.auditType === 1
            })
          }).then(({data}) => {
            if (data && data.code === 0) {
              data.obj.orgCodeList = getFathersById(data.obj.orgCode, this.orgList)
              this.dataForm = data.obj
              this.changePropsInfo = data.obj.changePropsInfo && data.obj.changePropsInfo.length > 0 ? _.map(data.obj.changePropsInfo, 'propName') : []
              this.dealInfoChangeProp()
            }
          })
        }
      })
    },
    dealInfoChangeProp () {
      let formLabelItems = document.getElementsByClassName('el-form-item__label')
      if (!formLabelItems || formLabelItems.length <= 0) {
        return
      }
      _.forEach(formLabelItems, item => {
        if (!this.changePropsInfo || this.changePropsInfo.length <= 0) {
          item.classList.remove('info_change')
        } else if (this.changePropsInfo.indexOf(item.htmlFor) >= 0) {
          item.classList.add('info_change')
        } else {
          item.classList.remove('info_change')
        }
      })
    },
    exit() {
      this.visible = false
      this.$emit('refreshDataList')
    },
    getLogs() {
      this.$http({
        url: this.$http.adornUrl(`/admin/team/audit/log/getLogsByTeamId`),
        method: 'get',
        params: this.$http.adornParams({teamId: this.dataForm.id})
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.dataList = data.obj
        } else {
          this.$message.error(data.msg)
        }
      })
    },
    //获取所属区域
   async getOrg() {
     await this.$http({
        url: this.$http.adornUrl(`/admin/org/getOrgTree`),
        method: 'get',
        params: this.$http.adornParams()
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.orgList = this.getTreeData(data.obj)
        } else {
          this.orgList = []
        }
      })
    },
    //获取所属领域
    async getBelongFields() {
      await this.$http({
        url: this.$http.adornUrl(`/admin/zyz/platform/belongFieldDict/getTopBelongField`),
        method: 'get'
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.belongFieldList = data.obj
        } else {
          this.belongFieldList = []
        }
      })
    },
    handleChange(value) {
      this.dataForm.orgCode = value && value.length > 0 ? value[value.length - 1] : null
      // console.log(this.dataForm.orgCode)
    },
    belongFieldsSelect(value) {
      this.dataForm.belongFields = value && value.length > 0 ? value.join(',') : null
      // console.log(this.dataForm.belongFields)
    },
    // *处理点位分类最后children数组为空的状态
    getTreeData: function (data) {
      let that = this
      // 循环遍历json数据
      data.forEach(function (e) {
        if (!e.children || e.children.length < 1) {
          e.children = undefined
        } else {
          // children若不为空数组，则继续 递归调用 本方法
          that.getTreeData(e.children)
        }
      })
      return data
    },
    noPassAudit() {
      this.noPassAuditVisible = true
      this.$nextTick(() => {
        this.$refs.noPassAudit.init(this.dataForm.id, false)
      })
    },
    passAudit() {
      this.$confirm(`确定通过审核操作?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        closeOnClickModal: false
      }).then(() => {
        this.$http({
          url: this.$http.adornUrl('/admin/team/audit/log/audit'),
          method: 'get',
          params: this.$http.adornParams({
            'teamIds': this.dataForm.id,
            'pass': true
          })
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.$message({
              message: '操作成功',
              type: 'success',
              duration: 1500,
              onClose: () => {
                this.visible = false
                this.$emit('refreshDataList')
              }
            })
          } else {
            this.$message.error(data.msg)
          }
        })
      })
    },
    handleAvatarSuccess(res, file) {
      if (res.success) {
        this.$message({
          message: '操作成功',
          type: 'success',
          duration: 1500
        })
        this.dataForm.teamPhoto = res.obj.path
      } else {
        this.$message.error('上传失败')
      }
    },
    beforeAvatarUpload: function (file) {
      let isAccept = ['image/jpg', 'image/jpeg', 'image/png', 'image/bmp'].indexOf(file.type) !== -1
      let isLt2M = file.size / 1024 / 1024 < 2

      if (!isAccept) {
        this.$message.error('上传图片只能是图片!')
      }
      if (!isLt2M) {
        this.$message.error('上传文件大小不能超过 2MB!')
      }
      return isAccept && isLt2M
    },
    // 上传成功
    uploadSuccess(res, file, fileList) {
      if (res.success) {
        this.$message({
          message: '操作成功',
          type: 'success',
          duration: 1500
        })
        let file = {
          name: res.obj.fileName,
          path: res.obj.path
        }
        this.dataForm.attachmentList.push(file)
      } else {
        this.$message.error('上传失败')
      }
    },
    handleRemove(file) {
      let index = _.findIndex(this.dataForm.attachmentList, {url: file.url})
      this.dataForm.attachmentList.splice(index, 1)
    },
    beforeUpload(file) {
      const isLt2M = file.size / 1024 / 1024 < 2
      if (!isLt2M) {
        this.$message.error('上传文件大小不能超过 2M!')
        return false
      }
    },
    download(file) {
      window.open(this.$http.adornAttachmentUrl(file.path), '_blank')
    }
  }
}
</script>
<style scoped lang="scss">
::v-deep .info_change {
  color: red;
}
</style>
