<template>
  <div class="mod-config">
    <el-form :inline="true" :model="dataForm" ref="dataForm" @keyup.enter.native="getDataList()">
      <el-form-item prop="key" label="关键字：">
        <el-input v-model="dataForm.key" placeholder="手机号/志愿者姓名" clearable></el-input>
      </el-form-item>
      <el-form-item label="状态：" prop="status">
        <el-select v-model="dataForm.status" clearable>
          <el-option v-for="item in [{label: '拉黑', value: 'true'}, {label: '解禁', value: 'false'}]"
                     :key="item.value" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button @click="getDataList()">查询</el-button>
        <el-button icon="el-icon-refresh" @click="resetForm()">重置</el-button>
      </el-form-item>
    </el-form>
    <div class="f-s-c"
         style="padding: 5px 0;margin-bottom:15px;display: flex ;justify-content:space-between;align-items: center ">
      <div style="font-weight: bold;">为您查询到{{ totalPage || 0 }}条数据</div>
      <div>
        <el-button icon="el-icon-plus" type="success" @click="addOrUpdateHandle()">
          新增
        </el-button>
        <el-button type="danger" @click="deleteHandle()"
                   :disabled="dataListSelections.length <= 0">批量删除
        </el-button>
      </div>
    </div>
    <el-table
        :data="dataList"
        border
        v-loading="dataListLoading"
        @selection-change="selectionChangeHandle"
        style="width: 100%;">
      <el-table-column
          type="selection"
          header-align="center"
          align="center"
          width="50">
      </el-table-column>
      <el-table-column
          prop="mobile"
          header-align="center"
          align="center"
          label="手机号码">
      </el-table-column>
      <el-table-column
          prop="name"
          header-align="center"
          align="center"
          label="用户名">
      </el-table-column>
      <el-table-column
          prop="blocked"
          header-align="center"
          align="center"
          label="状态">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.status == false" size="small" type="danger">解禁</el-tag>
          <el-tag v-else size="small">拉黑</el-tag>
        </template>
      </el-table-column>
      <el-table-column
          prop="blockTime"
          header-align="center"
          align="center"
          label="拉黑时间">
      </el-table-column>
      <el-table-column
          prop="cancelTime"
          header-align="center"
          align="center"
          label="解除时间">
      </el-table-column>
      <el-table-column
          prop="reason"
          header-align="center"
          align="center"
          :show-overflow-tooltip="true"
          label="理由">
      </el-table-column>
      <el-table-column
          fixed="right"
          header-align="center"
          align="center"
          width="150"
          label="操作">
        <template slot-scope="scope">
          <el-button v-if="scope.row.status == false" type="text" size="small" @click="bannedBlackList(scope.row.id)">
            拉黑
          </el-button>
          <el-button v-if="scope.row.status == true" type="text" size="small" @click="cancelBlackHandle(scope.row.id)">
            解禁
          </el-button>
          <el-button type="text" size="small" @click="addOrUpdateHandle(scope.row.id)">修改</el-button>
          <el-button type="text" size="small" @click="deleteHandle(scope.row.id)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
        @size-change="sizeChangeHandle"
        @current-change="currentChangeHandle"
        :current-page="pageIndex"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pageSize"
        :total="totalPage"
        layout="total, sizes, prev, pager, next, jumper">
    </el-pagination>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"></add-or-update>
    <change-num v-if="changeNumVisable" ref="changeNum" @refreshDataList="getDataList"></change-num>

  </div>
</template>

<script>
import AddOrUpdate from './zyz-black-list-add-or-update'
import ChangeNum from './zyz-balck-list-banned'
import listMixin from '@/mixins/list-mixins'

export default {
  mixins: [listMixin],
  data() {
    return {
      changeNumVisable: false,
      mixinOptions: {
        dataUrl: '/admin/zyz/black/list/pageForList',
        deleteUrl: '/admin/zyz/black/list/removeByIds'
      },
      dataForm: {
        key: '',
        status: ''
      }

    }
  },
  activated() {
    this.getDataList()
  },
  components: {
    AddOrUpdate,
    ChangeNum
  },
  methods: {
    resetForm() {
      this.$refs['dataForm']?.resetFields()
      this.getDataList()
    },
    bannedBlackList(id) {
      this.changeNumVisable = true
      this.$nextTick(() => {
        this.$refs.changeNum.init(id)
      })
    },
    // 解禁志愿者
    cancelBlackHandle(id) {
      var ids = id
      this.$confirm(`确定解禁该志愿者?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$http({
          url: this.$http.adornUrl('/admin/zyz/black/list/recall'),
          method: 'get',
          params: this.$http.adornParams({
            'id': ids
          })
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.$message({
              message: '操作成功',
              type: 'success',
              duration: 1500,
              onClose: () => {
                this.getDataList()
              }
            })
          } else {
            this.$message.error(data.msg)
          }
        })
      })
    }
  }
}
</script>
