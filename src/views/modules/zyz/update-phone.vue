<template>
  <el-dialog
      :title="'修改手机号码'"
      :close-on-click-modal="false"
      width="40%"
      :visible.sync="visible">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" label-width="120px">
      <el-form-item label-width="50px" >
        <span style="color: red">*注意：确保手机号与身份证号为同一人的信息</span>
      </el-form-item>
      <el-form-item label="手机号：" prop="phone">
         <el-input v-model="dataForm.phone" placeholder="手机号"></el-input>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="updatePhone()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import moment from 'moment'

export default {
  data() {
    var checkPhone = (rule, value, callback) => {
      var reg = /^1[3456789]\d{9}$/;
      if (!value) {
        return callback(new Error('手机号码不能为空'));
      }
      if (reg.test(value)) {
        callback();
      } else {
        callback(new Error('手机格式不对，请检查！'));
        ;
      }
    };
    return {
      visible: false,
      dataForm: {
        id: null,
        version: null,
        phone: ''
      },
      dataRule: {
        phone: [
          {validator: checkPhone, required: true, trigger: 'change'}
        ]
      },
      phoneError: null
    }
  },
  components: {
    moment
  },
  methods: {
    init(id) {
      this.dataForm.id = id || null
      this.dataForm.phone = ''
      this.visible = true
    },
    updatePhone(){
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.$confirm(`确定进行修改手机号操作，请确保手机号为本人使用手机号?`, '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
          }).then(() => {

            this.$http({
              url: this.$http.adornUrl(`/admin/zyz/volunteer/updatePhone`),
              method: 'get',
              params: this.$http.adornParams({
                'id': this.dataForm.id,
                'phone': this.dataForm.phone
              })
            }).then(({data}) => {
              if (data && data.code === 0) {
                this.$message({
                  message: '操作成功',
                  type: 'success',
                  duration: 1500,
                  onClose: () => {
                    this.visible = false
                    this.$emit('refreshDataList')
                  }
                })
              } else {
                this.$message.error(data.msg)
              }
            })

          })
        }
      })
    }
  }
}
</script>
