<template>
  <div class="mod-config">
    <el-form :inline="true" ref="dataForm" :model="dataForm" @keyup.enter.native="queryPage()">
      <el-form-item label="姓名" prop="name">
        <el-input v-model="dataForm.name" placeholder="姓名" clearable></el-input>
      </el-form-item>
      <el-form-item :label="'证件类型'" prop="certificateType">
        <el-dict :code="'certificate_type'" v-model="dataForm.certificateType"></el-dict>
      </el-form-item>
      <el-form-item label="证件号码" prop="certificateId">
        <el-input v-model="dataForm.certificateId" placeholder="请输入证件号码"></el-input>
      </el-form-item>
      <el-form-item label="手机号" prop="phone">
        <el-input v-model="dataForm.phone" placeholder="请输入手机号"></el-input>
      </el-form-item>
      <el-form-item :label="'同步状态'" prop="isSync">
        <el-dict :code="'sync_status'" v-model="dataForm.isSync"></el-dict>
      </el-form-item>
      <el-form-item label="注册时间" prop="founded">
        <el-date-picker
            v-model="dataForm.founded"
            clearable
            type="daterange"
            value-format="yyyy-MM-dd"
            format="yyyy-MM-dd"
            align="right"
            start-placeholder="开始时间"
            end-placeholder="结束时间">
        </el-date-picker>
      </el-form-item>
      <el-form-item :label="'所属区域'" prop="orgCodeList" v-if="isShow">
        <el-cascader
            placeholder="所属区域"
            v-model="dataForm.orgCodeList"
            :options="orgList"
            :show-all-levels="false"
            clearable
            :props="{checkStrictly: true,value: 'code',label: 'name'}"
        ></el-cascader>
      </el-form-item>
      <el-form-item v-if="isShow" label="是否党员" prop="isParty">
        <el-radio-group v-model="dataForm.isParty">
          <el-radio :label="null">全部</el-radio>
          <el-radio :label="true">是</el-radio>
          <el-radio :label="false">否</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item :label="'行业类别'" prop="industryCate" v-if="isShow">
        <el-dict :code="'industry_cate'" v-model="dataForm.industryCate"></el-dict>
      </el-form-item>
      <el-form-item :label="'专业能力'" prop="skill" v-if="isShow">
        <el-dict :code="'skill'" v-model="dataForm.skill"></el-dict>
      </el-form-item>
      <el-form-item v-if="isShow" label="是否活跃" prop="isLively">
        <el-radio-group v-model="dataForm.isLively">
          <el-radio :label="null">全部</el-radio>
          <el-radio :label="true">是</el-radio>
          <el-radio :label="false">否</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item v-if="isShow" label="是否注销" prop="writeOff">
        <el-radio-group v-model="dataForm.writeOff">
          <el-radio :label="null">全部</el-radio>
          <el-radio :label="true">是</el-radio>
          <el-radio :label="false">否</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item>
        <el-button type="text" size="big" @click="changeSelect()">{{ isShow ? '折叠' : '高级查询' }}</el-button>
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-search" type="warning" @click="queryPage()">查询</el-button>
        <el-button @click="resetForm()" icon="el-icon-refresh-left">重置</el-button>
      </el-form-item>
    </el-form>
    <div class="f-s-c" style="padding: 15px 0;display: flex;justify-content:space-between;align-items: center ">
      <div style="font-weight: bold;">服务总时长：{{ serviceLongCount }} 小时
        <div style="font-weight: bold;color: red" v-if="showMsg">(权限不足，无法查看此数据。请联系管理员设置数据权限)</div>
      </div>
      <div>
        <el-button v-if="isAuth('volunteer:create')" icon="el-icon-plus" type="primary" @click="addOrUpdateHandle()">
          新增
        </el-button>
        <el-button v-if="isAuth('volunteer:import')" icon="el-icon-upload2" type="success" @click="importHandle()">批量导入
        </el-button>
        <el-button icon="el-icon-plus" type="danger" @click="syncPartyInfo()">同步党员信息</el-button>
        <el-button type="info" icon="el-icon-refresh" @click="getDataList()">刷新</el-button>
      </div>
    </div>
    <el-table
        :data="dataList"
        border
        v-loading="dataListLoading"
        @selection-change="selectionChangeHandle"
        style="width: 100%;">
      <el-table-column
          type="selection"
          header-align="center"
          align="center"
          width="50">
      </el-table-column>
      <el-table-column
          prop="name"
          header-align="center"
          align="center"
          min-min-width="120"
          label="姓名">
        <template slot-scope="scope">
          <a style="cursor: pointer" @click="addOrUpdateHandle(scope.row.id, true)">{{ scope.row.name }}</a>
        </template>
      </el-table-column>
      <el-table-column
          prop="certificateTypeText"
          header-align="center"
          align="center"
          min-width="150px"
          label="证件类型">
      </el-table-column>
      <el-table-column
          prop="certificateId"
          header-align="center"
          align="center"
          min-width="200px"
          label="证件号码">
      </el-table-column>
      <el-table-column
          prop="phone"
          header-align="center"
          align="center"
          min-width="150px"
          label="手机号码">
      </el-table-column>
      <el-table-column
          prop="serviceLong"
          header-align="center"
          align="center"
          min-width="120px"
          label="服务时长">
      </el-table-column>
      <el-table-column
          prop="serviceLongThisYear"
          header-align="center"
          align="center"
          min-width="120px"
          label="当年服务时长">
      </el-table-column>
      <el-table-column
          prop="totalPoint"
          header-align="center"
          align="center"
          label="总积分">
      </el-table-column>
      <el-table-column
          prop="status"
          header-align="center"
          align="center"
          min-width="100px"
          label="志愿者状态">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.status && !scope.row.writeOff" type="success">正常</el-tag>
          <el-tag v-else-if="scope.row.writeOff" type="danger">注销</el-tag>
          <el-tag v-if="!scope.row.status" type="warning">冻结</el-tag>
        </template>
      </el-table-column>
      <el-table-column
          prop="isSyncText"
          header-align="center"
          align="center"
          min-width="100px"
          label="同步状态">
        <template slot-scope="scope">
          <!--          <div v-if="scope.row.isSyncText === '同步失败'" style="color: #f6070f;cursor:pointer">{{scope.row.isSyncText}}</div>-->
          <!--          <div v-if="scope.row.isSyncText === '待同步'" style="color: #eee98a;cursor:pointer">{{scope.row.isSyncText}}</div>-->
          <!--          <div v-if="scope.row.isSyncText === '已同步'" style="color: #03f303;cursor:pointer">{{scope.row.isSyncText}}</div>-->
          <el-popover trigger="hover" placement="top">
            <p style="text-align: center">
              {{
                '同步时间：' + (scope.row.syncTime ? scope.row.syncTime : '')
              }}<br>{{ scope.row.syncRemark }}</p>
            <div slot="reference" class="name-wrapper">
              <el-tag v-if="scope.row.isSync === 'sync_failure'" type="danger">{{ scope.row.isSyncText }}</el-tag>
              <el-tag v-if="scope.row.isSync === 'sync_wait'" type="warning">{{ scope.row.isSyncText }}</el-tag>
              <el-tag v-if="scope.row.isSync === 'sync_success'" type="success">{{ scope.row.isSyncText }}</el-tag>
            </div>
          </el-popover>
        </template>
      </el-table-column>
      <el-table-column
          prop="founded"
          header-align="center"
          align="center"
          min-width="200px"
          label="注册时间">
      </el-table-column>
      <el-table-column
          fixed="right"
          header-align="center"
          align="center"
          min-width="200"
          label="操作">
        <template slot-scope="scope">
          <el-button type="text" size="small" @click="getTeam(scope.row.id)">所在团队</el-button>
          <el-button type="text" size="small" @click="serviceLongHandle(scope.row.id)">服务时长</el-button>
          <el-dropdown @command="" style="padding-left: 10px">
            <el-button type="text" size="small">更多操作</el-button>
            <el-dropdown-menu slot="dropdown" class="header-new-drop">
              <el-dropdown-item v-if="isAuth('volunteer:edit')" @click.native="addOrUpdateHandle(scope.row.id)">
                <el-button type="text" size="small">编辑</el-button>
              </el-dropdown-item>
              <el-dropdown-item v-if="isAuth('volunteer:sync')" @click.native="syncHandle(scope.row.id)"
                                :disabled="scope.row.isSync === 'sync_success'">
                <el-button type="text" size="small">同步</el-button>
              </el-dropdown-item>
              <el-dropdown-item v-if="isAuth('volunteer:freeze')" @click.native="freeze(scope.row.id)">
                <el-button type="text" size="small">{{ scope.row.status ? '冻结' : '解除冻结' }}</el-button>
              </el-dropdown-item>
              <el-dropdown-item v-if="isAuth('volunteer:move')" @click.native="moveToDirtyHandle(scope.row.id)">
                <el-button type="text" size="small">解绑</el-button>
              </el-dropdown-item>
              <el-dropdown-item v-if="isAuth('volunteer:updatePhone')" @click.native="updatePhoneHandle(scope.row.id)">
                <el-button type="text" size="small">修改手机号</el-button>
              </el-dropdown-item>
              <el-dropdown-item @click.native="certificateQueryHandle(scope.row.id)">
                <el-button type="text" size="small">志愿者证书查询</el-button>
              </el-dropdown-item>
              <el-dropdown-item @click.native="badgeQueryHandle(scope.row.id)">
                <el-button type="text" size="small">志愿勋章查询</el-button>
              </el-dropdown-item>
              <!--              <el-dropdown-item :command="beforeHandleCommand(scope.row.id,'d')">删除</el-dropdown-item>-->
            </el-dropdown-menu>
          </el-dropdown>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
        @size-change="sizeChangeHandle"
        @current-change="currentChangeHandle"
        :current-page="pageIndex"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pageSize"
        :total="totalPage"
        layout="total, sizes, prev, pager, next, jumper">
    </el-pagination>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"></add-or-update>
    <!-- 弹窗, 导入 -->
    <import v-if="importVisible" ref="import" @refreshDataList="getDataList"></import>
    <!-- 获取所在团队 -->
    <get-team v-if="teamVisible" ref="getTeam" @refreshDataList="getDataList"></get-team>
    <service-long v-if="serviceLongVisible" ref="serviceLong" @refreshDataList="getDataList"></service-long>
    <!-- 弹窗, 修改手机号 -->
    <update-phone v-if="updatePhoneVisible" ref="updatePhone" @refreshDataList="getDataList"></update-phone>
    <volunteer-sync-party v-if="volunteerSyncPartyVisible" ref="volunteerSyncParty"
                          @refreshDataList="getDataList"></volunteer-sync-party>
    <certificate-query v-if="certificateQueryVisible" ref="certificateQuery"></certificate-query>
    <badge-query v-if="badgeQueryVisible" ref="badgeQuery"></badge-query>
  </div>
</template>

<script>
import AddOrUpdate from './volunteer-add-or-update'
import Import from './volunteer-import'
import GetTeam from './team'
import ServiceLong from './volunteer-service-long'
import UpdatePhone from './update-phone'
import VolunteerSyncParty from "@/views/modules/zyz/volunteer-sync-party";
import CertificateQuery from "@/views/modules/zyz/volunteer-certificate-query";
import BadgeQuery from "@/views/modules/zyz/volunteer-badge-query";
import {treeDataTranslate} from '@/utils'

export default {
  data() {
    return {
      dataForm: {
        name: '',
        certificateType: '',
        phone: '',
        certificateId: '',
        isSync: '',
        industryCate: '',
        orgCode: '',
        orgCodeList: [],
        isParty: null,
        volunteerStatus: '',
        founded: '',
        skill: '',
        isLively: null,
        writeOff: null
      },
      showMsg: false,
      isShow: false,
      serviceLongCount: '',
      dataList: [],
      orgList: [],
      pageIndex: 1,
      pageSize: 10,
      totalPage: 0,
      dataListLoading: false,
      dataListSelections: [],
      addOrUpdateVisible: false,
      updatePhoneVisible: false,
      teamVisible: false,
      serviceLongVisible: false,
      importVisible: false,
      volunteerSyncPartyVisible: false,
      certificateQueryVisible: false,
      badgeQueryVisible: false
    }
  },
  components: {
    AddOrUpdate,
    Import,
    GetTeam,
    ServiceLong,
    UpdatePhone,
    VolunteerSyncParty,
    CertificateQuery,
    BadgeQuery
  },
  activated() {
    this.queryPage()
    this.getOrg()
  },
  methods: {
    queryPage() {
      this.pageIndex = 1
      this.getDataList()
      this.getServiceLongCount()
    },
    // 获取数据列表
    getDataList() {
      this.dataListLoading = true
      this.$http({
        url: this.$http.adornUrl('/admin/zyz/volunteer/getPages'),
        method: 'post',
        data: this.$http.adornData({
          'currentPage': this.pageIndex,
          'pageSize': this.pageSize,
          'name': this.dataForm.name,
          'certificateType': this.dataForm.certificateType,
          'certificateId': this.dataForm.certificateId,
          'phone': this.dataForm.phone,
          'isSync': this.dataForm.isSync,
          'orgCodeLink': this.dataForm.orgCodeList.join(','),
          'skill': this.dataForm.skill,
          'isParty': this.dataForm.isParty,
          'industryCate': this.dataForm.industryCate,
          'isLively': this.dataForm.isLively,
          'volunteerStatus': this.dataForm.volunteerStatus,
          'status': this.dataForm.status,
          'foundedStartDate': this.dataForm.founded != null ? this.dataForm.founded[0] : null,
          'foundedEndDate': this.dataForm.founded != null ? this.dataForm.founded[1] : null,
          'writeOff': this.dataForm.writeOff
        })
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.dataList = data.obj.records
          this.totalPage = data.obj.total
        } else {
          this.dataList = []
          this.totalPage = 0
        }
        this.dataListLoading = false
      })
    },
    // 每页数
    sizeChangeHandle(val) {
      this.pageSize = val
      this.pageIndex = 1
      this.getDataList()
    },
    // 当前页
    currentChangeHandle(val) {
      this.pageIndex = val
      this.getDataList()
    },
    // 多选
    selectionChangeHandle(val) {
      this.dataListSelections = val
    },
    // 新增 / 修改
    addOrUpdateHandle(id, disabled) {
      this.addOrUpdateVisible = true
      this.$nextTick(() => {
        this.$refs.addOrUpdate.init(id, disabled)
      })
    },
    //修改手机号
    updatePhoneHandle(id) {
      this.updatePhoneVisible = true
      this.$nextTick(() => {
        this.$refs.updatePhone.init(id)
      })
    },
    serviceLongHandle(id) {
      this.serviceLongVisible = true
      this.$nextTick(() => {
        this.$refs.serviceLong.init(id)
      })
    },
    // 同步
    syncHandle(id) {
      this.$confirm(`确定要进行同步嘛，请勿重复操作，否则可能会带来不可预知的后果`, '确认同步？', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        closeOnClickModal: false
      }).then(() => {
        this.dataListLoading = true
        this.$http({
          url: this.$http.adornUrl('/admin/zyz/platform-sync/syncVolunteer'),
          method: 'post',
          params: this.$http.adornParams({
            'volunteerId': id
          })
        }).then(({data}) => {
          this.dataListLoading = false
          if (data && data.code === 0) {
            this.$message({
              message: '请求同步成功，市平台返回：（' + data.obj.message + '）',
              type: data.obj.resultCode === 'SUCCESS' ? 'success' : 'warning',
              duration: 3000,
              onClose: () => {
                this.getDataList()
              }
            })
          } else {
            this.$message.error(data.msg)
          }
        })
      })
    },
    //所在团队
    getTeam(id) {
      this.teamVisible = true
      this.$nextTick(() => {
        this.$refs.getTeam.init(id)
      })
    },
    importHandle() {
      this.importVisible = true
      this.$nextTick(() => {
        this.$refs.import.init()
      })
    },
    // 冻结
    freeze(id) {
      this.$confirm(`冻结后该账号无法正常使用本平台，确认是否冻结`, '确认冻结？', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        closeOnClickModal: false
      }).then(() => {
        this.$http({
          url: this.$http.adornUrl('/admin/zyz/volunteer/freeze'),
          method: 'get',
          params: this.$http.adornParams({
            'id': id
          })
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.$message({
              message: '操作成功',
              type: 'success',
              duration: 1500,
              onClose: () => {
                this.getDataList()
              }
            })
          } else {
            this.$message.error(data.msg)
          }
        })
      })
    },
    changeSelect() {
      this.isShow = !this.isShow
    },
    // 重置
    resetForm() {
      this.$refs['dataForm']?.resetFields()
      this.getDataList()
    },
    // 获取服务时间总长
    getServiceLongCount() {
      this.$http({
        url: this.$http.adornUrl(`/admin/zyz/volunteer/getServiceLongCount`),
        method: 'post',
        data: this.$http.adornData({
          'currentPage': this.pageIndex,
          'pageSize': this.pageSize,
          'name': this.dataForm.name,
          'certificateType': this.dataForm.certificateType,
          'certificateId': this.dataForm.certificateId,
          'phone': this.dataForm.phone,
          'isSync': this.dataForm.isSync,
          'orgCodeLink': this.dataForm.orgCodeList.join(','),
          'skill': this.dataForm.skill,
          'isParty': this.dataForm.isParty,
          'industryCate': this.dataForm.industryCate,
          'isLively': this.dataForm.isLively,
          'volunteerStatus': this.dataForm.volunteerStatus,
          'foundedStartDate': this.dataForm.founded != null ? this.dataForm.founded[0] : null,
          'foundedEndDate': this.dataForm.founded != null ? this.dataForm.founded[1] : null,
          'writeOff': this.dataForm.writeOff
        })
      }).then(({data}) => {
        // if (data) {
        //   this.serviceLongCount = data || []
        if (data && data.code === 0) {
          // this.$message({
          //   message: '操作成功',
          //   type: 'success',
          //   duration: 1500,
          //   onClose: () => {
          //     this.showMsg = false
          //     this.serviceLongCount = data.obj || 0
          //   }
          // })
          this.showMsg = false
          this.serviceLongCount = data.obj || 0
        } else {
          this.serviceLongCount = '--'
          this.showMsg = true
        }
      })
    },
    //获取所属区域
    getOrg() {
      this.$http({
        url: this.$http.adornUrl(`/admin/org/getOrgTree`),
        method: 'get',
        params: this.$http.adornParams()
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.orgList = this.getTreeData(data.obj)
        } else {
          this.orgList = []
        }
      })
    },
    moveToDirtyHandle(id) {
      this.$confirm(`确定确认将志愿者数据移除到脏数据?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        closeOnClickModal: false
      }).then(() => {
        this.$http({
          url: this.$http.adornUrl('/admin/zyz/volunteer/moveToDirty'),
          method: 'get',
          params: this.$http.adornParams({
            'id': id
          })
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.$message({
              message: '操作成功',
              type: 'success',
              duration: 1500,
              onClose: () => {
                this.getDataList()
              }
            })
          } else {
            this.$message.error(data.msg)
          }
        })
      })
    },
    // *处理点位分类最后children数组为空的状态
    getTreeData: function (data) {
      let that = this
      // 循环遍历json数据
      data.forEach(function (e) {
        if (!e.children || e.children.length < 1) {
          e.children = undefined
        } else {
          // children若不为空数组，则继续 递归调用 本方法
          that.getTreeData(e.children)
        }
      })
      return data
    },
    syncPartyInfo() {
      this.$confirm(`确定要党员信息？由于需要同步两个平台，可能会耗费比较久的时间，请耐心等待。`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        closeOnClickModal: false
      }).then(() => {
        this.volunteerSyncPartyVisible = true
        this.$nextTick(() => {
          this.$refs.volunteerSyncParty.init()
        })
      })
    },
    certificateQueryHandle(id) {
      this.certificateQueryVisible = true
      this.$nextTick(() => {
        this.$refs.certificateQuery.init(id)
      })
    },
    badgeQueryHandle(id) {
      this.badgeQueryVisible = true
      this.$nextTick(() => {
        this.$refs.badgeQuery.init(id)
      })
    }
  }
}
</script>
<style>
.el-dropdown-link {
  cursor: pointer;
  color: #409EFF;
}

.header-new-drop li {
  color: #409EFF;
}

.el-icon-arrow-down {
  font-size: 9px;
}

.demonstration {
  display: block;
  color: #8492a6;
  font-size: 14px;
  margin-bottom: 20px;
}
</style>
