<template>
  <div class="mod-list">
    <el-form :inline="true" :model="dataForm" ref="dataForm" @keyup.enter.native="getDataList()">
      <el-form-item prop="teamId" label="团队名称">
        <el-select v-model="dataForm.teamId" placeholder="请选择" clearable>
          <el-option
              v-for="item in grantedTeamList"
              :key="item.teamId"
              :label="item.teamName"
              :value="item.teamId">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item prop="volunteerName" label="志愿者姓名">
        <el-input v-model="dataForm.volunteerName" placeholder="请输入" clearable/>
      </el-form-item>
      <el-form-item prop="volunteerPhone" label="志愿者手机号码">
        <el-input v-model="dataForm.volunteerPhone" placeholder="请输入" clearable/>
      </el-form-item>
      <el-form-item label="申请时间" prop="timeRange">
        <el-date-picker v-model="dataForm.timeRange" clearable type="datetimerange" value-format="yyyy-MM-dd HH:mm:ss"
                        align="right" start-placeholder="开始时间" end-placeholder="结束时间" :default-time="['00:00:00', '23:59:59']">
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-search" type="warning" @click="getDataList()">查询</el-button>
        <el-button @click="resetForm()"icon="el-icon-refresh-left">重置</el-button>
      </el-form-item>
    </el-form>
    <div class="f-s-c" style="padding: 15px 0; display: flex; justify-content: flex-end; align-items: center">
      <div>
        <el-button icon="el-icon-download" type="success" v-loading.fullscreen.lock="exportLoading" @click="exportHandle()">导出</el-button>
      </div>
    </div>
    <el-table :data="dataList" border v-loading="dataListLoading" @selection-change="selectionChangeHandle" style="width: 100%;">
      <el-table-column type="index" header-align="center" align="center" width="50" label="序号"/>
      <el-table-column prop="teamName" header-align="center" align="center" label="团队名称"/>
      <el-table-column prop="volunteerName" header-align="center" align="center" label="志愿者姓名"/>
      <el-table-column prop="volunteerPhone" header-align="center" align="center" label="志愿者联系方式"/>
      <el-table-column prop="joinTime" header-align="center" align="center" label="选择加入时间" fixed="right">
        <template slot-scope="scope">
          {{scope.row.joinTime && scope.row.joinTime.length > 16 ? scope.row.joinTime.substr(0, 16) : scope.row.joinTime}}
        </template>
      </el-table-column>
    </el-table>
    <el-pagination @size-change="sizeChangeHandle" @current-change="currentChangeHandle" :current-page="pageIndex"
                   :page-sizes="[10, 20, 50, 100]" :page-size="pageSize" :total="totalPage" layout="total, sizes, prev, pager, next, jumper"/>
  </div>
</template>

<script>
  import listMixin from '@/mixins/list-mixins'
  export default {
    mixins: [listMixin],
    data () {
      return {
        mixinOptions: {
          dataUrl: '/admin/zyz/volunteer/joinGrantBigActivityTeam/pages',
          exportUrl: '/admin/zyz/volunteer/joinGrantBigActivityTeam/export',
          exportFileName: '志愿者加入大型赛事授权团队记录'
        },
        grantedTeamList: [],
        dataForm: {
          teamId: null,
          volunteerName: null,
          volunteerPhone: null,
          timeRange: [],
          joinTimeStart: null,
          joinTimeEnd: null,
          orders: [{column: 'joinTime', sort: 'desc'}]
        },
        exportLoading: false
      }
    },
    activated () {
      this.getGrantedTeamList()
    },
    methods: {
      resetForm() {
        this.$refs['dataForm']?.resetFields()
        this.$set(this.dataForm, 'timeRange', [])
        this.getDataList()
      },
      getGrantedTeamList() {
        this.$http({
          url: this.$http.adornUrl('/admin/zyz/team/grantBigActivity/getGrantedTeam'),
          method: 'get'
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.grantedTeamList = data.obj
          } else {
            this.$message.error(data.msg)
            this.grantedTeamList = []
          }
        })
      },
      queryBeforeHandle () {
        this.dataForm.joinTimeStart = this.dataForm.timeRange && this.dataForm.timeRange.length >= 1 ? this.dataForm.timeRange[0] : null
        this.dataForm.joinTimeEnd = this.dataForm.timeRange && this.dataForm.timeRange.length >= 2 ? this.dataForm.timeRange[1] : null
      }
    }
  }
</script>

<style lang="scss" scoped>
</style>
