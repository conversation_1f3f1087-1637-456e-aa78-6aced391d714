<template>
    <div class="mod-config">
        <el-form :inline="true" :model="dataForm" ref="dataForm" @keyup.enter="queryPage()">
            <el-form-item label="查询条件" prop="key">
                <el-input v-model="dataForm.key" placeholder="名称/编码/内容/备注" clearable></el-input>
            </el-form-item>
            <el-form-item label="证书分类" prop="category">
                <el-select v-model="dataForm.category" placeholder="请选择分类" clearable>
                    <el-option
                        v-for="item in categoryOptions"
                        :key="item.id"
                        :label="item.name"
                        :value="item.id">
                    </el-option>
                </el-select>
            </el-form-item>
            <el-form-item>
                <el-button icon="el-icon-search" type="warning" @click="queryPage()">查询</el-button>
                <el-button @click="resetForm()" icon="el-icon-refresh-left">重置</el-button>
            </el-form-item>
        </el-form>
        <div class="f-s-c" style="padding: 15px 0;display: flex;justify-content:flex-end;align-items: center ">
            <div>
                <el-button icon="el-icon-plus" type="primary" @click="addOrUpdateHandle()">新增</el-button>
                <el-button icon="el-icon-delete" type="danger" @click="deleteHandle()"
                           :disabled="dataListSelections.length <= 0">批量删除
                </el-button>
            </div>
        </div>
        <el-table
                :data="dataList"
                border
                v-loading="dataListLoading"
                @selection-change="selectionChangeHandle"
                style="width: 100%;">
            <el-table-column
                    type="selection"
                    header-align="center"
                    align="center"
                    width="50">
            </el-table-column>
            <el-table-column
                    prop="name"
                    header-align="center"
                    align="center"
                    label="证书名称">
            </el-table-column>
            <el-table-column
                    prop="code"
                    header-align="center"
                    align="center"
                    label="证书编码">
            </el-table-column>
            <el-table-column
                    prop="categoryName"
                    header-align="center"
                    align="center"
                    label="证书分类">
            </el-table-column>
            <el-table-column
                    prop="defaultCer"
                    header-align="center"
                    align="center"
                    label="默认证书">
                <template slot-scope="scope">
                    <el-tag v-if="scope.row.defaultCer === false" size="small" type="danger">否</el-tag>
                    <el-tag v-else size="small">是</el-tag>
                </template>
            </el-table-column>
            <el-table-column
                prop="realTimeCer"
                header-align="center"
                align="center"
                label="实时证书">
                <template slot-scope="scope">
                    <el-tag v-if="scope.row.realTimeCer === false" size="small" type="danger">否</el-tag>
                    <el-tag v-else size="small">是</el-tag>
                </template>
            </el-table-column>
            <el-table-column
                    prop="status"
                    header-align="center"
                    align="center"
                    label="状态">
                <template slot-scope="scope">
                    <el-tag :type="scope.row.status ? 'success' : 'info'" size="small">
                        {{ scope.row.status ? '上架中' : '已下架' }}
                    </el-tag>
                </template>
            </el-table-column>
            <el-table-column
                prop="sequence"
                header-align="center"
                align="center"
                label="排序">
            </el-table-column>
            <el-table-column
                    fixed="right"
                    header-align="center"
                    align="center"
                    width="230"
                    label="操作">
                <template slot-scope="scope">
                    <el-button type="text" size="small" @click="previewHandle(scope.row.id)">图片预览</el-button>
                    <el-button type="text" size="small" @click="addOrUpdateHandle(scope.row.id)">编辑</el-button>
                    <el-button 
                        type="text" 
                        size="small" 
                        @click="switchStatusHandle(scope.row)">
                        {{ scope.row.status ? '下架' : '上架' }}
                    </el-button>
                    <el-button type="text" size="small" @click="importHandle(scope.row.id)" :disabled="!scope.row.status">批量颁发</el-button>
                    <el-button type="text" size="small" @click="deleteHandle(scope.row.id)">删除</el-button>
                </template>
            </el-table-column>
        </el-table>
        <div class="com-pagination">
            <el-pagination
                    @size-change="sizeChangeHandle"
                    @current-change="currentChangeHandle"
                    :current-page="pageIndex"
                    :page-sizes="[10, 20, 50, 100]"
                    :page-size="pageSize"
                    :total="totalPage"
                    layout="total, sizes, prev, pager, next, jumper"
            />
        </div>
        <!-- 弹窗, 新增 / 修改 -->
        <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"></add-or-update>
        <!-- 弹窗, 导入 -->
        <import v-if="importVisible" ref="import" @refreshDataList="getDataList"></import>

        <preview v-if="previewVisible" ref="preview" ></preview>
    </div>
</template>

<script>
import AddOrUpdate from './zyz-certificate-add-or-update.vue'
import Import from './zyz-volunteer-certificate-award-import.vue'
import Preview from './imagePreview.vue'
export default {
    data() {
        return {
            dataForm: {
                key: null,
                category: null
            },
            pageIndex: 1,
            pageSize: 10,
            totalPage: 0,
            dataList: [],
            dataListLoading: false,
            addOrUpdateVisible: false,
            importVisible: false,
            previewVisible: false,
            dataListSelections: [],
            categoryOptions: [] // 分类选项
        }
    },
    components: {
        Import,
        AddOrUpdate,
        Preview
    },
    activated() {
        this.getCategoryOptions()
        this.queryPage()
    },
    methods: {
        // 获取分类选项
        getCategoryOptions() {
            this.$http({
                url: this.$http.adornUrl('/admin/zyz/certificate/category/all'),
                method: 'get'
            }).then(({data}) => {
                if (data) {
                    this.categoryOptions = data || []
                }
            })
        },
        queryPage() {
            this.pageIndex = 1
            this.getDataList()
        },

        // 获取数据列表
        getDataList() {
            this.dataListLoading = true
            this.$http({
                url: this.$http.adornUrl('/admin/zyz/certificate/pages'),
                method: 'post',
                data: this.$http.adornData({
                    'currentPage': this.pageIndex,
                    'pageSize': this.pageSize,
                    'key': this.dataForm.key,
                    'category': this.dataForm.category
                })
            }).then(({data}) => {
                if (data && data.code === 0) {
                    this.dataList = data.obj.records
                    this.totalPage = data.obj.total
                } else {
                    this.dataList = []
                    this.totalPage = 0
                }
                this.dataListLoading = false
            })
        },
        // 每页数
        sizeChangeHandle(val) {
            this.pageSize = val
            this.pageIndex = 1
            this.getDataList()
        },
        // 当前页
        currentChangeHandle(val) {
            this.pageIndex = val
            this.getDataList()
        },
        // 多选
        selectionChangeHandle(val) {
            this.dataListSelections = val
        },
        // 新增 / 修改
        addOrUpdateHandle(id, self) {
            this.addOrUpdateVisible = true
            this.$nextTick(() => {
                this.$refs.addOrUpdate.init(id, self)
            })
        },
        // 重置
        resetForm() {
            this.$refs['dataForm']?.resetFields()
            this.getDataList()
        },
        // 删除
        deleteHandle(id) {
            var ids = id ? [id] : this.dataListSelections.map(item => {
                return item.id
            })
            this.$confirm(`确定进行[${id ? '删除' : '批量删除'}]操作?`, '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning',
                closeOnClickModal: false
            }).then(() => {
                this.$http({
                    url: this.$http.adornUrl('/admin/zyz/certificate/removeByIds'),
                    method: 'get',
                    params: this.$http.adornParams({
                        'ids': ids.join(',')
                    })
                }).then(({data}) => {
                    if (data && data.code === 0) {
                        this.$message({
                            message: '操作成功',
                            type: 'success',
                            duration: 1500,
                            onClose: () => {
                                this.getDataList()
                            }
                        })
                    } else {
                        this.$message.error(data.msg)
                    }
                })
            })
        },
        importHandle(id) {
            this.importVisible = true
            this.$nextTick(() => {
                this.$refs.import.init(id)
            })
        },
        previewHandle(id) {
            this.previewVisible = true
            this.$nextTick(() => {
                this.$refs.preview.init(id)
            })
        },
        switchStatusHandle(item) {
            const status = !item.status
            this.$confirm(`确定要${status ? '上架' : '下架'}该证书吗?`, '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消', 
                type: 'warning',
                closeOnClickModal: false
            }).then(() => {
                this.$http({
                    url: this.$http.adornUrl('/admin/zyz/certificate/status/switch'),
                    method: 'post',
                    params: this.$http.adornParams({
                        'id': item.id
                    })
                }).then(({data}) => {
                    if (data && data.code === 0) {
                        this.$message({
                            message: status ? '上架成功' : '下架成功',
                            type: 'success',
                            duration: 1500,
                            onClose: () => {
                                this.getDataList()
                            }
                        })
                    } else {
                        this.$message.error(data.msg || `${status ? '上架' : '下架'}失败`)
                    }
                }).catch(() => {
                    this.$message.error(`${status ? '上架' : '下架'}失败`)
                })
            })
        },
    }
}
</script>
