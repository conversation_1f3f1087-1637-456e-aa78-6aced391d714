<template>
  <div class="mod-config">
    <!-- <el-form :inline="true" :model="dataForm" ref="dataForm" @keyup.enter.native="getDataList()">
      <el-form-item prop="key" label="关键字：">
        <el-input v-model="dataForm.key" placeholder="关键字" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-button @click="getDataList()">查询</el-button>
        <el-button icon="el-icon-refresh" @click="resetForm()">重置</el-button>
      </el-form-item>
    </el-form> -->
    <div class="f-s-c"
         style="padding: 5px 0;margin-bottom:15px;display: flex ;justify-content: space-between;">
      <div style="font-weight: bold;">为您查询到{{ totalPage || 0 }}条数据</div>
      <div>
        <el-button icon="el-icon-plus" type="primary" @click="addOrUpdateHandle()">
          新增
        </el-button>
      </div>
    </div>
    <el-table
        :data="dataList"
        border
        v-loading="dataListLoading"
        style="width: 100%;">
      <el-table-column
          type="index"
          header-align="center"
          align="center"
          width="80"
          label="序号">
      </el-table-column>
      <el-table-column
          prop="name"
          header-align="center"
          align="center"
          label="分类名称">
          <template slot-scope="scope">
            <el-button type="text" @click="viewDetailHandle(scope.row.id)">{{scope.row.name}}</el-button>
          </template>
      </el-table-column>
      <el-table-column
          prop="createDate"
          header-align="center"
          align="center"
          width="180"
          label="创建时间">
      </el-table-column>
      <el-table-column
          prop="description"
          header-align="center"
          align="center"
          label="分类说明">          
          <template slot-scope="scope">
            <el-tooltip class="description-tooltip" effect="dark" :content="scope.row.description" placement="top" popper-class="description-popper">
              <div class="description-cell">{{ scope.row.description }}</div>
            </el-tooltip>
          </template>
      </el-table-column>
      <el-table-column
          fixed="right"
          header-align="center"
          align="center"
          width="150"
          label="操作">
        <template slot-scope="scope">
          <el-button type="text" size="small" @click="addOrUpdateHandle(scope.row.id)">编辑</el-button>
          <el-button type="text" size="small" @click="deleteHandle(scope.row.id)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
        @size-change="sizeChangeHandle"
        @current-change="currentChangeHandle"
        :current-page="pageIndex"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pageSize"
        :total="totalPage"
        layout="total, sizes, prev, pager, next, jumper">
    </el-pagination>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"></add-or-update>
  </div>
</template>

<script>
import ZyzCertificateCategoryAddOrUpdate from './zyz-certificate-category-add-or-update.vue'
import listMixin from '@/mixins/list-mixins'

export default {
  mixins: [listMixin],
  data() {
    return {
      mixinOptions: {
        dataUrl: '/admin/zyz/certificate/category/pages',
        deleteUrl: '/admin/zyz/certificate/category/removeByIds'
      },
      dataForm: {
        key: ''
      }
    }
  },
  components: {
    AddOrUpdate: ZyzCertificateCategoryAddOrUpdate
  },
  methods: {
    // 查看详情（只读模式）
    viewDetailHandle(id) {
      this.addOrUpdateVisible = true
      this.$nextTick(() => {
        this.$refs.addOrUpdate.init(id, true) // 传递第二个参数表示只读模式
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.description-cell {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 3; /* 最多显示3行 */
  line-clamp: 3; /* 标准属性 */
  -webkit-box-orient: vertical;
  line-height: 1.5;
  max-height: 4.5em; /* 3行的高度 */
  word-break: break-all;
  text-align: left;
}
</style>

<style lang="scss">
.description-popper {
  max-width: 400px !important;
  white-space: normal !important;
  word-break: break-word !important;
  line-height: 1.5 !important;
  padding: 10px !important;
}
</style>
