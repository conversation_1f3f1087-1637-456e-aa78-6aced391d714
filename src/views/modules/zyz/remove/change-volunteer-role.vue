<template>
  <el-dialog
      :title="'变更角色'"
      :close-on-click-modal="false"
      width="40%"
      append-to-body
      :visible.sync="visible">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" label-width="120px">
      <el-form-item label="志愿者角色" prop="dutyType">
        <el-radio-group v-model="dataForm.dutyType">
          <el-radio :label="'duty_type_curator'">负责人</el-radio>
          <el-radio :label="'duty_type_admin'">管理员</el-radio>
          <el-radio :label="'duty_type_common'">普通成员</el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="updateRole()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import moment from 'moment'

export default {
  data() {
    return {
      visible: false,
      dataForm: {
        volunteerId: null,
        teamId: null,
        version: null,
        dutyType: null
      },
      dataRule: {
        dutyType: [
          {required: true, message: '重点团队类型不能为空', trigger: 'blur'}
        ]
      },
      livelyError: null
    }
  },
  components: {
    moment
  },
  methods: {
    init(teamId, volunteerId, dutyType) {
      this.visible = true
      this.dataForm.teamId = teamId
      this.dataForm.volunteerId = volunteerId
      this.dataForm.dutyType = dutyType
    },
    updateRole() {
      this.$http({
        url: this.$http.adornUrl(`/admin/zyz/volunteer/team/updateVolunteerRole`),
        method: 'get',
        params: this.$http.adornParams({
          'teamId': this.dataForm.teamId,
          'volunteerId': this.dataForm.volunteerId,
          'dutyType': this.dataForm.dutyType
        })
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.$message({
            message: '操作成功',
            type: 'success',
            duration: 1500,
            onClose: () => {
              this.visible = false
              this.$emit('refreshDataList')
            }
          })
        } else {
          this.$message.error(data.msg)
        }
      })
    }
  }
}
</script>
