<template>
  <el-dialog
      :title="!dataForm.id ? '新增' : '修改'"
      :close-on-click-modal="false"
      width="80%"
      :visible.sync="visible">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmit()"
             label-width="150px">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="姓名" prop="name" :error="nameError">
            <el-input v-model="dataForm.name" clearable placeholder="请输入姓名" :disabled="this.dataForm.id !== null || (this.disabled && this.disabled === true)"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="电话" prop="phone" :error="phoneError">
            <el-input v-model="dataForm.phone" clearable placeholder="请输入电话" :disabled="this.dataForm.id !== null || (this.disabled && this.disabled === true)"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="证件类型" prop="certificateType" :error="certificateTypeError">
            <el-dict :code="'certificate_type'" v-model="dataForm.certificateType"
                     :disabled="this.dataForm.id !== null || (this.disabled && this.disabled === true)"></el-dict>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="证件号码" prop="certificateId"
                        :error="certificateIdError">
            <el-input @blur="getSex()" clearable v-model="dataForm.certificateId" placeholder="请输入证件号码"
                      :disabled="this.dataForm.id !== null || (this.disabled && this.disabled === true)"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item
              v-if="this.dataForm.certificateType !== '' && this.dataForm.certificateType !== 'certificate_type_03' && !(this.disabled && this.disabled === true) "
              label="证件正面照片" prop="certificateFrontUrl">
            <el-upload class="avatar-uploader" :action="this.$http.adornUrl(`/admin/oss/upload`)" :headers="myHeaders"
                       :data="{ serverCode: this.serverCode, media: false }" :show-file-list="false"
                       :disabled="this.disabled && this.disabled === true"
                       :on-success="frontHandleAvatarSuccess" :before-upload="beforeAvatarUpload">
              <img v-if="dataForm.certificateFrontUrl" :src="$http.adornAttachmentUrl(dataForm.certificateFrontUrl)"
                   class="avatar">
              <i v-else class="el-icon-plus avatar-uploader-icon"></i>
              <div class="el-upload__tip" slot="tip" style="color: red;">只能上传图片，且不超过2M!</div>
            </el-upload>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item  v-if="this.dataForm.certificateType !== '' && this.dataForm.certificateType !== 'certificate_type_03' && (this.disabled && this.disabled === true)" label="证件正面照片"  prop="certificateFrontUrl">
            <el-image
                :src="$http.adornAttachmentUrl(dataForm.certificateFrontUrl)"
                :preview-src-list="[$http.adornAttachmentUrl(dataForm.certificateFrontUrl)]"
                class="avatar"
            >
            </el-image>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item
              v-if="this.dataForm.certificateType !== '' && this.dataForm.certificateType !== 'certificate_type_03'&& !(this.disabled && this.disabled === true)"
              label="证件反面照片" prop="certificateBackUrl">
            <el-upload class="avatar-uploader" :action="this.$http.adornUrl(`/admin/oss/upload`)" :headers="myHeaders"
                       :data="{ serverCode: this.serverCode, media: false }" :show-file-list="false"
                       :disabled="this.disabled && this.disabled === true"
                       :on-success="backHandleAvatarSuccess" :before-upload="beforeAvatarUpload">
              <img v-if="dataForm.certificateBackUrl" :src="$http.adornAttachmentUrl(dataForm.certificateBackUrl)" class="avatar">
              <i v-else class="el-icon-plus avatar-uploader-icon"></i>
              <div class="el-upload__tip" slot="tip" style="color: red;">只能上传图片，且不超过2M!</div>
            </el-upload>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item  v-if="this.dataForm.certificateType !== '' && this.dataForm.certificateType !== 'certificate_type_03' && (this.disabled && this.disabled === true)" label="证件反面照片"  prop="certificateBackUrl">
            <el-image
                :src="$http.adornAttachmentUrl(dataForm.certificateBackUrl)"
                :preview-src-list="[$http.adornAttachmentUrl(dataForm.certificateBackUrl)]"
                class="avatar"
            >
            </el-image>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item v-if="!this.dataForm.isAdult" label="监护人证件类型" prop="guardianCertificateType"
                        :error="certificateTypeError">
            <el-dict :code="'certificate_type'" v-model="dataForm.guardianCertificateType"
                     :disabled="this.dataForm.id !== null || (this.disabled && this.disabled === true)"></el-dict>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item v-if="this.dataForm.guardianCertificateType && !this.dataForm.isAdult" label="监护人证件号码"
                        prop="guardianCardId" :error="guardianCardIdError">
            <el-input v-model="dataForm.guardianCardId" placeholder="请输入监护人证件号码" clearable
                      :disabled="this.dataForm.id !== null || (this.disabled && this.disabled === true)"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item :label="'所属区域'" prop="orgCodeList">
            <el-cascader
                clearable
                style="width: 100%"
                placeholder="所属区域"
                v-model="dataForm.orgCodeList"
                :options="orgList"
                :show-all-levels="false"
                :disabled="this.disabled && this.disabled === true"
                :props="{label: 'name',value: 'code',disabled:'disabled'}"
                @change="handleChange"
            ></el-cascader>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="性别" prop="sex" :error="sexError">
            <el-radio-group v-model="dataForm.sex"
                            :disabled="this.dataForm.certificateType === 'certificate_type_03' || this.dataForm.id !== null || (this.disabled && this.disabled === true) ">
              <el-radio :label="'m'">男</el-radio>
              <el-radio :label="'f'">女</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="是否成年" prop="isAdult" :error="sexError">
            <el-radio-group v-model="dataForm.isAdult"
                            :disabled="this.dataForm.certificateType === 'certificate_type_03' || this.dataForm.id !== null || (this.disabled && this.disabled === true)">
              <el-radio :label="true">是</el-radio>
              <el-radio :label="false">否</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="地址" prop="address" :error="addressError">
            <el-input v-model="dataForm.address" clearable placeholder="请输入地址" :disabled="this.disabled && this.disabled === true"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="完整地址" prop="fullAddress">
            <el-input v-model="dataForm.fullAddress" clearable placeholder="请输入完整地址" :disabled="this.disabled && this.disabled === true"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="国家/地区" prop="country" :error="countryError">
            <el-dict :code="'country_list'" v-model="dataForm.country" :disabled="this.disabled && this.disabled === true"></el-dict>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="城市" prop="city" :error="cityError">
            <el-input v-model="dataForm.city" clearable placeholder="请输入城市" :disabled="this.disabled && this.disabled === true"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="邮箱" prop="email" :error="emailError">
            <el-input v-model="dataForm.email" clearable placeholder="请输入邮箱" :disabled="this.disabled && this.disabled === true"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="民族" prop="nation" :error="nationError">
            <el-dict :code="'nation'" v-model="dataForm.nation" :disabled="this.disabled && this.disabled === true"></el-dict>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="教育程度" prop="education" :error="educationError">
            <!--            <el-input v-model="dataForm.education" placeholder="请输入教育程度"></el-input>-->
            <el-dict :code="'education'" v-model="dataForm.education" :disabled="this.disabled && this.disabled === true"></el-dict>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="学校" prop="school" :error="schoolError">
            <el-input v-model="dataForm.school" clearable placeholder="请输入最高学历学校名称" :disabled="this.disabled && this.disabled === true"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">

        <el-col :span="12">
          <el-form-item label="政治面貌" prop="politicalScape" :error="politicalScapeError">
            <el-dict :code="'political_scape'" v-model="dataForm.politicalScape" :disabled="this.disabled && this.disabled === true"></el-dict>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="是否党员" prop="isParty">
            <el-radio-group v-model="dataForm.isParty" :disabled="this.dataForm.politicalScape !=='' || (this.disabled && this.disabled === true)">
              <el-radio :label="true">是</el-radio>
              <el-radio :label="false">否</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
          <el-col :span="6"  v-if="this.disabled === true" >
              <el-form-item label="是否实名" prop="certification"  >
                  <el-radio-group v-model="dataForm.certification"
                                disabled>
                      <el-radio :label="true">是</el-radio>
                      <el-radio :label="false">否</el-radio>
                  </el-radio-group>
              </el-form-item>
          </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="行业类别" prop="industryCate" :error="industryCateError">
            <el-dict :code="'industry_cate'" v-model="dataForm.industryCate" :disabled="this.disabled && this.disabled === true"></el-dict>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="专业技能" prop="skillList" :error="skillError">
            <el-dict
                :code="'skill'"
                v-model="dataForm.skillList"
                :multiple="true"
                :multiple-limit="5"
                :disabled="this.disabled && this.disabled === true"
                placeholder="请选择专业技能（最多5个）"></el-dict>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="服务领域" prop="serviceTypeList">
            <el-cascader
                style="width: 100%"
                v-model="dataForm.serviceTypeList"
                :options="serviceTypeList"
                :show-all-levels="false"
                :disabled="this.disabled && this.disabled === true"
                :props="{
                  checkStrictly: false,
                  label: 'label',
                  value: 'value',
                  leaf: 'leaf',
                  multiple: true
                }"
                @change="handleServiceTypeChange"
                clearable
                placeholder="请选择服务领域（最多5个）"/>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="兴趣爱好" prop="hobby" :error="hobbyError">
            <el-input type="textarea" rows="7" maxlength="2000" show-word-limit v-model="dataForm.hobby" placeholder="请输入兴趣爱好" :disabled="this.disabled && this.disabled === true"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import moment from 'moment'
import {isEmail} from '@/utils/validate'
import {getFathersById} from '@/utils'
import Vue from "vue";

export default {
  data() {
    var checkPhone = (rule, value, callback) => {
      var reg = /^1[3456789]\d{9}$/;
      if (!value) {
        return callback(new Error('手机号码不能为空'));
      }
      if (reg.test(value)) {
        callback();
      } else {
        callback(new Error('手机格式不对，请检查！'));
        ;
      }
    };
    var checkCertificateId = (rule, value, callback) => {
      var reg = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/;
      if (!value) {
        return callback(new Error('证件号码不能为空'));
      }
      console.log(this.dataForm)
      if (this.dataForm && this.dataForm.certificateType === 'certificate_type_03') {
        var sum = 0,
            weights = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2],
            codes = "10X98765432";
        for (var i = 0; i < value.length - 1; i++) {
          sum += value[i] * weights[i];
        }
        var last = codes[sum % 11]; //计算出来的最后一位身份证号码
        if (value[value.length - 1] !== last) {
          callback(new Error("你输入的身份证号非法"));
        } else {
          callback();
        }
        if (reg.test(value)) {
          callback();
        } else {
          callback(new Error('证件号码格式不对，请检查!'));
        }
      } else {
        callback();
      }
    };
    var checkGuardianCardId = (rule, value, callback) => {
      var reg = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/;
      if (!value) {
        return callback(new Error('证件号码不能为空'));
      }
      if (this.dataForm && this.dataForm.certificateType === 'certificate_type_03') {
        if (reg.test(value)) {
          callback();
        } else {
          callback(new Error('证件号码格式不对，请检查！'));
        }
      } else {
        callback();
      }
    };
    var validateEmail = (rule, value, callback) => {
      if (!value) {
        callback()
      }
      if (!isEmail(value)) {
        callback(new Error('邮箱格式错误'))
      } else {
        callback()
      }
    };
    return {
      visible: false,
      disabled: false,
      serverCode: 'LocalServer',
      myHeaders: {Authorization: Vue.cookie.get('Authorization')},
      uploadData: {serverCode: 'LocalServer', media: false},
      orgList: [],
      serviceTypeList: [],
      dataForm: {},
      initForm: {
        id: '',
        version: null,
        name: '',
        certificateFrontUrl: '',
        certificateBackUrl: '',
        email: '',
        isAdult: true,
        serviceLong: 0,
        serviceLongThisYear: 0,
        totalPoint: 0,
        certificateType: '',
        certificateId: '',
        orgCode: '',
        guardianCardId: '',
        guardianCertificateType: '',
        lively: '',
        phone: '',
        country: '',
        city: '',
        address: '',
        sex: '',
        nation: '',
        school: '',
        education: '',
        industryCate: '',
        skill: '',
        skillList: [],
        hobby: '',
        volunteerStatus: '',
        isSync: '',
        syncTime: '',
        syncPartySystem: '',
        uuId: '',
        politicalScape: '',
        isParty: false,
        orgCodeList: [],
        fullAddress: '',
        serviceType: null,
        serviceTypeList: []
      },
      dataRule: {
        certificateType: [
          {required: true, message: '证件类型（身份证还是护照等等)不能为空', trigger: 'change'}
        ],
        certificateFrontUrl: [
          {required: true, message: '证件照片正面不能为空', trigger: 'change'}
        ],
        certificateBackUrl: [
          {required: true, message: '证件照片反面不能为空', trigger: 'change'}
        ],
        certificateId: [
          {required: true, validator: checkCertificateId, trigger: 'change'}
        ],
        orgCodeList: [
          {required: true, message: '所属区域', trigger: 'change'}
        ],
        guardianCertificateType: [
          {required: true, message: '监护人证件类型', trigger: 'change'}
        ],
        guardianCardId: [
          {required: true, validator: checkGuardianCardId, trigger: 'blur'}
        ],
        name: [
          {required: true, message: '姓名不能为空', trigger: 'change'}
        ],
        phone: [
          {validator: checkPhone, required: true, trigger: 'change'}
        ],
        email: [
          {validator: validateEmail, trigger: 'change'}
        ],
        sex: [
          {required: true, message: '性别不能为空', trigger: 'change'}
        ],
        nation: [
          {required: true, message: '民族不能为空', trigger: 'change'}
        ],
        education: [
          {required: true, message: '文化程度不能为空', trigger: 'change'}
        ],
        politicalScape: [
          {required: true, message: '政治面貌不能为空', trigger: 'change'}
        ],
        skillList: [
          {required: true, message: '专业技能不能为空', trigger: 'change'}
        ],
        serviceTypeList: [
          {required: true, message: '服务领域不能为空', trigger: 'change'}
        ]
      },
      nameError: null,
      emailError: null,
      serviceLongError: null,
      serviceLongThisYearError: null,
      totalPointError: null,
      certificateTypeError: null,
      certificateIdError: null,
      orgCodeError: null,
      guardianCardIdError: null,
      livelyError: null,
      phoneError: null,
      countryError: null,
      cityError: null,
      addressError: null,
      sexError: null,
      nationError: null,
      schoolError: null,
      educationError: null,
      industryCateError: null,
      skillError: null,
      hobbyError: null,
      volunteerStatusError: null,
      isSyncError: null,
      syncTimeError: null,
      syncPartySystemError: null,
      uuIdError: null,
      politicalScapeError: null
    }
  },
  watch: {
    // 判断下拉框的值是否有改变
    'dataForm.politicalScape'(val, oldVal) {
      if (this.dataForm.politicalScape === 'probationary_party' || this.dataForm.politicalScape === 'party') {
        this.dataForm.isParty = true
      } else {
        this.dataForm.isParty = false
      }
    },
    'dataForm.certificateType'(val, oldVal) {
      if (oldVal !== '') {
        this.dataForm.certificateId = ''
        this.dataForm.guardianCertificateType = ''
        this.dataForm.isAdult = true
        this.dataForm.guardianCardId = ''
      }
    },
  },
  components: {
    moment
  },
  methods: {
    async init(id, disabled) {
      Object.assign(this.$data, this.$options.data())
      this.dataForm = _.cloneDeep(this.initForm)
      this.dataForm.id = id || null
      this.disabled = disabled || false
      if (disabled && disabled === true) {
        this.dataRule = {}
      }
      this.visible = true
      await this.getOrg()
      await this.getServiceTypeList()
      this.$nextTick(() => {
        this.$refs['dataForm']?.resetFields()
        this.dataForm.isAdult = true
        this.dataForm.guardianCardId = null
        this.dataForm.guardianCertificateType = null
        if (this.dataForm.id) {
          this.$http({
            url: this.$http.adornUrl(`/admin/zyz/volunteer/getVolunteerById`),
            method: 'get',
            params: this.$http.adornParams({id: this.dataForm.id})
          }).then(({data}) => {
            if (data && data.code === 0) {
              data.obj.orgCodeList = getFathersById(data.obj.orgCode, this.orgList)
              // 处理多选字段，将逗号分隔的字符串转换为数组
              data.obj.serviceTypeList = data.obj.serviceType ? data.obj.serviceType.split(',') : []
              data.obj.skillList = data.obj.skill ? data.obj.skill.split(',') : []
              console.log(data.obj)
              this.dataForm = data.obj
            }
          })
        }
      })
    },
    // 表单提交
    dataFormSubmit() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.dataForm.orgCode = this.dataForm.orgCodeList[this.dataForm.orgCodeList.length - 1]
          // 处理多选字段，将数组转换为逗号分隔的字符串，限制最多5个
          if (this.dataForm.serviceTypeList && this.dataForm.serviceTypeList.length > 5) {
            this.$message.error('服务领域最多只能选择5个')
            return
          }
          if (this.dataForm.skillList && this.dataForm.skillList.length > 5) {
            this.$message.error('专业技能最多只能选择5个')
            return
          }
          this.dataForm.serviceType = this.dataForm.serviceTypeList && this.dataForm.serviceTypeList.length > 0 ? this.dataForm.serviceTypeList.join(',') : null
          this.dataForm.skill = this.dataForm.skillList && this.dataForm.skillList.length > 0 ? this.dataForm.skillList.join(',') : null
          console.log(this.dataForm)
          this.$http({
            url: this.$http.adornUrl(`/admin/zyz/volunteer/${!this.dataForm.id ? 'saveVolunteer' : 'update'}`),
            method: 'post',
            data: this.$http.adornData(this.dataForm)
          }).then(({data}) => {
            if (data && data.code === 0) {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 1500,
                onClose: () => {
                  this.visible = false
                  this.$emit('refreshDataList')
                }
              })
            } else if (data && data.code === 303) {
              for (let it of data.obj) {
                this[`${it.field}Error`] = it.message
              }
            } else {
              this.$message.error(data.msg)
            }
          })
        }
      })
    },
    //根据身份证判断是否成年，以及性别
    getSex() {
      var IdCard = this.dataForm.certificateId
      if (this.dataForm.certificateType !== 'certificate_type_03') {
        return true;
      }
      if (parseInt(IdCard.substr(16, 1)) % 2 === 1) {
        this.dataForm.sex = "m"
      } else {
        this.dataForm.sex = "f"
      }
      var ageDate = new Date()
      var month = ageDate.getMonth() + 1
      var day = ageDate.getDate()
      var age = ageDate.getFullYear() - IdCard.substring(6, 10) - 1
      if (IdCard.substring(10, 12) < month || IdCard.substring(10, 12) === month && IdCard.substring(12, 14) <= day) {
        age++
      }
      if (age <= 0) {
        age = 1
      }
      if (age < 17) {
        this.dataForm.isAdult = false
      }
      if (age > 17) {
        this.dataForm.isAdult = true
      }
    },
    //获取所属区域
    async getOrg() {
      const {
        data
      } = await this.$http({
        url: this.$http.adornUrl(`/admin/org/getOrgTree`),
        method: 'get',
        params: this.$http.adornParams()
      })
      if (data && data.code === 0) {
        data.obj.forEach((it) => {
          it.disabled = false
        })
        this.orgList = this.getTreeData(data.obj)
        console.log(this.orgList)
      } else {
        this.orgList = []
      }
    },
    // *处理点位分类最后children数组为空的状态
    getTreeData: function (data) {
      let that = this
      let tmpArr = []
      // 循环遍历json数据
      data.forEach(function (e) {
        if (!e.children || e.children.length < 1) {
          if (e.level !== 3) {
            return
          }
          e.children = undefined
        } else {
          // children若不为空数组，则继续 递归调用 本方法
          e.children = that.getTreeData(e.children)
        }
        tmpArr.push(e)
      })
      return tmpArr
    },
    //获取服务领域级联数据
    async getServiceTypeList() {
      await this.$http({
        url: this.$http.adornUrl('/admin/sgb/service-type/cascader-tree'),
        method: 'get'
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.serviceTypeList = this.getServiceTypeTreeData(data.obj || [])
        } else {
          this.serviceTypeList = []
        }
      })
    },
    // 处理服务领域树形数据
    getServiceTypeTreeData: function (data) {
      let that = this
      // 循环遍历json数据
      data.forEach(function (e) {
        if (!e.children || e.children.length < 1) {
          e.children = undefined
          e.leaf = true  // 标记为叶子节点
        } else {
          e.leaf = false  // 标记为非叶子节点
          // children若不为空数组，则继续 递归调用 本方法
          that.getServiceTypeTreeData(e.children)
        }
      })
      return data
    },
    // 处理服务领域选择变化
    handleServiceTypeChange(value) {
      // 检查选择数量是否超过5个
      if (value && value.length > 5) {
        this.$message.warning('服务领域最多只能选择5个，已自动保留前5个选择')
        // 保留前5个选择
        this.$nextTick(() => {
          this.dataForm.serviceTypeList = value.slice(0, 5)
        })
      }
    },
    handleChange(value) {
      console.log(this.dataForm.orgCodeList)
    },
    frontHandleAvatarSuccess(res, file) {
      if (res.success) {
        this.$message({
          message: '操作成功',
          type: 'success',
          duration: 1500
        })
        this.dataForm.certificateFrontUrl = res.obj.path
      } else {
        this.$message.error('上传失败')
      }
    },
    backHandleAvatarSuccess(res, file) {
      if (res.success) {
        this.$message({
          message: '操作成功',
          type: 'success',
          duration: 1500
        })
        this.dataForm.certificateBackUrl = res.obj.path
      } else {
        this.$message.error('上传失败')
      }
    },
    beforeAvatarUpload: function (file) {
      let isAccept = ['image/jpg', 'image/jpeg', 'image/png', 'image/bmp'].indexOf(file.type) !== -1
      let isLt2M = file.size / 1024 / 1024 < 2

      if (!isAccept) {
        this.$message.error('上传图片只能是图片!')
      }
      if (!isLt2M) {
        this.$message.error('上传文件大小不能超过 2MB!')
      }
      return isAccept && isLt2M
    },
  }
}
</script>
