<template>
    <el-dialog :title="'图片效果预览'" :close-on-click-modal="false" :visible.sync="visible"
               width="45%">
        <el-form :model="dataForm" ref="dataForm" label-width="150px">
            <el-row :gutter="20">
                <el-col :span="24">
                    <div class="demo-image__preview"
                         v-loading="loading"
                         element-loading-text="拼命加载中"
                         element-loading-spinner="el-icon-loading"
                         element-loading-background="rgba(0, 0, 0, 0.8)">
                        <el-image
                            style="width: 90%; height: 90%; display: block; margin: auto"
                            :src="url"
                            :preview-src-list="srcList"
                            v-if="url">
                        </el-image>
                    </div>
                </el-col>
            </el-row>
            <el-row :gutter="20">
                <el-col :span="24">
                 <span style="color: red">转换时间:{{executionTime}}ms</span>
                </el-col>
            </el-row>
        </el-form>

        <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
    </span>
    </el-dialog>
</template>
<script>

export default {

    data() {
        return {
            visible: false,
            url: null,
            srcList: [],
            executionTime: '',
            loading: true,
            dataForm: {
                id: null,
            },
        }
    },

    methods: {
        init(id) {
            this.dataForm.id = id || null
            this.visible = true
            this.url = ''
            this.srcList = []
            this.executionTime = ''
            this.loading = true
            this.$nextTick(() => {
                this.$refs['dataForm']?.resetFields()
                if (this.dataForm.id) {
                    this.loading = true
                    this.$http({
                        url: this.$http.adornUrl('/admin/zyz/certificate/imagePreview'),
                        method: 'get',
                        params: this.$http.adornParams({
                            'id': id
                        })
                    }).then(({data}) => {
                        if (data && data.code === 0) {
                            console.log(data.obj.executionTime + 'ms')
                            console.log(data.obj.imagePath)
                            this.url = this.$http.adornAttachmentUrl(data.obj.imagePath)
                            this.srcList.push(this.url)
                            this.executionTime = data.obj.executionTime
                            this.loading = false
                        } else {
                            this.$message.error(data.msg)
                            this.loading = false
                        }
                    })
                }
            })
        },

    }
}
</script>

