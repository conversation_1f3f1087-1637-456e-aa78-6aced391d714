<template>
  <el-dialog
    title="志愿者证书查询"
    :close-on-click-modal="false"
    :visible.sync="visible"
    width="40%">
    <div class="mod-config">
      <!-- 证书标题横向滚动区域 -->
      <div class="certificate-tabs-container" v-loading="dataListLoading">
        <div class="certificate-tabs" ref="tabsContainer">
          <div 
            v-for="(item, index) in dataList" 
            :key="item.id" 
            :class="['certificate-tab', {active: currentIndex === index}]"
            @click="selectCertificate(index)">
            {{ item.awardCertificateName }}
          </div>
          <!-- 当没有数据时显示占位元素 -->
          <div v-if="dataList.length === 0" class="certificate-tab-placeholder">暂无证书</div>
        </div>
        <div class="tab-arrows">
          <el-button icon="el-icon-arrow-left" circle @click="scrollTabs('left')" size="small"></el-button>
          <el-button icon="el-icon-arrow-right" circle @click="scrollTabs('right')" size="small"></el-button>
        </div>
      </div>

      <!-- 证书图片展示区域 -->
      <div class="certificate-display">
        <div v-if="dataList.length > 0" class="certificate-wrapper">
          <!-- 图片容器 -->
          <div class="certificate-image-outer">
            <div v-if="currentCertificate && currentCertificate.imgUrl" class="certificate-image-container" v-loading="imageLoading">
              <img 
                :src="$http.adornAttachmentUrl(currentCertificate.imgUrl)" 
                class="certificate-image" 
                @load="handleImageLoaded"
                @error="handleImageError"
                v-show="!imageLoading && !imageLoadError"
              />
              <div v-if="imageLoadError" class="image-load-error">
                <i class="el-icon-picture-outline"></i>
                <div class="error-text">图片加载失败</div>
              </div>
            </div>
            <div v-else class="certificate-no-image" v-loading="generatingCertificate">
              <template v-if="!generatingCertificate">
                <i class="el-icon-picture-outline"></i>
                <div class="no-image-text">证书尚未生成</div>
              </template>
            </div>
          </div>

          <!-- 信息容器，独立于图片容器 -->
          <div class="certificate-info">
            <div v-if="currentCertificate">
              <div class="info-row"><span class="info-label">证书名称:</span> {{ currentCertificate.awardCertificateName }}</div>
              <div class="info-row"><span class="info-label">获得时间:</span> {{ currentCertificate.receiveDate }}</div>
              <div class="info-row" v-if="currentCertificate.generateTime">
                <span class="info-label">生成时间:</span> {{ currentCertificate.generateTime }}
              </div>
            </div>
          </div>
        </div>
        
        <!-- 空状态 -->
        <div v-if="dataList.length === 0 && !dataListLoading" class="empty-state">
          <i class="el-icon-document"></i>
          <p>暂无证书数据</p>
        </div>
      </div>
      
      <div class="com-pagination">
        <el-pagination
          @size-change="sizeChangeHandle"
          @current-change="currentChangeHandle"
          :current-page="pageIndex"
          :page-sizes="[5]"
          :page-size="pageSize"
          :total="totalPage"
          layout="total, sizes, prev, pager, next, jumper"
        />
      </div>
    </div>
  </el-dialog>
</template>

<script>
export default {
  data() {
    return {
      visible: false,
      volunteerId: null,
      pageIndex: 1,
      pageSize: 10,
      totalPage: 0,
      dataList: [],
      currentIndex: 0,
      dataListLoading: false,
      generatingCertificate: false,
      fullscreenLoading: false,
      imageLoading: false,
      imageLoadError: false,
      currentImageUrl: ''
    }
  },
  computed: {
    currentCertificate() {
      return this.dataList.length > 0 ? this.dataList[this.currentIndex] : null;
    }
  },
  watch: {
    currentCertificate(newVal, oldVal) {
      if (newVal && newVal.imgUrl) {
        const newImgUrl = this.$http.adornAttachmentUrl(newVal.imgUrl);
        if (this.currentImageUrl !== newImgUrl) {
          this.imageLoading = true;
          this.imageLoadError = false;
          this.currentImageUrl = newImgUrl;
        }
      }
      
      // 检查是否需要生成证书
      if (newVal && (!newVal.imgUrl || (newVal.realTimeCer === true)) && !this.generatingCertificate) {
        this.autoGenerateCertificate(newVal.id);
      }
    }
  },
  methods: {
    init(id) {
      this.volunteerId = id
      this.visible = true
      this.dataList = []
      this.currentIndex = 0
      this.$nextTick(() => {
        this.getDataList()
      })
    },
    // 获取数据列表
    getDataList() {
      this.dataListLoading = true
      this.$http({
        url: this.$http.adornUrl('/admin/zyz/volunteer/certificate/getPages'),
        method: 'post',
        data: this.$http.adornData({
          'currentPage': this.pageIndex,
          'pageSize': this.pageSize,
          'volunteerId': this.volunteerId
        })
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.dataList = data.obj.records
          this.totalPage = data.obj.total
          this.currentIndex = 0 // 重置为第一个
          
          // 如果当前证书没有imgUrl，自动生成
          if (this.currentCertificate && !this.currentCertificate.imgUrl) {
            this.autoGenerateCertificate(this.currentCertificate.id);
          }
        } else {
          this.dataList = []
          this.totalPage = 0
        }
        this.dataListLoading = false
      })
    },
    // 获取单个证书详情
    getCertificateDetail(id) {
      this.$http({
        url: this.$http.adornUrl(`/admin/zyz/volunteer/certificate`),
        method: 'get',
        params: this.$http.adornParams(id)
      }).then(({data}) => {
        if (data && data.code === 0) {
          // 更新当前证书数据
          const index = this.dataList.findIndex(item => item.id === id);
          if (index !== -1) {
            this.dataList.splice(index, 1, data.obj);
            // 如果是当前选中的证书，刷新一下索引
            if (this.currentIndex === index) {
              this.currentIndex = index;
            }
          }
        }
      })
    },
    // 自动生成证书
    autoGenerateCertificate(id) {
      if (!id) return;
      
      this.generatingCertificate = true;
      
      this.$http({
        url: this.$http.adornUrl('/admin/zyz/volunteer/certificate/generateCertificatePdfByIds'),
        method: 'get',
        params: this.$http.adornParams({
          'ids': id
        })
      }).then(({data}) => {
        if (data && data.code === 0) {
          // 生成成功，添加生成时间
          const now = new Date();
          const generateTime = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}-${String(now.getDate()).padStart(2, '0')} ${String(now.getHours()).padStart(2, '0')}:${String(now.getMinutes()).padStart(2, '0')}:${String(now.getSeconds()).padStart(2, '0')}`;
          
          const index = this.dataList.findIndex(item => item.id === id);
          if (index !== -1) {
            this.dataList[index].generateTime = generateTime;
          }
          
          // 获取更新后的证书详情（包含新的imgUrl）
          this.getCertificateDetail({id}).then(() => {
            this.generatingCertificate = false;
          });
        } else {
          this.$message.error(data.msg || '生成证书失败');
          this.generatingCertificate = false;
        }
      }).catch(() => {
        this.generatingCertificate = false;
      })
    },
    // 图片加载完成事件
    handleImageLoaded() {
      this.imageLoading = false;
      this.imageLoadError = false;
    },
    // 图片加载失败事件
    handleImageError() {
      this.imageLoading = false;
      this.imageLoadError = true;
    },
    // 选择证书
    selectCertificate(index) {
      if (this.currentIndex !== index) {
        const selectedCertificate = this.dataList[index];
        if (selectedCertificate) {
          // 如果是实时证书或没有图片URL，则需要重新生成
          if (selectedCertificate.realTimeCer === true || !selectedCertificate.imgUrl) {
            this.currentIndex = index;
            this.autoGenerateCertificate(selectedCertificate.id);
          } else if (selectedCertificate.imgUrl) {
            this.imageLoading = true;
            this.currentImageUrl = this.$http.adornAttachmentUrl(selectedCertificate.imgUrl);
          }
          this.currentIndex = index;
        }
      }
    },
    // 滚动标签
    scrollTabs(direction) {
      const container = this.$refs.tabsContainer
      if (container) {
        const scrollAmount = 200
        const scrollLeft = direction === 'left' 
          ? container.scrollLeft - scrollAmount 
          : container.scrollLeft + scrollAmount
        
        container.scrollTo({
          left: scrollLeft,
          behavior: 'smooth'
        })
      }
    },
    // 每页数
    sizeChangeHandle(val) {
      this.pageSize = val
      this.pageIndex = 1
      this.getDataList()
    },
    // 当前页
    currentChangeHandle(val) {
      this.pageIndex = val
      this.getDataList()
    },
    exportInfo() {
      this.fullscreenLoading = true;
      this.$http({
        url: this.$http.adornUrl('/admin/zyz/volunteer/certificate/export'),
        method: 'post',
        responseType: 'arraybuffer',
        data: this.$http.adornData({
          'volunteerId': this.volunteerId
        })
      }).then(({
        data
      }) => {
        if (data.code && data.code !== 0) {
          this.$message.error('导出失败')
        } else {
          this.fullscreenLoading = false
          let blob = new Blob([data], {
            type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8'
          })
          let objectUrl = URL.createObjectURL(blob)
          let a = document.createElement('a')
          a.href = objectUrl
          a.download = '证书获取数据.xlsx'
          a.click()
          URL.revokeObjectURL(objectUrl)
          this.$message({
            type: 'success',
            message: '导出成功'
          })
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.certificate-tabs-container {
  position: relative;
  margin-bottom: 20px;
  border-bottom: 1px solid #e4e7ed;
  padding-bottom: 10px;
  padding-right: 100px; /* 为箭头按钮留空间 */
  min-height: 50px; /* 确保即使没有内容也有最小高度 */
}

.certificate-tabs {
  display: flex;
  overflow-x: auto;
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
  white-space: nowrap;
  min-height: 40px; /* 确保空状态时仍有高度 */
}

.certificate-tabs::-webkit-scrollbar {
  display: none; /* Chrome, Safari, Opera */
}

.certificate-tab {
  padding: 10px 15px;
  cursor: pointer;
  border-radius: 4px;
  margin-right: 10px;
  background-color: #f5f7fa;
  transition: all 0.3s;
  font-size: 14px;
  flex-shrink: 0;
}

/* 添加占位元素样式 */
.certificate-tab-placeholder {
  padding: 10px 15px;
  color: #909399;
  font-size: 14px;
}

.certificate-tab.active {
  background-color: #409EFF;
  color: white;
}

.tab-arrows {
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  display: flex;
  gap: 5px;
  z-index: 10; /* 确保箭头在最上层 */
}

.certificate-display {
  display: flex;
  justify-content: center;
  align-items: flex-start;
  min-height: 300px;
  margin-top: 20px; /* 与上方区域保持距离 */
}

.certificate-wrapper {
  width: 100%;
  margin: 0 auto;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.certificate-image-outer {
  width: 100%;
  border-bottom: 1px solid #ebeef5; /* 添加底部边框分隔图片和信息 */
}

.certificate-image-container {
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f5f7fa;
  width: 100%;
  min-height: 300px; /* 设置最小高度 */
  position: relative; /* 添加相对定位 */
  overflow: hidden; /* 防止图片溢出 */
}

.certificate-image {
  width: 100%; /* 宽度占满容器 */
  height: auto; /* 高度自动等比例缩放 */
  display: block;
  object-fit: contain; /* 确保图片完整显示且不变形 */
  max-height: none; /* 移除最大高度限制，让图片自然缩放 */
}

.certificate-no-image {
  height: 400px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background-color: #f5f7fa;
}

.certificate-no-image i {
  font-size: 64px;
  color: #c0c4cc;
}

.no-image-text {
  margin-top: 10px;
  color: #909399;
  font-size: 16px;
}

.certificate-info {
  padding: 15px;
  background-color: #fff;
  font-size: 14px;
  color: #606266;
}

.info-row {
  margin-bottom: 8px;
}

.info-label {
  font-weight: bold;
  margin-right: 5px;
}

.empty-state {
  width: 100%;
  padding: 60px 0;
  text-align: center;
  color: #909399;
  margin-top: 20px;
}

.empty-state i {
  font-size: 48px;
  margin-bottom: 20px;
}

.com-pagination {
  margin-top: 20px;
}

.image-load-error {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
}

.image-load-error i {
  font-size: 64px;
  color: #c0c4cc;
}

.error-text {
  margin-top: 10px;
  color: #909399;
  font-size: 16px;
}
</style> 
