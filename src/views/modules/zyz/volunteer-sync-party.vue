<template>
  <el-dialog
      :title="'同步志愿者党员信息中，请勿关闭此页面'"
      :close-on-click-modal="false"
      width="800px"
      :visible.sync="visible">
    <el-steps :active="active" finish-status="success">
      <el-step title="步骤 1" description="同步红色管家平台"></el-step>
      <el-step title="步骤 2" description="同步苏州市智慧党建平台"></el-step>
      <el-step title="完成" description="完成"></el-step>
    </el-steps>
    <div style="margin-top: 40px;font-weight: bold;color: red"><span icon="el-icon-loading">{{ text }}<div v-loading="loading"></div></span></div>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">关闭</el-button>
    </span>
  </el-dialog>
</template>

<script>
import moment from 'moment'

export default {
  data() {
    return {
      loading: false,
      visible: false,
      active: 1,
      text: ''
    }
  },
  components: {
    moment
  },
  methods: {
    init() {
      this.visible = true
      this.$nextTick(() => {
        this.loading = true
        this.active = 1
        this.text = '正在同步红色管家'
        let cnt = 0
        let interval = setInterval(() => {
          cnt++
          if (cnt > 10) {
            clearInterval(interval)
            this.active = 3
            this.text = '同步完成'
            this.$message({
              message: '同步已完成',
              type: 'success',
              duration: 2000,
              onClose: () => {
                this.loading = false
                this.visible = false
                this.$emit('refreshDataList')
              }
            })
          }
          if (cnt > 6) {
            this.active = 2
            this.text = '正在同步苏州市智慧党建平台'
          }
        }, 1000)
      })
    }
  }
}
</script>
