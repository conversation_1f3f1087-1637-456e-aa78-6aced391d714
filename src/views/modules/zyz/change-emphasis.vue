<template>
  <el-dialog
      :title="'设为重点团队'"
      :close-on-click-modal="false"
      width="65%"
      :visible.sync="visible">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" label-width="120px">
      <el-form-item label="重点团队：" prop="isEmphasis">
        <el-radio-group v-model="dataForm.isEmphasis" >
          <el-radio :label="'emphasis_type_asso'" border size="medium">协会重点团队</el-radio>
          <el-radio :label="'emphasis_type_sub_asso'" border size="medium">分协会重点团队</el-radio>
          <el-radio :label="'emphasis_type_community'" border size="medium">社区重点团队</el-radio>
          <el-radio :label="'emphasis_type_common'" border size="medium">取消设置为重点团队</el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="updateEmphasis()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import moment from 'moment'

export default {
  data() {
    return {
      visible: false,
      dataForm: {
        id: null,
        version: null,
        isEmphasis: ''
      },
      dataRule: {
        emphasis: [
          {required: true, message: '重点团队类型不能为空', trigger: 'blur'}
        ]
      },
      livelyError: null
    }
  },
  components: {
    moment
  },
  methods: {
    init(id) {
      this.dataForm.id = id || null
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm']?.resetFields()
        if (this.dataForm.id) {
          this.$http({
            url: this.$http.adornUrl(`/admin/zyz/team`),
            method: 'get',
            params: this.$http.adornParams({ id: this.dataForm.id })
          }).then(({data}) => {
            if (data && data.code === 0) {
              this.dataForm = data.obj
            }
          })
        }
      })
    },
    updateEmphasis(){
        this.$http({
          url: this.$http.adornUrl(`/admin/zyz/team/updateEmphasis`),
          method: 'get',
          params: this.$http.adornParams({
            'teamId': this.dataForm.id,
            'emphasisType': this.dataForm.isEmphasis
          })
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.$message({
              message: '操作成功',
              type: 'success',
              duration: 1500,
              onClose: () => {
                this.visible = false
                this.$emit('refreshDataList')
              }
            })
          } else {
            this.$message.error(data.msg)
          }
        })
    }
  }
}
</script>
