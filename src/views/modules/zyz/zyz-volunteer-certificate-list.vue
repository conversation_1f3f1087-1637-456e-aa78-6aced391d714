<template>
    <div class="mod-config">
        <el-form :inline="true" :model="dataForm" ref="dataForm" @keyup.enter="queryPage()">
            <el-form-item label="志愿者姓名" prop="key">
                <el-input v-model="dataForm.key" placeholder="志愿者姓名" clearable></el-input>
            </el-form-item>
            <el-form-item label="证书" prop="awardCertificateId">
                <el-select v-model="dataForm.awardCertificateId" clearable
                           placeholder="请选择" style="width: 100%">
                    <el-option
                        v-for="item in cerList"
                        :key="item.id"
                        :label="item.name"
                        :value="item.id">
                    </el-option>
                </el-select>
            </el-form-item>
            <el-form-item>
                <el-button icon="el-icon-search" type="warning" @click="queryPage()">查询</el-button>
                <el-button @click="resetForm()" icon="el-icon-refresh-left">重置</el-button>
            </el-form-item>
        </el-form>
        <div class="f-s-c" style="padding: 15px 0;display: flex;justify-content:flex-end;align-items: center ">
            <div>
                <el-button icon="el-icon-download" type="success" v-loading.fullscreen.lock="fullscreenLoading" @click="exportInfo()">导出
                </el-button>
                <el-button icon="el-icon-delete" type="danger" @click="deleteHandle()"
                           :disabled="dataListSelections.length <= 0">批量删除
                </el-button>
                <el-button icon="el-icon-delete" type="danger" @click="generateHandle()"
                           :disabled="dataListSelections.length <= 0">批量生成
                </el-button>
            </div>
        </div>
        <el-table
                :data="dataList"
                border
                v-loading="dataListLoading"
                @selection-change="selectionChangeHandle"
                style="width: 100%;">
            <el-table-column
                    type="selection"
                    header-align="center"
                    align="center"
                    width="50">
            </el-table-column>
            <el-table-column
                    prop="volunteerName"
                    header-align="center"
                    align="center"
                    label="志愿者姓名">
                <template slot-scope="scope">
                    <a style="cursor: pointer" @click="detailHandle(scope.row.id)">{{ scope.row.volunteerName }}</a>
                </template>
            </el-table-column>
            <el-table-column
                    prop="certificateId"
                    header-align="center"
                    align="center"
                    label="证件号码">
            </el-table-column>
            <el-table-column
                    prop="phone"
                    header-align="center"
                    align="center"
                    label="手机号码">
            </el-table-column>
            <el-table-column
                    prop="awardCertificateName"
                    header-align="center"
                    align="center"
                    label="证书名称">
            </el-table-column>
            <el-table-column
                    prop="receiveDate"
                    header-align="center"
                    align="center"
                    label="获得时间">
            </el-table-column>
            <el-table-column
                    fixed="right"
                    header-align="center"
                    align="center"
                    width="150"
                    label="操作">
                <template slot-scope="scope">
                    <el-button type="text" size="small" @click="deleteHandle(scope.row.id)">删除</el-button>
                    <el-button type="text" size="small" @click="generateHandle(scope.row.id)">证书生成</el-button>
                </template>
            </el-table-column>
        </el-table>
        <div class="com-pagination">
            <el-pagination
                    @size-change="sizeChangeHandle"
                    @current-change="currentChangeHandle"
                    :current-page="pageIndex"
                    :page-sizes="[10, 20, 50, 100]"
                    :page-size="pageSize"
                    :total="totalPage"
                    layout="total, sizes, prev, pager, next, jumper"
            />
        </div>
        <!-- 弹窗, 新增 / 修改 -->
        <detail v-if="detailVisible" ref="detail" ></detail>
    </div>
</template>

<script>
import Detail from './zyz-volunteer-certificate-detail.vue'

export default {
    data() {
        return {
            dataForm: {
                key: null,
                awardCertificateId: null
            },
            pageIndex: 1,
            pageSize: 10,
            totalPage: 0,
            dataList: [],
            cerList: [],
            dataListLoading: false,
            detailVisible: false,
            dataListSelections: [],
            fullscreenLoading: false
        }
    },
    components: {
        Detail
    },
    activated() {
        this.getCertificateList()
        this.queryPage()
    },
    methods: {
        queryPage() {
            this.pageIndex = 1
            this.getDataList()
        },

        // 获取数据列表
        getDataList() {
            this.dataListLoading = true
            this.$http({
                url: this.$http.adornUrl('/admin/zyz/volunteer/certificate/getPages'),
                method: 'post',
                data: this.$http.adornData({
                    'currentPage': this.pageIndex,
                    'pageSize': this.pageSize,
                    'key': this.dataForm.key,
                    'awardCertificateId':this.dataForm.awardCertificateId
                })
            }).then(({data}) => {
                if (data && data.code === 0) {
                    this.dataList = data.obj.records
                    this.totalPage = data.obj.total
                } else {
                    this.dataList = []
                    this.totalPage = 0
                }
                this.dataListLoading = false
            })
        },
        // 每页数
        sizeChangeHandle(val) {
            this.pageSize = val
            this.pageIndex = 1
            this.getDataList()
        },
        // 当前页
        currentChangeHandle(val) {
            this.pageIndex = val
            this.getDataList()
        },
        // 多选
        selectionChangeHandle(val) {
            this.dataListSelections = val
        },
        // 详情
        detailHandle(id) {
            this.detailVisible = true
            this.$nextTick(() => {
                this.$refs.detail.init(id)
            })
        },
        // 重置
        resetForm() {
            this.$refs['dataForm']?.resetFields()
            this.getDataList()
        },
        // 删除
        deleteHandle(id) {
            var ids = id ? [id] : this.dataListSelections.map(item => {
                return item.id
            })
            this.$confirm(`确定进行[${id ? '删除' : '批量删除'}]操作?`, '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning',
                closeOnClickModal: false
            }).then(() => {
                this.$http({
                    url: this.$http.adornUrl('/admin/zyz/volunteer/certificate/removeByIds'),
                    method: 'get',
                    params: this.$http.adornParams({
                        'ids': ids.join(',')
                    })
                }).then(({data}) => {
                    if (data && data.code === 0) {
                        this.$message({
                            message: '操作成功',
                            type: 'success',
                            duration: 1500,
                            onClose: () => {
                                this.getDataList()
                            }
                        })
                    } else {
                        this.$message.error(data.msg)
                    }
                })
            })
        },
        // 生成
        generateHandle(id) {
            var ids = id ? [id] : this.dataListSelections.map(item => {
                return item.id
            })
            this.$confirm(`确定进行[${id ? '生成' : '批量生成'}]操作?`, '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning',
                closeOnClickModal: false
            }).then(() => {
                this.$http({
                    url: this.$http.adornUrl('/admin/zyz/volunteer/certificate/generateCertificatePdfByIds'),
                    method: 'get',
                    params: this.$http.adornParams({
                        'ids': ids.join(',')
                    })
                }).then(({data}) => {
                    if (data && data.code === 0) {
                        this.$message({
                            message: '操作成功',
                            type: 'success',
                            duration: 1500,
                            onClose: () => {
                                this.getDataList()
                            }
                        })
                    } else {
                        this.$message.error(data.msg)
                    }
                })
            })
        },
        getCertificateList() {
            this.$http({
                url: this.$http.adornUrl(`/admin/zyz/certificate/all`),
                method: 'get',
                params: this.$http.adornParams()
            }).then(({data}) => {
                if (data) {
                    this.cerList = data
                } else {
                    this.cerList = []
                }
            })
        },
        exportInfo() {
            this.fullscreenLoading = true;
            this.$http({
                url: this.$http.adornUrl('/admin/zyz/volunteer/certificate/export'),
                method: 'post',
                responseType: 'arraybuffer',
                data: this.$http.adornData({
                    'currentPage': this.pageIndex,
                    'pageSize': this.pageSize,
                    'key': this.dataForm.key,
                    'awardCertificateId':this.dataForm.awardCertificateId
                })
            }).then(({
                         data
                     }) => {
                if (data.code && data.code !== 0) {
                    this.$message.error('导出失败')
                } else {
                    this.fullscreenLoading = false
                    let blob = new Blob([data], {
                        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8'
                    })
                    let objectUrl = URL.createObjectURL(blob)
                    let a = document.createElement('a')
                    // window.location.href = objectUrl
                    a.href = objectUrl
                    a.download = '证书获取数据.xlsx'
                    a.click()
                    URL.revokeObjectURL(objectUrl)
                    this.$message({
                        type: 'success',
                        message: '导出成功'
                    })
                }
            })
        },
    }
}
</script>
