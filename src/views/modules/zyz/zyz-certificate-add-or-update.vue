<template>
    <el-dialog :title="!dataForm.id ? '新增' : '编辑'" :close-on-click-modal="false" :visible.sync="visible"
               width="60%">


        <el-form :model="dataForm" :rules="dataRule" ref="dataForm" label-width="150px">
            <el-row :gutter="20">
                <el-col :span="12">
                    <el-form-item label="证书名称" prop="name">
                        <el-input v-model="dataForm.name" placeholder="证书名称"></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="证书编码" prop="code">
                        <el-input v-model="dataForm.code" placeholder="证书编码"></el-input>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row :gutter="20">
                <el-col :span="12">
                    <el-form-item label="证书分类" prop="category">
                        <el-select v-model="dataForm.category" placeholder="请选择证书分类" style="width: 100%;">
                            <el-option
                                v-for="item in categoryOptions"
                                :key="item.id"
                                :label="item.name"
                                :value="item.id">
                            </el-option>
                        </el-select>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row :gutter="20">
                <el-col :span="24">
                    <el-form-item label="内容" prop="content">
                        <teditor style="width: 100%;" :value="dataForm.content" :disabled="false" ref="teditor"
                                 @changeEditorValue="changeAnswer"/>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row :gutter="20">
                <el-col :span="24">
                    <el-form-item label="备注" prop="remarks">

                        <el-input type="textarea" v-model="dataForm.remarks" placeholder="服务内容" rows="5" maxlength="200" show-word-limit/>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row :gutter="20">
                <el-col :span="12">
                    <el-form-item label="状态" prop="status">
                        <el-radio-group v-model="dataForm.status">
                            <el-radio :label="true">上架</el-radio>
                            <el-radio :label="false">下架</el-radio>
                        </el-radio-group>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                <el-form-item label="排序" prop="sequence">
                    <el-input-number v-model="dataForm.sequence" placeholder="排序"/>
                </el-form-item>
                </el-col>
            </el-row>
            <el-row :gutter="20">
                <el-col :span="12">
                    <el-form-item label="默认证书" prop="defaultCer">
                        <el-radio-group v-model="dataForm.defaultCer">
                            <el-radio :label="true">是</el-radio>
                            <el-radio :label="false">否</el-radio>
                        </el-radio-group>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row :gutter="20">
                <el-col :span="12">
                    <el-form-item label="实时数据证书" prop="realTimeCer">
                        <el-radio-group v-model="dataForm.realTimeCer">
                            <el-radio :label="true">是</el-radio>
                            <el-radio :label="false">否</el-radio>
                        </el-radio-group>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="dpi" prop="dpi">
                        <el-input-number v-model="dataForm.dpi" placeholder="dpi"/>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row :gutter="20">
                <el-col :span="12">
                    <el-form-item label="dpi说明" prop="dpi">
                        <span style="color: red">渲染图像的目标分辨率，单位为 DPI（每英寸点数）。常见的值有 72 DPI（屏幕显示）、300 DPI（打印质量）等。</span>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row :gutter="20">
                <el-col :span="24">
                    <el-form-item  label="证书pdf"  prop="attachmentList">
                        <el-upload
                            ref="doc"
                            class="upload-demo"
                            :multiple="true"
                            :action="this.$http.adornUrl('/admin/oss/upload')"
                            :headers="myHeaders"
                            :data="{serverCode: 'LocalServer', media: false}"
                            :on-success="docUploadSuccess"
                            :file-list="dataForm.attachmentList"
                            :on-remove="handleRemove"
                            :limit=1
                            accept=".pdf"
                            :before-upload="docBeforeUpload">
                            <el-button size="small" type="primary">点击上传</el-button>
                            <div slot="tip" class="el-upload__tip">只能上传'pdf'文件且文件不超过20M, 占位符支持:
                              <br>志愿者姓名：volunteerName
                              <br>身份证：idCard
                              <br>服务时长：serviceLong
                              <br>星级：star
                              <br>系统时间：sysDate
                            </div>
                        </el-upload>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>

        <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
    </el-dialog>
</template>

<script>
import Vue from "vue";
import Teditor from '@/components/tinymce'
import fileUploadMixin from '@/mixins/file-upload-mixins'
import editMixin from "@/mixins/edit-mixins";
import _ from "lodash";
export default {
    mixins: [editMixin, fileUploadMixin],
    data() {
        return {
            visible: false,
            serverCode: 'LocalServer',
            myHeaders: {Authorization: Vue.cookie.get('Authorization')},
            uploadData: {serverCode: 'LocalServer', media: false},
            fileExts: 'png',
            fileList: [],
            uploadOptions: {
                fieldName: 'img'
            },
            categoryOptions: [], // 证书分类选项
            dataForm: {
                id: null,
                version: null,
                name: null,
                code: null,
                content: null,
                remarks: null,
                status: true,
                pdfUrl: null,
                filePaths: [],
                attachmentList: [],
                defaultCer: false,
                realTimeCer: false,
                sequence: 0,
                dpi: 36,
                category: null // 证书分类
            },
            dataRule: {
                name: [
                    {required: true, message: '证书名称不能为空', trigger: 'blur'}
                ],
                code: [
                    {required: true, message: '证书编码不能为空', trigger: 'blur'}
                ],
                category: [
                    {required: true, message: '证书分类不能为空', trigger: 'change'}
                ],
                status: [
                    {required: true, message: '状态不能为空', trigger: 'blur'}
                ],
                dpi: [
                    {required: true, message: 'dpi不能为空', trigger: 'blur'}
                ],
                attachmentList: [
                    {required: true, message: 'pdf不能为空', trigger: 'blur'}
                ]
            }
        }
    },
    components: {Teditor},
    methods: {
        init(id) {
            this.dataForm.id = id || null
            this.visible = true
            this.dataForm.attachmentList = []
            this.changeAnswer('')
            // 获取证书分类选项
            this.getCategoryOptions()
            this.$nextTick(() => {
                this.$refs['dataForm']?.resetFields()
                if (this.dataForm.id) {
                    this.$http({
                        url: this.$http.adornUrl(`/admin/zyz/certificate`),
                        method: 'get',
                        params: this.$http.adornParams({id: this.dataForm.id})
                    }).then(({data}) => {
                        if (data && data.code === 0) {
                            this.dataForm = data.obj
                        }
                    })
                }
            })
        },
        // 获取证书分类选项
        getCategoryOptions() {
            this.$http({
                url: this.$http.adornUrl('/admin/zyz/certificate/category/all'),
                method: 'get'
            }).then(({data}) => {
                if (data) {
                    this.categoryOptions = data || []
                }
            })
        },
        // 表单提交
        dataFormSubmit() {
            console.log( this.dataForm)
            this.$refs['dataForm'].validate((valid) => {
                if (valid) {
                    this.$http({
                        url: this.$http.adornUrl(`/admin/zyz/certificate/${!this.dataForm.id ? 'save' : 'update'}`),
                        method: 'post',
                        data: this.$http.adornData(this.dataForm)
                    }).then(({data}) => {
                        if (data && data.code === 0) {
                            this.$message({
                                message: '操作成功',
                                type: 'success',
                                duration: 1500,
                                onClose: () => {
                                    this.visible = false
                                    this.$emit('refreshDataList')
                                }
                            })
                        } else if (data && data.code === 303) {
                            for (let it of data.obj) {
                                this[`${it.field}Error`] = it.message
                            }
                        } else {
                            this.$message.error(data.msg)
                        }
                    })
                }
            })
        },
        handleAvatarSuccess(res, file) {
            if (res.success) {
                this.$message({
                    message: '操作成功',
                    type: 'success',
                    duration: 1500
                })

                this.dataForm.pdfUrl = res.obj.path
                console.log()
            } else {
                this.$message.error('上传失败')
            }
        },
        beforeAvatarUpload: function (file) {
            console.log("文件类型"+file)
            let isLt2M = file.size / 1024 / 1024 < 2

            if (!isLt2M) {
                this.$message.error('上传文件大小不能超过 2MB!')
            }
            return  isLt2M
        },
        // 富文本赋值
        changeAnswer(html) {
            this.dataForm.content = html
        },
        // 附件上传
        docUploadSuccess (res, file, fileList) {
            this.uploadOptions.fileList = fileList
            if (!res.success) {
                return this.$message.error('上传失败,请重试')
            }
            this.$message({
                message: '操作成功',
                type: 'success',
                duration: 1500
            })
            let f = {
                name: res.obj.fileName,
                path: res.obj.path
            }
            this.dataForm.attachmentList.push(f)

        },
        handleRemove(file) {
            let index = _.findIndex(this.dataForm.attachmentList, {url: file.url})
            this.dataForm.attachmentList.splice(index, 1)
        },
        docBeforeUpload (file) {
            // 检查文件类型
            if (file.type !== 'application/pdf') {
                this.$message.error('请上传PDF格式的文件!')
                return false
            }
            // 检查文件大小
            const isLt20M = file.size / 1024 / 1024 < 20
            if (!isLt20M) {
                this.$message.error('上传文档大小不能超过 20M!')
                return false
            }
            this.canSubmit = false
            return true
        },
    }
}
</script>
