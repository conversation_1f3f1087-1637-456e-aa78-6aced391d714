<template>
    <el-dialog title="拉黑" :visible.sync="visible" :append-to-body="true" width="30%">
        <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmit()"
                 label-width="120px">
            <el-form-item label="解除时间" prop="cancelTime">
                <el-date-picker
                        v-model="dataForm.cancelTime"
                        value-format="yyyy-MM-dd HH:mm:ss"
                        type="datetime"
                        :picker-options="{
                  min: new Date()}"
                        placeholder="解除时间">
                </el-date-picker>
            </el-form-item>
        </el-form>
        <span slot="footer" class="dialog-footer">
			<el-button @click="visible = false">取消</el-button>
			<el-button type="primary" @click="dataFormSubmit()">确定</el-button>
		</span>
    </el-dialog>
</template>

<script>
export default {
    data() {
        return {
            visible: false,
            dataList: [],
            dataListLoading: false,
            dataForm: {
                id: null,
                cancelTime: ''
            },
            dataRule: {
                cancelTime: [{
                    required: true,
                    message: '拉黑时间不能为空',
                    trigger: 'blur'
                }]
            },
            goodsCountError: null
        }
    },

    methods: {
        init(id) {
            this.dataForm.id = id || null
            this.visible = true
            this.$refs['dataForm']?.resetFields()
        },
        // 表单提交
        dataFormSubmit() {
            this.$refs['dataForm'].validate((valid) => {
                if (valid) {
                    this.$http({
                        url: this.$http.adornUrl('/admin/zyz/black/public/list/black'),
                        method: 'post',
                        data: this.$http.adornData(this.dataForm)
                    }).then(({
                                 data
                             }) => {
                        if (data && data.code === 0) {
                            this.$message({
                                message: '操作成功',
                                type: 'success',
                                duration: 1000,
                                onClose: () => {
                                    this.visible = false
                                    this.$emit('refreshDataList')
                                }
                            })
                        } else {
                            this.$message.error(data.msg)
                        }
                    })
                }
            })
        }
    }
}
</script>
