<template>
    <el-dialog
            :title="!dataForm.id ? '新增' : '修改'"
            :close-on-click-modal="false"
            :visible.sync="visible">
        <el-form :model="dataForm" :rules="dataRule" ref="dataForm"
                 label-width="80px">
            <el-row :gutter="20">
                <el-col :span="6">
                    <el-form-item label="手机号码" prop="mobile" :error="errors['mobile']">
                        <el-input v-model="dataForm.mobile" placeholder="手机号码"></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="姓名" prop="name" :error="errors['name']">
                        <el-input v-model="dataForm.name" placeholder="姓名"></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="解除时间" prop="cancelTime" :error="errors['cancelTime']">
                        <el-date-picker
                                v-model="dataForm.cancelTime"
                                value-format="yyyy-MM-dd HH:mm:ss"
                                type="datetime"
                                :picker-options="{
                  min: new Date()}"
                                placeholder="解除时间">
                        </el-date-picker>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row :gutter="20">
                <el-col :span="20">
                    <el-form-item label="理由" prop="reason" :error="errors['reason']">
                        <el-input type="textarea" :rows="5" maxlength="500" show-word-limit v-model="dataForm.reason"
                                  placeholder="理由"></el-input>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
    </el-dialog>
</template>

<script>
import moment from 'moment'
import editMixin from '@/mixins/edit-mixins'

export default {
    mixins: [editMixin],
    data() {
        return {
            editOptions: {
                initUrl: '/admin/zyz/black/public/list',
                saveSuffix: 'saveOrUpdateBlackList',
                updateSuffix: 'saveOrUpdateBlackList'
            },
            dataForm: {
                mobile: '',
                volunteerId: '',
                name: null,
                blocked: '',
                blockTime: '',
                cancelTime: '',
                reason: '',
                breakNums: ''
            },
            dataRule: {
                mobile: [
                    {required: true, message: '手机号不能为空', trigger: 'blur'}
                ],
                name: [
                    {required: true, message: '姓名不能为空', trigger: 'blur'}
                ],
                cancelTime: [
                    {required: true, message: '解除时间不能为空', trigger: 'blur'}
                ],
                reason: [
                    {required: true, message: '理由不能为空', trigger: 'blur'}
                ]
            }
        }
    },
    components: {
        moment
    },
    methods: {}
}
</script>
