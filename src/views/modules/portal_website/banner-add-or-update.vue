<template>
  <div>
    <el-dialog
        :title="!dataForm.id ? '新增' : '修改'"
        :close-on-click-modal="false"
        :visible.sync="visible" width="80%">
      <el-form :model="dataForm" :rules="dataRule" ref="dataForm" label-width="100px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="栏目：" prop="categoryIds" :error="errors['categoryIds']">
              <el-cascader
                  style="width: 50%"
                  clearable
                  placeholder="请选择"
                  v-model="dataForm.categoryIds"
                  :options="categories"
                  :props="{checkStrictly: true}"
              ></el-cascader>
              <!--              <el-cascader v-model="dataForm.categoryIds" :data="categories"></el-cascader>-->
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="标题：" prop="title" :error="errors['title']">
              <el-input v-model="dataForm.title" placeholder="请输入" clearable></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="图片：" prop="imageUrl">
              <el-upload
                  class="avatar-uploader"
                  :action="this.$http.adornUrl(`/admin/oss/upload`)"
                  :headers="headers"
                  :data="{serverCode: uploadOptions.serverCode, media: false}"
                  :show-file-list="false"
                  :on-success="successHandle"
                  :on-change="changHandle"
                  :on-exceed="exceedHandle"
                  :before-upload="beforeUploadHandle">
                <img v-if="dataForm.imageUrl" :src="$http.adornAttachmentUrl(dataForm.imageUrl)" class="avatar">
                <i v-else class="el-icon-plus avatar-uploader-icon"></i>
              </el-upload>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="有效日期：" prop="startEndTime">
              <el-date-picker
                  v-model="dataForm.startEndTime"
                  type="daterange"
                  value-format="yyyy-MM-dd"
                  range-separator="-"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期">
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="排序：" prop="sequence" :error="errors['sequence']">
              <el-input-number v-model="dataForm.sequence" controls-position="right" :min="0" placeholder="请输入"></el-input-number>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="上架状态：" prop="grounding" :error="errors['grounding']">
              <el-radio-group v-model="dataForm.grounding">
                <el-radio :label="true">上架</el-radio>
                <el-radio :label="false">下架</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="链接：" prop="link" :error="errors['link']">
              <el-input v-model="dataForm.link" clearable placeholder="URL链接（如不需要跳转请空着或输入#）"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="visible = false">取消</el-button>
        <el-button type="primary" @click="dataFormSubmit()"  :disabled="dataFormLoading">确定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import moment from 'moment'
import editMixin from '@/mixins/edit-mixins'
import fileUploadMixin from '@/mixins/file-upload-mixins'
export default {
  mixins: [editMixin, fileUploadMixin],
  data () {
    return {
      uploadOptions: {
        fieldName: 'imageUrl'
      },
      editOptions: {
        initUrl: '/admin/portal_website/banner',
        saveSuffix: 'save',
        updateSuffix: 'update',
        submitUrl: '/admin/portal_website/banner'
      },
      addFileVisible: false,
      dataForm: {
        categoryIds: [],
        title: null,
        imageUrl: null,
        link: null,
        startEndTime: null,
        startTime: '',
        endTime: '',
        sequence: 0,
        grounding: true
      },
      dataRule: {
        title: [
          { required: true, message: '标题不能为空', trigger: 'blur' }
        ],
        imageUrl: [
          { required: true, message: '图片不能为空', trigger: 'blur' }
        ],
        link: [
          { required: true, message: '链接不能为空', trigger: 'blur' }
        ],
        categoryIds: [
          {required: true, message: '所属栏目不能为空', trigger: 'change'}
        ],
        startEndTime: [
          { required: true, message: '有效日期不能为空', trigger: 'change' }
        ],
        sequence: [
          { required: true, message: '排序不能为空', trigger: 'blur' }
        ],
        grounding: [
          {required: true, message: '上下架状态不能为空', trigger: 'change'}
        ]
      },
      categories: []
    }
  },
  components: {
    moment
  },
  methods: {
    init (id) {
      this.dataForm = {
        id: id || undefined,
        categoryIds: [],
        title: null,
        imageUrl: null,
        link: null,
        startEndTime: null,
        startTime: '',
        endTime: '',
        sequence: 0,
        grounding: true
      }
      this.visible = true
      this.getCategories()
      this.$nextTick(() => {
        this.$refs['dataForm']?.resetFields()
        if (this.dataForm.id) {
          this.getData()
        }
      })
    },
    initCallback (data) {
      // 初始化回调函数
      this.dataForm = data
      this.$set(this.dataForm, 'startEndTime', [
        data.startTime, data.endTime
      ])
    },
    getCategories() {
      this.$http({
        url: this.$http.adornUrl('/admin/cms/category/cascaderByParentCode'),
        method: 'get',
        params: this.$http.adornParams({
          'parentCode': 'BANNER'
        })
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.categories = this.getTreeData(data.obj)
        } else {
          this.categories = []
        }
      })
    },
    // *处理点位分类最后children数组为空的状态
    getTreeData: function (data) {
      var that = this
      // 循环遍历json数据
      data.forEach(function (e) {
        if (!e.children || e.children.length < 1) {
          e.children = undefined
        } else {
          // children若不为空数组，则继续 递归调用 本方法
          that.getTreeData(e.children)
        }
      })
      return data
    },
    handleImgRemove () {
      this.dataForm.imageUrl = null
    },
    submitBeforeHandle () {
      // 提交前预处理函数
      this.dataForm.startTime = this.dataForm.startEndTime && this.dataForm.startEndTime[0]
      this.dataForm.endTime = this.dataForm.startEndTime && this.dataForm.startEndTime[1]
    }
  }
}
</script>

<style lang="scss">
.avatar {
  width: 178px;
  height: 178px;
  display: block;
}
.avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.avatar-uploader .el-upload:hover {
  border-color: #409EFF;
}

.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  line-height: 178px;
  text-align: center;
}
.mod-menu {
  .menu-list__input,
  .icon-list__input {
    > .el-input__inner {
      cursor: pointer;
    }
  }
  &__icon-popover {
    width: 458px;
    overflow: hidden;
  }
  &__icon-inner {
    width: 478px;
    max-height: 258px;
    overflow-x: hidden;
    overflow-y: auto;
  }
  &__icon-list {
    width: 458px;
    padding: 0;
    margin: -8px 0 0 -8px;
    > .el-button {
      padding: 8px;
      margin: 8px 0 0 8px;
      > span {
        display: inline-block;
        vertical-align: middle;
        width: 18px;
        height: 18px;
        font-size: 18px;
      }
    }
  }
  .icon-list__tips {
    font-size: 18px;
    text-align: center;
    color: #e6a23c;
    cursor: pointer;
  }
}
</style>
