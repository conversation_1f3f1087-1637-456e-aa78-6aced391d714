<template>
  <div>
    <el-dialog
      :title="'视频信息'"
      :close-on-click-modal="false"
      :visible.sync="visible" width="80%">
      <el-form :model="dataForm"  ref="dataForm" label-width="100px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="原文件名：" prop="fileName">
              <div>
                <span style="background-color: #e7e4e4; border: 1px solid rgba(215, 215, 215, 1); border-radius: 4px; padding: 5px" v-model="dataForm.fileName">{{dataForm.fileName}}</span>
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="文件类型：" prop="fileExt">
              <span style="background-color: #e7e4e4; border: 1px solid rgba(215, 215, 215, 1); border-radius: 4px; padding: 5px" v-model="dataForm.fileExt">{{dataForm.fileExt}}</span>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="文件大小：" prop="fileSizeText">
              <span style="background-color: #e7e4e4; border: 1px solid rgba(215, 215, 215, 1); border-radius: 4px; padding: 5px" v-model="dataForm.fileSizeText">{{dataForm.fileSizeText}}</span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="视频预览：" prop="filePath">
              <video
                  :src="dataForm.filePath"
                  style="width:95%;height:95%"
                  controls="controls"
                  id="video_scan"
              >您的浏览器不支持视频播放</video>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="visible = false">关闭</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
  export default {
    data () {
      return {
        dataForm: {
          id: null,
          fileName: null,
          fileExt: null,
          fileSizeText: null,
          filePath: null
        },
        visible: false
      }
    },
    methods: {
      init(id) {
        this.dataForm.id = id || null
        this.visible = true
        this.$nextTick(() => {
          this.$refs['dataForm']?.resetFields()
          if (this.dataForm.id) {
            this.getVideoInfo()
          }
        })
      },
      getVideoInfo () {
        this.$http({
          url: this.$http.adornUrl('/admin/portal_website/video/getVideoInfo'),
          method: 'get',
          params: this.$http.adornParams({id: this.dataForm.id})
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.dataForm = data.obj
            this.dataForm.filePath = this.$http.adornAttachmentUrl(data.obj.path)
          }
        })
      }
    }
  }
</script>

<style lang="scss">
</style>
