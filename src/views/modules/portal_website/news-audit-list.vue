<template>
  <div>
    <el-dialog
        title="审核记录"
        :close-on-click-modal="false"
        :visible.sync="visible" width="50%">
    <el-table
      :data="dataList"
      border
      v-loading="dataListLoading"
      style="width: 100%;">
      <el-table-column
          prop="auditor"
          header-align="center"
          align="center"
          label="审核人">
      </el-table-column>
      <el-table-column
        prop="auditTime"
        header-align="center"
        align="center"
        label="审核时间">
      </el-table-column>
      <el-table-column
        prop="pass"
        header-align="center"
        align="center"
        label="审核结果">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.pass === false" size="small" type="danger">驳回</el-tag>
          <el-tag v-else size="small">通过</el-tag>
        </template>
      </el-table-column>
      <el-table-column
        prop="memo"
        header-align="center"
        align="center"
        width="250"
        :show-overflow-tooltip="true"
        label="审核信息">
      </el-table-column>
    </el-table>
    <el-pagination
      @size-change="sizeChangeHandle"
      @current-change="currentChangeHandle"
      :current-page="pageIndex"
      :page-sizes="[10, 20, 50, 100]"
      :page-size="pageSize"
      :total="totalPage"
      layout="total, sizes, prev, pager, next, jumper">
    </el-pagination>
    </el-dialog>
  </div>
</template>

<script>
  export default {
    data () {
      return {
        visible: false,
        dataList: [],
        dataListLoading: false,
        dataForm: {
          contentId: null
        },
        pageIndex: 1,               // 当前页
        pageSize: 10,               // 分页大小
        totalPage: 0,
      }
    },
    methods: {
      init(id) {
        this.dataForm.contentId = id || null
        this.visible = true
        this.getDataList()
      },
      getDataList() {
        if (!this.dataForm.contentId) {
          this.$message.error('为获取到内容id')
          return
        }
        this.dataListLoading = true
        this.$http({
          url: this.$http.adornUrl('/admin/portal_website/news_audit/pages'),
          method: 'post',
          data: this.$http.adornData({
            currentPage: this.pageIndex,
            pageSize: this.pageSize,
            ...this.dataForm
          })
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.dataList = data.obj.records
            this.totalPage = data.obj.total
          } else {
            this.$message.error('获取审核记录失败')
          }
          this.dataListLoading = false
        })
      },
      // 分页, 每页条数
      sizeChangeHandle (val) {
        this.pageSize = val
        this.pageIndex = 1
        this.getDataList()
      },
      // 分页, 当前页
      currentChangeHandle (val) {
        this.pageIndex = val
        this.getDataList()
      }
    }
  }
</script>
