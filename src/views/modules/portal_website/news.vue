<template>
  <div class="mod-role">
    <el-form :inline="true" ref="dataForm" :model="dataForm" @keyup.enter.native="getDataList()" >
      <el-form-item label="标题" prop="title">
        <el-input v-model="dataForm.title" placeholder="请输入" clearable></el-input>
      </el-form-item>
      <el-form-item label="栏目" prop="categoryIds">
        <el-cascader
            placeholder="请选择"
            v-model="dataForm.categoryIds"
            :options="categories"
            @change="(value) => {handleChange(value, 'category')}"
            clearable
            :props="{checkStrictly: true}"
        ></el-cascader>
      </el-form-item>
      <el-form-item label="上级组织" prop="orgCodes" v-if="!teamCapacity">
        <el-cascader
            placeholder="请选择"
            v-model="dataForm.orgCodes"
            :options="orgList"
            :disabled="this.dataForm.id"
            :show-all-levels="false"
            clearable
            :props="{checkStrictly: true, value: 'code',label: 'name'}"
            @change="(value) => {handleChange(value, 'org')}"
        ></el-cascader>
      </el-form-item>
      <el-form-item label="发布人" prop="publisher">
        <el-input v-model="dataForm.publisher" placeholder="请输入" clearable></el-input>
      </el-form-item>
      <el-form-item v-if="!dataForm.needAudit" label="审核状态" prop="auditStatus">
        <el-dict :code="'NEWS_AUDIT_STATUS'" v-model="dataForm.auditStatus"></el-dict>
      </el-form-item>
      <el-form-item label="是否置顶" prop="stick">
        <el-select v-model="dataForm.stick" placeholder="请选择" clearable>
          <el-option
              v-for="item in [{label: '已置顶', value: true}, {label: '未置顶', value: false}]"
              :key="item.value"
              :label="item.label"
              :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="上架状态" prop="grounding">
        <el-select v-model="dataForm.grounding" placeholder="请选择" clearable>
          <el-option
              v-for="item in [{label: '已上架', value: true}, {label: '未上架', value: false}]"
              :key="item.value"
              :label="item.label"
              :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item :label="'同步状态'" prop="sync">
        <el-dict :code="'sync_status'" v-model="dataForm.sync"></el-dict>
      </el-form-item>
      <el-form-item  prop="needAudit">
        <el-checkbox v-model="dataForm.needAudit">需要我审核的</el-checkbox>
      </el-form-item>
      <el-form-item>
        <el-button type="warning" icon="el-icon-search" @click="getDataList()">查询</el-button>
        <el-button icon="el-icon-refresh-left" @click="resetForm()">重置</el-button>
      </el-form-item>
    </el-form>
    <div class="f-s-c" style="padding: 15px 0;display: flex; justify-content: flex-end">
      <div>
        <el-button icon="el-icon-plus" type="primary" @click="addOrUpdateHandle()">新增</el-button>
        <el-button v-if="isAuth('news:list:audit')" icon="el-icon-s-check" type="success"  @click="auditBatch()" :disabled="dataListSelections.length <= 0">批量审核</el-button>
        <el-button icon="el-icon-delete" type="danger" @click="deleteHandle()" :disabled="dataListSelections.length <= 0">批量删除</el-button>
      </div>
    </div>
    <el-table
      :data="dataList"
      border
      v-loading="dataListLoading"
      @selection-change="selectionChangeHandle"
      style="width: 100%;">
      <el-table-column
        type="selection"
        header-align="center"
        align="center"
        width="50">
      </el-table-column>
      <el-table-column
        prop="title"
        header-align="center"
        align="center"
        width="300"
        :show-overflow-tooltip="true"
        label="标题">
        <template slot-scope="scope">
          <a style="cursor: pointer" @click="newsDetail(scope.row.id)">{{ scope.row.title }}</a>
        </template>
      </el-table-column>
      <el-table-column
        prop="category"
        header-align="center"
        align="center"
        label="栏目">
      </el-table-column>
      <el-table-column
        prop="effectiveDate"
        header-align="center"
        align="center"
        width="160"
        label="发布时间">
      </el-table-column>
      <el-table-column
        prop="publisher"
        header-align="center"
        align="center"
        width="200"
        :show-overflow-tooltip="true"
        label="发布人">
      </el-table-column>
      <el-table-column
        prop="auditStatusText"
        header-align="center"
        width="200"
        align="center"
        label="审核状态">
      </el-table-column>
      <el-table-column
        prop="auditMemo"
        header-align="center"
        align="center"
        width="150"
        :show-overflow-tooltip="true"
        label="审核信息">
      </el-table-column>
      <el-table-column
          prop="syncText"
          header-align="center"
          align="center"
          label="同步状态">
        <template slot-scope="scope">
          <!--          <div v-if="scope.row.isSyncText === '同步失败'" style="color: #f6070f;cursor:pointer">{{scope.row.isSyncText}}</div>-->
          <!--          <div v-if="scope.row.isSyncText === '待同步'" style="color: #eee98a;cursor:pointer">{{scope.row.isSyncText}}</div>-->
          <!--          <div v-if="scope.row.isSyncText === '已同步'" style="color: #03f303;cursor:pointer">{{scope.row.isSyncText}}</div>-->
          <el-popover trigger="hover" placement="top">
            <p style="text-align: center">
              {{
                '同步时间：' + (scope.row.syncTime ? scope.row.syncTime : '')
              }}<br>{{ scope.row.syncRemark }}</p>
            <div slot="reference" class="name-wrapper">
              <el-tag v-if="scope.row.sync === 'sync_failure'" type="danger">{{ scope.row.syncText }}</el-tag>
              <el-tag v-if="scope.row.sync === 'sync_wait'" type="warning">{{ scope.row.syncText }}</el-tag>
              <el-tag v-if="scope.row.sync === 'sync_success'" type="success">{{ scope.row.syncText }}</el-tag>
            </div>
          </el-popover>
        </template>
      </el-table-column>
      <el-table-column
          prop="stick"
          header-align="center"
          align="center"
          label="是否置顶">
        <template slot-scope="scope">
          <el-switch
              size="mini"
              active-color="#13ce66"
              inactive-color="#ff4949"
              v-model="scope.row.stick"
              @change="stickHandle(scope.row.id, scope.row.stick, scope.row)"
              :disabled="!isAuth('news:list:stick')">
          </el-switch>
<!--          <el-tag v-if="scope.row.stick === true" type="success">已置顶</el-tag>-->
<!--          <el-tag v-else type="warning">未置顶</el-tag>-->
        </template>
      </el-table-column>
      <el-table-column
          prop="grounding"
          header-align="center"
          align="center"
          label="是否上架">
        <template slot-scope="scope">
          <el-switch
              size="mini"
              active-color="#13ce66"
              inactive-color="#ff4949"
              v-model="scope.row.grounding"
              @change="groundHandle(scope.row.id, scope.row)"
              :disabled="!(isAuth('news:list:ground') && ((scope.row.auditOrgCode && userOrgCode && userOrgCode === scope.row.auditOrgCode) || (scope.row.orgCode === userOrgCode && managerCapacity === 'ASSOCIATION_ADMIN')) && scope.row.auditStatus === 'nas_pass')">
          </el-switch>
<!--          <el-tag v-if="scope.row.grounding === true" type="success">已上架</el-tag>-->
<!--          <el-tag v-else type="warning">未上架</el-tag>-->
        </template>
      </el-table-column>
      <el-table-column
        fixed="right"
        header-align="center"
        align="center"
        width="180px"
        label="操作">
        <template slot-scope="scope">
          <el-button v-if="(userOrgCode && scope.row.orgCode && !scope.row.teamId && userOrgCode === scope.row.orgCode) || (userTeamId && scope.row.teamId && userTeamId === scope.row.teamId)" type="text" size="small" @click="addOrUpdateHandle(scope.row.id)" class="btn-control">修改</el-button>
          <el-button type="text" size="small" @click="deleteHandle(scope.row.id)" class="btn-control">删除</el-button>
<!--          <el-button v-if="isAuth('news:list:stick')" type="text" size="small" @click="stickHandle(scope.row.id, scope.row.stick)">{{scope.row.stick === true ? '取消置顶' : '置顶'}}</el-button>-->
          <el-button v-if="isAuth('news:list:audit') && scope.row.auditOrgCode && userOrgCode && userOrgCode === scope.row.auditOrgCode && scope.row.auditStatus !== 'nas_pass' && scope.row.auditStatus !== 'nas_refuse'" type="text" size="small" @click="audit(scope.row.id)">审核</el-button>
<!--          <el-button v-if="isAuth('news:list:ground') && ((scope.row.auditOrgCode && userOrgCode && userOrgCode === scope.row.auditOrgCode) || (scope.row.orgCode === userOrgCode && managerCapacity === 'ASSOCIATION_ADMIN')) && scope.row.auditStatus === 'nas_pass'" type="text" size="small" @click="groundHandle(scope.row.id)">{{scope.row.grounding ? '下架' : '上架'}}</el-button>-->
          <el-button type="text" size="small" @click="auditRecord(scope.row.id)">审核记录</el-button>

          <el-button v-if="isAuth('News:send-tag-message')" size="small" type="text"  @click="sendTagMessage(scope.row.id)">同领域用户推送</el-button>

          <el-button type="text" size="small" @click="syncHandle(scope.row.id)"
                     v-if="isAuth('news:sync') && !(scope.row.sync==='sync_success' || scope.row.auditStatus!=='nas_pass')">同步
          </el-button>
          <el-button type="text" size="small" v-if="scope.row.dockingZSQ" @click="zsqDockingRecords(scope.row.id)">知社区对接记录</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      @size-change="sizeChangeHandle"
      @current-change="currentChangeHandle"
      :current-page="pageIndex"
      :page-sizes="[10, 20, 50, 100]"
      :page-size="pageSize"
      :total="totalPage"
      layout="total, sizes, prev, pager, next, jumper">
    </el-pagination>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"></add-or-update>
    <!-- 审核 -->
    <audit v-if="auditVisible" ref="audit" @refreshDataList="getDataList"></audit>
    <audit-list v-if="auditListVisible" ref="auditList"></audit-list>
    <zsq-docking-records v-if="zsqDockingRecordsVisible" ref="zsqDockingRecords"></zsq-docking-records>
  </div>
</template>

<script>
  import AddOrUpdate from './news-add-or-update'
  import AuditList from './news-audit-list'
  import listMixin from '@/mixins/list-mixins'
  import Audit from "@/components/audit/audit"
  import ZsqDockingRecords from './news-zsq-docking-records'
  import _ from 'lodash'
  export default {
    mixins: [listMixin],
    data () {
      return {
        mixinOptions: {
          dataUrl: '/admin/portal_website/news/pages',
          deleteUrl: '/admin/portal_website/news/removeByIds'
        },
        managerCapacity: null,
        teamCapacity: false,
        userTeamId: null,
        userOrgCode: null,
        dataForm: {
          title: '',
          categoryIds: [],
          orgCodes: [],
          categoryId: '',
          orgCode: '',
          publisher: '',
          auditStatus: null,
          needAudit: false,
          stick: null,
          grounding: null,
          sync: null
        },
        categories: [],
        orgList: [],
        auditVisible: false,
        auditListVisible: false,
        zsqDockingRecordsVisible: false
      }
    },
    components: {
      AddOrUpdate,
      Audit,
      AuditList,
      ZsqDockingRecords
    },
    activated () {
      this.$set(this.dataForm, 'needAudit', this.$route.query.needAudit || false)
      this.getDataList()
      this.getUserInfo()
      this.getCategories()
    },
    methods: {
      resetForm () {
        this.$refs['dataForm']?.resetFields()
        this.dataForm.needAudit = false
        this.dataForm.categoryIds = []
        this.dataForm.categoryId = ''
        this.dataForm.orgCode = ''
        this.getDataList()
      },
      // 获取当前管理员信息
      getUserInfo() {
        this.$http({
          url: this.$http.adornUrl('/admin/user/loginUser'),
          method: 'get',
          params: this.$http.adornParams()
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.userOrgCode = data.obj['orgCode']
            this.managerCapacity = data.obj['managerCapacity']
            this.userTeamId = data.obj['teamId']
            this.teamCapacity = data.obj['managerCapacity'] === 'TEAM_ADMIN'
            if (!this.teamCapacity) {
              this.getOrg()
            }
          } else {
            this.$message.error(data.msg)
          }
        })
      },
      //获取所属区域
      getOrg (){
        this.$http({
          url: this.$http.adornUrl(`/admin/org/getOrgTree`),
          method: 'get',
          params: this.$http.adornParams()
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.orgList = this.getTreeData(data.obj)
          } else {
            this.orgList = []
          }
        })
      },
      stickHandle (id, stick, row) {
        this.$confirm(`确定进行${stick ? '置顶' : '取消置顶'}操作?`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
          closeOnClickModal: false
        }).then(() => {
          this.$http({
            url: this.$http.adornUrl('/admin/portal_website/news/setStick'),
            method: 'get',
            params: this.$http.adornParams({
              'id': id
            })
          }).then(({data}) => {
            if (data && data.code === 0) {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 1500,
                onClose: () => {
                  this.getDataList()
                }
              })
            } else {
              row.stick = !stick
              this.$message.error(data.msg)
            }
          }).catch(() => {
            row.stick = !stick
          })
        }).catch(() => {
          row.stick = !stick
        })
      },
      getCategories () {
        this.$http({
          url: this.$http.adornUrl('/admin/cms/category/cascaderByParentCode'),
          method: 'get',
          params: this.$http.adornParams({
            'parentCode': 'NEWS'
          })
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.categories = this.getTreeData(data.obj)
          } else {
            this.categories = []
          }
        })
      },
      // *处理点位分类最后children数组为空的状态
      getTreeData: function (data) {
        var that = this
        // 循环遍历json数据
        data.forEach(function (e) {
          if (!e.children || e.children.length < 1) {
            e.children = undefined
          } else {
            // children若不为空数组，则继续 递归调用 本方法
            that.getTreeData(e.children)
          }
        })
        return data
      },
      handleChange(value, type) {
        if (type === 'category') {
          this.dataForm.categoryId = value[value.length - 1]
        }
        if (type === 'org') {
          this.dataForm.orgCode = value && value.length > 0 ? value[value.length - 1] : null
        }
      },
      audit (id) {
        this.auditVisible = true
        this.$nextTick(() => {
          this.$refs.audit.init(id, '/admin/portal_website/news/audit', false)
        })
      },
      auditBatch () {
        let flag = true
        for (let i = 0; i < this.dataListSelections.length; i ++) {
          let item = this.dataListSelections[i]
          if (!(item.auditOrgCode && this.userOrgCode && this.userOrgCode === item.auditOrgCode && item.auditStatus !== 'nas_pass' && item.auditStatus !== 'nas_refuse')) {
            flag = false
            break
          }
        }
        if (!flag) {
          this.$message.warning('存在您无法审核的数据，请检查勾选情况！')
          return
        }
        this.auditVisible = true
        this.$nextTick(() => {
          this.$refs.audit.init(this.dataListSelections.map(item => {return item.id}).join(','), '/admin/portal_website/news/auditBatch', true)
        })
      },
      auditRecord(id) {
        this.auditListVisible = true
        this.$nextTick(() => {
          this.$refs.auditList.init(id)
        })
      },
      groundHandle(id, row) {
        this.$http({
          url: this.$http.adornUrl('/admin/portal_website/news/ground'),
          method: 'get',
          params: this.$http.adornParams({
            'id': id
          })
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.$message.success("操作成功")
            this.query()
          } else {
            row.grounding = !row.grounding
            this.$message.error(data.msg)
          }
        }).catch(() => {
          row.grounding = !row.grounding
        })
      },
      sendTagMessage(id) {
        this.$confirm(`确定进行给本活动领域活跃志愿者推送相关新闻吗?`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
          closeOnClickModal: false
        }).then(() => {
          this.dataListLoading = true
          this.$http({
            url: this.$http.adornUrl('/admin/portal_website/news/sendTagMessage'),
            method: 'get',
            params: this.$http.adornParams({'id': id})
          }).then(({data}) => {
            if (data && data.code === 0) {
              this.$message({
                message: '推送成功',
                type: 'success',
                duration: 3000
              })
            } else {
              this.$message.error(data.msg)
            }
          })
          this.dataListLoading = false
        })
      },
      // 同步
      syncHandle(id) {
        this.$confirm(`确定要进行同步嘛，请勿重复操作，否则可能会带来不可预知的后果`, '确认同步？', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
          closeOnClickModal: false
        }).then(() => {
          this.dataListLoading = true
          this.$http({
            url: this.$http.adornUrl('/admin/zyz/platform-sync/syncNews'),
            method: 'post',
            params: this.$http.adornParams({
              'newsId': id
            })
          }).then(({data}) => {
            this.dataListLoading = false
            if (data && data.code === 0) {
              this.$message({
                message: '请求同步成功，市平台返回：（' + data.obj.message + '）',
                type: data.obj.resultCode === 'SUCCESS' ? 'success' : 'warning',
                duration: 3000,
                onClose: () => {
                  this.getDataList()
                }
              })
            } else {
              this.$message.error(data.msg)
            }
          })
        })
      },
      newsDetail (id) {
        this.addOrUpdateVisible = true
        this.$nextTick(() => {
          this.$refs.addOrUpdate.init(id, true)
        })
      },
      zsqDockingRecords(id) {
        this.zsqDockingRecordsVisible = true
        this.$nextTick(() => {
          this.$refs.zsqDockingRecords.init(id)
        })
      }
    }
  }
</script>
