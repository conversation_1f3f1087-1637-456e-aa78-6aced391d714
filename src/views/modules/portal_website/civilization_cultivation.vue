<template>
  <div class="mod-role">
    <el-form :inline="true" ref="dataForm" :model="dataForm" @keyup.enter.native="getDataList()">
      <el-form-item label="名称" prop="title">
        <el-input v-model="dataForm.title" placeholder="请输入" clearable></el-input>
      </el-form-item>
<!--      <el-form-item label="类型" prop="categoryIds">-->
<!--        <el-cascader-->
<!--            placeholder="请选择"-->
<!--            v-model="dataForm.categoryIds"-->
<!--            :options="categories"-->
<!--            @change="(value) => {handleChange(value, 'category')}"-->
<!--            clearable-->
<!--            :props="{checkStrictly: true}"-->
<!--        ></el-cascader>-->
<!--      </el-form-item>-->
      <el-form-item label="类型" prop="categoryId">
        <el-select v-model="dataForm.categoryId" placeholder="请选择" clearable>
          <el-option
              v-for="item in categories"
              :key="item.value"
              :label="item.label"
              :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="发布日期" prop="publishDate">
        <el-date-picker
            v-model="dataForm.publishDate"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="上架状态" prop="grounding">
        <el-select v-model="dataForm.grounding" placeholder="请选择" clearable>
          <el-option
              v-for="item in [{label: '已上架', value: true}, {label: '未上架', value: false}]"
              :key="item.value"
              :label="item.label"
              :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="warning" icon="el-icon-search" @click="getDataList()">查询</el-button>
        <el-button icon="el-icon-refresh-left" @click="resetForm()">重置</el-button>
      </el-form-item>
      <div class="f-s-c" style="padding: 15px 0;display: flex; justify-content: flex-end">
        <div>
          <el-button icon="el-icon-plus" v-if="isAuth('civilization_cultivation:list:save')" type="primary" @click="addOrUpdateHandle()">新增</el-button>
          <el-button icon="el-icon-delete" v-if="isAuth('civilization_cultivation:list:update')" type="danger" @click="deleteHandle()" :disabled="dataListSelections.length <= 0">批量删除</el-button>
        </div>
      </div>
    </el-form>
    <el-table
        :data="dataList"
        border
        v-loading="dataListLoading"
        @selection-change="selectionChangeHandle"
        style="width: 100%;">
      <el-table-column
          type="selection"
          header-align="center"
          align="center"
          width="50">
      </el-table-column>
      <el-table-column
          prop="title"
          header-align="center"
          align="center"
          width="300"
          :show-overflow-tooltip="true"
          label="名称">
        <template slot-scope="scope">
          <a style="cursor: pointer" @click="newsDetail(scope.row.id)">{{ scope.row.title }}</a>
        </template>
      </el-table-column>
      <el-table-column
          prop="bigScreenTitle"
          header-align="center"
          align="center"
          width="200"
          label="大屏展示名称">
      </el-table-column>
      <el-table-column
          prop="sequence"
          header-align="center"
          align="center"
          label="排序">
      </el-table-column>
      <el-table-column
          prop="effectiveDate"
          header-align="center"
          align="center"
          label="发布日期">
      </el-table-column>
      <el-table-column
          prop="category"
          header-align="center"
          align="center"
          label="栏目">
      </el-table-column>
      <el-table-column
          prop="grounding"
          header-align="center"
          align="center"
          label="是否上架">
        <template slot-scope="scope">
          <el-switch
              size="mini"
              active-color="#13ce66"
              inactive-color="#ff4949"
              v-model="scope.row.grounding"
              @change="groundHandle(scope.row.id, scope.row)"
              :disabled="!isAuth('civilization_cultivation:list:ground')">
          </el-switch>
<!--          <el-tag v-if="scope.row.grounding === true" type="success">已上架</el-tag>-->
<!--          <el-tag v-else type="warning">未上架</el-tag>-->
        </template>
      </el-table-column>
      <el-table-column
          fixed="right"
          header-align="center"
          align="center"
          width="140"
          label="操作">
        <template slot-scope="scope">
          <el-button v-if="isAuth('civilization_cultivation:list:update')" type="text" size="small" @click="addOrUpdateHandle(scope.row.id)" class="btn-control">修改</el-button>
          <el-button v-if="isAuth('civilization_cultivation:list:remove')" type="text" size="small" @click="deleteHandle(scope.row.id)" class="btn-control">删除</el-button>
<!--          <el-button v-if="isAuth('mien:list:ground')" type="text" size="small" @click="groundHandle(scope.row.id)" class="btn-control">{{scope.row.grounding ? '下架' : '上架'}}</el-button>-->
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
        @size-change="sizeChangeHandle"
        @current-change="currentChangeHandle"
        :current-page="pageIndex"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pageSize"
        :total="totalPage"
        layout="total, sizes, prev, pager, next, jumper">
    </el-pagination>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"></add-or-update>
  </div>
</template>

<script>
import AddOrUpdate from './civilization-cultivation-add-or-update'
import listMixin from '@/mixins/list-mixins'
export default {
  mixins: [listMixin],
  data () {
    return {
      mixinOptions: {
        dataUrl: '/admin/portal_website/civilization_cultivation/pages',
        deleteUrl: '/admin/portal_website/civilization_cultivation/removeByIds'
      },
      dataForm: {
        title: '',
        categoryIds: [],
        categoryId: '',
        year: '',
        grounding: null
      },
      categories: []
    }
  },
  components: {
    AddOrUpdate
  },
  activated () {
    this.getDataList()
    this.getCategories()
  },
  methods: {
    resetForm () {
      this.$refs['dataForm']?.resetFields()
      this.dataForm.categoryId = ''
      this.getDataList()
    },
    getCategories () {
      this.$http({
        url: this.$http.adornUrl('/admin/cms/category/cascaderByParentCode'),
        method: 'get',
        params: this.$http.adornParams({
          'parentCode': 'CIVILIZATION_CULTIVATION'
        })
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.categories = this.getTreeData(data.obj)
        } else {
          this.categories = []
        }
      })
    },
    // *处理点位分类最后children数组为空的状态
    getTreeData: function (data) {
      var that = this
      // 循环遍历json数据
      data.forEach(function (e) {
        if (!e.children || e.children.length < 1) {
          e.children = undefined
        } else {
          // children若不为空数组，则继续 递归调用 本方法
          that.getTreeData(e.children)
        }
      })
      return data
    },
    groundHandle(id, row) {
      this.$http({
        url: this.$http.adornUrl('/admin/portal_website/civilization_cultivation/ground'),
        method: 'get',
        params: this.$http.adornParams({
          'id': id
        })
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.$message.success("操作成功")
          this.query()
        } else {
          row.grounding = !row.grounding
          this.$message.error(data.msg)
        }
      }).catch(() => {
        row.grounding = !row.grounding
      })
    },
    newsDetail (id) {
      this.addOrUpdateVisible = true
      this.$nextTick(() => {
        this.$refs.addOrUpdate.init(id, true)
      })
    }
    // handleChange(value, type) {
    //   if (type === 'category') {
    //     this.dataForm.categoryId = value[value.length - 1]
    //   }
    // }
  }
}
</script>
