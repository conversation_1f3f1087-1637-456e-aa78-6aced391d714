<template>
  <div>
    <el-dialog
        :title="onlyRead === true ? '详情' : (!dataForm.id ? '新增' : '修改')"
        :close-on-click-modal="false"
        :visible.sync="visible" width="80%">
      <el-form :model="dataForm" :rules="dataRule" ref="dataForm" label-width="120px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="栏目：" prop="categoryId" :error="errors['categoryId']">
              <el-select style="width: 100%" v-model="dataForm.categoryId" placeholder="请选择" clearable :disabled="onlyRead">
                <el-option
                    v-for="item in categories"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="名称：" prop="title" :error="errors['title']">
              <el-input v-model="dataForm.title" placeholder="请输入" clearable :disabled="onlyRead"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item  v-if="!onlyRead" label="图片：" prop="titleImgUrl">
              <el-upload
                  :disabled="onlyRead"
                  class="avatar-uploader"
                  :action="this.$http.adornUrl(`/admin/oss/upload`)"
                  :headers="headers"
                  :data="{serverCode: uploadOptions.serverCode, media: false}"
                  :show-file-list="false"
                  :on-success="successHandle"
                  :on-change="changHandle"
                  :on-exceed="exceedHandle"
                  :before-upload="beforeUploadHandle">
                <img v-if="dataForm.titleImgUrl" :src="$http.adornAttachmentUrl(dataForm.titleImgUrl)" class="avatar">
                <i v-else class="el-icon-plus avatar-uploader-icon"></i>
                <div slot="tip" class="el-upload__tip" style="color: red">尺寸建议600x1000像素，文件格式jpg、bmp或png，大小不超2M</div>
              </el-upload>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item  v-if="onlyRead" label="图片：" prop="titleImgUrl">
              <el-image
                  :src="$http.adornAttachmentUrl(dataForm.titleImgUrl)"
                  :preview-src-list="[$http.adornAttachmentUrl(dataForm.titleImgUrl)]"
                  class="avatar"
              >
              </el-image>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="发布日期：" prop="publishDate" :error="errors['publishDate']">
              <el-date-picker
                  :disabled="onlyRead"
                  v-model="dataForm.publishDate"
                  type="date"
                  value-format="yyyy-MM-dd"
                  placeholder="请选择">
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="排序：" prop="sequence" :error="errors['sequence']">
              <el-input-number v-model="dataForm.sequence" placeholder="请输入" :min="0" :disabled="onlyRead"></el-input-number>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="上架状态：" prop="grounding" :error="errors['grounding']">
              <el-radio-group v-model="dataForm.grounding" :disabled="onlyRead">
                <el-radio :label="true">上架</el-radio>
                <el-radio :label="false">下架</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="大屏展示名称：" prop="bigScreenTitle" >
              <el-input v-model="dataForm.bigScreenTitle" :disabled="onlyRead" ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="外部链接：" prop="customLinks" >
              <el-input v-model="dataForm.customLinks" :disabled="onlyRead" ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="简介：" prop="briefIntroduction" :error="errors['briefIntroduction']">
              <el-input type="textarea" rows="3" maxlength="500" show-word-limit v-model="dataForm.briefIntroduction" placeholder="请输入" :disabled="onlyRead"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="内容：" prop="description">
              <teditor
                  :disabled="onlyRead"
                  style="width: 100%;"
                  :value="dataForm.description"
                  ref="teditor"
                  @changeEditorValue="changeAnswer"
              ></teditor>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button v-if="!onlyRead || onlyRead === false" @click="visible = false">取消</el-button>
        <el-button v-if="onlyRead && onlyRead === true" @click="visible = false">关闭</el-button>
        <el-button v-if="!onlyRead || onlyRead === false" type="primary" @click="dataFormSubmit()" :disabled="dataFormLoading">确定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import Teditor from '@/components/tinymce'
import moment from 'moment'
import editMixin from '@/mixins/edit-mixins'
import fileUploadMixin from '@/mixins/file-upload-mixins'
export default {
  mixins: [editMixin, fileUploadMixin],
  data () {
    return {
      uploadOptions: {
        fieldName: 'titleImgUrl',
        maxSize: 2
      },
      editOptions: {
        initUrl: '/admin/portal_website/civilization_cultivation',
        saveSuffix: 'save',
        updateSuffix: 'update',
        submitUrl: '/admin/portal_website/civilization_cultivation'
      },
      addFileVisible: false,
      dataForm: {
        categoryId: null,
        title: null,
        bigScreenTitle: null,
        titleImgUrl: null,
        customLinks: null,
        briefIntroduction: null,
        description: null,
        grounding: true,
        publishDate: '',
        sequence: 0
      },
      dataRule: {
        title: [
          { required: true, message: '名称不能为空', trigger: 'blur' }
        ],
        categoryId: [
          {required: true, message: '所属栏目不能为空', trigger: 'change'}
        ],
        grounding: [
          {required: true, message: '上下架状态不能为空', trigger: 'change'}
        ],
        publishDate: [
          {required: true, message: '发布日期不能为空', trigger: 'change'}
        ],
        sequence: [
          { required: true, message: '排序不能为空', trigger: 'blur' }
        ]
      },
      categories: [],
      onlyRead: false
    }
  },
  components: {
    Teditor,
    moment
  },
  methods: {
    init (id, onlyRead) {
      this.dataForm = {
        id: id || undefined,
        categoryId: null,
        title: null,
        bigScreenTitle: null,
        titleImgUrl: null,
        customLinks: null,
        briefIntroduction: null,
        description: null,
        grounding: true,
        publishDate: '',
        sequence: 0
      }
      this.onlyRead = onlyRead
      this.changeAnswer('')
      this.visible = true
      this.getCategories()
      this.$nextTick(() => {
        this.$refs['dataForm']?.resetFields()
        this.dataForm.publishDate = moment(new Date()).format('YYYY-MM-DD')
        this.dataForm.description = null
        if (this.dataForm.id) {
          this.getData()
        }
      })
    },
    getCategories() {
      this.$http({
        url: this.$http.adornUrl('/admin/cms/category/cascaderByParentCode'),
        method: 'get',
        params: this.$http.adornParams({
          'parentCode': 'CIVILIZATION_CULTIVATION'
        })
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.categories = this.getTreeData(data.obj)
        } else {
          this.categories = []
        }
      })
    },
    // *处理点位分类最后children数组为空的状态
    getTreeData: function (data) {
      var that = this
      // 循环遍历json数据
      data.forEach(function (e) {
        if (!e.children || e.children.length < 1) {
          e.children = undefined
        } else {
          // children若不为空数组，则继续 递归调用 本方法
          that.getTreeData(e.children)
        }
      })
      return data
    },
    handleImgRemove () {
      this.dataForm.titleImgUrl = null
    },
    // 富文本赋值
    changeAnswer (html) {
      this.dataForm.description = html
    },
    submitBeforeHandle () {
      this.dataForm.effectiveDate = null
      return true
    }
  }
}
</script>

<style lang="scss">
.avatar {
  width: 178px;
  height: 178px;
  display: block;
}
.avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.avatar-uploader .el-upload:hover {
  border-color: #409EFF;
}

.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  line-height: 178px;
  text-align: center;
}
.mod-menu {
  .menu-list__input,
  .icon-list__input {
    > .el-input__inner {
      cursor: pointer;
    }
  }
  &__icon-popover {
    width: 458px;
    overflow: hidden;
  }
  &__icon-inner {
    width: 478px;
    max-height: 258px;
    overflow-x: hidden;
    overflow-y: auto;
  }
  &__icon-list {
    width: 458px;
    padding: 0;
    margin: -8px 0 0 -8px;
    > .el-button {
      padding: 8px;
      margin: 8px 0 0 8px;
      > span {
        display: inline-block;
        vertical-align: middle;
        width: 18px;
        height: 18px;
        font-size: 18px;
      }
    }
  }
  .icon-list__tips {
    font-size: 18px;
    text-align: center;
    color: #e6a23c;
    cursor: pointer;
  }
}
</style>
