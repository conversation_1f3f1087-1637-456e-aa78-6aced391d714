<template>
  <div>
    <el-dialog
      :title="!dataForm.id ? '新增' : '修改'"
      :close-on-click-modal="false"
      :visible.sync="visible" width="40%"
      :show-close="!uploadLoading">
      <el-form :model="dataForm" :rules="dataRule" ref="dataForm" label-width="100px" v-loading="uploadLoading">
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="名称：" prop="name" :error="errors['name']">
              <el-input v-model="dataForm.name" placeholder="名称"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20" v-if="isAuth('video:list:type_select')">
          <el-col :span="24">
            <el-form-item label="存储方式：" prop="serverCode" :error="errors['serverCode']">
              <el-dict :code="'server-code'" v-model="dataForm.serverCode"></el-dict>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="上传文件：" prop="file">
              <el-upload
                  ref="upload"
                  drag
                  :action="''"
                  :on-change="changHandle"
                  :limit=1
                  :on-exceed="handleExceed"
                  :before-upload="beforeFileUpload"
                  :http-request="saveResource"
                  :file-list="fileList"
                  :auto-upload="false">
                <i class="el-icon-upload"></i>
                <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
                <div class="el-upload__text">支持格式：{{fileExts}}</div>
              </el-upload>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="visible = false">取消</el-button>
        <el-button type="primary" @click="dataFormSubmit()" :disabled="uploadLoading">确定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
  import Vue from 'vue'
  import editMixin from '@/mixins/edit-mixins'
  import _ from "lodash";
  import {isAuth} from "@/utils";
  export default {
    mixins: [editMixin],
    data () {
      return {
        editOptions: {
          initUrl: '/admin/portal_website/video',
          saveSuffix: 'upload',
          updateSuffix: 'upload',
          submitUrl: '/admin/portal_website/video'
        },
        // addFileVisible: false,
        dataForm: {
          name: null,
          serverCode: null,
          fileName: null,
          fileExt: null,
          fileSize: null
        },
        dataRule: {
          name: [
            { required: true, message: '文件名不能为空', trigger: 'blur' }
          ],
          serverCode: [
            {required: true, message: '存储方式不能为空', trigger: 'blur'}
          ]
        },
        uploadLoading: false,
        fileExts: 'mp4/mov/avi/flv/wmv/mpeg/rmvb',
        fileList: []
      }
    },
    methods: {
      initBeforeHandle () {
        this.fileList = []
        this.dataForm.fileName = null
        this.dataForm.fileExt = null
        this.dataForm.fileSize = 0
        // 初始化预处理函数
      },
      changHandle (file, fileList) {
        let FileExt = file.name.replace(/.+\./, '')
        const isLt10G = file.size / 1024 / 1024 / 1024 < 10
        let allowTypes = `/${this.fileExts}/`
        if (allowTypes.indexOf(`/${FileExt}/`) === -1) {
          this.$message({
            type: '上传失败',
            message: `上传资源只能是${this.fileExts}格式!`
          })
          this.fileList = []
          return
        }
        if (!isLt10G) {
          this.$message.error('上传文件大小不能超过 10G!')
          this.fileList = []
          return
        }
        this.fileList = fileList
        this.dataForm.fileName = file.name
        this.dataForm.fileExt = FileExt
        this.dataForm.fileSize = file.size
      },
      handleExceed () {
        this.$message.error('只能选择一个文件，如需替换请删除已选文件后重试')
      },
      // 资源上传过滤
      beforeFileUpload () {
        let size = this.fileList.length
        if (size === 0) {
          this.$message.error('请选择资源文件')
          this.uploadLoading = false
          return false
        }
        if (size > 1) {
          this.$message.error('只能上传单个资源，请删除资源后再试')
          this.uploadLoading = false
          return false
        }
        let fileName = this.fileList[0].name
        if (fileName.indexOf('.') === -1) {
          this.uploadLoading = false
          this.$message.error('不支持的文件格式')
        }
        let fileExt = fileName.substring(fileName.lastIndexOf('.') + 1)
        let allowTypes = `/${this.fileExts}/`
        if (allowTypes.indexOf(`/${fileExt}/`) === -1) {
          this.$message.error(`上传资源只能是${this.fileExts}格式!`)
          this.uploadLoading = false
          return false
        }
        return true
      },
      dataFormSubmit() {
        if (this.fileList.length === 0) {
          this.$message.error('请选择视频文件')
          return
        }
        this.$refs['dataForm'].validate((valid) => {
          if (valid) {
            this.uploadLoading = true
            this.$refs.upload.submit()
          }
        })
      },
      saveResource (file) {
        if (!isAuth('video:list:type_select')) {
          this.dataForm.serverCode = null
        }
        this.$http({
          timeout: 600 * 1000,
          url: this.$http.adornUrl('/admin/portal_website/video/saveResource'),
          method: 'post',
          data: this.$http.adornData(this.dataForm)
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.$message({
              message: '开始上传',
              type: 'success'
            })
            this.uploadLoading = false
            this.$refs.upload.clearFiles()
            this.uploadFile(file, data.obj, this.dataForm.serverCode)
            this.visible = false
            this.$emit('refreshDataList')
          } else {
            this.uploadLoading = false
            this.$refs.upload.clearFiles()
            this.$message.error(data.msg)
          }
        })
      },
      async uploadFile(fileData, id, serverCode) {
        let item = {id: id, progressPercent: null, status: 'ready', failReason: null, timestamp: new Date().getTime()}
        let arr = JSON.parse(localStorage.getItem('uploadingData')) || []
        arr.push(item)
        localStorage.setItem('uploadingData', JSON.stringify(arr))
        this.$http({
          onUploadProgress: progressEvent => {
            let arr = JSON.parse(localStorage.getItem('uploadingData')) || []
            let data = _.find(arr, ['id', id])
            // progressEvent.loaded:已上传文件大小
            // progressEvent.total:被上传文件的总大小
            if (!data) {
              let item = {
                id: id,
                progressPercent: Number((progressEvent.loaded / progressEvent.total * 100).toFixed(2)),
                status: 'ing',
                failReason: null,
                timestamp: new Date().getTime()
              }
              arr.push(item)
            } else {
              data.status = 'ing'
              data.progressPercent = Number((progressEvent.loaded / progressEvent.total * 100).toFixed(2))
              data.timestamp = new Date().getTime()
            }
            localStorage.setItem('uploadingData', JSON.stringify(arr))
          },
          timeout: 600 * 1000,
          url: this.$http.adornUrl('/admin/portal_website/video/upload'),
          method: 'post',
          data: this.$http.adornData({
            'file': fileData.file,
            'serverCode': serverCode,
            'id': id
          }, true, 'file')
        }).then(({data}) => {
          let arr = JSON.parse(localStorage.getItem('uploadingData')) || []
          let exist = _.find(arr, ['id', id])
          if (!exist) {
            exist = {id: id, progressPercent: null, status: 'ing', failReason: null, timestamp: null}
            arr.push(exist)
          }
          if (data && data.code === 0) {
            exist.status = 'success'
            exist.timestamp = new Date().getTime()
            this.$message.success(fileData.file.name + '上传成功！')
          } else {
            exist.status = 'fail'
            exist.timestamp = new Date().getTime()
            exist.failReason = '上传失败：' + data.msg
            this.$message.error(fileData.file.name + '上传失败！')
          }
          localStorage.setItem('uploadingData', JSON.stringify(arr))
        }).catch((err) => {
          let arr = JSON.parse(localStorage.getItem('uploadingData')) || []
          let exist = _.find(arr, ['id', id])
          if (!exist) {
            exist = {id: id, progressPercent: null, status: 'ing', failReason: null, timestamp: null}
            arr.push(exist)
          }
          exist.status = 'fail'
          exist.timestamp = new Date().getTime()
          exist.failReason = '上传失败：' + err
          this.$message.success(fileData.file.name + '上传失败！')
          localStorage.setItem('uploadingData', JSON.stringify(arr))
        })
      }
    }
  }
</script>

<style lang="scss">
  .avatar {
    width: 178px;
    height: 178px;
    display: block;
  }
  .avatar-uploader .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
  }

  .avatar-uploader .el-upload:hover {
    border-color: #409EFF;
  }

  .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 178px;
    height: 178px;
    line-height: 178px;
    text-align: center;
  }
  .mod-menu {
    .menu-list__input,
    .icon-list__input {
      > .el-input__inner {
        cursor: pointer;
      }
    }
    &__icon-popover {
      width: 458px;
      overflow: hidden;
    }
    &__icon-inner {
      width: 478px;
      max-height: 258px;
      overflow-x: hidden;
      overflow-y: auto;
    }
    &__icon-list {
      width: 458px;
      padding: 0;
      margin: -8px 0 0 -8px;
      > .el-button {
        padding: 8px;
        margin: 8px 0 0 8px;
        > span {
          display: inline-block;
          vertical-align: middle;
          width: 18px;
          height: 18px;
          font-size: 18px;
        }
      }
    }
    .icon-list__tips {
      font-size: 18px;
      text-align: center;
      color: #e6a23c;
      cursor: pointer;
    }
  }
</style>
