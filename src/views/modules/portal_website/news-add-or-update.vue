<template>
  <div>
    <el-dialog
        :title="onlyRead === true ? '详情' : (!dataForm.id ? '新增' : '修改')"
        :close-on-click-modal="false"
        :visible.sync="visible" width="80%">
      <el-form :model="dataForm" :rules="dataRule" ref="dataForm" label-width="100px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="类型：" prop="categoryIds" :error="errors['categoryIds']">
              <el-cascader
                  style="width: 100%"
                  :disabled="onlyRead"
                  clearable
                  placeholder="请选择"
                  v-model="dataForm.categoryIds"
                  :options="categories"
                  :props="{checkStrictly: true}"
              ></el-cascader>
              <!--              <el-cascader v-model="dataForm.categoryIds" :data="categories"></el-cascader>-->
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="标题：" prop="title" :error="errors['title']">
              <el-input v-model="dataForm.title" placeholder="请输入" clearable :disabled="onlyRead" maxlength="100"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
            <el-col :span="8">
                <el-form-item :label="'使用外链'" prop="useCustomLinks">
                    <el-radio-group v-model="dataForm.useCustomLinks">
                        <el-radio :label="false">否</el-radio>
                        <el-radio :label="true">是</el-radio>
                    </el-radio-group>
                    <span style="color: red;padding-left: 15px">可使用公众号推文链接</span>
                </el-form-item>
            </el-col>
            <el-col :span="16">
                <el-form-item label="外部链接" prop="customLinks" :error="customLinksError"
                              v-if="dataForm.useCustomLinks">
                    <el-input v-model="dataForm.customLinks" placeholder="自定义链接"></el-input>
                </el-form-item>
            </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item v-if="!onlyRead" label="图片：" prop="titleImgUrl">
              <el-upload
                  :disabled="onlyRead"
                  class="avatar-uploader"
                  :action="this.$http.adornUrl(`/admin/oss/upload`)"
                  :headers="headers"
                  :data="{serverCode: uploadOptions.serverCode, media: false}"
                  :show-file-list="false"
                  :on-success="successHandle"
                  :on-change="changHandle"
                  :on-exceed="exceedHandle"
                  :before-upload="beforeUploadHandle">
                <img v-if="dataForm.titleImgUrl" :src="$http.adornAttachmentUrl(dataForm.titleImgUrl)" class="avatar">
                <i v-else class="el-icon-plus avatar-uploader-icon"></i>
                <div slot="tip" class="el-upload__tip" style="color: red">尺寸建议600x1000像素，文件格式jpg、bmp或png，大小不超2M</div>
              </el-upload>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item v-if="onlyRead" label="图片：" prop="titleImgUrl">
              <el-image
                  :src="$http.adornAttachmentUrl(dataForm.titleImgUrl)"
                  :preview-src-list="[$http.adornAttachmentUrl(dataForm.titleImgUrl)]"
                  class="avatar"
              >
              </el-image>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="发布时间：" prop="effectiveDate" :error="errors['effectiveDate']">
              <el-date-picker
                  :disabled="onlyRead"
                  v-model="dataForm.effectiveDate"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  type="datetime"
                  placeholder="请选择">
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="上架状态：" prop="grounding" :error="errors['grounding']">
              <el-radio-group v-model="dataForm.grounding" :disabled="onlyRead">
                <el-radio :label="true">上架</el-radio>
                <el-radio :label="false">下架</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item v-if="dataForm.categoryIds[0] === '1623521932668305410'" label="关联活动：" prop="activityId"
                          :error="errors['activityId']">
              <el-select :disabled="onlyRead" style="width: 70%" filterable  v-model="dataForm.activityId" placeholder="请选择"
                         clearable>
                <el-option v-for="item in activityList" :key="item.value" :label="item.label" :value="item.value"/>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="简介：" prop="briefIntroduction" :error="errors['briefIntroduction']">
              <el-input type="textarea" rows="3" maxlength="500" show-word-limit v-model="dataForm.briefIntroduction"
                        placeholder="请输入" :disabled="onlyRead"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="详细描述：" prop="description">
              <teditor
                  style="width: 100%;"
                  :value="dataForm.description"
                  :disabled="onlyRead"
                  ref="teditor"
                  @changeEditorValue="changeAnswer"
              ></teditor>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button v-if="!onlyRead || onlyRead === false" @click="visible = false">取消</el-button>
        <el-button v-if="onlyRead && onlyRead === true" @click="visible = false">关闭</el-button>
        <el-button v-if="!onlyRead || onlyRead === false" type="primary" @click="dataFormSubmit()"
                   :disabled="dataFormLoading">确定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import Teditor from '@/components/tinymce'
import moment from 'moment'
import editMixin from '@/mixins/edit-mixins'
import fileUploadMixin from '@/mixins/file-upload-mixins'

export default {
  mixins: [editMixin, fileUploadMixin],
  data() {
    return {
      uploadOptions: {
        fieldName: 'titleImgUrl',
        maxSize: 2
      },
      editOptions: {
        initUrl: '/admin/portal_website/news',
        saveSuffix: 'save',
        updateSuffix: 'update',
        submitUrl: '/admin/portal_website/news'
      },
      addFileVisible: false,
      dataForm: {
        categoryIds: [],
        activityId: '',
        title: null,
        titleImgUrl: null,
        briefIntroduction: null,
        description: null,
        effectiveDate: '',
        grounding: true,
        useCustomLinks: false,
        customLinks: null,
      },
      dataRule: {
        title: [
          {required: true, message: '标题不能为空', trigger: 'blur'}
        ],
        categoryIds: [
          {required: true, message: '所属栏目不能为空', trigger: 'change'}
        ],
        effectiveDate: [
          {required: true, message: '发布时间不能为空', trigger: 'change'}
        ],
        grounding: [
          {required: true, message: '上下架状态不能为空', trigger: 'change'}
        ],
        titleImgUrl: [
            {required: true, message: '图片不能为空', trigger: 'change'}
        ],
        useCustomLinks: [
            {required: true, message: '请选择是否需要使用外链', trigger: 'blur'}
        ]
      },
      onlyRead: false,
      categories: [],
      activityList: []
    }
  },
  components: {
    Teditor,
    moment
  },
  methods: {
    async init(id, onlyRead) {

      this.dataForm = {
        id: id || undefined,
        categoryIds: [],
        activityId: '',
        title: null,
        titleImgUrl: null,
        briefIntroduction: null,
        description: null,
        effectiveDate: '',
        grounding: true,
        useCustomLinks: false,
        customLinks: null,
      }
      this.onlyRead = onlyRead || false
      this.activityList = []
      await this.getActivityList()
      this.changeAnswer('')
      this.visible = true
      this.dataForm.effectiveDate = moment(new Date()).format('YYYY-MM-DD HH:mm:ss')
      this.getCategories()
      this.$nextTick(() => {
        this.$refs['dataForm']?.resetFields()
        this.dataForm.description = null
        if (this.dataForm.id) {
          this.getData()
        }
      })
    },
    getCategories() {
      this.$http({
        url: this.$http.adornUrl('/admin/cms/category/cascaderByParentCode'),
        method: 'get',
        params: this.$http.adornParams({
          'parentCode': 'NEWS'
        })
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.categories = this.getTreeData(data.obj)
        } else {
          this.categories = []
        }
      })
    },
    async getActivityList() {
      var url=this.$http.adornUrl('/admin/portal_website/news/getActivityListForNews');
      if(this.onlyRead){
          url=this.$http.adornUrl('/admin/portal_website/news/getActivityByNewsId');
      }
      await this.$http({
          url: url,
          method: 'get',
          params: this.$http.adornParams({
              "newsId": this.dataForm.id
          })
      }).then(({data}) => {
          if (data && data.code === 0) {
              this.activityList = this.getTreeData(data.obj)
          } else {
              this.activityList = []
          }
      })
    },
    // *处理点位分类最后children数组为空的状态
    getTreeData: function (data) {
      var that = this
      // 循环遍历json数据
      data.forEach(function (e) {
        if (!e.children || e.children.length < 1) {
          e.children = undefined
        } else {
          // children若不为空数组，则继续 递归调用 本方法
          that.getTreeData(e.children)
        }
      })
      return data
    },
    handleImgRemove() {
      this.dataForm.titleImgUrl = null
    },
    // 富文本赋值
    changeAnswer(html) {
      console.log(html)
      this.dataForm.description = html
    }
  }
}
</script>

<style lang="scss">
.avatar {
  width: 178px;
  height: 178px;
  display: block;
}

.avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.avatar-uploader .el-upload:hover {
  border-color: #409EFF;
}

.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  line-height: 178px;
  text-align: center;
}

.mod-menu {
  .menu-list__input,
  .icon-list__input {
    > .el-input__inner {
      cursor: pointer;
    }
  }

  &__icon-popover {
    width: 458px;
    overflow: hidden;
  }

  &__icon-inner {
    width: 478px;
    max-height: 258px;
    overflow-x: hidden;
    overflow-y: auto;
  }

  &__icon-list {
    width: 458px;
    padding: 0;
    margin: -8px 0 0 -8px;

    > .el-button {
      padding: 8px;
      margin: 8px 0 0 8px;

      > span {
        display: inline-block;
        vertical-align: middle;
        width: 18px;
        height: 18px;
        font-size: 18px;
      }
    }
  }

  .icon-list__tips {
    font-size: 18px;
    text-align: center;
    color: #e6a23c;
    cursor: pointer;
  }
}
</style>
