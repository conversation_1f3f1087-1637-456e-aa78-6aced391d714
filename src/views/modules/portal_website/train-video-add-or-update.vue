<template>
  <div>
    <el-dialog
        :title="!dataForm.id ? '新增' : '修改'"
        :close-on-click-modal="false"
        :visible.sync="visible" width="80%">
      <el-form :model="dataForm" :rules="dataRule" ref="dataForm" label-width="100px">
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="名称：" prop="title" :error="errors['title']">
              <el-input v-model="dataForm.title" placeholder="请输入"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="图片：" prop="titleImgUrl">
              <el-upload
                  class="avatar-uploader"
                  :action="this.$http.adornUrl(`/admin/oss/upload`)"
                  :headers="headers"
                  :data="{serverCode: uploadOptions.serverCode, media: false}"
                  :show-file-list="false"
                  :on-success="successHandle"
                  :on-change="changHandle"
                  :on-exceed="exceedHandle"
                  :before-upload="beforeUploadHandle">
                <img v-if="dataForm.titleImgUrl" :src="$http.adornAttachmentUrl(dataForm.titleImgUrl)" class="avatar">
                <i v-else class="el-icon-plus avatar-uploader-icon"></i>
                <div slot="tip" class="el-upload__tip" style="color: red">尺寸建议600x1000像素，文件格式jpg、bmp或png，大小不超2M</div>
              </el-upload>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="排序：" prop="sequence" :error="errors['sequence']">
              <el-input-number v-model="dataForm.sequence" placeholder="请输入" :min="0"></el-input-number>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="发布时间：" prop="effectiveDate" :error="errors['effectiveDate']">
              <el-date-picker
                  v-model="dataForm.effectiveDate"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  type="datetime"
                  placeholder="请选择">
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="上架状态：" prop="grounding" :error="errors['grounding']">
              <el-radio-group v-model="dataForm.grounding">
                <el-radio :label="true">上架</el-radio>
                <el-radio :label="false">下架</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="简介：" prop="briefIntroduction" :error="errors['briefIntroduction']">
              <el-input type="textarea" rows="3" maxlength="500" show-word-limit v-model="dataForm.briefIntroduction" placeholder="请输入"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="视频：" prop="videoShow">
              <el-tag
                  v-for="tag in dataForm.videoShow"
                  :key="tag"
                  type="info"
                  style="margin-right: 5px">
                <a :href="tag" target="_blank">{{tag}}</a>
              </el-tag>
<!--              <el-link v-for="item in dataForm.videoShow" :href="item" target="_blank">{{item}}</el-link>-->
              <el-button type="text" @click="selectVideo">{{videoSelectVisible ? '收起视频库' : '从视频库选择'}}</el-button>
              <el-button type="text" v-if="dataForm.videoShow && dataForm.videoShow.length > 0" @click="clearVideoInfo">清空</el-button>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-if="videoSelectVisible" :gutter="20">
          <el-col :span="23" :offset="1" style="border: 1px solid rgba(215, 215, 215, 1); border-radius: 4px">
            <el-form :inline="true" :model="videoForm" ref="videoForm" style="margin-top: 20px">
              <el-form-item label="文件名" prop="name">
                <el-input v-model="videoForm.name" placeholder="名称/原文件名模糊查询" clearable></el-input>
              </el-form-item>
              <el-form-item>
                <el-button @click="getVideoList()">查询</el-button>
              </el-form-item>
            </el-form>
            <el-table
                :data="videoList"
                border
                row-key="id"
                ref="videoTable"
                v-loading="videoListLoading"
                @selection-change="selectionChangeHandle"
                style="width: 100%;">
              <el-table-column
                  type="selection"
                  reserve-selection
                  header-align="center"
                  align="center"
                  width="50">
              </el-table-column>
              <el-table-column
                  prop="name"
                  header-align="center"
                  align="center"
                  label="名称">
              </el-table-column>
              <el-table-column
                  prop="fileName"
                  header-align="center"
                  align="center"
                  label="原文件名">
              </el-table-column>
              <el-table-column
                  prop="path"
                  header-align="center"
                  align="center"
                  width="300"
                  :show-overflow-tooltip="true"
                  label="URL地址">
                <template slot-scope="scope">
                  <div v-if="scope.row.path && scope.row.path !== ''" style="overflow: hidden; text-overflow: ellipsis;white-space: nowrap;">
<!--                    <el-button class="el-icon-document-copy" circle style="font-size: 20px; border: 0px; padding: 5px" @click="copyText($http.adornAttachmentUrl(scope.row.path))"/>-->
                    <a :href="$http.adornAttachmentUrl(scope.row.path)" target="_blank">{{$http.adornAttachmentUrl(scope.row.path)}}</a>
                  </div>
                </template>
              </el-table-column>
              <el-table-column
                  prop="createDate"
                  header-align="center"
                  align="center"
                  label="上传时间">
              </el-table-column>
            </el-table>
            <el-pagination
                style="margin-bottom: 20px"
                @size-change="sizeChangeHandle"
                @current-change="currentChangeHandle"
                :current-page="pageIndex"
                :page-sizes="[5, 10, 20, 50, 100]"
                :page-size="pageSize"
                :total="totalPage"
                layout="total, sizes, prev, pager, next, jumper">
            </el-pagination>
          </el-col>
        </el-row>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="visible = false">取消</el-button>
        <el-button type="primary" @click="dataFormSubmit()" :disabled="dataFormLoading">确定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import editMixin from '@/mixins/edit-mixins'
import fileUploadMixin from '@/mixins/file-upload-mixins'
import moment from 'moment'
export default {
  mixins: [editMixin, fileUploadMixin],
  data () {
    return {
      uploadOptions: {
        fieldName: 'titleImgUrl',
        maxSize: 2
      },
      editOptions: {
        initUrl: '/admin/portal_website/video_category',
        saveSuffix: 'save',
        updateSuffix: 'update',
        submitUrl: '/admin/portal_website/video_category'
      },
      addFileVisible: false,
      dataForm: {
        title: null,
        titleImgUrl: null,
        briefIntroduction: null,
        videoShow: [],
        description: null,
        effectiveDate: '',
        sequence: 0,
        type: 'TRAIN_VIDEO',
        grounding: true
      },
      dataRule: {
        title: [
          { required: true, message: '名称不能为空', trigger: 'blur' }
        ],
        effectiveDate: [
          {required: true, message: '发布时间不能为空', trigger: 'change'}
        ],
        sequence: [
          {required: true, message: '排序不能为空', trigger: 'blur'}
        ],
        grounding: [
          {required: true, message: '上下架状态不能为空', trigger: 'change'}
        ]
      },
      videoSelectVisible: false,
      videoList: [],
      videoListLoading: false,
      videoForm: {
        name: null,
        importStatus: 1
      },
      pageIndex: 1,
      pageSize: 5,
      totalPage: 0,
      currentSelect: null,
      dataListLoading: false
    }
  },
  components: {
    moment
  },
  methods: {
    init (id) {
      this.dataForm = {
        id: id || undefined,
        title: null,
        titleImgUrl: null,
        briefIntroduction: null,
        videoShow: [],
        description: null,
        effectiveDate: '',
        sequence: 0,
        type: 'TRAIN_VIDEO',
        grounding: true
      }
      this.changeAnswer('')
      this.visible = true
      this.dataForm.effectiveDate = moment(new Date()).format('YYYY-MM-DD HH:mm:ss')
      this.videoSelectVisible = false
      this.videoListLoading = false
      this.videoForm.name = null
      this.pageIndex = 1
      this.$nextTick(() => {
        this.$refs['dataForm']?.resetFields()
        this.dataForm.description = null
        if (this.dataForm.id) {
          this.getData()
        }
      })
    },
    handleImgRemove () {
      this.dataForm.titleImgUrl = null
    },
    // 富文本赋值
    changeAnswer (html) {
      this.dataForm.description = html
    },
    selectVideo () {
      if (this.videoSelectVisible) {
        this.videoForm.name = null
        this.videoListLoading = false
        this.videoList = []
        this.videoSelectVisible = false
      } else {
        this.videoSelectVisible = true
        this.getVideoList()
      }
    },
    selectionChangeHandle (val) {
      this.videoListSelections = val
      this.dataForm.videoShow = val.map(item => {
        return this.$http.adornAttachmentUrl(item.path)
      })
      this.dataForm.description = val.map(item => {
        return item.id
      }).join(',')
    },
    // 每页数
    sizeChangeHandle (val) {
      this.pageSize = val
      this.pageIndex = 1
      this.getVideoList()
    },
    // 当前页
    currentChangeHandle (val) {
      this.pageIndex = val
      this.getVideoList()
    },
    clearVideoInfo() {
      this.dataForm.videoShow = []
      this.dataForm.description = null
      if(this.$refs.videoTable) {
        this.$refs.videoTable.clearSelection()
      }
    },
    getVideoList() {
      this.videoListLoading = true
      this.$http({
        url: this.$http.adornUrl('/admin/portal_website/video/pages'),
        method: 'post',
        data: this.$http.adornData({
          currentPage: this.pageIndex,
          pageSize: this.pageSize,
          ...this.videoForm
        })
      }).then(({data}) => {
        this.videoListLoading =false
        if (data && data.code === 0) {
          // 查询成功操作
          this.videoList = data.obj.records
          this.totalPage = data.obj.total
        } else {
          this.videoList = []
          this.totalPage = 0
          return this.$message.error(data.msg)
        }
      }).catch(() => {
        this.videoListLoading = false
      })
    }
  }
}
</script>

<style lang="scss">
.avatar {
  width: 178px;
  height: 178px;
  display: block;
}
.avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.avatar-uploader .el-upload:hover {
  border-color: #409EFF;
}

.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  line-height: 178px;
  text-align: center;
}
.mod-menu {
  .menu-list__input,
  .icon-list__input {
    > .el-input__inner {
      cursor: pointer;
    }
  }
  &__icon-popover {
    width: 458px;
    overflow: hidden;
  }
  &__icon-inner {
    width: 478px;
    max-height: 258px;
    overflow-x: hidden;
    overflow-y: auto;
  }
  &__icon-list {
    width: 458px;
    padding: 0;
    margin: -8px 0 0 -8px;
    > .el-button {
      padding: 8px;
      margin: 8px 0 0 8px;
      > span {
        display: inline-block;
        vertical-align: middle;
        width: 18px;
        height: 18px;
        font-size: 18px;
      }
    }
  }
  .icon-list__tips {
    font-size: 18px;
    text-align: center;
    color: #e6a23c;
    cursor: pointer;
  }
}
</style>
