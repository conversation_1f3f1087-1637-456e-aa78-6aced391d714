<template>
  <div class="mod-role">
    <el-form :inline="true" :model="dataForm" ref="dataForm" @keyup.enter.native="getDataList()">
      <el-form-item label="名称" prop="name">
        <el-input v-model="dataForm.name" placeholder="名称/原文件名模糊查询" clearable></el-input>
      </el-form-item>
      <el-form-item label="存储方式" prop="serverCode" v-if="isAuth('video:list:type_select')">
        <el-dict :code="'server-code'" v-model="dataForm.serverCode"></el-dict>
      </el-form-item>
      <el-form-item label="上传状态" prop="importStatus">
        <el-select v-model="dataForm.importStatus" placeholder="请选择" clearable>
          <el-option
              v-for="item in [{label: '正在上传', value: 2}, {label: '上传成功', value: 1}, {label: '上传失败', value: 0}]"
              :key="item.value"
              :label="item.label"
              :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-search" type="warning" @click="getDataList()">查询</el-button>
        <el-button icon="el-icon-refresh-left" @click="resetForm()">重置</el-button>
      </el-form-item>
    </el-form>
    <div class="f-s-c" style="padding: 15px 0;display: flex; justify-content: flex-end">
      <div>
        <el-button icon="el-icon-plus" type="primary" @click="addOrUpdateHandle()" v-if="isAuth('video:list:upload')">新增</el-button>
        <el-button icon="el-icon-delete" type="danger" @click="deleteHandle()" v-if="isAuth('video:list:remove')" :disabled="dataListSelections.length <= 0">批量删除</el-button>
      </div>
    </div>
    <el-table
        :data="dataList"
        border
        v-loading="dataListLoading"
        @selection-change="selectionChangeHandle"
        style="width: 100%;">
      <el-table-column
          type="selection"
          header-align="center"
          align="center"
          width="50">
      </el-table-column>
      <el-table-column
          prop="name"
          header-align="center"
          align="center"
          :show-overflow-tooltip="true"
          label="名称">
        <template slot-scope="scope">
          <span v-if="!scope.row.nameEdit"><el-button icon="el-icon-edit" type="text" @click="editName(scope.row)"></el-button>{{scope.row.name}}</span>
          <el-input v-if="scope.row.nameEdit && scope.row.nameEdit === true" v-model="scope.row.name">
            <el-button style="padding-right:10px" slot="suffix" type="text" icon="el-icon-check" @click="finishEdit(scope.row)"></el-button>
          </el-input>
        </template>
      </el-table-column>
      <el-table-column
          prop="fileName"
          header-align="center"
          align="center"
          :show-overflow-tooltip="true"
          label="原文件名">
      </el-table-column>
      <el-table-column
          v-if="isAuth('video:list:type_select')"
          prop="serverCodeText"
          header-align="center"
          align="center"
          label="存储方式">
      </el-table-column>
      <el-table-column
          prop="path"
          header-align="center"
          align="center"
          width="300"
          :show-overflow-tooltip="true"
          label="URL地址">
        <template slot-scope="scope">
          <div v-if="scope.row.path && scope.row.path !== ''" style="overflow: hidden; text-overflow: ellipsis;white-space: nowrap;">
            <el-button class="el-icon-document-copy" circle style="font-size: 20px; border: 0px; padding: 5px" @click="copyText($http.adornAttachmentUrl(scope.row.path))"/>
            <a :href="$http.adornAttachmentUrl(scope.row.path)" target="_blank">{{$http.adornAttachmentUrl(scope.row.path)}}</a>
          </div>
<!--          <span v-if="scope.row.path && scope.row.path !== ''">{{scope.row.path}}</span>-->
          <el-progress v-else-if="scope.row.importStatus === 'ing'" :percentage="scope.row.statusShow" color="limegreen"></el-progress>
          <span v-else style="color: darkred">{{scope.row.failReason || scope.row.statusShow}}</span>
        </template>
      </el-table-column>
      <el-table-column
          prop="createDate"
          header-align="center"
          align="center"
          width="180"
          label="上传时间">
      </el-table-column>
      <el-table-column
          fixed="right"
          header-align="center"
          align="center"
          width="160"
          label="操作">
        <template slot-scope="scope">
          <el-button type="text" size="small" v-if="isAuth('video:list:download') && scope.row.path && scope.row.path !== ''" @click="download(scope.row)">下载</el-button>
          <el-button type="text" size="small" v-if="isAuth('video:list:remove')" @click="deleteHandle(scope.row.id)">删除</el-button>
          <el-button type="text" size="small" v-if="scope.row.path && scope.row.path !== ''" @click="detailHandle(scope.row.id)">视频信息</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
        @size-change="sizeChangeHandle"
        @current-change="currentChangeHandle"
        :current-page="pageIndex"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pageSize"
        :total="totalPage"
        layout="total, sizes, prev, pager, next, jumper">
    </el-pagination>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"></add-or-update>
    <detail v-if="detailVisible" ref="detail"></detail>
  </div>
</template>

<script>
import AddOrUpdate from './video-add-or-update'
import Detail from './video-detail'
import listMixin from '@/mixins/list-mixins'
import Clipboard from 'clipboard'
import _ from 'lodash'
export default {
  mixins: [listMixin],
  data () {
    return {
      uploadMonitorTimer: null,
      removeRequestInterruptTimer: null,
      mixinOptions: {
        dataUrl: '/admin/portal_website/video/pages',
        deleteUrl: '/admin/portal_website/video/removeByIds'
      },
      dataForm: {
        name: null,
        serverCode: null,
        importStatus: null
      },
      detailVisible: false
    }
  },
  components: {
    AddOrUpdate,
    Detail
  },
  activated () {
    this.startUploadMonitor()
    this.startRemoveRequestInterrupt()
    this.getDataList()
    this.updateRequestInterrupt()
  },
  beforeDestroy () {
    clearTimeout(this.uploadMonitorTimer)
  },
  methods: {
    updateRequestInterrupt() {
      let arr = JSON.parse(localStorage.getItem('uploadingData')) || []
      let needRemoveArr = []
      var needRefresh = false
      _.forEach(arr, item => {
        var now = new Date().getTime()
        if (item.status === 'ing') {
          if (!item.timestamp || now - item.timestamp > 60000) {
            needRemoveArr.push(item)
            needRefresh = true
          }
        }
        if (!item.timestamp || item.status === 'ready') {
          if (now - item.timestamp > 60000) {
            needRemoveArr.push(item)
            needRefresh = true
          }
        }
      })
      _.forEach(needRemoveArr, item => {
        arr.splice(arr.indexOf(item), 1)
      })
      localStorage.setItem('uploadingData', JSON.stringify(arr))
      if (needRefresh) {
        this.updateStatus(needRemoveArr.map(item => {
          return item.id
        }).join(','), false, '上传失败：请求中断', needRefresh)
      }
    },
    resetForm () {
      this.$refs['dataForm']?.resetFields()
      this.getDataList()
    },
    querySuccessHandle (data) {
      // 查询成功操作
      this.dataList = data.records
      this.totalPage = data.total
    },
    startUploadMonitor () {
      this.uploadMonitorTimer = setInterval(() => {
        var needRefresh = false
        let arr = JSON.parse(localStorage.getItem('uploadingData'))
        _.forEach(this.dataList, item => {
          if (item.path && item.path !== '') {
            return
          }
          var data = _.find(arr, ['id', item.id])
          if (!data) {
            return
          }
          this.$set(item, 'importStatus', data.status)
          if (data.status === 'ready') {
            this.$set(item, 'statusShow', '文件上传准备中……')
            return
          }
          if (data.status === 'ing') {
            this.$set(item, 'statusShow', data.progressPercent)
            return
          }
          if (data.status === 'fail') {
            this.$set(item, 'statusShow', data.failReason)
            this.updateStatus(item.id, false, data.failReason, false)
            arr.splice(arr.indexOf(data), 1)
            return
          }
          if (data.status === 'success') {
            needRefresh = true
            arr.splice(arr.indexOf(data), 1)
          }
        })
        localStorage.setItem('uploadingData', JSON.stringify(arr))
        if (needRefresh) {
          this.query()
        }
      }, 1000)
    },
    format(percentage) {
      return percentage === 100 ? '上传成功' : `${percentage}%`;
    },
    updateStatus (id, status, failReason, needRefresh) {
      this.$http({
        url: this.$http.adornUrl('/admin/portal_website/video/updateStatus'),
        method: 'get',
        params: this.$http.adornParams({
          id: id,
          status: status,
          failReason: failReason
        })
      }).then(({data}) => {
        if (data && data.code === 0) {
        } else {
          this.$message.error('状态更新失败！')
        }
        if (needRefresh) {
          this.query()
        }
      })
    },
    startRemoveRequestInterrupt () {
      this.removeRequestInterruptTimer = setInterval(() => {
        this.updateRequestInterrupt()
      }, 3000)
    },
    // 下载文件
    download (row) {
      let filepath = row.path
      if (row.serverCode === 'LocalServer') {
        filepath = this.$http.adornAttachmentUrl(row.path) + '?download=1'
      }
      let link = document.createElement('a')
      link.href = filepath
      link.download = row.fileName
      link.click()
    },
    copyText (text) {
      var clipboard = new Clipboard('.el-icon-document-copy', {
        text: function () {
          return text
        }
      })
      clipboard.on('success', e => {
        this.$message.success({
          message: '复制成功'
        })
        clipboard.destroy()
      })
      clipboard.on('error', e => {
        this.$message.warning({
          message: '该浏览器不支持自动复制'
        })
        clipboard.destroy()
      })
    },
    detailHandle (id) {
      this.detailVisible = true
      this.$nextTick(() => {
        this.$refs.detail.init(id)
      })
    },
    editName(row) {
      this.$set(row, 'nameEdit', true)
      this.$set(row, 'originName', row.name)
    },
    finishEdit(row) {
      if (row.originName === row.name) {
        this.$set(row, 'nameEdit', false)
        return
      }
      this.$http({
        url: this.$http.adornUrl('/admin/portal_website/video/updateName'),
        method: 'get',
        params: this.$http.adornParams({
          id: row.id,
          name: row.name
        })
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.$message.success('文件名称修改成功！')
        } else {
          this.$message.error('文件名称修改失败！')
          this.query()
        }
      }).catch((err) => {
        this.$message.error(err)
        this.query()
      })
      this.$set(row, 'nameEdit', false)
    }
  }
}
</script>
