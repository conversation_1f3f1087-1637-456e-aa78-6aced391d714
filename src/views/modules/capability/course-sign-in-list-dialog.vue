<template>
  <el-dialog
      :visible.sync="visible"
      title="签到管理"
      width="80%"
      :before-close="handleClose"
      append-to-body
  >
    <el-form
        :inline="true"
        ref="dataForm"
        :model="dataForm"
        @keyup.enter.native="getDataList()"
        style="display: flex; justify-content: space-between; align-items: flex-start"
    >
      <div style="display: flex">
        <el-form-item label="关键字" prop="keyword">
          <el-input v-model="dataForm.keyword" placeholder="请输入志愿者姓名/手机号码/身份证号码搜索"
                    style="min-width: 330px" clearable></el-input>
        </el-form-item>
        <el-form-item>
          <el-button icon="el-icon-search" type="primary" @click="getDataList()">查询</el-button>
        </el-form-item>
      </div>

      <el-form-item>
        <el-button type="success" @click="exportHandle()">导出</el-button>
      </el-form-item>
    </el-form>

    <el-table
        :data="dataList"
        v-loading="dataListLoading"
        border>
      <el-table-column prop="name" label="志愿者姓名" align="center"></el-table-column>
      <el-table-column prop="phone" label="手机号码" align="center"></el-table-column>
      <el-table-column prop="certificateId" label="身份证号码" align="center"></el-table-column>
      <el-table-column prop="courseStartAt" label="课程开始时间" align="center"></el-table-column>
      <el-table-column prop="signInAt" label="签到时间" align="center"></el-table-column>
      <el-table-column prop="actualCredits" label="学分" align="center"></el-table-column>
      <el-table-column label="操作" align="center">
        <template slot-scope="scope">
          <el-button type="text" size="small" @click="changeCredits(scope.row)">更正学分</el-button>
        </template>
      </el-table-column>
    </el-table>

    <el-pagination
        @size-change="sizeChangeHandle"
        @current-change="currentChangeHandle"
        :current-page="pageIndex"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pageSize"
        :total="totalPage"
        layout="total, sizes, prev, pager, next, jumper"
    ></el-pagination>
    <el-dialog
        title="更正学分"
        :visible.sync="creditsDialogVisible"
        width="400px"
        append-to-body
    >
      <el-form label-width="80px">
        <el-form-item label="学分">
          <el-input-number
              v-model="currentCredits"
              :min="0"
              :step="0.5"
              controls-position="right"
              style="width: 100%"
          ></el-input-number>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="creditsDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitCredits">确 定</el-button>
      </div>
    </el-dialog>
  </el-dialog>
</template>

<script>
import listMixin from '@/mixins/list-mixins'; // Reusing the mixin

export default {
  mixins: [listMixin], // Using the same mixin for this page
  data() {
    return {
      // 新增的数据项
      creditsDialogVisible: false,
      currentCredits: 0,
      currentCourseId: null,     // 当前正在修改的记录ID
      currentUserId: null,     // 当前正在修改的记录ID
      visible: false,
      mixinOptions: {
        dataUrl: '/admin/manager/capability-upgrading/course/sign-in/page',
        exportUrl: '/admin/manager/capability-upgrading/course/sign-in/export'
      },
      dataForm: {
        courseId: null,
        keyword: ''
      },
    };
  },
  methods: {
    // 修改原来的方法
    changeCredits(row) {
      this.currentCredits = row.actualCredits
      this.currentCourseId = row.courseId  // 假设数据行中有唯一标识id
      this.currentUserId = row.userId  // 假设数据行中有唯一标识id
      this.creditsDialogVisible = true
    },
    // 新增提交方法
    async submitCredits() {
      const url = '/admin/manager/capability-upgrading/course/credits/change';
      const params = {
        courseId: this.currentCourseId,
        userId: this.currentUserId,
        credits: this.currentCredits
      };
      this.$http({
        url: this.$http.adornUrl(url),
        method: 'post',
        data: this.$http.adornData(params)
      }).then(({data}) => {
        if (data.code === 0) {
          this.$message.success('操作成功');
          this.getDataList();
          this.creditsDialogVisible = false
        } else {
          this.$message.warning(data.msg);
        }
      })
    },
    handleClose() {
      this.$refs['dataForm']?.resetFields();
      this.visible = false
    },
    init(id) {
      this.visible = true;
      this.dataForm.courseId = id;
      this.getDataList();
    }
  },
};
</script>
