<template>
  <el-dialog title="签到二维码" :visible.sync="visible" width="33%">
    <div class="qrcode-content">
      <div class="course-info">
        <div class="time-range">{{ signTimeRange }}</div>
      </div>
      <div class="qrcode-wrapper">
        <img :src="$http.adornAttachmentUrl(checkInQrcode)" alt="签到二维码" v-if="checkInQrcode" class="qrcode-img">
        <div v-else class="loading-text">二维码加载中...</div>
      </div>
      <div class="qrcode-content">
        <div class="course-info">
          <div class="course-name">{{ courseName }}</div>
          <div class="time-range">
            签到时间范围: {{ signInStartAt }} - {{ signInEndAt }}
          </div>
        </div>
      </div>
      <div class="tip-text">
        <i class="el-icon-warning"></i>
        微信扫码签到
      </div>
    </div>
  </el-dialog>
</template>

<script>
export default {
  data() {
    return {
      visible: false,
      courseName: '',
      signTimeRange: '',
      checkInQrcode: '',
      signInStartAt: '',
      signInEndAt: '',
    }
  },
  methods: {
    init(courseId) {
      this.visible = true
      this.getQrcodeData(courseId)
    },
    async getQrcodeData(courseId) {
      try {
        const { data } = await this.$http({
          url: this.$http.adornUrl('/admin/manager/capability-upgrading/course/qrcode'),
          method: 'get',
          params: this.$http.adornParams({ id: courseId })
        })

        if (data.code === 0) {
          this.courseName = data.obj.courseName
          this.signInStartAt = data.obj.signInStartAt
          this.signInEndAt = data.obj.signInEndAt
          this.checkInQrcode = data.obj.checkInQrcode
        } else {
          this.$message.error(data.msg)
        }
      } catch (error) {
        this.$message.error('二维码获取失败')
      }
    }
  }
}
</script>

<style scoped>
.qrcode-content {
  text-align: center;
  padding: 20px;
}

.course-info {
  margin-bottom: 15px;
}

.course-name {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 8px;
}

.time-range {
  color: #666;
  font-size: 14px;
}

.qrcode-img {
  width: 200px;
  height: 200px;
}

.tip-text {
  background: #fffbe6;
  color: #e6a23c;
  padding: 10px;
  border-radius: 4px;
  display: inline-flex;
  align-items: center;
}

.el-icon-warning {
  margin-right: 5px;
}

/* 新增样式 */
.qrcode-wrapper {
  position: relative;
  width: 80%; /* 容器宽度 */
  max-width: 300px; /* 最大尺寸限制 */
  aspect-ratio: 1/1; /* 关键属性-强制正方形 */
  margin: 0 auto;
}

.qrcode-img {
  width: 100%;
  height: 100%;
  object-fit: contain; /* 保持原始比例 */
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

/* 加载占位图适配 */
.loading-text {
  position: absolute;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f5f7fa;
  border-radius: 8px;
}
</style>
