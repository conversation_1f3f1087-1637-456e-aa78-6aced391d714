<template>
  <el-dialog
      :title="!dataForm.id ? '新增课程' : '修改课程'"
      :visible.sync="visible"
      width="65%"
      :close-on-click-modal="false"
      append-to-body
  >
    <el-form
        ref="dataForm"
        :model="dataForm"
        :rules="dataRule"
        label-width="120px"
        @keyup.enter.native="dataFormSubmit"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="课程名称" prop="courseName">
            <el-input
                v-model="dataForm.courseName"
                placeholder="请输入课程名称"
                clearable
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="课程分类" prop="courseCategory">
            <el-dict :code="'COURSE_CATEGORY'" v-model="dataForm.courseCategory" placeholder="全部"></el-dict>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="课程类型" prop="courseType">
            <el-dict :code="'COURSE_TYPE'" v-model="dataForm.courseType" placeholder="全部"
                     :clearable="false"  :disabled="dataForm.id"></el-dict>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20" v-if="dataForm.courseType === 'OFFLINE'">
        <el-col :span="8">
          <el-form-item label="课程日期" prop="courseDate">
            <el-date-picker
                v-model="dataForm.courseDate"
                type="date"
                placeholder="选择日期"
                value-format="yyyy-MM-dd"
                style="width: 100%"
                clearable
            />
          </el-form-item>
        </el-col>
        <el-col :span="3">
          <el-form-item label-width="0" prop="courseStartTime">
            <el-time-picker
                v-model="dataForm.courseStartTime"
                placeholder="开始时间"
                format="HH:mm"
                value-format="HH:mm"
                style="width: 100%"
                clearable
            />
          </el-form-item>
        </el-col>
        <el-col :span="0.5">
          <el-form-item label-width="0">
            至
          </el-form-item>
        </el-col>
        <el-col :span="3">
          <el-form-item label-width="0" prop="courseEndTime">
            <el-time-picker
                v-model="dataForm.courseEndTime"
                placeholder="结束时间"
                format="HH:mm"
                value-format="HH:mm"
                style="width: 100%"
                clearable
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20" v-if="dataForm.courseType === 'ONLINE'">
        <el-col :span="8">
          <el-form-item label="线上截止时间" prop="onlineCourseDeadline">
            <el-date-picker
                v-model="dataForm.onlineCourseDeadline"
                type="datetime"
                placeholder="选择截止时间(仅线上课程)"
                format="yyyy-MM-dd HH:mm"
                value-format="yyyy-MM-dd HH:mm"
                style="width: 100%"
                clearable
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label-width="0" prop="longTermValid">
            <el-checkbox v-model="dataForm.longTermValid">
              长期有效
            </el-checkbox>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="6">
          <el-form-item label="课程学分" prop="courseCredits">
            <el-input-number
                v-model="dataForm.courseCredits"
                style="width: 100%"
                :min="0"
                :max="100"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="适用学分区间" prop="applicableCreditRange">
            <el-dict :code="'COURSE_CREDIT_RANGE'" v-model="dataForm.applicableCreditRange"
                     placeholder="全部"></el-dict>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="6">
          <el-form-item label="报名人数控制" prop="registrationLimit">
            <el-input-number
                v-model="dataForm.registrationLimit"
                style="width: 100%"
                :min="0"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20" v-if="dataForm.courseType === 'OFFLINE'">
        <el-col :span="24">
          <el-form-item label="地址" prop="address">
            <el-input
                v-model="dataForm.address"
                placeholder="请通过地图选择地址"
                @clear="addressClear"
                clearable>
              <template slot="append">
                <i class="el-icon-location-outline" style="cursor: pointer;" @click="showMap"></i>
              </template>
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="联系人" prop="contactPerson">
            <el-input
                v-model="dataForm.contactPerson"
                placeholder="请输入联系人姓名"
                clearable
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="联系电话" prop="contactPhone">
            <el-input
                v-model="dataForm.contactPhone"
                placeholder="请输入手机号或座机号"
                clearable
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="简介" prop="description">
            <el-input
                type="textarea"
                v-model="dataForm.description"
                placeholder="请输入课程简介"
                rows="5"
                maxlength="1000"
                show-word-limit
            />
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 课程图片 & 顺序 -->
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="图片" prop="image">
            <el-upload
                class="avatar-uploader"
                :action="uploadUrl"
                :headers="myHeaders"
                :show-file-list="false"
                :data="uploadData"
                :on-success="handleImageSuccess"
                :before-upload="beforeImageUpload"
            >
              <img v-if="dataForm.image" :src="$http.adornAttachmentUrl(dataForm.image)" class="avatar">
              <i v-else class="el-icon-plus avatar-uploader-icon"/>
              <div slot="tip" class="el-upload__tip" style="color: red">上传一张预览图，建议 500x300像所或5:3比例图片，大小不超过2MB</div>
            </el-upload>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="6">
          <el-form-item label="展示顺序" prop="orderNum">
            <el-input-number
                v-model="dataForm.orderNum"
                style="width: 100%"
                :min="0"
                :max="9999"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <!-- 底部按钮 -->
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取 消</el-button>
      <el-button type="primary" :disabled="submitLoading" @click="dataFormSubmit">
        确 定
      </el-button>
    </span>

    <!-- 地图弹窗 -->
    <el-dialog
        class="map-dialog"
        title="地图"
        width="80%"
        :visible.sync="mapVisible"
        :close-on-click-modal="false"
        :append-to-body="true"
    >
      <!-- 这里示例使用自定义 AMapInfo 组件，如不需要可自行移除 -->
      <AMapInfo
          :mapVisible.sync="mapVisible"
          :address.sync="dataForm.address"
          :latitude.sync="dataForm.latitude"
          :longitude.sync="dataForm.longitude"
          :radius="500"
      />
    </el-dialog>
  </el-dialog>
</template>

<script>
import Vue from 'vue'
import {isMobile, is8lPhone} from '@/utils/validate' // 假设有相关校验方法
// 若使用自定义地图组件，可自行导入
import AMapInfo from '@/components/map/a-map-info'
import _ from "lodash";

export default {
  name: 'CourseForm',
  components: {
    AMapInfo
  },
  data() {
    const validateAddress = (rule, value, callback) => {
      if (!this.dataForm.latitude || !this.dataForm.longitude) {
        callback(new Error('请通过地图选择地址'))
      } else {
        callback()
      }
    }
    return {
      visible: false,
      mapVisible: false,
      submitLoading: false,
      serverCode: 'LocalServer',
      // 上传相关配置示例
      uploadUrl: this.$http.adornUrl('/admin/oss/upload'), // 实际请根据后端接口调整
      myHeaders: {Authorization: Vue.cookie.get('Authorization')},
      uploadData: {serverCode: 'LocalServer', media: false},

      dataForm: {
        id: null, // 判断是新增还是修改
        courseName: '',
        courseCategory: '',
        courseType: 'OFFLINE',
        courseDate: null,
        courseStartTime: null,
        courseEndTime: null,
        longTermValid: false,
        onlineCourseDeadline: null,
        courseCredits: null,
        applicableCreditRange: '',
        registrationLimit: null,
        address: '',
        longitude: null,
        latitude: null,
        contactPerson: '',
        contactPhone: '',
        description: '',
        image: '',
        orderNum: 0
      },
      initForm: {
        id: null, // 判断是新增还是修改
        courseName: '',
        courseCategory: '',
        courseType: 'OFFLINE',
        courseDate: null,
        courseStartTime: null,
        courseEndTime: null,
        longTermValid: false,
        onlineCourseDeadline: null,
        courseCredits: null,
        applicableCreditRange: '',
        registrationLimit: null,
        address: '',
        longitude: null,
        latitude: null,
        contactPerson: '',
        contactPhone: '',
        description: '',
        image: '',
        orderNum: 0
      },
      dataRule: {
        courseName: [
          {required: true, message: '请输入课程名称', trigger: 'blur'}
        ],
        applicableCreditRange: [
          {required: true, message: '请输入课程名称', trigger: 'blur'}
        ],
        courseCategory: [
          {required: true, message: '请选择课程分类', trigger: 'change'}
        ],
        courseType: [
          {required: true, message: '请选择课程类型', trigger: 'change'}
        ],
        courseDate: [
          {required: true, message: '请选择课程日期', trigger: 'change'}
        ],
        courseStartTime: [
          {required: true, message: '请选择开始时间', trigger: 'change'}
        ],
        courseEndTime: [
          {required: true, message: '请选择结束时间', trigger: 'change'}
        ],
        onlineCourseDeadline: [
          {required: true, message: '请选择线上截止时间', trigger: 'change'}
        ],
        courseCredits: [
          {required: true, message: '请输入课程学分', trigger: 'change'}
        ],
        registrationLimit: [
          {required: true, message: '请输入报名人数限制', trigger: 'change'}
        ],
        address: [
          {required: true, message: '请选择课程类型', trigger: 'change'},
          {validator: validateAddress, trigger: 'change'}
        ],
        description: [
          {required: true, message: '请输入课程简介', trigger: 'blur'}
        ],
        image: [
          {required: true, message: '请上传课程图片', trigger: 'change'}
        ]
      }
    }
  },
  methods: {
    // 外部调用时，可以先设置 dataForm.id（如果是编辑），然后 visible = true
    openDialog(id) {
      if (id) {
        // 编辑场景：拉取后端数据，填充 dataForm
        this.fetchDetail(id)
      } else {
        // 新增场景：重置表单
        this.resetForm()
      }
      this.visible = true
    },
    resetForm() {
      this.$refs.dataForm && this.$refs.dataForm.resetFields()
      this.dataForm = {
        id: null,
        courseName: '',
        courseCategory: '',
        courseType: '',
        courseDate: null,
        courseStartTime: null,
        courseEndTime: null,
        longTermValid: false,
        onlineCourseDeadline: null,
        courseCredits: null,
        applicableCreditRange: '',
        registrationLimit: null,
        address: '',
        longitude: null,
        latitude: null,
        contactPerson: '',
        contactPhone: '',
        description: '',
        image: '',
        orderNum: 0
      }
    },
    async init(id) {
      this.dataForm = _.cloneDeep(this.initForm)
      this.dataForm.id = id || null
      this.visible = true
      await this.getInitData()
    },
    async getInitData() {
      this.$nextTick(() => {
        this.$refs['dataForm']?.resetFields()
        this.dataForm.updateDate = null
        this.dataForm.updater = null
        this.dataForm.createDate = null
        this.dataForm.creator = null
        if (this.dataForm.id) {
          this.$http({
            url: this.$http.adornUrl(`/admin/manager/capability-upgrading/course/detail`),
            method: 'get',
            params: this.$http.adornParams({id: this.dataForm.id})
          }).then(({data}) => {
            if (data && data.code === 0) {
              this.dataForm = data.obj
            }
          })
        }
      })
    },
    fetchDetail(id) {
      // TODO: 请求后端接口获取详情
      // this.$http.get(`/api/course/detail?id=${id}`).then(...)
      // 拿到数据后赋值给 dataForm
    },
    dataFormSubmit() {
      console.log(this.dataForm)
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.submitLoading = true
          const url = !this.dataForm.id? '/admin/manager/capability-upgrading/course/save': '/admin/manager/capability-upgrading/course/edit'
          this.$http({
            url: this.$http.adornUrl(url),
            method: 'post',
            data: this.$http.adornData(this.dataForm)
          }).then(({data}) => {
            if (data && data.code === 0) {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 1500,
                onClose: () => {
                  this.submitLoading = false
                  this.visible = false
                  this.$emit('refreshDataList')
                }
              })
            } else if (data && data.code === 303) {
              this.submitLoading = false
              for (let it of data.obj) {
                this[`${it.field}Error`] = it.message
              }
            } else {
              this.submitLoading = false
              this.$message.error(data.msg)
            }
          })
        }
      })
    },
    // 上传图片前校验
    beforeImageUpload(file) {
      const isImage =
          file.type === 'image/jpeg' ||
          file.type === 'image/png' ||
          file.type === 'image/bmp'
      const isLt2M = file.size / 1024 / 1024 < 2
      if (!isImage) {
        this.$message.error('上传图片只能是 JPG/PNG/BMP 格式!')
      }
      if (!isLt2M) {
        this.$message.error('上传图片大小不能超过 2MB!')
      }
      return isImage && isLt2M
    },
    // 上传成功
    handleImageSuccess(res, file) {
      if (res.success) {
        this.dataForm.image = res.obj.path
        this.$message.success('上传成功')
      } else {
        this.$message.error('上传失败')
      }
    },
    // 点击“地图定位”按钮
    showMap() {
      this.mapVisible = true
    },
    // 清空地址和经纬度
    addressClear() {
      this.dataForm.address = ''
      this.dataForm.longitude = null
      this.dataForm.latitude = null
    },
    beforeAvatarUpload: function (file) {
      let isAccept = ['image/jpg', 'image/jpeg', 'image/png', 'image/bmp'].indexOf(file.type) !== -1
      let isLt2M = file.size / 1024 / 1024 < 2

      if (!isAccept) {
        this.$message.error('上传图片只能是图片!')
      }
      if (!isLt2M) {
        this.$message.error('上传文件大小不能超过 2MB!')
      }
      return isAccept && isLt2M
    }
  }
}
</script>

<style scoped>

</style>
