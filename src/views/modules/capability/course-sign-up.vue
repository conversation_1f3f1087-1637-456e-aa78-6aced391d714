<template>
  <div class="mod-config">
    <el-form
        :inline="true"
        ref="dataForm"
        :model="dataForm"
        @keyup.enter.native="getDataList()"
    >
      <el-form-item label="关键字" prop="keyword">
        <el-input v-model="dataForm.keyword" placeholder="请输入志愿者姓名/手机号码/身份证号码搜索"
                  style="min-width: 330px" clearable></el-input>
      </el-form-item>
      <el-form-item label="课程名称" prop="courseName">
        <el-input v-model="dataForm.courseName" placeholder="请输入课程名称" clearable></el-input>
      </el-form-item>
      <el-form-item label="报名时间">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-date-picker
                v-model="dataForm.signUpStartTime"
                type="datetime"
                placeholder="开始时间"
                format="yyyy-MM-dd HH:mm"
                value-format="yyyy-MM-dd HH:mm:ss"
                clearable
            />
          </el-col>
          <el-col :span="12">
            <el-date-picker
                v-model="dataForm.signUpEndTime"
                type="datetime"
                placeholder="结束时间"
                format="yyyy-MM-dd HH:mm"
                value-format="yyyy-MM-dd HH:mm:ss"
                clearable
            />
          </el-col>
        </el-row>
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-search" type="primary" @click="getDataList()">查询</el-button>
      </el-form-item>
    </el-form>
    <div class="f-s-c" style="padding: 15px 0;display: flex;justify-content:flex-end;align-items: center ">
      <div>
        <el-button type="success" @click="exportHandle()">导出</el-button>
      </div>
    </div>
    <el-table
        :data="dataList"
        v-loading="dataListLoading"
        border>
      <el-table-column prop="name" label="志愿者姓名" align="center"></el-table-column>
      <el-table-column prop="phone" label="手机号码" align="center"></el-table-column>
      <el-table-column prop="certificateId" label="身份证号码" align="center"></el-table-column>
      <el-table-column prop="courseName" label="课程名称" align="center"></el-table-column>
      <el-table-column prop="courseTypeName" label="课程类型" align="center"></el-table-column>
      <el-table-column prop="signUpAt" label="报名时间" align="center"></el-table-column>

      <el-table-column label="操作" align="center">
        <template slot-scope="scope">
          <el-button type="text" size="small" @click="cancelHandle(scope.row)">取消报名</el-button>
        </template>
      </el-table-column>
    </el-table>

    <el-pagination
        @size-change="sizeChangeHandle"
        @current-change="currentChangeHandle"
        :current-page="pageIndex"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pageSize"
        :total="totalPage"
        layout="total, sizes, prev, pager, next, jumper"
    ></el-pagination>
  </div>
</template>

<script>
import listMixin from '@/mixins/list-mixins'; // import the mixin

export default {
  mixins: [listMixin], // using the mixin here
  data() {
    return {
      mixinOptions: {
        dataUrl: '/admin/manager/capability-upgrading/course/sign-up/page',
        exportUrl: '/admin/manager/capability-upgrading/course/sign-up/export'
      },
      dataForm: {
        courseId: null,
        keyword: '',
        courseName: '',
        signUpStartTime: null,
        signUpEndTime: null,
      },
    };
  },
  activated() {
    this.getDataList()
  },
  methods: {
    async cancelHandle(row) {
      this.$confirm('确定要取消报名该课程吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const url = '/admin/manager/capability-upgrading/course/sign-up/cancel';
        const params = {courseId: row.courseId, userId: row.userId};
        this.$http({
          url: this.$http.adornUrl(url),
          method: 'delete',
          params: this.$http.adornParams(params)
        }).then(({data}) => {
          if (data.code === 0) {
            this.$message.success('操作成功');
            this.getDataList();
          } else {
            this.$message.warning(data.msg);
          }
        })
      }).catch(() => {
        // 如果用户取消操作，不做任何事
        this.$message.info('操作已取消');
      });
    },
    handleClose() {
      this.$refs['dataForm']?.resetFields();
    },
    init(id) {
      this.dataForm.courseId = id;
      this.getDataList();
    }
  },
};
</script>
<style scoped>

</style>
