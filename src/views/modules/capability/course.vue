<template>
  <div class="mod-config">
    <el-form :inline="true" ref="dataForm" :model="dataForm" @keyup.enter.native="getDataList()">
      <el-form-item label="课程名称" prop="courseName" style="padding-right: 40px">
        <el-input v-model="dataForm.courseName" placeholder="请输入课程名称" clearable style="width: 120%"></el-input>
      </el-form-item>
      <el-form-item label="课程类型" prop="courseType">
        <el-dict :code="'COURSE_TYPE'" v-model="dataForm.courseType" placeholder="全部"></el-dict>
      </el-form-item>
      <el-form-item label="状态" prop="outerDisplay">
        <el-select v-model="dataForm.outerDisplay" placeholder="请选择">
          <el-option
              v-for="item in outerDisplayValues"
              :key="item.value"
              :label="item.label"
              :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-search" type="warning" @click="getDataList()">查询</el-button>
        <el-button class="el-icon-refresh-left" @click="resetForm()">重置</el-button>
      </el-form-item>
    </el-form>
    <div class="f-s-c" style="padding: 15px 0;display: flex;justify-content:flex-end;align-items: center ">
      <div>
        <el-button v-if="isAuth('capability:course:create')" type="primary" @click="addOrUpdateHandle()">新增
        </el-button>
      </div>
    </div>
    <el-table
        :data="dataList"
        border
        v-loading="dataListLoading"
        @selection-change="selectionChangeHandle"
        style="width: 100%;">
      <el-table-column
          prop="courseName"
          header-align="center"
          align="center"
          min-width="150px"
          label="课程名称">
      </el-table-column>
      <el-table-column
          prop="courseTypeName"
          header-align="center"
          align="center"
          label="课程类型">
      </el-table-column>
      <el-table-column
          prop="name"
          header-align="center"
          min-width="190px"
          align="center"
          label="课程时间">
        <template slot-scope="scope">
          <div v-if="scope.row.courseType ==='OFFLINE'">
            {{ scope.row.courseDate }} {{ scope.row.courseStartTime }} - {{ scope.row.courseEndTime }}
          </div>
          <div v-else>
            {{ scope.row.onlineCourseDeadline }}
          </div>
        </template>
      </el-table-column>
      <el-table-column
          prop="courseCredits"
          header-align="center"
          align="center"
          label="课程学分">
      </el-table-column>
      <el-table-column
          prop="orderNum"
          header-align="center"
          align="center"
          label="顺序">
      </el-table-column>
      <el-table-column
          prop="createDate"
          header-align="center"
          align="center"
          min-width="120px"
          label="发布时间">
      </el-table-column>
      <el-table-column
          prop="publishOrgName"
          header-align="center"
          align="center"
          min-width="120px"
          label="发布组织">
      </el-table-column>
      <el-table-column
          prop="outerDisplay"
          header-align="center"
          align="center"
          min-width="100px"
          label="状态">
        <template slot-scope="scope">
          <el-tag v-if="!scope.row.outerDisplay" size="small" type="danger">已下架</el-tag>
          <el-tag v-else size="small" type="success">上架中</el-tag>
        </template>
      </el-table-column>
      <el-table-column
          fixed="right"
          header-align="center"
          align="center"
          width="250"
          label="操作">
        <template slot-scope="scope">
          <el-button type="text" size="small" @click="switchOuterDisplayStatus(scope.row.id)">
            {{ scope.row.outerDisplay ? '下架' : '上架' }}
          </el-button>
          <el-button type="text" size="small" @click="addOrUpdateHandle(scope.row.id)">
            编辑
          </el-button>
          <el-button type="text" size="small" style="color: #f56c6c;" @click="deleteHandle(scope.row.id)">
            删除
          </el-button>
          <el-button v-if="scope.row.courseType === 'ONLINE'" type="text" size="small"
                     @click="studyHandle(scope.row.id)">
            学习管理
          </el-button>
          <el-button v-if="scope.row.courseType === 'OFFLINE'" type="text" size="small"
                     @click="signUpHandle(scope.row.id)">
            报名管理
          </el-button>
          <el-button v-if="scope.row.courseType === 'OFFLINE'" type="text" size="small"
                     @click="signInHandle(scope.row.id)">
            签到管理
          </el-button>
          <el-button v-if="scope.row.courseType === 'OFFLINE'" type="text" size="small"
                     @click="signInQrcodeHandle(scope.row.id)">
            签到二维码
          </el-button>
          <el-button type="text" size="small" @click="switchTopStatus(scope.row.id)">
            {{ scope.row.top ? '取消置顶' : '置顶' }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
        @size-change="sizeChangeHandle"
        @current-change="currentChangeHandle"
        :current-page="pageIndex"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pageSize"
        :total="totalPage"
        layout="total, sizes, prev, pager, next, jumper">
    </el-pagination>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"></add-or-update>
    <sign-up-dialog
        v-if="signUpVisible"
        ref="signUpDialog"
    />
    <!-- 新增签到管理弹窗 -->
    <sign-in-dialog
        v-if="signInVisible"
        ref="signInDialog"
    />
    <sign-in-qrcode-dialog
        v-if="signInQrcodeVisible"
        ref="signInQrcodeDialog"
    />

  </div>
</template>

<script>

import listMixin from '@/mixins/list-mixins'
import moment from "moment";
import {isAuth} from "@/utils";
import AddOrUpdate from "@/views/modules/capability/course-add-or-update.vue";
import SignUpDialog from "@/views/modules/capability/course-sign-up-list-dialog.vue";
import SignInDialog from "@/views/modules/capability/course-sign-in-list-dialog.vue";
import SignInQrcodeDialog from '@/views/modules/capability/course-sign-in-qrcode-dialog.vue'

export default {
  mixins: [listMixin],
  data() {
    return {
      mixinOptions: {
        dataUrl: '/admin/manager/capability-upgrading/course/page',
        deleteUrl: '/admin/manager/capability-upgrading/course/delete'
      },
      signInQrcodeVisible: false,
      fullscreenLoading: false,
      dataForm: {
        courseName: '',
        courseType: '',
        outerDisplay: null
      },
      signUpVisible: false,  // 控制报名管理弹窗显示
      signInVisible: false,  // 控制签到管理弹窗显示
      outerDisplayValues: [{
        value: true,
        label: '上架中'
      }, {
        value: false,
        label: '已下架'
      }],
    }
  },
  activated() {
    this.getDataList()
  },

  components: {
    AddOrUpdate,
    SignUpDialog,          // 新增报名管理弹窗组件
    SignInDialog,          // 新增签到管理弹窗组件
    SignInQrcodeDialog
  },
  methods: {
    isAuth,
    resetForm() {
      this.$refs['dataForm']?.resetFields()
      this.getDataList()
    },
    // 取消群众报名
    cancelApplyHandle(id, timePeriodId) {
      var ids = id ? [id] : this.dataListSelections.map(item => {
        return item.id
      })
      this.$confirm(`确定进取消该群众报名?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$http({
          url: this.$http.adornUrl('/api/activity/cancelApply'),
          method: 'get',
          params: this.$http.adornParams({
            'applyPublicIds': ids.join(','),
            'timePeriodId': timePeriodId
          })
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.$message({
              message: '操作成功',
              type: 'success',
              duration: 1500,
              onClose: () => {
                this.getDataList()
              }
            })
          } else {
            this.$message.error(data.msg)
          }
        })
      })
    },
    // 导出
    exportServiceLongPublic() {
      this.fullscreenLoading = true;
      this.$http({
        url: this.$http.adornUrl('/admin/zyz/activity/apply/public/exportForPublic'),
        method: 'post',
        responseType: 'arraybuffer',
        data: this.$http.adornData({
          'courseName': this.dataForm.courseName,
          'status': this.dataForm.status
        })
      }).then(({data}) => {
        if (data.code && data.code !== 0) {
          this.$message.error('导出失败')
        } else {
          this.fullscreenLoading = false;
          let blob = new Blob([data], {type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8'})
          let objectUrl = URL.createObjectURL(blob)
          let a = document.createElement('a')
          // window.location.href = objectUrl
          a.href = objectUrl
          a.download = '报名管理列表明细.xlsx'
          a.click()
          URL.revokeObjectURL(objectUrl)
          this.$message({
            type: 'success',
            message: '导出成功'
          })
        }
      })
    },
    querySuccessHandle(data) {
      data.records.forEach(it => {
        it.timePeriodRange = it.startTime && it.endTime ?
            moment(it.startTime).format('YYYY-MM-DD HH:mm') + '-' + moment(it.endTime).format('HH:mm') : ''
      })
      this.dataList = data.records
      this.totalPage = data.total
    },
    addOrUpdateHandle(id) {
      this.addOrUpdateVisible = true
      this.$nextTick(() => {
        this.$refs.addOrUpdate.init(id)
      })
    },
    async switchOuterDisplayStatus(id) {
      this.$confirm('确定要切换该课程的状态吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const url = '/admin/manager/capability-upgrading/course/outer-display/switch';
        const params = {id: id};
        this.$http({
          url: this.$http.adornUrl(url),
          method: 'post',
          params: this.$http.adornParams(params)
        }).then(({data}) => {
          if (data.code === 0) {
            this.$message.success('操作成功');
            this.getDataList();
          } else {
            this.$message.error(data.msg);
          }
        })
      }).catch(() => {
        // 如果用户取消操作，不做任何事
        this.$message.info('操作已取消');
      });
    },
    async deleteHandle(id) {
      this.$confirm('确定要删除该课程吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const url = '/admin/manager/capability-upgrading/course/delete';
        const params = {ids: id};
        this.$http({
          url: this.$http.adornUrl(url),
          method: 'delete',
          params: this.$http.adornParams(params)
        }).then(({data}) => {
          if (data.code === 0) {
            this.$message.success('操作成功');
            this.getDataList();
          } else {
            this.$message.warning(data.msg);
          }
        })
      }).catch(() => {
        // 如果用户取消操作，不做任何事
        this.$message.info('操作已取消');
      });
    },
    async switchTopStatus(id) {
      this.$confirm('确定要置顶该课程吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const url = '/admin/manager/capability-upgrading/course/top/switch';
        const params = {id: id};
        this.$http({
          url: this.$http.adornUrl(url),
          method: 'post',
          params: this.$http.adornParams(params)
        }).then(({data}) => {
          if (data.code === 0) {
            this.$message.success('操作成功');
            this.getDataList();
          } else {
            this.$message.warning(data.msg);
          }
        })
      }).catch(() => {
        // 如果用户取消操作，不做任何事
        this.$message.info('操作已取消');
      });
    },
    async studyHandle(id) {
      this.$message.success('敬请期待');
    },
    // 处理报名管理弹窗
    signUpHandle(id) {
      this.signUpVisible = true;
      this.$nextTick(() => {
        this.$refs.signUpDialog.init(id)
      });
    },
    // 处理签到管理弹窗
    signInHandle(id) {
      this.signInVisible = true;
      this.$nextTick(() => {
        this.$refs.signInDialog.init(id)
      });
    },
    signInQrcodeHandle(id) {
      this.signInQrcodeVisible = true
      this.$nextTick(() => {
        this.$refs.signInQrcodeDialog.init(id)
      })
    }
  }
}
</script>
