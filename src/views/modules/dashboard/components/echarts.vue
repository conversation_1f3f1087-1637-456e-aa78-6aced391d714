<template>
  <div class="echarts-box">
    <slot></slot>
    <div class="dashboard-title" style="margin-left: 20px" v-if="chartsShow">30天活动发布数、需求、资源数</div>
    <div class="echarts-data">
      <div class="echartsMain" ref="echartsMain" v-if="chartsShow">
      </div>
      <div class="component-loading" v-else>
        <span v-for="(item, index) in [0, 1, 2, 3, 4]"></span>
      </div>
      <div class="lable" v-if="thisMonthResReqActSumResult">
        <div class="lable-item" style="--color: #FB6A6A" v-if="thisMonthResReqActSumResult.actNum !== undefined && thisMonthResReqActSumResult.actNum !== null">
          <div class="lable-item-title">本月累计发布活动</div>
          <div class="lable-item-num">{{ thisMonthResReqActSumResult.actNum }}</div>
        </div>
        <div class="lable-item" style="--color: #E4B61E" v-if="thisMonthResReqActSumResult.resNum !== undefined && thisMonthResReqActSumResult.resNum !== null">
          <div class="lable-item-title">本月累计发布资源</div>
          <div class="lable-item-num">{{ thisMonthResReqActSumResult.resNum }}</div>
        </div>
        <div class="lable-item" style="--color: #FFA25B" v-if="thisMonthResReqActSumResult.reqNum !== undefined && thisMonthResReqActSumResult.reqNum !== null">
          <div class="lable-item-title">本月累计发布需求</div>
          <div class="lable-item-num">{{ thisMonthResReqActSumResult.reqNum }}</div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import * as echarts from 'echarts'
export default {
  props: {
    last30DaysBarSumResult: Array,
    thisMonthResReqActSumResult: Object
  },
  data () {
    return {
      chartsShow: false,
      chartsOption: null,
      charts: null
    }
  },
  watch: {
    last30DaysBarSumResult: {
      handler(newValue, oldValue) {
        if (newValue) {
          this.chartsOption = {
            tooltip: {trigger: 'axis'},
            grid: {left: '3%', right: '4%', bottom: '3%', containLabel: true},
            xAxis: {type: 'category', data: newValue.map(item => {return item.date})},
            yAxis: {type: 'value', minInterval: 1, splitLine: {show: false}, axisTick: {show: false}},
            series: [
              {name: '需求', type: 'bar', stack: 'total', data: newValue.map(item => {return item.sum.reqNum}), itemStyle: {color: '#FFA25B'}},
              {name: '资源', type: 'bar', stack: 'total', itemStyle: {normal: {color: '#E4B61E'}}, data: newValue.map(item => {return item.sum.resNum})},
              {name: '活动', type: 'bar', stack: 'total', itemStyle: {normal: {color: '#FB6A6A'}}, data: newValue.map(item => {return item.sum.actNum})}
            ]
          }
          this.chartsShow = true
        }
      },
      deep: true,
      immediate: true
    },
    chartsShow: {
      handler(newValue, oldValue) {
        if (newValue && newValue === true) {
          this.$nextTick(() => {
            this.charts = echarts.init(this.$refs.echartsMain)
            this.charts.setOption(this.chartsOption)
          })
        }
      },
      deep: true,
      immediate: true
    }
  }
}
</script>
<style scoped lang="scss">
.echarts-box{
  margin: 24px 0 0 0px;
  padding: 24px 0 24px 0px;
  border: 1px solid #eae9e9;
  box-shadow: 3px 3px 5px 0px rgba(0,0,0,0.2);
  .echarts-data{
    display: flex;
    justify-content: space-between;
    .echartsMain{
      width: 85%;
      height: 200px;
    }
    .lable{
      width: 15%;
      display: flex;
      flex-direction: column;
      justify-content: space-around;
      .lable-item-title{
        color: #465365;
        font-size: 14px;
        &::before{
          content: '';
          display: inline-block;
          width: 3px;
          height: 24px;
          border-radius: 4px;
          background: var(--color);
          transform: translateY(35%);
          margin-right: 20px;
        }
      }
      .lable-item-num{
        padding-left: 25px;
        color: var(--color);
        font-size: 30px;
        font-weight: bold;
      }
    }
  }
}
</style>
