<template>
  <div class="button-links-container">
    <el-button @click="tolink('activity-activity-time-show-list')">活动公示图片管理</el-button>
    <el-button @click="tolink('activity-activity-time-show-list')">活动图片上下架管理</el-button>
  </div>
</template>

<script>
export default {
  methods: {
    tolink (path) {
      this.$router.push({
        path
      })
    }
  }
}
</script>

<style scoped lang="scss">
.button-links-container {
  margin: 24px 0 0 0px;
  padding: 24px 24px 24px 24px;
  display: flex;
  justify-content: flex-start;
  gap: 20px;
  border: 1px solid #eae9e9;
  box-shadow: 3px 3px 5px 0px rgba(0,0,0,0.2);
}
</style> 