<template>
  <div :style="!tagSumResult ? {'border': '1px solid #eae9e9', 'box-shadow': '3px 3px 5px 0px rgba(0,0,0,0.2)'} : {}">
    <div class="grid-box" v-if="tagSumResult">
      <div @click="tolink('activity-activity-audit-list')" class="grid-item" style="--bgColor: #FF9854; width: 23%" v-if="tagSumResult.actWaitAuditNum !== undefined && tagSumResult.actWaitAuditNum !== null">
        <img src="~@/assets/img/workPlatform/icon_1.png" alt="">
        <div class="grid-item-title">活动待审核</div>
        <div class="grid-item-num">{{ tagSumResult.actWaitAuditNum }}</div>
      </div>
      <div @click="tolink('activity-activity-join-audit')" class="grid-item" style="--bgColor: #FF6D76; width: 23%" v-if="tagSumResult.actApplyWaitAuditNum !== undefined && tagSumResult.actApplyWaitAuditNum !== null">
        <img src="~@/assets/img/workPlatform/icon_2.png" alt="">
        <div class="grid-item-title">活动报名待审核</div>
        <div class="grid-item-num">{{ tagSumResult.actApplyWaitAuditNum }}</div>
      </div>
      <div @click="tolink('requirement-requirement-audit-list')" class="grid-item" style="--bgColor: #3592F7; width: 23%" v-if="tagSumResult.reqWaitAuditNum !== undefined && tagSumResult.reqWaitAuditNum !== null">
        <img src="~@/assets/img/workPlatform/icon_3.png" alt="">
        <div class="grid-item-title">需求待审核</div>
        <div class="grid-item-num">{{ tagSumResult.reqWaitAuditNum }}</div>
      </div>
      <div @click="tolink('resource-resource-audit-list')" class="grid-item" style="--bgColor: #00BCCA; width: 23%" v-if="tagSumResult.resWaitAuditNum !== undefined && tagSumResult.resWaitAuditNum !== null">
        <img src="~@/assets/img/workPlatform/icon_4.png" alt="">
        <div class="grid-item-title">资源待审核</div>
        <div class="grid-item-num">{{ tagSumResult.resWaitAuditNum }}</div>
      </div>
<!--      <div @click="tolink('resource-resource-appointment-audit-list')" class="grid-item" style="&#45;&#45;bgColor: #7D74F6" v-if="tagSumResult.resDockWaitAuditNum !== undefined && tagSumResult.resDockWaitAuditNum !== null">-->
<!--        <img src="~@/assets/img/workPlatform/icon_5.png" alt="">-->
<!--        <div class="grid-item-title">资源预约待审核</div>-->
<!--        <div class="grid-item-num">{{ tagSumResult.resDockWaitAuditNum }}</div>-->
<!--      </div>-->
      <div @click="toNewsAuditLink('portal_website-news')" class="grid-item" style="--bgColor: #17CFB6; width: 23%" v-if="tagSumResult.newsWaitAuditNum !== undefined && tagSumResult.newsWaitAuditNum !== null">
        <img src="~@/assets/img/workPlatform/icon_6.png" alt="">
        <div class="grid-item-title">新闻待审核</div>
        <div class="grid-item-num">{{ tagSumResult.newsWaitAuditNum }}</div>
      </div>
      <div @click="tolink('zyz-team-audit')" class="grid-item" style="--bgColor: #ADCA18; width: 23%" v-if="tagSumResult.teamRegWaitAuditNum !== undefined && tagSumResult.teamRegWaitAuditNum !== null">
        <img src="~@/assets/img/workPlatform/icon_7.png" alt="">
        <div class="grid-item-title">团队待审核</div>
        <div class="grid-item-num">{{ tagSumResult.teamRegWaitAuditNum }}</div>
      </div>
      <div @click="tolink('zyz-volunteer-team-audit')" class="grid-item" style="--bgColor: #7BD548; width: 23%" v-if="tagSumResult.joinTeamWaitAuditNum !== undefined && tagSumResult.joinTeamWaitAuditNum !== null">
        <img src="~@/assets/img/workPlatform/icon_8.png" alt="">
        <div class="grid-item-title">团队加入待审核</div>
        <div class="grid-item-num">{{ tagSumResult.joinTeamWaitAuditNum }}</div>
      </div>
      <div @click="tolink('project-project-audit')" class="grid-item" style="--bgColor: #7D74F6; width: 23%" v-if="tagSumResult.projectApplyWaitAuditNum !== undefined && tagSumResult.projectApplyWaitAuditNum !== null">
        <img src="~@/assets/img/workPlatform/icon_9.png" alt="">
        <div class="grid-item-title">项目申报待审核</div>
        <div class="grid-item-num">{{ tagSumResult.projectApplyWaitAuditNum }}</div>
      </div>
      <div @click="tolink('project-project-finish-audit')" class="grid-item" style="--bgColor: #b99112; width: 23%" v-if="tagSumResult.projectSumWaitAuditNum !== undefined && tagSumResult.projectSumWaitAuditNum !== null">
        <img src="~@/assets/img/workPlatform/icon_10.png" alt="">
        <div class="grid-item-title">项目结项待审核</div>
        <div class="grid-item-num">{{ tagSumResult.projectSumWaitAuditNum }}</div>
      </div>
      <div @click="tolink('project-project-docking-audit')" class="grid-item" style="--bgColor: #9A67EA; width: 23%" v-if="tagSumResult.projectDockingWaitAuditNum !== undefined && tagSumResult.projectDockingWaitAuditNum !== null">
        <img src="~@/assets/img/workPlatform/icon_10.png" alt="">
        <div class="grid-item-title">项目对接待审核数</div>
        <div class="grid-item-num">{{ tagSumResult.projectDockingWaitAuditNum }}</div>
      </div>
      <div @click="tolink('activity-activity-time-show-audit-list')" class="grid-item" style="--bgColor: #FF7F27; width: 23%" v-if="tagSumResult.activityPhotoPublicWaitAuditNum !== undefined && tagSumResult.activityPhotoPublicWaitAuditNum !== null">
        <img src="~@/assets/img/workPlatform/icon_10.png" alt="">
        <div class="grid-item-title">活动图片公示审核</div>
        <div class="grid-item-num">{{ tagSumResult.activityPhotoPublicWaitAuditNum }}</div>
      </div>
      <div @click="tolink('unit-info/unit-audit')" class="grid-item" style="--bgColor: #3CB44B; width: 23%" v-if="tagSumResult.advancedUnitWaitAuditNum !== undefined && tagSumResult.advancedUnitWaitAuditNum !== null">
        <img src="~@/assets/img/workPlatform/icon_10.png" alt="">
        <div class="grid-item-title">文明单位注册审核</div>
        <div class="grid-item-num">{{ tagSumResult.advancedUnitWaitAuditNum }}</div>
      </div>
      <div class="grid-item-visible" style="width: 23%" v-if="isSubAssociationAdmin || isTeamAdmin">
      </div>
      <div class="grid-item-visible" style="width: 23%" v-if="isSubAssociationAdmin || isTeamAdmin">
      </div>
      <div class="grid-item-visible" style="width: 23%; height: 0;"></div>
      <div class="grid-item-visible" style="width: 23%; height: 0;"></div>
      <div class="grid-item-visible" style="width: 23%; height: 0;"></div>
    </div>
    <div class="component-loading" v-else>
      <span v-for="(item, index) in [0, 1, 2, 3, 4]"></span>
    </div>
  </div>

</template>
<script>
export default {
  props: {
    tagSumResult: Object,
    managerCapacity: String
  },
  data () {
    return {
      isAssociationAdmin: this.managerCapacity === 'ASSOCIATION_ADMIN',
      isSubAssociationAdmin: this.managerCapacity === 'SUB_ASSOCIATION_ADMIN',
      isCommunityAdmin: this.managerCapacity === 'COMMUNITY_ADMIN',
      isTeamAdmin: this.managerCapacity === 'TEAM_ADMIN'
    }
  },
  methods: {
    tolink (path) {
      this.$router.push({
        path
      })
    },
    toNewsAuditLink (path) {
      this.$router.push({
        path: path, query: {needAudit: true}
      })
    }
  }
}
</script>
<style scoped lang="scss">
.grid-box{
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: 20px;
  .grid-item{
    cursor: pointer;
    height: 130px;
    border-radius: 4px;
    background-image: url('data:image/png;base64,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');
    background-repeat: no-repeat;
    background-size: contain;
    background-position: 0 100%;
    background-color: var(--bgColor);
    position: relative;
    color: #fff;
    padding: 1% 0 1% 2%;
    transition: all 0.5s;
    &:hover{
      transform: scale(1.1);
    }
    img{
      position: absolute;
      top: 14px;
      right: 14px;
      width: 30px;
    }
    .grid-item-title{
      font-size: 18px;
      margin-bottom: 20px;
    }
    .grid-item-num{
      font-size: 56px;
    }
  }
}
</style>
