<template>
  <div class="coreList-box" v-if="coreIndicatorDataSumResult">
    <div class="dashboard-title">核心指标数据</div>
    <div :class="type === 'demo1' ? 'list demo1_list' : 'list demo2_list'">
      <div class="list-item" v-if="coreIndicatorDataSumResult.todayRegisterVolunteerNum !== undefined && coreIndicatorDataSumResult.todayRegisterVolunteerNum !== null">
        <div class="key">今日新增志愿者数</div>
        <div class="value">{{ coreIndicatorDataSumResult.todayRegisterVolunteerNum }}</div>
      </div>
      <div class="list-item" v-if="coreIndicatorDataSumResult.volunteerNum !== undefined && coreIndicatorDataSumResult.volunteerNum !== null">
        <div class="key">志愿者数</div>
        <div class="value">{{ coreIndicatorDataSumResult.volunteerNum }}</div>
      </div>
      <div class="list-item" v-if="coreIndicatorDataSumResult.volunteerActiveNum !== undefined && coreIndicatorDataSumResult.volunteerActiveNum !== null">
        <div class="key">志愿者活跃数</div>
        <div class="value">{{ coreIndicatorDataSumResult.volunteerActiveNum }}</div>
      </div>
      <div class="list-item" v-if="coreIndicatorDataSumResult.communityNum !== undefined && coreIndicatorDataSumResult.communityNum !== null">
        <div class="key">社区数</div>
        <div class="value">{{ coreIndicatorDataSumResult.communityNum }}</div>
      </div>
      <div class="list-item" v-if="coreIndicatorDataSumResult.teamNum !== undefined && coreIndicatorDataSumResult.teamNum !== null">
        <div class="key">团队数</div>
        <div class="value">{{ coreIndicatorDataSumResult.teamNum }}</div>
      </div>
      <div class="list-item" v-if="coreIndicatorDataSumResult.totalServiceTime !== undefined && coreIndicatorDataSumResult.totalServiceTime !== null">
        <div class="key">累计服务时长</div>
        <div class="value">{{ coreIndicatorDataSumResult.totalServiceTime }}</div>
      </div>
      <div class="list-item" v-if="coreIndicatorDataSumResult.thisMonthActNum !== undefined && coreIndicatorDataSumResult.thisMonthActNum !== null">
        <div class="key">本月活动次数</div>
        <div class="value">{{ coreIndicatorDataSumResult.thisMonthActNum }}</div>
      </div>
      <div class="list-item" v-if="coreIndicatorDataSumResult.totalActNum !== undefined && coreIndicatorDataSumResult.totalActNum !== null">
        <div class="key">累计活动次数</div>
        <div class="value">{{ coreIndicatorDataSumResult.totalActNum }}</div>
      </div>
      <div class="list-item" v-if="coreIndicatorDataSumResult.totalReqNum !== undefined && coreIndicatorDataSumResult.totalReqNum !== null">
        <div class="key">需求数</div>
        <div class="value">{{ coreIndicatorDataSumResult.totalReqNum }}</div>
      </div>
      <div class="list-item" v-if="coreIndicatorDataSumResult.totalResNum !== undefined && coreIndicatorDataSumResult.totalResNum !== null">
        <div class="key">资源数</div>
        <div class="value">{{ coreIndicatorDataSumResult.totalResNum }}</div>
      </div>
    </div>
  </div>
  <div class="component-loading" v-else>
    <span v-for="(item, index) in [0, 1, 2, 3, 4]"></span>
  </div>
</template>
<script>
export default {
  props: {
    coreIndicatorDataSumResult: Object,
    type: String
  }
}
</script>
<style scoped lang="scss">
.demo1_list {
  .list-item {
    width: 15% !important;
  }
  .key {
    font-size: 12px;
  }
}
.demo2_list:after{
  content: '';
  width: 49%;
}
.demo2_list .list-item{
  width: 15%;
}
.list{
  gap: 18px;
  display: flex;
  justify-content: space-between;
  align-content: space-around;
  flex-wrap: wrap;
  text-align: center;
  .key{
    color: #999;
  }
  .value{
    margin-top: 10px;
    font-size: 26px;
    font-weight: bold;
    color: #465365;
  }
}
</style>
