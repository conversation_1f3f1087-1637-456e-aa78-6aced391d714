<template>
  <div class="table-alone-box">
    <div class="table-box-item">
      <el-card class="table-sum-card" shadow="hover">
        <slot name="title"></slot>
        <CoreList :core-indicator-data-sum-result="coreIndicatorDataSumResult" :type="'demo2'"/>
      </el-card>
    </div>
  </div>
</template>
<script>
export default {
  props: {
    coreIndicatorDataSumResult: Object
  },
  data () {
    return {
    }
  },
  components: {
    CoreList: () => import('./CoreList.vue')
  }
}
</script>
<style scoped lang="scss">
.table-alone-box{
  display: flex;
  gap: 10px;
  padding: 24px 0 24px 0px;
  .table-box-item{
    width: 100%;
    border: 1px solid #eae9e9;
    box-shadow: 3px 3px 5px 0px rgba(0,0,0,0.2);
  }
}
</style>
