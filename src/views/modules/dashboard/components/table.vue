<template>
  <div class="table-box">
    <div class="table-box-item">
      <el-card class="table-sum-card" shadow="hover" v-if="subAssociationDataSumResult">
        <slot name="table1"></slot>
        <el-table
          :data="subAssociationDataSumResult"
          style="width: 100%">
          <el-table-column
            label="排名"
            width="50"
            type="index">
            <template slot-scope="scoped">
              <div class="sort">{{scoped.$index + 1}}</div>
            </template>
          </el-table-column>
          <el-table-column
            align="center"
            prop="subAssociationName"
            label="街道"
            width="220">
          </el-table-column>
          <el-table-column
            align="center"
            prop="volunteerNum"
            label="志愿者数">
          </el-table-column>
          <el-table-column
            align="center"
            width="100"
            prop="activeVolunteerNum"
            label="本月活跃数">
          </el-table-column>
          <el-table-column
            align="center"
            prop="teamNum"
            label="团队数">
          </el-table-column>
          <el-table-column
            align="center"
            prop="actNum"
            label="活动数">
          </el-table-column>
          <el-table-column
            align="center"
            width="100"
            prop="serviceTimeTotal"
            label="总服务时长">
          </el-table-column>
        </el-table>
      </el-card>
      <div class="component-loading" v-else>
        <span v-for="(item, index) in [0, 1, 2, 3, 4]"></span>
      </div>
    </div>
    <div class="table-box-item">
      <el-card class="table-sum-card" shadow="hover" v-if="topBelongFieldDataSumResult">
        <slot name="table2"></slot>
        <div class="table-box-item-main">
          <el-table
            :data="topBelongFieldDataSumResult.slice(0, 5)"
            style="width: 100%">
            <el-table-column
              label="排名"
              width="50">
              <template slot-scope="scoped">
                <div class="sort">{{ scoped.$index + 1}}</div>
              </template>
            </el-table-column>
            <el-table-column
              align="center"
              prop="topBelongField"
              label="所属领域">
            </el-table-column>
            <el-table-column
              align="center"
              prop="dataNum"
              label="数量"
              width="100">
            </el-table-column>
            <el-table-column
              align="center"
              prop="percentage"
              label="占比"
              width="100">
            </el-table-column>
          </el-table>
          <el-table
            :data="topBelongFieldDataSumResult.slice(5)"
            style="width: 100%">
            <el-table-column
              label="排名"
              width="50"
              type="index">
              <template slot-scope="scoped">
                <div class="sort">{{ scoped.$index + 6}}</div>
              </template>
            </el-table-column>
            <el-table-column
              align="center"
              prop="topBelongField"
              label="所属领域">
            </el-table-column>
            <el-table-column
              align="center"
              prop="dataNum"
              label="数量"
              width="100">
            </el-table-column>
            <el-table-column
              align="center"
              prop="percentage"
              label="占比"
              width="100">
            </el-table-column>
          </el-table>
        </div>
      </el-card>
      <div class="component-loading" v-else>
        <span v-for="(item, index) in [0, 1, 2, 3, 4]"></span>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  props: {
    subAssociationDataSumResult: Array,
    topBelongFieldDataSumResult: Array,
  }
}
</script>
<style scoped lang="scss">
.table-box{
  display: flex;
  gap: 10px;
  justify-content: space-between;
  padding: 24px 0 24px 0;
  .table-box-item{
    width: 49%;
    border: 1px solid #eae9e9;
    box-shadow: 3px 3px 5px 0px rgba(0,0,0,0.2);
    .table-box-item-main{
      display: flex;
    }
  }
  .sort{
    background-color: #F59A23;
    color: #fff;
    border-radius: 50%;
    width: 30px;
    height: 30px;
    display: flex;
    justify-content: center;
    align-items: center;
  }
}
</style>
