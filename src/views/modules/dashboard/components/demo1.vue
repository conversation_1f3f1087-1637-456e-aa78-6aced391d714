<template>
  <div class="table-box">
    <div class="table-box-item table-box-item-left">
      <el-card class="table-sum-card">
<!--        <slot name="table1"></slot>-->
        <CoreList :core-indicator-data-sum-result="coreIndicatorDataSumResult" :type="'demo1'"/>
      </el-card>
    </div>
    <div class="table-box-item">
      <el-card class="table-sum-card" shadow="hover" v-if="topBelongFieldDataSumResult">
        <slot name="table2"></slot>
        <div class="table-box-item-main">
          <el-table
              :data="topBelongFieldDataSumResult.slice(0, 5)"
              style="width: 100%">
            <el-table-column
                label="排名"
                width="50">
              <template slot-scope="scoped">
                <div class="sort">{{ scoped.$index + 1}}</div>
              </template>
            </el-table-column>
            <el-table-column
                align="center"
                prop="topBelongField"
                label="所属领域">
            </el-table-column>
            <el-table-column
                align="center"
                prop="dataNum"
                label="数量"
                width="100">
            </el-table-column>
            <el-table-column
                align="center"
                prop="percentage"
                label="占比"
                width="100">
            </el-table-column>
          </el-table>
          <el-table
              :data="topBelongFieldDataSumResult.slice(5)"
              style="width: 100%">
            <el-table-column
                label="排名"
                width="50"
                type="index">
              <template slot-scope="scoped">
                <div class="sort">{{ scoped.$index + 6}}</div>
              </template>
            </el-table-column>
            <el-table-column
                align="center"
                prop="topBelongField"
                label="所属领域">
            </el-table-column>
            <el-table-column
                align="center"
                prop="dataNum"
                label="数量"
                width="100">
            </el-table-column>
            <el-table-column
                align="center"
                prop="percentage"
                label="占比"
                width="100">
            </el-table-column>
          </el-table>
        </div>
      </el-card>
      <div class="component-loading" v-else>
        <span v-for="(item, index) in [0, 1, 2, 3, 4]"></span>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  props: {
    coreIndicatorDataSumResult: Object,
    topBelongFieldDataSumResult: Array
  },
  data () {
    return {
      tableDataRight: [{
        name: '理论政策',
        name1: '27052',
        name2: '100%',
      }, {
        name: '理论政策',
        name1: '27052',
        name2: '100%',
      }, {
        name: '理论政策',
        name1: '27052',
        name2: '100%',
      }, {
        name: '理论政策',
        name1: '27052',
        name2: '100%',
      }, {
        name: '理论政策',
        name1: '27052',
        name2: '100%',
      }]
    }
  },
  components: {
    CoreList: () => import('./CoreList.vue')
  }
}
</script>
<style scoped lang="scss">
.table-box{
  display: flex;
  gap: 10px;
  padding: 24px 0 24px 0px;
  .table-box-item{
    width: 50%;
    border: 1px solid #eae9e9;
    box-shadow: 3px 3px 5px 0px rgba(0,0,0,0.2);
    .table-box-item-main{
      display: flex;
    }
    .table-box-item-left{
    }
  }
  .sort{
    background-color: #F59A23;
    color: #fff;
    border-radius: 50%;
    width: 30px;
    height: 30px;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .coreList-box ::v-deep{
    height: 300px;
    overflow: hidden;
    .list{
      height: 100%;
      align-items: center;
      .list-item{
        width: 112px;
      }
    }
  }
}
</style>
