<template>
<div>
  <GridData :tag-sum-result="tagSumResult" :manager-capacity="managerCapacity" v-if="managerCapacity === 'ASSOCIATION_ADMIN' || managerCapacity === 'SUB_ASSOCIATION_ADMIN' || managerCapacity === 'COMMUNITY_ADMIN' || managerCapacity === 'TEAM_ADMIN'"/>
  <ButtonLinks v-if="managerCapacity === 'ASSOCIATION_ADMIN'"/>
  <Echarts :last30-days-bar-sum-result="last30DaysBarSumResult" :this-month-res-req-act-sum-result="thisMonthResReqActSumResult" v-if="managerCapacity === 'ASSOCIATION_ADMIN'">
  </Echarts>
  <Tables :sub-association-data-sum-result="subAssociationDataSumResult" :top-belong-field-data-sum-result="topBelongFieldDataSumResult" v-if="managerCapacity === 'ASSOCIATION_ADMIN'">
    <template #table1>
      <div class="dashboard-title">分协会累计数据</div>
    </template>
    <template #table2>
      <div class="dashboard-title">所属领域</div>
    </template>
  </Tables>
<!--  <hr />-->
<!--  <h1>D1是核心指标数据和所属领域共占一行的组件，需要插入两个插槽#table1和#table2分别对应左右两个标题</h1>-->
<!--  <h1>D2是核心指标数据独占一行的组件,需要插入一个title的插槽</h1>-->
  <D1 :top-belong-field-data-sum-result="topBelongFieldDataSumResult" :core-indicator-data-sum-result="coreIndicatorDataSumResult" v-if="managerCapacity === 'SUB_ASSOCIATION_ADMIN'">
    <template #table1>
    </template>
    <template #table2>
      <div class="dashboard-title">所属领域</div>
    </template>
  </D1>
  <D2 :core-indicator-data-sum-result="coreIndicatorDataSumResult" v-if="managerCapacity === 'COMMUNITY_ADMIN' || managerCapacity === 'TEAM_ADMIN'">
    <template #title>
    </template>
  </D2>
</div>
</template>

<script>
import * as sr from './sum_request.js'
export default {
  name: "index",
  components: {
    GridData: () => import('./components/gridData.vue'),
    ButtonLinks: () => import('./components/buttonLinks.vue'),
    Echarts: () => import('./components/echarts.vue'),
    Tables: () => import('./components/table.vue'),
    D1: () => import('./components/demo1.vue'), // 核心指标数据 和 所属领域共占一行的组件 （记得拷贝.dashboard-title的样式）
    D2: () => import('./components/demo2.vue'), // 核心指标数据独占一行的组件 （记得拷贝.dashboard-title的样式）
  },
  activated() {
    this.dataInit()
  },
  data () {
    return {
      managerCapacity: this.$store.state.user.managerCapacity,
      sr: sr,
      tagSumResult: null,
      last30DaysBarSumResult: null,
      thisMonthResReqActSumResult: null,
      subAssociationDataSumResult: null,
      topBelongFieldDataSumResult: null,
      coreIndicatorDataSumResult: null
    }
  },
  methods: {
    dataInit() {
      if (this.managerCapacity !== 'ASSOCIATION_ADMIN' && this.managerCapacity !== 'SUB_ASSOCIATION_ADMIN' && this.managerCapacity !== 'COMMUNITY_ADMIN' && this.managerCapacity !== 'TEAM_ADMIN') {
        return
      }
      this.getTagSumResult()
      if (this.managerCapacity === 'ASSOCIATION_ADMIN') {
        this.getSubAssociationDataSumResult()
        this.getLast30DaysBarSumResult()
        this.getThisMonthResReqActSumResult()
        this.getTopBelongFieldDataSumResult()
        return
      }
      this.getCoreIndicatorDataSumResult()
      if (this.managerCapacity === 'SUB_ASSOCIATION_ADMIN') {
        this.getTopBelongFieldDataSumResult()
      }
    },
    async getTagSumResult () {
      this.tagSumResult = await sr.tagSum()
    },
    async getLast30DaysBarSumResult () {
      this.last30DaysBarSumResult = await sr.last30DaysBarSum()
    },
    async getThisMonthResReqActSumResult () {
      this.thisMonthResReqActSumResult = await sr.thisMonthResReqActSum()
    },
    async getSubAssociationDataSumResult () {
      this.subAssociationDataSumResult = await sr.subAssociationDataSum()
    },
    async getTopBelongFieldDataSumResult () {
      this.topBelongFieldDataSumResult = await sr.topBelongFieldDataSum()
    },
    async getCoreIndicatorDataSumResult () {
      this.coreIndicatorDataSumResult = await sr.coreIndicatorDataSum()
    }
  }
}
</script>

<style lang="scss">
.table-sum-card {
  border: 0px !important;
  height: 100%;
}
.dashboard-title{
  color: #465365;
  font-size: 20px;
  margin-bottom: 10px;
}
.component-loading{
  width: 100px;
  height: 40px;
  margin: 100px auto 100px;
  span{
    margin: 5px;
    display: inline-block;
    width: 8px;
    height: 80%;
    border-radius: 4px;
    background: lightgreen;
    -webkit-animation: load 1s ease infinite;
  }
}
@-webkit-keyframes load{
  0%, 100%{
    height: 30px;
    background: lightgreen;
  }
  50%{
    height: 60px;
    margin: -10px 5px;
    background: lightblue;
  }
}
.component-loading span:nth-child(2){
  -webkit-animation-delay:0.2s;
}
.component-loading span:nth-child(3){
  -webkit-animation-delay:0.4s;
}
.component-loading span:nth-child(4){
  -webkit-animation-delay:0.6s;
}
.component-loading span:nth-child(5){
  -webkit-animation-delay:0.8s;
}
</style>
