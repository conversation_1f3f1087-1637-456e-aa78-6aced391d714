import httpRequest from '@/utils/httpRequest'
import {Message} from "element-ui";

const tagSumApi = '/admin/dashboard/tag_sum'

const last30DaysBarSumApi = '/admin/dashboard/last_30_days_bar_sum'

const thisMonthResReqActSumApi = '/admin/dashboard/this_month_res_req_act_sum'

const subAssociationDataSumApi = '/admin/dashboard/sub_association_data_sum'

const topBelongFieldDataSumApi = '/admin/dashboard/top_belong_field_data_sum'

const coreIndicatorDataSumApi = '/admin/dashboard/core_indicator_data_sum'

/**
 * 标签统计
 */
export function tagSum() {
   return sum_request(tagSumApi)
}

/**
 * 近30天资源、需求、活动柱状统计
 */
export function last30DaysBarSum() {
    return sum_request(last30DaysBarSumApi)
}

/**
 * 本月累计资源、需求、活动数量统计
 */
export function thisMonthResReqActSum() {
    return sum_request(thisMonthResReqActSumApi)
}

/**
 * 分协会累计数据统计（按分协会志愿者数倒序rank）
 */
export function subAssociationDataSum() {
    return sum_request(subAssociationDataSumApi)
}

/**
 * 一级所属领域数据量占比统计（top10）
 */
export function topBelongFieldDataSum() {
    return sum_request(topBelongFieldDataSumApi)
}

/**
 * 核心指标数据（不包含协会）
 */
export function coreIndicatorDataSum() {
    return sum_request(coreIndicatorDataSumApi)
}

function sum_request(api) {
    return new Promise((resolve, reject)=>{
        httpRequest({
            url: httpRequest.adornUrl(api),
            method: 'get'
        }).then(({data}) => {
            if (data && data.code === 0) {
                resolve(data.obj);
            } else {
                Message.error(data.msg)
            }
        }).catch(err => {
            reject(err)
        })
    })
}
