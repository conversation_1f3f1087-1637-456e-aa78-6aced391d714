<template>
  <div>
    <el-dialog
      :title="!dataForm.id ? '查看' : '回复'"
      :close-on-click-modal="false"
      :visible.sync="visible" width="80%">
      <el-form :model="dataForm" :rules="dataRule" ref="dataForm" label-width="100px">
        <el-form-item label="评论人名" prop="name">
          <el-input v-model="dataForm.name" disabled placeholder="评论人名"></el-input>
        </el-form-item>
        <el-form-item label="评论时间" prop="code">
          <el-input v-model="dataForm.commentTime" disabled placeholder="评论时间"></el-input>
        </el-form-item>
        <el-form-item label="联系电话" prop="phone">
          <el-input v-model="dataForm.phone" placeholder="联系电话" disabled></el-input>
        </el-form-item>
        <el-form-item label="评论人ip" prop="ip">
          <el-input v-model="dataForm.ip" placeholder="评论人ip" disabled></el-input>
        </el-form-item>
        <div v-if="dataForm.reply">
          <el-form-item label="回复人" prop="replyPerson">
            <el-input v-model="dataForm.replyPerson" placeholder="回复人" disabled></el-input>
          </el-form-item>
          <el-form-item label="回复时间" prop="回复时间">
            <el-input v-model="dataForm.replyTime" placeholder="回复时间" disabled></el-input>
          </el-form-item>
          <el-form-item label="回复内容" prop="replyContent">
            <el-input type="textarea" v-model="dataForm.replyContent" disabled></el-input>
          </el-form-item>
        </div>
        <el-form-item label="回复内容" prop="nowReplyContent" v-if="replyNow">
          <el-input type="textarea" v-model="dataForm.nowReplyContent" :rows="7"></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="visible = false">{{replyNow?'取消':'关闭'}}</el-button>
        <el-button type="primary" @click="reply()" v-if="replyNow">确定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
  export default {
    data () {
      return {
        replyNow: false,
        visible: false,
        dataForm: {
          nowReplyContent: ''
        },
        dataRule: {
          nowReplyContent: [
            { required: true, message: '回复内容', trigger: 'blur' }
          ]
        }
      }
    },
    methods: {
      init (id, replyNow) {
        this.replyNow = replyNow || false
        this.visible = true
        this.dataForm.id = id
        if (this.dataForm.id) {
          // 修改
          this.$http({
            url: this.$http.adornUrl(`/admin/cms/comment/`),
            method: 'get',
            params: this.$http.adornParams({id: this.dataForm.id})
          }).then(({data}) => {
            this.dataForm = data.obj
          })
        }
      },
      reply () {
        this.$refs['dataForm'].validate((valid) => {
          if (valid) {
            this.$http({
              url: this.$http.adornUrl('/admin/cms/comment/reply'),
              method: 'post',
              params: this.$http.adornParams({
                'id': this.dataForm.id,
                'content': this.dataForm.nowReplyContent
              })
            }).then(({data}) => {
              if (data && data.code === 0) {
                this.$message({
                  message: '操作成功',
                  type: 'success',
                  duration: 1500,
                  onClose: () => {
                    this.visible = false
                    this.$emit('refreshDataList')
                  }
                })
              } else {
                this.$message.error(data.msg)
              }
            })
          }
        })
      }
    }
  }
</script>
