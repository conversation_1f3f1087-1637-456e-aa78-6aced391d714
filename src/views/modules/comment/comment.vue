<template>
  <div class="mod-config">
    <el-form :inline="true" ref="dataForm" :model="dataForm" @keyup.enter.native="getDataList()">
<!--      <el-form-item label="所属栏目" prop="categoryId">-->
<!--        <el-cascader-->
<!--          placeholder="所属栏目"-->
<!--          v-model="dataForm.categoryId"-->
<!--          :options="categories"-->
<!--          change-on-select-->
<!--          clearable=""-->
<!--        ></el-cascader>-->
<!--      </el-form-item>-->
      <el-form-item label="内容:" prop="contentId">
        <el-select
          v-model="dataForm.contentId"
          filterable
          remote
          reserve-keyword
          placeholder="请输入关键词"
          :remote-method="remoteMethod"
          :loading="loading">
          <el-option
            v-for="item in contentOptions"
            :key="item.id"
            :label="item.text"
            :value="item.id">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="评论人:" prop="name">
        <el-input v-model="dataForm.name" placeholder="评论人" clearable></el-input>
      </el-form-item>
      <el-form-item label="手机号:" prop="phone">
        <el-input v-model="dataForm.phone" placeholder="手机号" clearable></el-input>
      </el-form-item>
      <el-form-item label="评论时间:" prop="commentTime">
        <el-date-picker
          v-model="dataForm.commentTime"
          clearable
          type="daterange"
          value-format="yyyy-MM-dd"
          format="yyyy-MM-dd"
          align="right"
          start-placeholder="开始日期"
          end-placeholder="结束日期">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="回复状态:" prop="reply">
        <el-select v-model="dataForm.reply" placeholder="请选择">
          <el-option label="全部" value=""></el-option>
          <el-option label="未回复" value="false"></el-option>
          <el-option label="已回复" value="true"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button @click="getDataList()">查询</el-button>
        <el-button @click="resetForm()">重置</el-button>
        <el-button type="primary" @click="deleteHandle()" :disabled="dataListSelections.length <= 0">批量删除</el-button>
      </el-form-item>
    </el-form>
    <el-table
      :data="dataList"
      border
      v-loading="dataListLoading"
      @selection-change="selectionChangeHandle"
      style="width: 100%;">
      <el-table-column
        type="selection"
        header-align="center"
        align="center"
        width="50">
      </el-table-column>
      <el-table-column
        prop="categoryName"
        header-align="center"
        align="center"
        label="所属栏目">
      </el-table-column>
      <el-table-column
        prop="contentName"
        header-align="center"
        align="center"
        label="所属文章">
      </el-table-column>
      <el-table-column
        prop="name"
        header-align="center"
        align="center"
        label="评论人">
      </el-table-column>
      <el-table-column
        prop="commentTime"
        header-align="center"
        align="center"
        label="评论时间">
      </el-table-column>
      <el-table-column
        prop="phone"
        header-align="center"
        align="center"
        label="手机号">
      </el-table-column>
      <el-table-column
        prop="ip"
        header-align="center"
        align="center"
        label="ip地址">
      </el-table-column>
      <el-table-column
        prop="reply"
        header-align="center"
        align="center"
        label="回复状态">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.reply === false" size="small" type="danger">未回复</el-tag>
          <el-tag v-else size="small">已回复</el-tag>
        </template>
      </el-table-column>
      <el-table-column
        prop="replyPerson"
        header-align="center"
        align="center"
        label="回复人">
      </el-table-column>
      <el-table-column
        fixed="right"
        header-align="center"
        align="center"
        width="260"
        label="操作">
        <template slot-scope="scope">
          <el-button type="text" size="small" @click="addOrUpdateHandle(scope.row.id,true)" v-if="!scope.row.reply&&isAuth('comment:comment:reply')" class="btn-control">回复</el-button>
          <el-button type="text" size="small" v-if="isAuth('comment:comment:cat')" @click="addOrUpdateHandle(scope.row.id,false)" class="btn-control">查看详情</el-button>
          <el-button type="text" size="small" v-if="isAuth('comment:comment:delete')" @click="deleteHandle(scope.row.id)" class="btn-control">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      @size-change="sizeChangeHandle"
      @current-change="currentChangeHandle"
      :current-page="pageIndex"
      :page-sizes="[10, 20, 50, 100]"
      :page-size="pageSize"
      :total="totalPage"
      layout="total, sizes, prev, pager, next, jumper">
    </el-pagination>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"></add-or-update>
  </div>
</template>

<script>
  import AddOrUpdate from './comment-add-or-update'
  import listMixin from '@/mixins/list-mixins'
  export default {
    mixins: [listMixin],
    data () {
      return {
        mixinOptions: {
          dataUrl: '/admin/cms/comment/page',
          deleteUrl: '/admin/cms/comment/removeByIds'
        },
        loading: false,
        contentOptions: [],
        categories: [],
        dataForm: {
          contentId: '',
          categoryId: '',
          commentTime: [],
          name: '',
          phone: '',
          reply: null
        }
      }
    },
    components: {
      AddOrUpdate
    },
    methods: {
      queryBeforeHandle () {
        // 查询前操作
        if (this.dataForm.commentTime) {
          this.dataForm.startDate = this.dataForm.commentTime[0]
          this.dataForm.endDate=  this.dataForm.commentTime[1]
        }
      },
      addOrUpdateHandle (id, replyNow) {
        this.addOrUpdateVisible = true
        this.$nextTick(() => {
          this.$refs.addOrUpdate.init(id, replyNow)
        })
      },
      remoteMethod (query) {
        if (query !== '') {
          this.loading = true
          this.$http({
            url: this.$http.adornUrl('/admin/cms/content/fuzzyQueryByTitle'),
            method: 'get',
            params: this.$http.adornParams({
              'title': query
            })
          }).then(({data}) => {
            if (data && data.code === 0) {
              this.contentOptions = data.obj
            } else {
              this.options = []
            }
            this.loading = false
          })
        } else {
          this.options = []
        }
      }
    }
  }
</script>
