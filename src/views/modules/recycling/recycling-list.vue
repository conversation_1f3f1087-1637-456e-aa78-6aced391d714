<template>
  <div class="mod-role">
    <el-form :inline="true" ref="dataForm" :model="dataForm" @keyup.enter.native="getDataList()">
      <el-form-item label="标题:" prop="title">
        <el-input v-model="dataForm.title" placeholder="标题" clearable></el-input>
      </el-form-item>
      <el-form-item label="标签:" prop="labelName">
        <el-input v-model="dataForm.labelName" placeholder="标签" clearable></el-input>
      </el-form-item>
      <el-form-item label="所属栏目" prop="categoryId">
        <el-cascader
          placeholder="所属栏目"
          v-model="dataForm.categoryId"
          :options="categories"
          :props="{checkStrictly: true}"
        ></el-cascader>
      </el-form-item>
      <el-form-item label="审核状态：" prop="auditStatus">
        <el-select v-model="dataForm.auditStatus" clearable placeholder="请选择">
          <el-option
            v-for="item in auditStatusList"
            :key="item.code"
            :label="item.name"
            :value="item.code">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="过期时间" prop="dateTime">
        <el-date-picker
          v-model="dataForm.dateTime"
          clearable
          type="datetimerange"
          value-format="yyyy-MM-dd HH:mm:ss"
          format="yyyy/MM/dd"
          align="right"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="['00:00:00', '23:59:59']">
        </el-date-picker>
      </el-form-item>
       <el-form-item>
       <!-- <div style="float:right;margin-bottom: 15px;"> -->
        <!-- <el-row type="flex" justify="end" style="margin-bottom: 15px;"> -->
          <el-button icon="el-icon-search" @click="getDataList()">查询</el-button>
          <el-button icon="el-icon-refresh" @click="resetForm()">重置</el-button>
          <el-button type="primary" @click="deleteHandle()" :disabled="dataListSelections.length <= 0">批量恢复</el-button>
        <!-- </el-row> -->
        <!-- </div> -->
       </el-form-item>
    </el-form>

    <el-table
      :data="dataList"
      border
      v-loading="dataListLoading"
      @selection-change="selectionChangeHandle"
      style="width: 100%;">
      <el-table-column
        type="selection"
        header-align="center"
        align="center"
        width="50">
      </el-table-column>
      <el-table-column
        prop="title"
        header-align="center"
        align="center"
        label="标题">
      </el-table-column>
      <el-table-column
        prop="categoryName"
        header-align="center"
        align="center"
        label="所属栏目">
      </el-table-column>
      <el-table-column
        prop="parentName"
        header-align="center"
        align="center"
        label="所属上级栏目">
      </el-table-column>
      <el-table-column
        prop="labelName"
        header-align="center"
        align="center"
        label="标签">
      </el-table-column>
      <el-table-column
        prop="effectiveDate"
        header-align="center"
        align="center"
        label="生效日期">
      </el-table-column>
      <el-table-column
        prop="expirationDate"
        header-align="center"
        align="center"
        label="过期日期">
      </el-table-column>
      <el-table-column
        prop="sequence"
        header-align="center"
        align="center"
        label="排序">
      </el-table-column>
      <el-table-column
        prop="importantLevel"
        header-align="center"
        align="center"
        label="重要度">
      </el-table-column>
      <el-table-column
        prop="stick"
        header-align="center"
        align="center"
        label="置顶">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.stick == false" size="small" type="danger">否</el-tag>
          <el-tag v-else size="small">是</el-tag>
        </template>
      </el-table-column>
      <el-table-column
        prop="hot"
        header-align="center"
        align="center"
        label="热门">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.hot == false" size="small" type="danger">否</el-tag>
          <el-tag v-else size="small">是</el-tag>
        </template>
      </el-table-column>
      <el-table-column
        prop="auditStatusName"
        header-align="center"
        align="center"
        label="审核状态">
      </el-table-column>
      <el-table-column
        prop="comment"
        header-align="center"
        align="center"
        label="可以评论">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.comment == false" size="small" type="danger">否</el-tag>
          <el-tag v-else size="small">是</el-tag>
        </template>
      </el-table-column>
      <el-table-column
        fixed="right"
        header-align="center"
        align="center"
        width="150"
        label="操作">
        <template slot-scope="scope">
          <el-button v-if="isAuth('cms:recovery:detail')" type="text" size="small" @click="detailHandle(scope.row.id)" class="btn-control">查看</el-button>
          <el-button v-if="isAuth('cms:recovery:recovery')" type="text" size="small" @click="deleteHandle(scope.row.id)" class="btn-control">恢复</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      @size-change="sizeChangeHandle"
      @current-change="currentChangeHandle"
      :current-page="pageIndex"
      :page-sizes="[10, 20, 50, 100]"
      :page-size="pageSize"
      :total="totalPage"
      layout="total, sizes, prev, pager, next, jumper">
    </el-pagination>
    <!-- 查看 -->
    <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate"></add-or-update>
  </div>
</template>

<script>
  import AddOrUpdate from './recycling-add-or-update'
  export default {
    data () {
      return {
        dataForm: {
          title: '',
          labelName: '',
          auditStatus: '',
          dateTime: '',
          categoryId: []
        },
        auditStatusList: [],
        dataList: [],
        categories: [],
        pageIndex: 1,
        pageSize: 10,
        totalPage: 0,
        dataListLoading: false,
        dataListSelections: '',
        addOrUpdateVisible: false
      }
    },
    components: {
      AddOrUpdate
    },
    activated () {
      this.getDataList()
      this.getAuditStatusList()
      this.getCategories()
    },
    methods: {
      // 获取数据列表
      getDataList () {
        this.dataListLoading = true
        this.$http({
          url: this.$http.adornUrl('/admin/cms/categoryContent/page'),
          method: 'post',
          data: this.$http.adornData({
            'currentPage': this.pageIndex,
            'pageSize': this.pageSize,
            'title': this.dataForm.title,
            'labelName': this.dataForm.labelName,
            'auditStatus': this.dataForm.auditStatus,
            'recycling': true,
            'startDate': this.dataForm.dateTime != null ? this.dataForm.dateTime[0] : null,
            'endDate': this.dataForm.dateTime != null ? this.dataForm.dateTime[1] : null,
            'categoryList': this.dataForm.categoryId.join(',')
          })
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.dataList = data.obj.records
            this.totalPage = data.obj.total
          } else {
            this.dataList = []
            this.totalPage = 0
          }
          this.dataListLoading = false
        })
      },
      // 每页数
      sizeChangeHandle (val) {
        this.pageSize = val
        this.pageIndex = 1
        this.getDataList()
      },
      // 当前页
      currentChangeHandle (val) {
        this.pageIndex = val
        this.getDataList()
      },
      // 多选
      selectionChangeHandle (val) {
        this.dataListSelections = val
      },
      // 重置
      resetForm () {
        this.$nextTick(() => {
          this.$refs['dataForm']?.resetFields()
          this.getDataList()
        })
      },
      // 查看
      detailHandle (id) {
        this.addOrUpdateVisible = true
        this.$nextTick(() => {
          this.$refs.addOrUpdate.init(id)
        })
      },
      // 删除
      deleteHandle (id) {
        var ids = id ? [id] : this.dataListSelections.map(item => {
          return item.id
        })
        this.$confirm(`确定要删除操作吗？`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
          closeOnClickModal: false
        }).then(() => {
          this.$http({
            url: this.$http.adornUrl('/admin/cms/categoryContent/recoveryByIds'),
            method: 'get',
            params: this.$http.adornParams({
              'ids': ids.join(',')
            })
          }).then(({data}) => {
            if (data && data.code === 0) {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 1500,
                onClose: () => {
                  this.getDataList()
                }
              })
            } else {
              this.$message.error(data.msg)
            }
          })
        }).catch(() => {})
      },
      hotHandle (id, hot) {
        this.$confirm(`确定进行[${hot ? '取消热门' : '热门'}]操作?`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
          closeOnClickModal: false
        }).then(() => {
          this.$http({
            url: this.$http.adornUrl('/admin/cms/categoryContent/setHot'),
            method: 'get',
            params: this.$http.adornParams({
              'id': id
            })
          }).then(({data}) => {
            if (data && data.code === 0) {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 1500,
                onClose: () => {
                  this.getDataList()
                }
              })
            } else {
              this.$message.error(data.msg)
            }
          })
        }).catch(() => {})
      },
      stickHandle (id, stick) {
        this.$confirm(`确定进行[${stick ? '取消置顶' : '置顶'}]操作?`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
          closeOnClickModal: false
        }).then(() => {
          this.$http({
            url: this.$http.adornUrl('/admin/cms/categoryContent/setStick'),
            method: 'get',
            params: this.$http.adornParams({
              'id': id
            })
          }).then(({data}) => {
            if (data && data.code === 0) {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 1500,
                onClose: () => {
                  this.getDataList()
                }
              })
            } else {
              this.$message.error(data.msg)
            }
          })
        }).catch(() => {})
      },
      getCategories () {
        this.$http({
          url: this.$http.adornUrl('/admin/cms/category/cascader'),
          method: 'get',
          params: this.$http.adornParams()
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.categories = this.getTreeData(data.obj)
          } else {
            this.categories = []
          }
        })
      },

      // *处理点位分类最后children数组为空的状态
      getTreeData: function (data) {
        var that = this
        // 循环遍历json数据
        data.forEach(function (e) {
          if (!e.children || e.children.length < 1) {
            e.children = undefined
          } else {
            // children若不为空数组，则继续 递归调用 本方法
            that.getTreeData(e.children)
          }
        })
        return data
      },
      // 审核状态下拉列表
      getAuditStatusList () {
        this.$http({
          url: this.$http.adornUrl('/admin/dict/parent'),
          method: 'get',
          params: this.$http.adornParams({
            'code': 'validStatus'
          })
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.auditStatusList = data.obj
          } else {
            this.auditStatusList = []
          }
        })
      }
    }
  }
</script>
