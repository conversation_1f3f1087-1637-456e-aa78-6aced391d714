<template>
  <div>
    <el-dialog
      :title="'查看'"
      :close-on-click-modal="false"
      :visible.sync="visible" width="80%">
      <el-form :model="dataForm" ref="dataForm" label-width="100px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="所属栏目" prop="categoryId">
              <el-cascader-multi v-model="dataForm.categoryId" :data="categories" disabled> </el-cascader-multi>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="标题" prop="title">
              <el-input v-model="dataForm.title" disabled></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="SEO标题" prop="seoTitle">
              <el-input v-model="dataForm.seoTitle"disabled></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="SEO关健字" prop="seoKeyword">
              <el-input v-model="dataForm.seoKeyword" disabled></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="SEO描述" prop="seoRemark">
              <el-input v-model="dataForm.seoRemark" disabled></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="简介" prop="briefIntroduction">
              <el-input v-model="dataForm.briefIntroduction" disabled></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="生效日期" prop="effectiveDate">
              <el-input v-model="dataForm.effectiveDate" disabled></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="过期日期" prop="expirationDate">
              <el-input v-model="dataForm.expirationDate" disabled></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="重要度" prop="importantLevel">
              <el-input v-model="dataForm.importantLevel" disabled></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="排序" prop="sequence">
              <el-input v-model="dataForm.sequence" controls-position="right" :min="0" label="排序" disabled></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="自定义链接" prop="customLinks">
              <el-input v-model="dataForm.customLinks" disabled></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="属性配置" prop="customLinks">
              <el-checkbox v-model="dataForm.stick" disabled>置顶</el-checkbox>
              <el-checkbox v-model="dataForm.hot" disabled>热门</el-checkbox>
              <el-checkbox v-model="dataForm.audit" disabled>是否需要审核</el-checkbox>
              <el-checkbox v-model="dataForm.comment" disabled>是否可以评论</el-checkbox>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="10">
          <el-col :span="8">
            <el-form-item label="图片：" prop="titleImgUrl">
              <img v-if="dataForm.titleImgUrl" :src="$http.adornAttachmentUrl(dataForm.titleImgUrl)" class="avatar">
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-form-item label="详细描述：" prop="description">
            <quill-editor v-model="dataForm.description"
                          ref="myQuillEditor"
                          :options="editorOption"
                          disabled>
            </quill-editor>
          </el-form-item>
        </el-row>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="visible = false">取消</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
  import { quillEditor } from 'vue-quill-editor' // 调用编辑器
  import 'quill/dist/quill.core.css'
  import 'quill/dist/quill.snow.css'
  import 'quill/dist/quill.bubble.css'
  export default {
    data () {
      return {
        visible: false,
        quillUpdateImg: false,
        editorOption: {
          theme: 'snow', // or 'bubble'
          modules: {
            toolbar: []
          }
        },
        dataForm: {
          id: null,
          categoryId: [],
          title: null,
          contentId: null,
          seoTitle: null,
          seoKeyword: null,
          seoRemark: null,
          titleImgUrl: null,
          briefIntroduction: null,
          description: null,
          customLinks: null,
          effectiveDate: null,
          expirationDate: null,
          sequence: 0,
          importantLevel: null,
          stick: false,
          hot: false,
          audit: false,
          comment: false
        },
        categories: []
      }
    },
    components: {
      quillEditor
    },
    methods: {
      init (id) {
        this.dataForm.id = id || undefined
        this.$http({
          url: this.$http.adornUrl('/admin/cms/category/cascader'),
          method: 'get',
          params: this.$http.adornParams()
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.categories = data.obj
          } else {
            this.categories = []
          }
        }).then(() => {
          this.visible = true
          this.$nextTick(() => {
            this.$refs['dataForm']?.resetFields()
          })
        }).then(() => {
          if (this.dataForm.id) {
            // 修改
            this.$http({
              url: this.$http.adornUrl(`/admin/cms/categoryContent/getCategoryContentById`),
              method: 'get',
              params: this.$http.adornParams({ id: this.dataForm.id })
            }).then(({data}) => {
              this.dataForm = data.obj
            })
          }
        })
      }
    }
  }
</script>

<style lang="scss">
  .avatar {
    width: 178px;
    height: 178px;
    display: block;
  }
  .avatar-uploader .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
  }

  .avatar-uploader .el-upload:hover {
    border-color: #409EFF;
  }

  .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 178px;
    height: 178px;
    line-height: 178px;
    text-align: center;
  }
  .mod-menu {
    .menu-list__input,
    .icon-list__input {
      > .el-input__inner {
        cursor: pointer;
      }
    }
    &__icon-popover {
      width: 458px;
      overflow: hidden;
    }
    &__icon-inner {
      width: 478px;
      max-height: 258px;
      overflow-x: hidden;
      overflow-y: auto;
    }
    &__icon-list {
      width: 458px;
      padding: 0;
      margin: -8px 0 0 -8px;
      > .el-button {
        padding: 8px;
        margin: 8px 0 0 8px;
        > span {
          display: inline-block;
          vertical-align: middle;
          width: 18px;
          height: 18px;
          font-size: 18px;
        }
      }
    }
    .icon-list__tips {
      font-size: 18px;
      text-align: center;
      color: #e6a23c;
      cursor: pointer;
    }
  }
</style>
