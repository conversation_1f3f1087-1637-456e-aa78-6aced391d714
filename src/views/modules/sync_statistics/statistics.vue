<template>
  <div class="mod-config">
    <el-form :inline="true" :model="dataForm">
      <el-form-item label="日期" prop="startEndDate">
        <el-date-picker
            v-model="dataForm.startEndDate"
            type="monthrange"
            value-format="yyyy-MM"
            range-separator="-"
            start-placeholder="开始月份"
            end-placeholder="结束月份">
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-search" type="warning" @click="queryPage()">查询</el-button>
      </el-form-item>
    </el-form>
    <el-tabs v-model="activeName" @tab-click="handleClick">
      <el-tab-pane label="上报数据统计" name="first">
        <el-table :data="dataList" border v-loading="dataListLoading" style="width: 100%;">
          <el-table-column prop="yearMon" header-align="center" align="center" label="日期">
          </el-table-column>
          <el-table-column prop="addNum" header-align="center" align="center" min-width="80" label="新增数据">
          </el-table-column>
          <el-table-column prop="successNum" header-align="center" align="center" min-width="120" label="上报成功">
          </el-table-column>
          <el-table-column prop="failNum" header-align="center" align="center" min-width="120" label="上报失败">
          </el-table-column>
          <el-table-column v-if="apiVisible" prop="successNum" header-align="center" align="center" label="接口调用成功数">
          </el-table-column>
          <el-table-column  v-if="apiVisible" prop="failNum" header-align="center" align="center" min-width="80" label="接口调用失败数">
          </el-table-column>
          <el-table-column fixed="right" header-align="center" align="center" min-width="180" label="操作">
            <template slot-scope="scope">
              <el-button v-if="scope.row.failNum > 0" type="text" size="small" @click="exportFailData(scope.row.yearMon, 'FAIL', '')">导出失败数据</el-button>
            </template>
          </el-table-column>
        </el-table>
        <el-pagination @size-change="sizeChangeHandle" @current-change="currentChangeHandle" :current-page="pageIndex"
                       :page-sizes="[10, 20, 50, 100]" :page-size="pageSize" :total="totalPage"
                       layout="total, sizes, prev, pager, next, jumper">
        </el-pagination>
      </el-tab-pane>
      <el-tab-pane label="失败原因统计" name="second">
        <el-table :data="dataFailList" border v-loading="dataFailListLoading" style="width: 100%;">
          <el-table-column prop="rankNum" header-align="center" align="center" label="排名">
          </el-table-column>
          <el-table-column prop="errorMsg" header-align="center" align="center" label="上报失败原因">
          </el-table-column>
          <el-table-column prop="failNum" header-align="center" align="center" min-width="80" label="上报失败数量">
          </el-table-column>
          <el-table-column fixed="right" header-align="center" align="center" min-width="180" label="操作">
            <template slot-scope="scope">
              <el-button v-if="scope.row.failNum > 0" type="text" size="small" @click="exportFailData('', 'FAIL', scope.row.message)">导出失败数据</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<style scoped lang="scss">

</style>
<script>
import moment from 'moment'
export default {
  data() {
    return {
      activeName: 'first',
      dataList: [],
      dataFailList: [],
      pageIndex: 1,
      pageSize: 10,
      totalPage: 0,
      dataListLoading: false,
      dataFailListLoading: false,
      dataForm: {
        startEndDate: []
      }
    }
  },
  props: {
    bizType: {
      type: String,
      required: true,
      default: ''
    },
    apiVisible: {
      type: Boolean,
      required: false,
      default: false
    }
  },
  created() {
    const end = moment(new Date()).format('YYYY-MM-DD')
    const start = moment(new Date(new Date().getFullYear(), 0)).format('YYYY-MM-DD')
    this.dataForm.startEndDate = [start, end]
    this.getDetail();
  },
  methods: {
    handleClick(tab, event) {
      console.log(tab, event);
      if (tab.name === 'first') {
        this.getDataList()
      }
      if (tab.name === 'second') {
        this.getDataFailList()
      }
    },
    queryPage() {
      this.pageIndex = 1
      this.getDataList()
      this.getDataFailList()
    },
    getDataList () {
      this.dataListLoading = true
      this.$http({
        url: this.$http.adornUrl('/admin/zyz/sync-log/statisticsPage'),
        method: 'post',
        data: this.$http.adornData({
          'currentPage': this.pageIndex,
          'pageSize': this.pageSize,
          'bizType': this.bizType,
          'start': this.dataForm.startEndDate != null ? this.dataForm.startEndDate[0] : null,
          'end': this.dataForm.startEndDate != null ? this.dataForm.startEndDate[1] : null
        })
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.dataList = data.obj.records
          this.totalPage = data.obj.total
        } else {
          this.dataList = []
          this.totalPage = 0
        }
        this.dataListLoading = false
      })
    },
    getDataFailList () {
      this.dataFailListLoading = true
      this.$http({
        url: this.$http.adornUrl('/admin/zyz/sync-log/statisticsFailList'),
        method: 'post',
        data: this.$http.adornData({
          'bizType': this.bizType,
          'start': this.dataForm.startEndDate != null ? this.dataForm.startEndDate[0] : null,
          'end': this.dataForm.startEndDate != null ? this.dataForm.startEndDate[1] : null
        })
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.dataFailList = data.obj
        } else {
          this.dataFailList = []
        }
        this.dataFailListLoading = false
      })
    },
    // 每页数
    sizeChangeHandle(val) {
      this.pageSize = val
      this.pageIndex = 1
      this.getDataList()
    },
    // 当前页
    currentChangeHandle(val) {
      this.pageIndex = val
      this.getDataList()
    },
    // 导出
    exportFailData(yearMon, resultCode, message) {
      this.fullscreenLoading = true;
      this.$http({
        url: this.$http.adornUrl('/admin/zyz/sync-log/exportStatistics'),
        method: 'post',
        responseType: 'arraybuffer',
        data: this.$http.adornData({
          'bizType': this.bizType,
          'yearMon': yearMon,
          'message': message,
          'resultCode': resultCode,
          'start': this.dataForm.startEndDate != null ? this.dataForm.startEndDate[0] : null,
          'end': this.dataForm.startEndDate != null ? this.dataForm.startEndDate[1] : null
        })
      }).then(({data}) => {
        if (data.code && data.code !== 0) {
          this.$message.error('导出失败')
        } else {
          this.fullscreenLoading = false;
          let blob = new Blob([data], {type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8'})
          let objectUrl = URL.createObjectURL(blob)
          let a = document.createElement('a')
          // window.location.href = objectUrl
          a.href = objectUrl
          a.download = yearMon + '失败数据.xlsx'
          a.click()
          URL.revokeObjectURL(objectUrl)
          this.$message({
            type: 'success',
            message: '导出成功'
          })
        }
      })
    }
  }
}
</script>