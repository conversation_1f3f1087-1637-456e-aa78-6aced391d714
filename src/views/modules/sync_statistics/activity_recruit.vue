<template>
  <div>
    <Statistics ref="statistics" bizType="sync_biz_activity_recruit" apiVisible></Statistics>
  </div>
</template>
<script>
import Statistics from './statistics'
export default {
  data() {
    return {
    }
  },
  components: {
    Statistics
  },
  activated () {
    this.getData()
  },
  methods: {
    getData () {
      this.$nextTick(() => {
        this.$refs.statistics.queryPage()
      })
    }
  }
}
</script>