<template>
  <div>
    <Statistics ref="statistics" bizType="sync_practice_sheet" apiVisible></Statistics>
  </div>
</template>
<script>
import Statistics from './statistics'
export default {
  data() {
    return {
    }
  },
  components: {
    Statistics
  },
  activated () {
    this.getData()
  },
  methods: {
    getData () {
      this.$nextTick(() => {
        this.$refs.statistics.queryPage()
      })
    }
  }
}
</script>