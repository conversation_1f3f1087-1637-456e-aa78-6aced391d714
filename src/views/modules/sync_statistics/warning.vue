<template>
  <div class="mod-config">
    <el-form :inline="true" :model="dataForm">
      <el-form-item label="日期" prop="yearMon">
        <el-date-picker
            v-model="dataForm.yearMon"
            type="month"
            value-format="yyyy-MM-dd"
            placeholder="选择月">
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-search" type="warning" @click="getDataList()">查询</el-button>
      </el-form-item>
    </el-form>
    <el-table :data="dataList" border v-loading="dataListLoading" style="width: 100%;">
      <el-table-column prop="yearMon" header-align="center" align="center" label="日期">
      </el-table-column>
      <el-table-column prop="bizTypeName" header-align="center" align="center" min-width="120" label="业务类型">
      </el-table-column>
      <el-table-column prop="reportNum" header-align="center" align="center" min-width="120" label="上报数据量">
      </el-table-column>
      <el-table-column prop="failNum" header-align="center" align="center" min-width="120" label="上报失败数据量">
      </el-table-column>
      <el-table-column prop="failRate" header-align="center" align="center" min-width="120" label="上报错误率">
        <template slot-scope="scope">
          <span :style="scope.row.failRate >= 50 ? 'color: #ff0000' : ''">{{scope.row.failRate + '%'}}</span>
        </template>
      </el-table-column>
      <el-table-column prop="sms" header-align="center" align="center" min-width="120" label="短信通知">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.sms" size="small">是</el-tag>
          <el-tag v-else type="danger" size="small">否</el-tag>
        </template>
      </el-table-column>
      <el-table-column fixed="right" header-align="center" align="center" min-width="180" label="操作">
        <template slot-scope="scope">
          <el-button v-if="scope.row.failNum > 0" type="text" size="small" @click="exportFailData(scope.row.bizType, scope.row.yearMon, 'FAIL')">导出失败数据</el-button>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<style scoped lang="scss">

</style>
<script>
import moment from 'moment'
export default {
  data() {
    return {
      dataList: [],
      dataListLoading: false,
      dataForm: {
        startEndDate: []
      }
    }
  },
  created() {
    this.dataForm.yearMon = moment(new Date()).format('YYYY-MM-DD')
    this.getDataList();
  },
  methods: {
    getDataList () {
      this.dataListLoading = true
      this.$http({
        url: this.$http.adornUrl('/admin/zyz/sync-log/statisticsFailWaringList'),
        method: 'get',
        params: this.$http.adornParams({
          'date': this.dataForm.yearMon,
        })
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.dataList = data.obj
        } else {
          this.dataList = []
        }
        this.dataListLoading = false
      })
    },
    // 导出
    exportFailData(bizType, yearMon, resultCode) {
      this.fullscreenLoading = true;
      this.$http({
        url: this.$http.adornUrl('/admin/zyz/sync-log/exportStatistics'),
        method: 'post',
        responseType: 'arraybuffer',
        data: this.$http.adornData({
          'bizType': bizType,
          'yearMon': yearMon,
          'resultCode': resultCode
        })
      }).then(({data}) => {
        if (data.code && data.code !== 0) {
          this.$message.error('导出失败')
        } else {
          this.fullscreenLoading = false;
          let blob = new Blob([data], {type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8'})
          let objectUrl = URL.createObjectURL(blob)
          let a = document.createElement('a')
          // window.location.href = objectUrl
          a.href = objectUrl
          a.download = yearMon + '失败数据.xlsx'
          a.click()
          URL.revokeObjectURL(objectUrl)
          this.$message({
            type: 'success',
            message: '导出成功'
          })
        }
      })
    }
  }
}
</script>