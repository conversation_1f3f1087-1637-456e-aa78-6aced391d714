<template>
  <div>
    <div style="color: red">点击可进行切换工作节假日操作</div>
    <!--    <div style="display: flex;margin-top: 20px;align-items: center">-->
    <!--      <span>节假日: </span>-->
    <!--      <div style="height:17px;width:30px;background-color: red;margin: 10px;border-radius: 5px"></div>-->
    <!--      <span>工作日: </span>-->
    <!--      <div style="height:17px;width:30px;background-color: #008000;margin-left: 10px;border-radius: 5px"></div>-->
    <!--    </div>-->
    <el-calendar v-model="calendarValue1">
      <!--选中小红点-->
      <template
          slot="dateCell"
          slot-scope="{date, data}"
      >
        <div style="width: 100%;height: 100%;display: flex;justify-content: space-between"
             @click="setWorkDay(data.day,labelBg(data.day))">
          <div>{{ data.day.split('-').slice(2).join('-') }}</div>
          <div>
            <el-tag v-if="labelBg(data.day) === 'holiday'" type="success">节假日</el-tag>
            <el-tag v-if="labelBg(data.day) === 'workDay'" type="danger">工作日</el-tag>
          </div>
        </div>
        <!--        <div style="width: 100%;height: 100%; display: flex; justify-content: center; align-items: center"-->
        <!--             @click="setWorkDay(data.day)" :class="labelBg(data.day)">-->
        <!--          <div>{{ data.day.split('-').slice(2).join('-') }}</div>-->
        <!--        </div>-->
      </template>
    </el-calendar>
  </div>
</template>
<script>

export default {
  data() {
    return {
      calendarValue1: new Date(),
      activeDay: [],
      dataForm: {
        id: '',
        day: '',
        holiday: ''
      }
    }
  },
  activated() {
    this.getActiveDay()
  },
  methods: {
    // 判断class名
    labelBg(event) {
      let findItem = this.activeDay.find(item => item.day === event)
      if (findItem) {
        if (findItem.holiday === true) {
          return 'holiday'
        } else {
          return 'workDay'
        }
      } else if (this.isWeekend(event)) {
        // 节假日
        return 'holiday'
      } else {
        return 'workDay'
      }
    },
    isWeekend(date) {
      if (typeof date === "string") {
        date = new Date(date); // 如果是字符串，先转换成 Date 对象
      }
      const day = date.getDay();
      return day === 0 || day === 6;
    },
    getActiveDay() {
      this.$http({
        url: this.$http.adornUrl(`/admin/zyz/calendar/all`),
        method: 'get',
        params: this.$http.adornParams()
      }).then(({data}) => {
        if (data) {
          this.activeDay = data
        } else {
          this.activeDay = []
        }
      })
    }
    ,
    setWorkDay(date, type) {
      this.$confirm(`确定切换为${type === 'holiday' ? '工作日' : '节假日'}?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$http({
          url: this.$http.adornUrl(`/admin/zyz/calendar/setWorkDay`),
          method: 'get',
          params: this.$http.adornParams({
            'date': date
          })
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.$message({
              message: '操作成功',
              type: 'success',
              duration: 1500,
              onClose: () => {
                this.visible = false
                this.getActiveDay()
              }
            })
          } else {
            this.$message.error(data.msg)
          }
        })
      })
    }
  }
}
</script>


<style scoped>
</style>

