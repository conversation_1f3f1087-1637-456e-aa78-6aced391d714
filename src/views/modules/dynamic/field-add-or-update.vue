<template>
  <el-dialog class="common-dialog" :title="!dataForm.id ? '新增字段' : '修改字段'" :visible.sync="visible"
             :close-on-click-modal="false" :modal-append-to-body="false" width="50%">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmit()" label-width="120px">
      <el-form-item label="字段名称" prop="label">
        <el-input
            v-model="dataForm.label"
            placeholder="请输入不能与已有字段重复的字段名称"
            :disabled="dataForm.id !== null"
            clearable/>
      </el-form-item>
      <el-form-item label="字段类型" prop="fieldType">
        <el-dict :code="'FORM_FIELD_TYPE'" v-model="dataForm.fieldType" :disabled="dataForm.systemFlag"></el-dict>
      </el-form-item>
      <el-form-item label="字段输入提示" prop="placeholder">
        <el-input v-model="dataForm.placeholder" placeholder="请输入字段输入提示" clearable/>
      </el-form-item>
      <el-form-item label="校验类型" prop="validateType" v-if="dataForm.fieldType === 'FORM_TEXT_SINGLE'">
        <el-dict :code="'FORM_VALIDATION_TYPE'" v-model="dataForm.validateType"></el-dict>
      </el-form-item>
      <el-form-item label="正则校验规则" prop="regexRule" v-if="dataForm.fieldType === 'REGEX'">
        <el-input v-model="dataForm.regexRule" placeholder="请输入正则校验规则" clearable/>
      </el-form-item>
      <el-form-item label="字段选项" prop="fieldOption" v-if="showFieldOption">
        <div v-for="(option, index) in dataForm.fieldOption" :key="index" style="margin-bottom: 10px;">
          <el-input 
            v-model="dataForm.fieldOption[index]" 
            placeholder="请输入选项" 
            clearable
            style="width: calc(100% - 50px); margin-right: 10px;"
          >
            <template slot="append">
              <el-button type="danger" icon="el-icon-delete" @click="removeOption(index)"></el-button>
            </template>
          </el-input>
        </div>
        <el-button 
          type="primary" 
          icon="el-icon-plus" 
          @click="addOption"
        >
          添加选项
        </el-button>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()" :disabled="dataFormLoading">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
  import _ from 'lodash'
  import editMixin from '@/mixins/edit-mixins'
  import ElDict from '@/components/el-dict'

  export default {
    components: {
      ElDict
    },
    mixins: [editMixin],
    data () {
      return {
        editOptions: {
          initUrl: '/admin/dynamic/field',
          saveSuffix: 'add',
          editSuffix: 'edit'

        },
        dataForm: {},
        initForm: {
          id: null,
          version: null,
          moduleCode: 'zyz_activity_public_apply_form',
          fieldName: null,
          label: null,
          placeholder: null,
          fieldType: null,
          validateType: null,
          regexRule: null,
          fieldOption: [],
          systemFlag: false,
          commonFlag: false
        },
        dataRule: {
          label: [
            { required: true, message: '字段名称不能为空', trigger: 'change' }
          ],
          fieldName: [
            { required: true, message: '字段标识符不能为空', trigger: 'change' }
          ],
          fieldType: [
            { required: true, message: '字段类型不能为空', trigger: 'change' }
          ],
          placeholder: [
            { required: true, message: '字段输入提示不能为空', trigger: 'change' }
          ]
        }
      }
    },
    methods: {
      init (id, commonFlag) {
        this.dataForm = _.cloneDeep(this.initForm)
        this.dataForm.id = id || null
        // 如果没有传入 commonFlag，默认设为 false
        this.dataForm.commonFlag = commonFlag !== undefined ? commonFlag : false
        this.visible = true
        this.$nextTick(() => {
          this.$refs['dataForm']?.resetFields()
          if (this.dataForm.id) {
            this.getData()
          }
        })
      },
      addOption() {
        if(!this.dataForm['fieldOption']) {
          this.dataForm.fieldOption = []
        }
        this.dataForm.fieldOption.push('')
      },
      removeOption(index) {
        this.dataForm.fieldOption.splice(index, 1)
      }
    },
    computed: {
      showFieldOption() {
        return [
          'FORM_SELECT_SINGLE',
          'FORM_CHECKBOX',
          'FORM_RADIO',
        ].includes(this.dataForm.fieldType);
      }
    }
  }
</script>

<style lang="scss" scoped>
</style>
