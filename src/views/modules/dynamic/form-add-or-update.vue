<template>
  <el-dialog class="common-dialog" :title="!dataForm.id ? '新增' : '修改'" :visible.sync="visible"
             :close-on-click-modal="false" :modal-append-to-body="false" width="60%">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmit()"
             label-width="100px">
      <el-form-item label="模板名称" prop="formName">
        <el-input v-model="dataForm.formName" placeholder="请输入" clearable/>
      </el-form-item>
      <el-form-item label="模板描述" prop="formDescription">
        <el-input v-model="dataForm.formDescription" placeholder="请输入" clearable/>
      </el-form-item>
      <el-form-item label="状态" prop="statusFlag">
        <el-radio-group v-model="dataForm.statusFlag">
          <el-radio :label="true">启用</el-radio>
          <el-radio :label="false">停用</el-radio>
        </el-radio-group>
      </el-form-item>

      <!-- 常用字段区域 -->
      <el-form-item label="模板必填字段">
        <el-table
            :data="commonFields"
            style="width: 100%; margin-bottom: 20px"
            row-key="id"
            ref="commonTable"
            v-loading="fieldsLoading">
          <el-table-column align="center" width="60">
            <template slot-scope="scope">
              <i class="el-icon-sort drag-handle" style="cursor: move; font-size: 18px; color: #909399;"></i>
            </template>
          </el-table-column>
          <el-table-column prop="label" label="字段名称" align="center"/>
          <el-table-column prop="placeholder" label="字段输入提示" align="center"/>
          <el-table-column prop="fieldTypeText" label="字段类型" align="center"/>
          <el-table-column label="是否必填" align="center" width="100">
            <template slot-scope="scope">
              <el-checkbox
                  v-model="scope.row.requireFlag"
                  :disabled="['name', 'certificateId'].includes(scope.row.fieldName)"
              />
            </template>
          </el-table-column>
        </el-table>
      </el-form-item>

      <!-- 自定义字段区域 -->
      <el-form-item label="自定义字段">
        <el-button type="primary" size="mini" @click="openFieldSelectDialog" style="margin-bottom: 10px">添加字段
        </el-button>
        <el-table
            :data="customFields"
            style="width: 100%"
            row-key="id"
            ref="customTable">
          <el-table-column align="center" width="60">
            <template slot-scope="scope">
              <i class="el-icon-sort drag-handle" style="cursor: move; font-size: 18px; color: #909399;"></i>
            </template>
          </el-table-column>
          <el-table-column prop="label" label="字段名称" align="center"/>
          <el-table-column prop="placeholder" label="字段输入提示" align="center"/>
          <el-table-column prop="fieldTypeText" label="字段类型" align="center"/>
          <el-table-column label="是否必填" align="center" width="100">
            <template slot-scope="scope">
              <el-checkbox
                  v-model="scope.row.requireFlag"
                  :disabled="['name', 'certificateId'].includes(scope.row.fieldName)"
              />
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" width="80">
            <template slot-scope="scope">
              <el-button type="text" @click="removeCustomField(scope.$index)" size="small">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-form-item>

    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()" :disabled="dataFormLoading">确定</el-button>
    </span>

    <!-- 使用单独的字段选择组件 -->
    <field-selector v-if="fieldSelectorVisible" ref="fieldSelector" @confirm="handleFieldSelectorConfirm"
                    @cancel="fieldSelectorVisible = false"/>
  </el-dialog>
</template>

<script>
import _, {partition} from 'lodash'
import editMixin from '@/mixins/edit-mixins'
import FieldSelector from './components/field-selector'
import Sortable from 'sortablejs'

export default {
  mixins: [editMixin],
  components: {
    FieldSelector
  },
  data() {
    return {
      editOptions: {
        initUrl: '/admin/dynamic/form/detail',
        submitUrl: '/admin/dynamic/form',
        saveSuffix: 'add',
        updateSuffix: 'edit',
      },
      dataForm: {},
      initForm: {
        id: null,
        version: null,
        moduleCode: 'zyz_activity_public_apply_form',
        formName: null,
        formDescription: null,
        statusFlag: false,
        fields: []
      },
      dataRule: {
        formName: [
          {required: true, message: '模板名称不能为空', trigger: 'change'}
        ],
        formDescription: [
          {required: true, message: '模板描述不能为空', trigger: 'change'}
        ],
        statusFlag: [
          {required: true, message: '状态：1-启用，0-禁用不能为空', trigger: 'change'}
        ]
      },
      // 常用字段和自定义字段数据
      commonFields: [],
      customFields: [],
      fieldsLoading: false,

      // 字段选择器弹窗
      fieldSelectorVisible: false,

      // 排序实例
      commonSortable: null,
      customSortable: null
    }
  },
  methods: {
    init(id) {
      this.dataForm = _.cloneDeep(this.initForm)
      this.dataForm.id = id || null
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm']?.resetFields()
        // 初始化常用字段
        if (this.dataForm.id) {
          this.getData()
        } else {
          this.getCommonFields()
          // 新增时清空自定义字段
          this.customFields = []
        }
      })
    },

    // 覆盖父类的initCallback方法，处理字段展示
    initCallback(data) {
      this.dataForm = data

      // 处理字段分类展示
      if (data.fields && Array.isArray(data.fields)) {
        this.processFieldsData(data.fields)
      }

      // 初始化拖拽功能
      this.$nextTick(() => {
        this.initSortable()
      })
    },

    // 处理字段数据，拆分成常用和自定义字段
    processFieldsData(fields) {
      const [commonFields, customFields] = partition(fields, 'commonFlag')
      this.commonFields = commonFields
      this.customFields = customFields
    },

    // 获取常用字段列表
    getCommonFields() {
      this.fieldsLoading = true
      this.$http({
        url: this.$http.adornUrl('/admin/dynamic/field/list'),
        method: 'post',
        data: this.$http.adornData({
          commonFlag: true,
          currentPage: 1,
          pageSize: 100
        })
      }).then(({data}) => {
        this.fieldsLoading = false
        if (data && data.code === 0) {
          // 处理常用字段，添加排序和必填默认值
          this.commonFields = data.obj.map((item, index) => {
            return {
              ...item,
              requireFlag: true,
              fieldOrder: index + 1
            }
          })

          // 初始化拖拽功能
          this.$nextTick(() => {
            this.initSortable()
          })
        }
      }).catch(() => {
        this.fieldsLoading = false
      })
    },

    // 初始化拖拽功能
    initSortable() {
      // 如果已存在实例，先销毁
      if (this.commonSortable) {
        this.commonSortable.destroy()
      }
      if (this.customSortable) {
        this.customSortable.destroy()
      }

      // 初始化常用字段拖拽
      if (this.$refs.commonTable) {
        const el = this.$refs.commonTable.$el.querySelectorAll('.el-table__body-wrapper > table > tbody')[0]
        this.commonSortable = Sortable.create(el, {
          handle: '.drag-handle',
          animation: 150,
          onEnd: evt => {
            // 移动元素
            const itemToMove = this.commonFields.splice(evt.oldIndex, 1)[0]
            this.commonFields.splice(evt.newIndex, 0, itemToMove)
            // 更新排序
            this.updateCommonFieldsOrder()
          }
        })
      }

      // 初始化自定义字段拖拽
      if (this.$refs.customTable) {
        const el = this.$refs.customTable.$el.querySelectorAll('.el-table__body-wrapper > table > tbody')[0]
        this.customSortable = Sortable.create(el, {
          handle: '.drag-handle',
          animation: 150,
          onEnd: evt => {
            // 移动元素
            const itemToMove = this.customFields.splice(evt.oldIndex, 1)[0]
            this.customFields.splice(evt.newIndex, 0, itemToMove)
            // 更新排序
            this.updateCustomFieldsOrder()
          }
        })
      }
    },

    // 更新常用字段顺序
    updateCommonFieldsOrder() {
      // 重新计算排序值
      this.commonFields.forEach((field, index) => {
        field.fieldOrder = index + 1
      })
    },

    // 更新自定义字段顺序
    updateCustomFieldsOrder() {
      // 重新计算排序值
      this.customFields.forEach((field, index) => {
        field.fieldOrder = this.commonFields.length + index + 1
      })
    },

    // 打开字段选择弹窗
    openFieldSelectDialog() {
      this.fieldSelectorVisible = true
      this.$nextTick(() => {
        // 获取已选择的字段ID列表
        const selectedIds = [...this.commonFields, ...this.customFields].map(f => f.id)
        this.$refs.fieldSelector.init(selectedIds)
      })
    },

    // 处理字段选择器确认事件
    handleFieldSelectorConfirm(selectedFields) {
      if (selectedFields.length === 0) return

      // 计算新字段的序号
      const startOrder = this.commonFields.length + this.customFields.length + 1

      // 添加选中字段到自定义字段列表
      const newFields = selectedFields.map((field, index) => {
        return {
          ...field,
          requireFlag: false,
          fieldOrder: startOrder + index
        }
      })

      this.customFields = [...this.customFields, ...newFields]
      this.fieldSelectorVisible = false

      // 添加新字段后重新初始化拖拽
      this.$nextTick(() => {
        this.initSortable()
      })
    },

    // 移除自定义字段
    removeCustomField(index) {
      this.customFields.splice(index, 1)
      // 重新计算排序
      this.updateCustomFieldsOrder()
    },

    // 覆盖父类的submitBeforeHandle方法，在提交前汇总字段
    submitBeforeHandle() {
      // 更新所有字段顺序
      this.updateCommonFieldsOrder()
      this.updateCustomFieldsOrder()

      // 汇总所有字段
      const allFields = [
        ...this.commonFields,
        ...this.customFields
      ].map(field => {
        // 只保留需要的字段
        return {
          id: field.id,
          requireFlag: field.requireFlag,
          fieldOrder: field.fieldOrder
        }
      })

      // 设置到表单中
      this.dataForm.fields = allFields
      return true
    }
  }
}
</script>

<style lang="scss" scoped>
.drag-handle {
  cursor: move;
  transition: all 0.3s;

  &:hover {
    color: #409EFF !important;
    transform: scale(1.2);
  }
}

/* Sortable样式 */
.sortable-ghost {
  opacity: 0.8;
  background: #edf6ff !important;
}

.sortable-drag {
  opacity: 0.8;
  background: #edf6ff !important;
}
</style>
