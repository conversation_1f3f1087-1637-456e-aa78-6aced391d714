<template>
  <div class="mod-list">
    <el-form :inline="true" :model="dataForm" ref="dataForm" @keyup.enter.native="getDataList()">
      <el-form-item prop="label" label="字段名称">
        <el-input v-model="dataForm.label" placeholder="请输入字段名称" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-search" type="warning" @click="getDataList()">查询</el-button>
        <el-button @click="resetForm()"icon="el-icon-refresh-left">重置</el-button>
      </el-form-item>
    </el-form>
    <div class="f-s-c" style="padding: 15px 0; display: flex; justify-content: flex-end; align-items: center">
      <div>
        <el-button type="primary" @click="addOrUpdateHandle()" icon="el-icon-plus">新增字段</el-button>
      </div>
    </div>
    <el-table :data="dataList" border v-loading="dataListLoading" @selection-change="selectionChangeHandle" style="width: 100%;">
      <el-table-column type="index" label="序号" width="60" header-align="center" align="center" />
      <el-table-column prop="label" header-align="center" align="center" label="字段名称"/>
      <el-table-column prop="placeholder" header-align="center" align="center" label="字段输入提示"/>
      <el-table-column prop="fieldTypeText" header-align="center" align="center" label="字段类型"/>
      <el-table-column prop="fieldOption" header-align="center" align="center" label="字段内容" min-width="200">
        <template slot-scope="scope">
          <template v-if="scope.row.fieldOption && scope.row.fieldOption.length > 0">
            <div
                class="tag-container"
                style="display: flex; flex-wrap: wrap; gap: 5px; max-height: 100px; overflow-y: auto; justify-content: center; align-items: center;"
            >
              <el-tooltip 
                v-for="(option, index) in scope.row.fieldOption.slice(0, 5)" 
                :key="index"
                :content="option"
                placement="top"
                :disabled="option.length <= 10"
              >
                <el-tag 
                  type="primary" 
                  size="small" 
                  style="max-width: 150px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;"
                >
                  {{ option }}
                </el-tag>
              </el-tooltip>
              <el-tooltip 
                v-if="scope.row.fieldOption.length > 5" 
                :content="scope.row.fieldOption.join('，')"
                placement="top"
              >
                <el-tag 
                  type="success" 
                  size="small"
                >
                  +{{ scope.row.fieldOption.length - 5 }}
                </el-tag>
              </el-tooltip>
            </div>
          </template>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column prop="updateDate" header-align="center" align="center" label="更新时间"/>
      <el-table-column fixed="right" header-align="center" align="center" width="150" label="操作">
        <template slot-scope="scope">
          <el-button type="text" @click="addOrUpdateHandle(scope.row.id)">编辑</el-button>
          <el-button
              class="delete_btn"
              type="text"
              @click="deleteHandle(scope.row.id,scope.row.name)"
              :disabled="scope.row.systemFlag">删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination @size-change="sizeChangeHandle" @current-change="currentChangeHandle" :current-page="pageIndex"
                   :page-sizes="[10, 20, 50, 100]" :page-size="pageSize" :total="totalPage" layout="total, sizes, prev, pager, next, jumper"/>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"/>
  </div>
</template>

<script>
  import AddOrUpdate from '../field-add-or-update'
  import listMixin from '@/mixins/list-mixins'
  export default {
    mixins: [listMixin],
    props: {
      commonFlag: {
        type: Boolean,
        required: true
      }
    },
    data () {
      return {
        mixinOptions: {
          dataUrl: '/admin/dynamic/field/page',
          deleteUrl: '/admin/dynamic/field/removeByIds'
        },
        dataForm: {
          label: null,
          placeholder: null,
          commonFlag: this.commonFlag
        }
      }
    },
    components: {
      AddOrUpdate
    },
    methods: {
      resetForm() {
        this.$refs['dataForm']?.resetFields()
        this.getDataList()
      },
      // 覆盖mixin的query方法，添加commonFlag参数
      query() {
        this.queryBeforeHandle()
        let params = {
          ...this.dataForm,
          commonFlag: this.commonFlag
        }
        
        if (this.mixinOptions.pageable) {
          params = {
            currentPage: this.pageIndex,
            pageSize: this.pageSize,
            ...params
          }
        }
        
        let request = {
          url: this.$http.adornUrl(this.mixinOptions.dataUrl),
          method: this.mixinOptions.method
        }
        
        if (this.mixinOptions.method === 'post') {
          request.data = this.$http.adornData(params)
        } else {
          request.params = this.$http.adornParams(params)
        }
        
        this.dataListLoading = true
        this.$http(request).then(({data}) => {
          this.dataListLoading = false
          this.queryCallback(data)
        }).catch(() => {
          this.dataListLoading = false
        })
      },
      // 覆盖新增方法，传递commonFlag值
      addOrUpdateHandle(id) {
        this.addOrUpdateVisible = true
        this.$nextTick(() => {
          this.$refs.addOrUpdate.init(id, this.commonFlag)
        })
      }
    }
  }
</script>

<style lang="scss" scoped>
</style> 
