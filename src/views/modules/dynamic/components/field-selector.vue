<template>
  <el-dialog
    title="添加字段"
    :visible.sync="visible"
    width="70%"
    append-to-body
    @close="handleClose">
    <el-form :inline="true" :model="dataForm" ref="dataForm" @keyup.enter.native="getDataList()">
      <el-form-item prop="label" label="字段名称">
        <el-input v-model="dataForm.label" placeholder="请输入字段名称" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-search" type="warning" @click="getDataList()">查询</el-button>
        <el-button @click="resetForm()" icon="el-icon-refresh-left">重置</el-button>
      </el-form-item>
    </el-form>

    <el-table
      :data="dataList"
      border
      style="width: 100%"
      v-loading="dataListLoading"
      @selection-change="selectionChangeHandle">
      <el-table-column type="selection" width="55" align="center">
        <template slot-scope="scope">
          <el-checkbox 
            :disabled="excludeIds.includes(scope.row.id)"
            :value="dataListSelections.includes(scope.row) || excludeIds.includes(scope.row.id)"
          ></el-checkbox>
        </template>
      </el-table-column>
      <el-table-column type="index" label="序号" width="60" align="center" />
      <el-table-column prop="label" label="字段名称" align="center" />
      <el-table-column prop="placeholder" label="字段输入提示" align="center" />
      <el-table-column prop="fieldTypeText" label="字段类型" align="center" />
      <el-table-column prop="fieldOption" label="字段内容" align="center">
        <template slot-scope="scope">
          <template v-if="scope.row.fieldOption && scope.row.fieldOption.length > 0">
            <div class="tag-container" style="display: flex; flex-wrap: wrap; gap: 5px; max-height: 100px; overflow-y: auto; justify-content: center; align-items: center;">
              <el-tooltip
                v-for="(option, index) in scope.row.fieldOption.slice(0, 5)"
                :key="index"
                :content="option"
                placement="top"
                :disabled="option.length <= 10"
              >
                <el-tag
                  type="primary"
                  size="small"
                  style="max-width: 150px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;"
                >
                  {{ option }}
                </el-tag>
              </el-tooltip>
              <el-tooltip
                v-if="scope.row.fieldOption.length > 5"
                :content="scope.row.fieldOption.join('，')"
                placement="top"
              >
                <el-tag
                  type="success"
                  size="small"
                >
                  +{{ scope.row.fieldOption.length - 5 }}
                </el-tag>
              </el-tooltip>
            </div>
          </template>
          <span v-else>-</span>
        </template>
      </el-table-column>
    </el-table>

    <div class="pagination-footer">
      <el-pagination
        @size-change="sizeChangeHandle"
        @current-change="currentChangeHandle"
        :current-page="pageIndex"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pageSize"
        :total="totalPage"
        layout="total, sizes, prev, pager, next, jumper"
        style="text-align: right; width: 100%;" />
    </div>

    <div>
        <el-button type="primary" @click="handleConfirm">确定</el-button>
        <el-button @click="handleCancel">取消</el-button>
      </div>
  </el-dialog>
</template>

<script>
import listMixin from '@/mixins/list-mixins'

export default {
  name: 'FieldSelector',
  mixins: [listMixin],
  data () {
    return {
      visible: true,
      mixinOptions: {
        dataUrl: '/admin/dynamic/field/page'
      },
      dataForm: {
        label: null,
        commonFlag: false
      },
      excludeIds: [] // 排除已选择的字段ID
    }
  },
  methods: {
    init (excludeIds = []) {
      this.excludeIds = excludeIds
      this.resetForm()
      this.getDataList()
    },
    
    resetForm () {
      this.$refs['dataForm']?.resetFields()
      this.dataForm.label = null
    },
    
    // 重写查询前操作，添加过滤条件
    queryBeforeHandle () {
      this.dataForm.commonFlag = false // 确保只查询非常用字段
    },
    
    // 重写查询回调，过滤已选字段
    querySuccessHandle (data) {
      // 保留所有记录，但标记已选择的字段
      this.dataList = data.records
      this.totalPage = data.total
    },
    
    // 确认选择
    handleConfirm () {
      if (this.dataListSelections.length === 0) {
        this.$message.warning('请至少选择一个字段')
        return
      }
      this.$emit('confirm', this.dataListSelections)
    },
    
    // 取消选择
    handleCancel () {
      this.$emit('cancel')
    },
    
    // 关闭弹窗
    handleClose () {
      this.$emit('cancel')
    }
  }
}
</script>

<style scoped>
.pagination-footer {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  margin-top: 10px;
}
</style> 
