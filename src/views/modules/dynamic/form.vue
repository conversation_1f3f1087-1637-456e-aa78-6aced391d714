<template>
  <div class="mod-list">
    <el-form :inline="true" :model="dataForm" ref="dataForm" @keyup.enter.native="getDataList()">
      <el-form-item prop="formName" label="模板名称">
        <el-input v-model="dataForm.formName" placeholder="请输入模板名称" clearable></el-input>
      </el-form-item>
      <el-form-item prop="statusFlag" label="状态">
        <el-select v-model="dataForm.statusFlag" placeholder="请选择状态" clearable>
          <el-option label="启用中" :value="1"></el-option>
          <el-option label="已停用" :value="0"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-search" type="warning" @click="getDataList()">查询</el-button>
        <el-button @click="resetForm()"icon="el-icon-refresh-left">重置</el-button>
      </el-form-item>
    </el-form>
    <div class="f-s-c" style="padding: 15px 0; display: flex; justify-content: flex-end; align-items: center">
      <div>
        <el-button type="primary" @click="addOrUpdateHandle()" icon="el-icon-plus">新增</el-button>
      </div>
    </div>
    <el-table :data="dataList" border v-loading="dataListLoading" @selection-change="selectionChangeHandle" style="width: 100%;">
<!--      <el-table-column type="selection" header-align="center" align="center" width="50"/>-->
      <el-table-column type="index" label="序号" width="60" header-align="center" align="center" />
      <el-table-column prop="formName" header-align="center" align="center" label="模板名称"/>
      <el-table-column prop="formDescription" header-align="center" align="center" label="模板内容描述"/>
      <el-table-column prop="createDate" header-align="center" align="center" label="创建时间"/>
      <el-table-column prop="statusFlag" header-align="center" align="center" label="状态">
        <template slot-scope="scope">
          <el-tag v-if="!scope.row.statusFlag" size="small" type="danger">已停用</el-tag>
          <el-tag v-else size="small">启用中</el-tag>
        </template>
      </el-table-column>
        <el-table-column fixed="right" header-align="center" align="center" width="150" label="操作">
        <template slot-scope="scope">
          <el-button type="text" @click="addOrUpdateHandle(scope.row.id)">编辑</el-button>
          <el-button class="delete_btn" type="text" @click="deleteHandle(scope.row.id,scope.row.name)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination @size-change="sizeChangeHandle" @current-change="currentChangeHandle" :current-page="pageIndex"
                   :page-sizes="[10, 20, 50, 100]" :page-size="pageSize" :total="totalPage" layout="total, sizes, prev, pager, next, jumper"/>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"/>
  </div>
</template>

<script>
  import AddOrUpdate from './form-add-or-update'
  import listMixin from '@/mixins/list-mixins'
  export default {
    mixins: [listMixin],
    data () {
      return {
        mixinOptions: {
          dataUrl: '/admin/dynamic/form/pages',
          deleteUrl: '/admin/dynamic/form/removeByIds'
        },
        dataForm: {
          formName: null,
          moduleCode: 'zyz_activity_public_apply_form',
          statusFlag: null
        }
      }
    },
    components: {
      AddOrUpdate
    },
    methods: {
      resetForm() {
        this.$refs['dataForm']?.resetFields()
        this.getDataList()
      }
    }
  }
</script>

<style lang="scss" scoped>
</style>
