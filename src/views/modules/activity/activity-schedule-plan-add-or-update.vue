<template>
    <el-dialog :title="!dataForm.id ? '新增' : '编辑'" :close-on-click-modal="false" :visible.sync="visible"
               width="90%">
        <el-form :model="dataForm"  ref="dataForm" label-width="150px">
            <el-row :gutter="20">
                <el-col :span="12">
                    <el-form-item label="计划类型" prop="scheduleType">
                        <el-radio-group v-model="dataForm.scheduleType" disabled>
                            <el-radio :label="'schedule_type_month'">月计划</el-radio>
                            <el-radio :label="'schedule_type_quarter'">季度计划</el-radio>
                            <el-radio :label="'schedule_type_year'">年计划</el-radio>
                        </el-radio-group>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="计划数值" prop="scheduleTypeData">
                        <el-date-picker
                                v-if="dataForm.scheduleType === 'schedule_type_month'"
                                v-model="dataForm.scheduleTypeData"
                                type="month"
                                format="yyyy-MM"
                                value-format="yyyyMM"
                                :editable="false"
                                placeholder="选择月"
                                disabled
                        ></el-date-picker>
                        <span v-if="dataForm.scheduleType === 'schedule_type_quarter'">
      <!--季度时间选择控件 -->
                        <el-quarter-picker v-model="dataForm.scheduleTypeData" placeholder="选择季度" disabled
                                          /></span>
                        <el-date-picker
                                v-if="dataForm.scheduleType === 'schedule_type_year'"
                                v-model="dataForm.scheduleTypeData"
                                type="year"
                                format="yyyy"
                                value-format="yyyy"
                                :editable="false"
                                placeholder="选择年"
                                disabled
                        ></el-date-picker>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row :gutter="20">
                <el-col :span="12">
                    <el-form-item label="计划主题" prop="title" >
                        <el-input v-model="dataForm.title" placeholder="计划主题" disabled></el-input>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-table id="detail_table" :key="tableKey" ref="detailTable" :data="dataForm.detailList" border
                      style="width: 100%;">
                <el-table-column
                    prop="name"
                    width="300"
                    header-align="center"
                    align="center"
                    label="活动预告">
                </el-table-column>
                <el-table-column prop="startTime" width="300" header-align="center" align="center" label="活动起止时间"
                                 disabled>
                    <template slot-scope="scope">
                        <span>{{  moment(scope.row.startTime).format('YYYY-MM-DD HH:mm') + '-' + moment(scope.row.endTime).format('HH:mm') }}</span>
                    </template>
                </el-table-column>
                <el-table-column
                        prop="planSyncText"
                        header-align="center"
                        align="center"
                        width="120"
                        label="关联同步状态">
                    <template slot-scope="scope">
                        <el-popover trigger="hover" placement="top">
                            <p style="text-align: center">
                                {{
                                '同步时间：' + (scope.row.planSyncTime ? scope.row.planSyncTime : '')
                                }}<br>{{ scope.row.planSyncRemark }}</p>
                            <div slot="reference" class="name-wrapper">
                                <el-tag v-if="scope.row.planSync === 'sync_failure'" type="danger">{{ scope.row.planSyncText }}</el-tag>
                                <el-tag v-if="scope.row.planSync === 'sync_wait'" type="warning">{{ scope.row.planSyncText }}</el-tag>
                                <el-tag v-if="scope.row.planSync === 'sync_success'" type="success">{{ scope.row.planSyncText }}</el-tag>
                            </div>
                        </el-popover>
                    </template>
                </el-table-column>
                <el-table-column prop="activityName"  header-align="center" align="center" label="关联活动">
                    <template slot-scope="scope">
                        <el-form-item :prop="`detailList.${scope.$index}.activityName`" >
                            <el-input v-model="scope.row.activityName" readonly  @click.native="searchActivity(scope.$index)" :disabled="scope.row.syncSuccess">
                                <template #append><el-button type="primary" icon="el-icon-search" @click="searchActivity(scope.$index)" :disabled="scope.row.syncSuccess">关联活动</el-button></template>
                            </el-input>
                        </el-form-item>
                    </template>
                </el-table-column>
                <el-table-column
                        prop="activityId"
                        width="20"
                        v-if="false"
                        header-align="center"
                        align="center"
                        label="活动名称">
                </el-table-column>
                <el-table-column
                        prop="activityDateRange"
                        width="300"
                        header-align="center"
                        align="center"
                        label="活动时间">
                </el-table-column>
                <el-table-column fixed="right" width="120" header-align="center" align="center" label="操作">
                    <template slot-scope="scope">
                        <el-button v-if="scope.row.cancelEnable" type="text" size="small" @click="cancelHandle(scope.$index)">取消关联</el-button>
                        <el-button v-if="scope.row.syncEnable &&  isAuth('plan:sync')" size="small" type="text" @click="syncHandle(scope.row.planId)">同步</el-button>
                    </template>
                </el-table-column>
            </el-table>
        </el-form>
        <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="success" @click="dataFormSubmit()"
                 :disabled="submitLoading">提交</el-button>
    </span>
        <search-list v-if="searchListVisible" ref="searchList" @getActivity="getActivity" ></search-list>
    </el-dialog>
</template>

<script>

import ElQuarterPicker from './schedule-components/ElQuarterPicker.vue'
import moment from 'moment/moment'
import {uuid} from '@/utils'
import SearchList from './activity-search-list.vue'
export default {
    data() {
        return {
            moment: moment,


            tableKey: uuid(),
            submitLoading: false,
            visible: false,
            searchListVisible:false,
            posed: false,
            activityList: [],
            dataForm: {
                id: null,
                version: null,
                scheduleType: 'schedule_type_month',
                scheduleTypeData: null,
                title: null,
                auditStatus: null,
                publishOrgCode: null,
                publishOrgName: null,
                pbId: null,
                teamId: null,
                teamPublish: null,
                auditOrgCode: null,
                sync: null,
                syncTime: null,
                syncRemark: null,
                schId: null,
                autoStatus: null,
                detailList: []
            },

        }
    },
    components: {
        SearchList,
        ElQuarterPicker
    },
    methods: {
        init(id) {
            this.dataForm.id = id || null
            this.dataForm.version = 0
            this.visible = true
            this.posed = false
            this.tableKey = uuid()
            this.dataForm.detailList = []
            this.teamList = []
            this.getActivity()
            this.$nextTick(() => {
                this.$refs['dataForm']?.resetFields()
                if (this.dataForm.id) {
                    this.$http({
                        url: this.$http.adornUrl(`/admin/zyz/activity/schedule/getDetail`),
                        method: 'get',
                        params: this.$http.adornParams({
                            scheduleId: this.dataForm.id
                        })
                    }).then(({data}) => {
                        if (data && data.code === 0) {
                            this.dataForm = data.obj
                            data.obj.detailList.forEach(it => {
                                this.$set(it, 'showStartTime', moment(new Date(it.startTime)).format('YYYY-MM-DD HH:mm'))
                                this.$set(it, 'showEndTime', moment(new Date(it.endTime)).format('HH:mm'))
                                this.$set(it, 'startEndTime', [it.startTime, it.endTime])
                                if(it.activityStartTime && it.activityEndTime){
                                    it.activityDateRange=  moment(it.activityStartTime).format('YYYY-MM-DD HH:mm') + '-' + moment(it.activityEndTime).format('HH:mm')
                                }

                                //同步成功
                                it.syncSuccess= it.planSync==='sync_success'
                                // 判断取消
                                it.cancelEnable = (it.activityId && !it.syncSuccess )
                                // 判断同步
                                it.syncEnable =it.planId && it.activityId && !it.syncSuccess

                            })
                        }
                    })
                }
            })
        },
        // 表单提交
        dataFormSubmit() {
            let method = ''
            this.$refs['dataForm'].validate((valid) => {
                if (valid) {
                        method = `/admin/zyz/activity/schedule/act/plan/saveScheduleActPlan`
                        this.submitForm(method, this.dataForm)
                    }
            })
        },
        async submitForm(method, dataForm) {

            this.submitLoading = true
            this.$http({
                url: this.$http.adornUrl(method),
                method: 'post',
                data: this.$http.adornData(dataForm)
            }).then(({data}) => {
                if (data && data.code === 0) {
                    this.$message({
                        message: '操作成功',
                        type: 'success',
                        duration: 1500,
                        onClose: () => {
                            this.submitLoading = false
                            this.visible = false
                            this.$emit('refreshDataList')
                        }
                    })
                } else {
                    this.$message.error(data.msg)
                }
                this.submitLoading = false
            })
        },
        cancelHandle(index) {
            this.$set(this.dataForm.detailList[index], 'activityId',null)
            this.$set(this.dataForm.detailList[index], 'activityName', null)
            this.$set(this.dataForm.detailList[index], 'activityDateRange', null)

        },
        // 活动查询
        searchActivity(index) {

            this.searchListVisible = true
            this.$nextTick(() => {
                this.$refs.searchList.init(index)
            })
        },
        getActivity(item,index) {
            if(item){
                this.$set(this.dataForm.detailList[index], 'activityId',item.id)
                this.$set(this.dataForm.detailList[index], 'activityName', item.name)
                this.$set(this.dataForm.detailList[index], 'activityDateRange', item.timeRange)
            }

        },
        // 同步
        syncHandle(planId) {
            this.$confirm(`确定是否进行同步，请谨慎操作！`, '确认同步？', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning',
                closeOnClickModal: false
            }).then(() => {
                this.dataListLoading = true
                this.$http({
                    url: this.$http.adornUrl('/admin/zyz/platform-sync/syncScheduleActPlan'),
                    method: 'post',
                    params: this.$http.adornParams({
                        'planId': planId
                    })
                }).then(({data}) => {
                    this.dataListLoading = false
                    if (data && data.code === 0) {
                        this.$message({
                            message: '请求同步成功，市平台返回：（' + data.obj.message + '）',
                            type: data.obj.resultCode === 'SUCCESS' ? 'success' : 'warning',
                            duration: 3000,
                            onClose: () => {
                                this.init(this.dataForm.id)
                            }
                        })
                    } else {
                        this.$message.error(data.msg)
                    }
                })
            })
        },

    }
}
</script>

<style scoped lang="scss">
#detail_table ::v-deep .el-form-item {
  margin-bottom: 0px;
  margin-left: -150px;
}

#detail_table::v-deep .el-form-item__error {
  position: static;
  text-align: center;
}
</style>

