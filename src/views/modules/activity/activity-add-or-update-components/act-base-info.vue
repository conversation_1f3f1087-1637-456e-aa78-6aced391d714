<template>
  <el-card>
    <div slot="header" class="clearfix"><span>基本信息</span></div>
    <el-form :model="baseInfoForm" :rules="dataRule" ref="baseInfoForm" label-width="140px">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="活动名称" prop="name">
            <el-input v-model="baseInfoForm.name" placeholder="活动名称"/>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="所属领域" prop="fieldIds">
            <el-cascader ref="belongField" style="width: 100%" placeholder="请选择" v-model="baseInfoForm.fieldIds" :options="fields" @change="fieldsChange" clearable :props="{ label: 'typeName', value: 'typeId' }"/>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item v-if="!isTeam" label="本组织创建" prop="orgSelf">
            <el-radio-group v-model="baseInfoForm.orgSelf" @change="orgSelfChange">
              <el-radio :label="true">是</el-radio>
              <el-radio :label="false">否</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item v-if="(!isTeam) && (!baseInfoForm.orgSelf)" label="活动举办主体" prop="teamId">
            <el-select style="width: 100%" v-model="baseInfoForm.teamId" placeholder="请选择" filterable @change="teamChange">
              <el-option v-for="item in teamList" :key="item.id" :label="item.name" :value="item.id"/>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="招募对象" prop="recruitTargetArr">
            <el-checkbox-group v-model="baseInfoForm.recruitTargetArr" @change="recruitTargetArrChange">
              <el-checkbox label="rt_volunteer">志愿招募</el-checkbox>
              <el-checkbox label="rt_masses">群众参与</el-checkbox>
            </el-checkbox-group>
          </el-form-item>
        </el-col>
        <el-col :span="12" v-if="baseInfoForm.recruitTargetArr.length > 1">
          <el-form-item label="招募人数区别控制" prop="recruitNumDistinguish">
            <el-radio-group v-model="baseInfoForm.recruitNumDistinguish" @change="recruitNumDistinguishChange"><el-radio :label="true">是</el-radio><el-radio :label="false">否</el-radio></el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="6">
          <el-form-item label="报名是否需要审核" prop="applyCheck">
            <el-tooltip content="该限制仅作用于志愿招募，不限制群众参与" placement="top">
              <el-radio-group
                  v-model="baseInfoForm.applyCheck"
                  :disabled="baseInfoForm.recruitTargetArr.length === 1 && baseInfoForm.recruitTargetArr[0] === 'rt_masses'">
                <el-radio :label="true">是</el-radio>
                <el-radio :label="false">否</el-radio>
              </el-radio-group>
            </el-tooltip>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="公开招募" prop="open">
            <el-radio-group v-model="baseInfoForm.open"><el-radio :label="true">是</el-radio><el-radio :label="false">否</el-radio></el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="惠及人数" prop="benefitPeopleNum">
            <el-input-number style="width:100%" v-model="baseInfoForm.benefitPeopleNum" controls-position="right" :min="0"/>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="6">
          <el-form-item prop="auditShow"><el-checkbox v-model="baseInfoForm.auditShow">审核通过前端立即展示</el-checkbox></el-form-item>
        </el-col>
<!--        <el-col :span="6">-->
<!--          <el-form-item prop="top"><el-checkbox v-model="baseInfoForm.top">前端推荐</el-checkbox></el-form-item>-->
<!--        </el-col>-->
        <el-col :span="6">
          <el-form-item prop="checkLocation" v-if="false"><el-checkbox v-model="baseInfoForm.checkLocation">签到定位</el-checkbox></el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item  label="签到半径(m)" prop="locationDistance">
            <el-input-number :disabled="!isAssociationAdmin" style="width:100%" v-model="baseInfoForm.locationDistance" controls-position="right" :min="0"/>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item class="address-item" label="活动地址" prop="address">
            <el-input v-model="baseInfoForm.address" placeholder="活动地址" clearable @clear="addressClear"/><el-button icon="el-icon-location-outline" type="primary" @click="showMap">地图定位</el-button>
          </el-form-item>
        </el-col>
      </el-row>
        <el-row :gutter="20">
            <el-col :span="6">
                <el-form-item label="经度" prop="longitude" >
                    <el-input v-model="baseInfoForm.longitude" :placeholder="'经度'" disabled></el-input>
                </el-form-item>
            </el-col>
            <el-col :span="6">
                <el-form-item label="纬度" prop="latitude" >
                    <el-input v-model="baseInfoForm.latitude" :placeholder="'纬度'" disabled></el-input>
                </el-form-item>
            </el-col>
            <el-col :span="12">
                <el-form-item :label="'活动所在社区'" prop="activityPlaceRegionCode">
                    <el-cascader style="width: 100%" placeholder="活动所在社区" v-model="baseInfoForm.activityPlaceRegionCode" :options="regionList"
                                 :show-all-levels="false" clearable
                                 :props="{checkStrictly: true,value: 'regionCode',label: 'regionName'}"></el-cascader>
                </el-form-item>
            </el-col>
        </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="联系人" prop="contactPerson"><el-input style="width: 100%" v-model="baseInfoForm.contactPerson" placeholder="联系人"/></el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="联系电话" prop="contactPhone"><el-input style="width: 100%" v-model="baseInfoForm.contactPhone" placeholder="联系电话"/></el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="活动简介" prop="actSynopsis"><el-input style="width: 100%" type="textarea" v-model="baseInfoForm.actSynopsis" placeholder="活动简介" rows="5" maxlength="1000" show-word-limit/></el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="报名条件" prop="applicationRequirements"><el-input type="textarea" v-model="baseInfoForm.applicationRequirements" placeholder="报名条件" rows="5" maxlength="1000" show-word-limit/></el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="图片" prop="picture">
            <el-upload
                class="avatar-uploader"
                :action="this.$http.adornUrl(`/admin/oss/upload`)"
                :headers="headers"
                :data="{serverCode: uploadOptions.serverCode, media: false}"
                :show-file-list="false"
                :on-success="successHandle"
                :on-change="changHandle"
                :on-exceed="exceedHandle"
                :before-upload="beforeUploadHandle">
              <img v-if="baseInfoForm.picture" :src="$http.adornAttachmentUrl(baseInfoForm.picture)" class="avatar">
              <i v-else class="el-icon-plus avatar-uploader-icon"></i>
              <div slot="tip" class="el-upload__tip" style="color: red">尺寸建议600x1000像素，文件格式jpg、bmp或png，大小不超2M</div>
            </el-upload>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="阵地计划选择" prop="scheduleDetailId">
            <el-cascader
                style="width: 100%"
                v-model="baseInfoForm.scheduleDetailId"
                :options="scheduleOptions"
                :show-all-levels="false"
                :props="{ checkStrictly: false, emitPath: false}"
                placeholder="请选择阵地计划"
                clearable>
            </el-cascader>
          </el-form-item>
        </el-col>
        <el-col :span="12"  v-if="baseInfoForm.recruitTargetArr.includes('rt_masses')">
          <el-form-item label="群众报名模板" prop="applyPublicFormId">
            <div style="display: flex;">
              <el-select style="width: 100%" v-model="baseInfoForm.applyPublicFormId" placeholder="请选择群众报名模板" filterable clearable @change="formIdChange">
                <el-option v-for="item in formList" :key="item.id" :label="item.formName" :value="item.id"/>
              </el-select>
              <el-button type="primary" style="margin-left: 10px;" @click="previewFormTemplate" :disabled="!baseInfoForm.applyPublicFormId">模板预览</el-button>
            </div>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <el-dialog class="map-dialog" title="地图" width="80%" :close-on-click-modal="false" :visible.sync="mapVisible" :modal-append-to-body="false" :append-to-body="true">
      <AMapInfo :mapVisible.sync="mapVisible" :address.sync="baseInfoForm.address" :latitude.sync="baseInfoForm.latitude" :longitude.sync="baseInfoForm.longitude" />
    </el-dialog>

    <FormTemplatePreview
        :visible.sync="formPreviewVisible"
        :form-id="baseInfoForm.applyPublicFormId"
        :snapshot-id="baseInfoForm.applyPublicFormSnapshotId"/>
  </el-card>
</template>

<script>
import {is8lPhone, isMobile} from '@/utils/validate';
import fileUploadMixin from '@/mixins/file-upload-mixins';
import AMapInfo from "@/components/map/a-map-info";
import FormTemplatePreview from "@/components/form-template-preview";
import _ from "lodash";
import {getFathersById, treeDataTranslate} from "@/utils";

export default {
  name: "act-base-info",
  mixins: [fileUploadMixin],
  props: {
    minTimeLimit: {
      type: Number,
      default: null
    },
    maxTimeLimit: {
      type: Number,
      default: null
    },
    fieldId: {
      type: String,
      default: null,
    },
    teamId: {
      type: String,
      default: null,
    },
    recruitNumDistinguish: {
      type: Boolean,
      default: false
    },
    recruitMaintain: {
      type: Boolean,
      default: false
    },
    dockingMaintain: {
      type: Boolean,
      default: false
    },
    refreshRecruitInfo: {
      type: Function,
      default: null
    },
    refreshDockingInfo: {
      type: Function,
      default: null
    }
  },
  watch: {
      // 经纬度值是否有改变
    'baseInfoForm.longitude'(val, oldVal) {
      if((this.baseInfoForm.id && val!==this.originalLongitude) || (this.baseInfoForm.id==null && !this.baseInfoForm.copy)) {
        this.scheduleAddressChange();
      }
    },
    'baseInfoForm.latitude'(val, oldVal) {
      if((this.baseInfoForm.id && val!==this.originalLatitude) || (this.baseInfoForm.id==null && !this.baseInfoForm.copy)) {
        this.scheduleAddressChange();
      }
    },
    // 监控招募对象变化，当仅为群众时，报名审核自动设为否且不可修改
    'baseInfoForm.recruitTargetArr'(val) {
      if (val && val.length === 1 && val[0] === 'rt_masses') {
        this.baseInfoForm.applyCheck = false;
      }
    }
  },
  components: {AMapInfo, FormTemplatePreview},
  data() {
    const validateContactPhone = (rule, value, callback) => {
      if (!isMobile(value) && !is8lPhone(value)) {callback(new Error('联系电话须为手机号或区号+8位座机号例如051288888888或0512-88888888'))}
      else {callback()}
    }
    const validateAddress = (rule, value, callback) => {
      if (!this.baseInfoForm.longitude || this.baseInfoForm.longitude === '' || !this.baseInfoForm.latitude || this.baseInfoForm.latitude === '') {callback(new Error('需要活动地址经纬度，请通过地图定位选择地址！'))}
      else {callback()}
    }
    return {
      uploadOptions: {
        fieldName: 'picture',
        maxSize: 2
      },
      isAssociationAdmin: this.$store.state.user.managerCapacity === 'ASSOCIATION_ADMIN',
      isTeam: this.$store.state.user.managerCapacity === 'TEAM_ADMIN',
      teamList: [],
      fields: [],
      formList: [],
      addressChangePending: false,
      mapVisible: false,
      originRecruitTargetArr: [],
      originTeamId: null,
      originalLongitude: null,
      originalLatitude: null,
      regionList: [],
      baseInfoForm: {
        id: null,
        version: null,
        name: null,
        fieldIds: [],
        belongFieldTop: null,
        belongFieldNameTop: null,
        belongFieldEnd: null,
        belongFieldNameEnd: null,
        orgSelf: true,
        teamId: null,
        recruitTargetArr: ['rt_volunteer'],
        recruitTargets: 'rt_volunteer',
        recruitNumDistinguish: false,
        applyCheck: true,
        open: true,
        benefitPeopleNum: 0,
        auditShow: true,
        top: false,
        checkLocation: null,
        locationDistance: 300,
        address: null,
        longitude: null,
        latitude: null,
        contactPerson: null,
        contactPhone: null,
        actSynopsis: null,
        applicationRequirements: null,
        picture: null,
        activityPlaceRegionCode:null,
        applyPublicFormId: null,
        applyPublicFormSnapshotId: null,
        scheduleDetailId: null,
        copy : false
      },
      dataRule: {
        name: [{required: true, message: '活动名称不能为空', trigger: 'change'}],
        fieldIds: [{required: true, message: '所属领域不能为空', trigger: 'change'}],
        orgSelf: [{required: true, message: '是否本组织创建不能为空', trigger: 'change'}],
        teamId: [{required: true, message: '活动主体不能为空', trigger: 'change'}],
        recruitTargetArr: [{required: true, message: '招募对象不能为空', trigger: 'change'}],
        recruitNumDistinguish: [{required: true, message: '招募人数控制不能为空', trigger: 'change'}],
        applyCheck: [{required: true, message: '报名是否需要审核不能为空', trigger: 'change'}],
        open: [{required: true, message: '公开活动不能为空', trigger: 'change'}],
        locationDistance: [{required: true, message: '签到距离范围单位m不能为空', trigger: 'change'}],
        address: [{required: true, message: '活动地址不能为空', trigger: 'change'},
          {validator: validateAddress, trigger: 'change'}],
        contactPerson: [{required: true, message: '联系人不能为空', trigger: 'change'}],
        contactPhone: [
          {required: true, message: '联系电话不能为空', trigger: 'change'},
          {validator: validateContactPhone, trigger: 'change'}
        ],
        actSynopsis: [{required: true, message: '活动简介不能为空', trigger: 'change'}],
        picture: [{required: true, message: '图片不能为空', trigger: 'change'}],
        activityPlaceRegionCode: [
          {required: true, message: '请选择该活动开展地所在社区网格，支持全苏州市，选择后该社区不参与审核', trigger: 'change'},
         {type: "string", required: true, len: 12, message: '请选择该活动开展地所在社区网格，支持全苏州市，选择后该社区不参与审核', trigger: 'blur'}
        ],
      },
      formPreviewVisible: false,
      scheduleOptions: [], // 阵地计划选项
    }
  },
  mounted () {
    this.getFields()
    this.getTeams()
    this.initRegionTree()
    this.getFormList()
    this.getScheduleData()
  },
  methods: {
    init (baseInfoForm) {
      this.baseInfoForm = _.cloneDeep(baseInfoForm)
      this.originRecruitTargetArr = baseInfoForm.recruitTargetArr
      this.baseInfoForm.checkLocation = true
      this.originTeamId = baseInfoForm.teamId
      this.originalLongitude = baseInfoForm.longitude
      this.originalLatitude= baseInfoForm.latitude
    },
    getTeams() {
      this.$http({
        url: this.$http.adornUrl(`/admin/zyz/team/getOrgTeamList`),
        method: 'get',
        params: this.$http.adornParams()
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.teamList = this.getTreeData(data.obj)
        } else {
          this.teamList = []
        }
      })
    },
    getFields() {
      this.$http({
        url: this.$http.adornUrl('/admin/zyz/platform/belongFieldDict/cascade'),
        method: 'get'
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.fields = this.getTreeData(data.obj)
        } else {
          this.fields = []
        }
      })
    },
    getTreeData: function (data) {
      let that = this
      data.forEach(function (e) {
        if (!e.children || e.children.length < 1) {
          e.children = undefined
        } else {
          that.getTreeData(e.children)
        }
      })
      return data
    },
    fieldsChange(value) {
      console.log(value)
      this.baseInfoForm.belongFieldTop = value && value.length === 2 ? value[0] : null
      this.baseInfoForm.belongFieldEnd = value && value.length === 2 ? value[1] : null
      this.baseInfoForm.belongFieldNameTop = value && value.length === 2 ? this.$refs.belongField.getCheckedNodes()[0].parent.label : null
      this.baseInfoForm.belongFieldNameEnd = value && value.length === 2 ? this.$refs.belongField.getCheckedNodes()[0].label : null
      this.$emit('update:fieldId', value && value.length === 2 ? value[value.length - 1] : null)
      this.getLimitTime(value && value.length === 2 ? value[value.length - 1] : null)
    },
    getLimitTime(typeId) {
      if (!typeId) {
        this.$emit('update:minTimeLimit', null)
        this.$emit('update:maxTimeLimit', null)
        return
      }
      this.$http({
        url: this.$http.adornUrl(`/admin/zyz/platform/belongFieldDict/getByTypeId`),
        method: 'get',
        params: this.$http.adornParams({
          'typeId': typeId,
        })
      }).then(({data}) => {
        if (data && data.code === 0 && data.obj) {
          this.$emit('update:minTimeLimit', data.obj.minTimeLimit)
          this.$emit('update:maxTimeLimit', data.obj.maxTimeLimit)
        } else {
          this.$emit('update:minTimeLimit', null)
          this.$emit('update:maxTimeLimit', null)
        }
      })
    },
    orgSelfChange(value) {
      if (this.dockingMaintain) {
        this.$confirm(`修改该属性将清空对接信息，是否继续?`, '提示', {
              confirmButtonText: '继续',
              cancelButtonText: '取消',
              type: 'warning',
              closeOnClickModal: false
        }).then(() => {
          this.baseInfoForm.teamId = null
          this.originTeamId = null
          this.$emit('update:teamId', null)
          this.refreshDockingInfo()
        }).catch(() => {
          this.baseInfoForm.orgSelf = !value
        })
      } else {
        this.baseInfoForm.teamId = null
        this.originTeamId = null
        this.$emit('update:teamId', null)
        this.refreshDockingInfo()
      }
    },
    teamChange (value) {
      if (this.dockingMaintain) {
        this.$confirm(`修改该属性将清空对接信息，是否继续?`, '提示', {
          confirmButtonText: '继续',
          cancelButtonText: '取消',
          type: 'warning',
          closeOnClickModal: false
        }).then(() => {
          this.$emit('update:teamId', value)
          this.refreshDockingInfo()
          this.originTeamId = value
        }).catch(() => {
          this.baseInfoForm.teamId = this.originTeamId
        })
      } else {
        this.$emit('update:teamId', value)
        this.refreshDockingInfo()
        this.originTeamId = value
      }
    },
    recruitTargetArrChange (value) {
      if (value.length <= 1) {
        let refreshRecruitFlag = this.baseInfoForm.recruitNumDistinguish
        // 区分控制招募人数为真且招募信息维护为真时，弹出提示框
        if (refreshRecruitFlag && this.recruitMaintain) {
          this.$confirm(`修改该属性将清空招募信息，是否继续?`, '提示', {
            confirmButtonText: '继续',
            cancelButtonText: '取消',
            type: 'warning',
            closeOnClickModal: false
          }).then(() => {
            this.baseInfoForm.recruitNumDistinguish = false
            this.$emit('update:recruitNumDistinguish', false)
            this.refreshRecruitInfo()
            this.originRecruitTargetArr = value
            this.baseInfoForm.recruitTargets = value.join(',')
            // 仅群众时，报名审核设为否
            if (value.length === 1 && value[0] === 'rt_masses') {
              this.baseInfoForm.applyCheck = false
            }
          }).catch(() => {
            this.baseInfoForm.recruitTargetArr = this.originRecruitTargetArr
          })
        } else{
          this.baseInfoForm.recruitNumDistinguish = false
          this.$emit('update:recruitNumDistinguish', false)
          this.originRecruitTargetArr = value
          this.baseInfoForm.recruitTargets = value.join(',')
            // 仅群众时，报名审核设为否
            if (value.length === 1 && value[0] === 'rt_masses') {
              this.baseInfoForm.applyCheck = false
            }
          if (!this.recruitMaintain) {
            this.refreshRecruitInfo()
          }
        }
      } else {
        this.baseInfoForm.recruitNumDistinguish = false
        this.$emit('update:recruitNumDistinguish', false)
        this.originRecruitTargetArr = value
        this.baseInfoForm.recruitTargets = value.join(',')
        if (!this.recruitMaintain) {
          this.refreshRecruitInfo()
        }
      }
    },
    recruitNumDistinguishChange (value) {
      if (this.recruitMaintain) {
        this.$confirm(`修改该属性将清空招募信息，是否继续?`, '提示', {
          confirmButtonText: '继续',
          cancelButtonText: '取消',
          type: 'warning',
          closeOnClickModal: false
        }).then(() => {
          this.$emit('update:recruitNumDistinguish', value)
          this.refreshRecruitInfo()
        }).catch(() => {
          this.baseInfoForm.recruitNumDistinguish = !value
        })
      } else {
        this.$emit('update:recruitNumDistinguish', value)
        this.refreshRecruitInfo()
      }
    },
    showMap() {
      this.mapVisible = true
    },
    scheduleAddressChange() {
      if (!this.addressChangePending) {
        this.addressChangePending = true;
        this.$nextTick(() => {
          this.addressChange();
          this.addressChangePending = false;
        });
      }
    },
    addressClear() {
      this.baseInfoForm.address = null
      this.baseInfoForm.longitude = null
      this.baseInfoForm.latitude = null
      this.baseInfoForm.activityPlaceRegionCode=null
    },
    callback (res) {
      this.baseInfoForm.picture = res.obj.path
    },
   initRegionTree() {
       this.$http({
          url: this.$http.adornUrl(`/admin/zyz/sync/region/dict/all/all`),
          method: 'get',
          params: this.$http.adornParams()
      }).then(({data}) => {
          this.regionList = treeDataTranslate(data, 'regionCode', 'parentCode')
      })
  },
    addressChange() {
        if(this.baseInfoForm.longitude && this.baseInfoForm.latitude){
            this.$http({
                url: this.$http.adornUrl('/api/mini/volunteer/getRegionCodeByLocation'),
                method: 'get',
                params: this.$http.adornParams({
                    'longitude': this.baseInfoForm.longitude,
                    'latitude': this.baseInfoForm.latitude
                })
            }).then(({data}) => {
                if (data && data.code === 0) {
                    this.baseInfoForm.activityPlaceRegionCode = getFathersById(data.obj, this.regionList, 'regionCode')
                } else {
                    this.baseInfoForm.activityPlaceRegionCode =null
                }
            })
        }
    },
    getFormList() {
      this.$http({
        url: this.$http.adornUrl('/admin/dynamic/form/list'),
        method: 'post',
        data: this.$http.adornData({
          moduleCode: 'zyz_activity_public_apply_form'
        })
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.formList = data.obj || []
        } else {
          this.formList = []
        }
      })
    },
    getScheduleData() {
      this.$http({
        url: this.$http.adornUrl('/admin/zyz/activity/schedule/getCascaderData'),
        method: 'get',
        params: this.$http.adornParams()
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.scheduleOptions = data.obj || []
        } else {
          this.scheduleOptions = []
        }
      })
    },
    previewFormTemplate() {
      this.formPreviewVisible = true
    },
    formIdChange(val) {
      // 记录上一次的值
      const oldValue = this.baseInfoForm.applyPublicFormId;
      
      // 比较选择前后是否一样
      if (val !== oldValue) {
        // 不一样则设置dataform.applyPublicFormSnapshotId为null, 清空储存的快照ID, 重新生成快照
        this.baseInfoForm.applyPublicFormSnapshotId = null;
      }
    },
  }
}
</script>

<style scoped lang="scss">
  .avatar {
    width: 178px;
    height: 178px;
    display: block;
  }
  .avatar-uploader .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
  }
  .avatar-uploader .el-upload:hover {
    border-color: #409EFF;
  }
  .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 178px;
    height: 178px;
    line-height: 178px;
    text-align: center;
  }
  ::v-deep .map-dialog {
    .el-dialog__body {
      padding: 15px 20px 30px;
    }
  }
  ::v-deep .address-item {
    .el-form-item__content {
      display: flex;
      flex-direction: row;

      .el-button {
        margin-left: 15px;
      }
    }
  }
</style>
