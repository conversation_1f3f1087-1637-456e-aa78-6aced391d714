<template>
  <el-dialog
      :title="'批量新增活动时间段'"
      :close-on-click-modal="false"
      append-to-body
      width="80%"
      :visible.sync="visible">
    <div style="color: red; margin: 0 0 20px 50px">
      <h3 style="margin-top: -20px">注意事项：</h3>
      1、批量新增会清空之前的时间段数据。<br>
      2、签到时间不能晚于活动结束时间；活动结束前30分钟内签到的将不记录服务时长。<br>
      3、报名截止时间不能晚于活动结束时间，活动结束前30分钟内报名的将不记录服务时长。<br>
      4、招募人数至少得维护 1 人，分开控制志愿者招募和群众参与的须至少分别维护 1 人。
    </div>
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmit()"
             label-width="120px" style="margin-right: 20px">
      <el-row :gutter="20" v-if="minTimeLimit !== undefined && minTimeLimit !== null">
        <el-col :span="24">
          <el-form-item label-position="left" label-width="50px">
            <div v-if="minTimeLimit !== undefined && minTimeLimit !== null" style="font-size: medium; color: red">当前所属领域限制活动时间段时长为：{{ '最少' + minTimeLimit + '小时' }}
              <template v-if="maxTimeLimit !== undefined && maxTimeLimit !== null">且最多{{ ' ' + maxTimeLimit + '小时' }}</template>
          </div>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="活动日期" prop="dateList">
            <el-date-picker
                type="dates"
                style="width: 100%"
                v-model="dataForm.dateList"
                :picker-options="!addRecord ? pickerOptions : pickerOptionsRecord"
                placeholder="选择一个或多个日期">
            </el-date-picker>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20" style="margin-top: 20px">
        <el-col :span="12">
          <el-form-item label="活动时间" prop="endTime">
            <span slot="label" style="color: red">* <span style="color: black">活动时间</span></span>
            <div style="width:100%; display: flex; justify-content: space-between">
              <el-time-select
                  placeholder="起始时间"
                  v-model="dataForm.startTime"
                  value-format="HH:mm"
                  style="width: 45%"
                  :picker-options="{
                  start: '00:00',
                  step: '00:15',
                  end: '23:45',
                  maxTime: dataForm.endTime}"/>
              <el-time-select
                  placeholder="结束时间"
                  style="width: 45%"
                  v-model="dataForm.endTime"
                  value-format="HH:mm"
                  :picker-options="{
                  start: '00:00',
                  step: '00:15',
                  end: '23:45',
                  minTime: dataForm.startTime}"/>
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="是否上下架" prop="webShow">
            <el-radio-group v-model="dataForm.webShow">
              <el-radio :label="true">启用</el-radio>
              <el-radio :label="false">禁用</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20" style="margin-top: 20px">
        <el-col :span="12">
          <el-form-item label="签到时间" prop="signTime">
            <el-time-select
                @change="signTimeChange"
                placeholder="签到时间"
                style="width: 100%"
                v-model="dataForm.signTime"
                value-format="HH:mm"
                :picker-options="{
                  start: '00:00',
                  step: '00:15',
                  end: '24:00',
                  maxTime: dataForm.endTime && dataForm.endTime !== '' ? moment(moment(new Date()).format('YYYY-MM-DD') + ' ' + dataForm.endTime).add(1, 'm').format('HH:mm') : '24:00'}"/>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <div style="height:15px; color: red; font-size: xx-small; margin-left: 120px; margin-top: -15px">（必须在活动开始前的24小时完成志愿者线上报名及志愿者审核工作，否则志愿者无法购买保险）</div>
          <el-form-item label="报名结束时间" prop="applyEndTime">
            <el-time-select
                @change="applyEndTimeChange"
                placeholder="报名结束时间"
                style="width: 100%"
                v-model="dataForm.applyEndTime"
                value-format="HH:mm"
                :picker-options="{
                  start: '00:00',
                  step: '00:15',
                  end: '24:00',
                  maxTime: dataForm.endTime && dataForm.endTime !== '' ? moment(moment(new Date()).format('YYYY-MM-DD') + ' ' + dataForm.endTime).add(1, 'm').format('HH:mm') : '24:00'}"/>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20" v-if="!recruitNumDistinguish" style="margin-top: 20px">
        <el-col :span="12">
          <el-form-item label="招募总人数" prop="recruitmentNum">
            <el-input-number style="width: 100%" v-model="dataForm.recruitmentNum" controls-position="right" :min="1"/>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20" v-if="recruitNumDistinguish" style="margin-top: 20px">
        <el-col :span="12">
          <el-form-item label="志愿招募人数" prop="volunteerRecruitNum">
            <el-input-number style="width: 100%" v-model="dataForm.volunteerRecruitNum" controls-position="right" :min="1"/>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="群众参与人数" prop="massesRecruitNum">
            <el-input-number style="width: 100%" v-model="dataForm.massesRecruitNum" controls-position="right" :min="1"/>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import moment from 'moment'
import {uuid} from "@/utils";

export default {
  data() {
    const validateTimeSelectable = (rule, value, callback) => {
      if ((!this.dataForm.startTime || this.dataForm.startTime === '') && (!this.dataForm.endTime || this.dataForm.endTime === '')) {
        callback(new Error(`开始结束时间不能为空`))
        return
      }
      let date = moment(new Date()).format('YYYY-MM-DD')
      if (!this.dataForm.startTime || this.dataForm.startTime === '') {
        callback(new Error(`开始时间不能为空`))
        this.$set(this.dataForm, 'applyEndTime', this.dataForm.endTime >= '00:30' ? moment(date + ' ' + this.dataForm.endTime).subtract(30, 'm').format('HH:mm') : '00:00')
        return
      }
      if (!this.dataForm.endTime || this.dataForm.endTime === '') {
        callback(new Error(`结束时间不能为空`))
        this.$set(this.dataForm, 'signTime', this.dataForm.startTime >= '00:30' ? moment(date + ' ' + this.dataForm.startTime).subtract(30, 'm').format('HH:mm') : '00:00')
        return
      }
      if (!this.dataForm.signTime || this.dataForm.signTime === '') {
        this.$set(this.dataForm, 'signTime', this.dataForm.startTime >= '00:30' ? moment(date + ' ' + this.dataForm.startTime).subtract(30, 'm').format('HH:mm') : '00:00')
      }
      if (!this.dataForm.applyEndTime || this.dataForm.applyEndTime === '') {
        this.$set(this.dataForm, 'applyEndTime', this.dataForm.endTime >= '00:30' ? moment(date + ' ' + this.dataForm.endTime).subtract(30, 'm').format('HH:mm') : '00:00')
      }
      const h = this.calculateHoursBetween(this.dataForm.startTime, value)
      if (h < this.minTimeLimit * 60 * 60 * 1000) {
        callback(new Error(`当前所属领域限制时长最少为 ${this.minTimeLimit}小时`))
      }
      if (this.maxTimeLimit != null && (h > this.maxTimeLimit * 60 * 60 * 1000)) {
        callback(new Error(`当前所属领域限制时长最多为${this.maxTimeLimit}小时`))
      } else {
        callback()
      }
    }
    return {
      moment: moment,
      visible: false,
      addRecord: false,
      minTimeLimit: null,
      maxTimeLimit: null,
      recruitNumDistinguish: false,
      pickerOptions: {
        disabledDate(time) {
          return moment(time).isBefore(moment().subtract(1, 'd')) || moment(time).isAfter(moment().add(30, 'd'))
        }
      },
      pickerOptionsRecord: {
        disabledDate(time) {
          return moment(time).isBefore(moment().subtract(4, 'd')) || moment(time).isAfter(moment().add(30, 'd'))
        }
      },
      dataForm: {
        dateList: [],
        recruitmentNum: 1,
        volunteerRecruitNum: 1,
        massesRecruitNum: 1,
        startTime: null,
        endTime: null,
        signTime: null,
        applyEndTime: null,
        webShow: true
      },
      dataRule: {
        dateList: [
          {required: true, message: '活动日期不能为空', trigger: 'change'}
        ],
        recruitmentNum: [
          {required: true, message: '招募总人数不能为空', trigger: 'change'}
        ],
        volunteerRecruitNum: [
          {required: true, message: '志愿招募人数不能为空', trigger: 'change'}
        ],
        massesRecruitNum: [
          {required: true, message: '群众参与人数不能为空', trigger: 'change'}
        ],
        startTime: [
          {required: true, message: '开始时间不能为空', trigger: 'blchangeur'}
        ],
        endTime: [
          // {required: true, message: '结束时间不能为空', trigger: 'change'},
          {validator: validateTimeSelectable, trigger: 'change'}
        ],
        signTime: [
          {required: true, message: '签到时间不能为空', trigger: 'change'}
        ],
        applyEndTime: [
          {required: true, message: '报名结束时间不能为空', trigger: 'change'}
        ],
        webShow: [
          {required: true, message: '是否上下架不能为空', trigger: 'change'}
        ]
      }
    }
  },
  components: {
    moment
  },
  methods: {
    init(addRecord, minTimeLimit, maxTimeLimit, recruitNumDistinguish) {
      this.addRecord = addRecord || false
      this.minTimeLimit = minTimeLimit !== undefined && minTimeLimit !== null ? minTimeLimit : null
      this.maxTimeLimit = maxTimeLimit !== undefined && maxTimeLimit !== null ? maxTimeLimit : null
      this.recruitNumDistinguish = recruitNumDistinguish || false
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm']?.resetFields()
        this.dataForm.startTime = null
        this.dataForm.recruitmentNum = 0
        this.dataForm.volunteerRecruitNum = 0
        this.dataForm.massesRecruitNum = 0
      })
    },
    signTimeChange(value) {
      this.$set(this.dataForm, 'signTime', value)
    },
    applyEndTimeChange(value) {
      this.$set(this.dataForm, 'applyEndTime', value)
    },
    calculateHoursBetween(startTime, endTime) {
      // 将时间格式转换为数组
      let start = startTime.split(':')
      let end = endTime.split(':')
      // 将时间转换为 Date 类型
      let startDate = new Date(0, 0, 0, start[0], start[1])
      let endDate = new Date(0, 0, 0, end[0], end[1])
      // 计算时间戳差异
      let diff = endDate.getTime() - startDate.getTime()
      // 返回结果
      return diff
    },
    // 表单提交
    dataFormSubmit() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.visible = false
          this.$emit('refreshDataList', this.dataForm)
        }
      })
    }
  }
}
</script>
