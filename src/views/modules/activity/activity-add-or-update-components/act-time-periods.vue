<template>
  <el-card style="margin-top: 20px">
    <div slot="header" class="clearfix">
      <span>招募信息</span>
    </div>
    <div style="display: flex; justify-content: space-between; margin-bottom: 10px">
      <div>
        <div style="font-size: medium; font-weight: bolder; color: red; " v-if="fieldId && fieldId !== ''">当前所属领域限制活动时间段时长为：最少{{ '' + minTimeLimit }}小时
          <template v-if="maxTimeLimit && maxTimeLimit !== ''">且最多{{ '' + maxTimeLimit }}小时</template>
        </div>
        <br v-if="fieldId && fieldId !== ''">
        <span style="font-size: small; color: red">
          1、签到时间不能晚于活动结束时间；活动结束前30分钟内签到的将不记录服务时长。<br>
          2、报名截止时间不能晚于活动结束时间，活动结束前30分钟内报名的将不记录服务时长。<br>
          3、招募人数至少得维护 1 人，分开控制志愿者招募和群众参与的须至少分别维护 1 人。
        </span>
      </div>
      <div>
        <el-button icon="el-icon-plus" type="primary" style="margin-bottom: 10px"  @click="addHandle()">新增活动时段</el-button>
        <el-button icon="el-icon-circle-plus" type="warning" style="margin-left: 5px; margin-bottom: 10px"  @click="addOrUpdateHandle()">批量新增</el-button>
      </div>
    </div>
    <el-form :model="activityTimeForm" ref="activityTimeForm" :rules="activityTimeForm.activityTimeRules">
      <el-table id="time_range_table" :key="tableKey" ref="timeRangeTable" :data="activityTimeForm.activityTimeList" border style="width: 100%;">
        <el-table-column prop="startTime" width="450" header-align="center" align="center" label="活动起止时间">
          <template slot-scope="scope">
            <el-form-item v-if="scope.row.editFlag" :prop="`activityTimeList.${scope.$index}.startEndTime`" :rules='activityTimeForm.activityTimeRules.startEndTime'>
              <single-day-datetime-range-picker
                  v-model="scope.row.startEndTime"
                  :date-options="addRecord ? pickerOptionsRecord : pickerOptions"
                  :size="'mini'"
                  @input="(value) => refreshStartEndTime(value, scope.$index)"/>
            </el-form-item>
            <span v-else>{{ scope.row.showStartTime }} -- {{scope.row.showEndTime}}</span>
          </template>
        </el-table-column>
        <el-table-column prop="signTimeShow" header-align="center" align="center" label="签到时间">
          <template slot-scope="scope">
            <el-form-item v-if="scope.row.editFlag" :prop="`activityTimeList.${scope.$index}.signTimeShow`" :rules='activityTimeForm.activityTimeRules.signTimeShow'>
              <el-time-select
                  placeholder="签到时间"
                  :style="recruitNumDistinguish ? {width: '140px'} : {width: '160px'}"
                  v-model="scope.row.signTimeShow"
                  size="mini"
                  clearable
                  value-format="HH:mm"
                  @change="(value) => signTimeChange(value, scope.$index)"
                  :picker-options="{
                  start: '00:00',
                  step: '00:15',
                  end: '24:00',
                  maxTime: scope.row.startEndTime && scope.row.startEndTime.length === 2 ? moment(scope.row.startEndTime[1]).add(1, 'm').format('HH:mm') : undefined}"/>
<!--              <el-date-picker-->
<!--                  ref="gain"-->
<!--                  v-model="scope.row.signTime"-->
<!--                  type="datetime"-->
<!--                  placeholder="签到时间"-->
<!--                  clearable-->
<!--                  size="mini"-->
<!--                  format="yyyy-MM-dd HH:mm:ss"-->
<!--                  value-format="yyyy-MM-dd HH:mm:ss"-->
<!--                  style="width: 100%"/>-->
            </el-form-item>
            <span v-else>{{ scope.row.signTime }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="applyEndTimeShow" header-align="center" align="center" label="报名结束时间">
          <template slot-scope="scope">
            <el-form-item v-if="scope.row.editFlag" :prop="`activityTimeList.${scope.$index}.applyEndTimeShow`" :rules='activityTimeForm.activityTimeRules.applyEndTimeShow'>
              <el-time-select
                  placeholder="报名结束时间"
                  :style="recruitNumDistinguish ? {width: '140px'} : {width: '160px'}"
                  v-model="scope.row.applyEndTimeShow"
                  size="mini"
                  clearable
                  value-format="HH:mm"
                  @change="(value) => applyEndTimeChange(value, scope.$index)"
                  :picker-options="{
                  start: '00:00',
                  step: '00:15',
                  end: '24:00' ,
                  maxTime: scope.row.startEndTime && scope.row.startEndTime.length === 2 ? moment(scope.row.startEndTime[1]).add(1, 'm').format('HH:mm') : undefined}"/>
<!--              <el-date-picker-->
<!--                  ref="gain"-->
<!--                  v-model="scope.row.applyEndTime"-->
<!--                  placeholder="报名结束时间"-->
<!--                  type="datetime"-->
<!--                  size="mini"-->
<!--                  format="yyyy-MM-dd HH:mm:ss"-->
<!--                  value-format="yyyy-MM-dd HH:mm:ss"-->
<!--                  style="width: 100%"/>-->
            </el-form-item>
            <span v-else>{{ scope.row.applyEndTime }}</span>
          </template>
        </el-table-column>
        <el-table-column v-if="!recruitNumDistinguish" width="200" prop="recruitmentNum" header-align="center" align="center" label="招募人数">
          <template slot-scope="scope">
            <el-form-item v-if="scope.row.editFlag" :prop="`activityTimeList.${scope.$index}.recruitmentNum`" :rules='activityTimeForm.activityTimeRules.recruitmentNum'>
              <el-input-number ref="gain" v-model="scope.row.recruitmentNum" size="mini" style="width: 100%"  :min="0"/>
            </el-form-item>
            <span v-else>{{ scope.row.recruitmentNum }}</span>
          </template>
        </el-table-column>
        <el-table-column v-if="recruitNumDistinguish" header-align="center" align="center" label="招募人数">
          <el-table-column prop="volunteerRecruitNum" width="150" header-align="center" align="center" label="志愿招募">
            <template slot-scope="scope">
              <el-form-item v-if="scope.row.editFlag" :prop="`activityTimeList.${scope.$index}.volunteerRecruitNum`" :rules='activityTimeForm.activityTimeRules.volunteerRecruitNum'>
                <el-input-number ref="gain" v-model="scope.row.volunteerRecruitNum" size="mini" style="width: 100%" :min="1"/>
              </el-form-item>
              <span v-else>{{ scope.row.volunteerRecruitNum }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="massesRecruitNum" width="150" header-align="center" align="center" label="群众参与">
            <template slot-scope="scope">
              <el-form-item v-if="scope.row.editFlag" :prop="`activityTimeList.${scope.$index}.massesRecruitNum`" :rules='activityTimeForm.activityTimeRules.massesRecruitNum'>
                <el-input-number ref="gain" v-model="scope.row.massesRecruitNum" size="mini" style="width: 100%" :min="1"/>
              </el-form-item>
              <span v-else>{{ scope.row.massesRecruitNum }}</span>
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column prop="webShow" header-align="center" width="160" align="center" label="是否上下架">
          <template slot-scope="scope">
            <el-form-item  :prop="`activityTimeList.${scope.$index}.webShow`" :rules='activityTimeForm.activityTimeRules.webShow'>
              <el-switch size="mini" active-color="#13ce66" inactive-color="#ff4949" v-model="scope.row.webShow"/>
            </el-form-item>
          </template>
        </el-table-column>
        <el-table-column fixed="right" width="120" header-align="center" align="center" label="操作">
          <template slot-scope="scope">
            <el-button type="text" v-if="!scope.row.editFlag" size="small" @click="updateHandle(scope.$index)">修改</el-button>
            <el-button type="text" size="small" @click="deleteHandle(scope.$index)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-form>
    <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="timeCreateCallback"/>
  </el-card>
</template>

<script>
import AddOrUpdate from './act-time-period-add-or-update'
import moment from "moment";
import _ from "lodash";
import { uuid } from '@/utils';

export default {
  name: "act-time-periods",
  components: {AddOrUpdate},
  props: ['addRecord', 'recruitNumDistinguish', 'minTimeLimit', 'maxTimeLimit', 'fieldId', 'startTime', 'endTime', 'applyStartTime', 'applyEndTime', 'recruitMaintain'],
  data() {
    const validateTimeSelectable = (rule, value, callback) => {
      if (value.length === 0) {
        callback(new Error('招募起止时间未维护或维护不完全！'))
      }
      const startTime = new Date(value[0]).getTime(),
            endTime = new Date(value[1]).getTime(),
            hourMs = 60 * 60 * 1000,
            interval = endTime - startTime,
            minTimeLimit = this.minTimeLimit,
            maxTimeLimit = this.maxTimeLimit
      if (interval >= 24 * hourMs) {
        callback(new Error('活动时间须选择在当天一天之内！'))
      } else if (interval < minTimeLimit * hourMs) {
        callback(new Error('当前所属领域限制时长最少为' + minTimeLimit + '小时'))
      } else if (maxTimeLimit != null && (interval > maxTimeLimit * hourMs)) {
        callback(new Error('当前所属领域限制时长最多为' + maxTimeLimit + '小时'))
      } else {
        callback()
      }
    }
    return {
      moment: moment,
      tableKey: null,
      pickerOptions: {
        disabledDate(time) {return moment(time).isBefore(moment().subtract(1, 'd')) || moment(time).isAfter(moment().add(30, 'd'))}
      },
      pickerOptionsRecord: {
        disabledDate(time) {return moment(time).isBefore(moment().subtract(4, 'd')) || moment(time).isAfter(moment().add(30, 'd'))}
      },
      addOrUpdateVisible: false,
      activityTimeForm: {
        activityTimeList: [],
        activityTimeRules: {
          startEndTime: [{validator: validateTimeSelectable, trigger: 'change'}],
          signTimeShow: [
            { required: true, message: '签到时间不能为空', trigger: 'change' }
          ],
          applyEndTimeShow: [
            { required: true, message: '报名结束时间不能为空', trigger: 'change' }
          ],
          recruitmentNum: [
            { required: true, message: '招募人数不能为空', trigger: 'change' },
              { pattern: /^\+?[1-9]\d*$/, message: '请输入大于0的正整数'}
          ],
          volunteerRecruitNum: [
            { required: true, message: '志愿招募人数不能为空', trigger: 'change' }
          ],
          massesRecruitNum: [
            { required: true, message: '群众参与人数不能为空', trigger: 'change' }
          ],
          webShow: [
            { required: true, message: '上下架不能为空', trigger: 'change' }
          ]
        }
      }
    }
  },
  mounted() {
    this.tableKey = uuid()
  },
  methods: {
    init (actTimePeriods) {
      this.tableKey = uuid()
      this.$set(this.activityTimeForm, 'activityTimeList', actTimePeriods)
      if (this.activityTimeForm.activityTimeList.length > 0) {
        let that = this
        _.forEach(this.activityTimeForm.activityTimeList, function (item) {
          that.$set(item, 'signTimeShow', moment(item.signTime).format('HH:mm'))
          that.$set(item, 'applyEndTimeShow', moment(item.applyEndTime).format('HH:mm'))
        })
      }
      console.log(this.activityTimeForm.activityTimeList)
      if (actTimePeriods.length > 0) {
        this.$emit('update:recruitMaintain', true)
      }
    },
    addOrUpdateHandle() {
      this.addOrUpdateVisible = true
      this.$nextTick(() => {
        this.$refs.addOrUpdate.init(this.addRecord,  this.minTimeLimit, this.maxTimeLimit, this.recruitNumDistinguish)
      })
    },
    timeCreateCallback(obj) {
      if (obj !== null) {
        this.activityTimeForm.activityTimeList = []
        obj.dateList.forEach(it => {
          this.activityTimeForm.activityTimeList.push({
            'startTime': this.combineDateTime(it, obj.startTime),
            'endTime': this.combineDateTime(it, obj.endTime),
            'startEndTime': [this.combineDateTime(it, obj.startTime), this.combineDateTime(it, obj.endTime)],
            'showStartTime': moment(this.combineDateTime(it, obj.startTime)).format('YYYY-MM-DD HH:mm'),
            'showEndTime': obj.endTime,
            "signTime": this.combineDateTime(it, obj.signTime),
            "signTimeShow": obj.signTime,
            "applyEndTime": this.combineDateTime(it, obj.applyEndTime),
            "applyEndTimeShow": obj.applyEndTime,
            "webShow": obj.webShow,
            'recruitmentNum': obj.recruitmentNum,
            'volunteerRecruitNum': obj.volunteerRecruitNum,
            'massesRecruitNum': obj.massesRecruitNum,
            'editFlag': false,  // 可编辑标识
          })
        })
        this.activityTimeForm.activityTimeList = _.sortBy(this.activityTimeForm.activityTimeList, ['startTime'])
      }
    },
    combineDateTime(date, time) {
      // 将日期和时间转换为 UTC 时间
      return moment(date).format('YYYY-MM-DD') + ' ' + time + ':00'
    },
    refreshRecruitInfo() {
      this.activityTimeForm.activityTimeList = []
      this.$emit('update:recruitMaintain', false)
      this.tableKey = uuid()
      this.$emit('update:startTime', null)
      this.$emit('update:endTime', null)
      this.$emit('update:applyStartTime', null)
      this.$emit('update:applyEndTime', null)
    },
    addHandle() {
      let timeRange = {
        startTime: '',
        endTime: '',
        startEndTime: [],
        signTime: '',
        signTimeShow: '',
        applyEndTime: '',
        applyEndTimeShow: '',
        webShow: true,
        recruitmentNum: 0,
        volunteerRecruitNum: 1,
        massesRecruitNum: 1,
        editFlag: true,  // 可编辑标识
      }
      this.activityTimeForm.activityTimeList.push(timeRange)
      this.$emit('update:recruitMaintain', true)
    },
    updateHandle(index) {
      this.$set(this.activityTimeForm.activityTimeList[index], 'editFlag', true)
      this.$emit('update:recruitMaintain', true)
    },
    deleteHandle(id) {
      this.activityTimeForm.activityTimeList.splice(id, 1)
      if (this.activityTimeForm.activityTimeList.length === 0) {
        this.$emit('update:recruitMaintain', false)
      } else {
        this.$emit('update:recruitMaintain', true)
      }
    },
    signTimeChange(value, index) {
      let startEndTime = this.activityTimeForm.activityTimeList[index].startEndTime
      let date = startEndTime && startEndTime.length === 2 ? moment(startEndTime[0]).format('YYYY-MM-DD') : null
      if (date) {
        this.$set(this.activityTimeForm.activityTimeList[index], 'signTime', date + ' ' + value + ':00')
      } else {
        this.$set(this.activityTimeForm.activityTimeList[index], 'signTime', '')
      }
    },
    applyEndTimeChange(value, index) {
      let startEndTime = this.activityTimeForm.activityTimeList[index].startEndTime
      let date = startEndTime && startEndTime.length === 2 ? moment(startEndTime[0]).format('YYYY-MM-DD') : null
      if (date) {
        this.$set(this.activityTimeForm.activityTimeList[index], 'applyEndTime', date + ' ' + value + ':00')
      } else {
        this.$set(this.activityTimeForm.activityTimeList[index], 'applyEndTime', '')
      }
    },
    refreshStartEndTime (value, index) {
      this.$set(this.activityTimeForm.activityTimeList[index], 'startTime', value[0])
      this.$set(this.activityTimeForm.activityTimeList[index], 'endTime', value[1])
      this.$set(this.activityTimeForm.activityTimeList[index], 'startEndTime', value)
      let startTime = value[0] ? moment(value[0]) : null
      let endTime = value[1] ? moment(value[1]) : null
      let Date = moment(value[0]).format('YYYY-MM-DD')
      let signTime, applyEndTime
      if (!startTime) {
        signTime = ''
      } else {
        if (startTime.format('HH:mm:ss') > '00:30:00') {
          signTime = startTime.subtract(30, 'm').format('HH:mm')
        } else {
          signTime = '00:00'
        }
      }
      if (!endTime) {
        applyEndTime = ''
      } else {
        if (endTime.format('HH:mm:ss') > '00:30:00') {
          applyEndTime = endTime.subtract(30, 'm').format('HH:mm')
        } else {
          applyEndTime = '00:00'
        }
      }
      if (signTime && signTime !== '') {
        this.$set(this.activityTimeForm.activityTimeList[index], 'signTimeShow', signTime)
        this.$set(this.activityTimeForm.activityTimeList[index], 'signTime', Date + ' ' + signTime + ':00')
      }
      if (applyEndTime && applyEndTime !== '') {
        this.$set(this.activityTimeForm.activityTimeList[index], 'applyEndTimeShow', applyEndTime)
        this.$set(this.activityTimeForm.activityTimeList[index], 'applyEndTime', Date + ' ' + applyEndTime + ':00')
      }
    }
  }
}
</script>

<style scoped lang="scss">
#time_range_table ::v-deep .el-form-item {
  margin-bottom: 0px;
}
#time_range_table ::v-deep .el-form-item__error {
  position: static;
  text-align: center;
}
</style>
