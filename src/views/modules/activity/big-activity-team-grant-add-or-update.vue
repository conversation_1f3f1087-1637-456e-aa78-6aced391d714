<template>
  <el-dialog class="common-dialog" title="新增授权" :visible.sync="visible"
             :close-on-click-modal="false" :modal-append-to-body="false" width="40%">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmit()" label-width="120px">
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="选择已有团队" prop="teamId">
<!--            <el-select v-model="dataForm.teamId" filterable remote placeholder="请输入团队名称模糊搜索"-->
<!--                       :remote-method="searchTeamByName" :loading="teamSearchLoading" style="width: 90%">-->
<!--              <el-option v-for="item in teamSearchList" :key="item.id" :label="item.name" :value="item.id">-->
<!--              </el-option>-->
<!--            </el-select>-->
            <el-select v-model="dataForm.teamId" filterable placeholder="请输入团队名称模糊搜索" style="width: 90%">
              <el-option v-for="item in teamSearchList" :key="item.id" :label="item.name" :value="item.id">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()" :disabled="dataFormLoading">确认授权</el-button>
    </span>
  </el-dialog>
</template>

<script>
  import editMixin from '@/mixins/edit-mixins'
  export default {
    mixins: [editMixin],
    data () {
      return {
        dataForm: {
          teamId: null
        },
        teamSearchList: [],
        teamSearchLoading: false,
        dataRule: {
          teamId: [
            { required: true, message: '团队不能为空', trigger: 'change' }
          ]
        }
      }
    },
    methods: {
      init () {
        this.visible = true
        this.$nextTick(() => {
          this.$refs['dataForm'].resetFields()
          this.searchTeamByName(null)
        })
      },
      searchTeamByName(query) {
        this.teamSearchLoading = true
        this.$http({
          url: this.$http.adornUrl('/admin/zyz/team/getTeamListByNameForBAGrant'),
          method: 'get',
          params: this.$http.adornParams({
            teamName: query
          })
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.teamSearchList = data.obj
          } else {
            this.teamSearchList = []
          }
          this.teamSearchLoading = false
        })
      },
      dataFormSubmit () {
        this.$refs['dataForm'].validate((valid) => {
          if (valid) {
            this.dataFormLoading = true
            this.$http({
              url: this.$http.adornUrl('/admin/zyz/team/grantBigActivity/teamGrant'),
              method: 'get',
              params: this.$http.adornParams({
                'teamId': this.dataForm.teamId
              })
            }).then(({data}) => {
              if (data && data.code === 0) {
                this.$message({
                  message: '操作成功',
                  type: 'success',
                  duration: 1500,
                  onClose: () => {
                    this.dataFormLoading = false
                    this.visible = false
                    this.$emit('refreshDataList')
                  }
                })
              } else {
                this.dataFormLoading = false
                this.$message.error(data.msg)
              }
            })
          }
        })
      }
    }
  }
</script>

<style lang="scss" scoped>
</style>
