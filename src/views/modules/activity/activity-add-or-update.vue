<template>
  <div>
    <el-dialog :title="!id ? (addRecord ? '补录' : '新增') : '修改'" :close-on-click-modal="false" width="95%"
               :visible.sync="visible">
      <div style="color: red; margin: 0 0 20px 20px">
        <h3 style="margin-top: -20px">购买保险提醒：</h3>
        1、志愿者报名及审核请务必在活动开始前提前一天的21:00前完成，否则志愿者将无法获得保险。<br>
        2、志愿者务必填写真实的姓名及身份证号码，否则志愿者将无法获得保险。<br>
        3、发布活动的组织管理员，务必填写真实的姓名及身份证号码，否则本组织发布的活动都无法获得保险。<br>
        4、不按规则要求操作的导致购买保险失败的，一律后果自负。
      </div>
      <base-info
          :key="baseInfoFormKey"
          :min-time-limit.sync="minTimeLimit"
          :max-time-limit.sync="maxTimeLimit"
          :field-id.sync="fieldId"
          :team-id.sync="teamId"
          :recruit-num-distinguish.sync="recruitNumDistinguish"
          :recruit-maintain="recruitMaintain"
          :docking-maintain="dockingMaintain"
          :refresh-recruit-info="refreshRecruitInfo"
          :refresh-docking-info="refreshDockingInfo"
          ref="baseInfoForm"/>
      <time-periods
          :key="timePeriodsFormKey"
          :add-record="addRecord"
          :min-time-limit="minTimeLimit"
          :max-time-limit="maxTimeLimit"
          :field-id="fieldId"
          :start-time.sync="startTime"
          :end-time.sync="endTime"
          :apply-start-time.sync="applyStartTime"
          :apply-end-time.sync="applyEndTime"
          :recruit-maintain.sync="recruitMaintain"
          :recruit-num-distinguish="recruitNumDistinguish"
          ref="timePeriodsForm"/>
      <docking-info
          :key="dockingInfoFormKey"
          :act-id="id"
          :team-id.sync="teamId"
          :copy="copy"
          :docking-maintain.sync="dockingMaintain"
          ref="dockingInfoForm"/>
      <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataSubmit(false)" :disabled="submitLoading">保存草稿</el-button>
      <el-button type="success" @click="dataSubmit(true)" :disabled="submitLoading">提交审核</el-button>
    </span>
    </el-dialog>
  </div>
</template>

<script>
import moment from 'moment'
import BaseInfo from './activity-add-or-update-components/act-base-info'
import TimePeriods from './activity-add-or-update-components/act-time-periods'
import DockingInfo from './activity-add-or-update-components/act-docking-info'
import {getFathersById, uuid} from "@/utils";

export default {
  data() {
    return {
      visible: false,
      baseInfoFormKey: null,
      timePeriodsFormKey: null,
      dockingInfoFormKey: null,
      id: null,
      copy: false,
      addRecord: false,
      minTimeLimit: null,
      maxTimeLimit: null,
      teamId: null,
      fieldId: null,
      recruitNumDistinguish: false,
      recruitMaintain: false,
      dockingMaintain: false,
      startTime: null,
      endTime: null,
      applyStartTime: null,
      applyEndTime: null,
      submitLoading: false
    }
  },
  components: {BaseInfo, TimePeriods, DockingInfo},
  methods: {
    init(id, isCopy, isRecordAdd) {
      this.id = isCopy ? null : (id || null)
      this.baseInfoFormKey = uuid()
      this.timePeriodsFormKey = uuid()
      this.dockingInfoFormKey = uuid()
      this.addRecord = isRecordAdd
      this.copy = isCopy
      this.teamId = null
      this.fieldId = null
      this.minTimeLimit = null
      this.maxTimeLimit = null
      this.recruitNumDistinguish = false
      this.recruitMaintain = false
      this.dockingMaintain = false
      this.startTime = null
      this.endTime = null
      this.applyStartTime = null
      this.applyEndTime = null
      this.visible = true
      this.$nextTick(() => {
        if (id) {
          this.$http({
            url: this.$http.adornUrl(`/admin/zyz/activity/getDetailForEdit`),
            method: 'get',
            params: this.$http.adornParams({id: id})
          }).then(({data}) => {
            if (data && data.code === 0) {
              this.startTime = data.obj.startTime
              this.endTime = data.obj.endTime
              this.applyStartTime = data.obj.applyStartTime
              this.applyEndTime = data.obj.applyEndTime
              this.addRecord = data.obj.addRecord
              this.teamId = data.obj.baseInfo.teamId
              this.fieldId = data.obj.baseInfo.belongFieldEnd
              this.$refs.baseInfoForm.getLimitTime(data.obj.baseInfo.belongFieldEnd)
              this.recruitNumDistinguish = data.obj.baseInfo.recruitNumDistinguish
              this.$nextTick(() => {
                if (isCopy) {
                  data.obj.baseInfo.id = null
                  data.obj.baseInfo.version = null
                  data.obj.baseInfo.top = false
                  data.obj.baseInfo.copy = isCopy
                  data.obj.baseInfo.locationDistance = 300
                }
                this.$refs.baseInfoForm.init(data.obj.baseInfo)
                  if(data.obj.baseInfo.activityPlaceRegionCode){
                      this.$refs.baseInfoForm.activityPlaceRegionCode = getFathersById(data.obj.baseInfo.activityPlaceRegionCode, this.$refs.baseInfoForm.regionList, 'regionCode')
                  }

                if (!isCopy) {
                  data.obj.actTimePeriods.forEach(it => {
                    this.$set(it, 'showStartTime', moment(new Date(it.startTime)).format('YYYY-MM-DD HH:mm'))
                    this.$set(it, 'showEndTime', moment(new Date(it.endTime)).format('HH:mm'))
                    this.$set(it, 'startEndTime', [it.startTime, it.endTime])
                  })
                  this.$refs.timePeriodsForm.init(data.obj.actTimePeriods)
                }
                this.$refs.dockingInfoForm.init(data.obj.dockingInfo)
              })
            }
          })
        }
      })
    },
    refreshRecruitInfo() {
      this.$nextTick(() => {
        this.$refs.timePeriodsForm.refreshRecruitInfo()
      })
    },
    refreshDockingInfo() {
      this.$nextTick(() => {
        this.$refs.dockingInfoForm.refreshDockingInfo()
      })
    },
    dataSubmit(isRender){
      if(this.$refs.baseInfoForm.baseInfoForm.longitude=='120.723503' && this.$refs.baseInfoForm.baseInfoForm.latitude=='31.324015') {
          this.$confirm(`签到位置是默认经纬度，请检查位置是否存在异常，异常定位会导致签到失败？`, '提示', {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'warning'
          }).then(() => {
              this.dataFormSubmit(isRender)
          }).catch(() => {
          })
      } else {
          this.dataFormSubmit(isRender)
          }
      },
    dataFormSubmit(isRender) {
      if(!this.$refs.baseInfoForm.baseInfoForm.id){
        this.$refs.baseInfoForm.baseInfoForm.checkLocation=true
      }
      let baseInfoForm = this.$refs.baseInfoForm.baseInfoForm
        let tmpRegionCode = baseInfoForm.activityPlaceRegionCode
         this.$refs.baseInfoForm.baseInfoForm.activityPlaceRegionCode = tmpRegionCode && Array.isArray(tmpRegionCode) ? baseInfoForm.activityPlaceRegionCode[baseInfoForm.activityPlaceRegionCode.length - 1] : tmpRegionCode
      if (this.copy) {
        baseInfoForm.id = null
        baseInfoForm.version = null
      }
      let activityTimeList = this.$refs.timePeriodsForm.activityTimeForm.activityTimeList
      let dockingForm = this.$refs.dockingInfoForm.dockingForm
      this.getStartEndTime(activityTimeList)
      console.log(activityTimeList)
      console.log(this.startTime)
      console.log(this.endTime)
      let dataForm = {}
      dataForm.addRecord = this.addRecord
      dataForm.startTime = this.startTime
      dataForm.endTime = this.endTime
      dataForm.applyStartTime = this.applyStartTime
      dataForm.applyEndTime = this.applyEndTime
      dataForm.baseInfo = baseInfoForm
      dataForm.dockingInfo = dockingForm
      dataForm.actTimePeriods = activityTimeList
      let method = ''
      if (isRender) {
        method = '/admin/zyz/activity/render'
      } else {
        method = `/admin/zyz/activity/${!baseInfoForm.id ? 'saveToDraft' : 'updateToDraft'}`
      }
      if (isRender) {
        this.$confirm(`确定进行提交操作?`, '提示',
            {confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning', closeOnClickModal: false}
        ).then(() => {
          this.submitFun(method, dataForm)
        })
      } else {
        this.submitFun(method, dataForm)
      }
    },
    async submitFun(method, dataForm) {
      let validate = false
      await this.$refs.baseInfoForm.$refs['baseInfoForm'].validate((valid) => {
        validate = valid
      })
      if (!validate) {
        this.$message.error('活动基本信息有误，请检查')
        return
      }
      this.$refs.dockingInfoForm.$refs['dockingForm'].validate((valid) => {
        validate = valid
      })
      if (!validate) {
        this.$message.error('活动对接信息有误，请检查')
        return
      }
      this.$refs.timePeriodsForm.$refs['activityTimeForm'].validate((valid) => {
        validate = valid
      })
      if (!validate) {
        this.$message.error('活动招募信息有误，请检查')
        return
      }
      this.submitLoading = true
      this.$http({
        url: this.$http.adornUrl(method),
        method: 'post',
        data: this.$http.adornData(dataForm)
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.$message({
            message: '操作成功',
            type: 'success',
            duration: 1500,
            onClose: () => {
              this.submitLoading = false
              this.visible = false
              this.$emit('refreshDataList')
            }
          })
        } else {
          this.$message.error(data.msg)
        }
        this.submitLoading = false
      })
    },
    getStartEndTime(activityTimeList) {
      if (!activityTimeList || activityTimeList.length === 0) {
        activityTimeList = this.$refs.timePeriodsForm.activityTimeForm.activityTimeList
      }
      if (activityTimeList && activityTimeList.length > 0) {
        let activityTimeListStart = null, activityTimeListEnd = null, activityTimeListApplyEnd = null
        for (let i = 0; i < activityTimeList.length; i++) {
          let activity = activityTimeList[i]
          // 将开始时间和结束时间转换为 Date 类型
          let startDate = moment(new Date(activity.startEndTime[0])).format('YYYY-MM-DD HH:mm:ss')
          let endDate = moment(new Date(activity.startEndTime[1])).format('YYYY-MM-DD HH:mm:ss')
          let applyEndDate = moment(new Date(activity.applyEndTime)).format('YYYY-MM-DD HH:mm:ss')
          if (activityTimeListStart === null || startDate < activityTimeListStart) {
            activityTimeListStart = startDate
          }
          if (activityTimeListEnd === null || endDate > activityTimeListEnd) {
            activityTimeListEnd = endDate
          }
          if (activityTimeListApplyEnd === null || applyEndDate > activityTimeListApplyEnd) {
            activityTimeListApplyEnd = applyEndDate
          }
          // 判断是否需要更新最早开始时间和最晚结束时间
          if ((!this.startTime && this.startTime !== '') || startDate < this.startTime) {
            this.startTime = startDate
          }
          if ((!this.endTime && this.endTime !== '') || endDate > this.endTime) {
            this.endTime = endDate
          }
          if ((!this.applyEndTime && this.applyEndTime !== '') || applyEndDate > this.applyEndTime) {
            this.applyEndTime = applyEndDate
          }
        }
        if ((!this.startTime && this.startTime !== '') || activityTimeListStart !== this.startTime) {
          this.startTime = activityTimeListStart
        }
        if ((!this.endTime && this.endTime !== '') || activityTimeListEnd !== this.endTime) {
          this.endTime = activityTimeListEnd
        }
        if ((!this.applyEndTime && this.applyEndTime !== '') || activityTimeListApplyEnd !== this.applyEndTime) {
          this.applyEndTime = activityTimeListApplyEnd
        }
      }
    }
  }
}
</script>
<style lang="scss" scoped>
</style>
