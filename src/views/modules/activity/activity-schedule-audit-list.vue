<template>
    <div class="mod-config">
        <el-form :inline="true" :model="dataForm" ref="dataForm" @keyup.enter="queryPage()">
            <el-form-item label="计划主题" prop="key" style="padding-right: 170px">
                <el-input v-model="dataForm.key" placeholder="计划主题" clearable style="width: 180%"></el-input>
            </el-form-item>
            <el-form-item :label="'上级组织'" prop="publishOrgCodes">
                <el-cascader placeholder="请选择" v-model="dataForm.publishOrgCodes" :options="orgList"
                             :disabled="this.dataForm.id"
                             :show-all-levels="false" clearable :props="{ checkStrictly: true, value: 'code', label: 'name' }"
                             @change="(value) => { handleChange(value, 'org') }"></el-cascader>
            </el-form-item>
            <el-form-item label="发布时间" prop="timeRange">
                <el-date-picker v-model="dataForm.timeRange" clearable type="daterange"
                                value-format="yyyy-MM-dd" align="right" start-placeholder="开始时间" end-placeholder="结束时间">
                </el-date-picker>
            </el-form-item>
            <el-form-item label="计划类型" prop="scheduleType">
                <el-dict :code="'activity_schedule_type'" v-model="dataForm.scheduleType"></el-dict>
            </el-form-item>
            <el-form-item label="计划数值" prop="scheduleTypeData" v-if="dataForm.scheduleType">
                <el-date-picker
                    v-if="dataForm.scheduleType === 'schedule_type_month'"
                    v-model="dataForm.scheduleTypeData"
                    type="month"
                    format="yyyy-MM"
                    value-format="yyyy-MM"
                    :editable="false"
                    placeholder="选择月"
                ></el-date-picker>
                <span v-if="dataForm.scheduleType === 'schedule_type_quarter'">
      <!--季度时间选择控件 -->
                        <el-quarter-picker v-model="dataForm.scheduleTypeData" placeholder="选择季度"
                        /></span>
                <el-date-picker
                    v-if="dataForm.scheduleType === 'schedule_type_year'"
                    v-model="dataForm.scheduleTypeData"
                    type="year"
                    format="yyyy"
                    value-format="yyyy"
                    :editable="false"
                    placeholder="选择年"
                ></el-date-picker>
            </el-form-item>
            <el-form-item v-if="!dataForm.needAudit" label="审核状态" prop="auditStatus">
                <el-select v-model="dataForm.auditStatus" placeholder="请选择" clearable>
                    <el-option
                            v-for="item in [{label: '待审核', value: 'act_schedule_wait_audit'}, {label: '审核通过', value: 'act_schedule_audit_success'}, {label: '驳回', value: 'act_schedule_reject'}]"
                            :key="item.value" :label="item.label" :value="item.value">
                    </el-option>
                </el-select>
            </el-form-item>
            <el-form-item  prop="needAudit">
                <el-checkbox v-model="dataForm.needAudit">需要我审核的</el-checkbox>
            </el-form-item>
            <el-form-item>
                <el-button icon="el-icon-search" type="warning" @click="queryPage()">查询</el-button>
                <el-button @click="resetForm()" icon="el-icon-refresh-left">重置</el-button>
            </el-form-item>
        </el-form>
        <div class="f-s-c" style="padding: 15px 0;display: flex;justify-content:flex-end;align-items: center ">
            <div>
                <el-button icon="el-icon-s-check" :disabled="dataListSelections.length <= 0"
                           type="primary" @click="auditHandle(null, null, true)">批量通过
                </el-button>
            </div>
        </div>
        <el-table
                :data="dataList"
                border
                v-loading="dataListLoading"
                @selection-change="selectionChangeHandle"
                style="width: 100%;">
            <el-table-column
                    type="selection"
                    header-align="center"
                    align="center"
                    width="50">
            </el-table-column>
            <el-table-column
                    prop="title"
                    header-align="center"
                    align="center"
                    label="计划主题">
                <template slot-scope="scope">
                    <a style="cursor: pointer" @click="detailHandle(scope.row.id)">{{ scope.row.title }}</a>
                </template>
            </el-table-column>
            <el-table-column
                    prop="scheduleTypeText"
                    header-align="center"
                    align="center"
                    width="80"
                    label="计划类型">
            </el-table-column>
            <el-table-column
                    prop="scheduleTypeData"
                    header-align="center"
                    align="center"
                    width="120"
                    label="计划数值">
            </el-table-column>
            <el-table-column
                    prop="publishOrgName"
                    header-align="center"
                    align="center"
                    label="发布单位">
            </el-table-column>
            <el-table-column
                    prop="creatorName"
                    header-align="center"
                    align="center"
                    width="80"
                    label="发布人">
            </el-table-column>
            <el-table-column
                    prop="createDate"
                    header-align="center"
                    align="center"
                    width="180"
                    label="发布时间">
            </el-table-column>
            <el-table-column
                    prop="auditStatusText"
                    header-align="center"
                    align="center"
                    width="80"
                    label="审核状态">
            </el-table-column>
            <el-table-column
                    fixed="right"
                    header-align="center"
                    align="center"
                    width="150"
                    label="操作">
                label="操作">
                <template #default="scope">
                    <el-button v-if="scope.row.canAudit" type="text" size="small"
                               @click="auditHandle(scope.row.id, scope.row.name, true)">通过
                    </el-button>
                    <el-button v-if="scope.row.canAudit" type="text" size="small"
                               @click="auditHandle(scope.row.id, scope.row.name, false)">驳回
                    </el-button>

                </template>
            </el-table-column>
        </el-table>
        <div class="com-pagination">
            <el-pagination
                    @size-change="sizeChangeHandle"
                    @current-change="currentChangeHandle"
                    :current-page="pageIndex"
                    :page-sizes="[10, 20, 50, 100]"
                    :page-size="pageSize"
                    :total="totalPage"
                    layout="total, sizes, prev, pager, next, jumper"
            />
        </div>
        <!-- 弹窗, 新增 / 修改 -->
        <detail v-if="detailVisible" ref="detail" ></detail>
    </div>
</template>

<script>
import Detail from './activity-schedule-detail'
import moment from "moment/moment";
import ElQuarterPicker from "@/views/modules/activity/schedule-components/ElQuarterPicker.vue";

export default {
    data() {
        return {
            dataForm: {
                key: null,
                scheduleType: null,
                scheduleTypeData: null,
                publishOrgCodes: [],
                publishOrgCode: null,
                needAudit: true,
                auditStatus: '',
            },
            dataList: [],
            orgList: [],
            fields: [],
            pageIndex: 1,
            pageSize: 10,
            totalPage: 0,
            dataListLoading: false,
            addOrUpdateVisible: false,
            dataListSelections: [],
            detailVisible:false
        }
    },
    components: {
        ElQuarterPicker,
        Detail
    },
    activated() {
        this.getOrg()
        this.queryPage()
    },
    methods: {
        dateTypeSelectChange(val) {
            this.dataForm.scheduleTypeData = null
        },
        queryPage() {
            this.pageIndex = 1
            this.getDataList()
        },
        // 获取数据列表
        getDataList() {
            this.dataListLoading = true
            this.$http({
                url: this.$http.adornUrl('/admin/zyz/activity/schedule/pagesForAudit'),
                method: 'post',
                data: this.$http.adornData({
                    'currentPage': this.pageIndex,
                    'pageSize': this.pageSize,
                    'key': this.dataForm.key,
                    'needAudit':this.dataForm.needAudit,
                    'auditStatus':this.dataForm.auditStatus,
                    'publishOrgCode': this.dataForm.publishOrgCode,
                    'startTime': this.dataForm.timeRange && this.dataForm.timeRange.length === 2 ? this.dataForm.timeRange[0] + ' 00:00:00' : null,
                    'endTime': this.dataForm.timeRange && this.dataForm.timeRange.length === 2 ? this.dataForm.timeRange[1] + ' 23:59:59' : null,
                    'scheduleType':this.dataForm.scheduleType,
                    'scheduleTypeData': this.dataForm.scheduleTypeData

                })
            }).then(({data}) => {
                if (data && data.code === 0) {
                    this.dataList = data.obj.records
                    this.dataList.forEach(it => {
                        it.canAudit = it.auditStatus === 'act_schedule_wait_audit' && it.auditOrgCode === this.$store.state.user
                            .orgCode
                    })
                    this.totalPage = data.obj.total
                } else {
                    this.$message.error(data.msg)
                    this.dataList = []
                    this.totalPage = 0
                }
                this.dataListLoading = false
            })
        },
        // 每页数
        // 每页数
        sizeChangeHandle(val) {
            this.pageSize = val
            this.pageIndex = 1
            this.getDataList()
        },
        // 当前页
        currentChangeHandle(val) {
            this.pageIndex = val
            this.getDataList()
        },
        // 多选
        selectionChangeHandle(val) {
            this.dataListSelections = val
        },
        // 重置
        resetForm() {
            this.$nextTick(() => {
                this.$refs['dataForm'].resetFields()
                this.getDataList()
            })
        },
        //获取所属区域
        getOrg() {
            this.$http({
                url: this.$http.adornUrl(`/admin/org/getOrgTree`),
                method: 'get',
                params: this.$http.adornParams()
            }).then(({data}) => {
                if (data && data.code === 0) {
                    this.orgList = this.getTreeData(data.obj)
                } else {
                    this.orgList = []
                }
            })
        },
        // *处理点位分类最后children数组为空的状态
        getTreeData: function (data) {
            var that = this
            // 循环遍历json数据
            data.forEach(function (e) {
                if (!e.children || e.children.length < 1) {
                    e.children = undefined
                } else {
                    // children若不为空数组，则继续 递归调用 本方法
                    that.getTreeData(e.children)
                }
            })
            return data
        },
        handleChange(value, type) {
            if (type === 'field') {
                this.dataForm.fieldId = value[value.length - 1]
            }
            if (type === 'org') {
                this.dataForm.publishOrgCode = value && value.length > 0 ? value[value.length - 1] : null
            }
        },
        auditHandle(id, name, status) {
            if (!id && this.dataListSelections.length === 0) {
                this.$message.warning('未选中需要审核的活动记录！')
                return
            }
            if (!id) {
                let ids = this.dataListSelections.map(item => {
                    return item.id
                })
                id = ids.join(',')
            }
            this.$confirm(`确定${status ? '审核通过' : '驳回'}` + (name ? '"' + name + '"' : '') + '阵地计划？', '系统提示', {
                customClass: 'audit_pass_msg_box',
                confirmButtonText: '确认',
                cancelButtonText: '取消',
                showInput: !status,
                inputPlaceholder: '请输入驳回意见……',
                inputType: 'textarea',
                closeOnClickModal: false
            }).then((val) => {
                this.$http({
                    url: this.$http.adornUrl('/admin/zyz/activity/schedule/audit'),
                    method: 'get',
                    params: this.$http.adornParams({
                        'ids': id,
                        'status': status,
                        'remark': val.value
                    })
                }).then(({
                             data
                         }) => {
                    if (data && data.code === 0) {
                        this.$message.success(status ? '审核通过成功！' : '驳回成功！')
                        this.queryPage()
                    } else {
                        this.$message.error(status ? '审核通过失败！' : '驳回失败！' + data.msg)
                    }
                })
            })
            setTimeout(() => {
                var content = document.getElementsByClassName('audit_pass_msg_box')[0].childNodes[1]
                content.style.paddingLeft = '60px'
                content.style.paddingRight = '60px'
                var submitBtn = document.getElementsByClassName('audit_pass_msg_box')[0].childNodes[2]
                    .childNodes[1]
                submitBtn.style.backgroundColor = '#F56C6C'
                submitBtn.style.borderColor = '#F56C6C'
            }, 50)
        },
        detailHandle(id) {
            this.detailVisible = true
            this.$nextTick(() => {
                this.$refs.detail.init(id)
            })
        },
    }
}
</script>
