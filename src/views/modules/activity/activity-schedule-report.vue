<template>
    <div class="mod-config">
        <el-form :inline="true" :model="dataForm" ref="dataForm" @keyup.enter="queryPage()">
            <el-form-item :label="'上级组织'" prop="publishOrgCodes">
                <el-cascader placeholder="请选择" v-model="dataForm.publishOrgCodes" :options="orgList"
                             :disabled="this.dataForm.id"
                             :show-all-levels="false" clearable
                             :props="{ checkStrictly: true, value: 'code', label: 'name' }"
                             @change="(value) => { handleChange(value, 'org') }"></el-cascader>
            </el-form-item>
            <el-form-item label="计划年月" prop="scheduleTypeData">
                <el-date-picker
                        v-model="dataForm.scheduleTypeData"
                        type="month"
                        format="yyyyMM"
                        value-format="yyyyMM"
                        :editable="false"
                        placeholder="选择月"
                ></el-date-picker>
            </el-form-item>
            <el-form-item>
                <el-button icon="el-icon-search" type="warning" @click="queryPage()">查询</el-button>
                <el-button @click="resetForm()" icon="el-icon-refresh-left">重置</el-button>
            </el-form-item>
        </el-form>
        <div class="f-s-c" style="padding: 15px 0;display: flex;justify-content:flex-end;align-items: center ">
            <div>
                <el-button icon="el-icon-download" type="success" v-loading.fullscreen.lock="fullscreenLoading" @click="exportScheduleReport()">导出</el-button>
            </div>
        </div>
        <el-table
                :data="dataList"
                border
                v-loading="dataListLoading"
                style="width: 100%;">
            <el-table-column
                    prop="orgName"
                    header-align="center"
                    align="center"
                    label="组织">
            </el-table-column>
            <el-table-column
                    prop="scheduleTypeData"
                    header-align="center"
                    align="center"
                    label="月份">
            </el-table-column>
            <el-table-column
                    prop="actSchedulePassNum"
                    header-align="center"
                    align="center"
                    label="审核通过">
            </el-table-column>
            <el-table-column
                    prop="actScheduleProcessingNum"
                    header-align="center"
                    align="center"
                    label="审核中">
            </el-table-column>
            <el-table-column
                    prop="actScheduleRejectNum"
                    header-align="center"
                    align="center"
                    label="审核不通过">
            </el-table-column>
        </el-table>
        <div class="com-pagination">
            <el-pagination
                    @size-change="sizeChangeHandle"
                    @current-change="currentChangeHandle"
                    :current-page="pageIndex"
                    :page-sizes="[10, 20, 50, 100]"
                    :page-size="pageSize"
                    :total="totalPage"
                    layout="total, sizes, prev, pager, next, jumper"
            />
        </div>
        <!-- 弹窗, 新增 / 修改 -->
        <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"></add-or-update>
        <detail v-if="detailVisible" ref="detail" ></detail>
        <plan-add-or-update v-if="planAddOrUpdateVisible" ref="planAddOrUpdate" @refreshDataList="getDataList"></plan-add-or-update>
    </div>
</template>

<script>
import AddOrUpdate from './activity-schedule-add-or-update.vue'
import PlanAddOrUpdate from './activity-schedule-plan-add-or-update.vue'
import ElQuarterPicker from "@/views/modules/activity/schedule-components/ElQuarterPicker.vue";
import Detail from './activity-schedule-detail'
import moment from "moment";

export default {
    data() {
        return {
            dataForm: {
                key: null,
                scheduleType: null,
                scheduleTypeData: moment(new Date()).format('YYYYMM'),
                publishOrgCodes: [],
                publishOrgCode: null,
            },
            dataList: [],
            orgList: [],
            pageIndex: 1,
            pageSize: 10,
            totalPage: 0,
            dataListLoading: false,
            addOrUpdateVisible: false,
            planAddOrUpdateVisible:false,
            detailVisible:false,
            fullscreenLoading: false,
        }
    },
    components: {
        Detail,
        ElQuarterPicker,
        AddOrUpdate,
        PlanAddOrUpdate
    },
    activated() {
        this.getOrg()
        this.queryPage()
    },
    methods: {
        dateTypeSelectChange(val) {
            this.dataForm.scheduleTypeData = null
        },
        queryPage() {
            this.pageIndex = 1
            this.getDataList()
        },
        // 获取数据列表
        getDataList() {
            this.dataListLoading = true
            this.$http({
                url: this.$http.adornUrl('/admin/zyz/activity/schedule/pagesForReport'),
                method: 'post',
                data: this.$http.adornData({
                    'currentPage': this.pageIndex,
                    'pageSize': this.pageSize,
                    'publishOrgCode': this.dataForm.publishOrgCode,
                    'scheduleTypeData': this.dataForm.scheduleTypeData
                })
            }).then(({data}) => {
                if (data && data.code === 0) {
                    this.dataList = data.obj.records
                    this.dataList.forEach(item => {
                    })
                    this.totalPage = data.obj.total
                } else {
                    this.dataList = []
                    this.totalPage = 0
                }
                this.dataListLoading = false
            })
        },
        // 每页数
        sizeChangeHandle(val) {
            this.pageSize = val
            this.pageIndex = 1
            this.getDataList()
        },
        // 当前页
        currentChangeHandle(val) {
            this.pageIndex = val
            this.getDataList()
        },
        // 多选
        selectionChangeHandle(val) {
            this.dataListSelections = val
        },
        // 重置
        resetForm() {
            this.$nextTick(() => {
                this.$refs['dataForm'].resetFields()
                this.getDataList()
            })
        },
        // 新增 / 修改
        addOrUpdateHandle(id) {
            this.addOrUpdateVisible = true
            this.$nextTick(() => {
                this.$refs.addOrUpdate.init(id)
            })
        },
        // 新增 / 修改
        planAddOrUpdateHandle(id) {
            this.planAddOrUpdateVisible = true
            this.$nextTick(() => {
                this.$refs.planAddOrUpdate.init(id)
            })
        },
        //获取所属区域
        getOrg() {
            this.$http({
                url: this.$http.adornUrl(`/admin/org/getOrgTree`),
                method: 'get',
                params: this.$http.adornParams()
            }).then(({data}) => {
                if (data && data.code === 0) {
                    this.orgList = this.getTreeData(data.obj)
                } else {
                    this.orgList = []
                }
            })
        },
        // *处理点位分类最后children数组为空的状态
        getTreeData: function (data) {
            var that = this
            // 循环遍历json数据
            data.forEach(function (e) {
                if (!e.children || e.children.length < 1) {
                    e.children = undefined
                } else {
                    // children若不为空数组，则继续 递归调用 本方法
                    that.getTreeData(e.children)
                }
            })
            return data
        },
        handleChange(value, type) {
            if (type === 'field') {
                this.dataForm.fieldId = value[value.length - 1]
            }
            if (type === 'org') {
                this.dataForm.publishOrgCode = value && value.length > 0 ? value[value.length - 1] : null
            }
        },
        // 删除
        deleteHandle(id) {
            var ids = id ? [id] : this.dataListSelections.map(item => {
                return item.id
            })
            this.$confirm(`确定进行[${id ? '删除' : '批量删除'}]操作?`, '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning',
                closeOnClickModal: false
            }).then(() => {
                this.$http({
                    url: this.$http.adornUrl('/admin/zyz/activity/removeByIds'),
                    method: 'get',
                    params: this.$http.adornParams({
                        'ids': ids.join(',')
                    })
                }).then(({data}) => {
                    if (data && data.code === 0) {
                        this.$message({
                            message: '操作成功',
                            type: 'success',
                            duration: 1500,
                            onClose: () => {
                                this.getDataList()
                            }
                        })
                    } else {
                        this.$message.error(data.msg)
                    }
                })
            })
        },
        // 提交
        render(id) {
            this.$confirm(`确定进行提交操作?`, '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning',
                closeOnClickModal: false
            }).then(() => {
                this.$http({
                    url: this.$http.adornUrl('/admin/zyz/activity/schedule/renderById'),
                    method: 'get',
                    params: this.$http.adornParams({
                        'id': id
                    })
                }).then(({data}) => {
                    if (data && data.code === 0) {
                        this.$message({
                            message: '操作成功',
                            type: 'success',
                            duration: 1500,
                            onClose: () => {
                                this.getDataList()
                            }
                        })
                    } else {
                        this.$message.error(data.msg)
                    }
                })
            })
        },
        recallHandle(id) {
            this.$confirm(`确定进行撤回操作?撤回后将变为草稿状态`, '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning',
                closeOnClickModal: false
            }).then(() => {
                this.$http({
                    url: this.$http.adornUrl('/admin/zyz/activity/schedule/recall'),
                    method: 'get',
                    params: this.$http.adornParams({
                        'id': id
                    })
                }).then(({data}) => {
                    if (data && data.code === 0) {
                        this.$message({
                            message: '操作成功',
                            type: 'success',
                            duration: 1500,
                            onClose: () => {
                                this.getDataList()
                            }
                        })
                    } else {
                        this.$message.error(data.msg)
                    }
                })
            })
        },
        detailHandle(id) {
            this.detailVisible = true
            this.$nextTick(() => {
                this.$refs.detail.init(id)
            })
        },
        // 同步
        syncHandle(scheduleId) {
            this.$confirm(`确定是否进行同步，请谨慎操作！`, '确认同步？', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning',
                closeOnClickModal: false
            }).then(() => {
                this.dataListLoading = true
                this.$http({
                    url: this.$http.adornUrl('/admin/zyz/platform-sync/syncActSchedule'),
                    method: 'post',
                    params: this.$http.adornParams({
                        'scheduleId': scheduleId
                    })
                }).then(({data}) => {
                    this.dataListLoading = false
                    if (data && data.code === 0) {
                        this.$message({
                            message: '请求同步成功，市平台返回：（' + data.obj.message + '）',
                            type: data.obj.resultCode === 'SUCCESS' ? 'success' : 'warning',
                            duration: 3000,
                            onClose: () => {
                                this.getDataList()
                            }
                        })
                    } else {
                        this.$message.error(data.msg)
                    }
                })
            })
        },
        // 上下架
        updateAutoStatus(id, autoStatus) {
            this.$confirm(`确定进行[${!autoStatus ? '上架' : '下架'}]操作?`, '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning',
                closeOnClickModal: false
            }).then(() => {
                this.$http({
                    url: this.$http.adornUrl('/admin/zyz/activity/schedule/updateAutoStatus'),
                    method: 'get',
                    params: this.$http.adornParams({
                        'id': id
                    })
                }).then(({data}) => {
                    if (data && data.code === 0) {
                        this.$message({
                            message: '操作成功',
                            type: 'success',
                            duration: 1500,
                            onClose: () => {
                                this.getDataList()
                            }
                        })
                    } else {
                        this.$message.error(data.msg)
                    }
                })
            })
        },
        //导出
        exportScheduleReport() {
            this.fullscreenLoading = true;
            this.$http({
                url: this.$http.adornUrl('/admin/zyz/activity/schedule/exportScheduleReport'),
                method: 'post',
                responseType: 'arraybuffer',
                data: this.$http.adornData({
                    'currentPage': this.pageIndex,
                    'pageSize': this.pageSize,
                    'publishOrgCode': this.dataForm.publishOrgCode,
                    'scheduleTypeData': this.dataForm.scheduleTypeData
                })
            }).then(({data}) => {
                if (data.code && data.code !== 0) {
                    this.$message.error('导出失败')
                } else {
                    this.fullscreenLoading = false
                    let blob = new Blob([data], {type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8'})
                    let objectUrl = URL.createObjectURL(blob)
                    let a = document.createElement('a')
                    // window.location.href = objectUrl
                    a.href = objectUrl
                    a.download = '阵地报表.xlsx'
                    a.click()
                    URL.revokeObjectURL(objectUrl)
                    this.$message({
                        type: 'success',
                        message: '导出成功'
                    })
                }
            })
        },
    }
}
</script>
