<template>
    <div class="mod-config">
        <el-form :inline="true" :model="dataForm" ref="dataForm" @keyup.enter="queryPage()">
            <el-form-item label="关键字" prop="key" style="padding-right: 170px">
                <el-input v-model="dataForm.key" placeholder="活动名称/联系人/联系方式/活动地址/活动主体" clearable style="width: 180%"></el-input>
            </el-form-item>
            <el-form-item label="活动时间" prop="timeRange">
                <el-date-picker v-model="dataForm.timeRange" clearable type="daterange" value-format="yyyy-MM-dd"
                                align="right" start-placeholder="开始时间" end-placeholder="结束时间">
                </el-date-picker>
            </el-form-item>
            <el-form-item label="审核状态" prop="auditStatus">
                <el-dict :code="'activity_show_audit_status'" v-model="dataForm.auditStatus"></el-dict>
            </el-form-item>
            <el-form-item>
                <el-button icon="el-icon-search" type="warning" @click="queryPage()">查询</el-button>
                <el-button @click="resetForm()" icon="el-icon-refresh-left">重置</el-button>
            </el-form-item>
        </el-form>
        <div class="f-s-c" style="padding: 15px 0;display: flex;justify-content:flex-end;align-items: center ">
            <div>
                <el-button icon="el-icon-refresh" :disabled="dataListSelections.length <= 0"
                           type="primary" @click="batchUpdateAutoStatus()">{{ batchButtonText }}
                </el-button>
            </div>
        </div>
        <el-table
                :data="dataList"
                border
                v-loading="dataListLoading"
                @selection-change="selectionChangeHandle"
                @select-all="handleSelectAll"
                ref="multipleTable"
                style="width: 100%;">
            <el-table-column
                type="selection"
                :selectable="checkSelectable"
                :reserve-selection="true"
                header-align="center"
                align="center"
                width="50">
            </el-table-column>
            <el-table-column
                prop="activityName"
                header-align="center"
                align="center"
                min-width="150"
                label="活动名称">
                <template slot-scope="scope">
                    <a style="cursor: pointer" @click="detailHandle(scope.row.activityTimePeriodId,scope.row.id)">{{ scope.row.activityName }}</a>
                </template>
            </el-table-column>
            <el-table-column
                prop="timeRange"
                header-align="center"
                align="center"
                min-width="120"
                label="时段">
            </el-table-column>
            <el-table-column
                    prop="content"
                    header-align="center"
                    min-width="220"
                    align="center"
                    label="公示内容">
                <template slot-scope="scope">
                    <el-tooltip class="item" effect="dark" :content="scope.row.content" placement="top">
                        <div style="display: -webkit-box; -webkit-line-clamp: 6; -webkit-box-orient: vertical; overflow: hidden; text-overflow: ellipsis; white-space: normal;">
                            {{ scope.row.content }}
                        </div>
                    </el-tooltip>
                </template>
            </el-table-column>
            <el-table-column
                    prop="picList"
                    header-align="center"
                    align="center"
                    min-width="280"
                    label="公示图片列表">
                <template slot-scope="scope">
                    <el-image  style="width:80px;height:80px;border:none;" v-for="(item, index) in scope.row.picList" :key="index":src="$http.adornAttachmentUrl(item)"
                              :preview-src-list="[$http.adornAttachmentUrl(item)]">
                    </el-image>
                </template>
            </el-table-column>
            <el-table-column
                prop="creatorName"
                header-align="center"
                align="center"
                width="80"
                label="提交人"/>
            <el-table-column
                prop="auditStatusText"
                header-align="center"
                align="center"
                width="80"
                label="审核状态"/>
            <el-table-column
                prop="syncText"
                header-align="center"
                align="center"
                width="110"
                label="公示同步状态">
                <template slot-scope="scope">
                    <el-popover trigger="hover" placement="top">
                        <p style="text-align: center">
                            {{
                                '同步时间：' + (scope.row.syncTime ? scope.row.syncTime : '')
                            }}<br>{{ scope.row.syncRemark }}</p>
                        <div slot="reference" class="name-wrapper">
                            <el-tag v-if="scope.row.sync === 'sync_failure'" type="danger">{{ scope.row.syncText }}</el-tag>
                            <el-tag v-if="scope.row.sync === 'sync_wait'" type="warning">{{ scope.row.syncText }}</el-tag>
                            <el-tag v-if="scope.row.sync === 'sync_success'" type="success">{{ scope.row.syncText }}</el-tag>
                        </div>
                    </el-popover>
                </template>
            </el-table-column>
            <el-table-column
                    fixed="right"
                    header-align="center"
                    align="center"
                    width="100"
                    label="操作">
                <template #default="scope">
                    <el-button size="small" type="text" v-if="scope.row.enable" @click="addOrUpdateHandle(scope.row.activityTimePeriodId,scope.row.id)">公示内容上传</el-button>
                    <el-button size="small" type="text" v-if="scope.row.renderEnable" @click="render(scope.row.id)">提交审核</el-button>
                    <el-button size="small" type="text"  v-if="scope.row.recallEnable" @click="recallHandle(scope.row.id)">撤回</el-button>
                    <el-button size="small" type="text" v-if="scope.row.syncEnable && isAuth('show:sync')"  @click="syncHandle(scope.row.id)">同步</el-button>
                    <el-button  size="small" type="text" v-if="scope.row.autoEnable" @click="updateAutoStatus(scope.row.id, scope.row.autoStatus)">
                        {{ !scope.row.autoStatus ? '上架' : '下架' }}
                    </el-button>
                </template>
            </el-table-column>
        </el-table>
        <div class="com-pagination">
            <el-pagination
                    @size-change="sizeChangeHandle"
                    @current-change="currentChangeHandle"
                    :current-page="pageIndex"
                    :page-sizes="[10, 20, 50, 100]"
                    :page-size="pageSize"
                    :total="totalPage"
                    layout="total, sizes, prev, pager, next, jumper"
            />
        </div>
        <!-- 弹窗, 新增 / 修改 -->
        <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"></add-or-update>
        <detail v-if="detailVisible" ref="detail" ></detail>
    </div>
</template>

<script>
import AddOrUpdate from './activity-time-show-add-or-update'
import Detail from './activity-time-show-detail'
import moment from "moment/moment";

export default {
    data() {
        return {
            dataForm: {
                key: null
            },
            dataList: [],
            pageIndex: 1,
            pageSize: 10,
            totalPage: 0,
            dataListLoading: false,
            addOrUpdateVisible: false,
            detailVisible:false,
            dataListSelections: [],
            firstSelectedStatus: null,
        }
    },
    computed: {
        batchButtonText() {
            if (this.dataListSelections.length <= 0) return '批量操作'
            return this.firstSelectedStatus ? '批量下架' : '批量上架'
        }
    },
    components: {
        AddOrUpdate,
        Detail
    },
    activated() {
        this.getDataList()
    },
    methods: {
        queryPage() {
            this.pageIndex = 1
            this.getDataList()
        },
        // 获取数据列表
        getDataList() {
            this.dataListLoading = true
            this.$http({
                url: this.$http.adornUrl('/admin/zyz/activity/time/period/show/pagesForShow'),
                method: 'post',
                data: this.$http.adornData({
                    'currentPage': this.pageIndex,
                    'pageSize': this.pageSize,
                    'key': this.dataForm.key,
                    'auditStatus': this.dataForm.auditStatus,
                    'startTime': this.dataForm.timeRange && this.dataForm.timeRange.length === 2 ? this.dataForm.timeRange[0] + ' 00:00:00' : null,
                    'endTime': this.dataForm.timeRange && this.dataForm.timeRange.length === 2 ? this.dataForm.timeRange[1] + ' 23:59:59' : null
                })
            }).then(({data}) => {
                if (data && data.code === 0) {
                    this.dataList = data.obj.records
                    this.totalPage = data.obj.total
                    this.dataList.forEach(item => {
                        // 判断是否为本组织创建
                        let own = (  item.teamPublish && this.$store.state.user.managerCapacity === 'TEAM_ADMIN' && item.publishTeamId === this.$store.state.user.teamId) ||
                            ((!( item.teamPublish)) &&  this.$store.state.user.managerCapacity !== 'TEAM_ADMIN' && item.publishOrgCode === this.$store.state.user.orgCode)
                        // 判断草稿,驳回状态的操作

                        console.log('own'+own+'/'+item.publishTeamId +'/'+this.$store.state.user.teamId+'/' +item.teamPublish+'/'+ this.$store.state.user.managerCapacity )
                        item.enable = own && (item.auditStatus === 'act_show_draft' || item.auditStatus === 'act_show_reject' || item.auditStatus === 'act_show_wait_upload'|| !item.auditStatus )
                        item.renderEnable =own &&  (item.auditStatus === 'act_show_draft' || item.auditStatus === 'act_show_reject'  )
                        // 判断撤回
                        item.recallEnable =own && item.auditStatus === 'act_show_wait_audit'
                        // 判断同步
                        item.syncEnable = item.auditStatus === 'act_show_audit_success' && (item.sync==='sync_wait'|| item.sync==='sync_failure')
                        //判断上下架
                        item.autoEnable = item.auditStatus === 'act_show_audit_success'
                        item.timeRange = moment(item.startTime).format('YYYY-MM-DD HH:mm') + '-' + moment(item.endTime).format('HH:mm')
                        if(item.picsUrl){
                            item.picList= item.picsUrl.split(',')
                            console.log( item.picList)
                        }
                    })
                } else {
                    this.dataList = []
                    this.totalPage = 0
                }
                this.dataListLoading = false
            })
        },
        // 每页数
        sizeChangeHandle(val) {
            this.pageSize = val
            this.pageIndex = 1
            this.getDataList()
        },
        // 当前页
        currentChangeHandle(val) {
            this.pageIndex = val
            this.getDataList()
        },
        // 处理全选
        handleSelectAll(selection) {
            if (selection.length === 0) {
                // 如果是取消全选，重置状态
                this.firstSelectedStatus = null;
                return;
            }
            
            // 如果是全选，我们只选择状态符合的行
            this.$nextTick(() => {
                // 清除当前选择
                this.$refs.multipleTable.clearSelection();
                
                // 设置第一个启用的行的状态为过滤条件
                const enabledRows = this.dataList.filter(row => row.autoEnable);
                if (enabledRows.length > 0) {
                    this.firstSelectedStatus = enabledRows[0].autoStatus;
                    
                    // 只选择状态匹配的行
                    this.dataList.forEach(row => {
                        if (row.autoEnable && row.autoStatus === this.firstSelectedStatus) {
                            this.$refs.multipleTable.toggleRowSelection(row, true);
                        }
                    });
                }
            });
        },
        // 多选
        selectionChangeHandle(val) {
            // 不是全选操作时才更新选择项
            this.dataListSelections = val;
            
            // 更新第一个选择的状态
            if (val.length > 0) {
                // 只有当没有设置状态或选择为空时设置第一个状态
                if (this.firstSelectedStatus === null) {
                    this.firstSelectedStatus = val[0].autoStatus;
                }
            } else {
                // 重置状态
                this.firstSelectedStatus = null;
            }
        },
        // 检查是否可选
        checkSelectable(row) {
            // 只有审核通过的记录可以选择
            if (!row.autoEnable) return false
            
            // 如果已有选择，那么只有与第一个选择状态相同的行可以被选中
            if (this.firstSelectedStatus !== null) {
                return row.autoStatus === this.firstSelectedStatus
            }
            
            return true
        },
        // 重置
        resetForm() {
            this.$nextTick(() => {
                this.$refs['dataForm'].resetFields()
                this.getDataList()
            })
        },
        // 新增 / 修改
        addOrUpdateHandle(activityTimePeriodId,id) {
            this.addOrUpdateVisible = true
            this.$nextTick(() => {
                this.$refs.addOrUpdate.init(activityTimePeriodId,id)
            })
        },
        // 提交
        render(id) {
            this.$confirm(`确定进行提交操作?`, '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning',
                closeOnClickModal: false
            }).then(() => {
                this.$http({
                    url: this.$http.adornUrl('/admin/zyz/activity/time/period/show/renderById'),
                    method: 'get',
                    params: this.$http.adornParams({
                        'id': id
                    })
                }).then(({data}) => {
                    if (data && data.code === 0) {
                        this.$message({
                            message: '操作成功',
                            type: 'success',
                            duration: 1500,
                            onClose: () => {
                                this.getDataList()
                            }
                        })
                    } else {
                        this.$message.error(data.msg)
                    }
                })
            })
        },
        recallHandle(id) {
            this.$confirm(`确定进行撤回操作?撤回后将变为草稿状态`, '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning',
                closeOnClickModal: false
            }).then(() => {
                this.$http({
                    url: this.$http.adornUrl('/admin/zyz/activity/time/period/show/recall'),
                    method: 'get',
                    params: this.$http.adornParams({
                        'id': id
                    })
                }).then(({data}) => {
                    if (data && data.code === 0) {
                        this.$message({
                            message: '操作成功',
                            type: 'success',
                            duration: 1500,
                            onClose: () => {
                                this.getDataList()
                            }
                        })
                    } else {
                        this.$message.error(data.msg)
                    }
                })
            })
        },
        detailHandle(activityTimePeriodId,id) {
            this.detailVisible = true
            this.$nextTick(() => {
                this.$refs.detail.init(activityTimePeriodId,id)
            })
        },
        // 同步
        syncHandle(actShowId) {
            this.$confirm(`确定是否进行同步，请谨慎操作！`, '确认同步？', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning',
                closeOnClickModal: false
            }).then(() => {
                this.dataListLoading = true
                this.$http({
                    url: this.$http.adornUrl('/admin/zyz/platform-sync/syncActShow'),
                    method: 'post',
                    params: this.$http.adornParams({
                        'actShowId': actShowId
                    })
                }).then(({data}) => {
                    this.dataListLoading = false
                    if (data && data.code === 0) {
                        this.$message({
                            message: '请求同步成功，市平台返回：（' + data.obj.message + '）',
                            type: data.obj.resultCode === 'SUCCESS' ? 'success' : 'warning',
                            duration: 3000,
                            onClose: () => {
                                this.getDataList()
                            }
                        })
                    } else {
                        this.$message.error(data.msg)
                    }
                })
            })
        },
        // 上下架
        updateAutoStatus(id, autoStatus) {
            this.$confirm(`确定进行[${!autoStatus ? '上架' : '下架'}]操作?`, '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning',
                closeOnClickModal: false
            }).then(() => {
                this.$http({
                    url: this.$http.adornUrl('/admin/zyz/activity/time/period/show/updateAutoStatus'),
                    method: 'get',
                    params: this.$http.adornParams({
                        'id': id
                    })
                }).then(({data}) => {
                    if (data && data.code === 0) {
                        this.$message({
                            message: '操作成功',
                            type: 'success',
                            duration: 1500,
                            onClose: () => {
                                this.getDataList()
                            }
                        })
                    } else {
                        this.$message.error(data.msg)
                    }
                })
            })
        },
        // 批量上下架
        batchUpdateAutoStatus() {
            if (this.dataListSelections.length === 0) {
                this.$message.warning('请选择要操作的记录！')
                return
            }
            
            // 检查选中项是否都是已审核通过的记录
            const invalidItems = this.dataListSelections.filter(item => !item.autoEnable)
            if (invalidItems.length > 0) {
                this.$message.warning('只能批量操作审核通过的记录！')
                return
            }
            
            const currentStatus = this.firstSelectedStatus
            let ids = this.dataListSelections.map(item => item.id).join(',')
            
            this.$confirm(`确定批量进行[${currentStatus ? '下架' : '上架'}]操作?`, '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning',
                closeOnClickModal: false
            }).then(() => {
                this.$http({
                    url: this.$http.adornUrl('/admin/zyz/activity/time/period/show/updateAutoStatus'),
                    method: 'get',
                    params: this.$http.adornParams({
                        'id': ids
                    })
                }).then(({data}) => {
                    if (data && data.code === 0) {
                        this.$message({
                            message: '操作成功',
                            type: 'success',
                            duration: 1500,
                            onClose: () => {
                                this.getDataList()
                            }
                        })
                    } else {
                        this.$message.error(data.msg)
                    }
                })
            })
        },
    }
}
</script>
