<template>
    <div class="mod-role">
        <el-form :inline="true" :model="dataForm" ref="dataForm" @keyup.enter="queryPage()">
            <el-form-item label="关键字" prop="key" style="padding-right: 170px">
                <el-input v-model="dataForm.key" placeholder="活动名称/联系人/联系方式/活动地址/活动主体" clearable style="width: 180%"></el-input>
            </el-form-item>
            <el-form-item label="活动时间" prop="timeRange">
                <el-date-picker v-model="dataForm.timeRange" clearable type="daterange" value-format="yyyy-MM-dd"
                                align="right" start-placeholder="开始时间" end-placeholder="结束时间">
                </el-date-picker>
            </el-form-item>
            <el-form-item v-if="!dataForm.needAudit" label="审核状态" prop="auditStatus">
                <el-select v-model="dataForm.auditStatus" placeholder="请选择" clearable>
                    <el-option
                        v-for="item in [{label: '待审核', value: 'act_show_wait_audit'}, {label: '审核通过', value: 'act_show_audit_success'}, {label: '驳回', value: 'act_show_reject'}]"
                        :key="item.value" :label="item.label" :value="item.value">
                    </el-option>
                </el-select>
            </el-form-item>
            <el-form-item  prop="needAudit">
                <el-checkbox v-model="dataForm.needAudit">需要我审核的</el-checkbox>
            </el-form-item>
            <el-form-item>
                <el-button icon="el-icon-search" type="warning" @click="queryPage()">查询</el-button>
                <el-button @click="resetForm()" icon="el-icon-refresh-left">重置</el-button>
            </el-form-item>
        </el-form>
        <div class="f-s-c" style="padding: 15px 0;display: flex;justify-content:flex-end;align-items: center ">
            <div>
                <el-button icon="el-icon-s-check" :disabled="dataListSelections.length <= 0"
                           type="primary" @click="auditHandle(null, null, true)">批量通过
                </el-button>
            </div>
        </div>
        <el-table
                :data="dataList"
                border
                v-loading="dataListLoading"
                @selection-change="selectionChangeHandle"
                style="width: 100%;">
            <el-table-column
                type="selection"
                header-align="center"
                align="center"
                width="50">
            </el-table-column>
            <el-table-column
                prop="activityName"
                header-align="center"
                align="center"
                width="200"
                label="活动名称">
                <template slot-scope="scope">
                    <a style="cursor: pointer" @click="detailHandle(scope.row.activityTimePeriodId,scope.row.id)">{{ scope.row.activityName }}</a>
                </template>
            </el-table-column>
            <el-table-column
                    prop="timeRange"
                    header-align="center"
                    align="center"
                    label="时段">
            </el-table-column>
            <el-table-column
                    prop="syncText"
                    header-align="center"
                    align="center"
                    width="120"
                    label="公示同步状态">
            </el-table-column>
            <el-table-column
                    prop="content"
                    header-align="center"
                    min-width="200"
                    align="center"
                    label="公示内容">
            </el-table-column>
            <el-table-column
                prop="picList"
                header-align="center"
                align="center"
                min-width="200"
                label="公示图片列表">
                <template slot-scope="scope">
                    <el-image  style="width:80px;height:80px;border:none;" v-for="(item, index) in scope.row.picList" :key="index":src="$http.adornAttachmentUrl(item)"
                               :preview-src-list="[$http.adornAttachmentUrl(item)]">
                    </el-image>
                </template>
            </el-table-column>
            <el-table-column
                prop="publishOrgName"
                header-align="center"
                align="center"
                label="提交组织">
            </el-table-column>
            <el-table-column
                prop="creatorName"
                header-align="center"
                align="center"
                width="80"
                label="提交人"/>
            <el-table-column
                prop="auditStatusText"
                header-align="center"
                align="center"
                min-width="80"
                label="审核状态"/>
            <el-table-column
                prop="creatorDate"
                header-align="center"
                align="center"
                min-width="80"
                label="提交时间"/>
            <el-table-column
                    fixed="right"
                    header-align="center"
                    align="center"
                    width="150"
                    label="操作">
                <template #default="scope">
                    <el-button v-if="scope.row.canAudit" type="text" size="small"
                               @click="auditHandle(scope.row.id, scope.row.name, true)">通过
                    </el-button>
                    <el-button v-if="scope.row.canAudit" type="text" size="small"
                               @click="auditHandle(scope.row.id, scope.row.name, false)">驳回
                    </el-button>

                </template>
            </el-table-column>
        </el-table>
        <div class="com-pagination">
            <el-pagination
                    @size-change="sizeChangeHandle"
                    @current-change="currentChangeHandle"
                    :current-page="pageIndex"
                    :page-sizes="[10, 20, 50, 100]"
                    :page-size="pageSize"
                    :total="totalPage"
                    layout="total, sizes, prev, pager, next, jumper"
            />
        </div>
        <detail v-if="detailVisible" ref="detail" ></detail>
    </div>
</template>

<script>
import moment from 'moment/moment'
import Detail from './activity-time-show-detail'

export default {
    data() {
        return {
            dataForm: {
                key: null,
                needAudit: true,
                auditStatus: '',
                timeRange: [],
            },
            dataList: [],
            pageIndex: 1,
            pageSize: 10,
            totalPage: 0,
            dataListLoading: false,
            addOrUpdateVisible: false,
            detailVisible:false,
            dataListSelections: [],
        }
    },
    components: {
        Detail,
    },
    activated() {
        this.getDataList()
    },
    methods: {
        queryPage() {
            this.pageIndex = 1
            this.getDataList()
        },
        // 获取数据列表
        getDataList() {
            this.dataListLoading = true
            this.$http({
                url: this.$http.adornUrl('/admin/zyz/activity/time/period/show/pagesForAudit'),
                method: 'post',
                data: this.$http.adornData({
                    'currentPage': this.pageIndex,
                    'pageSize': this.pageSize,
                    'key': this.dataForm.key,
                    'needAudit':this.dataForm.needAudit,
                    'auditStatus':this.dataForm.auditStatus,
                    'startTime': this.dataForm.timeRange && this.dataForm.timeRange.length === 2 ? this.dataForm.timeRange[0] + ' 00:00:00' : null,
                    'endTime': this.dataForm.timeRange && this.dataForm.timeRange.length === 2 ? this.dataForm.timeRange[1] + ' 23:59:59' : null
                })
            }).then(({data}) => {
                if (data && data.code === 0) {
                    this.dataList = data.obj.records
                    this.totalPage = data.obj.total
                    data.obj.records.forEach((it) => {
                        it.canAudit = it.auditStatus === 'act_show_wait_audit' && it.auditOrgCode === this.$store.state.user
                            .orgCode
                        it.timeRange = moment(it.startTime).format('YYYY-MM-DD HH:mm') + '-' + moment(it.endTime).format('HH:mm')
                        if(it.picsUrl){
                            it.picList= it.picsUrl.split(',')
                            console.log( it.picList)
                        }
                    })
                } else {
                    this.dataList = []
                    this.totalPage = 0
                }
                this.dataListLoading = false
            })
        },
        // 每页数
        sizeChangeHandle(val) {
            this.pageSize = val
            this.pageIndex = 1
            this.getDataList()
        },
        // 当前页
        currentChangeHandle(val) {
            this.pageIndex = val
            this.getDataList()
        },
        // 多选
        selectionChangeHandle(val) {
            this.dataListSelections = val
        },
        // 重置
        resetForm() {
            this.$nextTick(() => {
                this.$refs['dataForm'].resetFields()
                this.getDataList()
            })
        },
        auditHandle(id, name, status) {
            if (!id && this.dataListSelections.length === 0) {
                this.$message.warning('未选中需要审核的活动记录！')
                return
            }
            if (!id) {
                let ids = this.dataListSelections.map(item => {
                    return item.id
                })
                id = ids.join(',')
            }
            this.$confirm(`确定${status ? '审核通过' : '驳回'}` + (name ? '"' + name + '"' : '') + '活动公示？', '系统提示', {
                customClass: 'audit_pass_msg_box',
                confirmButtonText: '确认',
                cancelButtonText: '取消',
                showInput: !status,
                inputPlaceholder: '请输入驳回意见……',
                inputType: 'textarea',
                closeOnClickModal: false
            }).then((val) => {
                this.$http({
                    url: this.$http.adornUrl('/admin/zyz/activity/time/period/show/audit'),
                    method: 'get',
                    params: this.$http.adornParams({
                        'ids': id,
                        'status': status,
                        'remark': val.value
                    })
                }).then(({
                             data
                         }) => {
                    if (data && data.code === 0) {
                        this.$message.success(status ? '审核通过成功！' : '驳回成功！')
                        this.queryPage()
                    } else {
                        this.$message.error(status ? '审核通过失败！' : '驳回失败！' + data.msg)
                    }
                })
            })
            setTimeout(() => {
                var content = document.getElementsByClassName('audit_pass_msg_box')[0].childNodes[1]
                content.style.paddingLeft = '60px'
                content.style.paddingRight = '60px'
                var submitBtn = document.getElementsByClassName('audit_pass_msg_box')[0].childNodes[2]
                    .childNodes[1]
                submitBtn.style.backgroundColor = '#F56C6C'
                submitBtn.style.borderColor = '#F56C6C'
            }, 50)
        },
        detailHandle(activityTimePeriodId,id) {
            this.detailVisible = true
            this.$nextTick(() => {
                this.$refs.detail.init(activityTimePeriodId,id)
            })
        },
    }
}
</script>
<style lang="scss" scoped>
.el-table::v-deep .warning-row {
    background: #f5d2a5 !important;
}
</style>
