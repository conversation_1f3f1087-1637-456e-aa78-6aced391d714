<template>
  <div class="mod-role">
    <el-form :inline="true" ref="dataForm" :model="dataForm" @keyup.enter.native="getDataList()">
      <el-form-item label="关键字" prop="key" style="padding-right: 40px">
        <el-input v-model="dataForm.key" placeholder="活动名称/联系人/联系方式/评价内容" clearable style="width: 120%">
        </el-input>
      </el-form-item>
      <el-form-item label="活动时间" prop="timeRange">
        <el-date-picker v-model="dataForm.timeRange" clearable type="datetimerange"
                        value-format="yyyy-MM-dd HH:mm:ss" align="right" start-placeholder="开始时间" end-placeholder="结束时间"
                        :default-time="['00:00:00', '23:59:59']">
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button class="el-icon-search" type="warning" @click="getDataList()">查询</el-button>
        <el-button icon="el-icon-refresh-left" @click="resetForm()">重置</el-button>
      </el-form-item>
    </el-form>
<!--    <div class="f-s-c" style="padding: 15px 0;display: flex; justify-content: flex-end">-->
<!--      <div>-->
<!--        <el-button v-if="isAuth('service-time-list:list:export')" type="success"-->
<!--                   v-loading.fullscreen.lock="exportLoading" @click="exportHandle()">-->
<!--          导出-->
<!--        </el-button>-->
<!--      </div>-->
<!--    </div>-->
    <el-table :data="dataList" border v-loading="dataListLoading" @selection-change="selectionChangeHandle"
              style="width: 100%;">
      <!--			<el-table-column type="index" label="序号" align="center" width="50">-->
      <!--			</el-table-column>-->
      <el-table-column prop="activityName" header-align="center" align="center" min-width="300" :show-overflow-tooltip="true"
                       label="活动名称">
        <template slot-scope="scope">
          <a style="cursor: pointer" @click="activityDetailHandler(scope.row.activityId)">{{ scope.row.activityName }}</a>
        </template>
      </el-table-column>
      <el-table-column prop="volunteerName" header-align="center" align="center" min-width="100" label="志愿者姓名">
      </el-table-column>
      <el-table-column prop="volunteerPhone" header-align="center" align="center" min-width="160" label="联系电话">
      </el-table-column>
      <el-table-column prop="signTime" header-align="center" align="center" min-width="160" label="签到时间">
      </el-table-column>
      <el-table-column prop="timePeriodRange" header-align="center" align="center" min-width="160" label="活动时间">
      </el-table-column>
      <el-table-column prop="signTime" header-align="center" align="center" min-width="160" label="是否结算">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.exchange && scope.row.exchange === true" type="success">已结算</el-tag>
          <el-tag v-else type="danger">未结算</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="serviceLong" header-align="center" min-width="160" :show-overflow-tooltip="true"
                       align="center" label="服务时长">
      </el-table-column>
      <el-table-column
          prop="applyIsSyncText"
          header-align="center"
          align="center"
          min-width="120"
          label="报名同步状态">
        <template slot-scope="scope">
          <!--          <div v-if="scope.row.isSyncText === '同步失败'" style="color: #f6070f;cursor:pointer">{{scope.row.isSyncText}}</div>-->
          <!--          <div v-if="scope.row.isSyncText === '待同步'" style="color: #eee98a;cursor:pointer">{{scope.row.isSyncText}}</div>-->
          <!--          <div v-if="scope.row.isSyncText === '已同步'" style="color: #03f303;cursor:pointer">{{scope.row.isSyncText}}</div>-->
          <el-popover trigger="hover" placement="top">
            <p style="text-align: center">
              {{
                '同步时间：' + (scope.row.applySyncTime ? scope.row.applySyncTime : '')
              }}<br>{{ scope.row.applySyncRemark }}</p>
            <div slot="reference" class="name-wrapper">
              <el-tag v-if="scope.row.applyIsSync === 'sync_failure'" type="danger">{{
                  scope.row.applyIsSyncText
                }}
              </el-tag>
              <el-tag v-if="scope.row.applyIsSync === 'sync_wait'" type="warning">{{
                  scope.row.applyIsSyncText
                }}
              </el-tag>
              <el-tag v-if="scope.row.applyIsSync === 'sync_success'" type="success">{{
                  scope.row.applyIsSyncText
                }}
              </el-tag>
            </div>
          </el-popover>
        </template>
      </el-table-column>
      <el-table-column
          prop="serviceLongIsSyncText"
          header-align="center"
          align="center"
          min-width="140"
          label="服务时长同步状态">
        <template slot-scope="scope">
          <!--          <div v-if="scope.row.isSyncText === '同步失败'" style="color: #f6070f;cursor:pointer">{{scope.row.isSyncText}}</div>-->
          <!--          <div v-if="scope.row.isSyncText === '待同步'" style="color: #eee98a;cursor:pointer">{{scope.row.isSyncText}}</div>-->
          <!--          <div v-if="scope.row.isSyncText === '已同步'" style="color: #03f303;cursor:pointer">{{scope.row.isSyncText}}</div>-->
          <el-popover trigger="hover" placement="top">
            <p style="text-align: center">
              {{
                '同步时间：' + (scope.row.serviceLongSyncTime ? scope.row.serviceLongSyncTime : '')
              }}<br>{{ scope.row.serviceLongSyncRemark }}</p>
            <div slot="reference" class="name-wrapper">
              <el-tag v-if="scope.row.serviceLongIsSync === 'sync_failure'" type="danger">
                {{ scope.row.serviceLongIsSyncText }}
              </el-tag>
              <el-tag v-if="scope.row.serviceLongIsSync === 'sync_wait'" type="warning">
                {{ scope.row.serviceLongIsSyncText }}
              </el-tag>
              <el-tag v-if="scope.row.serviceLongIsSync === 'sync_success'" type="success">
                {{ scope.row.serviceLongIsSyncText }}
              </el-tag>
            </div>
          </el-popover>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination @size-change="sizeChangeHandle" @current-change="currentChangeHandle" :current-page="pageIndex"
                   :page-sizes="[10, 20, 50, 100]" :page-size="pageSize" :total="totalPage"
                   layout="total, sizes, prev, pager, next, jumper">
    </el-pagination>
    <!-- 活动详情 -->
    <activity-detail v-if="activityDetailVisible" ref="activityDetail"></activity-detail>
  </div>
</template>

<script>
import listMixin from '@/mixins/list-mixins'
import ActivityDetail from './activity-detail'
import moment from 'moment'

export default {
  mixins: [listMixin],
  data() {
    return {
      mixinOptions: {
        dataUrl: '/admin/zyz/activity/apply/pageForServiceLong',
        exportUrl: '/admin/zyz/activity/apply/exportForServiceLong', // 导出接口，API地址
        exportFileName: '活动服务时长记录列表'
      },
      dataForm: {
        key: null,
        actType: null,
        auditStatus: null,
        teamMember: null,
        timeRange: []
      },
      fields: [],
      orgList: [],
      exportLoading: false,
      activityDetailVisible: false
    }
  },
  components: {
    ActivityDetail
  },
  activated() {
    this.getDataList()
  },
  methods: {
    resetForm() {
      this.$refs['dataForm']?.resetFields()
      this.dataForm.startTime = null
      this.dataForm.endTime = null
      this.dataForm.key = null
      this.dataForm.timeRange = []
      this.getDataList()
    },
    queryBeforeHandle() {
      // 查询前操作
      this.dataForm.startTime = this.dataForm.timeRange && this.dataForm.timeRange[0]
      this.dataForm.endTime = this.dataForm.timeRange && this.dataForm.timeRange[1]
    },
    querySuccessHandle(data) {
      data.records.forEach(item => {
        item.timePeriodRange = item.timePeriodStartTime && item.timePeriodEndTime ?
            moment(item.timePeriodStartTime).format('YYYY-MM-DD HH:mm') + '-' + moment(item.timePeriodEndTime).format('HH:mm') : ''
      })
      this.dataList = data.records
      this.totalPage = data.total
    },
    /* 	querySuccessHandle (data) {
        // 查询成功操作
        this.dataList = data.records
        this.totalPage = data.total
        data.records.forEach((it) => {
          it.canAudit = it.auditStatus === 'act_apply_wait_audit'
        })
      }, */
    activityDetailHandler(activityId) {
      this.activityDetailVisible = true
      this.$nextTick(() => {
        this.$refs.activityDetail.init(activityId)
      })
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
