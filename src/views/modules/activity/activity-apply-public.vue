<template>
  <div class="mod-config">
    <el-form :inline="true" ref="dataForm" :model="dataForm" @keyup.enter.native="getDataList()">
      <el-form-item label="关键字" prop="activityName" style="padding-right: 40px">
        <el-input v-model="dataForm.activityName" placeholder="活动名称/姓名/联系方式" clearable style="width: 120%"></el-input>
      </el-form-item>
      <el-form-item label="报名状态:" prop="status">
        <el-radio-group v-model="dataForm.status">
          <el-radio :label="null">全部</el-radio>
          <el-radio :label="true">已报名</el-radio>
          <el-radio :label="false">取消报名</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-search" type="warning" @click="getDataList()">查询</el-button>
        <el-button class="el-icon-refresh-left" @click="resetForm()">重置</el-button>
      </el-form-item>
      <el-form-item style="float: right">
        <el-button icon="el-icon-download" type="success" v-loading.fullscreen.lock="fullscreenLoading"
                   @click="exportServiceLongPublic()">全部导出
        </el-button>
      </el-form-item>
    </el-form>
    <el-table
        :data="dataList"
        border
        v-loading="dataListLoading"
        @selection-change="selectionChangeHandle"
        style="width: 100%;">
      <el-table-column
          prop="activityName"
          header-align="center"
          align="center"
          min-width="150px"
          label="活动名称">
      </el-table-column>
      <el-table-column
          prop="timePeriodRange"
          header-align="center"
          align="center"
          min-width="190px"
          label="活动时间段">
      </el-table-column>
      <el-table-column
          prop="name"
          header-align="center"
          align="center"
          label="姓名">
      </el-table-column>
      <el-table-column
          prop="phone"
          header-align="center"
          align="center"
          min-width="120px"
          label="联系方式">
      </el-table-column>
      <el-table-column
          prop="certificateId"
          header-align="center"
          align="center"
          min-width="160px"
          label="身份证号">
      </el-table-column>
      <el-table-column
              prop="sexText"
              header-align="center"
              align="center"
              min-width="60px"
              label="性别">
      </el-table-column>
      <el-table-column
              prop="age"
              header-align="center"
              align="center"
              min-width="60px"
              label="年龄">
      </el-table-column>
      <el-table-column
          prop="party"
          header-align="center"
          align="center"
          label="是否党员">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.party == false" size="small" type="danger">否</el-tag>
          <el-tag v-else size="small">是</el-tag>
        </template>
      </el-table-column>
        <el-table-column
                prop="address"
                header-align="center"
                align="center"
                min-width="160px"
                :show-overflow-tooltip="true"
                label="住址">
        </el-table-column>
      <el-table-column
          prop="status"
          header-align="center"
          align="center"
          label="报名状态">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.status == false" size="small" type="danger">已取消报名</el-tag>
          <el-tag v-else size="small">已报名</el-tag>
        </template>
      </el-table-column>
              <el-table-column
              fixed="right"
              header-align="center"
              align="center"
              width="150"
              label="操作">
              <template v-if="scope.row.status" slot-scope="scope">
                <el-button type="text" size="small" @click="cancelApplyHandle(scope.row.id,scope.row.timePeriodId)">取消报名</el-button>
              </template>
            </el-table-column>
    </el-table>
    <el-pagination
        @size-change="sizeChangeHandle"
        @current-change="currentChangeHandle"
        :current-page="pageIndex"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pageSize"
        :total="totalPage"
        layout="total, sizes, prev, pager, next, jumper">
    </el-pagination>
    <!-- 弹窗, 新增 / 修改 -->
    <!--    <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"></add-or-update>-->
  </div>
</template>

<script>

import listMixin from '@/mixins/list-mixins'
import moment from "moment";

export default {
  mixins: [listMixin],
  data() {
    return {
      mixinOptions: {
        dataUrl: '/admin/zyz/activity/apply/public/pagesForApplyPublic',
        deleteUrl: '/admin/zyz/activity/apply/public/removeByIds'
      },
      fullscreenLoading: false,
      dataForm: {
        activityName: '',
        status: true
      }
    }
  },
  activated() {
    this.getDataList()
  },

  components: {},
  methods: {
    resetForm() {
      this.$refs['dataForm']?.resetFields()
      this.getDataList()
    },
    // 取消群众报名
    cancelApplyHandle(id,timePeriodId) {
      var ids = id ? [id] : this.dataListSelections.map(item => {
        return item.id
      })
      this.$confirm(`确定进取消该群众报名?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$http({
          url: this.$http.adornUrl('/api/activity/cancelApply'),
          method: 'get',
          params: this.$http.adornParams({
            'applyPublicIds': ids.join(','),
            'timePeriodId': timePeriodId
          })
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.$message({
              message: '操作成功',
              type: 'success',
              duration: 1500,
              onClose: () => {
                this.getDataList()
              }
            })
          } else {
            this.$message.error(data.msg)
          }
        })
      })
    },
    // 导出
    exportServiceLongPublic() {
      this.fullscreenLoading = true;
      this.$http({
        url: this.$http.adornUrl('/admin/zyz/activity/apply/public/exportForPublic'),
        method: 'post',
        responseType: 'arraybuffer',
        data: this.$http.adornData({
          'activityName': this.dataForm.activityName,
          'status': this.dataForm.status
        })
      }).then(({data}) => {
        if (data.code && data.code !== 0) {
          this.$message.error('导出失败')
        } else {
          this.fullscreenLoading = false;
          let blob = new Blob([data], {type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8'})
          let objectUrl = URL.createObjectURL(blob)
          let a = document.createElement('a')
          // window.location.href = objectUrl
          a.href = objectUrl
          a.download = '报名管理列表明细.xlsx'
          a.click()
          URL.revokeObjectURL(objectUrl)
          this.$message({
            type: 'success',
            message: '导出成功'
          })
        }
      })
    },
    querySuccessHandle(data) {
      data.records.forEach(it => {
        it.timePeriodRange = it.startTime && it.endTime ?
            moment(it.startTime).format('YYYY-MM-DD HH:mm') + '-' + moment(it.endTime).format('HH:mm') : ''
      })
      this.dataList = data.records
      this.totalPage = data.total
    }

  }
}
</script>
