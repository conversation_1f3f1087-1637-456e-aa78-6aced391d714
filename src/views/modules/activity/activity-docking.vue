<template>
  <div>
    <el-dialog :title="'活动对接'" :close-on-click-modal="false" width="80%" append-to-body :visible.sync="visible">
      <el-form :model="dataForm" :rules="dataRule" ref="dataForm" label-width="150px" style="padding-right: 20px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="活动名称" prop="name">
              <el-input v-model="dataForm.name" disabled placeholder="活动名称" clearable></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="对接类型" prop="dockingTypes">
              <el-select v-model="dataForm.dockingTypes" autocomplete clearable disabled
                         placeholder="请选择" filterable style="width: 100%" multiple>
                <el-option
                    v-for="item in dockingTypesList"
                    :disabled="dataForm.dockingTypes && ((dataForm.dockingTypes.indexOf('zyz_activity_docking_type_resource') >= 0 || dataForm.dockingTypes.indexOf('zyz_activity_docking_type_requirement') >= 0) && item.code === 'zyz_activity_docking_type_no_docking') ||
                               (dataForm.dockingTypes.indexOf('zyz_activity_docking_type_no_docking') >= 0 && (item.code === 'zyz_activity_docking_type_requirement' || item.code === 'zyz_activity_docking_type_resource'))"
                    :key="item.code"
                    :label="item.name"
                    :value="item.code">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12" v-if="dataForm.dockingTypes && dataForm.dockingTypes.indexOf('zyz_activity_docking_type_requirement') >= 0">
            <el-form-item
                v-if="dataForm.dockingReqCreate && dataForm.dockingReqCreate === true && (!dataForm.auditStatus || dataForm.auditStatus !== 'act_audit_success')"
                label="对接的需求（新创）" prop="reqName">
              <el-input :style="!disableEdit || disableEdit === false ? {'width': '60%'} : {'width': '100%'}" v-model="dataForm.reqName" disabled></el-input>
              <el-button v-if="!disableEdit || disableEdit === false" style="width:17%; margin-left: 3%" type="warning" @click="updateReq">修改</el-button>
              <el-button v-if="!disableEdit || disableEdit === false" style="width:17%; float: right" type="danger" @click="clearReq">清除</el-button>
            </el-form-item>
            <el-form-item v-else label="对接的需求" prop="reqId">
              <el-input v-if="disableEdit && disableEdit === true" style="width: 100%" disabled v-model="dataForm.reqName" placeholder="对接需求"/>
              <el-select v-if="!disableEdit || disableEdit === false" style="width: 60%" v-model="dataForm.reqId"
                         placeholder="请选择" :disabled="disableEdit">
                <el-option v-for="item in reqList" :key="item.id" :label="item.name" :value="item.id">
                  <span style="float: left">{{ item.name }}</span>
                  <span style="float: right; color: #8492a6; font-size: 8px">{{ item.startTime + '--' + item.endTime }}</span>
                </el-option>
              </el-select>
              <!--            <el-select v-if="disableEdit && disableEdit === true" style="width: 100%" v-model="dataForm.reqId"-->
              <!--                       placeholder="请选择" :disabled="disableEdit">-->
              <!--              <el-option v-for="item in reqList" :key="item.id" :label="item.name" :value="item.id">-->
              <!--              </el-option>-->
              <!--            </el-select>-->
              <el-button v-if="!disableEdit || disableEdit === false" style="width:20%; float: right" type="success"
                         @click="createReq">新建需求
              </el-button>
            </el-form-item>
          </el-col>
          <el-col :span="12" v-if="dataForm.dockingTypes && dataForm.dockingTypes.indexOf('zyz_activity_docking_type_resource') >= 0">
            <el-form-item
                v-if="dataForm.dockingResCreate && dataForm.dockingResCreate === true && (!dataForm.auditStatus || dataForm.auditStatus !== 'act_audit_success')"
                label="对接的资源（新创）" prop="resName">
              <el-input :style="!disableEdit || disableEdit === false ? {'width': '60%'} : {'width': '100%'}" v-model="dataForm.resName" disabled></el-input>
              <el-button v-if="!disableEdit || disableEdit === false" style="width:17%; margin-left: 3%" type="warning" @click="updateRes">修改</el-button>
              <el-button v-if="!disableEdit || disableEdit === false" style="width:17%; float: right" type="danger" @click="clearRes">清除</el-button>
            </el-form-item>
            <el-form-item v-else label="对接的资源" prop="resId">
              <el-input v-if="disableEdit && disableEdit === true" style="width: 100%" disabled v-model="dataForm.resName" placeholder="对接资源"/>
              <el-select v-if="!disableEdit || disableEdit === false" style="width: 60%" v-model="dataForm.resId"
                         placeholder="请选择" :disabled="disableEdit" filterable remote :remote-method="remoteResourceSearch">
                <el-option v-for="item in resList" :key="item.id" :label="item.name" :value="item.id">
                  <span style="float: left">{{ item.name }}</span>
                  <span style="float: right; color: #8492a6; font-size: 8px">{{ item.publishOrgName }}</span>
                </el-option>
              </el-select>
              <!--            <el-select v-if="disableEdit && disableEdit === true" style="width: 100%" v-model="dataForm.resId"-->
              <!--                       placeholder="请选择" :disabled="disableEdit">-->
              <!--              <el-option v-for="item in resList" :key="item.id" :label="item.name" :value="item.id">-->
              <!--              </el-option>-->
              <!--            </el-select>-->
              <el-button v-if="!disableEdit || disableEdit === false" style="width:20%; float: right" type="success"
                         @click="createRes">新建资源
              </el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <span slot="footer" class="dialog-footer">
      <el-button v-if="!disableEdit || disableEdit === false" @click="visible = false">取消</el-button>
      <el-button v-if="!disableEdit || disableEdit === false" type="primary" @click="dataFormSubmit()">提交</el-button>
      <el-button v-if="disableEdit && disableEdit === true" @click="visible = false">关闭</el-button>
    </span>
    </el-dialog>
    <!-- 弹窗, 需求新增 / 需求修改 -->
    <req-add-or-update v-if="reqAddOrUpdateVisible" ref="reqAddOrUpdate"
                       @reqCreateCallback="reqCreateCallback"></req-add-or-update>
    <!-- 弹窗, 资源新增 / 资源修改 -->
    <res-add-or-update v-if="resAddOrUpdateVisible" ref="resAddOrUpdate"
                       @resCreateCallback="resCreateCallback"></res-add-or-update>
  </div>
</template>

<script>
import moment from 'moment'
import _ from 'lodash'
import ReqAddOrUpdate from '../requirement/requirement-add-or-update'
import ResAddOrUpdate from '../resource/resource-add-or-update'
import Dict from "@/views/modules/sys/dict";

export default {
  data() {
    return {
      visible: false,
      dockingTypesList: [],
      resList: [],
      reqList: [],
      reqAddOrUpdateVisible: false,
      resAddOrUpdateVisible: false,
      actForm: {},
      disableEdit: false,
      dataForm: {
        id: null,
        version: null,
        name: '',
        dockingType: '',
        dockingTypes: [],
        auditStatus: null,
        resId: '',
        teamId: '',
        reqId: '',
        dockingResCreate: false,
        resName: null,
        dockingReqCreate: false,
        reqName: null
      },
      dataRule: {
        // dockingTypes: [
        //   {required: true, message: '对接类型不能为空', trigger: 'blur'}
        // ],
        resId: [
          {required: true, message: '对接资源不能为空', trigger: 'blur'}
        ],
        reqId: [
          {required: true, message: '对接需求不能为空', trigger: 'blur'}
        ],
        resName: [
          {required: true, message: '新创资源不能为空', trigger: 'blur'}
        ],
        reqName: [
          {required: true, message: '新创需求不能为空', trigger: 'blur'}
        ]
      }
    }
  },
  components: {
    Dict,
    moment,
    ReqAddOrUpdate,
    ResAddOrUpdate
  },
  // watch: {
  //   //监听下拉框
  //   'dataForm.dockingTypes'(val, oldVal) {
  //     if (this.dataForm.dockingTypes && this.dataForm.dockingTypes.length > 0) {
  //       this.dataForm.dockingType = _.join(this.dataForm.dockingTypes, ',')
  //     }
  //     if (this.dataForm.dockingTypes && this.dataForm.dockingTypes.indexOf("zyz_activity_docking_type_resource") >= 0) {
  //       this.getResList(this.dataForm.teamId)
  //     } else {
  //       this.dataForm.resId = null
  //       this.dataForm.dockingResCreate = false
  //     }
  //     if (this.dataForm.dockingTypes && this.dataForm.dockingTypes.indexOf("zyz_activity_docking_type_requirement") >= 0) {
  //       this.getReqList(this.dataForm.id, this.dataForm.teamId)
  //     } else {
  //       this.dataForm.reqId = null
  //       this.dataForm.dockingReqCreate = false
  //     }
  //   }
  // },
  methods: {
    init(row) {
      this.dataForm.id = row.id || null
      this.dataForm.name = row.name || null
      this.dataForm.resId = row.resId || null
      this.dataForm.reqId = row.reqId || null
      this.dataForm.teamId = row.teamId || null
      this.dataForm.dockingType = row.dockingType || null
      this.dataForm.dockingTypes = row.dockingType && row.dockingType !== '' ? row.dockingType.split(',') : null
      this.dataForm.dockingResCreate = row.dockingResCreate || null
      this.dataForm.dockingReqCreate = row.dockingReqCreate || null
      this.dataForm.resName = row.resName || null
      this.dataForm.reqName = row.reqName || null
      this.dataForm.auditStatus = row.auditStatus || null
      this.actForm = row
      this.actForm.fieldIds = [row.belongFieldTop, row.belongFieldEnd]
      this.$set(this.actForm, 'timeRange', [row.startTime, row.endTime])
      this.disableEdit = !row.enable
      this.resList = []
      this.reqList = []
      this.getDockingTypeList()
      this.getResList(this.dataForm.teamId, null, this.dataForm.resId)
      this.getReqList(this.dataForm.id, this.dataForm.teamId)
      this.visible = true
    },
    getResList(teamId, searchName, pointId) {
      if (this.dataForm.dockingResCreate && this.dataForm.dockingResCreate === true && (!this.dataForm.auditStatus || this.dataForm.auditStatus !== 'act_audit_success')) {
        return
      }
      this.$http({
        url: this.$http.adornUrl('/admin/zyz/resource/getEnableDocking'),
        method: 'get',
        params: this.$http.adornParams({
          'teamId': teamId,
          'searchName': searchName,
          'pointResourceId': pointId
        })
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.resList = this.getTreeData(data.obj)
        } else {
          this.resList = []
        }
      })
    },
    remoteResourceSearch (searchName) {
      this.getResList(this.teamId, searchName, null)
    },
    getReqList(actId, teamId) {
      if (this.dataForm.dockingReqCreate && this.dataForm.dockingReqCreate === true && (!this.dataForm.auditStatus || this.dataForm.auditStatus !== 'act_audit_success')) {
        return
      }
      this.$http({
        url: this.$http.adornUrl('/admin/zyz/requirement/getEnableDocking'),
        method: 'get',
        params: this.$http.adornParams({
          'actId': actId,
          'teamId': teamId
        })
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.reqList = this.getTreeData(data.obj)
        } else {
          this.reqList = []
        }
      })
    },
    getDockingTypeList() {
      this.$http({
        url: this.$http.adornUrl(`/admin/dict/parent`),
        method: 'get',
        params: this.$http.adornParams({code: 'zyz_activity_docking_type'})
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.dockingTypesList = data.obj || []
        }
      })
    },
    // *处理点位分类最后children数组为空的状态
    getTreeData: function (data) {
      var that = this
      // 循环遍历json数据
      data.forEach(function (e) {
        if (!e.children || e.children.length < 1) {
          e.children = undefined
        } else {
          // children若不为空数组，则继续 递归调用 本方法
          that.getTreeData(e.children)
        }
      })
      return data
    },
    dataFormSubmit() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.$http({
            url: this.$http.adornUrl('/admin/zyz/activity/dockResOrReq'),
            method: 'get',
            params: this.$http.adornParams({
              'id': this.dataForm.id,
              'dockingType': _.join(this.dataForm.dockingTypes, ','),
              'resId': this.dataForm.resId,
              'reqId': this.dataForm.reqId,
              'dockingResCreate': this.dataForm.dockingResCreate,
              'dockingReqCreate': this.dataForm.dockingReqCreate
            })
          }).then(({data}) => {
            if (data && data.code === 0) {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 1500,
                onClose: () => {
                  this.visible = false
                  this.$emit('refreshDataList')
                }
              })
            } else {
              this.$message.error(data.msg)
            }
          })
        }
      })
    },
    createRes() {
      this.$confirm(`如需要创建资源，您可先行完善好活动信息，资源创建时将为您带出相关信息！`, '提示', {
        confirmButtonText: '立即创建',
        cancelButtonText: '稍后创建',
        type: 'warning',
        closeOnClickModal: false
      }).then(() => {
        this.resAddOrUpdateVisible = true
        this.$nextTick(() => {
          this.$refs.resAddOrUpdate.fromActInit(null, this.actForm)
        })
      })
    },
    updateRes() {
      this.resAddOrUpdateVisible = true
      this.$nextTick(() => {
        this.$refs.resAddOrUpdate.fromActInit(this.dataForm.resId, null)
      })
    },
    resCreateCallback(name, id) {
      this.$set(this.dataForm, 'dockingResCreate', true)
      this.$set(this.dataForm, 'resName', name)
      this.$set(this.dataForm, 'resId', id)
    },
    clearRes() {
      this.$confirm(`确定清除您创建的这条资源？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        closeOnClickModal: false
      }).then(() => {
        this.dataForm.dockingResCreate = false
        this.dataForm.resName = null
        this.dataForm.resId = null
        this.getResList(this.dataForm.teamId, null, null)
        this.$message({
          message: '清除成功',
          type: 'success',
          duration: 1000
        })
      })
    },
    createReq() {
      this.$confirm(`如需要创建需求，您可先行完善好活动信息，需求创建时将为您带出相关信息！`, '提示', {
        confirmButtonText: '立即创建',
        cancelButtonText: '稍后创建',
        type: 'warning',
        closeOnClickModal: false
      }).then(() => {
        this.reqAddOrUpdateVisible = true
        this.$nextTick(() => {
          this.$refs.reqAddOrUpdate.fromActInit(null, this.actForm)
        })
      })
    },
    updateReq() {
      this.reqAddOrUpdateVisible = true
      this.$nextTick(() => {
        this.$refs.reqAddOrUpdate.fromActInit(this.dataForm.reqId, null)
      })
    },
    reqCreateCallback(name, id) {
      this.$set(this.dataForm, 'dockingReqCreate', true)
      this.$set(this.dataForm, 'reqName', name)
      this.$set(this.dataForm, 'reqId', id)
    },
    clearReq() {
      this.$confirm(`确定清除您创建的这条需求？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        closeOnClickModal: false
      }).then(() => {
        this.dataForm.dockingReqCreate = false
        this.dataForm.reqName = null
        this.dataForm.reqId = null
        this.getReqList(this.dataForm.id, this.dataForm.teamId)
        this.$message({
          message: '清除成功',
          type: 'success',
          duration: 1000
        })
      })
    }
  }
}
</script>
