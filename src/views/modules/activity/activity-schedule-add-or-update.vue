<template>
    <el-dialog :title="!dataForm.id ? '新增' : '编辑'" :close-on-click-modal="false" :visible.sync="visible"
               width="90%">
        <el-form :model="dataForm" :rules="dataRule" ref="dataForm" label-width="150px">
            <el-row :gutter="20">
                <el-col :span="12">
                    <el-form-item label="计划类型" prop="scheduleType">
                        <el-radio-group v-model="dataForm.scheduleType" @change="dateTypeSelectChange()">
                            <el-radio :label="'schedule_type_month'">月计划</el-radio>
                            <el-radio :label="'schedule_type_quarter'">季度计划</el-radio>
                            <el-radio :label="'schedule_type_year'">年计划</el-radio>
                        </el-radio-group>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="计划数值" prop="scheduleTypeData">
                        <el-date-picker
                                v-if="dataForm.scheduleType === 'schedule_type_month'"
                                v-model="dataForm.scheduleTypeData"
                                type="month"
                                format="yyyy-MM"
                                value-format="yyyyMM"
                                :editable="false"
                                placeholder="选择月"
                                @change="monthChange"
                        ></el-date-picker>
                        <span v-if="dataForm.scheduleType === 'schedule_type_quarter'">
      <!--季度时间选择控件 -->
                        <el-quarter-picker v-model="dataForm.scheduleTypeData" placeholder="选择季度"
                                           @change="quarterChange"/></span>
                        <el-date-picker
                                v-if="dataForm.scheduleType === 'schedule_type_year'"
                                v-model="dataForm.scheduleTypeData"
                                type="year"
                                format="yyyy"
                                value-format="yyyy"
                                :editable="false"
                                placeholder="选择年"
                                @change="yearChange"
                        ></el-date-picker>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row :gutter="20">
                <el-col :span="12">
                    <el-form-item label="计划主题" prop="title">
                        <el-input v-model="dataForm.title" placeholder="计划主题" maxlength="100"></el-input>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row>
                <el-col :span="3">
                    <el-form-item >
                        <el-button icon="el-icon-plus" type="primary" style="margin-bottom: 10px" @click="addHandle()" size="mini">
                            新增预告
                        </el-button>
                    </el-form-item>
                </el-col>
                <el-col :span="3" v-if="isAuth('schedule:detail:import')">
                    <el-form-item >
                        <el-button type="primary" @click="templateDownload()" size="mini">Excel模版下载</el-button>
                    </el-form-item>
                </el-col>
                <el-col :span="3" v-if="isAuth('schedule:detail:import')">
                    <el-form-item>
                        <el-upload
                                ref="upload"
                                name="excel"
                                :action="this.$http.adornUrl(`/admin/zyz/activity/schedule/detail/detailImport`)"
                                :headers="myHeaders"
                                :data="{ scheduleId: this.dataForm.id?this.dataForm.id:0  }"
                                :on-success="successHandle"
                                :on-change="changHandle"
                                accept="xlsx"
                                :limit="1"
                                :show-file-list="false"
                                :on-exceed="handleExceed"
                                :before-upload="function (file){return docBeforeUpload(file)}"
                                :file-list="fileList">
                            <el-button type="primary" size="mini">Excel上传</el-button>
                        </el-upload>
                    </el-form-item>
                </el-col>
                <el-col :span="9">
                    <el-form-item style="color: red">
                        *只能上传xls或xlsx文件，大小10M以内，请对数据去重
                    </el-form-item>
                </el-col>
                <el-col :span="4">
                    <el-form-item label-width="0px">
                        <a :href="$http.adornAttachmentUrl(dataForm.excelFile.path)" v-if="dataForm.excelFile"
                           style="cursor: pointer; margin-right: 5px">{{ dataForm.excelFile.fileName }}</a>
                        <i v-if="dataForm.excelFile" id="excel_remove" class="el-icon-delete"
                           style="cursor: pointer" @click="handleExcelRemove"></i>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-table id="detail_table" :key="tableKey" ref="detailTable" :data="dataForm.detailList" border
                      style="width: 100%;">
                <el-table-column
                        width="300"
                        header-align="center"
                        align="center"
                        label="活动预告">
                    <template slot-scope="scope">
                        <el-form-item :prop="`detailList.${scope.$index}.name`" :rules='detailRules.name'>
                            <el-input v-model="scope.row.name" maxlength="100"></el-input>
                        </el-form-item>
                    </template>
                </el-table-column>
                <el-table-column prop="startTime" width="450" header-align="center" align="center" label="活动起止时间">
                    <template slot-scope="scope">
                        <el-form-item :prop="`detailList.${scope.$index}.startEndTime`"
                                      :rules='detailRules.startEndTime'>
                            <single-day-datetime-range-picker
                                    v-model="scope.row.startEndTime"
                                    :date-options="pickerOptions"
                                    @input="(value) => refreshStartEndTime(value, scope.$index)"/>
                        </el-form-item>
                    </template>
                </el-table-column>
                <el-table-column prop="activityType" width="200" header-align="center" align="center" label="活动类别">
                    <template slot-scope="scope">
                        <el-form-item :prop="`detailList.${scope.$index}.activityType`"
                                      :rules='detailRules.activityType'>
                            <el-dict :code="'schedule_activity_type'" v-model="scope.row.activityType"></el-dict>
                        </el-form-item>
                    </template>
                </el-table-column>
                <el-table-column prop="subjects" width="200" header-align="center" align="center" label="实施主体">
                    <template slot-scope="scope">
                        <el-form-item :prop="`detailList.${scope.$index}.subjects`" :rules='detailRules.subjects'>
                            <el-select style="width: 100%" v-model="scope.row.subjects" placeholder="请选择" clearable
                                       allow-create
                                       filterable>
                                <el-option v-for="item in teamList" :key="item.name" :label="item.name"
                                           :value="item.name"/>
                            </el-select>
                        </el-form-item>
                    </template>
                </el-table-column>
                <el-table-column prop="targets" width="200" header-align="center" align="center" label="服务对象">
                    <template slot-scope="scope">
                        <el-form-item :prop="`detailList.${scope.$index}.targets`" :rules='detailRules.targets'>
                            <el-dict :code="'schedule_targets_type'" v-model="scope.row.targets"></el-dict>
                        </el-form-item>
                    </template>
                </el-table-column>
                <el-table-column prop="linkMan" width="200" header-align="center" align="center" label="联系人">
                    <template slot-scope="scope">
                        <el-form-item :prop="`detailList.${scope.$index}.linkMan`" :rules='detailRules.linkMan'>
                            <el-input v-model="scope.row.linkMan" placeholder="联系人" maxlength="50"></el-input>
                        </el-form-item>
                    </template>
                </el-table-column>
                <el-table-column prop="linkPhone" width="200" header-align="center" align="center" label="联系电话">
                    <template slot-scope="scope">
                        <el-form-item :prop="`detailList.${scope.$index}.linkPhone`" :rules='detailRules.linkPhone'>
                            <el-input v-model="scope.row.linkPhone" placeholder="联系电话"></el-input>
                        </el-form-item>
                    </template>
                </el-table-column>
                <el-table-column prop="address" width="300" header-align="center" align="center" label="活动地点">
                    <template slot-scope="scope">
                        <el-form-item :prop="`detailList.${scope.$index}.address`" :rules='detailRules.address'>
                            <el-input v-model="scope.row.address" placeholder="活动地点"></el-input>
                        </el-form-item>
                    </template>
                </el-table-column>
                <el-table-column fixed="right" width="120" header-align="center" align="center" label="操作">
                    <template slot-scope="scope">
                        <el-button type="text" size="small" @click="updateHandle(scope.$index)">修改</el-button>
                        <el-button type="text" size="small" @click="deleteHandle(scope.$index)">删除</el-button>
                    </template>
                </el-table-column>
            </el-table>
        </el-form>
        <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit(false)"
                 :disabled="submitLoading">保存草稿</el-button>
      <el-button type="success" @click="dataFormSubmit(true)"
                 :disabled="submitLoading">提交审核</el-button>
    </span>
    </el-dialog>
</template>

<script>

import ElQuarterPicker from './schedule-components/ElQuarterPicker.vue'
import moment from 'moment/moment'
import {uuid} from '@/utils'
import {is8lPhone, isMobile} from "@/utils/validate";
import Vue from "vue";
import _ from "lodash";

export default {
    data() {
        const validateTimeSelectable = (rule, value, callback) => {
            if (value.length === 0) {
                callback(new Error('起止时间未维护或维护不完全！'))
            }
            const startTime = new Date(value[0]).getTime(),
                endTime = new Date(value[1]).getTime(),
                hourMs = 60 * 60 * 1000,
                interval = endTime - startTime,
                minTimeLimit = this.minTimeLimit,
                maxTimeLimit = this.maxTimeLimit
            if (interval >= 24 * hourMs) {
                callback(new Error('活动时间须选择在当天一天之内！'))
            } else if (interval < minTimeLimit * hourMs) {
                callback(new Error('当前所属领域限制时长最少为' + minTimeLimit + '小时'))
            } else if (maxTimeLimit != null && (interval > maxTimeLimit * hourMs)) {
                callback(new Error('当前所属领域限制时长最多为' + maxTimeLimit + '小时'))
            } else {
                callback()
            }
        }
        const validateContactPhone = (rule, value, callback) => {
            if (!isMobile(value) && !is8lPhone(value)) {
                callback(new Error('联系电话须为手机号或区号+8位座机号例如051288888888或0512-88888888'))
            } else {
                callback()
            }
        }
        return {
            moment: moment,
            tableKey: uuid(),
            submitLoading: false,
            visible: false,
            posed: false,
            teamList: [],
            myHeaders: {Authorization: Vue.cookie.get('Authorization')},
            fileList: [],
            pickerOptions: this.processDate(),
            detailListLoading: null,
            dataForm: {
                id: null,
                version: null,
                scheduleType: 'schedule_type_month',
                scheduleTypeData: null,
                title: null,
                auditStatus: null,
                publishOrgCode: null,
                publishOrgName: null,
                pbId: null,
                teamId: null,
                teamPublish: null,
                auditOrgCode: null,
                sync: null,
                syncTime: null,
                syncRemark: null,
                schId: null,
                autoStatus: null,
                detailList: [],
                extensionFile: null,
                overLimit: null,
                excelFile: null,
            },
            dataRule: {
                scheduleType: [
                    {required: true, message: '计划类型不能为空', trigger: 'blur'}
                ],
                scheduleTypeData: [
                    {required: true, message: '计划数值不能为空', trigger: 'blur'}
                ],
                title: [
                    {required: true, message: '计划主题不能为空', trigger: 'blur'}
                ]
            },
            detailRules: {
                name: [
                    {required: true, message: '名称不能为空', trigger: 'change'}
                ],
                startEndTime: [{validator: validateTimeSelectable, trigger: 'change'}],
                activityType: [
                    {required: true, message: '活动类别不能为空', trigger: 'change'}
                ],
                subjects: [
                    {required: true, message: '实施主体不能为空', trigger: 'change'}
                ],
                targets: [
                    {required: true, message: '服务对象不能为空', trigger: 'change'}
                ],
                linkMan: [
                    {required: true, message: '联系人不能为空', trigger: 'change'}
                ],
                linkPhone: [
                    {required: true, message: '联系电话不能为空', trigger: 'change'},
                    {validator: validateContactPhone, trigger: 'change'}
                ],
                address: [
                    {required: true, message: '地址不能为空', trigger: 'change'}
                ],

            }
        }
    },
    components: {
        ElQuarterPicker
    },
    methods: {
        dateTypeSelectChange(val) {
            this.dataForm.scheduleTypeData = null
        },
        validateScheduleTypeData() {
            if (this.$refs.dataForm) {
                this.$refs.dataForm.validateField('scheduleTypeData');
            }
        },
        monthChange(val) {
            if (!this.dataForm.id) {
                if (this.$store.state.user.managerCapacityName) {
                    this.dataForm.title = this.$store.state.user.managerCapacityName.split('-')[1] + this.dataForm.scheduleTypeData + '月计划'
                }
            }
            this.validateScheduleTypeData();
            this.updateDetailListDates();
        },
        quarterChange(val) {
            if (!this.dataForm.id) {
                if (this.$store.state.user.managerCapacityName) {
                    this.dataForm.title = this.$store.state.user.managerCapacityName.split('-')[1] + this.dataForm.scheduleTypeData + '季度计划'
                }
            }
            this.validateScheduleTypeData();
            this.updateDetailListDates();
        },
        yearChange(val) {
            if (!this.dataForm.id) {
                if (this.$store.state.user.managerCapacityName) {
                    this.dataForm.title = this.$store.state.user.managerCapacityName.split('-')[1] + this.dataForm.scheduleTypeData + '年计划'
                }
            }
            this.validateScheduleTypeData();
            this.updateDetailListDates();
        },
        updateDetailListDates() {
            if (!this.dataForm.scheduleTypeData || !this.dataForm.detailList || this.dataForm.detailList.length === 0) {
                return;
            }
            
            let date;
            if (this.dataForm.scheduleType === 'schedule_type_month') {
                date = this.dataForm.scheduleTypeData + '01';
            } else if (this.dataForm.scheduleType === 'schedule_type_year') {
                date = this.dataForm.scheduleTypeData + '0101';
            } else {
                let y = this.dataForm.scheduleTypeData.substring(0, 4);
                let m = this.dataForm.scheduleTypeData.substring(5, 6) * 3 - 2;
                date = y + m.toString().padStart(2, '0') + '01';
            }
            
            this.dataForm.detailList.forEach(item => {
                const ds = moment(date).format('YYYY-MM-DD');
                const de = moment(date).hours(23).minutes(59).format('YYYY-MM-DD HH:mm:ss');
                item.startEndTime = [ds, de];
                item.startTime = moment(ds).format('YYYY-MM-DD HH:mm:ss');
                item.endTime = de;
            });
        },
        init(id) {
            this.dataForm.id = id || null
            this.dataForm.version = 0
            this.visible = true
            this.posed = false
            this.tableKey = uuid()
            this.dataForm.detailList = []
            this.teamList = []
            this.dataForm.excelFile = null
            this.fileList = []
            this.getTeams()
            this.$nextTick(() => {
                this.$refs['dataForm']?.resetFields()
                if (this.dataForm.id) {
                    this.$http({
                        url: this.$http.adornUrl(`/admin/zyz/activity/schedule/getDetail`),
                        method: 'get',
                        params: this.$http.adornParams({
                            scheduleId: this.dataForm.id
                        })
                    }).then(({data}) => {
                        if (data && data.code === 0) {
                            this.dataForm = data.obj
                            data.obj.detailList.forEach(it => {
                                if (it.startTime && it.endTime) {
                                    this.$set(it, 'showStartTime', moment(new Date(it.startTime)).format('YYYY-MM-DD HH:mm'))
                                    this.$set(it, 'showEndTime', moment(new Date(it.endTime)).format('HH:mm'))
                                    this.$set(it, 'startEndTime', [it.startTime, it.endTime])
                                }

                            })
                        }
                    })
                }
            })
        },
        // 表单提交
        dataFormSubmit(isRender) {
            if (!this.dataForm.detailList || this.dataForm.detailList.length == 0) {
                this.$message.error("请维护计划详情！")
                return
            }
            let method = ''
            this.$refs['dataForm'].validate((valid) => {
                if (valid) {
                    if (isRender) {
                        method = '/admin/zyz/activity/schedule/render'
                    } else {
                        method = `/admin/zyz/activity/schedule/saveOrUpdateToDraft`
                    }
                    if (isRender) {
                        this.$confirm(`确定进行提交操作?`, '提示',
                            {
                                confirmButtonText: '确定',
                                cancelButtonText: '取消',
                                type: 'warning',
                                closeOnClickModal: false
                            }
                        ).then(() => {
                            this.submitForm(method, this.dataForm)
                        })
                    } else {
                        this.submitForm(method, this.dataForm)
                    }

                }
            })
        },
        async submitForm(method, dataForm) {

            this.submitLoading = true
            this.$http({
                url: this.$http.adornUrl(method),
                method: 'post',
                data: this.$http.adornData(dataForm)
            }).then(({data}) => {
                if (data && data.code === 0) {
                    this.$message({
                        message: '操作成功',
                        type: 'success',
                        duration: 1500,
                        onClose: () => {
                            this.submitLoading = false
                            this.visible = false
                            this.$emit('refreshDataList')
                        }
                    })
                } else {
                    this.$message.error(data.msg)
                }
                this.submitLoading = false
            })
        },
        addHandle() {
            let timeRange = {
                name: '',
                startTime: '',
                endTime: '',
                startEndTime: [],
                activityType: '',
                subjects: '',
                targets: '',
                linkMan: '',
                linkPhone: '',
                address: '',
            }
            if (this.dataForm.scheduleTypeData) {
                let date = this.dataForm.scheduleTypeData + '01'
                if (this.dataForm.scheduleType === 'schedule_type_month') {
                    date = this.dataForm.scheduleTypeData + '01'

                } else if (this.dataForm.scheduleType === 'schedule_type_year') {
                    date = this.dataForm.scheduleTypeData + '0101'
                } else {
                    let y = this.dataForm.scheduleTypeData.substring(0, 4)
                    let m = this.dataForm.scheduleTypeData.substring(5, 6) * 3 - 2
                    date = y + m.toString().padStart(2, '0') + '01'
                }
                let ds = moment(date).format('YYYY-MM-DD')
                let de = moment(date).hours(23).minutes(59)
                timeRange.startEndTime = [ds, de]
                timeRange.startTime = moment(ds).format('YYYY-MM-DD HH:mm:ss')
                timeRange.endTime = moment(de).format('YYYY-MM-DD HH:mm:ss')
            }
            this.dataForm.detailList.push(timeRange)

        },
        updateHandle(index) {
            this.$set(this.dataForm.detailList[index], 'editFlag', true)

        },
        deleteHandle(id) {
            this.dataForm.detailList.splice(id, 1)
        },
        refreshStartEndTime(value, index) {
            this.$set(this.dataForm.detailList[index], 'startTime', value[0])
            this.$set(this.dataForm.detailList[index], 'endTime', value[1])
            this.$set(this.dataForm.detailList[index], 'startEndTime', value)
        },
        //获取组织团队
        getTeams() {
            this.$http({
                url: this.$http.adornUrl(`/admin/zyz/team/getOrgTeamList`),
                method: 'get',
                params: this.$http.adornParams()
            }).then(({data}) => {
                if (data && data.code === 0) {
                    this.teamList = data.obj
                } else {
                    this.teamList = []
                }
                let orgData = {
                    name: this.$store.state.user.managerCapacityName.split('-')[1],
                }
                this.teamList.unshift(orgData)
            })
        },
        processDate() {
            const self = this
            return {
                disabledDate(time) {
                    if (self.dataForm.scheduleTypeData) {
                        if (self.dataForm.scheduleType === 'schedule_type_month') {
                            let date = self.dataForm.scheduleTypeData + '01'
                            return moment(time).isBefore(moment(date).subtract(0, 'months')) || moment(time).isAfter(moment(date).add(1, 'months'))
                        } else if (self.dataForm.scheduleType === 'schedule_type_year') {
                            let date = self.dataForm.scheduleTypeData + '0101'
                            return moment(time).isBefore(moment(date).subtract(0, 'years')) || moment(time).isAfter(moment(date).add(1, 'years'))
                        } else {
                            let y = self.dataForm.scheduleTypeData.substring(0, 4)
                            let m = self.dataForm.scheduleTypeData.substring(5, 6) * 3 - 2
                            let date = y + m.toString().padStart(2, '0') + '01'
                            return moment(time).isBefore(moment(date).subtract(0, 'months')) || moment(time).isAfter(moment(date).add(3, 'months'))
                        }


                    }

                }
            }
        },
        // 下载模板文件
        templateDownload() {
            this.$http({
                url: this.$http.adornUrl('/admin/zyz/activity/schedule/detail/excelTemplate'),
                method: 'get',
                responseType: 'arraybuffer',
                params: this.$http.adornParams()
            }).then(({data}) => {
                if (data.code && data.code !== 0) {
                    this.$message.error('下载失败')
                } else {
                    let blob = new Blob([data], {type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8'})
                    let objectUrl = URL.createObjectURL(blob)
                    let a = document.createElement('a')
                    // window.location.href = objectUrl
                    a.href = objectUrl
                    a.download = '明细导入模版.xlsx'
                    a.click()
                    URL.revokeObjectURL(objectUrl)
                    this.$message({
                        type: 'success',
                        message: '下载模板成功'
                    })
                }
            })
        },
        // 上传成功
        successHandle(response) {
            if (response && response.code === 0) {
                this.dataForm.overLimit = response.obj.overLimit
                this.dataForm.excelFile = response.obj.ossObj
                if (response.obj.overLimit === true) {
                    this.$message.warning('由于您上传的数据量超过2000，考虑到网页性能，将不为您展示上传数据！如需修改，您可以下载上传的文件进行数据修改后重新上传！')
                    this.detailListLoading = false
                    return
                }
                _.forEach(response.obj.detailList, it => {
                    let data = {
                        name: it.name,
                        startTime: it.startTime,
                        endTime: it.endTime,
                        startEndTime: [it.startTime, it.endTime],
                        activityType: it.activityType,
                        subjects: it.subjects,
                        targets: it.targets,
                        linkMan: it.linkMan,
                        linkPhone: it.linkPhone,
                        address: it.address
                    }
                    if (this.dataForm.detailList.indexOf(data) == -1) {
                        this.dataForm.detailList.unshift(data)
                    }

                })
            } else {
                this.$message.error(response.msg)
                this.fileList = []
                this.$refs.upload.clearFiles()
            }
            this.detailListLoading = false
        },
        handleExcelRemove() {
            this.$confirm('确定要删除该文件吗?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.dataForm.excelFile = null
                this.fileList = []
                this.$refs.upload.clearFiles()
                this.$message({
                    type: 'success',
                    message: '删除成功!'
                })
            }).catch(() => {})
        },
        changHandle(file, fileList) {
            let FileExt = file.name.replace(/.+\./, '')
            const isLt10M = file.size / 1024 / 1024 < 10
            if (['xlsx', 'xls'].indexOf(FileExt.toLowerCase()) === -1) {
                this.$message({
                    type: '上传失败',
                    message: '请上传后缀名为xlsx或xls的文件！'
                })
                this.fileList = []
                return
            }
            if (!isLt10M) {
                this.$message.error('上传文件大小不能超过 10M!')
                this.fileList = []
                return
            }
            this.fileList = fileList
        },
        handleExceed() {
            this.$message.error('只能选择一个文件，如需替换请删除已选文件后重试')
        },
        docBeforeUpload(file) {
            let FileExt = file.name.replace(/.+\./, '')
            if (['xlsx', 'xls'].indexOf(FileExt.toLowerCase()) === -1) {
                this.$message({
                    type: '上传失败',
                    message: '请上传后缀名为xlsx, xls的附件！'
                })
                return false
            }
            const isLt10M = file.size / 1024 / 1024 < 10
            if (['xlsx', 'xls'].indexOf(FileExt.toLowerCase()) !== -1 && !isLt10M) {
                this.$message.error('附件大小不能超过 10M!')
                return false
            }
            this.sendPersonListLoading = true
        },


    }
}
</script>

<style scoped lang="scss">
#detail_table ::v-deep .el-form-item {
  margin-bottom: 0px;
  margin-left: -150px;
}

#detail_table::v-deep .el-form-item__error {
  position: static;
  text-align: center;
}
</style>
