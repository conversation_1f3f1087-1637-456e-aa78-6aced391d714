<template>
    <div class="mod-config">
        <el-form :inline="true" :model="dataForm" ref="dataForm" @keyup.enter="queryPage()">
            <el-form-item label="计划主题" prop="key" style="padding-right: 170px">
                <el-input v-model="dataForm.key" placeholder="计划主题" clearable style="width: 180%"></el-input>
            </el-form-item>
            <el-form-item :label="'上级组织'" prop="publishOrgCodes">
                <el-cascader placeholder="请选择" v-model="dataForm.publishOrgCodes" :options="orgList"
                             :disabled="this.dataForm.id"
                             :show-all-levels="false" clearable
                             :props="{ checkStrictly: true, value: 'code', label: 'name' }"
                             @change="(value) => { handleChange(value, 'org') }"></el-cascader>
            </el-form-item>
            <el-form-item label="发布时间" prop="timeRange">
                <el-date-picker v-model="dataForm.timeRange" clearable type="daterange"
                                value-format="yyyy-MM-dd" align="right" start-placeholder="开始时间"
                                end-placeholder="结束时间">
                </el-date-picker>
            </el-form-item>
            <el-form-item label="计划类型" prop="scheduleType">
                <el-dict :code="'activity_schedule_type'" v-model="dataForm.scheduleType" @change="dateTypeSelectChange()"></el-dict>
            </el-form-item>
            <el-form-item label="计划数值" prop="scheduleTypeData" v-if="dataForm.scheduleType">
                <el-date-picker
                        v-if="dataForm.scheduleType === 'schedule_type_month'"
                        v-model="dataForm.scheduleTypeData"
                        type="month"
                        format="yyyy-MM"
                        value-format="yyyyMM"
                        :editable="false"
                        placeholder="选择月"
                ></el-date-picker>
                <span v-if="dataForm.scheduleType === 'schedule_type_quarter'">
      <!--季度时间选择控件 -->
                        <el-quarter-picker v-model="dataForm.scheduleTypeData" placeholder="选择季度"
                        /></span>
                <el-date-picker
                        v-if="dataForm.scheduleType === 'schedule_type_year'"
                        v-model="dataForm.scheduleTypeData"
                        type="year"
                        format="yyyy"
                        value-format="yyyy"
                        :editable="false"
                        placeholder="选择年"
                ></el-date-picker>
            </el-form-item>
            <el-form-item label="审核状态" prop="auditStatus">
                <el-dict :code="'activity_schedule_audit_status'" v-model="dataForm.auditStatus"></el-dict>
            </el-form-item>
            <el-form-item>
                <el-button icon="el-icon-search" type="warning" @click="queryPage()">查询</el-button>
                <el-button @click="resetForm()" icon="el-icon-refresh-left">重置</el-button>
            </el-form-item>
        </el-form>
        <div class="f-s-c" style="padding: 15px 0;display: flex;justify-content:flex-end;align-items: center ">
            <div>
                <el-button icon="el-icon-plus" type="primary" @click="addOrUpdateHandle(null)">新增</el-button>
                <el-button icon="el-icon-download" type="success" v-loading.fullscreen.lock="fullscreenLoading" @click="exportScheduleDetail()">导出</el-button>
            </div>
        </div>
        <el-table
                :data="dataList"
                border
                v-loading="dataListLoading"
                style="width: 100%;">
            <el-table-column
                    prop="title"
                    header-align="center"
                    align="center"
                    label="计划主题">
                <template slot-scope="scope">
                    <a style="cursor: pointer" @click="detailHandle(scope.row.id)">{{ scope.row.title }}</a>
                </template>
            </el-table-column>
            <el-table-column
                prop="countDisplay"
                header-align="center"
                align="center"
                width="160"
                label="预告/关联活动">
            </el-table-column>
            <el-table-column
                    prop="scheduleTypeText"
                    header-align="center"
                    align="center"
                    width="80"
                    label="计划类型">
            </el-table-column>
            <el-table-column
                    prop="scheduleTypeData"
                    header-align="center"
                    align="center"
                    width="120"
                    label="计划数值">
            </el-table-column>
            <el-table-column
                    prop="publishOrgName"
                    header-align="center"
                    align="center"
                    label="发布单位">
            </el-table-column>
            <el-table-column
                    prop="creatorName"
                    header-align="center"
                    align="center"
                    width="80"
                    label="发布人">
            </el-table-column>
            <el-table-column
                    prop="createDate"
                    header-align="center"
                    align="center"
                    width="180"
                    label="发布时间">
            </el-table-column>
            <el-table-column
                    prop="auditStatusText"
                    header-align="center"
                    align="center"
                    width="80"
                    label="状态">
                <template slot-scope="scope">
                    <div>
                        <div v-if="scope.row.auditStatus === 'act_schedule_audit_success'">
                            <div v-if="scope.row.autoStatus">上架中</div>
                            <div v-else>已下架</div>
                        </div>
                        <div v-else>{{ scope.row.auditStatusText }}</div>
                    </div>
                </template>
            </el-table-column>
            <el-table-column
                    prop="syncText"
                    header-align="center"
                    align="center"
                    width="120"
                    label="同步状态">
                <template slot-scope="scope">
                    <el-popover trigger="hover" placement="top">
                        <p style="text-align: center">
                            {{
                            '同步时间：' + (scope.row.syncTime ? scope.row.syncTime : '')
                            }}<br>{{ scope.row.syncRemark }}</p>
                        <div slot="reference" class="name-wrapper">
                            <el-tag v-if="scope.row.sync === 'sync_failure'" type="danger">{{
                                scope.row.syncText
                                }}
                            </el-tag>
                            <el-tag v-if="scope.row.sync === 'sync_wait'" type="warning">{{
                                scope.row.syncText
                                }}
                            </el-tag>
                            <el-tag v-if="scope.row.sync === 'sync_success'" type="success">{{
                                scope.row.syncText
                                }}
                            </el-tag>
                        </div>
                    </el-popover>
                </template>
            </el-table-column>
            <el-table-column
                    fixed="right"
                    header-align="center"
                    align="center"
                    width="150"
                    label="操作">
                <template #default="scope">
                    <el-button size="small" type="text" v-if="scope.row.enable"
                               @click="addOrUpdateHandle(scope.row.id)">修改
                    </el-button>
                    <el-button size="small" type="text" v-if="scope.row.renderEnable" @click="render(scope.row.id)">
                        提交审核
                    </el-button>
                    <el-button size="small" type="text" v-if="scope.row.recallEnable"
                               @click="recallHandle(scope.row.id)">撤回
                    </el-button>
                    <el-button size="small" type="text" v-if="scope.row.syncEnable && isAuth('schedule:sync')" @click="syncHandle(scope.row.id)">
                        同步
                    </el-button>
                    <el-button size="small" type="text" v-if="scope.row.autoEnable"
                               @click="updateAutoStatus(scope.row.id, scope.row.autoStatus)">
                        {{ !scope.row.autoStatus ? '上架' : '下架' }}
                    </el-button>
                    <el-button size="small" type="text" v-if="scope.row.autoEnable"
                               @click="planAddOrUpdateHandle(scope.row.id)">
                     关联活动
                    </el-button>
                </template>
            </el-table-column>
        </el-table>
        <div class="com-pagination">
            <el-pagination
                    @size-change="sizeChangeHandle"
                    @current-change="currentChangeHandle"
                    :current-page="pageIndex"
                    :page-sizes="[10, 20, 50, 100]"
                    :page-size="pageSize"
                    :total="totalPage"
                    layout="total, sizes, prev, pager, next, jumper"
            />
        </div>
        <!-- 弹窗, 新增 / 修改 -->
        <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"></add-or-update>
        <detail v-if="detailVisible" ref="detail" ></detail>
        <plan-add-or-update v-if="planAddOrUpdateVisible" ref="planAddOrUpdate" @refreshDataList="getDataList"></plan-add-or-update>
    </div>
</template>

<script>
import AddOrUpdate from './activity-schedule-add-or-update.vue'
import PlanAddOrUpdate from './activity-schedule-plan-add-or-update.vue'
import ElQuarterPicker from "@/views/modules/activity/schedule-components/ElQuarterPicker.vue";
import Detail from './activity-schedule-detail'

export default {
    data() {
        return {
            dataForm: {
                key: null,
                scheduleType: null,
                scheduleTypeData: null,
                publishOrgCodes: [],
                publishOrgCode: null,
            },
            dataList: [],
            orgList: [],
            pageIndex: 1,
            pageSize: 10,
            totalPage: 0,
            dataListLoading: false,
            addOrUpdateVisible: false,
            planAddOrUpdateVisible:false,
            detailVisible:false,
           fullscreenLoading: false,
        }
    },
    components: {
        Detail,
        ElQuarterPicker,
        AddOrUpdate,
        PlanAddOrUpdate
    },
    activated() {
        this.getOrg()
        this.queryPage()
    },
    methods: {
        dateTypeSelectChange(val) {
            this.dataForm.scheduleTypeData = null
        },
        queryPage() {
            this.pageIndex = 1
            this.getDataList()
        },
        // 获取数据列表
        getDataList() {
            this.dataListLoading = true
            this.$http({
                url: this.$http.adornUrl('/admin/zyz/activity/schedule/pagesForSchedule'),
                method: 'post',
                data: this.$http.adornData({
                    'currentPage': this.pageIndex,
                    'pageSize': this.pageSize,
                    'key': this.dataForm.key,
                    'auditStatus': this.dataForm.auditStatus,
                    'publishOrgCode': this.dataForm.publishOrgCode,
                    'startTime': this.dataForm.timeRange && this.dataForm.timeRange.length === 2 ? this.dataForm.timeRange[0] + ' 00:00:00' : null,
                    'endTime': this.dataForm.timeRange && this.dataForm.timeRange.length === 2 ? this.dataForm.timeRange[1] + ' 23:59:59' : null,
                    'scheduleType': this.dataForm.scheduleType,
                    'scheduleTypeData': this.dataForm.scheduleTypeData
                })
            }).then(({data}) => {
                if (data && data.code === 0) {
                    this.dataList = data.obj.records
                    this.dataList.forEach(item => {
                        // 判断是否为本组织创建
                        let own = ( item.teamPublish && this.$store.state.user.managerCapacity === 'TEAM_ADMIN' && item.publishTeamId === this.$store.state.user.teamId) ||
                            ((!( item.teamPublish)) && this.$store.state.user.managerCapacity !== 'TEAM_ADMIN' && item.publishOrgCode === this.$store.state.user.orgCode)
                        // 判断草稿,驳回状态的操作
                        item.enable = own && (item.auditStatus === 'act_schedule_draft' || item.auditStatus === 'act_schedule_reject')
                        // 判断撤回
                        item.recallEnable = own && item.auditStatus === 'act_schedule_wait_audit'
                        // 判断同步
                        item.syncEnable = item.auditStatus === 'act_schedule_audit_success' && (item.sync === 'sync_wait' || item.sync === 'sync_failure')
                        // 判断上下架
                        item.autoEnable = item.auditStatus === 'act_schedule_audit_success'
                        item.renderEnable =own &&  (item.auditStatus === 'act_schedule_draft' || item.auditStatus === 'act_schedule_reject'  )
                        item.countDisplay = item.detailCount+'/'+ item.joinActCount
                    })
                    this.totalPage = data.obj.total
                } else {
                    this.dataList = []
                    this.totalPage = 0
                }
                this.dataListLoading = false
            })
        },
        // 每页数
        sizeChangeHandle(val) {
            this.pageSize = val
            this.pageIndex = 1
            this.getDataList()
        },
        // 当前页
        currentChangeHandle(val) {
            this.pageIndex = val
            this.getDataList()
        },
        // 多选
        selectionChangeHandle(val) {
            this.dataListSelections = val
        },
        // 重置
        resetForm() {
            this.$nextTick(() => {
                this.$refs['dataForm'].resetFields()
                this.dataForm.publishOrgCode = null
                this.getDataList()
            })
        },
        // 新增 / 修改
        addOrUpdateHandle(id) {
            this.addOrUpdateVisible = true
            this.$nextTick(() => {
                this.$refs.addOrUpdate.init(id)
            })
        },
        // 新增 / 修改
        planAddOrUpdateHandle(id) {
            this.planAddOrUpdateVisible = true
            this.$nextTick(() => {
                this.$refs.planAddOrUpdate.init(id)
            })
        },
        //获取所属区域
        getOrg() {
            this.$http({
                url: this.$http.adornUrl(`/admin/org/getOrgTree`),
                method: 'get',
                params: this.$http.adornParams()
            }).then(({data}) => {
                if (data && data.code === 0) {
                    this.orgList = this.getTreeData(data.obj)
                } else {
                    this.orgList = []
                }
            })
        },
        // *处理点位分类最后children数组为空的状态
        getTreeData: function (data) {
            var that = this
            // 循环遍历json数据
            data.forEach(function (e) {
                if (!e.children || e.children.length < 1) {
                    e.children = undefined
                } else {
                    // children若不为空数组，则继续 递归调用 本方法
                    that.getTreeData(e.children)
                }
            })
            return data
        },
        handleChange(value, type) {
            if (type === 'field') {
                this.dataForm.fieldId = value[value.length - 1]
            }
            if (type === 'org') {
                this.dataForm.publishOrgCode = value && value.length > 0 ? value[value.length - 1] : null
            }
        },
        // 删除
        deleteHandle(id) {
            var ids = id ? [id] : this.dataListSelections.map(item => {
                return item.id
            })
            this.$confirm(`确定进行[${id ? '删除' : '批量删除'}]操作?`, '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning',
                closeOnClickModal: false
            }).then(() => {
                this.$http({
                    url: this.$http.adornUrl('/admin/zyz/activity/removeByIds'),
                    method: 'get',
                    params: this.$http.adornParams({
                        'ids': ids.join(',')
                    })
                }).then(({data}) => {
                    if (data && data.code === 0) {
                        this.$message({
                            message: '操作成功',
                            type: 'success',
                            duration: 1500,
                            onClose: () => {
                                this.getDataList()
                            }
                        })
                    } else {
                        this.$message.error(data.msg)
                    }
                })
            })
        },
        // 提交
        render(id) {
            this.$confirm(`确定进行提交操作?`, '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning',
                closeOnClickModal: false
            }).then(() => {
                this.$http({
                    url: this.$http.adornUrl('/admin/zyz/activity/schedule/renderById'),
                    method: 'get',
                    params: this.$http.adornParams({
                        'id': id
                    })
                }).then(({data}) => {
                    if (data && data.code === 0) {
                        this.$message({
                            message: '操作成功',
                            type: 'success',
                            duration: 1500,
                            onClose: () => {
                                this.getDataList()
                            }
                        })
                    } else {
                        this.$message.error(data.msg)
                    }
                })
            })
        },
        recallHandle(id) {
            this.$confirm(`确定进行撤回操作?撤回后将变为草稿状态`, '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning',
                closeOnClickModal: false
            }).then(() => {
                this.$http({
                    url: this.$http.adornUrl('/admin/zyz/activity/schedule/recall'),
                    method: 'get',
                    params: this.$http.adornParams({
                        'id': id
                    })
                }).then(({data}) => {
                    if (data && data.code === 0) {
                        this.$message({
                            message: '操作成功',
                            type: 'success',
                            duration: 1500,
                            onClose: () => {
                                this.getDataList()
                            }
                        })
                    } else {
                        this.$message.error(data.msg)
                    }
                })
            })
        },
        detailHandle(id) {
            this.detailVisible = true
            this.$nextTick(() => {
                this.$refs.detail.init(id)
            })
        },
        // 同步
        syncHandle(scheduleId) {
            this.$confirm(`确定是否进行同步，请谨慎操作！`, '确认同步？', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning',
                closeOnClickModal: false
            }).then(() => {
                this.dataListLoading = true
                this.$http({
                    url: this.$http.adornUrl('/admin/zyz/platform-sync/syncActSchedule'),
                    method: 'post',
                    params: this.$http.adornParams({
                        'scheduleId': scheduleId
                    })
                }).then(({data}) => {
                    this.dataListLoading = false
                    if (data && data.code === 0) {
                        this.$message({
                            message: '请求同步成功，市平台返回：（' + data.obj.message + '）',
                            type: data.obj.resultCode === 'SUCCESS' ? 'success' : 'warning',
                            duration: 3000,
                            onClose: () => {
                                this.getDataList()
                            }
                        })
                    } else {
                        this.$message.error(data.msg)
                    }
                })
            })
        },
        // 上下架
        updateAutoStatus(id, autoStatus) {
            this.$confirm(`确定进行[${!!autoStatus ? '下架' : '上架'}]操作?`, '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning',
                closeOnClickModal: false
            }).then(() => {
                this.$http({
                    url: this.$http.adornUrl('/admin/zyz/activity/schedule/updateAutoStatus'),
                    method: 'get',
                    params: this.$http.adornParams({
                        'id': id
                    })
                }).then(({data}) => {
                    if (data && data.code === 0) {
                        this.$message({
                            message: '操作成功',
                            type: 'success',
                            duration: 1500,
                            onClose: () => {
                                this.getDataList()
                            }
                        })
                    } else {
                        this.$message.error(data.msg)
                    }
                })
            })
        },
        //导出
        exportScheduleDetail() {
            this.fullscreenLoading = true;
            this.$http({
                url: this.$http.adornUrl('/admin/zyz/activity/schedule/detail/exportScheduleDetail'),
                method: 'post',
                responseType: 'arraybuffer',
                data: this.$http.adornData({
                    'currentPage': this.pageIndex,
                    'pageSize': this.pageSize,
                    'key': this.dataForm.key,
                    'auditStatus': this.dataForm.auditStatus,
                    'publishOrgCode': this.dataForm.publishOrgCode,
                    'startTime': this.dataForm.timeRange && this.dataForm.timeRange.length === 2 ? this.dataForm.timeRange[0] + ' 00:00:00' : null,
                    'endTime': this.dataForm.timeRange && this.dataForm.timeRange.length === 2 ? this.dataForm.timeRange[1] + ' 23:59:59' : null,
                    'scheduleType': this.dataForm.scheduleType,
                    'scheduleTypeData': this.dataForm.scheduleTypeData
                })
            }).then(({data}) => {
                if (data.code && data.code !== 0) {
                    this.$message.error('导出失败')
                } else {
                    this.fullscreenLoading = false
                    let blob = new Blob([data], {type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8'})
                    let objectUrl = URL.createObjectURL(blob)
                    let a = document.createElement('a')
                    // window.location.href = objectUrl
                    a.href = objectUrl
                    a.download = '阵地计划明细.xlsx'
                    a.click()
                    URL.revokeObjectURL(objectUrl)
                    this.$message({
                        type: 'success',
                        message: '导出成功'
                    })
                }
            })
        },
    }
}
</script>
