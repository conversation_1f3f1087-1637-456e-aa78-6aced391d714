<template>
  <div class="mod-list">
    <el-form :inline="true" :model="dataForm" ref="dataForm" @keyup.enter.native="getDataList()">
      <el-form-item prop="keyword" label="关键字">
        <el-input v-model="dataForm.keyword" placeholder="请输入团队名称" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-search" type="warning" @click="getDataList()">查询</el-button>
        <el-button @click="resetForm()"icon="el-icon-refresh-left">重置</el-button>
      </el-form-item>
    </el-form>
    <div class="f-s-c" style="padding: 15px 0; display: flex; justify-content: flex-end; align-items: center">
      <div>
        <el-button type="primary" @click="addOrUpdateHandle()" icon="el-icon-plus">新增授权</el-button>
      </div>
    </div>
    <el-table :data="dataList" border v-loading="dataListLoading" @selection-change="selectionChangeHandle" style="width: 100%;">
      <el-table-column type="index" header-align="center" align="center" width="50" label="序号"/>
      <el-table-column prop="teamName" header-align="center" align="center" label="团队名称"/>
      <el-table-column prop="teamAdminName" header-align="center" align="center" label="团队管理员"/>
      <el-table-column prop="teamAdminContact" header-align="center" align="center" label="管理员联系方式"/>
      <el-table-column prop="grantTime" header-align="center" align="center" label="授权时间"/>
      <el-table-column prop="grantTime" header-align="center" align="center" label="授权状态">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.status" type="success">已授权</el-tag>
          <el-tag v-else type="danger">未授权</el-tag>
        </template>
      </el-table-column>
      <el-table-column fixed="right" header-align="center" align="center" width="150" label="操作">
        <template slot-scope="scope">
          <el-button type="text" @click="grantOperation(scope.row.id, scope.row.teamId, scope.row.status)">{{scope.row.status ? '取消授权' : '授权'}}</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination @size-change="sizeChangeHandle" @current-change="currentChangeHandle" :current-page="pageIndex"
                   :page-sizes="[10, 20, 50, 100]" :page-size="pageSize" :total="totalPage" layout="total, sizes, prev, pager, next, jumper"/>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"/>
  </div>
</template>

<script>
  import AddOrUpdate from './big-activity-team-grant-add-or-update'
  import listMixin from '@/mixins/list-mixins'
  export default {
    mixins: [listMixin],
    data () {
      return {
        mixinOptions: {
          dataUrl: '/admin/zyz/team/grantBigActivity/pages',
          deleteUrl: '/admin/zyz/team/grantBigActivity/removeByIds'
        },
        dataForm: {
          keyword: null
        }
      }
    },
    components: {
      AddOrUpdate
    },
    methods: {
      resetForm() {
        this.$refs['dataForm']?.resetFields()
        this.getDataList()
      },
      grantOperation(id, teamId, status) {
        this.$confirm(`确定进行${status ? '取消授权' : '授权'}操作?`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
          closeOnClickModal: false
        }).then(() => {
          this.$http({
            url: this.$http.adornUrl(`/admin/zyz/team/grantBigActivity/teamGrant`),
            method: 'get',
            params: this.$http.adornParams({
              'grantId': id,
              'teamId': teamId
            })
          }).then(({data}) => {
            if (data && data.code === 0) {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 1000,
                onClose: () =>{}
              })
              this.query()
            } else {
              this.$message.error(data.msg)
            }
          })
        }).catch(() => {
        })
      }
    }
  }
</script>

<style lang="scss" scoped>
</style>
