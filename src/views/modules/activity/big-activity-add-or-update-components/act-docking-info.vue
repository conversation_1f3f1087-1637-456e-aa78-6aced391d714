<template>
  <el-card style="margin-top: 20px">
    <div slot="header" class="clearfix"><span>对接信息</span></div>
    <el-form :model="dockingForm" :rules="dataRule" ref="dockingForm" label-width="140px">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item v-if="dockingForm.dockingReqCreate && dockingForm.dockingReqCreate === true && (!dockingForm.auditStatus || dockingForm.auditStatus !== 'act_audit_success')" label="新创建的需求" prop="reqName">
            <el-input style="width: 60%" v-model="dockingForm.reqName" disabled/>
            <el-button icon="el-icon-edit" style="width:17%; margin-left: 3%" type="primary" @click="updateReq">修改</el-button>
            <el-button icon="el-icon-delete" style="width:17%; float: right" type="danger" @click="clearReq">清除</el-button>
          </el-form-item>
          <el-form-item v-else label="选择对接的需求" prop="reqId">
<!--            <el-select style="width: 70%" filterable v-model="dockingForm.reqId" disabled placeholder="请创建" clearable>-->
<!--              <el-option v-for="item in reqList" :key="item.id" :label="item.name" :value="item.id">-->
<!--                <span style="float: left">{{ item.name }}</span>-->
<!--                <span style="float: right; color: #8492a6; font-size: 8px">{{ item.startTime + '&#45;&#45;' + item.endTime }}</span>-->
<!--              </el-option>-->
<!--            </el-select>-->
            <el-input style="width: 70%" v-model="dockingForm.reqName" disabled placeholder="请创建需求"/>
            <el-button icon="el-icon-plus" style="width:20%; float: right" type="primary" @click="createReq">新建</el-button>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item v-if="dockingForm.dockingResCreate && dockingForm.dockingResCreate === true && (!dockingForm.auditStatus || dockingForm.auditStatus !== 'act_audit_success')" label="新创建的资源" prop="resName">
            <el-input style="width: 60%" v-model="dockingForm.resName" disabled/>
            <el-button icon="el-icon-edit" style="width:17%; margin-left: 3%" type="primary" @click="updateRes">修改</el-button>
            <el-button icon="el-icon-delete" style="width:17%; float: right" type="danger" @click="clearRes">清除</el-button>
          </el-form-item>
          <el-form-item v-else label="选择对接的资源" prop="resId">
            <el-select style="width: 100%" v-model="dockingForm.resId" disabled filterable remote :remote-method="remoteResourceSearch" placeholder="请输入要选择的资源名" clearable @blur="blurSearch" @clear="remoteResourceSearch(null)">
              <el-option v-for="item in resList" :key="item.id" :label="item.name" :value="item.id">
                <span style="float: left">{{ item.name }}</span>
                <span style="float: right; color: #8492a6; font-size: 8px">{{ item.publishOrgName }}</span>
              </el-option>
            </el-select>
<!--            <el-button icon="el-icon-plus" style="width:20%; float: right" type="primary" @click="createRes">新建</el-button>-->
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <req-add-or-update v-if="reqAddOrUpdateVisible" ref="reqAddOrUpdate" @reqCreateCallback="reqCreateCallback"/>
    <res-add-or-update v-if="resAddOrUpdateVisible" ref="resAddOrUpdate" @resCreateCallback="resCreateCallback"/>
  </el-card>
</template>

<script>
import ReqAddOrUpdate from '../../requirement/requirement-add-or-update';
import ResAddOrUpdate from '../../resource/resource-add-or-update';
import _ from "lodash";

export default {
  name: 'act-docking-info',
  props: ['actId', 'teamId', 'copy', 'dockingMaintain'],
  components: {ReqAddOrUpdate, ResAddOrUpdate},
  data() {
    return {
      reqAddOrUpdateVisible: false,
      resAddOrUpdateVisible: false,
      resList: [],
      reqList: [],
      dockingForm: {
        auditStatus: null,
        resId: null,
        reqId: null,
        dockingResCreate: false,
        resName: null,
        dockingReqCreate: false,
        reqName: null
      },
      dataRule: {
        resId: [{required: true, message: '对接资源不能为空', trigger: 'change'}],
        reqId: [{required: true, message: '对接需求不能为空', trigger: 'change'}],
        resName: [{required: true, message: '新创资源不能为空', trigger: 'change'}],
        reqName: [{required: true, message: '新创需求不能为空', trigger: 'change'}]
      }
    }
  },
  mounted() {
    this.getReqList(this.teamId)
    this.getResList(this.teamId, null, null)
  },
  methods: {
    init (dockingForm) {
      if (dockingForm.dockingResCreate && dockingForm.auditStatus !== 'act_audit_success') {
        this.getResList(this.teamId, null, null)
      } else {
        this.getResList(this.teamId, null, dockingForm.resId)
        this.resSelected = true
      }
      this.getReqList(this.teamId, this.actId)
      this.dockingForm = _.cloneDeep(dockingForm)
      if (this.copy) {
        if (this.dockingForm.dockingResCreate) {
          this.dockingForm.dockingResCreate = false
          if (this.dockingForm.auditStatus !== 'act_audit_success') {
            this.dockingForm.resId = null
            this.resSelected = false
          }
        }
        this.dockingForm.auditStatus = null
        this.dockingForm.reqId = null
        this.dockingForm.reqName = null
        this.dockingForm.dockingReqCreate = false
        if (this.dockingForm.resId) {
          this.$emit('update:dockingMaintain', true)
        }
      } else {
        this.$emit('update:dockingMaintain', true)
      }
    },
    refreshDockingInfo() {
      this.getResList(this.teamId, null, null)
      this.getReqList(this.teamId)
      this.dockingForm.resId = null
      this.dockingForm.reqId = null
      this.dockingForm.resName = null
      this.dockingForm.reqName = null
      this.dockingForm.dockingResCreate = false
      this.dockingForm.dockingReqCreate = false
      this.$emit('update:dockingMaintain', false)
    },
    remoteResourceSearch (searchName) {
      this.getResList(this.teamId, searchName, null)
    },
    blurSearch() {
      setTimeout(() => {
        console.log(this.dockingForm.resId)
        if (!this.dockingForm.resId || this.dockingForm.resId === '') {
          this.remoteResourceSearch(null)
        }
      }, 300)
    },
    getResList(teamId, searchName, pointId) {
      this.$http({
        url: this.$http.adornUrl('/admin/zyz/resource/getEnableDocking'),
        method: 'get',
        params: this.$http.adornParams({
          'teamId': teamId,
          'searchName': searchName,
          'pointResourceId': pointId
        })
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.resList = data.obj
          this.dockingForm.resId = '999999999999999999'
        } else {
          this.resList = []
        }
      })
    },
    getReqList(teamId, actId) {
      this.$http({
        url: this.$http.adornUrl('/admin/zyz/requirement/getEnableDocking'),
        method: 'get',
        params: this.$http.adornParams({
          'teamId': teamId,
          'actId': actId
        })
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.reqList = data.obj
        } else {
          this.reqList = []
        }
      })
    },
    createRes() {
      let baseInfo = this.$parent.$parent.$refs['baseInfoForm'].baseInfoForm
      this.$confirm(`如需要创建资源，您可先行完善好活动信息，资源创建时将为您带出相关信息！`, '提示',
          {confirmButtonText: '立即创建', cancelButtonText: '稍后创建', type: 'warning', closeOnClickModal: false}
      ).then(() => {
        this.resAddOrUpdateVisible = true
        this.$parent.$parent.getStartEndTime(null)
        baseInfo.startTime = this.$parent.$parent.startTime
        baseInfo.endTime = this.$parent.$parent.endTime
        if (!this.$parent.$parent.startTime || !this.$parent.$parent.endTime) {
          baseInfo.timeRange = []
        } else {
          baseInfo.timeRange = [this.$parent.$parent.startTime, this.$parent.$parent.endTime]
        }
        //设置资源名称为，活动名称+所属领域+资源
        // baseInfo.resName = baseInfo.resName + '-' + baseInfo.domainName + '-资源'
        console.log(baseInfo)
        this.$nextTick(() => {this.$refs.resAddOrUpdate.fromActInit(null, baseInfo)})
      })
    },
    updateRes() {
      this.resAddOrUpdateVisible = true
      this.$nextTick(() => {this.$refs.resAddOrUpdate.fromActInit(this.dockingForm.resId, null)})
    },
    resCreateCallback(name, id) {
      this.$set(this.dockingForm, 'dockingResCreate', true)
      this.$set(this.dockingForm, 'resName', name)
      this.$set(this.dockingForm, 'resId', id)
    },
    clearRes() {
      this.$confirm(`确定清除您创建的这条资源？`, '提示',
          {confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning', closeOnClickModal: false}
      ).then(() => {
        this.dockingForm.dockingResCreate = false
        this.dockingForm.resName = null
        this.dockingForm.resId = null
        this.getResList(this.teamId, null, null)
        this.$message({message: '清除成功', type: 'success', duration: 1000})
      })
    },
    createReq() {
      let baseInfo = this.$parent.$parent.$refs['baseInfoForm'].baseInfoForm
      this.$confirm(`如需要创建需求，您可先行完善好活动信息，需求创建时将为您带出相关信息！`, '提示',
          {confirmButtonText: '立即创建', cancelButtonText: '稍后创建', type: 'warning', closeOnClickModal: false}
      ).then(() => {
        this.reqAddOrUpdateVisible = true
        this.$parent.$parent.getStartEndTime(null)
        baseInfo.startTime = this.$parent.$parent.startTime
        baseInfo.endTime = this.$parent.$parent.endTime
        if (!this.$parent.$parent.startTime || !this.$parent.$parent.endTime) {
          baseInfo.timeRange = []
        } else {
          baseInfo.timeRange = [this.$parent.$parent.startTime, this.$parent.$parent.endTime]
        }
        this.$nextTick(() => {this.$refs.reqAddOrUpdate.fromActInit(null, baseInfo, true)})
      })
    },
    updateReq() {
      this.reqAddOrUpdateVisible = true
      this.$nextTick(() => {this.$refs.reqAddOrUpdate.fromActInit(this.dockingForm.reqId, null)})
    },
    reqCreateCallback(name, id) {
      this.$set(this.dockingForm, 'dockingReqCreate', true)
      this.$set(this.dockingForm, 'reqName', name)
      this.$set(this.dockingForm, 'reqId', id)
    },
    clearReq() {
      this.$confirm(`确定清除您创建的这条需求？`, '提示',
          {confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning', closeOnClickModal: false}
      ).then(() => {
        this.dockingForm.dockingReqCreate = false
        this.dockingForm.reqName = null
        this.dockingForm.reqId = null
        this.getReqList(this.teamId)
        this.$message({message: '清除成功', type: 'success', duration: 1000})
      })
    }
  }
}
</script>

<style scoped>

</style>
