<template>
  <div class="mod-role">
    <el-form :inline="true" ref="dataForm" :model="dataForm" @keyup.enter.native="getDataList()">
      <el-form-item label="关键字" prop="key" style="padding-right: 40px">
        <el-input v-model="dataForm.key" placeholder="活动名称/联系人/联系方式" clearable style="width: 120%"></el-input>
      </el-form-item>
      <el-form-item label="上级组织" prop="publishOrgCodes">
        <el-cascader placeholder="请选择" v-model="dataForm.publishOrgCodes" :options="orgList"
                     :disabled="this.dataForm.id" :show-all-levels="false" clearable
                     :props="{checkStrictly: true, value: 'code',label: 'name'}"
                     @change="(value) => {handleChange(value, 'org')}"></el-cascader>
      </el-form-item>
<!--      <el-form-item label="活动类型" prop="actType">-->
<!--        <el-dict :code="'zyz_activity_type'" v-model="dataForm.actType"></el-dict>-->
<!--      </el-form-item>-->
      <el-form-item label="对接类型" prop="dockingType">
        <el-dict :code="'zyz_activity_docking_type'" v-model="dataForm.dockingType"></el-dict>
      </el-form-item>
      <el-form-item label="所属领域" prop="fieldIds">
        <el-cascader placeholder="请选择" v-model="dataForm.fieldIds" :options="fields"
                     @change="(value) => {handleChange(value, 'field')}" clearable
                     :props="{checkStrictly: true, label: 'typeName', value: 'typeId'}"></el-cascader>
      </el-form-item>
      <el-form-item label="活动时间" prop="timeRange">
        <el-date-picker v-model="dataForm.timeRange" clearable type="datetimerange"
                        value-format="yyyy-MM-dd HH:mm:ss" align="right" start-placeholder="开始时间" end-placeholder="结束时间"
                        :default-time="['00:00:00', '23:59:59']">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="提交时间" prop="timeAppointmentRange">
        <el-date-picker v-model="dataForm.timeAppointmentRange" clearable type="datetimerange"
                        value-format="yyyy-MM-dd HH:mm:ss" align="right" start-placeholder="开始时间" end-placeholder="结束时间"
                        :default-time="['00:00:00', '23:59:59']">
        </el-date-picker>
      </el-form-item>
      <el-form-item v-if="!dataForm.needAudit" label="审核状态" prop="auditStatus">
        <el-select v-model="dataForm.auditStatus" placeholder="请选择" clearable>
          <el-option
              v-for="item in [{label: '待审核', value: 'act_wait_audit'}, {label: '审核通过', value: 'act_audit_success'}, {label: '驳回', value: 'act_reject'}]"
              :key="item.value" :label="item.label" :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item  prop="needAudit">
        <el-checkbox v-model="dataForm.needAudit">需要我审核的</el-checkbox>
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-search" type="warning" @click="getDataList()">查询</el-button>
        <el-button class="el-icon-refresh-left" @click="resetForm()">重置</el-button>
      </el-form-item>
    </el-form>
    <div class="f-s-c" style="padding: 15px 0;display: flex; justify-content: space-between">
      <div style="display: flex; justify-content: flex-start; color: red">*<div style="background-color: #f5d2a5; width: 40px; height: 16px"></div>标识的活动关联了新创建的资源或需求，请谨慎审核！</div>
      <div>
        <el-button icon="el-icon-s-check" :disabled="dataListSelections.length <= 0" v-if="isAuth('resource:list:passBatch')"
                   type="primary" @click="auditHandle(null, null, true, false)">批量通过
        </el-button>
      </div>
    </div>
    <el-table :data="dataList" border v-loading="dataListLoading" @selection-change="selectionChangeHandle"
              style="width: 100%;" :row-class-name="tableRowClassName">
      <el-table-column type="selection" header-align="center" align="center"
                       :selectable="(row, index) => {return row.auditStatus === 'act_wait_audit' && !row.dockingResCreate && !row.dockingReqCreate}" width="40">
      </el-table-column>
      <el-table-column type="index" label="序号" align="center" width="50">
      </el-table-column>
      <el-table-column prop="name" header-align="center" align="center" min-width="200" :show-overflow-tooltip="true"
                       label="活动名称">
        <template slot-scope="scope">
          <a style="cursor: pointer" @click="activityDetailHandler(scope.row.id)">{{ scope.row.name }}</a>
        </template>
      </el-table-column>
<!--      <el-table-column prop="actTypeText" header-align="center" min-width="160" align="center" label="活动类型">-->
<!--      </el-table-column>-->
      <el-table-column prop="recruitTargetsText" header-align="center" align="center" min-width="160" label="招募对象"/>
      <el-table-column prop="dockingTypeText" header-align="center" width="100" align="center" label="对接类型">
      </el-table-column>
      <el-table-column prop="belongField" header-align="center" align="center" min-width="180" label="所属领域">
        <template slot-scope="scope">
          <span>{{
              scope.row.belongFieldNameTop && scope.row.belongFieldNameEnd ? scope.row.belongFieldNameTop + '-' + scope.row.belongFieldNameEnd : (scope.row.belongFieldNameTop || scope.row.belongFieldNameEnd)
            }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="publishOrgName" header-align="center" min-width="150" :show-overflow-tooltip="true"
                       align="center" label="发布组织">
      </el-table-column>
      <el-table-column prop="timeRange" header-align="center" align="center" min-width="270" label="活动时间">
      </el-table-column>
      <el-table-column prop="contactPerson" header-align="center" align="center" label="联系人">
      </el-table-column>
      <el-table-column prop="contactPhone" header-align="center" align="center" min-width="130" label="联系方式">
      </el-table-column>
      <el-table-column prop="submitTime" header-align="center" min-width="180" align="center" label="提交时间">
      </el-table-column>
      <el-table-column prop="timeRange" header-align="center" align="center" min-width="80" label="公开活动">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.webShow" size="small">是</el-tag>
          <el-tag v-else size="small" type="danger">否</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="auditStatus" header-align="center"
                       :fixed="false" align="center" label="审核状态" min-width="130">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.auditStatus === 'act_wait_audit'" type="warning">待审核</el-tag>
          <el-tag v-if="scope.row.auditStatus === 'act_audit_success'" type="success">审核通过</el-tag>
          <el-tag v-if="scope.row.auditStatus === 'act_reject'" type="danger">驳回</el-tag>
          <el-tag v-if="scope.row.auditStatus === 'act_draft'" type="info">草稿</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="auditOrgCodeName" header-align="center" min-width="150" show-overflow-tooltip align="center"
                       label="当前审核组织机构">
      </el-table-column>
      <el-table-column fixed="right" header-align="center" align="center"
                       min-width="100px" label="操作">
        <template slot-scope="scope">
          <el-button v-if="scope.row.canAudit" type="text" size="small"
                     @click="auditHandle(scope.row.id, scope.row.name, true, scope.row.dockingResCreate || scope.row.dockingReqCreate)">通过
          </el-button>
          <el-button v-if="scope.row.canAudit" type="text" size="small"
                     @click="auditHandle(scope.row.id, scope.row.name, false, scope.row.dockingResCreate || scope.row.dockingReqCreate)">驳回
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination @size-change="sizeChangeHandle" @current-change="currentChangeHandle" :current-page="pageIndex"
                   :page-sizes="[10, 20, 50, 100]" :page-size="pageSize" :total="totalPage"
                   layout="total, sizes, prev, pager, next, jumper">
    </el-pagination>
    <!-- 活动详情 -->
    <activity-detail v-if="activityDetailVisible" ref="activityDetail"></activity-detail>
  </div>
</template>

<script>
import listMixin from '@/mixins/list-mixins'
import ActivityDetail from './activity-detail.vue'
import moment from 'moment'

export default {
  mixins: [listMixin],
  data() {
    return {
      mixinOptions: {
        dataUrl: '/admin/zyz/activity/pagesForAudit'
      },
      dataForm: {
        key: null,
        publishOrgCodes: [],
        publishOrgCode: null,
        fieldIds: [],
        fieldId: null,
        actType: null,
        dockingType: null,
        needAudit: true,
        auditStatus: '',
        timeRange: [],
        timeAppointmentRange: [],
        publishStartTime: '',
        publishEndTime: ''
      },
      fields: [],
      orgList: [],
      activityDetailVisible: false
    }
  },
  components: {
    ActivityDetail
  },
  activated() {
    this.getOrg()
    this.getDataList()
    this.getFields()
  },
  methods: {
    tableRowClassName (row, rowIndex) {
      console.log(row)
      if (row.row.canAudit && (row.row.dockingResCreate || row.row.dockingReqCreate)) {
        return 'warning-row';
      } else {
        return '';
      }
    },
    resetForm() {
      this.$refs['dataForm']?.resetFields()
      this.dataForm.fieldIds = []
      this.dataForm.fieldId = ''
      this.dataForm.orgCode = ''
      this.dataForm.startTime = null
      this.dataForm.endTime = null
      this.dataForm.submitStartTime = null
      this.dataForm.submitEndTime = null
      this.dataForm.publishOrgCode = null
      this.dataForm.timeRange = []
      this.getDataList()
    },
    queryBeforeHandle() {
      // 查询前操作
      this.dataForm.startTime = this.dataForm.timeRange && this.dataForm.timeRange[0]
      this.dataForm.endTime = this.dataForm.timeRange && this.dataForm.timeRange[1]
      this.dataForm.publishOrgCode = this.dataForm.publishOrgCodes.join(',')
      this.dataForm.submitStartTime = this.dataForm.timeAppointmentRange && this.dataForm.timeAppointmentRange[0]
      this.dataForm.submitEndTime = this.dataForm.timeAppointmentRange && this.dataForm.timeAppointmentRange[1]
    },
    querySuccessHandle(data) {
      // 查询成功操作
      this.dataList = data.records
      this.totalPage = data.total
      data.records.forEach((it) => {
        it.canAudit = it.auditStatus === 'act_wait_audit' && it.auditOrgCode === this.$store.state.user
            .orgCode
        it.timeRange = moment(it.startTime).format('YYYY-MM-DD HH:mm') + '-' + moment(it.endTime).format('YYYY-MM-DD HH:mm')
      })
    },
    //获取所属区域
    getOrg() {
      this.$http({
        url: this.$http.adornUrl(`/admin/org/getOrgTreeOfAll`),
        method: 'get',
        params: this.$http.adornParams()
      }).then(({
                 data
               }) => {
        if (data && data.code === 0) {
          this.orgList = this.getTreeData(data.obj)
        } else {
          this.orgList = []
        }
      })
    },
    getFields() {
      this.$http({
        url: this.$http.adornUrl('/admin/zyz/platform/belongFieldDict/cascade'),
        method: 'get'
      }).then(({
                 data
               }) => {
        if (data && data.code === 0) {
          this.fields = this.getTreeData(data.obj)
        } else {
          this.fields = []
        }
      })
    },
    // *处理点位分类最后children数组为空的状态
    getTreeData: function (data) {
      var that = this
      // 循环遍历json数据
      data.forEach(function (e) {
        if (!e.children || e.children.length < 1) {
          e.children = undefined
        } else {
          // children若不为空数组，则继续 递归调用 本方法
          that.getTreeData(e.children)
        }
      })
      return data
    },
    handleChange(value, type) {
      if (type === 'field') {
        this.dataForm.fieldId = value[value.length - 1]
      }
      if (type === 'org') {
        this.dataForm.publishOrgCode = value && value.length > 0 ? value[value.length - 1] : null
      }
    },
    activityDetailHandler(activityId) {
      this.activityDetailVisible = true
      this.$nextTick(() => {
        this.$refs.activityDetail.init(activityId)
      })
    },
    auditHandle(id, name, status, dockingCreate) {
      if (!id && this.dataListSelections.length === 0) {
        this.$message.warning('未选中需要审核的活动记录！')
        return
      }
      if (!id) {
        let ids = this.dataListSelections.map(item => {
          return item.id
        })
        id = ids.join(',')
      }
      this.$confirm(`${dockingCreate ? '该活动存在新建需求/资源信息，' : ''}确定${status ? '审核通过' : '驳回'}` + (name ? '"' + name + '"' : '') + '活动？', '系统提示', {
        customClass: 'audit_pass_msg_box',
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        showInput: !status,
        inputPlaceholder: '请输入驳回意见……',
        inputType: 'textarea',
        closeOnClickModal: false
      }).then((val) => {
        this.$http({
          url: this.$http.adornUrl('/admin/zyz/activity/audit'),
          method: 'get',
          params: this.$http.adornParams({
            'ids': id,
            'status': status,
            'remark': val.value
          })
        }).then(({
                   data
                 }) => {
          if (data && data.code === 0) {
            this.$message.success(status ? '审核通过成功！' : '驳回成功！')
            this.query()
          } else {
            this.$message.error(status ? '审核通过失败！' : '驳回失败！' + data.msg)
          }
        })
      })
      setTimeout(() => {
        var content = document.getElementsByClassName('audit_pass_msg_box')[0].childNodes[1]
        content.style.paddingLeft = '60px'
        content.style.paddingRight = '60px'
        var submitBtn = document.getElementsByClassName('audit_pass_msg_box')[0].childNodes[2]
            .childNodes[1]
        submitBtn.style.backgroundColor = '#F56C6C'
        submitBtn.style.borderColor = '#F56C6C'
      }, 50)
    }
  }
}
</script>

<style lang="scss" scoped>
.el-table::v-deep .warning-row {
  background: #f5d2a5 !important;
}
</style>
