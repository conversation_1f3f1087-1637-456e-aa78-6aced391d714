<template>
    <el-dialog title="阵地计划详情" :close-on-click-modal="false" :visible.sync="visible"
               width="90%">
        <el-form :model="dataForm" ref="dataForm" label-width="150px">
            <el-row :gutter="20">
                <el-col :span="12">
                    <el-form-item label="计划类型" prop="scheduleType">
                        <el-radio-group v-model="dataForm.scheduleType" disabled>
                            <el-radio :label="'schedule_type_month'">月计划</el-radio>
                            <el-radio :label="'schedule_type_quarter'">季度计划</el-radio>
                            <el-radio :label="'schedule_type_year'">年计划</el-radio>
                        </el-radio-group>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="计划数值" prop="scheduleTypeData">
                        <el-date-picker
                                v-if="dataForm.scheduleType === 'schedule_type_month'"
                                v-model="dataForm.scheduleTypeData"
                                type="month"
                                format="yyyy-MM"
                                value-format="yyyyMM"
                                :editable="false"
                                placeholder="选择月"
                                disabled
                        ></el-date-picker>
                        <span v-if="dataForm.scheduleType === 'schedule_type_quarter'">
      <!--季度时间选择控件 -->
                        <el-quarter-picker v-model="dataForm.scheduleTypeData" placeholder="选择季度" disabled/></span>
                        <el-date-picker
                                v-if="dataForm.scheduleType === 'schedule_type_year'"
                                v-model="dataForm.scheduleTypeData"
                                type="year"
                                format="yyyy"
                                value-format="yyyy"
                                :editable="false"
                                placeholder="选择年"
                                disabled
                        ></el-date-picker>
                    </el-form-item>
                </el-col>
            </el-row>
                <el-row :gutter="20">
                <el-col :span="12">
                    <el-form-item label="计划主题" prop="title">
                        <el-input v-model="dataForm.title" placeholder="计划主题" disabled></el-input>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-table id="detail_table" :key="tableKey" ref="detailTable" :data="dataForm.detailList" border
                      style="width: 100%;">
                <el-table-column
                        prop="name"
                        width="300"
                        header-align="center"
                        align="center"
                        label="活动预告">
                </el-table-column>
                <el-table-column prop="startTime" width="450" header-align="center" align="center" label="活动起止时间"
                                 disabled>
                    <template slot-scope="scope">
                        <span>{{  moment(scope.row.startTime).format('YYYY-MM-DD HH:mm') + '-' + moment(scope.row.endTime).format('HH:mm') }}</span>
                    </template>
                </el-table-column>
                <el-table-column prop="activityTypeText" width="200" header-align="center" align="center"
                                 label="活动类别">
                </el-table-column>
                <el-table-column prop="subjects" width="200" header-align="center" align="center" label="实施主体">
                </el-table-column>
                <el-table-column prop="targetsText" width="200" header-align="center" align="center" label="服务对象">
                </el-table-column>
                <el-table-column prop="linkMan" width="200" header-align="center" align="center" label="联系人">
                </el-table-column>
                <el-table-column prop="linkPhone" width="200" header-align="center" align="center" label="联系电话">
                </el-table-column>
                <el-table-column prop="address" width="300" header-align="center" align="center" label="活动地点">
                </el-table-column>
            </el-table>
        </el-form>
        <el-tabs v-model="tabName" type="card" ref="tabs">
            <el-tab-pane label="日志记录" name="log">
                <el-table key="logKey" :data="logList" border style="width: 100%;">
                    <el-table-column type="index" align="center" label="序号" width="50">
                    </el-table-column>
                    <el-table-column align="center" label="日志记录">
                        <el-table-column prop="operateTypeText" header-align="center" width="180" :show-overflow-tooltip="true"
                                         align="center" label="操作类型">
                        </el-table-column>
                        <el-table-column prop="operatorName" header-align="center" align="center" width="160" label="操作人">
                        </el-table-column>
                        <el-table-column prop="operatorOrgName" header-align="center" align="center" width="300" label="所在组织">
                        </el-table-column>
                        <el-table-column prop="operateTime" header-align="center" align="center" width="180" label="操作时间">
                        </el-table-column>
                        <el-table-column prop="remark" header-align="center" align="center" label="操作结果">
                        </el-table-column>
                    </el-table-column>
                </el-table>
            </el-tab-pane>
        </el-tabs>
        <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
    </span>
    </el-dialog>
</template>

<script>

import ElQuarterPicker from './schedule-components/ElQuarterPicker.vue'
import moment from 'moment/moment'
import {uuid} from '@/utils'

export default {
    data() {

        return {
            moment: moment,
            tableKey: uuid(),
            logList:[],
            tabName: 'log',
            pickerOptions: {
                disabledDate(time) {
                    return moment(time).isBefore(moment().subtract(1, 'd')) || moment(time).isAfter(moment().add(30, 'd'))
                }
            },
            submitLoading: false,
            visible: false,
            posed: false,
            teamList: [],
            dataForm: {
                id: null,
                version: null,
                scheduleType: 'schedule_type_month',
                scheduleTypeData: null,
                title: null,
                auditStatus: null,
                publishOrgCode: null,
                publishOrgName: null,
                pbId: null,
                teamId: null,
                teamPublish: null,
                auditOrgCode: null,
                sync: null,
                syncTime: null,
                syncRemark: null,
                schId: null,
                autoStatus: null,
                detailList: []
            },
        }
    },
    components: {
        ElQuarterPicker
    },
    methods: {
        init(id) {
            this.dataForm.id = id || null
            this.dataForm.version = 0
            this.visible = true
            this.posed = false
            this.tableKey = uuid()
            this.dataForm.detailList = []
            this.teamList = []
            this.getLogs()
            this.$nextTick(() => {
                this.$refs['dataForm']?.resetFields()
                if (this.dataForm.id) {
                    this.$http({
                        url: this.$http.adornUrl(`/admin/zyz/activity/schedule/getDetail`),
                        method: 'get',
                        params: this.$http.adornParams({
                            scheduleId: this.dataForm.id
                        })
                    }).then(({data}) => {
                        if (data && data.code === 0) {
                            this.dataForm = data.obj
                            data.obj.detailList.forEach(it => {
                                this.$set(it, 'showStartTime', moment(new Date(it.startTime)).format('YYYY-MM-DD HH:mm'))
                                this.$set(it, 'showEndTime', moment(new Date(it.endTime)).format('HH:mm'))
                                this.$set(it, 'startEndTime', [it.startTime, it.endTime])
                            })
                        }
                    })
                }
            })
        },
        //获取日志
        getLogs() {
            this.$http({
                url: this.$http.adornUrl(`/admin/zyz/activity/schedule/log/getLogsByScheduleId`),
                method: 'get',
                params: this.$http.adornParams({scheduleId: this.dataForm.id})
            }).then(({data}) => {
                if (data && data.code === 0) {
                    this.logList = data.obj
                } else {
                    this.logList = []
                }
            })
        },

    }
}
</script>

<style scoped lang="scss">
#detail_table ::v-deep .el-form-item {
  margin-bottom: 0px;
  margin-left: -150px;
}

#detail_table::v-deep .el-form-item__error {
  position: static;
  text-align: center;
}
</style>

