<template>
  <div>
    <el-dialog :title="'活动详情'" :close-on-click-modal="false" width="80%" :visible.sync="visible">
      <el-form :model="dataForm" :rules="dataRule" ref="dataForm"
               label-width="140px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="活动名称" prop="name">
              <el-input disabled v-model="dataForm.name" placeholder="活动名称"/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="所属领域" prop="fieldIds">
              <el-cascader style="width: 100%" disabled placeholder="请选择" v-model="dataForm.fieldIds" :options="fields" clearable :props="{ label: 'typeName', value: 'typeId' }"/>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item v-if="!this.isTeam" label="本组织创建" prop="orgSelf">
              <el-radio-group v-model="dataForm.orgSelf" disabled>
                <el-radio :label="true">是</el-radio>
                <el-radio :label="false">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item v-if="(!this.isTeam) && (!dataForm.orgSelf)" label="活动举办主体" prop="publishOrgName">
              <el-input style="width: 100%" disabled v-model="dataForm.publishOrgName" placeholder="活动举办主体"/>
<!--              <el-select style="width: 100%" disabled v-model="dataForm.teamId" placeholder="请选择">-->
<!--                <el-option v-for="item in teamList" :key="item.id" :label="item.name" :value="item.id"/>-->
<!--              </el-select>-->
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="招募对象" prop="recruitTargetArr">
              <el-checkbox-group disabled v-model="dataForm.recruitTargetArr">
                <el-checkbox label="rt_volunteer">志愿招募</el-checkbox>
                <el-checkbox label="rt_masses">群众参与</el-checkbox>
              </el-checkbox-group>
            </el-form-item>
          </el-col>
          <el-col :span="12" v-if="dataForm.recruitTargetArr && dataForm.recruitTargetArr.length > 1">
            <el-form-item label="招募人数区别控制" prop="recruitNumDistinguish">
              <el-radio-group disabled v-model="dataForm.recruitNumDistinguish">
                <el-radio :label="true">是</el-radio>
                <el-radio :label="false">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="21">
          <el-col :span="6">
            <el-form-item label="报名是否需要审核" prop="applyCheck">
              <el-radio-group disabled v-model="dataForm.applyCheck">
                <el-radio :label="true">是</el-radio>
                <el-radio :label="false">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="公开招募" prop="open">
              <el-radio-group disabled v-model="dataForm.open">
                <el-radio :label="true">是</el-radio>
                <el-radio :label="false">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="惠及人数" prop="benefitPeopleNum">
              <el-input-number style="width:100%" disabled v-model="dataForm.benefitPeopleNum" controls-position="right" :min="0"/>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item label="是否前端展示" prop="webShow">
              <el-radio-group disabled v-model="dataForm.webShow">
                <el-radio :label="true">是</el-radio>
                <el-radio :label="false">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item prop="auditShow">
              <el-checkbox disabled v-model="dataForm.auditShow">审核通过前端立即展示</el-checkbox>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item prop="top">
              <el-checkbox disabled v-model="dataForm.top">前端推荐</el-checkbox>
            </el-form-item>
          </el-col>
          <el-col :span="6" v-if="!dataForm.checkLocation">
            <el-form-item prop="checkLocation">
              <el-checkbox disabled v-model="dataForm.checkLocation">签到定位</el-checkbox>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="活动地址" prop="address">
              <el-input style="width:100%" disabled v-model="dataForm.address" placeholder="活动地址"/>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="经纬度" prop="longitudeAndLatitude">
              <el-input style="width:100%" disabled v-model="dataForm.longitudeAndLatitude" controls-position="right"/>
            </el-form-item>
          </el-col>
            <el-col :span="6">
                <el-form-item :label="'活动所在社区'" prop="activityPlaceRegionCode">
                    <el-cascader style="width: 100%" placeholder="活动所在社区" v-model="dataForm.activityPlaceRegionCode" :options="regionList"
                                 :show-all-levels="false" clearable
                                 :props="{checkStrictly: true,value: 'regionCode',label: 'regionName'}" disabled></el-cascader>
                </el-form-item>
            </el-col>
        </el-row>

        <el-row :gutter="20" v-if="dataForm.checkLocation">
          <el-col :span="12">
            <el-form-item prop="checkLocation">
              <el-checkbox disabled v-model="dataForm.checkLocation">签到定位</el-checkbox>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="签到半径(m)" prop="locationDistance">
              <el-input-number style="width:100%" disabled v-model="dataForm.locationDistance" controls-position="right" :min="0"/>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="联系人" prop="contactPerson">
              <el-input style="width: 100%" disabled v-model="dataForm.contactPerson" placeholder="联系人"/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="联系电话" prop="contactPhone">
              <el-input style="width: 100%" disabled v-model="dataForm.contactPhone" placeholder="联系电话"/>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="活动简介" prop="actSynopsis">
              <el-input style="width: 100%" disabled type="textarea" v-model="dataForm.actSynopsis" placeholder="活动简介" rows="5"/>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="报名条件" prop="applicationRequirements">
              <el-input style="width: 100%;" disabled type="textarea" v-model="dataForm.applicationRequirements" placeholder="报名条件" rows="5"/>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="图片" prop="picture">
<!--              <img v-if="dataForm.picture" :src="$http.adornAttachmentUrl(dataForm.picture)" class="avatar">-->
              <el-image v-if="dataForm.picture" :src="$http.adornAttachmentUrl(dataForm.picture)" :preview-src-list="[$http.adornAttachmentUrl(dataForm.picture)]" class="avatar"/>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item v-if="dataForm.dockingReqCreate && dataForm.dockingReqCreate === true && (!dataForm.auditStatus || dataForm.auditStatus !== 'act_audit_success')" label="新创建的需求" prop="reqName">
              <a style="cursor: pointer" @click="dockingReqCreateDetail(dataForm.reqId)">{{ dataForm.reqName }}</a>
              <!--            <el-input style="width: 100%" v-model="dataForm.reqName" disabled></el-input>-->
            </el-form-item>
            <el-form-item v-else label="选择对接的需求" prop="reqName">
              <el-input style="width: 100%" disabled v-model="dataForm.reqName" placeholder="对接需求"/>
<!--              <el-select style="width: 100%" v-model="dataForm.reqId" placeholder="请选择" disabled>-->
<!--                <el-option v-for="item in reqList" :key="item.id" :label="item.name" :value="item.id"/>-->
<!--              </el-select>-->
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
                v-if="dataForm.dockingResCreate && dataForm.dockingResCreate === true && (!dataForm.auditStatus || dataForm.auditStatus !== 'act_audit_success')" label="新创建的资源" prop="resName">
              <a style="cursor: pointer" @click="dockingResCreateDetail(dataForm.resId)">{{ dataForm.resName }}</a>
              <!--            <el-input style="width: 100%" v-model="dataForm.resName" disabled></el-input>-->
            </el-form-item>
            <el-form-item v-else label="选择对接的资源" prop="resName">
              <el-input style="width: 100%" disabled v-model="dataForm.resName" placeholder="对接资源"/>
<!--              <el-select style="width: 100%" v-model="dataForm.resId" placeholder="请选择" disabled>-->
<!--                <el-option v-for="item in resList" :key="item.id" :label="item.name" :value="item.id"/>-->
<!--              </el-select>-->
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="阵地计划选择" prop="scheduleDetailId">
          <el-cascader
              style="width: 100%"
              disabled
              v-model="dataForm.scheduleDetailId"
              :options="scheduleOptions"
              :props="{ checkStrictly: false, emitPath: false }"
              placeholder="阵地计划">
          </el-cascader>
        </el-form-item>
        <el-row :gutter="20" v-if="dataForm.recruitTargetArr && dataForm.recruitTargetArr.includes('rt_masses')">
          <el-col :span="12">
            <el-form-item label="群众报名模板" prop="applyPublicFormId">
              <div style="display: flex;">
                <el-select style="width: 100%" disabled v-model="dataForm.applyPublicFormId" placeholder="群众报名模板">
                  <el-option v-for="item in formList" :key="item.id" :label="item.formName" :value="item.id"/>
                </el-select>
                <el-button
                    type="primary"
                    style="margin-left: 10px;"
                    @click="previewFormTemplate"
                    :disabled="!dataForm.applyPublicFormId">
                  模板预览
                </el-button>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <el-tabs v-model="activeName" type="card" ref="tabs">
        <el-tab-pane label="招募信息" name="time">
          <el-table
              :data="dataForm.activityTimes"
              :key="recruitInfoKey"
              border
              style="width: 100%;">
            <el-table-column align="center" label="招募信息">
              <el-table-column
                  prop="startTime"
                  header-align="center"
                  align="center"
                  width="470px"
                  label="起止时间">
                <template slot-scope="scope">
                  <span>{{ scope.row.showStartTime }} -- {{scope.row.showEndTime }}</span>
                </template>
              </el-table-column>
              <el-table-column
                  prop="signTime"
                  header-align="center"
                  align="center"
                  width="250px"
                  label="签到时间">
              </el-table-column>
              <el-table-column
                  prop="applyEndTime"
                  header-align="center"
                  align="center"
                  width="250px"
                  label="报名结束时间">
              </el-table-column>
              <el-table-column
                  v-if="!dataForm.recruitNumDistinguish"
                  prop="recruitmentNum"
                  header-align="center"
                  align="center"
                  width="150px"
                  label="招募人数">
              </el-table-column>
              <el-table-column
                  v-if="dataForm.recruitNumDistinguish"
                  header-align="center"
                  align="center"
                  label="招募人数">
                <el-table-column
                    prop="volunteerRecruitNum"
                    header-align="center"
                    align="center"
                    width="100px"
                    label="志愿招募">
                </el-table-column>
                <el-table-column
                    prop="massesRecruitNum"
                    header-align="center"
                    align="center"
                    width="100px"
                    label="群众参与">
                </el-table-column>
              </el-table-column>
              <el-table-column
                  prop="webShow"
                  header-align="center"
                  align="center"
                  label="是否上下架">
                <template slot-scope="scope">
                  <el-tag v-if="scope.row.webShow == false" size="small" type="danger">否</el-tag>
                  <el-tag v-else size="small">是</el-tag>
                </template>
              </el-table-column>
            </el-table-column>
          </el-table>
        </el-tab-pane>
        <el-tab-pane label="日志记录" name="log">
          <el-table :key="logKey" :data="logList" border style="width: 100%;">
            <el-table-column type="index" align="center" label="序号" width="50">
            </el-table-column>
            <el-table-column align="center" label="日志记录">
              <el-table-column prop="operateTypeText" header-align="center" width="180" :show-overflow-tooltip="true"
                               align="center" label="操作类型">
              </el-table-column>
              <el-table-column prop="operatorName" header-align="center" align="center" width="160" label="操作人">
              </el-table-column>
              <el-table-column prop="operatorOrgName" header-align="center" align="center" width="200" label="所在组织">
              </el-table-column>
              <el-table-column prop="operateTime" header-align="center" align="center" width="180" label="操作时间">
              </el-table-column>
              <el-table-column prop="remark" header-align="center" align="center" label="操作结果">
              </el-table-column>
            </el-table-column>
          </el-table>
        </el-tab-pane>
        <el-tab-pane label="报名记录" name="apply">
          <el-table :key="applyInfoKey" :data="applyList" border style="width: 100%;">
            <el-table-column type="index" align="center" label="序号" width="50">
            </el-table-column>
            <el-table-column align="center" label="报名记录">
              <el-table-column prop="volunteerName" header-align="center" width="180" :show-overflow-tooltip="true"
                               align="center" label="姓名">
              </el-table-column>
              <el-table-column prop="volunteerPhone" header-align="center" align="center" width="160" label="手机号">
              </el-table-column>
              <el-table-column prop="applyTime" header-align="center" align="center" width="160" label="报名时间">
              </el-table-column>
              <el-table-column prop="signTime" header-align="center" align="center" width="160" label="签到时间">
              </el-table-column>
              <el-table-column prop="timeRange" header-align="center" align="center" width="190" label="活动时段">
                <template slot-scope="scope">
                  <span>{{ scope.row.showStartTime + '--' + scope.row.showEndTime }}</span>
                </template>
              </el-table-column>
              <el-table-column prop="serviceLong" header-align="center" align="center" label="服务时长（H）">
              </el-table-column>
              <el-table-column prop="auditStatusText" header-align="center" align="center" width="160" label="审核状态">
              </el-table-column>
            </el-table-column>
          </el-table>
        </el-tab-pane>
      </el-tabs>
      <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
    </span>
    </el-dialog>
    <!-- 弹窗, 需求详情 -->
    <req-detail v-if="reqDetailVisible" ref="reqDetail"></req-detail>
    <!-- 弹窗, 资源详情 -->
    <res-detail v-if="resDetailVisible" ref="resDetail"></res-detail>
    <!-- 弹窗, 表单模板预览 -->
    <form-template-preview :visible.sync="formPreviewVisible" :form-id="dataForm.applyPublicFormId" :snapshot-id="dataForm.applyPublicFormSnapshotId" />
  </div>
</template>
<script>
import moment from 'moment'
import Vue from 'vue'
import _ from 'lodash'
import Teditor from '@/components/tinymce'
import ResDetail from '../resource/resource-detail'
import ReqDetail from '../requirement/requirement-detail'
import {treeDataTranslate, uuid} from "@/utils";
import FormTemplatePreview from "@/components/form-template-preview";

export default {
  data() {
    return {
      visible: false,
      isRecordAdd: false,
      activeName: '',
      resOrReqList: [],
      logList: [],
      applyList: [],
      options: [],
      resList: [],
      reqList: [],
      dockingTypesList: [],
      serverCode: 'LocalServer',
      myHeaders: {Authorization: Vue.cookie.get('Authorization')},
      uploadData: {serverCode: 'LocalServer', media: false},
      fields: [],
      isTeam: this.$store.state.user.managerCapacity === 'TEAM_ADMIN',
      teamList: [],
      regionList: [],
      scheduleOptions: [],
      formList: [],
      pickerOptionsRecord: {
        disabledDate(time) {
          return moment(time).isBefore(moment().subtract(4, 'd'))
        }
      },
      reqDetailVisible: false,
      resDetailVisible: false,
      recruitInfoKey: null,
      logKey: null,
      applyInfoKey: null,
      formPreviewVisible: false,
      initForm: {
        id: null,
        version: null,
        name: '',
        address: '',
        longitude: '',
        latitude: '',
        recruitmentNum: '',
        applyNum: '',
        contactPerson: '',
        contactPhone: '',
        benefitPeopleNum: '',
        top: '',
        actType: '',
        recruitTargetArr: [],
        recruitTargets: null,
        recruitNumDistinguish: false,
        belongFieldTop: '',
        belongFieldNameTop: '',
        belongFieldEnd: '',
        belongFieldNameEnd: '',
        startTime: '',
        endTime: '',
        picture: '',
        auditStatus: '',
        auditOrgCode: '',
        signTime: '',
        applyCheck: '',
        actContents: '',
        applyStartTime: '',
        applyEndTime: '',
        applicationRequirements: '',
        publishOrgCode: '',
        publishOrgName: '',
        submitTime: '',
        teamId: '',
        teamPublish: '',
        webShow: '',
        auditShow: '',
        checkLocation: '',
        locationDistance: '',
        addRecord: '',
        actSynopsis: '',
        dockingType: '',
        dockingTypes: [],
        activityTimes: [],
        resId: '',
        reqId: '',
        shareDesc: '',
        qrCodeUrl: '',
        isSync: '',
        syncTime: '',
        syncRemark: '',
        syncId: '',
        balanceStatus: '',
        balanceTime: '',
        recruitIsSync: '',
        recruitSyncTime: '',
        recruitSyncRemark: '',
        recruitSyncId: '',
        timeRange: [],
        fieldIds: [],
        fieldId: null,
        orgSelf: true,
        longitudeAndLatitude: '',
        dockingResCreate: false,
        resName: null,
        dockingReqCreate: false,
        reqName: null,
        scheduleDetailId: null,
        applyPublicFormId: null,
        applyPublicFormSnapshotId: null
      },
      dataForm: {},
      dataRule: {
      },
      nameError: null,
      addressError: null,
      longitudeError: null,
      latitudeError: null,
      recruitmentNumError: null,
      applyNumError: null,
      contactPersonError: null,
      contactPhoneError: null,
      benefitPeopleNumError: null,
      topError: null,
      actTypeError: null,
      belongFieldTopError: null,
      belongFieldNameTopError: null,
      belongFieldEndError: null,
      belongFieldNameEndError: null,
      startTimeError: null,
      endTimeError: null,
      pictureError: null,
      auditStatusError: null,
      auditOrgCodeError: null,
      signTimeError: null,
      applyCheckError: null,
      actContentsError: null,
      applyStartTimeError: null,
      applyEndTimeError: null,
      applicationRequirementsError: null,
      publishOrgCodeError: null,
      publishOrgNameError: null,
      submitTimeError: null,
      teamIdError: null,
      teamPublishError: null,
      webShowError: null,
      auditShowError: null,
      checkLocationError: null,
      locationDistanceError: null,
      addRecordError: null,
      actSynopsisError: null,
      dockingTypesError: null,
      resIdError: null,
      reqIdError: null,
      shareDescError: null,
      qrCodeUrlError: null,
      isSyncError: null,
      syncTimeError: null,
      syncRemarkError: null,
      syncIdError: null,
      balanceStatusError: null,
      balanceTimeError: null,
      recruitIsSyncError: null,
      recruitSyncTimeError: null,
      recruitSyncRemarkError: null,
      recruitSyncIdError: null
    }
  },
  components: {
    Teditor,
    moment,
    ResDetail,
    ReqDetail,
    FormTemplatePreview
  },
  methods: {
    init(id) {
      this.dataForm = _.cloneDeep(this.initForm)
      this.dataForm.id = id || null
      this.activeName = 'time'
      this.getFields()
      this.getTeams()
      this.getCode()
      this.getLogs()
      this.getApplyLogs()
      this.initRegionTree()
      this.getScheduleData()
      this.getFormList()
      this.recruitInfoKey = uuid()
      this.logKey = uuid()
      this.applyInfoKey = uuid()
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm']?.resetFields()
        if (this.dataForm.id) {
          this.$http({
            url: this.$http.adornUrl(`/admin/zyz/activity/getDetail`),
            method: 'get',
            params: this.$http.adornParams({id: this.dataForm.id})
          }).then(({data}) => {
            if (data && data.code === 0) {
              this.dataForm = data.obj
              this.$set(this.dataForm, 'dockingTypes', data.obj.dockingType ? data.obj.dockingType.split(',') : 'zyz_activity_docking_type_no_docking')
              this.dataForm.fieldIds = [this.dataForm.belongFieldTop, this.dataForm.belongFieldEnd]
              this.dataForm.recruitTargetArr = this.dataForm.recruitTargets.split(',')
              this.dataForm.longitudeAndLatitude = data.obj.longitude + ',' + data.obj.latitude
              this.$set(this.dataForm, 'timeRange', [data.obj.startTime, data.obj.endTime])
              this.dataForm.activityTimes.forEach(it => {
              this.$set(it, 'showStartTime', moment(new Date(it.startTime)).format('YYYY-MM-DD HH:mm'))
              this.$set(it, 'showEndTime', moment(new Date(it.endTime)).format('HH:mm'))
              })
            }
          })
        }
      })
    },
    getResList(teamId) {
      if (this.dataForm.dockingResCreate && this.dataForm.dockingResCreate === true && (!this.dataForm.auditStatus || this.dataForm.auditStatus !== 'act_audit_success')) {
        return
      }
      this.$http({
        url: this.$http.adornUrl('/admin/zyz/resource/getEnableDocking'),
        method: 'get',
        params: this.$http.adornParams({
          'teamId': teamId
        })
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.resList = this.getTreeData(data.obj)
        } else {
          this.resList = []
        }
      })
    },
    getReqList(actId, teamId) {
      if (this.dataForm.dockingReqCreate && this.dataForm.dockingReqCreate === true && (!this.dataForm.auditStatus || this.dataForm.auditStatus !== 'act_audit_success')) {
        return
      }
      this.$http({
        url: this.$http.adornUrl('/admin/zyz/requirement/getEnableDocking'),
        method: 'get',
        params: this.$http.adornParams({
          'actId': actId,
          'teamId': teamId
        })
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.reqList = this.getTreeData(data.obj)
        } else {
          this.reqList = []
        }
      })
    },
    //获取组织团队
    getTeams() {
      this.$http({
        url: this.$http.adornUrl(`/admin/zyz/team/getOrgTeamList`),
        method: 'get',
        params: this.$http.adornParams()
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.teamList = this.getTreeData(data.obj)
        } else {
          this.teamList = []
        }
      })
    },
    //获取日志
    getLogs() {
      this.$http({
        url: this.$http.adornUrl(`/admin/zyz/activity/log/getLogsByActivityId`),
        method: 'get',
        params: this.$http.adornParams({activityId: this.dataForm.id})
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.logList = data.obj
        } else {
          this.logList = []
        }
      })
    },
    getApplyLogs() {
      this.$http({
        url: this.$http.adornUrl(`/admin/zyz/activity/apply/getApplyByActivityId`),
        method: 'get',
        params: this.$http.adornParams({activityId: this.dataForm.id})
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.applyList = this.getTreeData(data.obj)
          this.applyList.forEach(it =>{
            this.$set(it, 'showStartTime', moment(new Date(it.serviceStartTime)).format('YYYY-MM-DD HH:mm'))
            this.$set(it, 'showEndTime', moment(new Date(it.serviceEndTime)).format('HH:mm'))
          })
        } else {
          this.applyList = []
        }
      })
    },
    getCode() {
      this.$http({
        url: this.$http.adornUrl(`/admin/dict/parent`),
        method: 'get',
        params: this.$http.adornParams({code: 'zyz_activity_docking_type'})
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.options = data.obj || []
        }
      })
    },
    getFields() {
      this.$http({
        url: this.$http.adornUrl('/admin/zyz/platform/belongFieldDict/cascade'),
        method: 'get'
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.fields = this.getTreeData(data.obj)
        } else {
          this.fields = []
        }
      })
    },
    // *处理点位分类最后children数组为空的状态
    getTreeData: function (data) {
      var that = this
      // 循环遍历json数据
      data.forEach(function (e) {
        if (!e.children || e.children.length < 1) {
          e.children = undefined
        } else {
          // children若不为空数组，则继续 递归调用 本方法
          that.getTreeData(e.children)
        }
      })
      return data
    },
    // 富文本赋值
    changeAnswer(html) {
      this.dataForm.actSynopsis = html
    },
    dockingResCreateDetail(resId) {
      this.resDetailVisible = true
      this.$nextTick(() => {
        this.$refs.resDetail.initFromAct(resId, true)
      })
    },
    dockingReqCreateDetail(reqId) {
      this.reqDetailVisible = true
      this.$nextTick(() => {
        this.$refs.reqDetail.initFromAct(reqId, true)
      })
    },
    initRegionTree() {
        this.$http({
            url: this.$http.adornUrl(`/admin/zyz/sync/region/dict/all/all`),
            method: 'get',
            params: this.$http.adornParams()
        }).then(({data}) => {
            this.regionList = treeDataTranslate(data, 'regionCode', 'parentCode')
        })
    },
    getScheduleData() {
      this.$http({
        url: this.$http.adornUrl('/admin/zyz/activity/schedule/getCascaderData'),
        method: 'get',
        params: this.$http.adornParams()
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.scheduleOptions = data.obj || []
        } else {
          this.scheduleOptions = []
        }
      })
    },
    getFormList() {
      this.$http({
        url: this.$http.adornUrl('/admin/dynamic/form/list'),
        method: 'post',
        data: this.$http.adornData({
          moduleCode: 'zyz_activity_public_apply_form'
        })
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.formList = data.obj || []
        } else {
          this.formList = []
        }
      })
    },
    previewFormTemplate() {
      this.formPreviewVisible = true
    },
  }
}
</script>
