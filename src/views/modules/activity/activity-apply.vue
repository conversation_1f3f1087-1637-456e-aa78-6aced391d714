<template>
  <el-dialog :title="'活动报名管理'" :close-on-click-modal="false" width="85%" :visible.sync="visible" append-to-body>
    <div><span style="font-size: medium">志愿招募</span></div>
    <div v-if="dataForm.timePeriodId">
      <span> 活动时段： {{ timePeriodInfo.startTime }} -- {{ timePeriodInfo.endTime }}<br></span>
      <span> 已招/计划人数： {{ timePeriodInfo.applyNum ? timePeriodInfo.applyNum : 0 }} / {{
          timePeriodInfo.recruitmentNum
        }}<br></span>
    </div>
    <hr>
    <el-form :inline="true" ref="dataForm" :model="dataForm" @keyup.enter.native="queryPage()">
      <el-form-item label="关键字" prop="key" style="padding-right: 40px">
        <el-input v-model="dataForm.key" placeholder="志愿者姓名/联系方式" clearable style="width: 120%"></el-input>
      </el-form-item>
      <el-form-item label="审核状态" prop="auditStatus">
        <el-select v-model="dataForm.auditStatus" placeholder="请选择" clearable>
          <el-option
              v-for="item in [{ label: '待审核', value: 'act_apply_wait_audit' },
               { label: '审核通过', value: 'act_apply_audit_success' },
               { label: '已取消', value: 'act_apply_cancelled' },
               { label: '驳回', value: 'act_apply_reject' }]"
              :key="item.value" :label="item.label" :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="warning" icon="el-icon-search" @click="queryPage()">查询</el-button>
        <el-button @click="resetForm()" icon="el-icon-refresh-left">重置</el-button>
      </el-form-item>
      <el-form-item style="float: right">
        <el-button icon="el-icon-download" type="success" v-loading.fullscreen.lock="fullscreenLoading"
                   @click="exportServiceLong()">全部导出
        </el-button>
      </el-form-item>
    </el-form>
    <el-table :data="dataList" border v-loading="dataListLoading" @selection-change="selectionChangeHandle"
              style="width: 100%;">
      <el-table-column type="selection" header-align="center" align="center"
                       :selectable="(row, index) => { return row.auditStatus === 'act_apply_wait_audit' }" width="40">
      </el-table-column>
      <el-table-column type="index" label="序号" align="center" width="50">
      </el-table-column>
      <el-table-column prop="volunteerName" header-align="center" width="100" align="center" label="志愿者姓名">
      </el-table-column>
      <el-table-column prop="volunteerPhone" header-align="center" align="center" width="180" label="联系电话">
      </el-table-column>
      <el-table-column prop="applyTime" header-align="center" width="200" :show-overflow-tooltip="true" align="center"
                       label="报名时间">
      </el-table-column>
      <el-table-column prop="signTime" header-align="center" width="200" :show-overflow-tooltip="true" align="center"
                       label="签到时间">
      </el-table-column>
      <el-table-column prop="timePeriodRange" header-align="center" align="center" width="260" label="活动时间">
      </el-table-column>
      <el-table-column prop="teamMember" header-align="center" align="center" width="120" label="是否本团队成员">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.teamMember" size="small" type="danger">是</el-tag>
          <el-tag v-else size="small">否</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="auditStatus" header-align="center" :fixed="false" align="center" label="审核状态" width="130">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.auditStatus === 'act_apply_wait_audit'" type="warning">待审核</el-tag>
          <el-tag v-else-if="scope.row.auditStatus === 'act_apply_audit_success'" type="success">审核通过</el-tag>
          <el-tag v-else-if="scope.row.auditStatus === 'act_apply_cancelled'" type="info">已取消</el-tag>
          <el-tag v-else type="danger">驳回</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="exchange" header-align="center" align="center" label="是否签到" width="120">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.signTime && scope.row.signTime !== ''" type="success">已签到</el-tag>
          <el-tag v-else type="danger">未签到</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="exchange" header-align="center" align="center" label="是否结算" width="120">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.exchange && scope.row.exchange === true" type="success">已结算</el-tag>
          <el-tag v-else type="danger">未结算</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="serviceLong" header-align="center" align="center" label="服务时长">
      </el-table-column>
      <el-table-column prop="appraiseFraction" header-align="center" align="center" width="200px" label="评价">
      </el-table-column>
      <el-table-column v-if="isAuth('resource:list:audit')" fixed="right" header-align="center" align="center"
                       width="100px" label="操作">
        <template slot-scope="scope">
          <el-button v-if="scope.row.canAudit" type="text" size="small"
                     @click="auditHandle(scope.row.id, scope.row.name, true)">通过
          </el-button>
          <el-button v-if="scope.row.canAudit" type="text" size="small"
                     @click="auditHandle(scope.row.id, scope.row.name, false)">驳回
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination style="text-align: right; margin-top: 10px" @size-change="sizeChangeHandle" @current-change="currentChangeHandle" :current-page="pageIndex"
                   :page-sizes="[5, 10, 20, 50, 100]" :page-size="pageSize" :total="totalPage"
                   layout="total, sizes, prev, pager, next, jumper">
    </el-pagination>
    <div style="margin-top: 50px;display: flex;justify-content:flex-start"><span style="font-size: medium">群众招募</span></div>
    <hr>
    <el-form :inline="true" :model="dataPublicForm">
      <el-form-item label="关键字" prop="activityName" style="padding-right: 40px">
        <el-input v-model="dataPublicForm.activityName" placeholder="活动名称/姓名/联系方式" clearable style="width: 120%"></el-input>
      </el-form-item>
      <el-form-item>
        <el-button type="warning" icon="el-icon-search" @click="queryPagePublic()">查询</el-button>
        <span style="margin-left: 10px; color: #EA9A37;">
          如使用自定义表单，请通过右侧导出按钮查看更多报名信息
        </span>
      </el-form-item>
      <el-form-item style="float: right">
        <el-button icon="el-icon-download" type="success" v-loading.fullscreen.lock="fullscreenLoading"
                   @click="exportServiceLongPublic()">全部导出
        </el-button>
      </el-form-item>
    </el-form>
    <el-table
        :data="applyPublicList"
        border
        v-loading="dataListLoadingPublic"
        style="width: 100%;">
      <el-table-column type="expand" v-if="showExpand">
        <template slot-scope="scope">
          <div v-if="scope.row.extraInfo && Object.keys(scope.row.extraInfo).length > 0">
            <el-form label-position="left" inline class="demo-table-expand">
              <!-- 循环显示extraInfo中的所有字段 -->
              <template>
                  <el-form-item v-for="(value, key) in scope.row.extraInfo" :key="key" :label="key + ':'">
                    <span>{{ value }}</span>
                  </el-form-item>
              </template>
            </el-form>
          </div>
          <div v-else>
            <span class="demo-table-expand-empty">暂无更多字段</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column
          prop="activityName"
          header-align="center"
          align="center"
          min-width="150px"
          label="活动名称">
      </el-table-column>
      <el-table-column
          prop="timePeriodRange"
          header-align="center"
          align="center"
          min-width="190px"
          label="活动时间段">
      </el-table-column>
      <el-table-column
          prop="name"
          header-align="center"
          align="center"
          label="姓名">
      </el-table-column>
      <el-table-column
          prop="phone"
          header-align="center"
          align="center"
          min-width="140px"
          label="联系方式">
      </el-table-column>
      <el-table-column
          prop="certificateId"
          header-align="center"
          align="center"
          min-width="160px"
          label="身份证号">
      </el-table-column>
        <el-table-column
                prop="sexText"
                header-align="center"
                align="center"
                min-width="60px"
                label="性别">
        </el-table-column>
        <el-table-column
                prop="age"
                header-align="center"
                align="center"
                min-width="60px"
                label="年龄">
        </el-table-column>
      <el-table-column
          prop="party"
          header-align="center"
          align="center"
          label="是否党员">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.party == false" size="small" type="danger">否</el-tag>
          <el-tag v-else size="small">是</el-tag>
        </template>
      </el-table-column>
        <el-table-column
                prop="address"
                header-align="center"
                align="center"
                min-width="160px"
                :show-overflow-tooltip="true"
                label="住址">
        </el-table-column>
      <el-table-column
          prop="status"
          header-align="center"
          align="center"
          label="报名状态">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.status == false" size="small" type="danger">已取消报名</el-tag>
          <el-tag v-else size="small">已报名</el-tag>
        </template>
      </el-table-column>
      <el-table-column
          fixed="right"
          header-align="center"
          align="center"
          width="150"
          label="操作">
        <template v-if="scope.row.status" slot-scope="scope">
          <el-button type="text" size="small" @click="cancelApplyHandle(scope.row.id,scope.row.timePeriodId)">取消报名</el-button>
        </template>
      </el-table-column>
    </el-table>
      <el-pagination style="text-align: right; margin-top: 10px" @size-change="sizeChangePublicHandle" @current-change="currentChangePublicHandle" :current-page="pageIndexPublic"
                     :page-sizes="[5, 10, 20, 50, 100]" :page-size="pageSizePublic" :total="totalPagePublic"
                     layout="total, sizes, prev, pager, next, jumper">
      </el-pagination>

  </el-dialog>
</template>
<script>
import moment from 'moment'

export default {
  data() {
    return {
      showExpand: false,
      visible: false,
      fullscreenLoading: false,
      name: '',
      startTime: '',
      endTime: '',
      activityStatusText: '',
      applyNum: '',
      recruitmentNum: '',
      dataForm: {
        key: '',
        auditStatus: null,
        activityId: null,
        timePeriodId: null,
        extraInfo: {}
      },
      dataPublicForm: {
        activityId: null,
        timePeriodId: null,
        activityName: ''
      },
      timePeriodInfo: {
        startTime: '',
        endTime: '',
        recruitmentNum: 0,
        applyNum: 0
      },
      dataList: [],
      applyPublicList: [],
      orgList: [],
      pageIndex: 1,
      pageIndexPublic: 1,
      pageSize: 10,
      pageSizePublic: 10,
      totalPage: 0,
      totalPagePublic: 0,
      dataListLoading: false,
      dataListLoadingPublic: false,
      dataListSelections: []
    }
  },
  methods: {
    init(activityId, timePeriodId, applyPublicFormSnapshotId) {
      this.dataForm.activityId = activityId || null
      this.dataForm.auditStatus = null
      this.dataForm.key = null
      this.dataForm.timePeriodId = timePeriodId
      this.dataPublicForm.activityId = activityId || null
      this.dataPublicForm.timePeriodId = timePeriodId
      if (timePeriodId) {
        this.getPeriodInfo()
      }
      this.showExpand = !!applyPublicFormSnapshotId
      this.visible = true
      this.queryPage()
      this.queryPagePublic()
    },
    getPeriodInfo() {
      this.$http({
        url: this.$http.adornUrl(`/admin/zyz/activity-time-period/`),
        method: 'get',
        params: this.$http.adornParams({id: this.dataForm.timePeriodId})
      }).then(({data}) => {
        this.timePeriodInfo = data.obj
      })
    },
    queryPage() {
      this.pageIndex = 1
      this.getDataList()
    },

    queryPagePublic() {
      this.pageIndexPublic = 1
      this.getApplyPublicList()
    },
    // 获取数据列表
    getDataList() {
      this.dataListLoading = true
      this.$http({
        url: this.$http.adornUrl('/admin/zyz/activity/apply/pagesForAuditAll'),
        method: 'post',
        data: this.$http.adornData({
          'currentPage': this.pageIndex,
          'pageSize': this.pageSize,
          'key': this.dataForm.key,
          'activityId': this.dataForm.activityId,
          'timePeriodId': this.dataForm.timePeriodId,
          'auditStatus': this.dataForm.auditStatus,
          'searchCount': true
        })
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.dataList = data.obj.records
          this.dataList.forEach((it) => {
            it.timePeriodRange = it.timePeriodStartTime && it.timePeriodEndTime ?
                moment(it.timePeriodStartTime).format('YYYY-MM-DD HH:mm') + '-' + moment(it.timePeriodEndTime).format('HH:mm') : ''
            it.canAudit = it.auditStatus === 'act_apply_wait_audit'
          })
          this.totalPage = data.obj.total
        } else {
          this.dataList = []
          this.totalPage = 0
        }
        this.dataListLoading = false
      })
    },
    // 获取群众数据列表
    getApplyPublicList() {
      this.dataListLoadingPublic = true
      this.$http({
        url: this.$http.adornUrl('/admin/zyz/activity/apply/public/pagesForApplyPublicAll'),
        method: 'post',
        data: this.$http.adornData({
          'currentPage': this.pageIndexPublic,
          'pageSize': this.pageSizePublic,
          'activityId': this.dataPublicForm.activityId,
          'timePeriodId': this.dataPublicForm.timePeriodId,
          'activityName': this.dataPublicForm.activityName
        })
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.applyPublicList = data.obj.records
          this.applyPublicList.forEach((it) => {
            it.timePeriodRange = it.startTime && it.endTime ?
                moment(it.startTime).format('YYYY-MM-DD HH:mm') + '-' + moment(it.endTime).format('HH:mm') : ''
          })
          this.totalPagePublic = data.obj.total
        } else {
          this.applyPublicList = []
          this.totalPagePublic = 0
        }
        this.dataListLoadingPublic = false
      })
    },
    // 取消群众报名
    cancelApplyHandle(id,timePeriodId) {
      var ids = id ? [id] : this.dataListSelections.map(item => {
        return item.id
      })
      this.$confirm(`确定进取消该群众报名?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$http({
          url: this.$http.adornUrl('/api/activity/cancelApply'),
          method: 'get',
          params: this.$http.adornParams({
            'applyPublicIds': ids.join(','),
            'timePeriodId': timePeriodId
          })
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.$message({
              message: '操作成功',
              type: 'success',
              duration: 1500,
              onClose: () => {
                this.getApplyPublicList()
              }
            })
          } else {
            this.$message.error(data.msg)
          }
        })
      })
    },
    // 每页数
    sizeChangeHandle(val) {
      this.pageSize = val
      this.pageIndex = 1
      this.getDataList()
    },
    sizeChangePublicHandle(val) {
      this.pageSizePublic = val
      this.pageIndexPublic = 1
      this.getApplyPublicList()
    },
    // 当前页
    currentChangeHandle(val) {
      this.pageIndex = val
      this.getDataList()
    },
    currentChangePublicHandle(val) {
      this.pageIndexPublic = val
      this.getApplyPublicList()
    },
    // 多选
    selectionChangeHandle(val) {
      this.dataListSelections = val
    },
    // 重置
    resetForm() {
      this.$refs['dataForm']?.resetFields()
      this.getDataList()
    },
    // 导出
    exportServiceLong() {
      this.fullscreenLoading = true;
      this.$http({
        url: this.$http.adornUrl('/admin/zyz/activity/apply/exportForAudit'),
        method: 'post',
        responseType: 'arraybuffer',
        data: this.$http.adornData({
          'key': this.dataForm.key,
          'activityId': this.dataForm.activityId,
          'auditStatus': this.dataForm.auditStatus
        })
      }).then(({data}) => {
        if (data.code && data.code !== 0) {
          this.$message.error('导出失败')
        } else {
          this.fullscreenLoading = false;
          let blob = new Blob([data], {type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8'})
          let objectUrl = URL.createObjectURL(blob)
          let a = document.createElement('a')
          // window.location.href = objectUrl
          a.href = objectUrl
          a.download = '报名管理列表明细.xlsx'
          a.click()
          URL.revokeObjectURL(objectUrl)
          this.$message({
            type: 'success',
            message: '导出成功'
          })
        }
      })
    },
    // 导出
    exportServiceLongPublic() {
      this.fullscreenLoading = true;
      this.$http({
        url: this.$http.adornUrl('/admin/zyz/activity/apply/public/exportWithDynamicFields'),
        method: 'post',
        responseType: 'arraybuffer',
        data: this.$http.adornData({
          'activityId': this.dataPublicForm.activityId,
          'timePeriodId': this.dataPublicForm.timePeriodId,
          'activityName': this.dataPublicForm.activityName
        })
      }).then(({data}) => {
        if (data.code && data.code !== 0) {
          this.$message.error('导出失败')
        } else {
          this.fullscreenLoading = false;
          let blob = new Blob([data], {type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8'})
          let objectUrl = URL.createObjectURL(blob)
          let a = document.createElement('a')
          // window.location.href = objectUrl
          a.href = objectUrl
          a.download = '报名管理列表明细.xlsx'
          a.click()
          URL.revokeObjectURL(objectUrl)
          this.$message({
            type: 'success',
            message: '导出成功'
          })
        }
      })
    },
    auditHandle(id, name, status) {
      if (!id && this.dataListSelections.length === 0) {
        this.$message.warning('未选中需要审核的活动记录！')
        return
      }
      if (!id) {
        let ids = this.dataListSelections.map(item => {
          return item.id
        })
        id = ids.join(',')
      }
      this.$confirm(`确定${status ? '审核通过' : '驳回'}` + (name ? '"' + name + '"' : '') + '此人活动报名？', '系统提示', {
        customClass: 'audit_pass_msg_box',
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        showInput: !status,
        inputPlaceholder: '请输入驳回意见……',
        inputType: 'textarea',
        closeOnClickModal: false
      }).then((val) => {
        this.$http({
          url: this.$http.adornUrl('/admin/zyz/activity/apply/audit'),
          method: 'get',
          params: this.$http.adornParams({
            'ids': id,
            'status': status,
            'remark': val.value
          })
        }).then(({
                   data
                 }) => {
          if (data && data.code === 0) {
            this.$message.success(status ? '审核通过成功！' : '驳回成功！')
            this.getDataList()
            this.getApplyNum(this.dataForm.id)
          } else {
            this.$message.error(status ? '审核通过失败！' : '驳回失败！' + data.msg)
          }
        })
      })
      setTimeout(() => {
        var content = document.getElementsByClassName('audit_pass_msg_box')[0].childNodes[1]
        content.style.paddingLeft = '60px'
        content.style.paddingRight = '60px'
        var submitBtn = document.getElementsByClassName('audit_pass_msg_box')[0].childNodes[2]
            .childNodes[1]
        submitBtn.style.backgroundColor = '#F56C6C'
        submitBtn.style.borderColor = '#F56C6C'
      }, 50)
    }

  }
}
</script>

<style>
  .demo-table-expand {
    font-size: 0;
    padding: 8px;
  }
  .demo-table-expand label {
    width: 90px;
    color: #99a9bf;
  }
  .demo-table-expand .el-form-item {
    margin-right: 0;
    margin-bottom: 0;
    width: 50%;
  }
  .demo-table-expand-empty {
    display: flex;
    justify-content: center;
  }
</style>