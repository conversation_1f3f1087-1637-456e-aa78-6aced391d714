<template>
  <div class="mod-role">
    <el-form :inline="true" ref="dataForm" :model="dataForm" @keyup.enter.native="getDataList()">
      <el-form-item label="关键字" prop="key" style="padding-right: 40px">
        <el-input v-model="dataForm.key" placeholder="活动名称/联系人/联系方式/评价内容" clearable style="width: 120%">
        </el-input>
      </el-form-item>
      <el-form-item label="是否本团队" prop="teamMember">
        <el-select v-model="dataForm.teamMember" placeholder="请选择" clearable>
          <el-option v-for="item in [{label: '本团队', value: true}, {label: '非本团队', value: false}]"
                     :key="item.value" :label="item.label" :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="是否展示" prop="appraiseStatus">
        <el-select v-model="dataForm.appraiseStatus" placeholder="请选择" clearable>
          <el-option v-for="item in [{label: '展示', value: true}, {label: '未展示', value: false}]"
                     :key="item.value" :label="item.label" :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-search" type="warning" @click="getDataList()">查询</el-button>
        <el-button class="el-icon-refresh-left" @click="resetForm()">重置</el-button>
      </el-form-item>
    </el-form>
    <div class="f-s-c" style="padding: 15px 0;display: flex; justify-content: flex-end">
      <div>
        <el-button icon="el-icon-star-on" :disabled="dataListSelections.length <= 0" v-if="isAuth('resource:list:passBatch')"
                   type="primary" @click="auditHandle(null, null, true)">批量展示
        </el-button>
      </div>
    </div>
    <el-table :data="dataList" border v-loading="dataListLoading" @selection-change="selectionChangeHandle"
              style="width: 100%;">
      <el-table-column type="selection" header-align="center" align="center"
                       :selectable="(row, index) => {return row.appraiseStatus === false}" width="40">
      </el-table-column>
      <el-table-column type="index" label="序号" align="center" width="50">
      </el-table-column>
      <el-table-column prop="activityName" header-align="center" align="center" width="250"
                       :show-overflow-tooltip="true"
                       label="活动名称">
        <template slot-scope="scope">
          <a style="cursor: pointer" @click="activityDetailHandler(scope.row.activityId)">{{ scope.row.activityName }}</a>
        </template>
      </el-table-column>
      <el-table-column prop="periodTimeRange" header-align="center" align="center" min-width="180" label="活动时间">
      </el-table-column>
      <el-table-column prop="volunteerName" header-align="center" align="center" min-width="100" label="志愿者姓名">
      </el-table-column>
      <el-table-column prop="volunteerPhone" header-align="center" align="center" min-width="150" label="联系电话">
      </el-table-column>
      <el-table-column prop="appraiseContent" header-align="center" :show-overflow-tooltip="true" min-width="250"
                       align="center" label="评价内容">
      </el-table-column>
      <el-table-column prop="appraiseTime" header-align="center" :show-overflow-tooltip="true" min-width="180"
                       align="center" label="评价时间">
      </el-table-column>
      <el-table-column fixed="right" prop="appraiseFraction" header-align="center" min-width="180" align="center"
                       label="活动内容">
        <template slot-scope="scope">
          <el-rate v-if="scope.row.appraiseFraction !=null" v-model="scope.row.appraiseFraction" disabled
                   show-score text-color="#ff9900" score-template="{value}">
          </el-rate>
        </template>
      </el-table-column>
      <el-table-column fixed="right" prop="appraiseFractionOrg" header-align="center" min-width="180" align="center"
                       label="发布组织">
        <template slot-scope="scope">
          <el-rate v-if="scope.row.appraiseFractionOrg !=null" v-model="scope.row.appraiseFractionOrg" disabled
                   show-score text-color="#ff9900" score-template="{value}">
          </el-rate>
        </template>
      </el-table-column>
      <el-table-column fixed="right" prop="appraiseFractionJoin" header-align="center" min-width="180" align="center"
                       label="活动参与">
        <template slot-scope="scope">
          <el-rate v-if="scope.row.appraiseFractionJoin !=null" v-model="scope.row.appraiseFractionJoin" disabled
                   show-score text-color="#ff9900" score-template="{value}">
          </el-rate>
        </template>
      </el-table-column>
      <el-table-column prop="status" header-align="center" align="center" min-width="95" label="前端状态">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.appraiseStatus == true" size="small">展示</el-tag>
          <el-tag v-else size="small" type="danger">不展示</el-tag>
        </template>
      </el-table-column>
      <el-table-column fixed="right" header-align="center" align="center"
                       min-width="100px" label="操作">
        <template slot-scope="scope">
          <el-button v-if="scope.row.appraiseStatus == true" type="text" size="small"
                     @click="auditHandle(scope.row.id, scope.row.name, false)">不展示
          </el-button>
          <el-button v-else type="text" size="small"
                     @click="auditHandle(scope.row.id, scope.row.name, true)">设为前端展示
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination @size-change="sizeChangeHandle" @current-change="currentChangeHandle" :current-page="pageIndex"
                   :page-sizes="[10, 20, 50, 100]" :page-size="pageSize" :total="totalPage"
                   layout="total, sizes, prev, pager, next, jumper">
    </el-pagination>
    <!-- 活动详情 -->
    <activity-detail v-if="activityDetailVisible" ref="activityDetail"></activity-detail>
  </div>
</template>

<script>
import moment from 'moment'
import listMixin from '@/mixins/list-mixins'
import ActivityDetail from './activity-detail'

export default {
  mixins: [listMixin],
  data() {
    return {
      mixinOptions: {
        dataUrl: '/admin/zyz/activity/apply/pageForAppraise'
      },
      dataForm: {
        key: null,
        actType: null,
        auditStatus: null,
        teamMember: null,
        timeRange: []
      },
      fields: [],
      orgList: [],
      activityDetailVisible: false
    }
  },
  components: {
    ActivityDetail
  },
  activated() {
    this.getDataList()
  },
  methods: {
    resetForm() {
      this.$refs['dataForm']?.resetFields()
      this.dataForm.applyStartTime = null
      this.dataForm.applyEndTime = null
      this.dataForm.key = null
      this.dataForm.auditStatus = null
      this.this.getDataList()
    },
    queryBeforeHandle() {
      // 查询前操作
      this.dataForm.applyStartTime = this.dataForm.timeRange[0]
      this.dataForm.applyEndTime = this.dataForm.timeRange[1]
    },
    activityDetailHandler(activityId) {
      this.activityDetailVisible = true
      this.$nextTick(() => {
        this.$refs.activityDetail.init(activityId)
      })
    },
    querySuccessHandle(data) {
      data.records.forEach(item => {
        item.periodTimeRange = item.timePeriodStartTime && item.timePeriodEndTime ?
            moment(item.timePeriodStartTime).format('YYYY-MM-DD HH:mm') + '-' + moment(item.timePeriodEndTime).format('HH:mm') : ''
      })
      this.dataList = data.records
      this.totalPage = data.total
    },
    auditHandle(id, name, status) {
      if (!id && this.dataListSelections.length === 0) {
        this.$message.warning('未选中需要修改为前端展示的记录！')
        return
      }
      if (!id) {
        let ids = this.dataListSelections.map(item => {
          return item.id
        })
        id = ids.join(',')
      }
      this.$confirm(`确定${status ? '设为前端展示' : '设为前端不展示'}` + (name ? '"' + name + '"' : '') + '活动的评价？', '系统提示', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        closeOnClickModal: false
      }).then((val) => {
        this.$http({
          url: this.$http.adornUrl('/admin/zyz/activity/apply/appraiseStatus'),
          method: 'get',
          params: this.$http.adornParams({
            'ids': id,
            'status': status
          })
        }).then(({
                   data
                 }) => {
          if (data && data.code === 0) {
            this.$message.success(status ? '设为前端展示成功！' : '设为前端不展示成功！')
            this.query()
          } else {
            this.$message.error(status ? '设为前端展示失败！' : '设为前端不展示失败！' + data.msg)
          }
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '操作取消'
        });
      });
      setTimeout(() => {
        var content = document.getElementsByClassName('audit_pass_msg_box')[0].childNodes[1]
        content.style.paddingLeft = '60px'
        content.style.paddingRight = '60px'
        var submitBtn = document.getElementsByClassName('audit_pass_msg_box')[0].childNodes[2]
            .childNodes[1]
        submitBtn.style.backgroundColor = '#F56C6C'
        submitBtn.style.borderColor = '#F56C6C'
      }, 50)
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
