<template>
  <div class="mod-role">
    <el-form :inline="true" ref="dataForm" :model="dataForm" @keyup.enter.native="getDataList()">
      <el-form-item label="关键字" prop="key" style="padding-right: 40px">
        <el-input v-model="dataForm.key" placeholder="活动名称/联系人/联系方式" clearable style="width: 120%"></el-input>
      </el-form-item>
      <el-form-item label="报名时间" prop="timeRange">
        <el-date-picker v-model="dataForm.timeRange" clearable type="datetimerange"
                        value-format="yyyy-MM-dd HH:mm:ss" align="right" start-placeholder="开始时间" end-placeholder="结束时间"
                        :default-time="['00:00:00', '23:59:59']">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="审核状态" prop="auditStatus">
        <el-select v-model="dataForm.auditStatus" placeholder="请选择" clearable>
          <el-option
              v-for="item in [{label: '待审核', value: 'act_apply_wait_audit'},
              {label: '审核通过', value: 'act_apply_audit_success'},
               { label: '已取消', value: 'act_apply_cancelled' },
               {label: '驳回', value: 'act_apply_reject'}]"
              :key="item.value" :label="item.label" :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="是否本团队" prop="teamMember">
        <el-select v-model="dataForm.teamMember" placeholder="请选择" clearable>
          <el-option v-for="item in [{label: '本团队', value: true}, {label: '非本团队', value: false}]"
                     :key="item.value" :label="item.label" :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-search" type="warning" @click="getDataList()">查询</el-button>
        <el-button icon="el-icon-refresh-left" @click="resetForm()">重置</el-button>
      </el-form-item>
    </el-form>
    <div class="f-s-c" style="padding: 15px 0;display: flex; justify-content: flex-end">
      <div>
        <el-button icon="el-icon-s-check" :disabled="dataListSelections.length <= 0" v-if="isAuth('resource:list:passBatch')"
                   type="primary" @click="auditHandle(null, null, true)">批量通过
        </el-button>
      </div>
    </div>
    <el-table :data="dataList" border v-loading="dataListLoading" @selection-change="selectionChangeHandle"
              style="width: 100%;">
      <el-table-column type="selection" header-align="center" align="center"
                       :selectable="(row, index) => {return row.auditStatus === 'act_apply_wait_audit'}" width="40">
      </el-table-column>
      <el-table-column type="index" label="序号" align="center" width="50">
      </el-table-column>
      <el-table-column prop="activityName" header-align="center" align="center" min-width="300"
                       :show-overflow-tooltip="true"
                       label="活动名称">
        <template slot-scope="scope">
          <a style="cursor: pointer" @click="activityDetailHandler(scope.row.activityId)">{{ scope.row.activityName }}</a>
        </template>
      </el-table-column>
      <el-table-column prop="timePeriodRange" header-align="center" min-width="200" :show-overflow-tooltip="true"
                       align="center" label="活动时段">
      </el-table-column>
      <el-table-column prop="volunteerName" header-align="center" min-width="100" align="center" label="志愿者姓名">
          <template slot-scope="scope">
              <a style="cursor: pointer" @click="zyzDetailHandle(scope.row.volunteerId, true)">{{ scope.row.volunteerName }}</a>
          </template>
      </el-table-column>
      <el-table-column prop="volunteerPhone" header-align="center" align="center" min-width="120" label="联系电话">
      </el-table-column>
      <el-table-column prop="applyTime" header-align="center" min-width="200" :show-overflow-tooltip="true"
                       align="center" label="报名时间">
      </el-table-column>
      <el-table-column prop="timeRange" header-align="center" align="center" min-width="120" label="是否本团队成员">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.teamMember" size="small" type="danger">是</el-tag>
          <el-tag v-else size="small">否</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="auditStatus" header-align="center" align="center" min-width="120" label="审核状态">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.auditStatus === 'act_apply_wait_audit'" type="warning">待审核</el-tag>
          <el-tag v-else-if="scope.row.auditStatus === 'act_apply_audit_success'" type="success">审核通过</el-tag>
          <el-tag v-else-if="scope.row.auditStatus === 'act_apply_cancelled'" type="info">已取消</el-tag>
          <el-tag v-else type="danger">驳回</el-tag>
        </template>
      </el-table-column>
      <el-table-column fixed="right" header-align="center" align="center" min-width="150" label="操作">
        <template slot-scope="scope">
          <el-button v-if="scope.row.canAudit" type="text" size="small"
                     @click="auditHandle(scope.row.id, scope.row.name, true)">通过
          </el-button>
          <el-button v-if="scope.row.canAudit" type="text" size="small"
                     @click="auditHandle(scope.row.id, scope.row.name, false)">驳回
          </el-button>
          <el-button v-if="isAuth('apply:sync') && scope.row.enableSync === true" type="text" size="small"
                     @click="syncMemberOne(scope.row.id)">同步
          </el-button>
          <!--          <el-dropdown @command="handleSync" style="padding-left: 10px">-->
          <!--            <el-button type="text" size="small">同步</el-button>-->
          <!--            <el-dropdown-menu slot="dropdown" class="header-new-drop">-->
          <!--              <el-dropdown-item :command="beforeHandleSync(scope.row.id,'member')" :disabled="scope.row.applyIsSync !== 'sync_failure' && scope.row.applyIsSync !== 'sync_wait'">报名同步</el-dropdown-item>-->
          <!--              <el-dropdown-item :command="beforeHandleSync(scope.row.id,'service_time')"-->
          <!--                                :disabled="scope.row.serviceLongIsSync !== 'sync_failure' && scope.row.serviceLongIsSync !== 'sync_wait'">服务时长同步-->
          <!--              </el-dropdown-item>-->
          <!--            </el-dropdown-menu>-->
          <!--          </el-dropdown>-->
        </template>
      </el-table-column>
    </el-table>
    <el-pagination @size-change="sizeChangeHandle" @current-change="currentChangeHandle" :current-page="pageIndex"
                   :page-sizes="[10, 20, 50, 100]" :page-size="pageSize" :total="totalPage"
                   layout="total, sizes, prev, pager, next, jumper">
    </el-pagination>
    <!-- 需求详情 -->
    <activity-detail v-if="activityDetailVisible" ref="activityDetail"></activity-detail>
      <!-- 弹窗, 新增 / 修改 -->
    <zyz-detail v-if="zyzDetailVisible" ref="zyzDetail"></zyz-detail>
  </div>
</template>

<script>
import listMixin from '@/mixins/list-mixins'
import ActivityDetail from './activity-detail'
import moment from 'moment'
import ZyzDetail from '../zyz/volunteer-add-or-update'

export default {
  mixins: [listMixin],
  data() {
    return {
      mixinOptions: {
        dataUrl: '/admin/zyz/activity/apply/pagesForAudit'
      },
      dataForm: {
        key: null,
        actType: null,
        auditStatus: 'act_apply_wait_audit',
        teamMember: null,
        timeRange: []
      },
      fields: [],
      orgList: [],
      activityDetailVisible: false,
      zyzDetailVisible: false,
    }
  },
  components: {
    ActivityDetail,
    ZyzDetail
  },
  activated() {
    this.totalPage = 0
    this.getDataList()
  },
  methods: {
    resetForm() {
      this.$refs['dataForm']?.resetFields()
      this.dataForm.applyStartTime = null
      this.dataForm.applyEndTime = null
      this.dataForm.key = null
      this.dataForm.auditStatus = null
      this.dataForm.timeRange = []
      this.getDataList()
    },
    queryBeforeHandle() {
      // 查询前操作
      this.dataForm.applyStartTime = this.dataForm.timeRange && this.dataForm.timeRange[0]
      this.dataForm.applyEndTime = this.dataForm.timeRange && this.dataForm.timeRange[1]
    },
    querySuccessHandle(data) {
      // 查询成功操作
      this.dataList = data.records
      this.totalPage = data.total
      data.records.forEach((it) => {
        it.timePeriodRange = it.timePeriodStartTime && it.timePeriodEndTime ? moment(it.timePeriodStartTime).format('YYYY-MM-DD HH:mm') + '-' + moment(it.timePeriodEndTime).format('HH:mm') : ''
        it.canAudit = it.auditStatus === 'act_apply_wait_audit'
      })
    },
    //获取所属区域
    getOrg() {
      this.$http({
        url: this.$http.adornUrl(`/admin/org/getOrgTreeOfAll`),
        method: 'get',
        params: this.$http.adornParams()
      }).then(({
                 data
               }) => {
        if (data && data.code === 0) {
          this.orgList = this.getTreeData(data.obj)
        } else {
          this.orgList = []
        }
      })
    },
    getFields() {
      this.$http({
        url: this.$http.adornUrl('/admin/zyz/platform/belongFieldDict/cascade'),
        method: 'get'
      }).then(({
                 data
               }) => {
        if (data && data.code === 0) {
          this.fields = this.getTreeData(data.obj)
        } else {
          this.fields = []
        }
      })
    },
    // 多选
    selectionChangeHandle (val) {
      this.dataListSelections = val
    },
    // 分页, 每页条数
    sizeChangeHandle (val) {
      this.pageSize = val
      this.pageIndex = 1
      this.query()
    },
    // 分页, 当前页
    currentChangeHandle (val) {
      this.pageIndex = val
      this.query()
    },
    // *处理点位分类最后children数组为空的状态
    getTreeData: function (data) {
      var that = this
      // 循环遍历json数据
      data.forEach(function (e) {
        if (!e.children || e.children.length < 1) {
          e.children = undefined
        } else {
          // children若不为空数组，则继续 递归调用 本方法
          that.getTreeData(e.children)
        }
      })
      return data
    },
    handleChange(value, type) {
      if (type === 'field') {
        this.dataForm.fieldId = value[value.length - 1]
      }
      if (type === 'org') {
        this.dataForm.publishOrgCode = value.join(',')
      }
    },
    activityDetailHandler(activityId) {
      this.activityDetailVisible = true
      this.$nextTick(() => {
        this.$refs.activityDetail.init(activityId)
      })
    },
    auditHandle(id, name, status) {
      if (!id && this.dataListSelections.length === 0) {
        this.$message.warning('未选中需要审核的活动报名记录！')
        return
      }
      if (!id) {
        let ids = this.dataListSelections.map(item => {
          return item.id
        })
        id = ids.join(',')
      }
      this.$confirm(`确定${status ? '审核通过' : '驳回'}` + (name ? '"' + name + '"' : '') + '此人报名？', '系统提示', {
        customClass: 'audit_pass_msg_box',
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        showInput: !status,
        inputPlaceholder: '请输入驳回意见……',
        inputType: 'textarea',
        closeOnClickModal: false
      }).then((val) => {
        this.$http({
          url: this.$http.adornUrl('/admin/zyz/activity/apply/audit'),
          method: 'get',
          params: this.$http.adornParams({
            'ids': id,
            'status': status,
            'remark': val.value
          })
        }).then(({
                   data
                 }) => {
          if (data && data.code === 0) {
            this.$message.success(status ? '审核通过成功！' : '驳回成功！')
            this.query()
          } else {
            this.$message.error(status ? '审核通过失败！' : '驳回失败！' + data.msg)
          }
        })
      })
      setTimeout(() => {
        var content = document.getElementsByClassName('audit_pass_msg_box')[0].childNodes[1]
        content.style.paddingLeft = '60px'
        content.style.paddingRight = '60px'
        var submitBtn = document.getElementsByClassName('audit_pass_msg_box')[0].childNodes[2]
            .childNodes[1]
        submitBtn.style.backgroundColor = '#F56C6C'
        submitBtn.style.borderColor = '#F56C6C'
      }, 50)
    },
    handleSync(command) {
      if (command.status === 'member') {
        this.syncMemberOne(command.id)
      }
      if (command.status === 'service_time') {
        this.syncServiceTimeOne(command.id)
      }
    },
    beforeHandleSync(id, status) {
      return {
        id,
        status
      }
    },
    syncMemberOne(id) {
      this.$confirm(`该操作将执行活动报名同步，报名同步结束后将自动执行服务时长同步，确定进行该操作?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        closeOnClickModal: false
      }).then(() => {
        this.dataListLoading = true
        this.$http({
          url: this.$http.adornUrl('/admin/zyz/activity/apply/syncMemberOne'),
          method: 'get',
          params: this.$http.adornParams({'id': id})
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.$message({
              message: '请求活动报名同步成功，市平台返回：（' + data.obj.message + '）',
              type: data.obj.resultCode === 'SUCCESS' ? 'success' : 'warning',
              duration: 3000
            })
            // this.getDataList()
          } else {
            this.$message.error(data.msg)
          }
          this.syncServiceTimeOne(id)
        })
      })
    },
    syncServiceTimeOne(id) {
      // this.$confirm(`确定进行服务时长同步?`, '提示', {
      //   confirmButtonText: '确定',
      //   cancelButtonText: '取消',
      //   type: 'warning'
      // }).then(() => {
      this.$http({
        url: this.$http.adornUrl('/admin/zyz/activity/apply/syncServiceTimeOne'),
        method: 'get',
        params: this.$http.adornParams({'id': id})
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.$message({
            message: '请求活动服务时长同步成功，市平台返回：（' + data.obj.message + '）',
            type: data.obj.resultCode === 'SUCCESS' ? 'success' : 'warning',
            duration: 3000
          })
        } else {
          this.$message.error(data.msg)
        }
        this.dataListLoading = false
        this.getDataList()
      })
      // })
    },
      // 详情
    zyzDetailHandle(id, disabled) {
        this.zyzDetailVisible = true
        this.$nextTick(() => {
            this.$refs.zyzDetail.init(id, disabled)
        })
    },
  }
}
</script>
