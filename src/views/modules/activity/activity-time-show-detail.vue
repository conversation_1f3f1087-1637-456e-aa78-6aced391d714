<template>
    <el-dialog title='公示详情' :close-on-click-modal="false" :visible.sync="visible"
               width="60%">
        <el-form :model="dataForm"  ref="dataForm" label-width="150px">
            <el-row :gutter="20">
                <el-col :span="24">
                    <el-form-item label="活动时段" prop="activityName">
                        <el-input v-model="dataForm.activityName" placeholder="活动时段" disabled></el-input>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row :gutter="20">
                <el-col :span="24">
                    <el-form-item label="公示内容" prop="content">
                        <el-input type="textarea" v-model="dataForm.content" placeholder="公示内容" rows="8" maxlength="2000" show-word-limit disabled/>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row :gutter="20">
                <el-col :span="24">
                    <el-form-item label="公示图片" prop="picsUrl">
                        <el-upload
                                list-type="picture-card"
                                :action="this.$http.adornUrl(`/admin/oss/upload`)"
                                :headers="myHeaders"
                                :data="{serverCode: this.serverCode,media:false}"
                                :show-file-list="true"
                                :multiple="true"
                                :file-list="fileList"
                                :on-preview="handlePictureCardPreview"
                                :on-remove="handleRemovePicture"
                                :on-success="function (res,file){return handleAvatarSuccess(res,file, 'pictureList')}"
                                :disabled="true"
                                :before-upload="beforeAvatarUpload"
                                :class="'hide'">
                            <div slot="tip" class="el-upload__tip" style="color: red">需上传3-9张图片，文件格式jpg、bmp或png，大小不超2M</div>
                        </el-upload>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <el-tabs v-model="tabName" type="card" ref="tabs">
            <el-tab-pane label="日志记录" name="log">
                <el-table key="logKey" :data="logList" border style="width: 100%;">
                    <el-table-column type="index" align="center" label="序号" width="50">
                    </el-table-column>
                    <el-table-column align="center" label="日志记录">
                        <el-table-column prop="operateTypeText" header-align="center" width="180" :show-overflow-tooltip="true"
                                         align="center" label="操作类型">
                        </el-table-column>
                        <el-table-column prop="operatorName" header-align="center" align="center" width="160" label="操作人">
                        </el-table-column>
                        <el-table-column prop="operatorOrgName" header-align="center" align="center" width="200" label="所在组织">
                        </el-table-column>
                        <el-table-column prop="operateTime" header-align="center" align="center" width="180" label="操作时间">
                        </el-table-column>
                        <el-table-column prop="remark" header-align="center" align="center" label="操作结果">
                        </el-table-column>
                    </el-table-column>
                </el-table>
            </el-tab-pane>
        </el-tabs>
        <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
    </span>
    </el-dialog>
</template>

<script>
import Vue from "vue";

export default {
    data() {
        return {
            visible: false,
            posed: false,
            submitLoading: false,
            pictureList: [],
            fileList: [],
            logList:[],
            tabName: 'log',
            serverCode: 'LocalServer',
            myHeaders: {Authorization: Vue.cookie.get('Authorization')},
            initForm: {
                id: null,
                periodId:null,
                version: null,
                activityId: null,
                activityName:null,
                signTime: null,
                sync: null,
                syncTime: null,
                syncRemark: null,
                recruitSyncId: null,
                content: null,
                picList: null,

            },
            dataForm: {},
        }
    },
    components: {},
    methods: {
        async init(periodId, id) {
            this.dataForm = _.cloneDeep(this.initForm)
            this.dataForm.id = id || null
            this.dataForm.periodId = periodId || null
            this.visible = true
            this.pictureList = []
            this.fileList  = []
            this.getLogs()
            this.$nextTick(() => {
                this.$refs['dataForm']?.resetFields()
                if (this.dataForm.periodId) {
                    this.$http({
                        url: this.$http.adornUrl(`/admin/zyz/activity/time/period/show/getDetail`),
                        method: 'get',
                        params: this.$http.adornParams({
                            periodId: this.dataForm.periodId,
                            id: this.dataForm.id
                        })
                    }).then(({data}) => {
                        if (data && data.code === 0) {
                            this.dataForm = data.obj
                            if(this.dataForm.picsUrl){
                                let picture = this.dataForm.picsUrl.split(',')
                                this.pictureList = picture
                                picture.forEach(item => {
                                    let obj = {
                                        url: this.$http.adornAttachmentUrl(item)
                                    }
                                    this.fileList.push(obj)
                                })
                            }

                        }
                    })
                }
            })
        },
        handlePictureCardPreview(file) {
            this.dialogImageUrl = file.url
            this.dialogVisible = true
        },
        handleAvatarSuccess(res, file, field) {
            if (res.success) {
                this.$message({
                    message: '操作成功',
                    type: 'success',
                    duration: 1500
                })
                this.pictureList.push(res.obj.path)
            } else {
                this.$message.error('上传失败')
            }
        },
        handleRemovePicture(file, fileList) {
            this.pictureList = []
            fileList.forEach(item => {
                console.log(item)
                try {
                    this.pictureList.push(item.url.slice(item.url.indexOf('vpath') - 1))
                } catch (e) {
                    this.pictureList.push(item.url)
                }
            })
        },
        beforeAvatarUpload: function (file) {
            var isJPG = file.type === 'image/jpeg'
            var isPNG = file.type === 'image/png'
            var isBMP = file.type === 'image/bmp'
            var isLt20M = file.size / 1024 / 1024 < 20

            if (!isJPG && !isPNG && !isBMP) {
                this.$message.error('上传图片只能是图片!')
            }
            if (!isLt20M) {
                this.$message.error('上传文件大小不能超过 20MB!')
            }
            return (isJPG || isPNG || isBMP) && isLt20M
        },
        //获取日志
        getLogs() {
            this.$http({
                url: this.$http.adornUrl(`/admin/zyz/activity/time/period/show/log/getLogsByShowId`),
                method: 'get',
                params: this.$http.adornParams({showId: this.dataForm.id})
            }).then(({data}) => {
                if (data && data.code === 0) {
                    this.logList = data.obj
                } else {
                    this.logList = []
                }
            })
        },
    }
}
</script>
<style lang="scss">
.hide .el-upload--picture-card {
    display: none !important;
}
</style>
