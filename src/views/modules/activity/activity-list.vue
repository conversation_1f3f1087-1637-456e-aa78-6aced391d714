<template>
  <div class="mod-config">
    <el-form :inline="true" :model="dataForm" ref="dataForm" @keyup.enter.native="queryPage()">
      <el-form-item label="关键字" prop="key" style="padding-right: 170px">
        <el-input v-model="dataForm.key" placeholder="活动名称/联系人/联系方式/活动地址/活动主体" clearable style="width: 180%"></el-input>
      </el-form-item>
      <el-form-item :label="'上级组织'" prop="publishOrgCodes">
        <el-cascader placeholder="请选择" v-model="dataForm.publishOrgCodes" :options="orgList"
                     :disabled="this.dataForm.id"
                     :show-all-levels="false" clearable :props="{ checkStrictly: true, value: 'code', label: 'name' }"
                     @change="(value) => { handleChange(value, 'org') }"></el-cascader>
      </el-form-item>
      <el-form-item label="所属领域" prop="fieldId">
        <el-cascader placeholder="请选择" v-model="dataForm.fieldIds" :options="fields"
                     @change="(value) => { handleChange(value, 'field') }" clearable
                     :props="{ checkStrictly: true, label: 'typeName', value: 'typeId' }"></el-cascader>
      </el-form-item>
      <el-form-item label="活动时间" prop="timeRange">
        <el-date-picker v-model="dataForm.timeRange" clearable type="daterange" value-format="yyyy-MM-dd"
                        align="right" start-placeholder="开始时间" end-placeholder="结束时间">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="发布时间" prop="publishTimeRange">
        <el-date-picker v-model="dataForm.publishTimeRange" clearable type="daterange"
                        value-format="yyyy-MM-dd" align="right" start-placeholder="开始时间" end-placeholder="结束时间">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="招募对象" prop="recruitTarget">
        <el-dict :code="'recruitment_targets'" v-model="dataForm.recruitTarget"></el-dict>
      </el-form-item>
      <el-form-item label="公开招募" prop="open">
        <el-select v-model="dataForm.open" placeholder="请选择" clearable>
          <el-option v-for="item in [{ label: '是', value: true }, { label: '否', value: false }]" :key="item.value"
                     :label="item.label" :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="是否置顶" prop="top">
        <el-select v-model="dataForm.top" placeholder="请选择" clearable>
          <el-option v-for="item in [{ label: '是', value: true }, { label: '否', value: false }]" :key="item.value"
                     :label="item.label" :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="活动审核状态" prop="auditStatus">
        <el-dict :code="'activity_audit_status'" v-model="dataForm.auditStatus"></el-dict>
      </el-form-item>
      <el-form-item label="阵地计划" prop="scheduleDetailId">
        <el-cascader
          placeholder="请选择"
          v-model="dataForm.scheduleDetailId"
          :options="scheduleOptions"
          :show-all-levels="false"
          :props="{ checkStrictly: false, emitPath: false}"
          clearable>
        </el-cascader>
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-search" type="warning" @click="queryPage()">查询</el-button>
        <el-button @click="resetForm()" icon="el-icon-refresh-left">重置</el-button>
      </el-form-item>
    </el-form>
    <div class="f-s-c" style="padding: 15px 0;display: flex;justify-content:flex-end;align-items: center ">
      <div>
        <el-button type="danger" @click="webShowBatchOn()" :disabled="dataListSelections.length <= 0">批量上架</el-button>
        <el-button type="danger" @click="webShowBatchOff()" :disabled="dataListSelections.length <= 0">批量下架</el-button>
        <el-button v-if="isAuth('activity:big:apply') && teamGranted" icon="el-icon-plus" type="primary" @click="bigActivityAddOrUpdateHandle(null, false)">发布大型赛事</el-button>
        <el-button icon="el-icon-plus" type="primary" @click="addOrUpdateHandle(null, false, false)">新增</el-button>
        <el-button icon="el-icon-download" type="success" v-loading.fullscreen.lock="fullscreenLoading" @click="exportActivity()">全部导出</el-button>
        <el-button icon="el-icon-circle-plus" v-if="isAuth('zyz:activity:bulu')" type="warning" @click="addOrUpdateHandle(null, false, true)">补录</el-button>
      </div>
    </div>
    <el-table :data="dataList" border v-loading="dataListLoading" @selection-change="selectionChangeHandle"
              style="width: 100%;">
      <el-table-column type="selection" header-align="center" align="center" width="50" :selectable="(row) => row.autoEnable"></el-table-column>
      <el-table-column prop="name" header-align="center" align="center" min-width="300" show-overflow-tooltip label="活动名称">
        <template slot-scope="scope">
          <a style="cursor: pointer" @click="activityDetailHandler(scope.row.id)">{{ scope.row.name }}</a>
        </template>
      </el-table-column>
      <el-table-column prop="auditStatusText" header-align="center" align="center" min-width="160" label="活动审核状态"/>
      <el-table-column prop="timeRange" header-align="center" align="center" min-width="180" label="活动起止时间">
        <template slot-scope="scope">
          <a style="cursor: pointer" @click="showTimeRange(scope.row.id)">{{ scope.row.timeRange }}</a>
        </template>
      </el-table-column>
      <el-table-column prop="activityStatusText" header-align="center" align="center" label="活动状态"/>
<!--      <el-table-column prop="actTypeText" header-align="center" align="center" min-width="160" label="活动类型"/>-->
      <el-table-column prop="recruitTargetsText" header-align="center" align="center" min-width="160" label="招募对象"/>
      <el-table-column prop="dockingTypeText" header-align="center" align="center" label="对接数据" min-width="100">
        <template slot-scope="scope">
          <a style="cursor: pointer" v-if="scope.row.actType !== 'zyz_activity_type_aspiration'"
             @click="actDockingHandler(scope.row)">{{
              scope.row.dockingTypeText
            }}</a>
          <span v-if="scope.row.actType === 'zyz_activity_type_aspiration'">无对接</span>
        </template>
      </el-table-column>
      <el-table-column prop="belongField" header-align="center" align="center" min-width="200" label="所属领域">
        <template slot-scope="scope">
          <span>{{
              scope.row.belongFieldNameTop && scope.row.belongFieldNameEnd ? scope.row.belongFieldNameTop +
                  '-' + scope.row.belongFieldNameEnd : (scope.row.belongFieldNameTop ||
                  scope.row.belongFieldNameEnd)
            }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="publishOrgName" header-align="center" align="center" min-width="150" show-overflow-tooltip label="发布单位"/>
      <el-table-column prop="contactPerson" header-align="center" align="center" min-width="140" label="联系人"/>
      <el-table-column prop="contactPhone" header-align="center" align="center" min-width="160" label="联系电话"/>
      <el-table-column prop="submitTime" header-align="center" align="center" min-width="160" label="提交时间"/>
      <el-table-column prop="scheduleDetailName" header-align="center" align="center" min-width="480" label="阵地计划"/>
      <el-table-column prop="open" header-align="center" align="center" label="公开招募">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.open" size="small">是</el-tag>
          <el-tag v-else size="small" type="danger">否</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="top" header-align="center" align="center" label="置顶">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.top" size="small">是</el-tag>
          <el-tag v-else size="small" type="danger">否</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="webShow" header-align="center" align="center" label="上/下架">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.webShow" size="small">上架</el-tag>
          <el-tag v-else size="small" type="danger">下架</el-tag>
        </template>
      </el-table-column>
      <el-table-column
          prop="syncText"
          header-align="center"
          align="center"
          min-width="120"
          v-if="!actSyncByTimePeriod"
          label="活动同步状态">
        <template slot-scope="scope">
          <el-popover trigger="hover" placement="top">
            <p style="text-align: center">
              {{'同步时间：' + (scope.row.syncTime ? scope.row.syncTime : '')}}<br>{{ scope.row.syncRemark }}</p>
            <div slot="reference" class="name-wrapper">
              <el-tag v-if="scope.row.sync === 'sync_failure'" type="danger">{{ scope.row.syncText }}</el-tag>
              <el-tag v-if="scope.row.sync === 'sync_wait'" type="warning">{{ scope.row.syncText }}</el-tag>
              <el-tag v-if="scope.row.sync === 'sync_success'" type="success">{{ scope.row.syncText }}</el-tag>
            </div>
          </el-popover>
        </template>
      </el-table-column>
      <el-table-column fixed="right" header-align="center" align="center" min-width="150" label="操作">
        <template slot-scope="scope">
          <el-button type="text" size="small" v-if="scope.row.bigActivity" @click="bigActivityAddOrUpdateHandle(scope.row.id, true)">复制</el-button>
          <el-button type="text" size="small" v-if="!scope.row.bigActivity" @click="addOrUpdateHandle(scope.row.id, true, false)">复制</el-button>
          <el-button type="text" size="small" @click="preview(scope.row.id)">预览</el-button>
          <el-dropdown @command="" style="padding-left: 10px">
            <el-button type="text" size="small">更多操作</el-button>
            <el-dropdown-menu slot="dropdown" class="header-new-drop">
              <el-dropdown-item v-if="scope.row.enable && !scope.row.bigActivity" @click.native="addOrUpdateHandle(scope.row.id, false, false)">
                <el-button size="small" type="text">修改</el-button>
              </el-dropdown-item>
              <el-dropdown-item v-if="scope.row.enable && scope.row.bigActivity" @click.native="bigActivityAddOrUpdateHandle(scope.row.id, false)">
                <el-button size="small" type="text">修改</el-button>
              </el-dropdown-item>
              <el-dropdown-item v-if="scope.row.enable" type="text" size="small" @click.native="deleteHandle(scope.row.id)">
                <el-button size="small" type="text">删除</el-button>
              </el-dropdown-item>
              <el-dropdown-item v-if="scope.row.enable" type="text" size="small" @click.native="render(scope.row.id)">
                <el-button size="small" type="text">提交审核</el-button>
              </el-dropdown-item>
              <el-dropdown-item v-if="scope.row.autoEnable" type="text" size="small" @click.native="updateWebShow(scope.row.id, scope.row.webShow)">
                <el-button size="small" type="text">{{ !scope.row.webShow ? '上架' : '下架' }}</el-button>
              </el-dropdown-item>
              <el-dropdown-item v-if="scope.row.recallEnable" type="text" size="small" @click.native="recallHandle(scope.row.id)">
                <el-button size="small" type="text">撤回</el-button>
              </el-dropdown-item>
              <el-dropdown-item
                  v-if="scope.row.auditStatus === 'act_audit_success'"
                  type="text"
                  size="small"
                  @click.native="activityApplyHandle(scope.row.id, scope.row.applyPublicFormSnapshotId)">
                <el-button size="small" type="text">报名管理</el-button>
              </el-dropdown-item>
              <el-dropdown-item v-if=" isAuth('activity:apply:import') && scope.row.activityStatus === 'act_finish' && scope.row.auditStatus === 'act_audit_success'" @click.native="memberImportHandle(scope.row.id)">
                <el-button size="small" type="text">报名导入</el-button>
              </el-dropdown-item>
              <el-dropdown-item v-if="isAuth('activity:updateTop')" @click.native="updateTop(scope.row.id,scope.row.top)">
                <el-button size="small" type="text">{{ !scope.row.top ? '置顶' : '取消置顶' }}</el-button>
              </el-dropdown-item>
              <el-dropdown-item v-if=" isAuth('activity:sync') && scope.row.enableSync === true" type="text" size="small" @click.native="syncOne(scope.row.id)">
                <el-button size="small" type="text">同步</el-button>
              </el-dropdown-item>
              <el-dropdown-item v-if=" isAuth('activity:send-tag-message') && scope.row.auditStatus === 'act_audit_success'" type="text" size="small" @click.native="sendTagMessage(scope.row.id)">
                <el-button size="small" type="text">同领域用户推送</el-button>
              </el-dropdown-item>
              <el-dropdown-item v-if=" isAuth('zyz:activity:sign_distance_change') && scope.row.checkLocation === true" type="text" size="small" @click.native="changeSignDistance(scope.row.id, scope.row.locationDistance)">
                <el-button size="small" type="text">更改签到距离</el-button>
              </el-dropdown-item>
              <el-dropdown-item @click.native="showQrCode(scope.row)">
                <el-button size="small" type="text" v-if="scope.row.auditStatus === 'act_audit_success'">二维码</el-button>
              </el-dropdown-item>
              <el-dropdown-item @click.native="showApplyLink(scope.row)">
                <el-button size="small" type="text" v-if="scope.row.auditStatus === 'act_audit_success'">报名链接</el-button>
              </el-dropdown-item>
                <el-dropdown-item @click.native="zsqDockingRecords(scope.row.id)">
                <el-button type="text" size="small" v-if="scope.row.dockingZSQ">知社区对接记录</el-button>
                </el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination @size-change="sizeChangeHandle" @current-change="currentChangeHandle" :current-page="pageIndex" :page-sizes="[10, 20, 50, 100]" :page-size="pageSize" :total="totalPage" layout="total, sizes, prev, pager, next, jumper"/>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"/>
    <!-- 弹窗, 新增 / 修改 -->
    <big-activity-add-or-update v-if="bigActivityAddOrUpdateVisible" ref="bigActivityAddOrUpdate" @refreshDataList="getDataList"/>
    <!-- 弹窗, 导入 -->
    <member-import v-if="memberImportVisible" ref="memberImport" @refreshDataList="getDataList"/>
    <!-- 弹窗, 对接类型 -->
    <activity-docking v-if="actDockingVisible" ref="actDocking" @refreshDataList="getDataList"/>
    <activity-detail v-if="activityDetailVisible" ref="activityDetail" @refreshDataList="getDataList"/>
    <apply-handle v-if="applyVisible" ref="activityApply" @refreshDataList="getDataList"/>
    <activity-time-range v-if="timeRangeVisible" ref="activityTimeRange" @refreshDataList="getDataList"/>
    <!-- 弹窗, 展示二维码 -->
    <el-dialog
        :title="'二维码展示'"
        :close-on-click-modal="false"
        min-width="40%"
        v-loading="qrCodeLoading"
        :visible.sync="qrCodeVisible">
      <div id="qrCodeDiv" ref="qrCodeDiv" style="display: flex; flex-direction: column; align-items: center;height: 80%;padding-bottom: 30px">
        <img :src="qrCodeUrl" style="width: 40%;margin: 50px 50px 20px 50px" crossOrigin="anonymous"/>
        <span style="font-size: 20px;font-weight: bold;white-space: pre-line">{{ qrCodeText }}</span>
      </div>
      <span slot="footer" class="dialog-footer">
          <el-button @click="qrCodeVisible = false">关闭</el-button>
          <el-button type="primary" @click="downloadQrCode()">下载二维码</el-button>
      </span>
    </el-dialog>
    <el-dialog
        :title="'报名链接'"
        :close-on-click-modal="false"
        min-width="40%"
        :visible.sync="applyLinkVisible">
      <div>
        <el-table :data="applyLink" border v-loading="applyLinkLoading" style="width: 100%;">
          <el-table-column header-align="center" align="center" min-width="300" label="报名链接" prop="applyLink"/>
          <el-table-column fixed="right" header-align="center" align="center" min-width="150" label="操作">
            <template slot-scope="scope">
              <el-button class="el-icon-document-copy" type="text" size="small" @click="copyHandle(scope.row.applyLink)">复制</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <span slot="footer" class="dialog-footer">
          <el-button @click="applyLinkVisible = false">关闭</el-button>
      </span>
    </el-dialog>
    <el-dialog
        :title="'签到距离修改'"
        :close-on-click-modal="false"
        width="30%"
        :visible.sync="distanceChangeVisible">
      <el-form :model="distanceForm" :rules="distanceRule" ref="distanceForm" label-width="100px">
        <el-form-item label="签到距离" prop="distance">
          <el-input-number style="width:100%" v-model="distanceForm.distance" controls-position="right" :min="0"/>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
          <el-button @click="distanceChangeVisible = false">取消</el-button>
          <el-button type="primary" @click="submitDistanceChange()">提交</el-button>
      </span>
    </el-dialog>
    <zsq-docking-records v-if="zsqDockingRecordsVisible" ref="zsqDockingRecords"></zsq-docking-records>
  </div>
</template>

<script>
import MemberImport from './activity-member-import'
import AddOrUpdate from './activity-add-or-update'
import BigActivityAddOrUpdate from './big-activity-add-or-update'
import ActivityDocking from './activity-docking.vue'
import ActivityDetail from './activity-detail.vue'
import ApplyHandle from './activity-apply.vue'
import ActivityTimeRange from './activity-time-range'
import moment from 'moment'
import html2canvas from 'html2canvas'
import Clipboard from "clipboard";
import {isAuth} from "@/utils";

import ZsqDockingRecords from './activity-zsq-docking-records'
export default {

  data() {
    return {
      memberImportVisible: false,
      actDockingVisible: false,
      activityDetailVisible: false,
      timeRangeVisible: false,
      applyVisible: false,
      zsqDockingRecordsVisible: false,
      actSyncByTimePeriod: false,
      qrCodeUrl: '',
      qrCodeText: '',
      dataForm: {
        key: '',
        open: null,
        auditStatus: null,
        timeRange: [],
        recruitTarget: null,
        publishTimeRange: [],
        publishOrgCodes: [],
        publishOrgCode: null,
        fieldIds: [],
        fieldId: null,
        top: null,
        scheduleDetailId: null
      },
      orgList: [],
      fields: [],
      dataList: [],
      pageIndex: 1,
      pageSize: 10,
      totalPage: 0,
      dataListLoading: false,
      fullscreenLoading: false,
      dataListSelections: [],
      addOrUpdateVisible: false,
      qrCodeVisible: false,
      qrCodeLoading: false,
      distanceChangeVisible: false,
      distanceForm: {
        actId: null,
        distance: 0
      },
      distanceRule: {
        distance: [
          {required: true, message: '签到距离不能为空', trigger: 'change'}
        ]
      },
      applyLinkLoading: false,
      applyLinkVisible: false,
      applyLink: [],
      teamGranted: false,
      bigActivityAddOrUpdateVisible: false,
      scheduleOptions: []
    }
  },
  components: {
    ZsqDockingRecords,
    AddOrUpdate,
    MemberImport,
    ActivityDetail,
    ActivityDocking,
    ApplyHandle,
    ActivityTimeRange,
    BigActivityAddOrUpdate
  },
  activated() {
    this.getOrg()
    this.queryPage()
    this.getFields()
    this.getActSyncByTimePeriod()
    this.validateTeamGranted()
    this.getScheduleData()
  },
  methods: {
    isAuth,
    handleCommand(command) {
      command()
    },
    getActSyncByTimePeriod() {
      this.$http({
        url: this.$http.adornUrl('/admin/zyz/activity/getActSyncByTimePeriod'),
        method: 'get'
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.actSyncByTimePeriod = data.obj
        } else {
          this.$message.error(data.msg)
        }
      })
    },
    validateTeamGranted() {
      if (isAuth('activity:big:apply')) {
        this.$http({
          url: this.$http.adornUrl('/admin/zyz/team/grantBigActivity/validateGranted'),
          method: 'get'
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.teamGranted = true
          } else {
            this.teamGranted = false
          }
        })
      }
    },
    queryPage() {
      this.pageIndex = 1
      this.getDataList()
    },
    // 获取数据列表
    getDataList() {
      this.dataListLoading = true
      this.$http({
        url: this.$http.adornUrl('/admin/zyz/activity/pagesForActivity'),
        method: 'post',
        data: this.$http.adornData({
          'currentPage': this.pageIndex,
          'pageSize': this.pageSize,
          'key': this.dataForm.key,
          'open': this.dataForm.open,
          'auditStatus': this.dataForm.auditStatus,
          'publishOrgCode': this.dataForm.publishOrgCode,
          'startTime': this.dataForm.timeRange && this.dataForm.timeRange.length === 2 ? this.dataForm.timeRange[0] + ' 00:00:00' : null,
          'endTime': this.dataForm.timeRange && this.dataForm.timeRange.length === 2 ? this.dataForm.timeRange[1] + ' 23:59:59' : null,
          'submitStartTime': this.dataForm.publishTimeRange != null ? this.dataForm.publishTimeRange[0] : null,
          'submitEndTime': this.dataForm.publishTimeRange != null ? this.dataForm.publishTimeRange[1] : null,
          'fieldId': this.dataForm.fieldId,
          'recruitTarget': this.dataForm.recruitTarget,
          'top': this.dataForm.top,
          'scheduleDetailId': this.dataForm.scheduleDetailId
        })
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.dataList = data.obj.records
          this.dataList.forEach(item => {
            // 判断是否为本组织创建
            let own = (item.orgSelf && item.teamPublish && this.$store.state.user.managerCapacity === 'TEAM_ADMIN' && item.teamId === this.$store.state.user.teamId) ||
                ((!(item.orgSelf && item.teamPublish)) && this.$store.state.user.managerCapacity !== 'TEAM_ADMIN' && item.publishOrgCode === this.$store.state.user.orgCode)
            // 判断草稿,驳回状态的操作
            item.enable = own && (item.auditStatus === 'act_draft' || item.auditStatus === 'act_reject')
            // 判断撤回
            item.recallEnable = own && item.auditStatus === 'act_wait_audit'
            // 判断上下架
            item.autoEnable = item.auditStatus === 'act_audit_success'
            // 判断报名管理
            item.autoEnable = item.auditStatus === 'act_audit_success'
            // 处理时间段
            item.timeRange = moment(item.startTime).format('YYYY-MM-DD') + '--' + moment(item.endTime).format('YYYY-MM-DD')
          })
          this.totalPage = data.obj.total
        } else {
          this.dataList = []
          this.totalPage = 0
        }
        this.dataListLoading = false
      })
    },
    activityDetailHandler(activityId) {
      this.activityDetailVisible = true
      this.$nextTick(() => {
        this.$refs.activityDetail.init(activityId)
      })
    },
    showTimeRange(activityId) {
      this.timeRangeVisible = true
      this.$nextTick(() => {
        this.$refs.activityTimeRange.init(activityId, this.actSyncByTimePeriod)
      })
    },
    handleChange(value, type) {
      if (type === 'field') {
        this.dataForm.fieldId = value[value.length - 1]
      }
      if (type === 'org') {
        this.dataForm.publishOrgCode = value && value.length > 0 ? value[value.length - 1] : null
      }
    },
    preview(id) {
      window.open(`${window.SITE_CONFIG['pcSiteBaseUrl']}/#/activity/detail?code=HPH_VS_AR&id=${id}&mode=preview`, '_blank');
    },
    //置顶
    updateTop(id,top){
      this.$confirm(`确定进行[${!top ? '置顶' : '取消置顶'}]操作?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        closeOnClickModal: false
      }).then(() => {
        this.$http({
          url: this.$http.adornUrl('/admin/zyz/activity/updateTopStatus'),
          method: 'get',
          params: this.$http.adornParams({
            'id': id
          })
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.$message({
              message: '操作成功',
              type: 'success',
              duration: 1500,
              onClose: () => {
                this.getDataList()
              }
            })
          } else {
            this.$message.error(data.msg)
          }
        })
      }).catch(() => {
      })
    },
    actDockingHandler(row) {
      this.actDockingVisible = true
      this.$nextTick(() => {
        this.$refs.actDocking.init(row)
      })
    },
    // 重置
    resetForm() {
      this.$refs['dataForm']?.resetFields()
      this.dataForm.fieldId = null
      this.dataForm.fieldIds = []
      this.dataForm.publishOrgCode = null
      this.dataForm.activityStatus = null
      this.dataForm.scheduleDetailId = null
      this.getDataList()
    },
    memberImportHandle(activityId) {
      this.memberImportVisible = true
      this.$nextTick(() => {
        this.$refs.memberImport.init(activityId)
      })
    },
    activityApplyHandle(activityId, applyPublicFormSnapshotId) {
      this.applyVisible = true
      this.$nextTick(() => {
        this.$refs.activityApply.init(activityId, null, applyPublicFormSnapshotId)
      })
    },
    // 每页数
    sizeChangeHandle(val) {
      this.pageSize = val
      this.pageIndex = 1
      this.getDataList()
    },
    // 当前页
    currentChangeHandle(val) {
      this.pageIndex = val
      this.getDataList()
    },
    // 多选
    selectionChangeHandle(val) {
      this.dataListSelections = val
    },
    // 新增 / 修改
    addOrUpdateHandle(id, isCopy, isRecordAdd) {
      this.addOrUpdateVisible = true
      this.$nextTick(() => {
        this.$refs.addOrUpdate.init(id, isCopy, isRecordAdd)
      })
    },
    // 新增 / 修改
    bigActivityAddOrUpdateHandle(id, isCopy) {
      this.bigActivityAddOrUpdateVisible = true
      this.$nextTick(() => {
        this.$refs.bigActivityAddOrUpdate.init(id, isCopy)
      })
    },
    // 展示二维码
    showQrCode(activity) {
      let activityId = activity.id
      this.qrCodeVisible = true
      // 调用接口生成
      if (!activity.qrCodeUrl || activity.qrCodeUrl === '') {
        this.qrCodeLoading = true
        this.$http({
          url: this.$http.adornUrl('/admin/zyz/activity/createQrCode'),
          method: 'get',
          params: this.$http.adornParams({
            'activityId': activityId,
            'appId': window.SITE_CONFIG['wechatAppID']
          })
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.qrCodeUrl = this.$http.adornAttachmentUrl(data.obj)
            this.qrCodeText = `活动名称：${activity.name} \n
                         活动时间：${moment(activity.startTime).format('YYYY-MM-DD')} 至 ${moment(activity.endTime).format('YYYY-MM-DD')}`
            this.qrCodeLoading = false
          } else {
            this.$message.error(data.msg)
            this.qrCodeText = `活动名称：${activity.name} \n
                         活动时间：${moment(activity.startTime).format('YYYY-MM-DD')} 至 ${moment(activity.endTime).format('YYYY-MM-DD')}`
            this.qrCodeLoading = false
          }
        }).catch(() => {
          this.qrCodeLoading = false
        })
      } else {
        this.qrCodeUrl = this.$http.adornAttachmentUrl(activity.qrCodeUrl)
        this.qrCodeText = `活动名称：${activity.name} \n
                         活动时间：${moment(activity.startTime).format('YYYY-MM-DD')} 至 ${moment(activity.endTime).format('YYYY-MM-DD')}`
      }
    },
    // 下载二维码
    downloadQrCode() {
      html2canvas(document.querySelector('#qrCodeDiv'), {useCORS: true}).then(canvas => {
        let dataUrl = canvas.toDataURL("image/png")
        console.info(dataUrl)
        if (dataUrl) {
          let a = document.createElement('a')
          a.download = '二维码.png'
          a.href = dataUrl
          a.click()
        }
      })
    },
    showApplyLink(activity) {
      let activityId = activity.id
      this.applyLinkVisible = true
      // 调用接口生成
      if (!activity.applyLinkUrl || activity.applyLinkUrl === '') {
        this.applyLinkLoading = true
        this.$http({
          url: this.$http.adornUrl('/admin/zyz/activity/createApplyLink'),
          method: 'get',
          params: this.$http.adornParams({
            'activityId': activityId,
            'appId': window.SITE_CONFIG['wechatAppID']
          })
        }).then(({data}) => {
          if (data && data.code === 0) {
            let al = {applyLink: null}
            al.applyLink = data.obj
            this.applyLink = [al]
            this.applyLinkLoading = false
          } else {
            this.$message.error(data.msg)
            this.applyLink = []
            this.applyLinkLoading = false
          }
        }).catch(() => {
          this.applyLink = []
          this.applyLinkLoading = false
        })
      } else {
        let al = {applyLink: activity.applyLinkUrl}
        this.applyLink = [al]
      }
    },
    copyHandle(applyLink) {
      var clipboard = new Clipboard('.el-icon-document-copy', {
        text: function () {
          return applyLink
        }
      })
      clipboard.on('success', e => {
        this.$message.success({
          message: '复制成功'
        })
        clipboard.destroy()
      })
      clipboard.on('error', e => {
        this.$message.warning({
          message: '该浏览器不支持自动复制'
        })
        clipboard.destroy()
      })
    },
    //获取所属区域
    getOrg() {
      this.$http({
        url: this.$http.adornUrl(`/admin/org/getOrgTree`),
        method: 'get',
        params: this.$http.adornParams()
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.orgList = this.getTreeData(data.obj)
        } else {
          this.orgList = []
        }
      })
    },
    getFields() {
      this.$http({
        url: this.$http.adornUrl('/admin/zyz/platform/belongFieldDict/cascade'),
        method: 'get'
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.fields = this.getTreeData(data.obj)
        } else {
          this.fields = []
        }
      })
    },
    // *处理点位分类最后children数组为空的状态
    getTreeData: function (data) {
      var that = this
      // 循环遍历json数据
      data.forEach(function (e) {
        if (!e.children || e.children.length < 1) {
          e.children = undefined
        } else {
          // children若不为空数组，则继续 递归调用 本方法
          that.getTreeData(e.children)
        }
      })
      return data
    },
    //导出
    exportActivity() {
      this.fullscreenLoading = true;
      this.$http({
        url: this.$http.adornUrl('/admin/zyz/activity/exportActivity'),
        method: 'post',
        responseType: 'arraybuffer',
        data: this.$http.adornData({
          'key': this.dataForm.key,
          'open': this.dataForm.open,
          'auditStatus': this.dataForm.auditStatus,
          'publishOrgCode': this.dataForm.publishOrgCode,
          'startTime': this.dataForm.timeRange && this.dataForm.timeRange.length === 2 ? this.dataForm.timeRange[0] + ' 00:00:00' : null,
          'endTime': this.dataForm.timeRange && this.dataForm.timeRange.length === 2 ? this.dataForm.timeRange[1] + ' 23:59:59' : null,
          'submitStartTime': this.dataForm.publishTimeRange != null ? this.dataForm.publishTimeRange[0] : null,
          'submitEndTime': this.dataForm.publishTimeRange != null ? this.dataForm.publishTimeRange[1] : null,
          'fieldId': this.dataForm.fieldId,
          'top': this.dataForm.top
        })
      }).then(({data}) => {
        if (data.code && data.code !== 0) {
          this.$message.error('导出失败')
        } else {
          this.fullscreenLoading = false
          let blob = new Blob([data], {type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8'})
          let objectUrl = URL.createObjectURL(blob)
          let a = document.createElement('a')
          // window.location.href = objectUrl
          a.href = objectUrl
          a.download = '活动明细.xlsx'
          a.click()
          URL.revokeObjectURL(objectUrl)
          this.$message({
            type: 'success',
            message: '导出成功'
          })
        }
      })
    },
    // 撤回
    recallHandle(id) {
      this.$confirm(`确定进行撤回操作?撤回后将变为草稿状态`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        closeOnClickModal: false
      }).then(() => {
        this.$http({
          url: this.$http.adornUrl('/admin/zyz/activity/recall'),
          method: 'get',
          params: this.$http.adornParams({
            'id': id
          })
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.$message({
              message: '操作成功',
              type: 'success',
              duration: 1500,
              onClose: () => {
                this.getDataList()
              }
            })
          } else {
            this.$message.error(data.msg)
          }
        })
      })
    },  // 上架
    updateWebShow(id, open) {
      this.$confirm(`确定进行[${!open ? '上架' : '下架'}]操作?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        closeOnClickModal: false
      }).then(() => {
        this.$http({
          url: this.$http.adornUrl('/admin/zyz/activity/updateWebShow'),
          method: 'get',
          params: this.$http.adornParams({
            'id': id
          })
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.$message({
              message: '操作成功',
              type: 'success',
              duration: 1500,
              onClose: () => {
                this.getDataList()
              }
            })
          } else {
            this.$message.error(data.msg)
          }
        })
      }).catch(() => {
      })
    },
    // 提交
    render(id) {
      this.$confirm(`确定进行提交操作?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        closeOnClickModal: false
      }).then(() => {
        this.$http({
          url: this.$http.adornUrl('/admin/zyz/activity/renderById'),
          method: 'get',
          params: this.$http.adornParams({
            'id': id
          })
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.$message({
              message: '操作成功',
              type: 'success',
              duration: 1500,
              onClose: () => {
                this.getDataList()
              }
            })
          } else {
            this.$message.error(data.msg)
          }
        })
      })
    },
    // 删除
    deleteHandle(id) {
      var ids = id ? [id] : this.dataListSelections.map(item => {
        return item.id
      })
      this.$confirm(`确定进行[${id ? '删除' : '批量删除'}]操作?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        closeOnClickModal: false
      }).then(() => {
        this.$http({
          url: this.$http.adornUrl('/admin/zyz/activity/removeByIds'),
          method: 'get',
          params: this.$http.adornParams({
            'ids': ids.join(',')
          })
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.$message({
              message: '操作成功',
              type: 'success',
              duration: 1500,
              onClose: () => {
                this.getDataList()
              }
            })
          } else {
            this.$message.error(data.msg)
          }
        })
      })
    },
    syncOne(id) {
      this.$confirm(`确定进行同步?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        closeOnClickModal: false
      }).then(() => {
        this.dataListLoading = true
        this.$http({
          url: this.$http.adornUrl('/admin/zyz/activity/syncOne'),
          method: 'get',
          params: this.$http.adornParams({'id': id})
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.$message({
              message: '请求活动同步成功，市平台返回：（' + data.obj.message + '）',
              type: data.obj.resultCode === 'SUCCESS' ? 'success' : 'warning',
              duration: 3000
            })
          } else {
            this.$message.error(data.msg)
          }
        })
      })
    },
    sendTagMessage(id) {
      this.$confirm(`确定进行给本活动领域活跃志愿者推送推荐参与活动消息吗?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        closeOnClickModal: false
      }).then(() => {
        this.dataListLoading = true
        this.$http({
          url: this.$http.adornUrl('/admin/zyz/activity/sendTagMessage'),
          method: 'get',
          params: this.$http.adornParams({'id': id})
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.$message({
              message: '推送成功',
              type: 'success',
              duration: 3000
            })
          } else {
            this.$message.error(data.msg)
          }
        })
        this.dataListLoading = false
      })
    },
    changeSignDistance(actId, distance) {
      this.distanceChangeVisible = true
      this.distanceForm.actId = actId
      this.distanceForm.distance = distance
    },
    submitDistanceChange () {
      this.$refs.distanceForm.validate((valid) => {
        if(valid) {
          this.$http({
            url: this.$http.adornUrl('/admin/zyz/activity/changeLocationDistance'),
            method: 'get',
            params: this.$http.adornParams({
              'actId': this.distanceForm.actId,
              'distance': this.distanceForm.distance
            })
          }).then(({data}) => {
            if (data && data.code === 0) {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 1500,
                onClose: () => {
                  this.distanceChangeVisible = false
                  this.getDataList()
                }
              })
            } else {
              this.$message.error(data.msg)
            }
          })
        }
      })
    },
    webShowBatchOn() {
      let validate = true
      _.forEach(this.dataListSelections, item => {
        if (item.webShow) {
          validate = false
        }
      })
      if (!validate) {
        this.$message.error('批量选中的数据里存在已经上架的，请重新选择数据！')
        return
      }
      let ids = this.dataListSelections.map(item => {return item.id})
      this.$confirm(`确定进行批量上架操作?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        closeOnClickModal: false
      }).then(() => {
        this.$http({
          url: this.$http.adornUrl('/admin/zyz/activity/updateWebShowBatch'),
          method: 'get',
          params: this.$http.adornParams({'ids': ids.join(',')})
        }).then(({data}) => {
          if (data.code !== 0) {
            return this.$message.error(data.msg)
          } else {
            this.$message({
              message: '操作成功',
              type: 'success',
              duration: 1000,
            })
            this.getDataList()
          }
        }).catch(() => {
        })
      }).catch(() => {
      })
    },
    webShowBatchOff() {
      let validate = true
      _.forEach(this.dataListSelections, item => {
        if (!item.webShow) {
          validate = false
        }
      })
      if (!validate) {
        this.$message.error('批量选中的数据里存在已经下架的，请重新选择数据！')
        return
      }
      let ids = this.dataListSelections.map(item => {return item.id})
      this.$confirm(`确定进行批量下架操作?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        closeOnClickModal: false
      }).then(() => {
        this.$http({
          url: this.$http.adornUrl('/admin/zyz/activity/updateWebShowBatch'),
          method: 'get',
          params: this.$http.adornParams({'ids': ids.join(',')})
        }).then(({data}) => {
          if (data.code !== 0) {
            return this.$message.error(data.msg)
          } else {
            this.$message({
              message: '操作成功',
              type: 'success',
              duration: 1000,
            })
            this.getDataList()
          }
        }).catch(() => {
        })
      }).catch(() => {
      })
    },
    zsqDockingRecords(id) {
        this.zsqDockingRecordsVisible = true
        this.$nextTick(() => {
            this.$refs.zsqDockingRecords.init(id)
        })
    },
    getScheduleData() {
      this.$http({
        url: this.$http.adornUrl('/admin/zyz/activity/schedule/getCascaderData'),
        method: 'get',
        params: this.$http.adornParams()
      }).then(({data}) => {
        if (data && data.code === 0) {
          let scheduleData = data.obj || []
          // 确保所有选项都可选择
          const enableAllOptions = (data) => {
            if (!data) return
            
            data.forEach(item => {
              item.disabled = false
              if (item.children && item.children.length > 0) {
                enableAllOptions(item.children)
              }
            })
          }
          
          enableAllOptions(scheduleData)
          this.scheduleOptions = scheduleData
        } else {
          this.scheduleOptions = []
        }
      })
    }
  }
}
</script>
