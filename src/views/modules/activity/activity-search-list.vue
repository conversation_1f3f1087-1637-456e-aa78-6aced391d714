<template>
    <el-dialog title="活动查询" :close-on-click-modal="false" append-to-body width="80%" :visible.sync="visible">
        <el-form :model="dataForm" ref="dataForm"
                 label-width="110px">
            <el-form-item label="关键字" prop="key">
                <el-input v-model="dataForm.key" placeholder="活动名称/联系人/联系方式/活动地址/活动主体"
                          clearable></el-input>
            </el-form-item>
            <el-form-item label="活动时间" prop="timeRange">
                <el-date-picker v-model="dataForm.timeRange" clearable type="daterange" value-format="yyyy-MM-dd"
                                align="right" start-placeholder="开始时间" end-placeholder="结束时间">
                </el-date-picker>
            </el-form-item>
            <el-form-item>
                <el-button icon="el-icon-search" type="warning" @click="queryPage()">查询</el-button>
            </el-form-item>
        </el-form>
        <el-table :data="dataList" border v-loading="dataListLoading" @selection-change="selectionChangeHandle"
                  @current-change="handleCurrentChange"
                  style="width: 100%;">
            <el-table-column type="selection" header-align="center" align="center" width="50"></el-table-column>
            <el-table-column prop="name" header-align="center" align="center" min-width="300" show-overflow-tooltip
                             label="活动名称">
            </el-table-column>
            <el-table-column prop="auditStatusText" header-align="center" align="center" min-width="160"
                             label="活动审核状态"/>
            <el-table-column prop="timeRange" header-align="center" align="center" min-width="180" label="活动起止时间">
            </el-table-column>
            <el-table-column prop="activityStatusText" header-align="center" align="center" label="活动状态"/>
            <!--      <el-table-column prop="actTypeText" header-align="center" align="center" min-width="160" label="活动类型"/>-->
            <el-table-column prop="recruitTargetsText" header-align="center" align="center" min-width="160"
                             label="招募对象"/>
            <el-table-column prop="publishOrgName" header-align="center" align="center" min-width="150"
                             show-overflow-tooltip label="发布单位"/>
            <el-table-column prop="contactPerson" header-align="center" align="center" min-width="140" label="联系人"/>
            <el-table-column prop="contactPhone" header-align="center" align="center" min-width="160" label="联系电话"/>
            <el-table-column prop="submitTime" header-align="center" align="center" min-width="160" label="提交时间"/>
        </el-table>
        <el-pagination @size-change="sizeChangeHandle" @current-change="currentChangeHandle" :current-page="pageIndex"
                       :page-sizes="[10, 20, 50, 100]" :page-size="pageSize" :total="totalPage"
                       layout="total, sizes, prev, pager, next, jumper"/>
        <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="selectActivity()">确定</el-button>
    </span>
    </el-dialog>
</template>

<script>
import moment from 'moment'
import _ from 'lodash'

export default {
    data() {
        return {
            visible: false,
            index: 0,
            dataList: [],
            pageIndex: 1,
            pageSize: 10,
            totalPage: 0,
            dataListLoading: false,
            dataForm: {},
            initForm: {},
            currentSelect: {},
            dataListSelections: [],

        }
    },
    components: {
        moment
    },
    methods: {
        init(index) {
            this.dataForm = _.cloneDeep(this.initForm)
            this.visible = true
            this.index = index
            this.queryPage()
        },
        queryPage() {
            this.pageIndex = 1
            this.getDataList()
        },
        // 获取数据列表
        getDataList() {
            this.dataListLoading = true
            this.$http({
                url: this.$http.adornUrl('/admin/zyz/activity/pagesForActivity'),
                method: 'post',
                data: this.$http.adornData({
                    'currentPage': this.pageIndex,
                    'pageSize': this.pageSize,
                    'key': this.dataForm.key,
                    'startTime': this.dataForm.timeRange && this.dataForm.timeRange.length === 2 ? this.dataForm.timeRange[0] + ' 00:00:00' : null,
                    'endTime': this.dataForm.timeRange && this.dataForm.timeRange.length === 2 ? this.dataForm.timeRange[1] + ' 23:59:59' : null,
                })
            }).then(({data}) => {
                if (data && data.code === 0) {
                    this.dataList = data.obj.records
                    this.dataList.forEach(item => {
                        // 处理时间段
                        item.timeRange = moment(item.startTime).format('YYYY-MM-DD HH:mm') + '-' + moment(item.endTime).format('HH:mm')
                    })
                    this.totalPage = data.obj.total
                } else {
                    this.dataList = []
                    this.totalPage = 0
                }
                this.dataListLoading = false
            })
        },
        // 每页数
        sizeChangeHandle(val) {
            this.pageSize = val
            this.pageIndex = 1
            this.getDataList()
        },
        // 当前页
        currentChangeHandle(val) {
            this.pageIndex = val
            this.getDataList()
        },
        // 多选
        selectionChangeHandle(val) {
            this.dataListSelections = val
        },
        handleCurrentChange(item) {
            this.currentSelect = item
        },
        selectActivity() {
            if (this.dataListSelections.length === 0) {
                this.$message.warning('未选中活动记录！')
                return
            }
            if (this.dataListSelections.length > 1) {
                this.$message.warning('只能选择一条活动记录！')
                return
            }
            this.visible = false
            this.$emit('getActivity', this.dataListSelections[0], this.index)
        },
    }
}
</script>


