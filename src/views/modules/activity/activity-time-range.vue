<template>
  <el-dialog :title="'活动时段管理'" :close-on-click-modal="false" width="80%" :visible.sync="visible" append-to-body>
    <span style="color: red">招募人数{{activityInfo.recruitNumDistinguish ? '分别' : '统一'}}控制</span>
    <el-table :data="dataList" border v-loading="dataListLoading" style="width: 100%; margin-top: 20px">
      <el-table-column prop="timePeriodRange" header-align="center" min-width="180" align="center" label="时段">
      </el-table-column>
      <el-table-column prop="applyEndTime" header-align="center" min-width="130" align="center" label="报名截止时间">
      </el-table-column>
      <el-table-column prop="signTime" header-align="center" min-width="130" align="center" label="签到时间">
      </el-table-column>
      <el-table-column v-if="!activityInfo.recruitNumDistinguish" prop="planDesc" header-align="center" min-width="130" align="center" label="（已/计划）招募人数">
      </el-table-column>
      <el-table-column v-if="activityInfo.recruitNumDistinguish" header-align="center" align="center" label="（已/计划）招募人数">
        <el-table-column prop="volunteerPlanDesc" header-align="center" min-width="100" align="center" label="志愿招募">
        </el-table-column>
        <el-table-column prop="massesPlanDesc" header-align="center" min-width="100" align="center" label="群众参与">
        </el-table-column>
      </el-table-column>
      <el-table-column prop="planDesc" header-align="center" min-width="100" align="center" label="是否展示">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.webShow === false" size="small" type="danger">下架</el-tag>
          <el-tag v-else size="small">上架</el-tag>
        </template>
      </el-table-column>
      <el-table-column
          prop="syncText"
          header-align="center"
          align="center"
          min-width="150"
          v-if="actSyncByTimePeriod"
          label="活动按时间段同步状态">
        <template slot-scope="scope">
          <el-popover trigger="hover" placement="top">
            <p style="text-align: center">
              {{
                '同步时间：' + (scope.row.syncTime ? scope.row.syncTime : '')
              }}<br>{{ scope.row.syncRemark }}</p>
            <div slot="reference" class="name-wrapper">
              <el-tag v-if="scope.row.sync === 'sync_failure'" type="danger">{{ scope.row.syncText }}</el-tag>
              <el-tag v-if="scope.row.sync === 'sync_wait'" type="warning">{{ scope.row.syncText }}</el-tag>
              <el-tag v-if="scope.row.sync === 'sync_success'" type="success">{{ scope.row.syncText }}</el-tag>
            </div>
          </el-popover>
        </template>
      </el-table-column>
      <el-table-column
          prop="recruitSyncText"
          header-align="center"
          align="center"
          min-width="150"
          label="招募同步状态">
        <template slot-scope="scope">
          <el-popover trigger="hover" placement="top">
            <p style="text-align: center">
              {{
                '同步时间：' + (scope.row.recruitSyncTime ? scope.row.recruitSyncTime : '')
              }}<br>{{ scope.row.recruitSyncRemark }}</p>
            <div slot="reference" class="name-wrapper">
              <el-tag v-if="scope.row.recruitSync === 'sync_failure'" type="danger">{{ scope.row.recruitSyncText }}</el-tag>
              <el-tag v-if="scope.row.recruitSync === 'sync_wait'" type="warning">{{ scope.row.recruitSyncText }}</el-tag>
              <el-tag v-if="scope.row.recruitSync === 'sync_success'" type="success">{{ scope.row.recruitSyncText }}</el-tag>
            </div>
          </el-popover>
        </template>
      </el-table-column>
      <el-table-column fixed="right" header-align="center" align="center"
                       min-width="220" label="操作">
        <template slot-scope="scope">
          <el-button type="text" v-if="scope.row.webShow" size="small"
                     @click="changeWebShow(scope.row.id,scope.row.webShow)">下架
          </el-button>
          <el-button type="text" v-else size="small" @click="changeWebShow(scope.row.id)">上架
          </el-button>
          <el-button v-if="scope.row.enableSync"
                     type="text" size="small"
                     @click="sync(scope.row.id)">同步
          </el-button>
          <el-button v-if="scope.row.enableSync"
                     type="text" size="small"
                     @click="syncApply(scope.row.id)">报名同步
          </el-button>
          <el-button size="small" type="text"
                     v-if="activityInfo.auditStatus === 'act_audit_success'"
                     @click.native="activityApplyHandle(scope.row.activityId,scope.row.id)">
            报名管理
          </el-button>
          <el-button v-if="isAuth('activity:apply:import') && scope.row.isImportEnable"
                     @click="memberImportHandle(scope.row.activityId, scope.row.id)" size="small" type="text">报名导入
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <apply-handle v-if="applyVisible" ref="activityApply" @refreshDataList="handleCallback"></apply-handle>
    <member-import v-if="memberImportVisible" ref="memberImport" @refreshDataList="handleCallback"></member-import>
  </el-dialog>
</template>
<script>
import moment from 'moment'
import ApplyHandle from './activity-apply.vue'
import MemberImport from "@/views/modules/activity/activity-member-import";

export default {
  data() {
    return {
      visible: false,
      applyVisible: false,
      memberImportVisible: false,
      fullscreenLoading: false,
      actSyncByTimePeriod: false,
      dataForm: {
        activityId: null
      },
      activityInfo: {
        name: '',
        auditStatus: ''
      },
      dataList: [],
      pageIndex: 1,
      pageSize: 5,
      totalPage: 0,
      dataListLoading: false,
      dataListSelections: []
    }
  },
  components: {
    ApplyHandle,
    MemberImport
  },
  methods: {
    async init(activityId, actSyncByTimePeriod) {
      this.dataForm.activityId = activityId
      this.actSyncByTimePeriod = actSyncByTimePeriod
      this.visible = true
      await this.queryActivityInfo()
      this.queryPage()
    },
    handleCallback() {
      this.init(this.dataForm.activityId)
    },
    queryPage() {
      this.getDataList()
    },
    async queryActivityInfo() {
      await this.$http({
        url: this.$http.adornUrl(`/admin/zyz/activity`),
        method: 'get',
        params: this.$http.adornParams({id: this.dataForm.activityId})
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.activityInfo = data.obj
        }
      })
    },
    // 获取数据列表
    getDataList() {
      this.dataListLoading = true
      this.$http({
        url: this.$http.adornUrl('/admin/zyz/activity-time-period/getTimePeriodListByActivityId'),
        method: 'get',
        params: this.$http.adornParams({
          'activityId': this.dataForm.activityId
        })
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.dataList = data.obj
          this.dataList.forEach((it) => {
            it.timePeriodRange = it.startTime && it.endTime ?
                moment(it.startTime).format('YYYY-MM-DD HH:mm') + '-' + moment(it.endTime).format('HH:mm') : ''
            it.planDesc = it.applyNum + '/' + it.recruitmentNum
            it.volunteerPlanDesc = it.volunteerApplyNum + '/' + it.volunteerRecruitNum
            it.massesPlanDesc = it.massesApplyNum + '/' + it.massesRecruitNum
            let isEnd = moment().isAfter(moment(it.endTime))
            it.isImportEnable = isEnd && this.activityInfo.auditStatus === 'act_audit_success'
          })
        } else {
          this.dataList = []
        }
        this.dataListLoading = false
      })
    },
    memberImportHandle(activityId, timePeriodId) {
      this.memberImportVisible = true
      this.$nextTick(() => {
        this.$refs.memberImport.init(activityId, timePeriodId)
      })
    },
    activityApplyHandle(activityId, timePeriodId) {
      this.applyVisible = true
      this.$nextTick(() => {
        this.$refs.activityApply.init(activityId, timePeriodId)
      })
    },
    changeWebShow(id, webShow) {
      this.$confirm(`确定进行[${!webShow ? '上架' : '下架'}]操作?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        closeOnClickModal: false
      }).then(() => {
        this.$http({
          url: this.$http.adornUrl('/admin/zyz/activity-time-period/changeWebShow'),
          method: 'post',
          params: this.$http.adornParams({
            'id': id
          })
        }).then(({data}) => {
          if (data.code === 0) {
            this.$message.success('操作成功')
            this.getDataList()
          } else {
            this.$message.error(data.msg)
          }
        })
      })
    },
    sync(id) {
      this.$confirm(`该操作将${this.actSyncByTimePeriod ? '执行活动分时段同步以及活动分时段招募同步，' : '活动分时段招募同步，'}确定进行同步操作?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        closeOnClickModal: false
      }).then(() => {
        this.dataListLoading = true
        this.$http({
          url: this.$http.adornUrl('/admin/zyz/activity-time-period/sync'),
          method: 'get',
          params: this.$http.adornParams({
            'id': id
          })
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.$message({
              message: '同步成功',
              type: 'success',
              duration: 1500,
              onClose: () => {
                this.getDataList()
              }
            })
          } else {
            this.$message.error(data.msg)
            this.dataListLoading = false
          }
        })
      })
    },
    syncApply(id) {
      this.$confirm(`该操作将执行活动分时段报名同步及报名人员服务时长同步，确定进行同步操作?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        closeOnClickModal: false
      }).then(() => {
        this.dataListLoading = true
        this.$http({
          url: this.$http.adornUrl('/admin/zyz/activity-time-period/syncApplyByTimePeriodId'),
          method: 'get',
          params: this.$http.adornParams({'id': id})
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.$message({
              message: data.msg,
              duration: 1500,
              type: 'success',
            })
          } else {
            this.$message.error(data.msg)
          }
          this.dataListLoading = false
        })
      })
    }
  }
}
</script>

