<template>
    <el-dialog :title="!dataForm.id ? '新增' : '编辑'" :close-on-click-modal="false" :visible.sync="visible"
               width="60%">
        <el-form :model="dataForm" :rules="dataRule" ref="dataForm" label-width="150px">
            <el-row :gutter="20">
                <el-col :span="24">
                    <el-form-item label="活动时段" prop="activityName">
                        <el-input v-model="dataForm.activityName" placeholder="活动时段" disabled></el-input>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row :gutter="20">
                <el-col :span="24">
                    <el-form-item label="公示内容" prop="content">
                        <el-input type="textarea" v-model="dataForm.content" placeholder="公示内容" rows="8" maxlength="2000" show-word-limit/>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row :gutter="20">
                <el-col :span="24">
                    <el-form-item label="公示图片" prop="picsUrl" :error="picsUrlError">
                        <el-upload
                            list-type="picture-card"
                            :action="this.$http.adornUrl(`/admin/oss/upload`)"
                            :headers="myHeaders"
                            :data="{serverCode: this.serverCode,media:false}"
                            :show-file-list="true"
                            :multiple="true"
                            :file-list="fileList"
                            :on-preview="handlePictureCardPreview"
                            :on-remove="handleRemovePicture"
                            :on-success="function (res,file){return handleAvatarSuccess(res,file, 'pictureList')}"
                            :before-upload="beforeAvatarUpload">
                            <i class="el-icon-plus"></i>
                            <div slot="tip" class="el-upload__tip" style="color: red">需上传3-9张图片，文件格式jpg、bmp或png，大小不超2M</div>
                        </el-upload>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
			  <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary"  @click="dataFormSubmit(false)" :disabled="submitLoading">保存草稿</el-button>
      <el-button type="success"    @click="dataFormSubmit(true)" :disabled="submitLoading">提交审核</el-button>
    </span>
    </el-dialog>
</template>

<script>
import Vue from "vue";

export default {
    data() {
        return {
            visible: false,
            posed: false,
            submitLoading: false,
            pictureList: [],
            fileList: [],
            serverCode: 'LocalServer',
            myHeaders: {Authorization: Vue.cookie.get('Authorization')},
            initForm: {
                id: null,
                periodId:null,
                version: null,
                activityId: null,
                signTime: null,
                sync: null,
                syncTime: null,
                syncRemark: null,
                recruitSyncId: null,
                content: null,
                picList: null
            },
            dataForm: {},
            dataRule: {
                content: [
                    {required: true, message: '公示内容不能为空', trigger: 'blur'}
                ],
                picsUrl: [
                    {required: true, message: '公示图片不能为空', trigger: 'blur'}
                ]

            },
            picsUrlError: null
        }
    },
    components: {},
    methods: {
        async init(periodId, id) {
            this.dataForm = _.cloneDeep(this.initForm)
            this.dataForm.id = id || null
            this.dataForm.periodId = periodId || null
            this.visible = true
            this.pictureList = []
            this.fileList  = []
            this.$nextTick(() => {
                this.$refs['dataForm']?.resetFields()
                if (this.dataForm.periodId) {
                    this.$http({
                        url: this.$http.adornUrl(`/admin/zyz/activity/time/period/show/getDetail`),
                        method: 'get',
                        params: this.$http.adornParams({
                            periodId: this.dataForm.periodId,
                            id: this.dataForm.id
                        })
                    }).then(({data}) => {
                        if (data && data.code === 0) {
                            this.dataForm = data.obj
                            if(this.dataForm.picsUrl){
                                let picture = this.dataForm.picsUrl.split(',')
                                this.pictureList = picture
                                picture.forEach(item => {
                                    let obj = {
                                        url: this.$http.adornAttachmentUrl(item)
                                    }
                                    this.fileList.push(obj)
                                })
                            }

                        }
                    })
                }
            })
        },
        handlePictureCardPreview(file) {
            this.dialogImageUrl = file.url
            this.dialogVisible = true
        },
        handleAvatarSuccess(res, file, field) {
            if (res.success) {
                this.$message({
                    message: '操作成功',
                    type: 'success',
                    duration: 1500
                })
                this.pictureList.push(res.obj.path)
            } else {
                this.$message.error('上传失败')
            }
        },
        handleRemovePicture(file, fileList) {
            this.pictureList = []
            fileList.forEach(item => {
                console.log(item)
                try {
                    this.pictureList.push(item.url.slice(item.url.indexOf('vpath') - 1))
                } catch (e) {
                    this.pictureList.push(item.url)
                }
            })
        },
        beforeAvatarUpload: function (file) {
            var isJPG = file.type === 'image/jpeg'
            var isPNG = file.type === 'image/png'
            var isBMP = file.type === 'image/bmp'
            var isLt20M = file.size / 1024 / 1024 < 20

            if (!isJPG && !isPNG && !isBMP) {
                this.$message.error('上传图片只能是图片!')
            }
            if (!isLt20M) {
                this.$message.error('上传文件大小不能超过 20MB!')
            }
            return (isJPG || isPNG || isBMP) && isLt20M
        },
        // 表单提交
        dataFormSubmit(isRender) {
            this.dataForm.picsUrl = this.pictureList.join(',') || null
            if(this.pictureList.length<3 || this.pictureList.length>9){
                this[`picsUrlError`] = '至少需要上传3张图片'
                return
            }
            let method = ''
            this.$refs['dataForm'].validate((valid) => {
                if (valid) {
                    if (isRender) {
                        method = '/admin/zyz/activity/time/period/show/render'
                    } else {
                        method = `/admin/zyz/activity/time/period/show/saveOrUpdateToDraft`
                    }
                    if (isRender) {
                        this.$confirm(`确定进行提交操作?`, '提示',
                            {confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning', closeOnClickModal: false}
                        ).then(() => {
                            this.submitForm(method, this.dataForm)
                        })
                    } else {
                        this.submitForm(method, this.dataForm)
                    }

                }
            })
        },
        async submitForm(method, dataForm) {

            this.submitLoading = true
            this.$http({
                url: this.$http.adornUrl(method),
                method: 'post',
                data: this.$http.adornData(dataForm)
            }).then(({data}) => {
                if (data && data.code === 0) {
                    this.$message({
                        message: '操作成功',
                        type: 'success',
                        duration: 1500,
                        onClose: () => {
                            this.submitLoading = false
                            this.visible = false
                            this.$emit('refreshDataList')
                        }
                    })
                } else {
                    this.$message.error(data.msg)
                }
                this.submitLoading = false
            })
        },
    }
}
</script>
