<template>
  <el-dialog
    :title="!dataForm.id ? '新增' : '修改'"
    :close-on-click-modal="false"
    width="80%"
    :visible.sync="visible">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" label-width="130px">
      <el-row :gutter="20">
        <el-col :span="12">
           <el-form-item label="活动名称：" prop="name" :error="nameError">
                <el-input v-model="dataForm.name" placeholder="活动名称"></el-input>
            </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="排序:" prop="sequence" :error="sequenceError">
            <el-input-number v-model="dataForm.sequence" controls-position="right" :min="0"></el-input-number>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="大赛简介：" prop="remind" :error="remindError">
            <teditor style="width: 100%;" :value="dataForm.remind" :disabled="false" ref="teditor"
                     @changeEditorValue="changeAnswer"></teditor>
          </el-form-item>
        </el-col>
      </el-row>
<!--      <el-row :gutter="20">-->
<!--            <el-col :span="20">-->
<!--              <el-form-item label="大赛简介：" prop="remind">-->
<!--                <el-upload-->
<!--                    class="avatar-uploader editor-upload-img"-->
<!--                    :action="this.$http.adornUrl(`/admin/oss/upload`)"-->
<!--                    :headers="headers"-->
<!--                    :data="{serverCode: uploadOptions.serverCode, media: false}"-->
<!--                    name="file"-->
<!--                    :show-file-list="false"-->
<!--                    :on-success="uploadSuccess"-->
<!--                    :on-error="uploadError"-->
<!--                    :before-upload="beforeUpload">-->
<!--                </el-upload>-->
<!--                <quill-editor v-model="dataForm.remind"-->
<!--                              ref="myQuillEditor"-->
<!--                              :options="editorOption">-->
<!--                </quill-editor>-->
<!--              </el-form-item>-->
<!--        </el-col>-->
<!--      </el-row>-->
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import moment from 'moment'
import { quillEditor } from 'vue-quill-editor' // 调用编辑器
import editMixin from '@/mixins/edit-mixins'
import fileUploadMixin from '@/mixins/file-upload-mixins'
import AddFile from "../content/file-list";
import Teditor from '@/components/tinymce'



  export default {
    mixins: [editMixin, fileUploadMixin],
    data () {
      return {
        visible: false,
        // editorOption: {
        //   theme: 'snow', // or 'bubble'
        //   placeholder: '您想说点什么？',
        //   modules: {
        //     toolbar: {
        //       container: toolbarOptions,
        //       // container: "#toolbar",
        //       handlers: {
        //         image: function (value) {
        //           if (value) {
        //             // 触发input框选择图片文件
        //             document.querySelector('.avatar-uploader.editor-upload-img input').click()
        //           } else {
        //             this.quill.format('image', false)
        //           }
        //         }
        //       }
        //     }
        //   }
        // },
        dataForm: {
          id: null,
          version: null,
          name: '',
          sequence: 0,
          enable: '',
          picture: '',
          remind: ''
        },
        dataRule: {
          enable: [
            { required: true, message: '状态(1:启用，0：禁用)不能为空', trigger: 'blur' }
          ],
          picture: [
            { required: true, message: '背景图不能为空', trigger: 'blur' }
          ],
          remind: [
            { required: true, message: '大赛简介不能为空', trigger: 'blur' }
          ]
        },
        nameError: null,
        enableError: null,
        pictureError: null,
        remindError: null
      }
    },
    components: {
      Teditor,
      quillEditor,
      moment,
      AddFile
    },
    methods: {
      init (id) {
        this.dataForm.id = id || null
        this.visible = true
        this.$nextTick(() => {
          this.$refs['dataForm']?.resetFields()
          if (this.dataForm.id) {
            this.$http({
              url: this.$http.adornUrl(`/admin/race/activity`),
              method: 'get',
              params: this.$http.adornParams({ id: this.dataForm.id })
            }).then(({data}) => {
              if (data && data.code === 0) {
                this.dataForm = data.obj
              }
            })
          }
        })
      },
      changeAnswer(html) {
        this.dataForm.remind = html
      },
      // 上传图片成功
      handleAvatarSuccess (res, file, field) {
        if (res.success) {
          this.$message({
            message: '操作成功',
            type: 'success',
            duration: 1500
          })
          console.log(res)
          this.dataForm[`${field}`] = res.obj.path
        } else {
          this.$message.error('上传失败')
        }
      },
      beforeAvatarUpload: function (file) {
        var isJPG = file.type === 'image/jpeg'
        var isPNG = file.type === 'image/png'
        var isBMP = file.type === 'image/bmp'
        var isLt2M = file.size / 1024 / 1024 < 2

        if (!isJPG && !isPNG && !isBMP) {
          this.$message.error('上传图片只能是图片!')
        }
        if (!isLt2M) {
          this.$message.error('上传文件大小不能超过 2MB!')
        }
        return (isJPG || isPNG || isBMP) && isLt2M
      },
      uploadSuccess (res, file) {
        let quill = this.$refs.myQuillEditor.quill
        // 如果上传成功
        if (res.success) {
          // 获取光标所在位置
          let length = quill.getSelection().index
          // 插入图片  res.url为服务器返回的图片地址
          quill.insertEmbed(length, 'image', this.$http.adornUrl(res.obj.path))
          // 调整光标到最后
          quill.setSelection(length + 1)
        } else {
          this.$message.error('图片插入失败')
        }
        // loading动画消失
        this.quillUpdateImg = false
      },
      // 富文本图片上传前
      beforeUpload () {
        this.quillUpdateImg = true
      },
      // 富文本图片上传失败
      uploadError () {
        this.quillUpdateImg = false
        this.$message.error('图片插入失败')
      },
      // 表单提交
      dataFormSubmit () {
        this.$refs['dataForm'].validate((valid) => {
          if (valid) {
            this.$http({
              url: this.$http.adornUrl(`/admin/race/activity/${!this.dataForm.id ? 'save' : 'update'}`),
              method: 'post',
              data: this.$http.adornData(this.dataForm)
            }).then(({data}) => {
              if (data && data.code === 0) {
                this.$message({
                  message: '操作成功',
                  type: 'success',
                  duration: 1500,
                  onClose: () => {
                    this.visible = false
                    this.$emit('refreshDataList')
                  }
                })
              } else if (data && data.code === 303) {
                for (let it of data.obj) {
                  this[`${it.field}Error`] = it.message
                }
              } else {
                this.$message.error(data.msg)
              }
            })
          }
        })
      }
    }
  }
</script>
