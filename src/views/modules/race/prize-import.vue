<template>
  <el-dialog
    title="导入获奖作品"
    append-to-body
    :close-on-click-modal="false"
    :visible.sync="visible">
    <el-form :model="dataForm" ref="dataForm" label-width="120px">
      <el-form-item label="Excel文件:" prop="excel">
<!--        <el-upload-->
<!--          ref="upload"-->
<!--          name="excel"-->
<!--          :action="this.$http.adornUrl(`/admin/race/activity/prize/uploadExcel`)"-->
<!--          :data="{type: this.type}"-->
<!--          :headers="myHeaders"-->
<!--          :on-success="successHandle"-->
<!--          :on-change="changHandle"-->
<!--          :limit=1-->
<!--          accept="xlsx"-->
<!--          :http-request="uploadFile"-->
<!--          :on-exceed="handleExceed"-->
<!--          :before-upload="function (file){return docBeforeUpload(file)}"-->
<!--          :file-list="fileList"-->
<!--          :auto-upload="false">-->
<!--          <el-button slot="trigger" size="small" type="primary">选取文件</el-button>-->
<!--          <div slot="tip" class="el-upload__tip">只能上传xlsx文件，大小10M以内</div>-->
<!--        </el-upload>-->
        <el-upload
            ref="upload"
            drag
            name="excel"
            :action="this.$http.adornUrl(`/admin/race/activity/prize/uploadExcel`)"
            :headers="myHeaders"
            :data="{activityId: this.dataForm.activityId}"
            :on-success="successHandle"
            :on-change="changHandle"
            :limit=1
            :http-request="uploadFile"
            :on-exceed="handleExceed"
            :before-upload="function (file){return docBeforeUpload(file)}"
            :file-list="fileList"
            :auto-upload="false">
          <i class="el-icon-upload"></i>
        </el-upload>
        <span style="font-size: 13px; color: #cc0000">请确认数据奖项名称与设置一致</span>
      </el-form-item>
<!--      <el-form-item label="Excel模板:" prop="prodName">-->
<!--        <el-button type="text" @click="downloadTemplate()">-->
<!--          <i class="el-icon-download el-icon&#45;&#45;right"></i>-->
<!--          导入模板下载.xlsx-->
<!--        </el-button>-->
<!--      </el-form-item>-->
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
  import Vue from 'vue'
  import moment from 'moment'
  export default {
    data () {
      return {
        visible: false,

        myHeaders: {Authorization: Vue.cookie.get('Authorization')},
        questionFileName: '导入问题数据.xlsx',
        fileList: [],
        objectCodeList: [],
        failCodeList: [],
        dataForm: {
          activityId: null,
          excel: null
        }
      }
    },
    components: {
      moment
    },
    methods: {
      init (activityId) {
        this.visible = true
        this.dataForm.activityId = activityId
        this.fileList = []
        this.objectCodeList = []
        this.$nextTick(() => {
          this.$refs['dataForm']?.resetFields()
        })
      },
      // 表单提交
      dataFormSubmit () {
        if (this.fileList.length === 0) {
          this.$message.error('请选择excel文件')
        }
        this.$refs['dataForm'].validate((valid) => {
          if (valid) {
            this.$refs.upload.submit()
          }
        })
      },
      // 上传成功
      successHandle (response) {
        if (response && response.code === 0) {
          this.objectCodeList = response.obj['success']
          this.failCodeList = response.obj['fail']
          this.$message({
            message: '操作成功',
            type: 'success',
            onClose: () => {}
          })
          this.visible = false
          this.$emit('setDataList', this.type, this.objectCodeList, this.failCodeList)
        } else {
          this.$message.error(response.msg)
        }
        this.fileList = []
      },
      changHandle (file, fileList) {
        let FileExt = file.name.replace(/.+\./, '')
        const isLt10M = file.size / 1024 / 1024 < 10
        if (['xlsx', 'xls'].indexOf(FileExt.toLowerCase()) === -1) {
          this.$message({
            type: '上传失败',
            message: '请上传后缀名为xlsx或xls的文件！'
          })
          this.fileList = []
          return
        }
        if (!isLt10M) {
          this.$message.error('上传文件大小不能超过 10M!')
          this.fileList = []
          return
        }
        this.fileList = fileList
      },
      handleExceed () {
        this.$message.error('只能选择一个文件，如需替换请删除已选文件后重试')
      },
      docBeforeUpload (file) {
        let FileExt = file.name.replace(/.+\./, '')
        if (['xlsx', 'xls'].indexOf(FileExt.toLowerCase()) === -1) {
          this.$message({
            type: '上传失败',
            message: '请上传后缀名为xlsx, xls的附件！'
          })
          return false
        }
        const isLt10M = file.size / 1024 / 1024 < 10
        if (['xlsx', 'xls'].indexOf(FileExt.toLowerCase()) !== -1 && !isLt10M) {
          this.$message.error('附件大小不能超过 10M!')
          return false
        }
      },
      uploadFile (params) {
        let file = params.file
        let FileExt = file.name.replace(/.+\./, '')
        if (['xlsx', 'xls'].indexOf(FileExt.toLowerCase()) === -1) {
          this.$message({
            type: '上传失败',
            message: '请上传后缀名为xlsx, xls的附件！'
          })
          return false
        }
        const isLt50M = file.size / 1024 / 1024 < 50
        if (['xlsx', 'xls'].indexOf(FileExt.toLowerCase()) !== -1 && !isLt50M) {
          this.$message.error('附件大小不能超过 50M!')
          return false
        }
        this.$http({
          timeout: 600 * 1000,
          url: this.$http.adornUrl(`/admin/race/activity/prize/uploadExcel`),
          method: 'post',
          data: this.$http.adornData({
            'excel': file,
            'activityId': this.dataForm.activityId
          }, true, 'file'),
          responseType: 'arraybuffer'
        }).then(({data}) => {
          var enc = new TextDecoder('utf-8')
          var str = enc.decode(new Uint8Array(data))
          if (!str) {
            this.$alert('所有数据导入成功', '成功', {
              type: 'success',
              confirmButtonText: '确定'
            })
          }
          else if (str.substr(0, 1) === '{') {
            var jsonData = JSON.parse(str)
            this.$alert(jsonData.msg, '出错了', {
              type: 'warning',
              confirmButtonText: '确定'
            })
          } else {
            let blob = new Blob([data], {type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8'})
            let objectUrl = URL.createObjectURL(blob)
            let a = document.createElement('a')
            a.href = objectUrl
            a.download = this.questionFileName
            a.click()
            URL.revokeObjectURL(objectUrl)
            this.$alert('操作成功，部分数据导入失败，请查看浏览器自动下载的EXCEL。纠正后再次导入', '部分数据有错误', {
              type: 'warning',
              confirmButtonText: '确定'
            })
          }
          this.uploadLoading = false
          this.$refs.upload.clearFiles()
          this.visible = false
          this.$emit('refreshDataList')
        })
      }
    }
  }
</script>
