<template>
  <el-dialog
      :title="'驳回'"
      :close-on-click-modal="false"
      width="40%"
      append-to-body
      :visible.sync="visible">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" label-width="120px">
      <el-form-item v-if="!dataForm.pass" label="驳回理由" prop="remark">
        <el-input type="textarea" :autosize="{ minRows: 2}" v-model="dataForm.remark"
                  placeholder="驳回理由"></el-input>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="updateStatusFail()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import moment from 'moment'

export default {
  data() {
    return {
      visible: false,
      isShow: false,
      dataForm: {
        id: null,
        version: null,
        remark: null,

      },
      dataRule: {
        remark: [
          {required: true, message: '驳回理由不能为空', trigger: 'blur'}
        ]
      },
      remarksError: null
    }
  },
  components: {
    moment
  },
  methods: {
    init(id) {
      this.dataForm.id = id || null
      this.dataForm.remark = null
      this.visible = true
    },
    updateStatusFail() {
        this.$http({
          url: this.$http.adornUrl('/admin/race/photo/updateStatus'),
          method: 'get',
          params: this.$http.adornParams({
            'ids': this.dataForm.id,
            'status': false,
            'auditRemark': this.dataForm.remark
          })
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.$message({
              message: '操作成功',
              type: 'success',
              duration: 1500,
              onClose: () => {
                this.visible = false
                this.$emit('refreshDataList')
              }
            })
          } else {
            this.$message.error(data.msg)
          }
        })
    },
  }
}
</script>
