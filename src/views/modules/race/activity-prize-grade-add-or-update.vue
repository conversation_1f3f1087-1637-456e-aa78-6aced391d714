<template>
  <el-dialog
    :title="!dataForm.id ? '新增' : '修改'"
    :close-on-click-modal="false"
    :visible.sync="visible"
    width="80%"
    append-to-body>
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmit()" label-width="80px">
      <el-row :gutter="20">
        <el-col :span="12">
           <el-form-item label="奖项名称" prop="name" :error="nameError">
                <el-input v-model="dataForm.name" placeholder="奖项名称"></el-input>
            </el-form-item>
        </el-col>
            <el-col :span="12">
           <el-form-item label="排序" prop="sequence" :error="sequenceError">
                <el-input-number v-model="dataForm.sequence" controls-position="right" :min="0"></el-input-number>
            </el-form-item>
        </el-col>
      </el-row>
        </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
  export default {
    data () {
      return {
        visible: false,
        dataForm: {
          id: null,
          version: null,
          name: '',
          sequence: '',
          activityId: ''
        },
        dataRule: {
          sequence: [
            { required: true, message: '排序不能为空', trigger: 'blur' }
          ],
          activityId: [
            { required: true, message: '活动id不能为空', trigger: 'blur' }
          ]
        },
        nameError: null,
        sequenceError: null,
        activityIdError: null
      }
    },
    methods: {
      init (id, activityId) {
        this.dataForm.id = id || null
        this.dataForm.activityId = activityId || null
        this.visible = true
        this.$nextTick(() => {
          this.$refs['dataForm']?.resetFields()
          if (this.dataForm.id) {
            this.$http({
              url: this.$http.adornUrl(`/admin/race/activity/prize/grade`),
              method: 'get',
              params: this.$http.adornParams({ id: this.dataForm.id })
            }).then(({data}) => {
              if (data && data.code === 0) {
                this.dataForm = data.obj
              }
            })
          }
        })
      },
      // 表单提交
      dataFormSubmit () {
        this.$refs['dataForm'].validate((valid) => {
          if (valid) {
            this.$http({
              url: this.$http.adornUrl(`/admin/race/activity/prize/grade/${!this.dataForm.id ? 'save' : 'update'}`),
              method: 'post',
              data: this.$http.adornData(this.dataForm)
            }).then(({data}) => {
              if (data && data.code === 0) {
                this.$message({
                  message: '操作成功',
                  type: 'success',
                  duration: 1500,
                  onClose: () => {
                    this.visible = false
                    this.$emit('refreshDataList')
                  }
                })
              } else if (data && data.code === 303) {
                for (let it of data.obj) {
                  this[`${it.field}Error`] = it.message
                }
              } else {
                this.$message.error(data.msg)
              }
            })
          }
        })
      }
    }
  }
</script>
