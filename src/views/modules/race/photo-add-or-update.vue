<template>
  <el-dialog
    :title="!dataForm.id ? '新增' : '修改'"
    :close-on-click-modal="false"
    :visible.sync="visible">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmit()" label-width="80px">
      <el-row :gutter="20">
        <el-col :span="12">
           <el-form-item label="作品名" prop="name" :error="nameError">
                <el-input v-model="dataForm.name" placeholder="作品名"></el-input>
            </el-form-item>
        </el-col>
            <el-col :span="12">
           <el-form-item label="拍摄地点" prop="place" :error="placeError">
                <el-input v-model="dataForm.place" placeholder="拍摄地点"></el-input>
            </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
           <el-form-item label="审核状态" prop="auditStatus" :error="auditStatusError">
                <el-input v-model="dataForm.auditStatus" placeholder="审核状态"></el-input>
            </el-form-item>
        </el-col>
            <el-col :span="12">
           <el-form-item label="图片列表" prop="pictureList" :error="pictureListError">
                <el-input v-model="dataForm.pictureList" placeholder="图片列表"></el-input>
            </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
           <el-form-item label="编号" prop="number" :error="numberError">
                <el-input-number v-model="dataForm.number" controls-position="right" :min="0"></el-input-number>
            </el-form-item>
        </el-col>
            <el-col :span="12">
           <el-form-item label="选手id" prop="userId" :error="userIdError">
                <el-input v-model="dataForm.userId" placeholder="选手id"></el-input>
            </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
           <el-form-item label="活动id" prop="activityId" :error="activityIdError">
                <el-input v-model="dataForm.activityId" placeholder="活动id"></el-input>
            </el-form-item>
        </el-col>
            <el-col :span="12">
           <el-form-item label="投稿类型" prop="contributeType" :error="contributeTypeError">
                <el-input v-model="dataForm.contributeType" placeholder="投稿类型"></el-input>
            </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
  export default {
    data () {
      return {
        visible: false,
        dataForm: {
          id: null,
          version: null,
          name: '',
          place: '',
          auditStatus: '',
          pictureList: [],
          number: '',
          userId: '',
          activityId: '',
          contributeType: ''
        },
        dataRule: {
          place: [
            { required: true, message: '拍摄地点不能为空', trigger: 'blur' }
          ],
          auditStatus: [
            { required: true, message: '审核状态不能为空', trigger: 'blur' }
          ],
          pictureList: [
            { required: true, message: '图片列表不能为空', trigger: 'blur' }
          ],
          number: [
            { required: true, message: '编号不能为空', trigger: 'blur' }
          ],
          userId: [
            { required: true, message: '选手id不能为空', trigger: 'blur' }
          ],
          activityId: [
            { required: true, message: '活动id不能为空', trigger: 'blur' }
          ],
          contributeType: [
            { required: true, message: '投稿类型不能为空', trigger: 'blur' }
          ]
        },
        nameError: null,
        placeError: null,
        auditStatusError: null,
        pictureListError: null,
        numberError: null,
        userIdError: null,
        activityIdError: null,
        contributeTypeError: null
      }
    },
    methods: {
      init (id) {
        this.dataForm.id = id || null
        this.visible = true
        this.$nextTick(() => {
          this.$refs['dataForm']?.resetFields()
          if (this.dataForm.id) {
            this.$http({
              url: this.$http.adornUrl(`/admin/race/photo`),
              method: 'get',
              params: this.$http.adornParams({ id: this.dataForm.id })
            }).then(({data}) => {
              if (data && data.code === 0) {
                this.dataForm = data.obj
              }
            })
          }
        })
      },
      // 表单提交
      dataFormSubmit () {
        this.$refs['dataForm'].validate((valid) => {
          if (valid) {
            this.$http({
              url: this.$http.adornUrl(`/admin/race/photo/${!this.dataForm.id ? 'save' : 'update'}`),
              method: 'post',
              data: this.$http.adornData(this.dataForm)
            }).then(({data}) => {
              if (data && data.code === 0) {
                this.$message({
                  message: '操作成功',
                  type: 'success',
                  duration: 1500,
                  onClose: () => {
                    this.visible = false
                    this.$emit('refreshDataList')
                  }
                })
              } else if (data && data.code === 303) {
                for (let it of data.obj) {
                  this[`${it.field}Error`] = it.message
                }
              } else {
                this.$message.error(data.msg)
              }
            })
          }
        })
      }
    }
  }
</script>
