iwm<template>
  <el-dialog
    title="作品详情"
    :close-on-click-modal="false"
    width = "40%"
    :visible.sync="visible">
<!--    <div  v-for="item in dataList">-->
<!--      <img width="100%" :src="item" alt="" lazy>-->
<!--    </div>-->
    <el-carousel >
      <el-carousel-item v-for="item in dataList" :key="item">
        <img :src="item" alt="" style="width: 100%; height: 100%; object-fit: contain">
      </el-carousel-item>
    </el-carousel>
  </el-dialog>
</template>

<script>

  export default {
    data () {
      return {
        visible: false,
        dialogImageUrl: '',
        dataListLoading: false,
        dataList: [],
        listIndex: 0
      }
    },
    methods: {
      init (pictureList) {
        this.dataList = []
        this.visible = true
        pictureList.forEach(it =>{
          this.dataList.push(window.SITE_CONFIG['attachmentUrl'] + it.path)
        })
      }
    }
  }
</script>
