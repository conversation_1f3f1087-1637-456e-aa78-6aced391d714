<template>
  <div class="mod-config">
    <el-form :inline="true" :model="dataForm" @keyup.enter.native="queryPage()">
      <el-form-item label="活动名称：">
        <el-input v-model="dataForm.name" placeholder="活动名称" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-search" type="warning" @click="queryPage()">查询</el-button>
        <el-button icon="el-icon-refresh-left" @click="resetField()">重置</el-button>
      </el-form-item>
    </el-form>
    <div class="f-s-c" style="padding: 15px 0;display: flex;justify-content:flex-end;align-items: center ">
      <div>
        <el-button icon="el-icon-plus" type="primary" @click="addOrUpdateHandle()">新增</el-button>
        <el-button icon="el-icon-delete" type="danger" @click="deleteHandle()"
                   :disabled="dataListSelections.length <= 0">批量删除
        </el-button>
      </div>
    </div>
    <el-table
        :data="dataList"
        border
        v-loading="dataListLoading"
        @selection-change="selectionChangeHandle"
        style="width: 100%;">
      <el-table-column
          type="selection"
          header-align="center"
          align="center"
          width="50">
      </el-table-column>
      <el-table-column
          prop="name"
          header-align="center"
          align="center"
          label="活动名称">
      </el-table-column>
      <el-table-column
          prop="prize"
          header-align="center"
          align="center"
          label="奖项">
      </el-table-column>
      <el-table-column
          prop="enable"
          header-align="center"
          align="center"
          label="是否为当前活动">
        <template slot-scope="scope">
          <el-switch
              v-model="scope.row.enable"
              active-color="#13ce66"
              inactive-color="#ff4949"
              @change="setEnableHandle(scope.row.id, scope.row.enable)">
          </el-switch>
        </template>
      </el-table-column>
      <el-table-column
          prop="show"
          header-align="center"
          align="center"
          label="是否展示获奖作品">
        <template slot-scope="scope">
          <el-switch
              v-model="scope.row.isShow"
              active-color="#13ce66"
              inactive-color="#ff4949"
              @change="setShowHandle(scope.row.id, scope.row.isShow)">
          </el-switch>
        </template>
      </el-table-column>
      <el-table-column
          prop="upload"
          header-align="center"
          align="center"
          label="是否可以上传作品">
        <template slot-scope="scope">
          <el-switch
              v-model="scope.row.upload"
              active-color="#13ce66"
              inactive-color="#ff4949"
              @change="setUploadEnable(scope.row.id, scope.row.isUpload)">
          </el-switch>
        </template>
      </el-table-column>
      <!--         <el-table-column-->
      <!--        prop="remind"-->
      <!--        header-align="center"-->
      <!--        align="center"-->
      <!--        label="大赛简介">-->
      <!--      </el-table-column>-->
      <el-table-column
          fixed="right"
          header-align="center"
          align="center"
          min-width="150"
          label="操作">
        <template slot-scope="scope">
          <el-button type="text" size="small" @click="addOrUpdateHandle(scope.row.id)">修改</el-button>
          <el-button type="text" size="small" @click="deleteHandle(scope.row.id)">删除</el-button>
          <el-button type="text" size="small" @click="prizeHandle(scope.row.id)">编辑奖项</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
        @size-change="sizeChangeHandle"
        @current-change="currentChangeHandle"
        :current-page="pageIndex"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pageSize"
        :total="totalPage"
        layout="total, sizes, prev, pager, next, jumper">
    </el-pagination>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"></add-or-update>
    <price-grade v-if="prizeGradeVisible" ref="prizeGrade" @refreshDataList="getDataList"></price-grade>
  </div>
</template>

<script>
import AddOrUpdate from './activity-add-or-update'
import PriceGrade from './activity-prize-grade'

export default {
  data() {
    return {
      dataForm: {
        name: ''
      },
      dataList: [],
      pageIndex: 1,
      pageSize: 10,
      totalPage: 0,
      dataListLoading: false,
      dataListSelections: [],
      addOrUpdateVisible: false,
      prizeGradeVisible: false
    }
  },
  components: {
    AddOrUpdate,
    PriceGrade
  },
  activated() {
    this.queryPage()
  },
  methods: {
    resetField() {
      this.dataForm.name = null
      this.pageIndex = 1
      this.getDataList()
    },
    queryPage() {
      this.pageIndex = 1
      this.getDataList()
    },
    // 获取数据列表
    getDataList() {
      this.dataListLoading = true
      this.$http({
        url: this.$http.adornUrl('/admin/race/activity/pages'),
        method: 'post',
        data: this.$http.adornData({
          'currentPage': this.pageIndex,
          'pageSize': this.pageSize,
          'name': this.dataForm.name
        })
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.dataList = data.obj.records
          this.totalPage = data.obj.total
        } else {
          this.dataList = []
          this.totalPage = 0
        }
        this.dataListLoading = false
      })
    },
    // 每页数
    sizeChangeHandle(val) {
      this.pageSize = val
      this.pageIndex = 1
      this.getDataList()
    },
    // 当前页
    currentChangeHandle(val) {
      this.pageIndex = val
      this.getDataList()
    },
    // 多选
    selectionChangeHandle(val) {
      this.dataListSelections = val
    },
    // 新增 / 修改
    addOrUpdateHandle(id) {
      this.addOrUpdateVisible = true
      this.$nextTick(() => {
        this.$refs.addOrUpdate.init(id)
      })
    },
    prizeHandle(id) {
      this.prizeGradeVisible = true
      this.$nextTick(() => {
        this.$refs.prizeGrade.init(id)
      })
    },
    // 删除
    deleteHandle(id) {
      var ids = id ? [id] : this.dataListSelections.map(item => {
        return item.id
      })
      this.$confirm(`确定进行[${id ? '删除' : '批量删除'}]操作?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$http({
          url: this.$http.adornUrl('/admin/race/activity/removeByIds'),
          method: 'get',
          params: this.$http.adornParams({
            'ids': ids.join(',')
          })
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.$message({
              message: '操作成功',
              type: 'success',
              duration: 1500,
              onClose: () => {
                this.getDataList()
              }
            })
          } else {
            this.$message.error(data.msg)
          }
        })
      })
    },
    // 启用、禁用
    setEnableHandle(id, enable) {
      this.$http({
        url: this.$http.adornUrl('/admin/race/activity/setEnable'),
        method: 'get',
        params: this.$http.adornParams({
          'id': id
        })
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.$message({
            message: '操作成功',
            type: 'success',
            duration: 1500,
            onClose: () => {
              this.getDataList()
            }
          })
        } else {
          this.$message.error(data.msg)
          this.getDataList()
        }
      }).catch(() => {
      })
    },
    //是否展示
    setShowHandle(id, enable) {
      this.$http({
        url: this.$http.adornUrl('/admin/race/activity/setShow'),
        method: 'get',
        params: this.$http.adornParams({
          'id': id
        })
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.$message({
            message: '操作成功',
            type: 'success',
            duration: 1500,
            onClose: () => {
              this.getDataList()
            }
          })
        } else {
          this.$message.error(data.msg)
          this.getDataList()
        }
      }).catch(() => {
      })
    },
    setUploadEnable(id) {
      this.$http({
        url: this.$http.adornUrl('/admin/race/activity/setUpload'),
        method: 'get',
        params: this.$http.adornParams({
          'id': id
        })
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.$message({
            message: '操作成功',
            type: 'success',
            duration: 1500,
            onClose: () => {
              this.getDataList()
            }
          })
        } else {
          this.$message.error(data.msg)
          this.getDataList()
        }
      })
    }
  }
}
</script>
