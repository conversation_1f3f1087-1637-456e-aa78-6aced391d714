<template>
  <el-dialog
      :title="'奖项编辑'"
      :close-on-click-modal="false"
      width="80%"
      :visible.sync="visible">
    <el-form :inline="true" :model="dataForm" @keyup.enter.native="queryPage()">
      <el-form-item style="float: right">
        <el-button icon="el-icon-plus" type="primary" @click="addOrUpdateHandle()">新增</el-button>
        <el-button icon="el-icon-delete" type="danger" @click="deleteHandle()" :disabled="dataListSelections.length <= 0">批量删除</el-button>
        <el-button icon="el-icon-upload2" type="success"  @click="importFile()">导入获奖作品</el-button>
        <el-button icon="el-icon-download" type="info" @click="templateDownload()">导入模版下载</el-button>
      </el-form-item>
    </el-form>
    <el-table
      :data="dataList"
      border
      v-loading="dataListLoading"
      @selection-change="selectionChangeHandle"
      style="width: 100%;">
      <el-table-column
        type="selection"
        header-align="center"
        align="center"
        width="50">
      </el-table-column>
       <el-table-column
        prop="name"
        header-align="center"
        align="center"
        label="奖项名称">
      </el-table-column>
         <el-table-column
        prop="sequence"
        header-align="center"
        align="center"
        label="排序">
      </el-table-column>
        <el-table-column
        fixed="right"
        header-align="center"
        align="center"
        width="150"
        label="操作">
        <template slot-scope="scope">
          <el-button type="text" size="small" @click="addOrUpdateHandle(scope.row.id)">修改</el-button>
          <el-button type="text" size="small" @click="deleteHandle(scope.row.id)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getPrizeList"></add-or-update>
    <!-- 弹窗, 上传excel -->
<!--    <import-component v-if="importVisible" ref="importComponent" v-on:setDataList="setDataList"></import-component>-->
    <import-component v-if="importVisible" ref="importComponent" @refreshDataList="getPrizeList"></import-component>
  </el-dialog>
</template>

<script>
  import AddOrUpdate from './activity-prize-grade-add-or-update'
  import importComponent from './prize-import'
  export default {
    data () {
      return {
        dataForm: {
          key: '',
          activityId: ''
        },
        visible: false,
        importVisible: false,
        dataList: [],
        pageIndex: 1,
        pageSize: 10,
        totalPage: 0,
        dataListLoading: false,
        dataListSelections: [],
        addOrUpdateVisible: false
      }
    },
    components: {
      importComponent,
      AddOrUpdate
    },
    activated () {
      this.queryPage()
    },
    methods: {
      init (id) {
        this.dataForm.activityId = id || null
        this.visible = true
        this.getPrizeList()
      },
      getPrizeList () {
        this.$http({
              url: this.$http.adornUrl('/admin/race/activity/prize/grade/getPrize'),
              method: 'get',
              params: this.$http.adornParams({
                'activityId': this.dataForm.activityId
              })
            }).then(({data}) => {
              if (data && data.code === 0) {
                this.dataList = data.obj
              } else {
                this.dataList = []
              }
              this.dataListLoading = false
            })
      },
      // 多选
      selectionChangeHandle (val) {
        this.dataListSelections = val
      },
      // 新增 / 修改
      addOrUpdateHandle (id) {
        this.addOrUpdateVisible = true
        this.$nextTick(() => {
          this.$refs.addOrUpdate.init(id, this.dataForm.activityId)
        })
      },
      // 删除
      deleteHandle (id) {
        var ids = id ? [id] : this.dataListSelections.map(item => {
          return item.id
        })
        this.$confirm(`确定进行[${id ? '删除' : '批量删除'}]操作?`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.$http({
            url: this.$http.adornUrl('/admin/race/activity/prize/grade/removeByIds'),
            method: 'get',
            params: this.$http.adornParams({
              'ids': ids.join(',')
            })
          }).then(({data}) => {
            if (data && data.code === 0) {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 1500,
                onClose: () => {
                  this.getPrizeList()
                }
              })
            } else {
              this.$message.error(data.msg)
            }
          })
        })
      },
      // 下载模板文件
      templateDownload () {
        this.$http({
          url: this.$http.adornUrl('/admin/race/activity/prize/winPhotoExcelTemplate'),
          method: 'get',
          responseType: 'arraybuffer',
          params: this.$http.adornParams()
        }).then(({data}) => {
          if (data.code && data.code !== 0) {
            this.$message.error('下载失败')
          } else {
            let blob = new Blob([data], {type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8'})
            let objectUrl = URL.createObjectURL(blob)
            let a = document.createElement('a')
            // window.location.href = objectUrl
            a.href = objectUrl
            a.download = '获奖作品导入模版.xlsx'
            a.click()
            URL.revokeObjectURL(objectUrl)
            this.$message({
              type: 'success',
              message: '下载模板成功'
            })
          }
        })
      },
      // 上传文件
      importFile () {
        this.importVisible = true
        this.$nextTick(() => {
          this.$refs.importComponent.init( this.dataForm.activityId)
        })
      }
    }
  }
</script>
