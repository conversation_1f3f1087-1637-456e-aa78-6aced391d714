<template>
  <div class="mod-config">
    <el-form :inline="true"  :model="dataForm" ref="dataForm" @keyup.enter.native="queryPage()">
      <el-form-item label="活动名称：" prop="activityId">
        <el-select v-model="dataForm.activityId" clearable placeholder="请选择">
          <el-option
              v-for="item in activityList"
              :key="item.id"
              :label="item.name"
              :value="item.id">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="作品名称：" prop="key">
        <el-input v-model="dataForm.key" placeholder="作品名称" clearable></el-input>
      </el-form-item>
      <el-form-item label="审核状态：" prop="auditStatus">
        <el-select v-model="dataForm.auditStatus" clearable placeholder="请选择">
          <el-option
              v-for="item in auditStatusList"
              :key="item.code"
              :label="item.name"
              :value="item.code">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-search" type="warning" @click="queryPage()">查询</el-button>
        <el-button @click="resetForm()" icon="el-icon-refresh-left">重置</el-button>
      </el-form-item>
    </el-form>
    <div class="f-s-c" style="padding: 15px 0;display: flex;justify-content:flex-end;align-items: center ">
      <div>
        <el-button icon="el-icon-s-check" type="primary" @click="updateStatusPass()" :disabled="dataListSelections.length <= 0">批量通过审核</el-button>
        <el-button icon="el-icon-download" type="success" @click="exportPhoto()">导出活动作品</el-button>
      </div>
    </div>
    <el-table
        :data="dataList"
        border
        v-loading="dataListLoading"
        @selection-change="selectionChangeHandle"
        style="width: 100%;">
      <el-table-column
          type="selection"
          header-align="center"
          align="center"
          width="50">
      </el-table-column>
      <el-table-column
          prop="number"
          header-align="center"
          align="center"
          label="编号">
      </el-table-column>
      <el-table-column
          prop="author"
          header-align="center"
          align="center"
          label="作者">
      </el-table-column>
      <el-table-column
          prop="authorPhone"
          header-align="center"
          align="center"
          label="作者联系方式">
      </el-table-column>
      <el-table-column
          prop="name"
          header-align="center"
          align="center"
          label="作品名称">
<!--        <template slot-scope="scope">-->
<!--          <div style="cursor: pointer" @click="getDetail(scope.row.pictureList)">{{scope.row.name}}</div>-->
<!--        </template>-->
        <template slot-scope="scope">
          <a style="cursor: pointer" @click="getDetail(scope.row.pictureList)">{{ scope.row.name }}</a>
        </template>
      </el-table-column>
      <el-table-column
          prop="place"
          header-align="center"
          align="center"
          label="拍摄地点">
      </el-table-column>
        <el-table-column
                prop="recordDate"
                header-align="center"
                align="center"
                label="拍摄时间">
        </el-table-column>
      <el-table-column
          prop="categoryText"
          header-align="center"
          align="center"
          show-overflow-tooltip
          label="投稿类别">
      </el-table-column>
            <el-table-column
          prop="auditStatusText"
          header-align="center"
          align="center"
          label="审核状态">
      </el-table-column>
      <el-table-column
          fixed="right"
          header-align="center"
          align="center"
          width="150"
          label="操作">
        <template v-if="scope.row.auditStatus === 'CheckPending'" slot-scope="scope">
          <el-button type="text" size="small" @click="updateStatusPass(scope.row.id)">审核通过</el-button>
          <el-button type="text" size="small" @click="updateStatusFail(scope.row.id)">审核不通过</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
        @size-change="sizeChangeHandle"
        @current-change="currentChangeHandle"
        :current-page="pageIndex"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pageSize"
        :total="totalPage"
        layout="total, sizes, prev, pager, next, jumper">
    </el-pagination>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"></add-or-update>
    <get-detail v-if="getDetailVisible" ref="getDetail" @refreshDataList="getDataList"></get-detail>
    <update-status-fail v-if="updateStatusFailVisible" ref="updateStatusFail" @refreshDataList="getDataList"></update-status-fail>
  </div>
</template>

<script>
import AddOrUpdate from './photo-add-or-update'
import GetDetail from './photo-detail'
import UpdateStatusFail from './photo-audit-no-pass'
import {map} from 'lodash'

export default {
  data() {
    return {
      dataForm: {
        auditStatus: '',
        key: '',
        activityId: ''
      },
      auditStatusList: [],
      activityList: [],
      orders: [
        {
          "column": "number",
          "sort": "asc"
        }
      ],
      dataList: [],
      pageIndex: 1,
      pageSize: 10,
      totalPage: 0,
      getDetailVisible: false,
      dataListLoading: false,
      dataListSelections: [],
      updateStatusFailVisible: false,
      addOrUpdateVisible: false
    }
  },
  components: {
    AddOrUpdate,
    GetDetail,
    UpdateStatusFail
  },
  activated() {
    this.queryPage()
    this.getAuditStatusList()
    this.getActivityList()
  },
  methods: {
    queryPage() {
      this.pageIndex = 1
      this.getDataList()
    },
    getDetail (pictureList) {
      console.log(1)
      this.getDetailVisible = true
      this.$nextTick(() => {
        this.$refs.getDetail.init(pictureList)
      })
    },
    resetForm() {
      this.$refs['dataForm']?.resetFields()
      this.getDataList()
    },
    // 获取数据列表
    getDataList() {
      this.dataListLoading = true
      this.$http({
        url: this.$http.adornUrl('/admin/race/photo/getPages'),
        method: 'post',
        data: this.$http.adornData({
          'currentPage': this.pageIndex,
          'pageSize': this.pageSize,
          'key': this.dataForm.key,
          'activityId': this.dataForm.activityId,
          'auditStatus': this.dataForm.auditStatus,
          'orders': this.orders
        })
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.dataList = data.obj.records
          this.totalPage = data.obj.total
        } else {
          this.dataList = []
          this.totalPage = 0
        }
        this.dataListLoading = false
      })
    },
    updateStatusPass(id) {
      var ids = id ? [id] : this.dataListSelections.map(item => {
        return item.id
      })
      this.$confirm(`审核通过?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$http({
          url: this.$http.adornUrl('/admin/race/photo/updateStatus'),
          method: 'get',
          params: this.$http.adornParams({
            'ids': ids.join(','),
            'status': true
          })
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.$message({
              message: '操作成功',
              type: 'success',
              duration: 1500,
              onClose: () => {
                this.getDataList()
              }
            })
          } else {
            this.$message.error(data.msg)
          }
        })
      })
    },
    updateStatusFail(id) {
      var ids = id ? [id] : this.dataListSelections.map(item => {
        return item.id
      })
      this.updateStatusFailVisible = true
      this.$nextTick(() => {
        this.$refs.updateStatusFail.init(ids.join(','))
      })
    },
    // 审核状态下拉列表
    getAuditStatusList () {
      this.$http({
        url: this.$http.adornUrl('/admin/dict/parent'),
        method: 'get',
        params: this.$http.adornParams({
          'code': 'validStatus'
        })
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.auditStatusList = data.obj
        } else {
          this.auditStatusList = []
        }
      })
    },
    // 活动下拉列表
    getActivityList () {
      this.$http({
        url: this.$http.adornUrl('/admin/race/activity/getActivityByShow'),
        method: 'get',
        params: this.$http.adornParams({
        })
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.activityList = data.obj
          this.activityList.forEach(it => {
        if(it.enable === true){
          this.dataForm.activityId = it.id
          console.log(this.dataForm.activityId)
        }
          })
        } else {
          this.activityList = []
        }
      })
    },
    // 每页数
    sizeChangeHandle(val) {
      this.pageSize = val
      this.pageIndex = 1
      this.getDataList()
    },
    // 当前页
    currentChangeHandle(val) {
      this.pageIndex = val
      this.getDataList()
    },
    // 多选
    selectionChangeHandle(val) {
      this.dataListSelections = val
    },
    // 新增 / 修改
    addOrUpdateHandle(id) {
      this.addOrUpdateVisible = true
      this.$nextTick(() => {
        this.$refs.addOrUpdate.init(id)
      })
    },
    // 删除
    deleteHandle(id) {
      var ids = id ? [id] : this.dataListSelections.map(item => {
        return item.id
      })
      this.$confirm(`确定进行[${id ? '删除' : '批量删除'}]操作?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$http({
          url: this.$http.adornUrl('/admin/race/photo/removeByIds'),
          method: 'get',
          params: this.$http.adornParams({
            'ids': ids.join(',')
          })
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.$message({
              message: '操作成功',
              type: 'success',
              duration: 1500,
              onClose: () => {
                this.getDataList()
              }
            })
          } else {
            this.$message.error(data.msg)
          }
        })
      })
    },
    exportPhoto () {
      this.dataListLoading = true
      const activityId = map(this.dataList, 'activityId')[0]
      this.$http({
        timeout: 10 * 60 * 1000,
        url: this.$http.adornUrl('/admin/race/photo/exportPhoto'),
        method: 'get',
        responseType: 'arraybuffer',
        params: this.$http.adornParams({'activityId': activityId})
      }).then(({data}) => {
        this.dataListLoading = false
        if (data.code && data.code !== 0) {
          this.$message.error('导出失败')
        } else {
          let blob = new Blob([data], {type: 'application/zip;charset=utf-8'})
          let objectUrl = URL.createObjectURL(blob)
          let a = document.createElement('a')
          // window.location.href = objectUrl
          a.href = objectUrl
          a.click()
          URL.revokeObjectURL(objectUrl)
          this.$message({
            type: 'success',
            message: '导出数据成功'
          })
        }
      })
    }
  }
}
</script>
