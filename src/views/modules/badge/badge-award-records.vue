<template>
  <el-dialog
    title="授权记录"
    :close-on-click-modal="false"
    :visible.sync="visible"
    width="75%">
    <el-form :inline="true" :model="dataForm" @keyup.enter.native="getDataList()">
      <el-form-item prop="toName" label="授权对象">
        <el-input v-model="dataForm.toName" placeholder="请输入授权对象姓名" clearable></el-input>
      </el-form-item>
      <el-form-item prop="phone" label="手机号">
        <el-input v-model="dataForm.phone" placeholder="请输入手机号" clearable></el-input>
      </el-form-item>
      <el-form-item prop="timeRange" label="授权时间">
        <el-date-picker
          v-model="timeRange"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="yyyy-MM-dd"
          @change="handleTimeRangeChange">
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-search" type="warning" @click="getDataList()">查询</el-button>
        <el-button @click="resetForm()" icon="el-icon-refresh-left">重置</el-button>
      </el-form-item>
    </el-form>
    <el-table :data="dataList" border v-loading="dataListLoading" style="width: 100%;">
      <el-table-column type="index" header-align="center" align="center" width="50" label="序号"/>
      <el-table-column prop="badgeName" header-align="center" align="center" label="勋章名称"/>
      <el-table-column prop="toName" header-align="center" align="center" label="授权对象"/>
      <el-table-column prop="toPhone" header-align="center" align="center" label="手机号"/>
      <el-table-column prop="fromName" header-align="center" align="center" label="授权人"/>
      <el-table-column prop="createDate" header-align="center" align="center" label="授权时间"/>
    </el-table>
    <el-pagination
      @size-change="sizeChangeHandle"
      @current-change="currentChangeHandle"
      :current-page="pageIndex"
      :page-sizes="[10, 20, 50, 100]"
      :page-size="pageSize"
      :total="totalPage"
      layout="total, sizes, prev, pager, next, jumper">
    </el-pagination>
  </el-dialog>
</template>

<script>
import Vue from 'vue'

export default {
  data () {
    return {
      visible: false,
      dataForm: {
        badgeId: null,
        toName: '',
        phone: '',
        operationType: 'BADGE_OPERATION_TYPE_GRANT', // 固定为授权类型
        startTime: '',
        endTime: ''
      },
      timeRange: [],
      dataList: [],
      pageIndex: 1,
      pageSize: 10,
      totalPage: 0,
      dataListLoading: false
    }
  },
  methods: {
    init (id) {
      this.visible = true
      this.dataForm.badgeId = id
      this.resetForm()
      this.$nextTick(() => {
        this.getDataList()
      })
    },
    // 获取数据列表
    getDataList () {
      this.dataListLoading = true
      this.$http({
        url: this.$http.adornUrl('/admin/zyz/badge/transaction/queryPage'),
        method: 'post',
        data: this.$http.adornData({
          currentPage: this.pageIndex,
          pageSize: this.pageSize,
          badgeId: this.dataForm.badgeId,
          operationType: this.dataForm.operationType,
          toName: this.dataForm.toName,
          phone: this.dataForm.phone,
          startTime: this.dataForm.startTime,
          endTime: this.dataForm.endTime
        })
      }).then(({data}) => {
        this.dataListLoading = false
        if (data && data.code === 0) {
          this.dataList = data.obj.records
          this.totalPage = data.obj.total
        } else {
          this.dataList = []
          this.totalPage = 0
          this.$message.error(data.msg || '查询失败')
        }
      }).catch(() => {
        this.dataListLoading = false
      })
    },
    // 重置表单
    resetForm () {
      this.dataForm.toName = ''
      this.dataForm.phone = ''
      this.dataForm.startTime = ''
      this.dataForm.endTime = ''
      this.timeRange = []
      this.pageIndex = 1
      this.getDataList()
    },
    // 处理时间范围变化
    handleTimeRangeChange (val) {
      if (val) {
        this.dataForm.startTime = val[0]
        this.dataForm.endTime = val[1]
      } else {
        this.dataForm.startTime = ''
        this.dataForm.endTime = ''
      }
    },
    // 每页数变化
    sizeChangeHandle (val) {
      this.pageSize = val
      this.pageIndex = 1
      this.getDataList()
    },
    // 当前页变化
    currentChangeHandle (val) {
      this.pageIndex = val
      this.getDataList()
    }
  }
}
</script>

<style lang="scss" scoped>
</style> 
