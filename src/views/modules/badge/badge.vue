<template>
  <div class="mod-list">
    <el-form :inline="true" :model="dataForm" ref="dataForm" @keyup.enter.native="getDataList()">
      <el-form-item prop="name" label="勋章名称">
        <el-input v-model="dataForm.name" placeholder="请输入勋章名称" clearable></el-input>
      </el-form-item>
      <el-form-item prop="isTop" label="是否置顶">
        <el-select v-model="dataForm.top" placeholder="全部" clearable>
          <el-option label="全部" :value="null"></el-option>
          <el-option label="是" :value="true"></el-option>
          <el-option label="否" :value="false"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item prop="status" label="状态">
        <el-select v-model="dataForm.status" placeholder="全部" clearable>
          <el-option label="全部" :value="null"></el-option>
          <el-option label="上架中" :value="true"></el-option>
          <el-option label="已下架" :value="false"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-search" type="warning" @click="getDataList()">查询</el-button>
        <el-button @click="resetForm()" icon="el-icon-refresh-left">重置</el-button>
      </el-form-item>
    </el-form>
    <div class="f-s-c" style="padding: 15px 0; display: flex; justify-content: flex-end; align-items: center">
      <div>
        <el-button type="primary" @click="addOrUpdateHandle()" icon="el-icon-plus">新增</el-button>
      </div>
    </div>
    <el-table :data="dataList" border v-loading="dataListLoading" @selection-change="selectionChangeHandle" style="width: 100%;">
      <el-table-column type="index" header-align="center" align="center" width="50" label="序号"/>
      <el-table-column prop="name" header-align="center" align="center" label="勋章名称"/>
      <el-table-column prop="briefIntro" header-align="center" align="center" label="勋章简介"/>
      <el-table-column prop="pointsRequired" header-align="center" align="center" label="兑换所需积分"/>
      <el-table-column prop="sortNum" header-align="center" align="center" label="顺序"/>
      <el-table-column prop="top" header-align="center" align="center" label="是否置顶">
        <template slot-scope="scope">
          <el-tag v-if="!scope.row.top" size="small" type="danger">否</el-tag>
          <el-tag v-else size="small">是</el-tag>
        </template>
      </el-table-column>
      <el-table-column header-align="center" align="center" label="后台状态">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.status" size="small" type="success">上架中</el-tag>
          <el-tag v-else size="small" type="info">已下架</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="createDate" header-align="center" align="center" label="创建时间"/>
      <el-table-column fixed="right" header-align="center" align="center" width="320" label="操作">
        <template slot-scope="scope">
          <el-button type="text" @click="addOrUpdateHandle(scope.row.id)">编辑</el-button>
          <el-button type="text" @click="deleteHandle(scope.row.id,scope.row.name)">删除</el-button>
          <el-button type="text" v-if="!scope.row.status" @click="publishHandle(scope.row.id)">上架</el-button>
          <el-button type="text" v-if="scope.row.status" @click="unpublishHandle(scope.row.id)">下架</el-button>
          <el-button type="text" v-if="!scope.row.top" @click="topHandle(scope.row.id)">置顶</el-button>
          <el-button type="text" v-if="scope.row.top" @click="topHandle(scope.row.id)">取消置顶</el-button>
          <el-button type="text" @click="viewAwardRecordsHandle(scope.row.id)">授权记录</el-button>
          <el-button
              type="text"
              @click="batchDistributeHandle(scope.row.id)"
              :disabled="!scope.row.status">批量授权
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination @size-change="sizeChangeHandle" @current-change="currentChangeHandle" :current-page="pageIndex"
                   :page-sizes="[10, 20, 50, 100]" :page-size="pageSize" :total="totalPage" layout="total, sizes, prev, pager, next, jumper"/>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"/>
    <!-- 3D预览对话框 -->
    <model-viewer v-model="previewVisible" :model-url="previewUrl"></model-viewer>
    <!-- 批量授权对话框 -->
    <badge-batch-award v-if="batchAwardVisible" ref="batchAward" @refreshDataList="getDataList"/>
    <!-- 授权记录对话框 -->
    <badge-auth-records v-if="authRecordsVisible" ref="authRecords"/>
  </div>
</template>

<script>
  import AddOrUpdate from './badge-add-or-update'
  import listMixin from '@/mixins/list-mixins'
  import BadgeBatchAward from './badge-batch-award'
  import BadgeAwardRecords from './badge-award-records.vue'
  export default {
    mixins: [listMixin],
    data () {
      return {
        mixinOptions: {
          dataUrl: '/admin/zyz/badge/pages',
          deleteUrl: '/admin/zyz/badge/removeByIds'
        },
        dataForm: {
          name: null,
          top: null,
          status: null
        },
        previewVisible: false,
        previewUrl: '',
        batchAwardVisible: false,
        authRecordsVisible: false
      }
    },
    components: {
      AddOrUpdate,
      BadgeBatchAward,
      BadgeAuthRecords: BadgeAwardRecords
    },
    methods: {
      resetForm() {
        this.$refs['dataForm']?.resetFields()
        this.getDataList()
      },
      // 勋章预览
      previewHandle(row) {
        if (row.badgeFile) {
          this.previewUrl = this.$http.adornAttachmentUrl(row.badgeFile)
          this.previewVisible = true
        } else {
          this.$message.warning('没有可预览的文件')
        }
      },
      // 查看详情
      detailsHandle(id) {
        // 空方法，后续实现
      },
      // 上架勋章
      publishHandle(id) {
        this.$confirm('确定要上架该勋章吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.$http.post(this.$http.adornUrl('/admin/zyz/badge/switchStatus'), null, {
            params: { id },
            headers: { Authorization: this.$cookie.get('Authorization') }
          }).then(({ data }) => {
            if (data && data.code === 0) {
              this.$message.success('操作成功');
              this.getDataList();
            } else {
              this.$message.error(data.msg || '操作失败');
            }
          });
        }).catch(() => {});
      },
      // 下架勋章
      unpublishHandle(id) {
        this.$confirm('确定要下架该勋章吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.$http.post(this.$http.adornUrl('/admin/zyz/badge/switchStatus'), null, {
            params: { id },
            headers: { Authorization: this.$cookie.get('Authorization') }
          }).then(({ data }) => {
            if (data && data.code === 0) {
              this.$message.success('操作成功');
              this.getDataList();
            } else {
              this.$message.error(data.msg || '操作失败');
            }
          });
        }).catch(() => {});
      },
      // 批量发放勋章
      batchDistributeHandle(id) {
        this.batchAwardVisible = true
        this.$nextTick(() => {
          this.$refs.batchAward.init(id)
        })
      },
      // 置顶勋章
      topHandle(id) {
        this.$http.post(this.$http.adornUrl('/admin/zyz/badge/switchTop'), null, {
          params: { id },
          headers: { Authorization: this.$cookie.get('Authorization') }
        }).then(({ data }) => {
          if (data && data.code === 0) {
            this.$message.success('操作成功');
            this.getDataList();
          } else {
            this.$message.error(data.msg || '操作失败');
          }
        });
      },
      // 取消置顶勋章
      cancelTopHandle(id) {
        // 空方法，后续实现
      },
      // 查看授权记录
      viewAwardRecordsHandle(id) {
        this.authRecordsVisible = true
        this.$nextTick(() => {
          this.$refs.authRecords.init(id)
        })
      }
    }
  }
</script>

<style lang="scss" scoped>
</style>
