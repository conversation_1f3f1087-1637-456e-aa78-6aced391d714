<template>
  <div class="mod-list">
    <el-form :inline="true" :model="dataForm" ref="dataForm" @keyup.enter.native="getDataList()">
      <el-form-item prop="badgeName" label="勋章名称">
        <el-input v-model="dataForm.badgeName" placeholder="请输入勋章名称" clearable></el-input>
      </el-form-item>
      <el-form-item prop="operationType" label="操作类型">
        <el-dict :code="'BADGE_OPERATION_TYPE'" v-model="dataForm.operationType"></el-dict>
      </el-form-item>
      <el-form-item prop="fromName" label="发起人">
        <el-input v-model="dataForm.fromName" placeholder="请输入发起人姓名" clearable></el-input>
      </el-form-item>
      <el-form-item prop="toName" label="接收人">
        <el-input v-model="dataForm.toName" placeholder="请输入接收人姓名" clearable></el-input>
      </el-form-item>
      <el-form-item prop="phone" label="手机号">
        <el-input v-model="dataForm.phone" placeholder="请输入手机号" clearable></el-input>
      </el-form-item>
      <el-form-item prop="timeRange" label="操作时间">
        <el-date-picker
          v-model="timeRange"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="yyyy-MM-dd"
          @change="handleTimeRangeChange">
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-search" type="warning" @click="getDataList()">查询</el-button>
        <el-button @click="resetForm()" icon="el-icon-refresh-left">重置</el-button>
      </el-form-item>
    </el-form>

    <el-table :data="dataList" border v-loading="dataListLoading" style="width: 100%;">
      <el-table-column type="index" header-align="center" align="center" width="50" label="序号"/>
      <el-table-column prop="badgeName" header-align="center" align="center" label="勋章名称"/>
      <el-table-column prop="fromName" header-align="center" align="center" label="发起人"/>
      <el-table-column prop="operationTypeText" header-align="center" align="center" label="操作类型"/>
      <el-table-column prop="toName" header-align="center" align="center" label="姓名"/>
      <el-table-column prop="fromPhone" header-align="center" align="center" label="操作人手机号"/>
      <el-table-column prop="toPhone" header-align="center" align="center" label="接收人手机号"/>
      <el-table-column prop="createDate" header-align="center" align="center" min-width="90" label="操作时间"/>
      <el-table-column prop="detail" header-align="center" align="left" label="操作详情" min-width="280">
        <template slot-scope="scope">
          <!-- 批量授权 -->
          <template v-if="scope.row.operationType === 'BADGE_OPERATION_TYPE_GRANT'">
            --
          </template>
          <!-- 赠送 -->
          <template v-else-if="scope.row.operationType === 'BADGE_OPERATION_TYPE_GIFT'">
            --
          </template>
          <!-- 交易 -->
          <template v-else-if="scope.row.operationType === 'BADGE_OPERATION_TYPE_TRADE'">
            <div>勋章交易积分：{{ scope.row.pointsBeforeFee }} 积分</div>
            <div>平台抽成比例：{{ scope.row.platformFeeRate }}%</div>
            <div>获得积分：{{ scope.row.pointsAfterFee }} 积分</div>
          </template>
          <!-- 回收 -->
          <template v-else-if="scope.row.operationType === 'BADGE_OPERATION_TYPE_RECYCLE'">
            <div>兑换组织：{{ scope.row.toName }}</div>
            <div>兑换积分：{{ scope.row.pointsBeforeFee }} 积分</div>
            <div>平台抽成比例：{{ scope.row.platformFeeRate }}%</div>
            <div>获得积分：{{ scope.row.pointsAfterFee }} 积分</div>
          </template>
          <!-- 默认 -->
          <template v-else>
            --
          </template>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      @size-change="sizeChangeHandle"
      @current-change="currentChangeHandle"
      :current-page="pageIndex"
      :page-sizes="[10, 20, 50, 100]"
      :page-size="pageSize"
      :total="totalPage"
      layout="total, sizes, prev, pager, next, jumper">
    </el-pagination>
  </div>
</template>

<script>
import listMixin from '@/mixins/list-mixins'

export default {
  mixins: [listMixin],
  data () {
    return {
      mixinOptions: {
        dataUrl: '/admin/zyz/badge/transaction/queryPage',
        activatedLoad: true
      },
      dataForm: {
        badgeName: '',
        operationType: '',
        fromName: '',
        toName: '',
        phone: '',
        startTime: '',
        endTime: ''
      },
      timeRange: []
    }
  },
  methods: {
    // 重置表单
    resetForm () {
      this.$refs['dataForm']?.resetFields()
      this.dataForm.badgeName = ''
      this.dataForm.operationType = ''
      this.dataForm.fromName = ''
      this.dataForm.toName = ''
      this.dataForm.phone = ''
      this.dataForm.startTime = ''
      this.dataForm.endTime = ''
      this.timeRange = []
      this.getDataList()
    },
    // 处理时间范围变化
    handleTimeRangeChange (val) {
      if (val) {
        this.dataForm.startTime = val[0]
        this.dataForm.endTime = val[1]
      } else {
        this.dataForm.startTime = ''
        this.dataForm.endTime = ''
      }
    },
    // 查询前操作
    queryBeforeHandle () {
      // 由于使用了mixins，这里不需要额外处理，只需要确保dataForm中有对应的参数
    }
  }
}
</script>

<style lang="scss" scoped>
</style> 
