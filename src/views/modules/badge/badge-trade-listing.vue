<template>
  <div class="mod-list">
    <el-form :inline="true" :model="dataForm" ref="dataForm" @keyup.enter.native="getDataList()">
      <el-form-item prop="name" label="勋章名称">
        <el-input v-model="dataForm.name" placeholder="请输入勋章名称" clearable></el-input>
      </el-form-item>
      <el-form-item prop="sellerName" label="发布人">
        <el-input v-model="dataForm.sellerName" placeholder="请输入发布人姓名" clearable></el-input>
      </el-form-item>
      <el-form-item prop="fromPlatform" label="是否平台发布">
        <el-select v-model="dataForm.fromPlatform" placeholder="请选择" clearable>
          <el-option :value="true" label="是"></el-option>
          <el-option :value="false" label="否"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item prop="publishDateStart" label="发布时间">
        <el-date-picker
          v-model="dataForm.publishDateStart"
          type="date"
          placeholder="开始日期"
          value-format="yyyy-MM-dd">
        </el-date-picker>
      </el-form-item>
      <el-form-item prop="publishDateEnd" label="至">
        <el-date-picker
          v-model="dataForm.publishDateEnd"
          type="date"
          placeholder="结束日期"
          value-format="yyyy-MM-dd">
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-search" type="warning" @click="getDataList()">查询</el-button>
        <el-button @click="resetForm()" icon="el-icon-refresh-left">重置</el-button>
      </el-form-item>
    </el-form>
    <div class="f-s-c" style="padding: 15px 0; display: flex; justify-content: flex-end; align-items: center">
<!--      <div>-->
<!--        <el-button type="primary" @click="addOrUpdateHandle()" icon="el-icon-plus">新增</el-button>-->
<!--      </div>-->
    </div>
    <el-table :data="dataList" border v-loading="dataListLoading" style="width: 100%;">
      <el-table-column type="index" header-align="center" align="center" label="序号" width="60" />
      <el-table-column prop="name" header-align="center" align="center" label="勋章名称" />
      <el-table-column prop="sellerName" header-align="center" align="center" label="发布人" />
      <el-table-column prop="pointsBeforeFee" header-align="center" align="center" label="兑换所需积分" />
      <el-table-column prop="createDate" header-align="center" align="center" label="发布时间" />
    </el-table>
    <el-pagination @size-change="sizeChangeHandle" @current-change="currentChangeHandle" :current-page="pageIndex"
                   :page-sizes="[10, 20, 50, 100]" :page-size="pageSize" :total="totalPage" layout="total, sizes, prev, pager, next, jumper"/>
    <!-- 弹窗, 新增 / 修改 -->
  </div>
</template>

<script>
  import listMixin from '@/mixins/list-mixins'
  export default {
    mixins: [listMixin],
    data () {
      return {
        mixinOptions: {
          dataUrl: '/admin/zyz/badge/trade-listing/page',
          deleteUrl: '/admin/zyz/badgeTradeListing/removeByIds'
        },
        dataForm: {
          name: null,
          sellerName: null,
          fromPlatform: null,
          publishDateStart: null,
          publishDateEnd: null
        }
      }
    },
    components: {
    },
    methods: {
      resetForm() {
        this.$refs['dataForm']?.resetFields()
        this.getDataList()
      }
    }
  }
</script>

<style lang="scss" scoped>
</style>
