<template>
  <el-dialog class="common-dialog" :title="!dataForm.id ? '新增' : '修改'" :visible.sync="visible"
             :close-on-click-modal="false" :modal-append-to-body="false" width="60%"
             @closed="handleDialogClosed">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmit()" label-width="120px">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="勋章名称" prop="name">
            <el-input v-model="dataForm.name" placeholder="请输入勋章名称" clearable/>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="兑换所需积分" prop="pointsRequired">
            <el-input-number v-model="dataForm.pointsRequired" controls-position="right" :min="0" :max="100"/>
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="勋章简介" prop="briefIntro">
            <el-input v-model="dataForm.briefIntro" placeholder="请输入勋章简介" clearable/>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="平台交易费率%" prop="transactionFeeRate">
            <el-input-number v-model="dataForm.transactionFeeRate" controls-position="right" :min="0" :max="100"/>
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-form-item label="顺序" prop="sortNum">
        <el-input-number v-model="dataForm.sortNum" controls-position="right" :min="0" style="width: 200px;"/>
      </el-form-item>
      
      <el-form-item label="是否置顶" prop="top">
        <el-radio-group v-model="dataForm.top">
          <el-radio :label="true">是</el-radio>
          <el-radio :label="false">否</el-radio>
        </el-radio-group>
      </el-form-item>
      
      <el-form-item label="勋章文件" prop="badgeFile">
        <el-upload
          class="badge-file-uploader"
          :action="uploadUrl"
          :headers="myHeaders"
          :show-file-list="false"
          :data="uploadData"
          :on-success="handleBadgeFileSuccess"
          :before-upload="beforeBadgeFileUpload"
          :limit="1"
          :on-exceed="handleExceed"
          :file-list="badgeFileList"
          accept=".gltf,.glb"
        >
          <el-button type="primary">上传勋章文件</el-button>
          <div slot="tip" class="el-upload__tip">只能上传一个.gltf/.glb格式的3D模型文件，且不超过20MB</div>
        </el-upload>
        
        <!-- 显示已上传的文件信息 -->
        <div v-if="dataForm.badgeFile" class="badge-file-info">
          <div class="file-name">
            <i class="el-icon-document"></i>
            <span>{{ getFileName(dataForm.badgeFile) }}</span>
          </div>
          <div class="file-actions">
            <el-button type="text" size="small" @click="previewBadgeFile">预览</el-button>
            <el-button type="text" size="small" @click="removeBadgeFile">删除</el-button>
          </div>
        </div>
      </el-form-item>

      <!-- 3D预览对话框 -->
      <model-viewer v-model="previewVisible" :model-url="previewUrl"></model-viewer>
      
      <el-form-item label="勋章介绍" prop="introduction">
        <teditor style="width: 100%;" :value="dataForm.introduction" :disabled="false" ref="teditor"
                 @changeEditorValue="changeAnswer"/>
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-radio-group v-model="dataForm.status">
          <el-radio :label="true">上架</el-radio>
          <el-radio :label="false">下架</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="预览图" prop="previewImage">
        <el-upload
          class="avatar-uploader"
          :action="uploadUrl"
          :headers="myHeaders"
          :show-file-list="false"
          :data="uploadData"
          :on-success="handlePreviewImageSuccess"
          :before-upload="beforePreviewImageUpload"
          accept="image/jpeg,image/png"
        >
          <img v-if="dataForm.previewImage" :src="$http.adornAttachmentUrl(dataForm.previewImage)" class="avatar">
          <i v-else class="el-icon-plus avatar-uploader-icon"></i>
          <div slot="tip" class="el-upload__tip" style="color: red">jpg/png格式图片，不超过2MB，建议尺寸500*300</div>
        </el-upload>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="closeDialog">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()" :disabled="dataFormLoading">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
  import _ from 'lodash'
  import editMixin from '@/mixins/edit-mixins'
  import Vue from 'vue'
  import Teditor from "@/components/tinymce/index.vue";
  
  export default {
    components: {Teditor},
    mixins: [editMixin],
    data () {
      return {
        editOptions: {
          initUrl: '/admin/zyz/badge',
          saveSuffix: 'saveAndCreateListing',
          updateSuffix: 'updateAndProcessListing',
          isUniteUrl: false
        },
        badgeFileList: [],
        dataForm: {},
        initForm: {
          id: null,
          version: null,
          name: null,
          briefIntro: null,
          pointsRequired: null,
          transactionFeeRate: null,
          sortNum: null,
          top: false,
          badgeFile: null,
          previewImage: null,
          introduction: null,
          status: null
        },
        dataRule: {
          name: [
            { required: true, message: '勋章名称不能为空', trigger: 'change' }
          ],
          briefIntro: [
            { required: true, message: '勋章简介不能为空', trigger: 'change' }
          ],
          pointsRequired: [
            { required: true, message: '兑换所需积分不能为空', trigger: 'change' }
          ],
          transactionFeeRate: [
            { required: true, message: '平台交易费率(%)不能为空', trigger: 'change' }
          ],
          sortNum: [
            { required: true, message: '顺序不能为空', trigger: 'change' }
          ],
          top: [
            { required: true, message: '是否置顶不能为空', trigger: 'change' }
          ],
          introduction: [
            { required: true, message: '勋章介绍不能为空', trigger: 'change' }
          ],
          status: [
            { required: true, message: '状态不能为空', trigger: 'change' }
          ]
        },
        uploadUrl: this.$http.adornUrl('/admin/oss/upload'),
        myHeaders: {Authorization: Vue.cookie.get('Authorization')},
        uploadData: {serverCode: 'LocalServer', media: false},
        previewVisible: false,
        previewUrl: ''
      }
    },
    methods: {
      init (id) {
        this.dataForm = _.cloneDeep(this.initForm)
        this.dataForm.id = id || null
        this.visible = true
        this.badgeFileList = []
        this.previewVisible = false
        this.previewUrl = ''
        this.changeAnswer('')
        this.$nextTick(() => {
          this.$refs['dataForm']?.resetFields()
          if (this.dataForm.id) {
            this.getData()
          }
        })
      },
      
      closeDialog() {
        this.visible = false
      },
      
      handleDialogClosed() {
        this.resetFormData()
      },
      
      resetFormData() {
        this.dataForm = _.cloneDeep(this.initForm)
        this.badgeFileList = []
        this.previewVisible = false
        this.previewUrl = ''
        this.$refs['dataForm']?.resetFields()
      },
      
      getFileName(path) {
        if (path) {
          return path.substring(path.lastIndexOf('/') + 1)
        }
        return ''
      },
      handleBadgeFileSuccess (res, file) {
        if (res.success) {
          this.dataForm.badgeFile = res.obj.path
          this.$refs['dataForm'].validateField('badgeFile')
          this.$message.success('上传成功')
        } else {
          this.$message.error('上传失败')
        }
      },
      removeBadgeFile () {
        this.dataForm.badgeFile = null
        this.$refs['dataForm'].validateField('badgeFile')
      },
      previewBadgeFile () {
        if (this.dataForm.badgeFile) {
          this.previewUrl = this.$http.adornAttachmentUrl(this.dataForm.badgeFile)
          this.previewVisible = true
        } else {
          this.$message.warning('没有可预览的文件')
        }
      },
      beforeBadgeFileUpload (file) {
        if (this.dataForm.badgeFile) {
          this.removeBadgeFile()
        }
        
        const isGLTF = file.name.endsWith('.gltf') || file.name.endsWith('.glb')
        const isLt20M = file.size / 1024 / 1024 < 20
        
        if (!isGLTF) {
          this.$message.error('只能上传.gltf或.glb格式的3D模型文件!')
        }
        if (!isLt20M) {
          this.$message.error('文件大小不能超过20MB!')
        }
        
        return isGLTF && isLt20M
      },
      handleExceed(files, fileList) {
        this.$message.warning(`只能上传 1 个文件，请先删除已有文件再上传新文件`)
      },
      handlePreviewImageSuccess(res, file) {
        if (res.success) {
          this.dataForm.previewImage = res.obj.path
          this.$message.success('上传成功')
        } else {
          this.$message.error('上传失败')
        }
      },
      beforePreviewImageUpload(file) {
        const isImage = file.type === 'image/jpeg' || file.type === 'image/png'
        const isLt2M = file.size / 1024 / 1024 < 2
        
        if (!isImage) {
          this.$message.error('上传图片只能是JPG/PNG格式!')
        }
        if (!isLt2M) {
          this.$message.error('上传图片大小不能超过2MB!')
        }
        
        return isImage && isLt2M
      },
      changeAnswer(html) {
        this.dataForm.introduction = html
      },
      
      dataFormSubmit() {
        this.$refs['dataForm'].validate((valid) => {
          if (valid) {
            this.clearErrors()
            if (!this.submitBeforeHandle()) {
              return
            }
            let submitUrl = this.editOptions.submitUrl || this.editOptions.initUrl
            if (!this.editOptions.isUniteUrl) {
              submitUrl = `${submitUrl}/${!this.dataForm.id ? this.editOptions.saveSuffix : this.editOptions.updateSuffix}`
            }
            this.dataFormLoading = true
            this.$http({
              url: this.$http.adornUrl(submitUrl),
              method: 'post',
              headers: { Authorization: Vue.cookie.get('Authorization') },
              data: this.dataForm
            }).then(({data}) => {
              if (data && data.code === 0) {
                this.$message({
                  message: '操作成功',
                  type: 'success',
                  duration: 1000,
                  onClose: () => {
                    this.dataFormLoading = false
                    this.resetFormData()
                    this.visible = false
                    if (this.editOptions.isEmit) {
                      this.$emit(this.editOptions.emit)
                    }
                  }
                })
                return
              }
              if (data && data.code === 303) {
                let errors = {}
                for (let it of data.obj) {
                  errors[`${it.field}`] = it.message
                }
                this.errors = _.cloneDeep(errors)
              } else {
                this.$message.error(data.msg)
              }
              this.dataFormLoading = false
            }).catch(() => {
              this.dataFormLoading = false
            })
          }
        })
      }
    }
  }
</script>

<style lang="scss" scoped>
.tips {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
}
.preview-container {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}
.badge-file-uploader {
  margin-bottom: 10px;
}
.badge-file-info {
  display: flex;
  align-items: center;
  border: 1px solid #ebeef5;
  padding: 8px 15px;
  border-radius: 4px;
  margin-top: 10px;
  background-color: #f8f8f8;
}
.file-name {
  display: flex;
  align-items: center;
  i {
    margin-right: 5px;
    color: #409EFF;
  }
}
.file-actions {
  margin-left: auto;
}

/* 预览图上传样式 */
.avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}
.avatar-uploader .el-upload:hover {
  border-color: #409EFF;
}
.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  line-height: 178px;
  text-align: center;
}
.avatar {
  width: 178px;
  height: 178px;
  display: block;
}
</style>
