<template>
  <div class="mod-list">
    <el-form :inline="true" :model="dataForm" ref="dataForm" @keyup.enter.native="getDataList()">
      <el-form-item prop="startTime" label="操作时间">
        <el-date-picker
          v-model="dataForm.startTime"
          type="date"
          placeholder="选择开始时间"
          value-format="yyyy-MM-dd">
        </el-date-picker>
      </el-form-item>
      <el-form-item prop="endTime" label="至">
        <el-date-picker
          v-model="dataForm.endTime"
          type="date"
          placeholder="选择结束时间"
          value-format="yyyy-MM-dd">
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-search" type="warning" @click="getDataList()">查询</el-button>
        <el-button @click="resetForm()" icon="el-icon-refresh-left">重置</el-button>
      </el-form-item>
    </el-form>
    
    <el-card class="statistics-card">
      <div slot="header" class="section-title">
        交易数量（时间范围内赠送数量、抵扣数量、积分兑换数量、批量授权数量）
      </div>
      <el-table :data="transactionData" border style="width: 100%;" v-loading="transactionLoading">
        <el-table-column prop="giftCount" header-align="center" align="center" label="赠送" />
        <el-table-column prop="recycleCount" header-align="center" align="center" label="抵扣" />
        <el-table-column prop="exchangeCount" header-align="center" align="center" label="积分兑换" />
        <el-table-column prop="grantCount" header-align="center" align="center" label="批量授权" />
        <el-table-column prop="total" header-align="center" align="center" label="合计" />
      </el-table>
    </el-card>
    
    <el-card class="statistics-card" style="margin-top: 20px;">
      <div slot="header" class="section-title">
        积分段兑换数量（仅统计志愿者通过积分兑换获得的勋章数，按勋章的积分段进行统计）
      </div>
      <el-table :data="pointsExchangeData" border style="width: 100%;" v-loading="pointsExchangeLoading">
        <el-table-column prop="range0To100Count" header-align="center" align="center" label="0-100" />
        <el-table-column prop="range101To200Count" header-align="center" align="center" label="101-200" />
        <el-table-column prop="rangeAbove200Count" header-align="center" align="center" label="200以上" />
        <el-table-column prop="total" header-align="center" align="center" label="合计" />
      </el-table>
    </el-card>
  </div>
</template>

<script>
  export default {
    data () {
      return {
        dataForm: {
          startTime: null,
          endTime: null
        },
        transactionData: [{ 
          giftCount: 0,
          recycleCount: 0,
          exchangeCount: 0,
          grantCount: 0,
          total: 0
        }],
        pointsExchangeData: [{
          range0To100Count: 0,
          range101To200Count: 0,
          rangeAbove200Count: 0,
          total: 0
        }],
        transactionLoading: false,
        pointsExchangeLoading: false
      }
    },
    created () {
      // 初始默认加载数据
      this.getDataList()
    },
    activated () {
      // 当页面被激活时加载数据
      this.getDataList()
    },
    methods: {
      // 获取统计数据
      getDataList () {
        this.getTransactionStatistics()
        this.getPointsExchangeStatistics()
      },
      
      // 获取交易数量统计
      getTransactionStatistics () {
        this.transactionLoading = true
        this.$http({
          url: this.$http.adornUrl('/admin/zyz/badge/transaction/statisticsTransaction'),
          method: 'post',
          data: this.$http.adornData({
            startTime: this.dataForm.startTime,
            endTime: this.dataForm.endTime
          })
        }).then(({data}) => {
          this.transactionLoading = false
          if (data && data.code === 0) {
            this.transactionData = [data.obj]
          } else {
            this.$message.error(data.msg || '获取交易数量统计失败')
          }
        }).catch(() => {
          this.transactionLoading = false
        })
      },
      
      // 获取积分兑换档数量统计
      getPointsExchangeStatistics () {
        this.pointsExchangeLoading = true
        this.$http({
          url: this.$http.adornUrl('/admin/zyz/badge/transaction/statisticsPointsExchange'),
          method: 'post',
          data: this.$http.adornData({
            startTime: this.dataForm.startTime,
            endTime: this.dataForm.endTime
          })
        }).then(({data}) => {
          this.pointsExchangeLoading = false
          if (data && data.code === 0) {
            this.pointsExchangeData = [data.obj]
          } else {
            this.$message.error(data.msg || '获取积分兑换档数量统计失败')
          }
        }).catch(() => {
          this.pointsExchangeLoading = false
        })
      },
      resetForm () {
        this.$refs['dataForm']?.resetFields()
        this.dataForm.startTime = null
        this.dataForm.endTime = null
        this.getDataList()
      }
    }
  }
</script>

<style lang="scss" scoped>
  .statistics-card {
    margin-bottom: 20px;
  }
  
  .section-title {
    font-size: 16px;
    font-weight: bold;
  }
</style> 
