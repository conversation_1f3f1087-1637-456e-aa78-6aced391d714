<template>
  <el-dialog
    :title="!dataForm.id ? '新增' : '修改'"
    :close-on-click-modal="false"
    :visible.sync="visible">
    <el-form :model="dataForm" ref="dataForm" @keyup.enter.native="dataFormSubmit()" label-width="100px">
      <el-form-item label="数据库类型" prop="databaseType">
        <el-select v-model="dataForm.databaseType" placeholder="请选择" @change="selectDatabase($event)">
          <el-option
            v-for="item in options"
            :key="item"
            :label="item"
            :value="item">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="数据库名称" prop="databaseName">
        <el-input v-model="dataForm.databaseName" placeholder="数据库名称"></el-input>
      </el-form-item>
      <el-form-item label="地址" prop="ipAddress">
        <el-input v-model="dataForm.ipAddress" placeholder="地址"></el-input>
      </el-form-item>
      <el-form-item label="驱动类" prop="driverClassName">
        <el-input v-model="dataForm.driverClassName" placeholder="驱动类"></el-input>
      </el-form-item>
      <el-form-item label="链接" prop="databaseUrl">
        <el-input v-model="dataForm.databaseUrl" placeholder="链接"></el-input>
      </el-form-item>
      <el-form-item label="用户名" prop="username">
        <el-input v-model="dataForm.username" placeholder="用户名"></el-input>
      </el-form-item>
      <el-form-item label="密码" prop="password">
        <el-input v-model="dataForm.password" placeholder="密码"></el-input>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()" v-preventReClick>保存</el-button>
    </span>
  </el-dialog>
</template>

<script>
  import editMixin from '@/mixins/edit-mixins'
  export default {
    mixins: [editMixin],
    data () {
      return {
        editOptions: {
          initUrl: '/admin/sysDatabase'
        },
        dataForm: {
          databaseType: '',
          databaseName: '',
          driverClassName: '',
          databaseUrl: '',
          username: '',
          password: '',
          ipAddress: ''
        },
        options: ['MySQL', 'Oracle', 'SQLServer', 'PostgreSQL']
      }
    },
    methods: {
      selectDatabase (val) {
        this.dataForm.databaseType = ''
        this.dataForm.databaseType = val
      }
    }
  }
</script>
