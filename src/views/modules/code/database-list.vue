<template>
  <div class="mod-config">
    <el-form :inline="true" :model="dataForm" @keyup.enter.native="getDataList()">
      <el-form-item label="数据库类型"  prop="databaseName">
        <el-select v-model="dataForm.databaseType" placeholder="请选择" clearable>
          <el-option
            v-for="item in options"
            :key="item"
            :label="item"
            :value="item">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button @click="getDataList()">查询</el-button>
        <el-button type="primary" @click="addOrUpdateHandle()">新增</el-button>
        <el-button type="primary" @click="deleteHandle()" :disabled="dataListSelections.length <= 0">批量删除</el-button>
      </el-form-item>
    </el-form>
    <el-table
      :data="dataList"
      border
      v-loading="dataListLoading"
      @selection-change="selectionChangeHandle"
      style="width: 100%;">
      <el-table-column
        type="selection"
        header-align="center"
        align="center"
        width="50">
      </el-table-column>
      <el-table-column
        prop="databaseType"
        header-align="center"
        align="center"
        label="数据库类型">
      </el-table-column>
      <el-table-column
        prop="databaseName"
        header-align="center"
        align="center"
        label="数据库名称">
      </el-table-column>
      <el-table-column
        prop="ipAddress"
        header-align="center"
        align="center"
        label="地址"
        show-overflow-tooltip>
      </el-table-column>
      <el-table-column
        prop="driverClassName"
        header-align="center"
        align="center"
        label="驱动类"
        show-overflow-tooltip>
      </el-table-column>
      <el-table-column
        prop="databaseUrl"
        header-align="center"
        align="center"
        label="链接"
        show-overflow-tooltip>
      </el-table-column>
      <el-table-column
        prop="username"
        header-align="center"
        align="center"
        label="用户名">
      </el-table-column>
      <el-table-column
        prop="password"
        header-align="center"
        align="center"
        label="密码">
      </el-table-column>
      <el-table-column
        fixed="right"
        header-align="center"
        align="center"
        width="150"
        label="操作">
        <template slot-scope="scope">
          <el-button type="text" size="small" @click="addOrUpdateHandle(scope.row.id)" class="btn-control">修改</el-button>
          <el-button type="text" size="small" @click="deleteHandle(scope.row.id)" class="btn-control">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      @size-change="sizeChangeHandle"
      @current-change="currentChangeHandle"
      :current-page="pageIndex"
      :page-sizes="[10, 20, 50, 100]"
      :page-size="pageSize"
      :total="totalPage"
      layout="total, sizes, prev, pager, next, jumper">
    </el-pagination>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"></add-or-update>
  </div>
</template>

<script>
  import AddOrUpdate from './database-add-or-update'
  import listMixin from '@/mixins/list-mixins'
  export default {
    mixins: [listMixin],
    data () {
      return {
        mixinOptions: {
          dataUrl: '/admin/sysDatabase/pages',
          deleteUrl: '/admin/sysDatabase/removeByIds'
        },
        dataForm: {
          databaseType: ''
        },
        options: ['MySQL', 'Oracle', 'SQLServer', 'PostgreSQL']
      }
    },
    components: {
      AddOrUpdate
    }
  }
</script>
