<template>
  <el-dialog :visible.sync="visible" :append-to-body="true">
    <span slot="title" class="dialog-title" style="font-size: 18px; font-weight: 400; display: flex;width: 100%; justify-content: space-between; align-items: center;">
      <span>修改密码</span>
      <span v-if="over90DaysNotChangePwd" style="position: absolute;left: 50%;transform: translateX(-50%);font-size: 16px;color: red">系统检测到您超过90天未修改密码，建议您修改！</span>
    </span>
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmit()"
             label-width="80px">
      <el-form-item label="账号">
        <span>{{ dataForm.username }}</span>
      </el-form-item>
      <el-form-item label="原密码" prop="password">
        <el-input type="password" v-model="dataForm.password"></el-input>
      </el-form-item>
      <el-form-item label="新密码" prop="newPassword">
        <el-input type="password" v-model="dataForm.newPassword"></el-input>
      </el-form-item>
      <el-form-item label="确认密码" prop="confirmPassword">
        <el-input type="password" v-model="dataForm.confirmPassword"></el-input>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import {clearLoginInfo} from '@/utils'

export default {
  data() {
    var validateConfirmPassword = (rule, value, callback) => {
      if (this.dataForm.newPassword !== value) {
        callback(new Error('确认密码与新密码不一致'))
      } else {
        callback()
      }
    };
    var validateNewPassword = (rule, value, callback) => {
      let reg = /(?=.*[A-Z])(?=.*[a-z])(?=.*[0-9])(?=.*[\W_]).{8,20}/
      if (!value || value === '') {
        callback(new Error("新密码不能为空"));
      } else {
        if (!reg.test(value)) {
          callback(new Error('密码须包含大小写、数字、特殊字符，且长度8~20位'))
        } else if (value === this.dataForm.password) {
          callback(new Error('新密码不能与原密码相同'))
        } else {
          callback()
        }
      }
    }
    return {
      visible: false,
      needLogout: false,
      over90DaysNotChangePwd: false,
      dataForm: {
        password: '',
        newPassword: '',
        confirmPassword: '',
        username: ''
      },
      dataRule: {
        password: [
          {required: true, message: '原密码不能为空', trigger: 'blur'}
        ],
        newPassword: [
          {required: true, validator: validateNewPassword, trigger: 'blur'}
        ],
        confirmPassword: [
          {required: true, message: '确认密码不能为空', trigger: 'blur'},
          {validator: validateConfirmPassword, trigger: 'blur'}
        ]
      }
    }
  },
  computed: {
    mainTabs: {
      get() {
        return this.$store.state.common.mainTabs
      },
      set(val) {
        this.$store.commit('common/updateMainTabs', val)
      }
    }
  },
  methods: {
    // 初始化
    init(username, needLoginOut = false, over90DaysNotChangePwd = false) {
      this.visible = true
      this.needLogout = needLoginOut
      this.over90DaysNotChangePwd = over90DaysNotChangePwd
      this.dataForm.username = username
      this.$nextTick(() => {
        this.$refs['dataForm']?.resetFields()
      })
    },
    // 表单提交
    dataFormSubmit() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.$http({
            url: this.$http.adornUrl('/admin/user/editPassWord'),
            method: 'post',
            data: this.$http.adornData({
              'username': this.dataForm.username,
              'oldPassWord': this.dataForm.password,
              'newPassWord': this.dataForm.newPassword
            })
          }).then(({data}) => {
            if (data && data.code === 0) {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 1500,
                onClose: () => {
                  this.visible = false
                  this.$nextTick(() => {
                    if (!this.needLogout) {
                      return
                    }
                    this.mainTabs = []
                    clearLoginInfo()
                    this.$router.replace({name: 'login'})
                  })
                }
              })
            } else {
              this.$message.error(data.msg)
            }
          })
        }
      })
    }
  }
}
</script>

