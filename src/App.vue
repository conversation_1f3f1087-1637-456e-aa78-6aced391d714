<template>
  <transition name="fade">
    <router-view></router-view>
  </transition>
</template>

<script>
export default {
  name: "App",
  data() {
    return {
      lastTime: null, // 最后一次点击的时间
      currentTime: null, // 当前点击的时间
      timeOut: 30 * 60 * 1000, // 设置超时时间:30分钟
      timeInterval: "",
      needReload: false
    };
  },

  created() {
      // let hasAuthorization = this.$cookie.get('Authorization')
      // console.log('hasAuthorization'+hasAuthorization)
      // let urlStatus = window.location.pathname.includes( 'volunteer_fas')
      // console.log('urlStatus'+window.location.pathname)
      // console.log(' window.location.href.startsWith'+window.location.href.startsWith('https://sungent.fyxmt.com'))
      // if (!hasAuthorization &&  urlStatus  && window.location.href.startsWith('https://sungent.fyxmt.com') ) {
      //     console.log('跳转')
      //     this.$router.push('/loginfas')
      // }
    window.onload = () => {
      window.document.onclick = () => {
        this.lastTime = new Date().getTime();
      }
      if (!this.timeInterval) {
        // 30s检测一次
        console.info('创建页面长时间不操作的监听')
        //默认当前时间
        this.lastTime = new Date().getTime()
        this.timeInterval = setInterval(this.isTimeOut, 30 * 1000);
      }

    }
  },
  methods: {
    isTimeOut() {
      // 登录页无需刷新
      if (this.$router.currentRoute.path === '/login') {
        return;
      }
      if (this.needReload) {
        return
      }
      this.currentTime = new Date().getTime(); // 当前时间
      // 判断上次最后一次点击的时间和这次点击的时间间隔是否大于规定的时间,例:10分钟
      if (this.currentTime - this.lastTime > this.timeOut) {
        this.needReload = true
        console.warn('超过一定时间没有使用页面，需要刷新')
        //判断是否为登录状态,只有登录状态下才调用方法
        //先返回首页,在给提示,可以根据具体需求调整
        this.$alert("检测到您长时间未操作系统,请刷新页面再使用系统", "温馨提示", {
          type: 'warning',
          confirmButtonText: "确定",
          callback: () => {
            window.location.reload()
          },
        })
      }
    },
  },
}
</script>

<style>
body {
  overflow: auto !important;
}

.el-table__fixed, .el-table__fixed-right {
  height: 100% !important;
}

body .el-table th.gutter {
  display: table-cell !important;
}

body .el-table colgroup.gutter {
  display: table-cell !important;
}
</style>
