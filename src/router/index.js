/**
 * 全站路由配置
 *
 * 建议:
 * 1. 代码中路由统一使用name属性跳转(不使用path属性)
 */
import Vue from 'vue'
import Router from 'vue-router'
import http from '@/utils/httpRequest'
import {isURL} from '@/utils/validate'
import {clearLoginInfo} from '@/utils'
import store from '@/store'

Vue.use(Router)

// 开发环境不使用懒加载, 因为懒加载页面太多的话会造成webpack热更新太慢, 所以只有生产环境使用懒加载
const _import = require('./import-' + process.env.NODE_ENV)

// 全局路由(无需嵌套上左右整体布局)
const globalRoutes = [
  {path: '/404', component: _import('common/404'), name: '404', meta: {title: '404未找到'}},
  {path: '/login', component: _import('common/login'), name: 'login', meta: {title: '登录'}},
  // {path: '/login/', component: _import('common/login'), name: 'login', meta: {title: '登录'}},
  {path: '/login-wait', component: _import('common/login-wait'), name: 'login-wait', meta: {title: '登录中'}},
  {path: '/login-zsq-wait', component: _import('common/login-zsq-wait'), name: 'login-zsq-wait', meta: {title: '登录中'}},
  // {path: '/login-wait/', component: _import('common/login-wait'), name: 'login-wait', meta: {title: '登录中'}}
  {path: '/loginfas', component: _import('common/loginfas'), name: 'loginfas', meta: {title: 'fas登录'}},
  {path: '/common/blank', component: _import('common/blank'), name: 'blank', meta: {title: 'zwt登录'}},
]

// 主入口路由(需嵌套上左右整体布局)
const mainRoutes = {
  path: '/',
  component: _import('main'),
  name: 'main',
  redirect: {name: 'home'},
  meta: {title: '主入口整体布局'},
  children: [
    // 通过meta对象设置路由展示方式
    // 1. isTab: 是否通过tab展示内容, true: 是, false: 否
    // 2. iframeUrl: 是否通过iframe嵌套展示内容, '以http[s]://开头': 是, '': 否
    // 提示: 如需要通过iframe嵌套展示内容, 但不通过tab打开, 请自行创建组件使用iframe处理!
    {path: '/home', component: _import('common/home'), name: 'home', meta: {title: '首页'}},
    {path: '/theme', component: _import('common/theme'), name: 'theme', meta: {title: '主题'}}
  ],
  beforeEnter (to, from, next) {
    let token = Vue.cookie.get('Authorization')
    if (!token || !/\S/.test(token)) {
      clearLoginInfo()
      next({name: 'login'})
    }
    next()
  }
}

const router = new Router({
  mode: 'hash',
  scrollBehavior: () => ({y: 0}),
  isAddDynamicMenuRoutes: false, // 是否已经添加动态(菜单)路由
  routes: globalRoutes.concat(mainRoutes)
})

router.beforeEach((to, from, next) => {
  // 添加动态(菜单)路由
  // 1. 已经添加 or 全局路由, 直接访问
  // 2. 获取菜单列表, 添加并保存本地存储
  if (router.options.isAddDynamicMenuRoutes || fnCurrentRouteType(to, globalRoutes) === 'global') {
    if (to.name === 'login' ) {
      let token = Vue.cookie.get('Authorization')
      if (!token || !/\S/.test(token)) {
        clearLoginInfo()
        next()
      } else {
        http({
          url: http.adornUrl('/admin/user/checkToken'),
          method: 'get'
        }).then(({data}) => {
          if (data && data.code === 0) {
            router.push({ name: from.name })
          }
        })
      }
    } else {
      next()
    }
  } else {
    // 提取后的菜单刷新方法
    refreshMenuList()
        .then(() => next({ ...to, replace: true }))
        .catch(error => {
          console.error('路由初始化失败:', error)
          clearLoginInfo()
          router.push({ name: 'login' })
        })
  }
})

// 封装菜单刷新方法
function refreshMenuList() {
  return new Promise(async (resolve, reject) => {
    try {
      // 先加载主菜单
      const mainMenuResponse = await http({
        url: http.adornUrl('/admin/user/userMenusAndActionAuthorities'),
        method: 'get',
        params: http.adornParams({ type: 'admin' })
      })

      // 检查主菜单是否加载成功
      if (mainMenuResponse.data.code !== 0) {
        reject(new Error('主菜单获取失败'))
        return
      }

      // 获取主菜单数据
      const menuList1 = mainMenuResponse.data.obj.menuList || []
      const permissions1 = mainMenuResponse.data.obj.permissions || []

      // 预处理SSO菜单：查找wmcj菜单、添加占位符或合并缓存菜单
      const { menuList: processedMenuList, permissions: processedPermissions }
          = preprocessSsoMenu(menuList1, permissions1)

      // 先用处理后的菜单初始化路由
      fnAddDynamicMenuRoutes(processedMenuList)

      // 更新到store
      store.commit('common/updateMenuList', processedMenuList)
      store.commit('common/updatePermissions', processedPermissions)
      store.commit('common/updateDynamicMenuRoutes', mainRoutes.children || [])

      // 记录路由状态
      router.options.isAddDynamicMenuRoutes = true

      // 立即resolve，不等待SSO菜单加载
      resolve()

      // 异步加载最新的SSO菜单，不影响主流程
      loadSsoMenuAndMerge().catch(error => {
        console.error('SSO菜单处理失败，但不影响主菜单使用:', error)
      })

    } catch (error) {
      reject(error)
    }
  })
}

// 加载SSO菜单并合并到主菜单
function loadSsoMenuAndMerge() {
  return new Promise(async (resolve, reject) => {
    try {
      const ssoMenuResponse = await http({
        url: http.adornUrl('/admin/sso/wmyq/resource'),
        method: 'post',
        params: http.adornParams({ groupType: 0 })
      }).catch(e => {
        console.error('SSO菜单获取失败，降级处理:', e)
        return { data: { code: -1, obj: [] } }
      })

      const menuList2 = ssoMenuResponse.data.code === 0
          ? processIframeMenuTree(ssoMenuResponse.data.obj.menuList || [])
          : []

      const permissions2 = ssoMenuResponse.data.code === 0
          ? ssoMenuResponse.data.obj.permissions || []
          : []

      // 无论是否有新菜单，都保存到本地缓存中
      // 即使为空数组也保存，这样可以避免下次刷新再请求
      try {
        localStorage.setItem('ssoMenuCache', JSON.stringify({
          menuList: menuList2,
          permissions: permissions2,
          timestamp: new Date().getTime()
        }))
      } catch (e) {
        console.error('缓存SSO菜单到本地存储失败:', e)
      }

      if (menuList2.length === 0) {
        resolve()
        return
      }

      // 从store获取当前菜单数据
      const currentMenuList = JSON.parse(JSON.stringify(store.state.common.menuList))

      // 找到路径为wmcj的菜单
      const wmcjMenuIndex = currentMenuList.findIndex(menu => menu.url === 'wmcj')

      if (wmcjMenuIndex !== -1) {
        // 处理菜单数据
        const processedMenuList = [...currentMenuList]

        // 先过滤掉加载中的占位菜单和所有SSO菜单（避免重复）
        if (processedMenuList[wmcjMenuIndex].children) {
          processedMenuList[wmcjMenuIndex].children = processedMenuList[wmcjMenuIndex].children.filter(
              item => item.id !== 'loading-placeholder' && !String(item.id).startsWith('o-')
          )
        } else {
          processedMenuList[wmcjMenuIndex].children = []
        }

        // 合并新的菜单
        processedMenuList[wmcjMenuIndex].children = [
          ...processedMenuList[wmcjMenuIndex].children,
          ...menuList2
        ]

        // 合并权限
        const newPermissions = [
          ...store.state.common.permissions,
          ...permissions2
        ]

        // 更新路由
        fnAddDynamicMenuRoutes(processedMenuList)

        // 通过mutation更新store
        store.commit('common/updateMenuList', processedMenuList)
        store.commit('common/updatePermissions', newPermissions)
        store.commit('common/updateDynamicMenuRoutes', mainRoutes.children || [])
      }

      resolve()
    } catch (error) {
      console.error('SSO菜单处理异常:', error)
      reject(error)
    }
  })
}

// 递归处理树形菜单的方法
function processIframeMenuTree(menuItems) {
  return menuItems.map(item => {
    // 深拷贝菜单项避免污染原始数据
    const newItem = {
      ...item,
      id: 'o-' + item.id, // 使用字符串拼接而非数值相加，确保ID唯一性
      children: item.children ? [...item.children] : null
    }
    if(newItem.url) {
      // console.log('newItem.url', newItem.url)
      if (newItem.url.startsWith('/')) {
        newItem.url = newItem.url.replace('/', '')
      }
      newItem.url = newItem.url.replace('/', '-')
    }

    // 处理URL拼接
    if (newItem.url?.trim()) {
      const baseUrl = window.SITE_CONFIG['ssoBaseUrl']
      // 处理URL格式规范
      newItem.url = baseUrl.endsWith('/')
          ? `${baseUrl}${newItem.url.replace(/^\//, '')}`
          : `${baseUrl}/${newItem.url.replace(/^\//, '')}`
    }
    // 递归处理子菜单
    if (newItem.children?.length) {
      newItem.children = processIframeMenuTree(newItem.children)
    }

    return newItem
  })
}

/**
 * 判断当前路由类型, global: 全局路由, main: 主入口路由
 * @param {*} route 当前路由
 */
function fnCurrentRouteType (route, globalRoutes = []) {
  var temp = []
  for (var i = 0; i < globalRoutes.length; i++) {
    if (route.path === globalRoutes[i].path) {
      return 'global'
    } else if (globalRoutes[i].children && globalRoutes[i].children.length >= 1) {
      temp = temp.concat(globalRoutes[i].children)
    }
  }
  return temp.length >= 1 ? fnCurrentRouteType(route, temp) : 'main'
}

/**
 * 从localStorage获取缓存的SSO菜单
 * @returns {Object} 包含menuList和permissions的对象
 */
function getSsoMenuCache() {
  let menuList = []
  let permissions = []
  try {
    const cachedSsoData = JSON.parse(localStorage.getItem('ssoMenuCache') || '{}')
    if (cachedSsoData.menuList && Array.isArray(cachedSsoData.menuList)) {
      menuList = cachedSsoData.menuList
      permissions = cachedSsoData.permissions || []
    }
  } catch (e) {
    console.error('读取缓存的SSO菜单失败:', e)
  }
  return { menuList, permissions }
}

/**
 * 预处理SSO菜单：查找wmcj菜单、添加占位符和合并缓存菜单
 * @param {Array} menuList 主菜单列表
 * @param {Array} mainPermissions 主菜单权限列表
 * @returns {Object} 包含处理后的菜单列表和合并后的权限
 */
function preprocessSsoMenu(menuList, mainPermissions = []) {
  // 检查当前路由是否以i-开头(iframe页面)
  const currentPath = window.location.hash.slice(1).split('?')[0] // 获取#后面的路径部分，去掉查询参数
  const isIframePage = currentPath.startsWith('/i-')

  // 找到wmcj菜单
  const wmcjMenuIndex = menuList.findIndex(menu => menu.url === 'wmcj')
  if (wmcjMenuIndex === -1) {
    return { menuList, permissions: mainPermissions }
  }

  // 确保有children数组
  if (!menuList[wmcjMenuIndex].children) {
    menuList[wmcjMenuIndex].children = []
  }

  // 尝试从localStorage获取缓存的SSO菜单，仅当当前是iframe页面时
  let cachedSsoMenus = []
  let cachedSsoPermissions = []

  if (isIframePage) {
    const cacheResult = getSsoMenuCache()
    cachedSsoMenus = cacheResult.menuList
    cachedSsoPermissions = cacheResult.permissions
  }

  // 如果有缓存的SSO菜单，合并缓存菜单
  if (cachedSsoMenus.length > 0) {
    // 先过滤掉加载中占位和可能存在的旧SSO菜单
    menuList[wmcjMenuIndex].children = menuList[wmcjMenuIndex].children.filter(
        item => item.id !== 'loading-placeholder' && !String(item.id).startsWith('o-')
    )

    // 合并缓存的SSO菜单
    menuList[wmcjMenuIndex].children = [
      ...menuList[wmcjMenuIndex].children,
      ...cachedSsoMenus
    ]
  } else {
    // 如果没有缓存菜单，添加加载中占位菜单
    menuList[wmcjMenuIndex].children.push({
      id: 'loading-placeholder',
      name: '菜单加载中...',
      url: '',
      icon: 'loading',  // 这里可以使用你的loading图标
      type: 2,
      parentId: menuList[wmcjMenuIndex].id,
      children: null
    })
  }

  // 合并权限
  const mergedPermissions = [...mainPermissions, ...cachedSsoPermissions]

  return {
    menuList,
    permissions: mergedPermissions
  }
}

/**
 * 添加动态(菜单)路由
 * @param {*} menuList 菜单列表
 * @param {*} routes 递归创建的动态(菜单)路由
 */
function fnAddDynamicMenuRoutes (menuList = [], routes = [], replaceUrl = false) {
  var temp = []
  for (var i = 0; i < menuList.length; i++) {
    if (menuList[i].children && menuList[i].children.length >= 1) {
      temp = temp.concat(menuList[i].children)
    } else if (menuList[i].url && /\S/.test(menuList[i].url)) {
      menuList[i].url = menuList[i].url.replace(/^\//, '')
      var route = {
        path: menuList[i].url.replace('/', '-'),
        component: null,
        name: menuList[i].url.replace('/', '-'),
        meta: {
          menuId: menuList[i].id,
          title: menuList[i].name,
          isDynamic: true,
          isTab: true,
          iframeUrl: ''
        }
      }
      // 非根节点
      // todo 前端帮忙看下怎么实现新窗口打开，我不会-~
      // url以http[s]://开头, 通过iframe展示
      if (isURL(menuList[i].url)) {
        route['path'] = `i-${menuList[i].id}`
        route['name'] = `i-${menuList[i].id}`
        route['meta']['iframeUrl'] = menuList[i].url
      } else {
        try {
          route['component'] = _import(`modules/${menuList[i].url}`) || null
        } catch (e) {
        }
      }
      routes.push(route)
    }
  }
  if (temp.length >= 1) {
    fnAddDynamicMenuRoutes(temp, routes)
  } else {
    mainRoutes.name = 'main-dynamic'
    mainRoutes.children = routes
    router.addRoutes([
      mainRoutes,
      {path: '*', redirect: {name: '404'}}
    ])
    sessionStorage.setItem('dynamicMenuRoutes', JSON.stringify(mainRoutes.children || '[]'))
  }
}

export default router
