/**  水印添加方法  */
const setWatermark = (str1, str2) => {
  const id = 'page-main'

  let removeNode = document.getElementById(id)
  if (removeNode !== null) {
    document.body.removeChild(removeNode)
  }

  const can = document.createElement('canvas')
  // 设置canvas画布大小
  can.width = 350
  can.height = 250

  const cans = can.getContext('2d')
  cans.rotate((-30 * Math.PI) / 180) // 水印旋转角度
  cans.font = '35px Vedana' //水印的字体大小
  cans.fillStyle = '#797979' //水印的字体颜色
  cans.textAlign = 'center' //水印的位置
  cans.textBaseline = 'Middle'
  cans.fillText(str1, can.width / 2, can.height) // 水印在画布的位置x，y轴
  cans.fillText(str2, can.width / 2, can.height + 50)

  const div = document.createElement('div')
  div.id = id
  div.style.pointerEvents = 'none'
  div.style.top = '100px'
  div.style.left = '100px'
  div.style.opacity = '0.05'
  div.style.position = 'fixed'
  div.style.zIndex = '100000'
  div.style.width = document.documentElement.clientWidth + 'px'
  div.style.height = document.documentElement.clientHeight + 'px'
  div.style.background =
    'url(' + can.toDataURL('image/png') + ') left top repeat'
  document.body.appendChild(div)
  return id
}

// 添加水印方法
export const setWaterMark = (str1, str2) => {
  let id = setWatermark(str1, str2)
  if (document.getElementById(id) === null) {
    id = setWatermark(str1, str2)
  }
}

// 移除水印方法
export const removeWatermark = () => {
  const id = '1.23452384164.123412415'
  let removeNode = document.getElementById(id)
  if (removeNode !== null) {
    removeNode.parentNode.removeChild(removeNode)
  }
}
