import Vue from 'vue'
import axios from 'axios'
import router from '@/router'
import qs from 'qs'
import merge from 'lodash/merge'
import {clearLoginInfo} from '@/utils'
import {Message, Notification} from 'element-ui'
import {encrypt} from "@/utils/cryptoUtil";

const http = axios.create({
  timeout: 1000 * 120,
  withCredentials: true,
  headers: {
    'Content-Type': 'application/json; charset=utf-8'
  }
})

/**
 * 请求拦截
 */
http.interceptors.request.use(config => {
  if (!config.url.endsWith('fykj/login') && !config.url.endsWith('fykj/refresh_token')) {
    config.headers['Authorization'] = Vue.cookie.get('Authorization') // 请求头带上token
  }
  return config
}, error => {
  return Promise.reject(error)
})

/**
 * 响应拦截
 */
let errorLock = false
http.interceptors.response.use(async(response) => {
  let data = response.data
  if (data && data.code === 40101) {
    return refreshTokenHandle(data.msg, response)
  }
  return response
}, error => {
  // 处理重复弹框，当服务不可用时，一个页面可能会调用多个接口，会出现重复弹窗，加个锁处理下
  if (!errorLock) {
    errorLock = true
  } else {
    return
  }
  Notification.error({
    title: '错误',
    message: '当前服务不可用，请稍后再试',
    onClose: () => {
      errorLock = false
    }
  })
  return Promise.reject(error)
})

/**
 * 请求地址处理
 * @param {*} actionName action方法名称
 */
http.adornUrl = (actionName) => {
  // 非生产环境 && 开启代理, 接口前缀统一使用[/proxyApi/]前缀做代理拦截!
  return (process.env.NODE_ENV !== 'production' && process.env.OPEN_PROXY ? '/proxyApi/' : window.SITE_CONFIG.baseUrl) + actionName
}

// /**
//  * 请求地址处理
//  * @param {*} actionName action方法名称
//  */
// http.adornFujianUrl = (actionName) => {
//   // 非生产环境 && 开启代理, 接口前缀统一使用[/proxyApi/]前缀做代理拦截!
//   return (process.env.NODE_ENV !== 'production' && process.env.OPEN_PROXY ? '/proxyApi/' : window.SITE_CONFIG.fujianUrl) + actionName
// }

/**
 * get请求参数处理
 * @param {*} params 参数对象
 * @param {*} openDefaultParams 是否开启默认参数?
 */
http.adornParams = (params = {}, openDefaultParams = true) => {
  var defaults = {
    't': new Date().getTime()
  }
  return openDefaultParams ? merge(defaults, params) : params
}

/**
 * 请求地址处理
 * @param {*} actionName action方法名称
 */
http.adornAttachmentUrl = (actionName) => {
  // 非生产环境 && 开启代理, 接口前缀统一使用[/proxyApi/]前缀做代理拦截!
  return (process.env.NODE_ENV !== 'production' && process.env.OPEN_PROXY ? '/proxyApi/' : window.SITE_CONFIG.attachmentUrl) + actionName
}


/**
 * post请求数据处理
 * @param {*} data 数据对象
 * @param {*} openDefaultData 是否开启默认数据?
 * @param {*} contentType 数据格式
 *  json: 'application/json; charset=utf-8'
 *  form: 'application/x-www-form-urlencoded; charset=utf-8'
 */
http.adornData = (data = {}, openDefaultData = true, contentType = 'json') => {
  let defaults = {
    't': new Date().getTime()
  }
  data = openDefaultData ? merge(defaults, data) : data
  if (contentType === 'file') { // 带文件上传
    let form = new FormData()
    for (let key in data) {
      form.append(key, data[key])
    }
    return form
  }
  return contentType === 'json' ? JSON.stringify(data) : qs.stringify(data)
}

let refreshTokenEnable = true, requestArr = []
const refreshTokenHandle = async (msg, res) => {
  const tokenInfo = JSON.parse(sessionStorage.getItem('oAuthToken'))
  const rToken = tokenInfo ? tokenInfo.refresh_token : null
  if (!rToken) {
    Vue.prototype.$message.error(msg)
    clearLoginInfo()
    router.push({name: 'login'})
    return
  }
  const config = res.config
  if (refreshTokenEnable) {
    refreshTokenEnable = false
    try {
      //刷新token
      const data = await refreshToken(rToken)
      if (data && data.code === 0) {
        // Message.success('token刷新成功！')
        console.info('token刷新成功')
        Vue.cookie.set('Authorization', data.obj.token_type + ' ' + data.obj.access_token)
        http.defaults.headers['Authorization'] = Vue.cookie.get('Authorization')
        sessionStorage.setItem('oAuthToken', JSON.stringify(data.obj))
        // 执行完更新操作,重新执行未执行请求
        requestArr.forEach((execute) => execute(Vue.cookie.get('Authorization')))
        // 置空队列数组
        requestArr = []
        return http(config)
      } else {
        Message.error(msg)
        clearLoginInfo()
        router.push({name: 'login'})
      }
    } catch {
      clearLoginInfo()
      router.push({name: 'login'})
    } finally {
      refreshTokenEnable = true
    }
  } else {
    // 通过异步将并行的接口加入队列中，等上面更新完token接口再执行队列
    return new Promise((resolve) => {
      // 用函数的方式将相应数据resolve出去，执行函数就能得到响应结果
      requestArr.push(() => {
        config.headers['Authorization'] = Vue.cookie.get('Authorization')
        resolve(http(config))
      })
    })
  }
  return res
}

const refreshToken = function (rToken) {
  return new Promise((resolve, reject)=>{
    http({
      url: http.adornUrl('/fykj/refresh_token'),
      method: 'post',
      data: http.adornData({
        'refreshToken': rToken
      })
    }).then(({data}) => {
      resolve(data);
    }).catch(err => {
      reject(err)
    })
  })
}

export default http
