import Vue from 'vue'
import router from '@/router'
import store from '@/store'
import _ from 'lodash'
import http from '@/utils/httpRequest'

/**
 * 获取uuid
 */
export function uuid() {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, c => {
        return (c === 'x' ? (Math.random() * 16 | 0) : ('r&0x3' | '0x8')).toString(16)
    })
}

/**
 * 是否有权限
 * @param {*} key
 */
export function isAuth(key) {
    return JSON.parse(sessionStorage.getItem('permissions') || '[]').indexOf(key) !== -1 || false
}

/**
 * 树形数据转换
 * @param {*} data
 * @param {*} id
 * @param {*} pid
 */
export function treeDataTranslate(data, id = 'id', pid = 'parentId') {
    var res = []
    var temp = {}
    for (var i = 0; i < data.length; i++) {
        temp[data[i][id]] = data[i]
    }
    for (var k = 0; k < data.length; k++) {
        if (temp[data[k][pid]] && data[k][id] !== data[k][pid]) {
            if (!temp[data[k][pid]]['children']) {
                temp[data[k][pid]]['children'] = []
            }
            data[k]['_level'] = setInitialLevel(temp, temp[data[k][pid]])
            temp[data[k][pid]]['children'].push(data[k])
            temp[data[k][pid]]['hasChildren'] = true
        } else {
            res.push(data[k])
        }
    }
    return res
}

export function getFathersById(id, data, prop = 'code') {
    var arrRes = []
    const rev = (data, nodeId) => {
        for (var i = 0, length = data.length; i < length; i++) {
            const node = data[i]
            if (node[prop] === nodeId) {
                arrRes.unshift(node[prop])
                return true
            } else {
                if (node.children && node.children.length) {
                    if (rev(node.children, nodeId)) {
                        arrRes.unshift(node[prop])
                        return true
                    }
                }
            }
        }
        return false
    }
    rev(data, id)
    return arrRes
}

export function setInitialLevel(temp, data, pid = 'parentId') {
    if (!data['_level']) {
        if (!data[pid]) {
            data['_level'] = 1
            return data['_level'] + 1
        } else {
            data['_level'] = setInitialLevel(temp, temp[data[pid]])
            return data['_level'] + 1
        }
    } else {
        return data['_level'] + 1
    }
}

/**
 * 清除登录信息
 */
export function clearLoginInfo() {
    Vue.cookie.delete('Authorization')
    store.commit('resetStore')
    router.options.isAddDynamicMenuRoutes = false
}

/**
 * 表单重置
 */
export function resetForm(refName) {
    let ref = refName || 'dataForm'
    if (this.$refs[ref]) {
        this.$refs[ref]?.resetFields()
    }
}

/**
 * 通用下载方法
 */
export function downloadExcel(data, fileName, type = '.xlsx') {
    if (!data) {
        this.$message.warning('文件下载失败')
        return
    }
    if (typeof window.navigator.msSaveBlob !== 'undefined') {
        window.navigator.msSaveBlob(new Blob([data], {type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8'}), fileName + type)
    } else {
        let url = window.URL.createObjectURL(new Blob([data], {type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8'}))
        let link = document.createElement('a')
        link.style.display = 'none'
        link.href = url
        link.setAttribute('download', fileName + type)
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link) // 下载完成移除元素
        window.URL.revokeObjectURL(url) // 释放掉blob对象
    }
}

export function downloadFile(content, fileName) {
    let base64ToBlob = function (code) {
        let parts = code.split(';base64,')
        let contentType = parts[0].split(':')[1]
        let raw = window.atob(parts[1])
        let rawLength = raw.length
        let uInt8Array = new Uint8Array(rawLength)
        for (let i = 0; i < rawLength; ++i) {
            uInt8Array[i] = raw.charCodeAt(i)
        }
        return new Blob([uInt8Array], {
            type: contentType
        })
    }
    let aLink = document.createElement('a')
    let blob = base64ToBlob(content) // new Blob([content]);
    let evt = document.createEvent('HTMLEvents')
    evt.initEvent('click', true, true) // initEvent 不加后两个参数在FF下会报错  事件类型，是否冒泡，是否阻止浏览器的默认行为
    aLink.download = fileName
    aLink.href = URL.createObjectURL(blob)
    aLink.click()
}

export function fileDownload(filepath, fileName) {
    if (filepath.indexOf('http') < 0) {
        filepath = http.adornAttachmentUrl(filepath)
    }
    const x = new window.XMLHttpRequest();
    x.open('GET', filepath, true);
    x.responseType = 'blob';
    x.onload = () => {
        const url = window.URL.createObjectURL(x.response);
        const a = document.createElement('a');
        a.href = url;
        a.download = fileName;
        a.click();
    };
    x.send();
}
