/**
 * 邮箱
 * @param {*} s
 */
export function isEmail (s) {
  return /^([a-zA-Z0-9_-])+@([a-zA-Z0-9_-])+((.[a-zA-Z0-9_-]{2,3}){1,2})$/.test(s)
}

/**
 * 中国手机号（宽松匹配）
 * @param {string} s 手机号
 * @returns {boolean}
 */
export function isMobile(s) {
  return /^1[0-9]{10}$/.test(s)
}

/**
 * 国内座机号（带区号，严格匹配）
 * @param {string} s 座机号
 * @returns {boolean}
 */
export function is8lPhone(s) {
  return /^(0\d{2,3})-?(\d{7,8})$/.test(s)
}

/**
 * 身份证号码
 * @param {*} s
 */
export function isIdNumber (s) {
  return /^[1-9]\d{5}(18|19|([23]\d))\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/.test(s)
}

/**
 * 姓名验证
 * @param {*} s
 */
export function isName (s) {
  return /^.{1,10}$/.test(s)
}

/**
 * 电话号码
 * @param {*} s
 */
export function isPhone (s) {
  return /^([0-9]{3,4}-)?[0-9]{7,8}$/.test(s)
}

/**
 * URL地址
 * @param {*} s
 */
export function isURL (s) {
  return /^http[s]?:\/\/.*/.test(s)
}

/**
 * 数字验证
 * @param {*} s
 */
export function isNum (s) {
  return /^[0-9]*$/.test(s)
}
