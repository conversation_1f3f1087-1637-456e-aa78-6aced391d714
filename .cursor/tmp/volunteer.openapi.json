{"openapi": "3.0.1", "info": {"title": "volunteer", "description": "", "version": "1.0.0"}, "tags": [], "paths": {"/admin/zyz/badge/instance/pageBadges": {"post": {"summary": "分页查询勋章列表", "deprecated": false, "description": "分页查询勋章列表", "tags": [], "parameters": [{"name": "Authorization", "in": "header", "description": "", "example": "bearer c5dfea92-7d3d-439f-ac1d-46b3b1d2da29", "schema": {"type": "string", "default": "bearer c5dfea92-7d3d-439f-ac1d-46b3b1d2da29"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ZyzBadgeInstanceParams", "description": "查询参数"}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/JsonResultIPageZyzBadgeInstanceVO", "description": "分页勋章实例列表（含勋章基础信息）"}, "example": {"code": 0, "msg": "", "obj": {"records": [{"id": 0, "version": 0, "badgeId": 0, "ownerId": 0, "recycle": false, "badgeInstanceId": 0, "badgeName": "", "badgeFile": "", "previewImage": "", "briefIntro": ""}], "total": 0, "size": 0, "current": 0, "orders": [{"column": "", "asc": false}], "optimizeCountSql": false, "searchCount": false, "optimizeJoinOfCountSql": false, "countId": "", "maxLimit": 0, "pages": 0}, "success": false}}}, "headers": {}}}, "security": []}}}, "components": {"schemas": {"OrderItem": {"type": "object", "properties": {"column": {"type": "string", "description": ""}, "asc": {"type": "boolean", "description": ""}}}, "ZyzBadgeInstanceVO": {"type": "object", "properties": {"id": {"type": "integer", "description": "主键(更新时必填)", "format": "int64"}, "version": {"type": "integer", "description": "版本号(更新时必填)"}, "badgeId": {"type": "integer", "description": "勋章定义ID", "format": "int64"}, "ownerId": {"type": "integer", "description": "持有者ID", "format": "int64"}, "recycle": {"type": "boolean", "description": "是否已回收(0:未回收,1:已回收)"}, "badgeInstanceId": {"type": "integer", "description": "勋章实例ID", "format": "int64"}, "badgeName": {"type": "string", "description": "勋章名称"}, "badgeFile": {"type": "string", "description": "勋章文件"}, "previewImage": {"type": "string", "description": "预览图"}, "briefIntro": {"type": "string", "description": "勋章简介"}}}, "Order": {"type": "object", "properties": {"column": {"type": "string", "description": "排序字段名称"}, "sort": {"type": "string", "description": "正序（asc）/倒序（desc）"}}}, "IPageZyzBadgeInstanceVO": {"type": "object", "properties": {"records": {"type": "array", "items": {"$ref": "#/components/schemas/ZyzBadgeInstanceVO", "description": "勋章实例VO-带有勋章基础信息"}, "description": "查询数据列表"}, "total": {"type": "integer", "description": "总数", "format": "int64"}, "size": {"type": "integer", "description": "每页显示条数，默认 10", "format": "int64"}, "current": {"type": "integer", "description": "当前页", "format": "int64"}, "orders": {"type": "array", "items": {"$ref": "#/components/schemas/OrderItem", "description": "com.baomidou.mybatisplus.core.metadata.OrderItem"}, "description": "排序字段信息"}, "optimizeCountSql": {"type": "boolean", "description": "自动优化 COUNT SQL"}, "searchCount": {"type": "boolean", "description": "是否进行 count 查询"}, "optimizeJoinOfCountSql": {"type": "boolean", "description": "{@link #optimizeJoinOfCountSql()}"}, "countId": {"type": "string", "description": "countId"}, "maxLimit": {"type": "integer", "description": "countId", "format": "int64"}, "pages": {"type": "integer", "format": "int64"}}}, "ZyzBadgeInstanceParams": {"type": "object", "properties": {"currentPage": {"type": "integer", "description": "当前页码（从1开始）"}, "pageSize": {"type": "integer", "description": "每页条数"}, "orders": {"type": "array", "items": {"$ref": "#/components/schemas/Order", "description": "fykj.microservice.core.base.BaseParams.Order"}, "description": "排序参数列表"}, "keyword": {"type": "string", "description": "关键字查询，模糊匹配"}, "ownerId": {"type": "integer", "description": "所有者ID", "format": "int64"}, "recycle": {"type": "boolean", "description": "是否回收"}}}, "JsonResultIPageZyzBadgeInstanceVO": {"type": "object", "properties": {"code": {"type": "integer", "description": ""}, "msg": {"type": "string", "description": ""}, "obj": {"$ref": "#/components/schemas/IPageZyzBadgeInstanceVO", "description": ""}, "success": {"type": "boolean"}}}}, "securitySchemes": {}}, "servers": [], "security": []}